# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/deepweed/proto/remote_veselka_dataset.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/deepweed/proto/remote_veselka_dataset.proto',
  package='deeplearning.deepweed.proto',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n8deeplearning/deepweed/proto/remote_veselka_dataset.proto\x12\x1b\x64\x65\x65plearning.deepweed.proto\"\x17\n\x07Request\x12\x0c\n\x04json\x18\x01 \x01(\t\"\x18\n\x08Response\x12\x0c\n\x04json\x18\x01 \x01(\t2\x98\t\n\x14RemoteDatasetService\x12W\n\x06Sample\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12W\n\x06\x43onfig\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12Z\n\tNumImages\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12U\n\x04Ping\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12^\n\rNewDatapoints\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12^\n\rOldDatapoints\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12\x63\n\x12NewDataCapturedAts\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12Y\n\x08\x46ilepath\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12i\n\x18LoadComparisonEmbeddings\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12\\\n\x0bImageUrl2Id\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12g\n\x16LoadSamplingEmbeddings\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x12i\n\x18SamplingEmbeddingsLoaded\x12$.deeplearning.deepweed.proto.Request\x1a%.deeplearning.deepweed.proto.Response\"\x00\x62\x06proto3'
)




_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='deeplearning.deepweed.proto.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='json', full_name='deeplearning.deepweed.proto.Request.json', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=112,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='deeplearning.deepweed.proto.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='json', full_name='deeplearning.deepweed.proto.Response.json', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=138,
)

DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'deeplearning.deepweed.proto.remote_veselka_dataset_pb2'
  # @@protoc_insertion_point(class_scope:deeplearning.deepweed.proto.Request)
  })
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
  'DESCRIPTOR' : _RESPONSE,
  '__module__' : 'deeplearning.deepweed.proto.remote_veselka_dataset_pb2'
  # @@protoc_insertion_point(class_scope:deeplearning.deepweed.proto.Response)
  })
_sym_db.RegisterMessage(Response)



_REMOTEDATASETSERVICE = _descriptor.ServiceDescriptor(
  name='RemoteDatasetService',
  full_name='deeplearning.deepweed.proto.RemoteDatasetService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=141,
  serialized_end=1317,
  methods=[
  _descriptor.MethodDescriptor(
    name='Sample',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.Sample',
    index=0,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Config',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.Config',
    index=1,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='NumImages',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.NumImages',
    index=2,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.Ping',
    index=3,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='NewDatapoints',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.NewDatapoints',
    index=4,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='OldDatapoints',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.OldDatapoints',
    index=5,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='NewDataCapturedAts',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.NewDataCapturedAts',
    index=6,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Filepath',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.Filepath',
    index=7,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadComparisonEmbeddings',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.LoadComparisonEmbeddings',
    index=8,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ImageUrl2Id',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.ImageUrl2Id',
    index=9,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadSamplingEmbeddings',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.LoadSamplingEmbeddings',
    index=10,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SamplingEmbeddingsLoaded',
    full_name='deeplearning.deepweed.proto.RemoteDatasetService.SamplingEmbeddingsLoaded',
    index=11,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_REMOTEDATASETSERVICE)

DESCRIPTOR.services_by_name['RemoteDatasetService'] = _REMOTEDATASETSERVICE

# @@protoc_insertion_point(module_scope)
