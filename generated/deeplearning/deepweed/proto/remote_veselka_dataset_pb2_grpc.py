# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.deeplearning.deepweed.proto import remote_veselka_dataset_pb2 as deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2


class RemoteDatasetServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Sample = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/Sample',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.Config = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/Config',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.NumImages = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/NumImages',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.Ping = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/Ping',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.NewDatapoints = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/NewDatapoints',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.OldDatapoints = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/OldDatapoints',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.NewDataCapturedAts = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/NewDataCapturedAts',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.Filepath = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/Filepath',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.LoadComparisonEmbeddings = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/LoadComparisonEmbeddings',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.ImageUrl2Id = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/ImageUrl2Id',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.LoadSamplingEmbeddings = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/LoadSamplingEmbeddings',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )
        self.SamplingEmbeddingsLoaded = channel.unary_unary(
                '/deeplearning.deepweed.proto.RemoteDatasetService/SamplingEmbeddingsLoaded',
                request_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
                response_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
                )


class RemoteDatasetServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Sample(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Config(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NumImages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NewDatapoints(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OldDatapoints(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NewDataCapturedAts(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Filepath(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadComparisonEmbeddings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ImageUrl2Id(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadSamplingEmbeddings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SamplingEmbeddingsLoaded(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RemoteDatasetServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Sample': grpc.unary_unary_rpc_method_handler(
                    servicer.Sample,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'Config': grpc.unary_unary_rpc_method_handler(
                    servicer.Config,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'NumImages': grpc.unary_unary_rpc_method_handler(
                    servicer.NumImages,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'NewDatapoints': grpc.unary_unary_rpc_method_handler(
                    servicer.NewDatapoints,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'OldDatapoints': grpc.unary_unary_rpc_method_handler(
                    servicer.OldDatapoints,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'NewDataCapturedAts': grpc.unary_unary_rpc_method_handler(
                    servicer.NewDataCapturedAts,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'Filepath': grpc.unary_unary_rpc_method_handler(
                    servicer.Filepath,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'LoadComparisonEmbeddings': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadComparisonEmbeddings,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'ImageUrl2Id': grpc.unary_unary_rpc_method_handler(
                    servicer.ImageUrl2Id,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'LoadSamplingEmbeddings': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadSamplingEmbeddings,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
            'SamplingEmbeddingsLoaded': grpc.unary_unary_rpc_method_handler(
                    servicer.SamplingEmbeddingsLoaded,
                    request_deserializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.FromString,
                    response_serializer=deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'deeplearning.deepweed.proto.RemoteDatasetService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RemoteDatasetService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Sample(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/Sample',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Config(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/Config',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NumImages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/NumImages',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/Ping',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NewDatapoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/NewDatapoints',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OldDatapoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/OldDatapoints',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NewDataCapturedAts(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/NewDataCapturedAts',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Filepath(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/Filepath',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LoadComparisonEmbeddings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/LoadComparisonEmbeddings',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ImageUrl2Id(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/ImageUrl2Id',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LoadSamplingEmbeddings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/LoadSamplingEmbeddings',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SamplingEmbeddingsLoaded(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.deepweed.proto.RemoteDatasetService/SamplingEmbeddingsLoaded',
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Request.SerializeToString,
            deeplearning_dot_deepweed_dot_proto_dot_remote__veselka__dataset__pb2.Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
