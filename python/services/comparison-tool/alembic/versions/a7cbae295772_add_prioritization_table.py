"""add prioritization table

Revision ID: a7cbae295772
Revises: 29873845ad00
Create Date: 2023-08-21 20:30:59.016537

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a7cbae295772"
down_revision = "29873845ad00"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "comparison_prioritizations",
        sa.<PERSON>umn("id", sa.String, primary_key=True),
        sa.<PERSON>umn("created", sa.BigInteger),
        sa.<PERSON>n("user", sa.String),
        sa.<PERSON>umn("done", sa.<PERSON>),
        sa.<PERSON>umn("image_one", sa.String),
        sa.<PERSON>umn("label_one", sa.String),
        sa.<PERSON>umn("label_one_category", sa.String),
        sa.<PERSON>umn("label_one_x", sa.String),
        sa.<PERSON>umn("label_one_y", sa.String),
        sa.<PERSON>umn("label_one_radius", sa.String),
        sa.<PERSON>umn("image_two", sa.String),
        sa.Column("label_two", sa.String),
        sa.Column("label_two_category", sa.String),
        sa.<PERSON>umn("label_two_x", sa.String),
        sa.Column("label_two_y", sa.String),
        sa.Column("label_two_radius", sa.String),
    )


def downgrade() -> None:
    op.drop_table("comparison_prioritizations")
