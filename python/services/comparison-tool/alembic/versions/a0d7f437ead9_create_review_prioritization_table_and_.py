"""create review prioritization table and create valid column on review

Revision ID: a0d7f437ead9
Revises: 55740d19bb62
Create Date: 2025-01-23 17:35:50.654221

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a0d7f437ead9'
down_revision = '55740d19bb62'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "comparison_review_prioritizations",
        sa.<PERSON>umn("id", sa.String, primary_key=True),
        sa.<PERSON>umn("created", sa.BigInteger),
        sa.<PERSON>umn("label_id", sa.String),
        sa.<PERSON>umn("done", sa.<PERSON>),
    )
    
    op.add_column("comparison_reviews", sa.<PERSON>umn("valid", sa.<PERSON>))
    op.execute(f"update comparison_reviews set valid=true")


def downgrade() -> None:
    op.drop_column("comparison_reviews", "valid")
    op.drop_table("comparison_review_prioritizations")

