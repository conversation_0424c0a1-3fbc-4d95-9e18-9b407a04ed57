"""add review column

Revision ID: 29873845ad00
Revises: 
Create Date: 2023-05-30 17:58:22.816749

"""
import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "29873845ad00"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "comparison_reviews",
        sa.Column("id", sa.String, primary_key=True),
        sa.Column("created", sa.BigInteger),
        sa.<PERSON>umn("label_id", sa.String),
        sa.<PERSON>umn("user", sa.String),
        sa.ForeignKeyConstraint(["label_id"], ["comparison_labels.id"]),
    )


def downgrade() -> None:
    op.drop_table("comparison_reviews")
