import logging
import os
import random
import shutil
import threading
import time
import uuid

import boto3
import streamlit as st
import streamlit_authenticator
import yaml

from constants import (
    ADMIN_MODE,
    DATA_DIR,
    DB_NAME,
    DEFAULT_DATASET,
    S3_BUCKET,
    S3_KEY_PREFIX,
)
from tool.comparison_prioritization import comparison_prioritization_page
from tool.comparison_review import comparison_review_page
from tool.comparison_review_prioritization import comparison_review_prioritization_page
from tool.comparison_tool import comparison_tool_page
from tool.add_reviewed_labels import add_reviewed_labels_page
from tool.perf_tracker import get_perf_log_line
from tool.utils import check_and_clean_images, check_and_sync_db, download, get_latest_veselka_labeling_dataset_id

CREDENTIAL_FILE = "/data/credentials.yaml"
TMP_CREDENTIAL_FILE = "/data/tmp_credentials.yaml"

PAGES = {
    "Label": comparison_tool_page,
    "Review": comparison_review_page,
}

ADMIN_ONLY_PAGES = {
    "Priorization": comparison_prioritization_page,
    "Review Prioritization": comparison_review_prioritization_page,
    "Add Reviewed Labels": add_reviewed_labels_page,
}

logging.basicConfig(format="%(asctime)s %(levelname)s:%(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S")


def submit_registration(name, email, password, repeat_password):
    if len(password) == 0:
        st.warning("Invalid password")
        return
    if password != repeat_password:
        st.warning("Passwords don't match")
        return
    if not (email.endswith("@imerit.net") or email.endswith("@carbonrobotics.com")):
        st.warning("Not a valid email address")
        return

    with open(CREDENTIAL_FILE) as file:
        config = yaml.load(file, Loader=yaml.SafeLoader)

    hashed_passwords = streamlit_authenticator.Hasher([password]).generate()

    config["credentials"]["names"].append(name)
    config["credentials"]["emails"].append(email)
    config["credentials"]["passwords"].append(hashed_passwords[0])

    with open(TMP_CREDENTIAL_FILE, "w") as file:
        yaml.dump(config, file)

    shutil.copyfile(TMP_CREDENTIAL_FILE, CREDENTIAL_FILE)

    s3 = boto3.client("s3")
    with open(CREDENTIAL_FILE, "rb") as f:
        s3.upload_fileobj(f, S3_BUCKET, os.path.join(S3_KEY_PREFIX, "credentials.yaml"))

    st.success("Registration Complete! Please try logging in.")


@st.cache_resource
def set_up_db_s3_sync(_db_lock: threading.Lock):
    threading.Thread(target=check_and_sync_db, kwargs={"db_lock": _db_lock}).start()


def set_up_db(db_lock: threading.Lock):
    if ADMIN_MODE == 1:
        dataset_id = st.sidebar.text_input("Dataset id", value="0bbadd11-d4f3-4283-91c4-c07157fa803f")
    else:
        now = time.time()
        if "previous_dataset_id" not in st.session_state or (now - st.session_state["previous_check_time"]) > 5 * 60:
            st.session_state["previous_dataset_id"] = get_latest_veselka_labeling_dataset_id()
            st.session_state["previous_check_time"] = now
        dataset_id = st.session_state["previous_dataset_id"]
        logging.warning(f"Using dataset {dataset_id}")

    try:
        if not os.path.exists(os.path.join(DATA_DIR, DB_NAME)):
            logging.info(
                f"Downloading {os.path.join(DATA_DIR, DB_NAME)} {S3_BUCKET} {os.path.join(S3_KEY_PREFIX, DB_NAME)}"
            )
            download(os.path.join(DATA_DIR, DB_NAME), S3_BUCKET, os.path.join(S3_KEY_PREFIX, DB_NAME))
    except Exception as e:
        logging.warning(f"Could not download db: {e}")

    set_up_db_s3_sync(db_lock)

    return dataset_id


@st.cache_resource
def set_up_cleanup(_display_lock: threading.Lock):
    threading.Thread(target=check_and_clean_images, kwargs={"display_lock": _display_lock}).start()


if __name__ == "__main__":
    st.set_page_config(layout="wide")

    if "key" in st.session_state:
        st.session_state["key"] = uuid.uuid()

    s3 = boto3.client("s3")

    try:
        if not os.path.exists(CREDENTIAL_FILE):
            download(CREDENTIAL_FILE, S3_BUCKET, os.path.join(S3_KEY_PREFIX, "credentials.yaml"))
    except Exception as e:
        logging.info(f"Could not download creds: {e}")

    with open(CREDENTIAL_FILE) as file:
        config = yaml.load(file, Loader=yaml.SafeLoader)
    authenticator = streamlit_authenticator.Authenticate(
        config["credentials"]["names"],
        config["credentials"]["emails"],
        config["credentials"]["passwords"],
        config["cookie"]["name"],
        config["cookie"]["key"],
        config["cookie"]["expiry_days"],
    )

    name, authentication_status, email = authenticator.login("Login", "main")
    if authentication_status and (email.endswith("@carbonrobotics.com") or email.endswith("@imerit.net")):
        db_lock = threading.Lock()
        display_lock = threading.Lock()
        dataset_id = set_up_db(db_lock)
        set_up_cleanup(display_lock)
        if dataset_id != "":
            dir = os.path.join(DATA_DIR, "size_category_options")
            os.makedirs(dir, exist_ok=True)
            size_category_filepath = os.path.join(dir, f"{dataset_id}.json")
            try:
                if not os.path.exists(size_category_filepath):
                    with st.spinner(text="Downloading dataset file, please wait..."):
                        download(
                            size_category_filepath,
                            S3_BUCKET,
                            os.path.join(S3_KEY_PREFIX, f"size_category_options/{dataset_id}.json"),
                        )
            except Exception as e:
                logging.warning(
                    f"Couldn't find a size_category_options file for {dataset_id}, building from scratch: {e}"
                )

            if email.endswith("@carbonrobotics.com"):
                pages = PAGES
                pages.update(ADMIN_ONLY_PAGES)
                page = st.sidebar.radio("Navigation", pages.keys())
                pages[page](email, dataset_id, size_category_filepath, db_lock, display_lock)

                with st.expander("Register User"):
                    register_form = st.form("Register user")
                    register_form.subheader("Register User")
                    name = register_form.text_input("Name")
                    email_addr = register_form.text_input("Email")
                    password = register_form.text_input("Password", type="password")
                    repeat_password = register_form.text_input("Repeat Password", type="password")
                    submit = register_form.form_submit_button("Submit")
                    if submit:
                        submit_registration(name, email_addr, password, repeat_password)
            else:
                page = st.sidebar.radio("Navigation", PAGES.keys(), index=1)
                PAGES[page](email, dataset_id, size_category_filepath, db_lock, display_lock)
            rand = random.random()
            if rand < 0.25:
                logging.info(get_perf_log_line())
        st.sidebar.markdown("___")
    else:
        st.warning("Please logout and back in")
    authenticator.logout("Logout", "sidebar")
