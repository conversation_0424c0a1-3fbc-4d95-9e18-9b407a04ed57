
from tool.utils import a_and_b_within_size_limit, does_square_a_overlap_center_b

def test_a_and_b_within_size_limit():
    size_multiple = 1.5

    assert a_and_b_within_size_limit(10, 15, size_multiple)
    assert a_and_b_within_size_limit(15, 10, size_multiple)
    assert not a_and_b_within_size_limit(10, 16, size_multiple)
    assert not a_and_b_within_size_limit(16, 10, size_multiple)


def test_does_square_a_overlap_center_b():
    
    a = {"x": 0, "y": 0}
    b = {"x": 100, "y": 10}
    
    assert does_square_a_overlap_center_b(a, b, 220)
    assert does_square_a_overlap_center_b(a, b, 200)
    assert not does_square_a_overlap_center_b(a, b, 190)