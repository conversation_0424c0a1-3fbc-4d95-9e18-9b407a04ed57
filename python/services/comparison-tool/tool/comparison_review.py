import datetime
import logging
import threading
from typing import Any, Dict, List, Optional

import pandas as pd
import pytz
import streamlit as st

from tool.comparison_tool import get_images_and_labels
from tool.perf_tracker import PerfCategory, duration_perf_recorder
from tool.utils import (
    Datapoint,
    extract_size_category_options_from_file,
    get_labels_for_reviews,
    modify_predictions,
    save_review,
)


def change_answer(label_id: str, match: Optional[bool], valid: bool, user: str, db_lock: threading.Lock):
    with duration_perf_recorder(PerfCategory.REVIEW, "change_answer"):
        modify_predictions(id=label_id, match=match, valid=valid, db_lock=db_lock)
        save_review(label_id=label_id, user=user, db_lock=db_lock)


def comparison_review_page(
    username: str, dataset_id: str, size_category_filepath: str, db_lock: threading.Lock, display_lock: threading.Lock
):
    with duration_perf_recorder(PerfCategory.REVIEW, "comparison_review_page"):
        st.title("Plant Comparison Review")

        if "images_dict" not in st.session_state:
            _, images_dict, _ = extract_size_category_options_from_file(size_category_filepath)
            st.session_state["images_dict"] = images_dict

        if username.endswith("@carbonrobotics.com"):
            user = st.sidebar.text_input("User")
            label_id = st.sidebar.text_input("Label ID")
            match = st.sidebar.radio("Match", options=[True, False])
            valid = st.sidebar.radio("Valid", options=[True, False])
            suspicious = st.sidebar.checkbox("Only show suspicious answers?", value=False)
            skip_reviewed = st.sidebar.checkbox("Skip reviewed images?", value=True)
            first_category = st.sidebar.text_input("First Category")
            second_category = st.sidebar.text_input("Second Category")
            
            today = datetime.datetime.now()
            default_start = datetime.date(2020, 1, 1)
            default_end = today + datetime.timedelta(days=1)

            start_date = st.sidebar.date_input("Start Date", value=default_start)
            end_date = st.sidebar.date_input("End Date", value=default_end)
        else:
            user = ""
            label_id = ""
            match = None
            valid = True
            suspicious = False
            skip_reviewed = True
            first_category = ""
            second_category = ""
            start_date = None
            end_date = None

        labels = get_labels_for_reviews(
            db_lock, user, match, suspicious, valid, skip_reviewed, label_id, first_category, second_category, start_date, end_date
        )
        
        if "review_label_index" not in st.session_state:
            st.session_state["review_label_index"] = 0
            
            
        if st.session_state["review_label_index"] >= len(labels):
            st.session_state["review_label_index"] = len(labels) - 1

        label_ind = st.sidebar.slider("Label", min_value=0, max_value=len(labels) - 1, value=st.session_state["review_label_index"], key="review_label_index")

        label = labels[label_ind]

        if username.endswith("@carbonrobotics.com"):
            st.write(
                pd.DataFrame(
                    {
                        "id": [label["id"]],
                        "category_one": [label["label_one_category"]],
                        "category_two": [label["label_two_category"]],
                        "match": [label["match"]],
                        "created": [
                            datetime.datetime.fromtimestamp(label["created"] // 1000)
                            .replace(tzinfo=datetime.timezone.utc)
                            .astimezone(tz=pytz.timezone("America/Los_Angeles"))
                            .strftime("%Y-%m-%d %H:%M:%S")
                        ],
                        "radius_one": [label["label_one_radius"]],
                        "radius_two": [label["label_two_radius"]],
                        "image_one": [label["image_one"]],
                        "image_two": [label["image_two"]],
                    }
                )
            )
        with display_lock:
            get_images_and_labels(
                (
                    Datapoint(
                        label_id="",
                        category=label["label_one_category"],
                        radius=label["label_one_radius"],
                        x=label["label_one_x"],
                        y=label["label_one_y"],
                        image_id=label["image_one"],
                    ),
                    Datapoint(
                        label_id="",
                        category=label["label_two_category"],
                        radius=label["label_two_radius"],
                        x=label["label_two_x"],
                        y=label["label_two_y"],
                        image_id=label["image_two"],
                    ),
                ),
                st.session_state["images_dict"],
            )

        if label["valid"]:
            st.text(f"These images were marked: {'Yes (match)' if label['match'] else 'No (not a match)'}")
        else:
            st.text("These images were marked: Skip (invalid)")
            
        st.session_state.label_id = label["id"]
        with st.expander("Would you like to change answer?", expanded=True):
            change_answer_form = st.form("Change Answer", clear_on_submit=True)
            change_answer_form.subheader("Change Answer")
            change_answer_form.radio("New answer", options=["Skip", "Yes", "No"], key="form_answer")
            change_answer_form.form_submit_button("Submit", on_click=callback, args=[username, db_lock])
        st.button("Accept Label", on_click=lambda: save_review(label_id=label["id"], user=username, db_lock=db_lock))

def callback(username: str, db_lock: threading.Lock):
    answer = st.session_state.form_answer
    label_id = st.session_state.label_id
    match = None
    valid = False
    if answer == "Yes":
        match = True
        valid = True
    elif answer == "No":
        match = False
        valid = True
    change_answer(label_id, match=match, valid=valid, user=username, db_lock=db_lock)