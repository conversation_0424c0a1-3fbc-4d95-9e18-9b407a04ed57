import logging
import threading
from typing import Any, List, Optional, Tuple, Union

import pandas as pd
import streamlit as st

from tool.utils import Datapoint, add_prioritization_to_db, save_predictions, todo_priorities
from tool.db.tables import ComparisonLabels

class HeaderLabel:
    def __init__(self, header: str, expected_type: Any, default_value: Optional[Union[str, float]] = None):
        self.header = header
        self.expected_type = expected_type
        self.default_value = default_value


HEADERS = [
    HeaderLabel("image_one", str),
    HeaderLabel("x_one", float),
    HeaderLabel("y_one", float),
    HeaderLabel("radius_one", float),
    HeaderLabel("category_one", str),
    HeaderLabel("label_one", str),
    HeaderLabel("image_two", str),
    HeaderLabel("x_two", float),
    HeaderLabel("y_two", float),
    HeaderLabel("radius_two", float),
    HeaderLabel("category_two", str),
    <PERSON>erLabel("label_two", str),
]


def make_empty(number_items: int, include_match: bool = False) -> Tuple[pd.DataFrame, List[HeaderLabel]]:
    items = []

    row = {item.header: "" for item in HEADERS}

    headers = HEADERS
    if include_match:
        row["match"] = ""
        headers = [HeaderLabel("match", bool)] + headers

    for _ in range(number_items):
        items.append(row)

    return pd.DataFrame(columns=[header.header for header in headers], data=items), headers


def verify_and_save(data: pd.DataFrame, db_lock: threading.Lock, username: str):
    records = data.to_dict("records")
    to_commit = []
    for record in records:
        for header in HEADERS:
            if type(record[header.header]) is None:
                record[header.header] = header.default_value
            try:
                header.expected_type(record[header.header])
            except Exception as e:
                st.warning(
                    f"Error, {header.header}: {type(record[header.header])}, expected {header.expected_type}, {e}"
                )
                raise e

        try:
            to_commit.append(
                (
                    Datapoint(
                        image_id=str(record["image_one"]),
                        label_id=record["label_one"] if record["label_one"] is not None else "",
                        category=str(record["category_one"]),
                        radius=float(record["radius_one"]),
                        x=float(record["x_one"]),
                        y=float(record["y_one"]),
                    ),
                    Datapoint(
                        image_id=str(record["image_two"]),
                        label_id=record["label_two"] if record["label_two"] is not None else "",
                        category=str(record["category_two"]),
                        radius=float(record["radius_two"]),
                        x=float(record["x_two"]),
                        y=float(record["y_two"]),
                    ),
                )
            )
        except Exception as e:
            logging.warning(f"Could not commit: {e}")

    st.text(f"To Commit {len(to_commit)}")

    if len(to_commit) > 0:
        add_prioritization_to_db(db_lock, username, to_commit)

    if "preprocessed_pairs" in st.session_state:
        todo = todo_priorities(db_lock)
        st.session_state["preprocessed_pairs"].load_prioritized_pairs(todo)


def verify_and_save_labels(data: pd.DataFrame, match: bool, valid: Optional[bool], db_lock: threading.Lock, username: str):
    records = data.to_dict("records")
    to_commit = []
    for record in records:
        for header in HEADERS:
            if type(record[header.header]) is None:
                record[header.header] = header.default_value
            try:
                header.expected_type(record[header.header])
            except Exception as e:
                st.warning(
                    f"Error, {header.header}: {type(record[header.header])}, expected {header.expected_type}, {e}"
                )
                raise e

        to_commit.append(
            ComparisonLabels(
                match=match,
                valid=valid,
                user=username,
                source='generated',
                image_one=str(record["image_one"]),
                label_one=record["label_one"] if record["label_one"] is not None else "",
                label_one_category=str(record["category_one"]),
                label_one_x=float(record["x_one"]),
                label_one_y=float(record["y_one"]),
                label_one_radius=float(record["radius_one"]),
                image_two=str(record["image_two"]),
                label_two=record["label_two"] if record["label_two"] is not None else "",
                label_two_category=str(record["category_two"]),
                label_two_x=float(record["x_two"]),
                label_two_y=float(record["y_two"]),
                label_two_radius=float(record["radius_two"]),
            )
        )

    st.text(f"To Commit {len(to_commit)}")

    if len(to_commit) > 0:
        try:
            save_predictions(to_commit, db_lock)
        except Exception as e:
            logging.warning(f"Could not commit: {e}")
        

def comparison_prioritization_page(
    username: str, dataset_id: str, size_category_filepath: str, db_lock: threading.Lock, display_lock: threading.Lock
):
    st.title("Plant Comparison Prioritization")
    st.text("""
        This page allows you to add your own labels, which will be prioritized for labelers. You can upload a csv file with the expected headers or copy
        and paste your data into the table below. If you want, you can also assign a label to all data, which will then put it in front of reviewers more
        quickly.
    """)

    todo = todo_priorities(db_lock)

    if "preprocessed_pairs" in st.session_state:
        st.session_state["preprocessed_pairs"].load_prioritized_pairs(todo)

    df = pd.DataFrame(todo)
    with st.expander(f"Prioritizations left to do: {df.shape[0]}"):
        st.table(df)

    initial_rows, headers = make_empty(1)
    uploaded_file = st.file_uploader("Upload a prioritization file")
    if uploaded_file is not None:
        initial_rows = pd.read_csv(uploaded_file, dtype={header.header: header.expected_type for header in headers})

    with st.form("add_data", clear_on_submit=True):
        st.write("Add Data")
        data = st.data_editor(initial_rows, num_rows="dynamic")

        give_a_default = st.checkbox("Give a default label?")
        default_label = st.radio("Default Label", ["Match", "Not a match"])

        submitted = st.form_submit_button("Save")
        if submitted:
            try:
                if give_a_default:
                    verify_and_save_labels(data, match=default_label=="Match", valid=True, db_lock=db_lock, username=username)
                else:
                    verify_and_save(data, db_lock, username)
                    
                st.success("Successfully saved labels")
            except:
                st.warning("Could not save labels")
