import logging
import os
import threading
from typing import Any, Dict, List, Optional, <PERSON>ple

import cv2
import numpy as np
import requests
import streamlit as st
from PIL import Image

from constants import DEVMO<PERSON>, MIN_SIZE, SIZE_BUCKETS, VESELKA_URL
from tool.db.tables import Comparison<PERSON>abels
from tool.perf_tracker import PerfCategory, duration_perf_recorder
from tool.utils import (
    PreprocessedPairs,
    create_point_dict,
    extract_size_category_options,
    extract_size_category_options_from_file,
    get_count,
    get_dataset,
    get_user_scoreboard,
    mark_prioritization_pair_done,
    save_predictions,
    todo_priorities,
)

from .abstractions.cache import S3Cache

S3_BUCKET_IND = 2

def get_images_and_labels(pair: Tuple[Dict[str, Any], Dict[str, Any]], images_dict: Dict[str, Any]) -> bool:
    with duration_perf_recorder(PerfCategory.LABELING, "get_images_and_labels"):
        col1, col2 = st.columns(2)
        radius_of_display = max(pair[0].radius, pair[1].radius) + 50

        image_one_url = None
        image_two_url = None
        if pair[0].image_id in images_dict:
            image_one_url = images_dict[pair[0].image_id]["uri"]
        if pair[1].image_id in images_dict:
            image_two_url = images_dict[pair[1].image_id]["uri"]

        try:
            image_ids = []
            if image_one_url is None:
                image_ids.append(pair[0].image_id)
            if image_two_url is None:
                image_ids.append(pair[1].image_id)
            if len(image_ids) > 0:
                response = requests.post(
                    VESELKA_URL + "/internal/images/ids_to_urls",
                    json={"image_ids": image_ids},
                )
                logging.debug(
                    f"Checking veselka for image url ({response.ok}, {response.status_code}) for image_ids: {image_ids}"
                )

                results = response.json()

                if image_one_url is None:
                    image_one_url = results["images"].get(pair[0].image_id)
                if image_two_url is None:
                    image_two_url = results["images"].get(pair[1].image_id)

            with col1:
                display_image(pair[0], radius_of_display, image_one_url, pair[0].image_id)
            with col2:
                display_image(pair[1], radius_of_display, image_two_url, pair[1].image_id)
        except Exception as e:
            # We never expect to be in this mode in production
            logging.warning(f"Couldn't get images: {e}")
            if not DEVMODE:
                st.warning(f"Something went wrong and we couldn't load images. Please try refreshing and reaching out to Carbon for further support")
                return False
            with col1:
                st.text(pair[0].image_id)
            with col2:
                st.text(pair[1].image_id)
                
    return True


def display_image(item: Dict[str, Any], radius_of_display: float, uri: str, image_id: str) -> None:
    with duration_perf_recorder(PerfCategory.LABELING, "display_image"):
        split_s3_path = uri.split("/")
        bucket_name = split_s3_path[S3_BUCKET_IND]
        split_ind = S3_BUCKET_IND + 1
        image_s3_key = os.path.join(*split_s3_path[split_ind:])
        logging.info(f"Grabbing {image_s3_key}")
        with S3Cache(bucket_name, image_s3_key, mode="rb") as f:
            image = Image.open(f).convert("RGB")

        cropped_image = image.crop(
            (
                int(item.x - radius_of_display),
                int(item.y - radius_of_display),
                int(item.x + radius_of_display),
                int(item.y + radius_of_display),
            )
        )
        open_cv_image = np.array(cropped_image)
        cv2.circle(
            open_cv_image,
            (int(radius_of_display), int(radius_of_display)),
            2,
            (255, 155, 0),
            1,
        )

        cv2.circle(
            open_cv_image,
            (int(radius_of_display), int(radius_of_display)),
            max(int(item.radius * 0.2), int(radius_of_display * 0.1)),
            (155, 255, 0),
            int(max(1, 0.01 * radius_of_display)),
        )

        size = open_cv_image.shape[0]
        while size < MIN_SIZE:
            size = 2 * size

        if size > open_cv_image.shape[0]:
            open_cv_image = cv2.resize(open_cv_image, (size, size), interpolation=cv2.INTER_NEAREST)
        st.image(open_cv_image)


def process_result(items: List[Dict[str, Any]], match: Optional[bool], valid: bool) -> Dict[str, Any]:
    return f"match={match}\nvalid={valid}\nitem_1={items[0]}\nitem_2={items[1]}"


def submit(username: str, db_lock: threading.Lock):
    with duration_perf_recorder(PerfCategory.LABELING, "submit"):
        answer = st.session_state.label_answer
        if answer == "Skip":
            match = None
            valid = False
        elif answer == "Yes":
            match = True
            valid = True
        elif answer == "No":
            match = False
            valid = True

        p = st.session_state[f"{username}_pair"]
        pair = p["pair"]
        one = pair.datapoint_one
        two = pair.datapoint_two
        source = p["source"]

        save_predictions(
            [
                ComparisonLabels(
                    match=match,
                    valid=valid,
                    image_one=one.image_id,
                    label_one=one.label_id,
                    label_one_category=one.category,
                    label_one_x=one.x,
                    label_one_y=one.y,
                    label_one_radius=one.radius,
                    image_two=two.image_id,
                    label_two=two.label_id,
                    label_two_category=two.category,
                    label_two_x=two.x,
                    label_two_y=two.y,
                    label_two_radius=two.radius,
                    user=username,
                    source=source,
                )
            ],
            db_lock=db_lock,
        )

        if pair.pair_id is not None:
            mark_prioritization_pair_done(pair.pair_id, db_lock)

        st.session_state[f"{username}_pair"] = None


def display(images_dict: Dict[str, Any], db_lock: threading.Lock, username: str) -> None:
    with duration_perf_recorder(PerfCategory.LABELING, "display"):
        pair = st.session_state[f"{username}_pair"]["pair"]
        one = pair.datapoint_one
        two = pair.datapoint_two
        success = get_images_and_labels((one, two), images_dict)

        if not success:
            return

        st.header("Are these the same plant?")

        with st.form("Answer", clear_on_submit=True):
            st.radio("Answer", ["Skip", "Yes", "No"], key="label_answer")
            st.form_submit_button("Submit", on_click=submit, args=(username, db_lock))


def make_size_radio_buttons():
    size_format = [f"{SIZE_BUCKETS[i]}-{SIZE_BUCKETS[i+1]}" for i in range(len(SIZE_BUCKETS) - 1)]
    return str(
        st.sidebar.radio(
            "Size buckets (radius mm)",
            SIZE_BUCKETS[:-1],
            format_func=lambda x: size_format[SIZE_BUCKETS.index(x)],
            index=4,
        )
    )


def comparison_tool_page(
    username: str, dataset_id: str, size_category_filepath: str, db_lock: threading.Lock, display_lock: threading.Lock
):
    with duration_perf_recorder(PerfCategory.LABELING, "comparison_tool_page"):
        st.title("Plant Comparison Labeling")

        is_carbon_user = username.endswith("@carbonrobotics.com")
        
        if not os.path.exists(size_category_filepath):
            extract_size_category_options(get_dataset(dataset_id=dataset_id), size_category_filepath, SIZE_BUCKETS)
        try:
            # Okay if point_dict doesn't exist for the dataset, do this
            if "point_dict" not in st.session_state or st.session_state.get("point_dict_dataset") != dataset_id:
                if os.path.exists(size_category_filepath):
                    _, _, category_size_options = extract_size_category_options_from_file(size_category_filepath)
                    st.session_state["point_dict"] = create_point_dict(category_size_options)
                    st.session_state["point_dict_dataset"] = dataset_id
            
            with st.spinner("Loading..."):
                if "preprocessed_pairs" not in st.session_state:
                    st.session_state["preprocessed_pairs"] = PreprocessedPairs(
                        size_category_filepath, st.session_state["point_dict"]
                    )
                    todo = todo_priorities(db_lock)
                    st.session_state["preprocessed_pairs"].load_prioritized_pairs(todo)
                if "images_dict" not in st.session_state:
                    st.session_state["images_dict"] = st.session_state["preprocessed_pairs"].images_dict

                if f"{username}_pair" in st.session_state:
                    logging.debug(f"Rerender {st.session_state[username+'_pair']}")
                if f"{username}_pair" not in st.session_state or st.session_state[f"{username}_pair"] is None:
                    pair, source = st.session_state["preprocessed_pairs"].pop()
                    st.session_state[f"{username}_pair"] = {"pair": pair, "source": source}
                with display_lock:
                    display(st.session_state["images_dict"], db_lock, username)
        except Exception as e:
            logging.warning(f"Issue: {e}", exc_info=True, stack_info=True)
            # traceback.print_exc()
            st.warning(f"Error: {e}")
        total_labels = get_count(db_lock)
        st.sidebar.markdown(f"Completed tasks (all users): {total_labels}")
        if is_carbon_user:
            st.header("Scoreboard")
            scoreboard = get_user_scoreboard(db_lock)
            st.markdown(f"Completed tasks (all users): {total_labels}")
            st.table(scoreboard)
