import datetime
import os
import uuid
from typing import Any, Dict

import sqlalchemy
import sqlalchemy.orm
from sqlalchemy import BigInteger, Boolean, Column, Float, ForeignKey, String

from constants import CONNECTION_STRING, DATA_DIR, DB_NAME

Base = sqlalchemy.orm.declarative_base()


def epoch_timestamp_ms():
    return int((datetime.datetime.utcnow() - datetime.datetime(1970, 1, 1)).total_seconds() * 1000)


def uuid4_str() -> str:
    return str(uuid.uuid4())


class ComparisonLabels(Base):
    __tablename__ = "comparison_labels"
    id = Column(String, primary_key=True, default=uuid4_str)
    created = Column(BigInteger, default=epoch_timestamp_ms)
    match = Column(Boolean, nullable=True)
    valid = Column(Boolean)
    image_one = Column(String)
    label_one = Column(String)
    label_one_category = Column(String)
    label_one_x = Column(Float)
    label_one_y = Column(Float)
    label_one_radius = Column(Float)
    image_two = Column(String)
    label_two = Column(String)
    label_two_category = Column(String)
    label_two_x = Column(Float)
    label_two_y = Column(Float)
    label_two_radius = Column(Float)
    user = Column(String)
    source = Column(String)

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


class ComparisonReviews(Base):
    __tablename__ = "comparison_reviews"
    id = Column(String, primary_key=True, default=uuid4_str)
    created = Column(BigInteger, default=epoch_timestamp_ms)
    label_id = Column(String, ForeignKey("comparison_labels.id"))
    user = Column(String)
    valid = Column(Boolean, default=lambda: True)


class Dataset(Base):
    __tablename__ = "datasets"
    id = Column(String, primary_key=True, default=uuid4_str)
    name = Column(String, nullable=False)
    created = Column(BigInteger, nullable=False, default=epoch_timestamp_ms)
    train = Column(String, nullable=False)
    validation = Column(String)
    test = Column(String)
    parent_id = Column(String)


class ComparisonPrioritizations(Base):
    __tablename__ = "comparison_prioritizations"
    id = Column(String, primary_key=True, default=uuid4_str)
    created = Column(BigInteger, default=epoch_timestamp_ms)
    user = Column(String)
    done = Column(Boolean, nullable=False, default=lambda: False)
    image_one = Column(String)
    label_one = Column(String)
    label_one_category = Column(String)
    label_one_x = Column(Float)
    label_one_y = Column(Float)
    label_one_radius = Column(Float)
    image_two = Column(String)
    label_two = Column(String)
    label_two_category = Column(String)
    label_two_x = Column(Float)
    label_two_y = Column(Float)
    label_two_radius = Column(Float)

    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}
    
    
class ComparisonReviewPrioritization(Base):
    __tablename__ = "comparison_review_prioritizations"
    id = Column(String, primary_key=True, default=uuid4_str)
    created = Column(BigInteger, default=epoch_timestamp_ms)
    label_id = Column(String, ForeignKey("comparison_labels.id"))
    done = Column(Boolean, nullable=False, default=lambda: False)
    
    def to_dict(self) -> Dict[str, Any]:
        return {column.key: getattr(self, column.key) for column in self.__table__.columns}


def get_veselka_sessionmaker():
    return sqlalchemy.orm.sessionmaker(sqlalchemy.create_engine(CONNECTION_STRING))


def get_local_sessionmaker():
    db_path = os.path.join(DATA_DIR, DB_NAME)
    engine = sqlalchemy.create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")
    with engine.connect() as connection:
        if not engine.dialect.has_table(connection, ComparisonLabels.__tablename__):
            meta = sqlalchemy.MetaData(engine)
            # Create a table with the appropriate Columns
            sqlalchemy.Table(
                ComparisonLabels.__tablename__,
                meta,
                Column("id", String, primary_key=True, default=uuid4_str),
                Column("created", BigInteger, default=epoch_timestamp_ms),
                Column("match", Boolean, nullable=True),
                Column("valid", Boolean),
                Column("image_one", String),
                Column("label_one", String),
                Column("label_one_category", String),
                Column("label_one_x", Float),
                Column("label_one_y", Float),
                Column("label_one_radius", Float),
                Column("image_two", String),
                Column("label_two", String),
                Column("label_two_category", String),
                Column("label_two_x", Float),
                Column("label_two_y", Float),
                Column("label_two_radius", Float),
                Column("user", String),
            )
            # Implement the creation
            meta.create_all()

    return sqlalchemy.orm.sessionmaker(engine)
