import datetime
from typing import Any, <PERSON>, Optional, Tuple

from sqlalchemy import func, or_, and_
import sqlalchemy

from tool.perf_tracker import PerfCategory, duration_perf_recorder

from .tables import (
    ComparisonLabels,
    ComparisonPrioritizations,
    ComparisonReviewPrioritization,
    ComparisonReviews,
    Dataset,
    get_local_sessionmaker,
    get_veselka_sessionmaker,
)


def get_label_review_counts() -> Tuple[int, int]:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_label_review_counts"):
        sessionmaker = get_local_sessionmaker()
        with sessionmaker() as session:
            label_count = session.query(func.count(ComparisonLabels.id)).scalar()
            review_count = session.query(func.count(ComparisonReviews.id)).scalar()

        return label_count, review_count


def get_label_count(user_filter: Optional[str] = None) -> int:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_label_count"):
        with get_local_sessionmaker()() as session:
            query = session.query(func.count(ComparisonLabels.id))
            if user_filter:
                query = query.filter(ComparisonLabels.user.contains(user_filter))
            counts = query.scalar()
        return counts


def get_user_scores() -> List[Tuple[str, Optional[bool], bool, int]]:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_user_scores"):
        with get_local_sessionmaker()() as session:
            grouped_values = (
                session.query(
                    ComparisonLabels.user,
                    ComparisonLabels.match,
                    ComparisonLabels.valid,
                    func.count(ComparisonLabels.id),
                )
                .group_by(ComparisonLabels.user, ComparisonLabels.match, ComparisonLabels.valid)
                .all()
            )
        return grouped_values


def get_label_items(
    user: str,
    match: Optional[bool],
    suspicious: bool,
    valid: bool,
    skip_reviewed: bool,
    label_id: str,
    first_category: str,
    second_category: str,
    start_date: Optional[datetime.date] = None,
    end_date: Optional[datetime.date] = None,
):
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_label_items"):
        with get_local_sessionmaker()() as session:
            query = session.query(ComparisonLabels).filter(ComparisonLabels.valid == valid)

            if valid and match is not None:
                query = query.filter(ComparisonLabels.match == match)

            if user != "":
                query = query.filter(ComparisonLabels.user.contains(user))

            if label_id != "":
                query = query.filter(ComparisonLabels.id.startswith(label_id))

            if first_category != "":
                query = query.filter(ComparisonLabels.label_one_category == first_category)

            if second_category != "":
                query = query.filter(ComparisonLabels.label_two_category == second_category)

            if start_date != None:
                start_time_ms = int(datetime.datetime.combine(start_date, datetime.time()).timestamp() * 1000)
                query = query.filter(ComparisonLabels.created > start_time_ms)
            
            if end_date != None:
                end_time_ms = int(datetime.datetime.combine(end_date, datetime.time()).timestamp() * 1000)
                query = query.filter(ComparisonLabels.created < end_time_ms)

            if suspicious:
                if match:
                    query = query.filter(ComparisonLabels.label_one_category != ComparisonLabels.label_two_category)
                else:
                    query = query.filter(ComparisonLabels.label_one_category == ComparisonLabels.label_two_category)

            if skip_reviewed:
                query = query.outerjoin(ComparisonReviews, and_(ComparisonLabels.id == ComparisonReviews.label_id, ComparisonReviews.valid == sqlalchemy.sql.expression.true())).filter(
                    or_(ComparisonReviews.id == None, ComparisonReviews.id == "")
                )

            all_items = query.order_by(ComparisonLabels.created).all()
        return all_items


def get_dataset_query(dataset_id: str) -> List[Tuple[Any, ...]]:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_dataset_query"):
        with get_veselka_sessionmaker()() as session:
            query = session.query(
                Dataset.id,
                Dataset.name,
                Dataset.created,
                Dataset.train,
                Dataset.validation,
                Dataset.test,
                Dataset.parent_id,
            ).filter(Dataset.id == dataset_id)

            return query.all()


def add_labels(comparison_labels: List[ComparisonLabels]) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "add_labels"):
        with get_local_sessionmaker()() as session:
            session.bulk_save_objects(comparison_labels)
            session.commit()


def update_prioritization_pair_done(pair_id: str) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "update_prioritization_pair_done"):
        with get_local_sessionmaker()() as session:
            session.query(ComparisonPrioritizations).filter(ComparisonPrioritizations.id == pair_id).update(
                {"done": True}
            )
            session.commit()
            
            
def update_review_prioritization_done(label_id: str) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "update_review_prioritization_done"):
        with get_local_sessionmaker()() as session:
            session.query(ComparisonReviewPrioritization).filter(ComparisonReviewPrioritization.label_id == label_id).update(
                {"done": True}
            )
            session.commit()


def update_label_match_valid(id: str, match: Optional[bool], valid: bool) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "update_label_match_valid"):
        with get_local_sessionmaker()() as session:
            session.query(ComparisonLabels).filter(ComparisonLabels.id == id).update({"match": match, "valid": valid})
            session.commit()


def add_review(comparison_review: ComparisonReviews) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "add_review"):
        with get_local_sessionmaker()() as session:
            session.add(comparison_review)
            session.commit()


def get_comparison_prioritizations() -> List[ComparisonPrioritizations]:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_comparison_prioritizations"):
        with get_local_sessionmaker()() as session:
            return (
                session.query(ComparisonPrioritizations)
                .filter(ComparisonPrioritizations.done == False)
                .order_by(ComparisonPrioritizations.created)
                .all()
            )

def get_comparison_review_prioritizations() -> List[ComparisonReviewPrioritization]:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "get_comparison_review_prioritizations"):
        with get_local_sessionmaker()() as session:
            return (
                session.query(ComparisonReviewPrioritization)
                .filter(ComparisonReviewPrioritization.done == False)
                .order_by(ComparisonReviewPrioritization.created)
                .all()
            )

def add_prioritizations(prioritizations: List[ComparisonPrioritizations]) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "add_prioritizations"):
        with get_local_sessionmaker()() as session:
            session.add_all(prioritizations)
            session.commit()
            
def add_review_prioritizations(prioritizations: List[ComparisonReviewPrioritization]) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "add_review_prioritizations"):
        with get_local_sessionmaker()() as session:
            session.add_all(prioritizations)
            session.commit()
            
def add_reviewed_labels(labels: List[ComparisonLabels], reviews: List[ComparisonReviews]) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "add_reviewed_labels"):
        with get_local_sessionmaker()() as session:
            session.add_all(labels)
            session.add_all(reviews)
            session.commit()
            
def remove_reviews(label_ids: List[str]) -> None:
    with duration_perf_recorder(PerfCategory.DB_QUERIES, "remove_reviews"):
        with get_local_sessionmaker()() as session:
            comparison_reviews = session.query(ComparisonReviews).where(ComparisonReviews.label_id.in_(label_ids)).all()
            if comparison_reviews is not None:
                for comparison_review in comparison_reviews:
                    comparison_review.valid = False
                
            session.commit()
            

