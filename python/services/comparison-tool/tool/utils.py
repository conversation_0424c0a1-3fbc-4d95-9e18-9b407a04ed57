import datetime
import json
import logging
import os
import random
import shutil
import threading
import time
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple, cast

import boto3
import numpy as np
import pandas as pd
import requests
import streamlit as st
from scipy.spatial import cKDTree

from constants import (
    DATA_DIR,
    DB_NAME,
    DEFAULT_DATASET,
    S3_BUCKET,
    S3_KEY_PREFIX,
    SIZE_MULTIPLE,
    SOURCE_PRIORITIZED,
    SOURCE_SAMPLED,
    WEED_CATEGORIES,
    VESELKA_URL,
    DEVMODE,
)
from tool.perf_tracker import PerfCategory, duration_perf_recorder

from .abstractions.cache import S3Cache
from .db.queries import (
    add_labels,
    add_prioritizations,
    add_review,
    add_review_prioritizations,
    add_reviewed_labels,
    get_comparison_prioritizations,
    get_comparison_review_prioritizations,
    get_dataset_query,
    get_label_count,
    get_label_items,
    get_label_review_counts,
    get_user_scores,
    remove_reviews,
    update_label_match_valid,
    update_prioritization_pair_done,
    update_review_prioritization_done,
)
from .db.tables import (
    ComparisonLabels,
    ComparisonPrioritizations,
    ComparisonReviewPrioritization,
    ComparisonReviews,
    uuid4_str,
)

CARBON_ML_PREFIX = "s3://carbon-ml/"

logging.basicConfig(level=logging.INFO)

class Datapoint:
    def __init__(self, image_id: str, x: float, y: float, radius: float, category: str, label_id: str):
        self.image_id = image_id
        self.x = x
        self.y = y
        self.radius = radius
        self.category = category
        self.label_id = label_id


class Pair:
    def __init__(self, datapoint_one: Datapoint, datapoint_two: Datapoint, pair_id: Optional[str] = None):
        self.datapoint_one = datapoint_one
        self.datapoint_two = datapoint_two
        self.pair_id = pair_id


def create_point_dict(category_size_options: Dict[str, Any]) -> Dict[str, Dict[str, List[Any]]]:
    weed_categories = set(WEED_CATEGORIES)
    metadata_dict = defaultdict(lambda: defaultdict(list))

    for category, values in category_size_options.items():
        for _, annotations in values.items():
            for annotation in annotations:
                if annotation.get("annotation_type", "point") == "point" and annotation["confidence"] == 2:
                    if category not in weed_categories:
                        metadata_dict[annotation["image_id"]]["crop"].append(annotation)
                    else:
                        metadata_dict[annotation["image_id"]]["weed"].append(annotation)
    
    logging.info(f"Number of images: {len(metadata_dict)}")

    return metadata_dict


def a_and_b_within_size_limit(r_a: float, r_b: float, size_multiple: float) -> bool:
    min_size = r_a / size_multiple
    max_size = r_a * size_multiple
    
    return r_b >= min_size and r_b <= max_size


def create_overlapped_points_dict(
    point_a_list: List[Dict[str, Any]], point_b_list: List[Dict[str, Any]]
) -> Tuple[List[int], List[int]]:
    overlapped_matrix = np.zeros(
        (len(point_a_list), len(point_b_list)), dtype=bool
    )  # overlapped_matrix contains False (non-overlapped or invalid), True (overlapped) only.

    for idx_a, point_a_info in enumerate(point_a_list):
        for idx_b, point_b_info in enumerate(point_b_list):
            assert (
                point_a_info != point_b_info
            ), "Invalid comparison: point_a_info cannot be point_b_info. One should be a crop, and the other should be a weed."
            flag = does_square_a_overlap_center_b(
                point_a=point_a_info, point_b=point_b_info
            )  # flag == True denotes overlapped, while flag == False denotes non-overlapped.
            flag = flag and a_and_b_within_size_limit(point_a_info["radius"], point_b_info["radius"], SIZE_MULTIPLE)
            overlapped_matrix[idx_a, idx_b] = flag

    points_index_with_overlap = list(np.where(overlapped_matrix.any(axis=1))[0])
    points_index_without_overlap = list(np.where(~overlapped_matrix.any(axis=1))[0])

    return points_index_with_overlap, points_index_without_overlap


def get_pair_overlapped_points(point_dict: Dict[str, Dict[str, List[Any]]], category: str) -> Tuple[Any, Any]:
    with duration_perf_recorder(PerfCategory.UTILS, "get_pair_categories_geohash_size"):
        for _ in range(10):
            image_choice = random.choice(list(point_dict.keys()))  # Get a random image.
            point_a_category = category
            point_b_category = "weed" if point_a_category == "crop" else "crop"
            point_a_list = point_dict[image_choice][point_a_category]
            point_b_list = point_dict[image_choice][point_b_category]

            points_index_with_overlap, points_index_without_overlap = create_overlapped_points_dict(
                point_a_list=point_a_list, point_b_list=point_b_list
            )  # Find the crops/weeds that overlap with other weeds/crops.

            use_two_overlap = random.random() < 0.5
            if use_two_overlap and len(points_index_with_overlap) > 1:
                (index_one, index_two) = random.sample(points_index_with_overlap, 2)
            elif len(points_index_with_overlap) >= 1 and len(points_index_without_overlap) >= 1:
                (index_one, index_two) = random.choice(points_index_with_overlap), random.choice(
                    points_index_without_overlap
                )
            else:
                continue

            datapoints = [
                Datapoint(
                    image_id=point_a_list[idx]["image_id"],
                    x=point_a_list[idx]["x"],
                    y=point_a_list[idx]["y"],
                    radius=point_a_list[idx]["radius"],
                    category=point_a_list[idx]["label"],
                    label_id=point_a_list[idx].get("label_id", ""),
                )
                for idx in [index_one, index_two]
            ]

            return Pair(*datapoints)

        return None


def does_square_a_overlap_center_b(point_a: Dict[str, Any], point_b: Dict[str, Any], square_length: int = 400) -> bool:
    """
    This method should return true if the 400x400 square centered around point_a overlaps with the center of point_b.
    """
    x_min, x_max = point_a["x"] - square_length // 2, point_a["x"] + square_length // 2
    y_min, y_max = point_a["y"] - square_length // 2, point_a["y"] + square_length // 2

    return cast(bool, x_min <= point_b["x"] <= x_max and y_min <= point_b["y"] <= y_max)


def get_latest_veselka_labeling_dataset_id():
    if DEVMODE:
        return DEFAULT_DATASET
    response = requests.get(
        f"{VESELKA_URL}/internal/comparison_labeling_dataset?valid=true",
    )
    assert response.ok, response.status_code
    comparison_labeling_dataset_json = cast(Dict[str, Any], response.json())
    latest_valid_dataset_id = comparison_labeling_dataset_json.get("dataset_id", None)
    logging.info(f"Get latest veselka labeling dataset_id: {latest_valid_dataset_id}.")

    return latest_valid_dataset_id if latest_valid_dataset_id is not None else DEFAULT_DATASET




def get_dataset(dataset_id: str) -> List[Dict[str, Any]]:
    with duration_perf_recorder(PerfCategory.UTILS, "get_dataset"):
        df = pd.DataFrame(
            get_dataset_query(dataset_id), columns=["id", "name", "created", "train", "validation", "test", "parent_id"]
        )
        dataset_jsons = []
        for dataset_type in ["test", "train", "validation"]:
            dataset_key = df[df["id"] == dataset_id][dataset_type].iloc[0]
            logging.warning(f"Attempting to retrieve {dataset_key}")

            if dataset_key.startswith(CARBON_ML_PREFIX):
                dataset_key = dataset_key[len(CARBON_ML_PREFIX) :]
            with S3Cache("carbon-ml", dataset_key) as f:
                dataset_json = json.load(f)

            dataset_jsons.append(dataset_json)

        return dataset_jsons


def in_bounds(x, y, height, width, border_to_extract_px) -> bool:
    return (
        x > border_to_extract_px
        and x < (width - border_to_extract_px)
        and y > border_to_extract_px
        and y < (height - border_to_extract_px)
    )


def load_annotations_images_categories(
    dataset_jsons: List[Dict[str, Any]],
    buckets: List[int],
    neighbors_to_remove_px: float = 100,
):
    with duration_perf_recorder(PerfCategory.UTILS, "load_annotations_images_categories"):
        size_category_options = {str(size): defaultdict(list) for size in buckets[:-1]}
        category_size_options = defaultdict(dict)
        images_dict = {}
        category_dict = {}

        for json_data in dataset_jsons:
            annotations = json_data["annotations"]

            images = json_data["images"]
            images_dict.update({image["id"]: image for image in images})
            categories = json_data["categories"]
            category_dict.update({cat["id"]: cat for cat in categories})

            images_to_annotations = defaultdict(list)

            for annotation in annotations:
                if annotation["annotation_type"] == "point":
                    images_to_annotations[annotation["image_id"]].append(annotation)

            def point_query_f(p):
                return (p["x"], p["y"])

            annotations_to_consider = []

            for _, anns in images_to_annotations.items():
                tree = cKDTree([point_query_f(a) for a in anns])
                for point in anns:
                    near_points = tree.query_ball_point(
                        point_query_f(point), neighbors_to_remove_px, return_length=True
                    )
                    if (
                        near_points == 1
                    ):  # One item will be the item in question, so we want to know if that's the only close point
                        annotations_to_consider.append(point)

            for annotation in annotations_to_consider:
                if annotation["annotation_type"] == "point" and annotation["confidence"] == 2:
                    for i in range(len(buckets) - 1):
                        min_size = (200 / 25.4) * buckets[i]
                        max_size = (200 / 25.4) * buckets[i + 1]
                        images_item_uri = images_dict[annotation["image_id"]]["uri"]

                        # TODO [Raven] we're assuming slayer height/width because height/width
                        # isn't currently being passed in dataset. We can change this to read
                        # from height/width fields in the future, once datasets store that
                        height = 3000
                        width = 4096
                        if "bud" in images_item_uri:
                            height = 4096
                            width = 3000

                        if (
                            annotation["radius"] >= min_size
                            and annotation["radius"] < max_size
                            and in_bounds(annotation["x"], annotation["y"], height, width, annotation["radius"])
                        ):
                            category = category_dict[annotation["category_id"]]["name"]
                            if int(annotation["radius"]) not in category_size_options[category]:
                                category_size_options[category][int(annotation["radius"])] = []
                            category_size_options[category][int(annotation["radius"])].append(annotation)

        return size_category_options, images_dict, category_size_options


def extract_size_category_options_from_file(size_category_filepath):
    logging.debug(f"Grabbing size category options from file {size_category_filepath}")
    with st.spinner(f"Extracting data from size_category file {size_category_filepath}, please wait..."):
        with duration_perf_recorder(PerfCategory.UTILS, "extract_size_category_options_from_file"):
            if os.path.exists(size_category_filepath):
                with open(size_category_filepath) as f:
                    item = json.load(f)

                return item.get("size_category_options"), item["images_dict"], item.get("category_size_options")


def extract_size_category_options(
    dataset,
    size_category_filepath,
    buckets: List[int],
) -> List[List[Tuple[Dict[str, Any]]]]:
    """
    Extracts a list of pairs. This list is indexed as buckets is,
    ie the item at the first index corresponds to the first bucket, etc.
    """
    with duration_perf_recorder(PerfCategory.UTILS, "extract_size_category_options"):
        size_category_options, images_dict, category_size_options = load_annotations_images_categories(dataset, buckets)
        combo = {
            "size_category_options": size_category_options,
            "images_dict": images_dict,
            "category_size_options": category_size_options,
        }

        with open(size_category_filepath, "w") as f:
            json.dump(combo, f)

        dataset_id_filename = size_category_filepath.split("/")[-1]
        s3 = boto3.client("s3")
        with open(size_category_filepath, "rb") as f:
            s3.upload_fileobj(f, S3_BUCKET, os.path.join(S3_KEY_PREFIX, f"size_category_options/{dataset_id_filename}"))

        return size_category_options, images_dict, category_size_options


def get_pair(size_category_options, size):
    category_options = size_category_options[str(size)]

    logging.debug(f"For size {size} we have {len(category_options)} categories.")

    with duration_perf_recorder(PerfCategory.UTILS, "get_pair"):
        prob_use_same = random.random()
        if prob_use_same < 0.5 or len(list(category_options)) <= 1:
            key = random.choice(list(category_options.keys()))
            key_one = key
            key_two = key

            if len(category_options[key]) >= 2:
                items = random.sample(category_options[key], k=2)
                one = items[0]
                two = items[1]
            else:
                keys = random.sample(list(category_options.keys()), k=2)
                key_one = keys[0]
                key_two = keys[1]
                one = random.choice(category_options[key_one])
                two = random.choice(category_options[key_two])
        else:
            keys = random.sample(list(category_options.keys()), k=2)
            key_one = keys[0]
            key_two = keys[1]
            one = random.choice(category_options[key_one])
            two = random.choice(category_options[key_two])

        return (one, two, key_one, key_two)


def get_pair_different_sizes(category_size_options) -> Pair:
    with duration_perf_recorder(PerfCategory.UTILS, "get_pair_different_sizes"):
        for _ in range(10):
            use_same_category = random.random() < 0.5
            if use_same_category:
                category = random.choice(list(category_size_options.keys()))
                category_one = category
                category_two = category
                size_options = category_size_options[category]
                first_item_size = random.choice(list(size_options.keys()))
                first_item = random.choice(size_options[first_item_size])
            else:
                categories = random.sample(category_size_options.keys(), k=2)
                category_one = categories[0]
                category_two = categories[1]
                size_options = category_size_options[category_two]
                first_item_size = random.choice(list(category_size_options[category_one].keys()))
                first_item = random.choice(category_size_options[category_one][first_item_size])

            min_size = int(first_item_size) / SIZE_MULTIPLE
            max_size = int(first_item_size) * SIZE_MULTIPLE
            sampleable_items = []
            for size, options in size_options.items():
                if int(size) >= min_size and int(size) <= max_size:
                    sampleable_items.extend(options)

            if len(sampleable_items) > 1:
                second_item = random.choice(sampleable_items)
                datapoint_one = Datapoint(
                    image_id=first_item["image_id"],
                    x=first_item["x"],
                    y=first_item["y"],
                    radius=first_item["radius"],
                    category=category_one,
                    label_id=first_item.get("label_id", ""),
                )
                datapoint_two = Datapoint(
                    image_id=second_item["image_id"],
                    x=second_item["x"],
                    y=second_item["y"],
                    radius=second_item["radius"],
                    category=category_two,
                    label_id=second_item.get("label_id", ""),
                )

                return Pair(datapoint_one, datapoint_two)

        return None


def get_second_item_candidates(category_size_options, second_category, first_item_size) -> List[Dict[str, Any]]:
    with duration_perf_recorder(PerfCategory.UTILS, "get_second_item_candidates"):
        sampleable_items = []
        second_item_size_options = category_size_options[second_category]
        for size, options in second_item_size_options.items():
            if a_and_b_within_size_limit(float(first_item_size), float(size), SIZE_MULTIPLE):
                sampleable_items.extend(options)

        return sampleable_items


def get_pair_categories_geohash_size(category_size_options, image_dict) -> Pair:
    with duration_perf_recorder(PerfCategory.UTILS, "get_pair_categories_geohash_size"):
        for _ in range(10):
            use_weed = random.random() < 0.9
            sampleable_items = []
            if use_weed:
                first_category = random.choice(WEED_CATEGORIES)
                first_item_size_options = category_size_options[first_category]
                first_item_size = random.choice(list(first_item_size_options.keys()))
                first_item = random.choice(first_item_size_options[first_item_size])

                pair_with_same_type = random.random() < 0.5
                if pair_with_same_type:
                    second_category = first_category
                    sampleable_items_unfiltered = get_second_item_candidates(
                        category_size_options, second_category, first_item_size
                    )
                    pair_with_same_geo6 = random.random() < 0.5
                    first_item_geo = image_dict[first_item["image_id"]]["geohash"]
                    if pair_with_same_geo6 and first_item_geo is not None:
                        first_geo = first_item_geo[:6]

                        sampleable_items = [
                            item
                            for item in sampleable_items_unfiltered
                            if image_dict[item["image_id"]]["geohash"] is not None
                            and image_dict[item["image_id"]]["geohash"][:6] == first_geo
                        ]
                    else:
                        sampleable_items = sampleable_items_unfiltered
                else:
                    pair_with_weed = random.random() < 0.5
                    if pair_with_weed:
                        second_category = random.choice([item for item in WEED_CATEGORIES if item != first_category])
                    else:
                        crop_list = [item for item in category_size_options.keys() if item not in WEED_CATEGORIES]
                        second_category = random.choice(crop_list)

                    # Find items in size constraint
                    sampleable_items = get_second_item_candidates(
                        category_size_options, second_category, first_item_size
                    )
            else:
                use_same_category = random.random() < 0.5
                if use_same_category:
                    category = random.choice(list(category_size_options.keys()))
                    first_category = category
                    second_category = category
                    size_options = category_size_options[category]
                    first_item_size = random.choice(list(size_options.keys()))
                    first_item = random.choice(size_options[first_item_size])
                else:
                    categories = random.sample(category_size_options.keys(), k=2)
                    first_category = categories[0]
                    second_category = categories[1]
                    size_options = category_size_options[second_category]
                    first_item_size = random.choice(list(category_size_options[first_category].keys()))
                    first_item = random.choice(category_size_options[first_category][first_item_size])

                sampleable_items = get_second_item_candidates(category_size_options, second_category, first_item_size)

            if len(sampleable_items) > 1:
                second_item = random.choice(sampleable_items)
                datapoint_one = Datapoint(
                    image_id=first_item["image_id"],
                    x=first_item["x"],
                    y=first_item["y"],
                    radius=first_item["radius"],
                    category=first_category,
                    label_id=first_item.get("label_id", ""),
                )
                datapoint_two = Datapoint(
                    image_id=second_item["image_id"],
                    x=second_item["x"],
                    y=second_item["y"],
                    radius=second_item["radius"],
                    category=second_category,
                    label_id=second_item.get("label_id", ""),
                )

                return Pair(datapoint_one, datapoint_two)

        return None


def save_predictions(
    comparison_labels: List[ComparisonLabels],
    db_lock,
):
    with duration_perf_recorder(PerfCategory.UTILS, "save_predictions"):
        with db_lock:
            add_labels(comparison_labels)


def mark_prioritization_pair_done(pair_id: str, db_lock: threading.Lock) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "mark_prioritization_pair_done"):
        with db_lock:
            update_prioritization_pair_done(pair_id)


def modify_predictions(id: str, match: Optional[bool], valid: bool, db_lock: threading.Lock) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "modify_predictions"):
        with db_lock:
            update_label_match_valid(id=id, match=match, valid=valid)


def save_review(label_id: str, user: str, db_lock: threading.Lock) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "save_review"):
        with db_lock:
            add_review(
                ComparisonReviews(
                    label_id=label_id,
                    user=user,
                )
            )

            update_review_prioritization_done(label_id)


def todo_priorities(db_lock: threading.Lock) -> List[Dict[str, Any]]:
    with duration_perf_recorder(PerfCategory.UTILS, "todo_priorities"):
        with db_lock:
            todo = get_comparison_prioritizations()

        return [item.to_dict() for item in todo]


def todo_review_priorities(db_lock: threading.Lock) -> List[Dict[str, Any]]:
    with duration_perf_recorder(PerfCategory.UTILS, "todo_review_priorities"):
        with db_lock:
            todo = get_comparison_review_prioritizations()

        return [item.to_dict() for item in todo]


class PreprocessedPairs:
    def __init__(self, size_category_filepath: str, point_dict: Dict[str, Dict[str, List[Any]]]) -> None:
        self._lock = threading.Lock()
        self._filepath = size_category_filepath
        self._pairs = []
        self._prioritized_pairs = []
        self._image_dict = {}
        self._num_pairs = 100
        self._point_dict = point_dict
        self._load_pairs()

    @property
    def images_dict(self) -> Dict[str, str]:
        return self._image_dict

    def _load_pairs(self) -> None:
        logging.debug(f"Loading {self._num_pairs} from {self._filepath}")
        counts = defaultdict(int)
        with duration_perf_recorder(PerfCategory.UTILS, "PreprocessedPairs::_load_pairs"):
            _, self._image_dict, category_size_options = extract_size_category_options_from_file(self._filepath)

            for _ in range(self._num_pairs // 2):  # Sample 50% based on category_size.
                pair = get_pair_categories_geohash_size(
                    category_size_options=category_size_options, image_dict=self._image_dict
                )
                if pair is not None:
                    self._pairs.append(pair)
                    counts["categories_geohash_size"] += 1
            for _ in range(self._num_pairs // 4):  # Sample 25% based on overlapped crops.
                pair = get_pair_overlapped_points(point_dict=self._point_dict, category="crop")
                if pair is not None:
                    self._pairs.append(pair)
                    counts["crop_with_overlap"] += 1
            for _ in range(self._num_pairs // 4):  # Sample 25% based on overlapped weeds.
                pair = get_pair_overlapped_points(point_dict=self._point_dict, category="weed")
                if pair is not None:
                    counts["weed_with_overlap"] += 1
                    self._pairs.append(pair)
            
            if len(self._pairs) > 0:        
                logging.info(f"Counts of pairs and sources {counts}")
                random.shuffle(self._pairs)  # Shuffle the pairs.

    def load_prioritized_pairs(self, todo: List[Dict[str, Any]]) -> None:
        logging.debug(f"Loading {len(todo)} prioritized_pairs")
        with duration_perf_recorder(PerfCategory.UTILS, "PreprocessedPairs::load_prioritized_pairs"):
            with self._lock:
                self._prioritized_pairs = []

                for item in todo:
                    pair = Pair(
                        datapoint_one=Datapoint(
                            image_id=str(item["image_one"]),
                            x=float(item["label_one_x"]),
                            y=float(item["label_one_y"]),
                            category=str(item["label_one_category"]),
                            radius=float(item["label_one_radius"]),
                            label_id=item["label_one"] if item["label_one"] is not None else "",
                        ),
                        datapoint_two=Datapoint(
                            image_id=str(item["image_two"]),
                            x=float(item["label_two_x"]),
                            y=float(item["label_two_y"]),
                            category=str(item["label_two_category"]),
                            radius=float(item["label_two_radius"]),
                            label_id=item["label_two"] if item["label_two"] is not None else "",
                        ),
                        pair_id=item["id"],
                    )
                    self._prioritized_pairs.append(pair)

    def pop(self) -> Tuple[Pair, str]:
        with duration_perf_recorder(PerfCategory.UTILS, "PreprocessedPairs::pop"):
            with self._lock:
                if len(self._prioritized_pairs) > 0:
                    return self._prioritized_pairs.pop(), SOURCE_PRIORITIZED
                if len(self._pairs) == 0:
                    self._load_pairs()

                return self._pairs.pop(), SOURCE_SAMPLED


def add_prioritization_to_db(db_lock: threading.Lock, username: str, items: List[Tuple[Datapoint, Datapoint]]) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "add_prioritization_to_db"):
        with db_lock:
            add_prioritizations(
                [
                    ComparisonPrioritizations(
                        user=username,
                        image_one=item[0].image_id,
                        label_one=item[0].label_id,
                        label_one_category=item[0].category,
                        label_one_x=item[0].x,
                        label_one_y=item[0].y,
                        label_one_radius=item[0].radius,
                        image_two=item[1].image_id,
                        label_two=item[1].label_id,
                        label_two_category=item[1].category,
                        label_two_x=item[1].x,
                        label_two_y=item[1].y,
                        label_two_radius=item[1].radius,
                    )
                    for item in items
                ]
            )
            
def add_reviewed_labels_to_db(db_lock: threading.Lock, username: str, items: List[Tuple[Datapoint, Datapoint, bool]]) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "add_reviewed_labels_to_db"):
        comparison_labels = [
            ComparisonLabels(
                id=uuid4_str(),
                user=username,
                image_one=item[0].image_id,
                label_one=item[0].label_id,
                label_one_category=item[0].category,
                label_one_x=item[0].x,
                label_one_y=item[0].y,
                label_one_radius=item[0].radius,
                image_two=item[1].image_id,
                label_two=item[1].label_id,
                label_two_category=item[1].category,
                label_two_x=item[1].x,
                label_two_y=item[1].y,
                label_two_radius=item[1].radius,
                valid=True,
                match=item[2],
                source="manually_labeled_and_reviewed",
            ) for item in items
        ]
        comparison_reviews = [
            ComparisonReviews(
                label_id=comparison_label.id, user=username
            ) for comparison_label in comparison_labels
        ]
        with db_lock:
            add_reviewed_labels(
                comparison_labels, comparison_reviews
            )



def add_review_prioritization_to_db(db_lock: threading.Lock, label_ids: List[str]) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "add_review_prioritization_to_db"):
        with db_lock:
            add_review_prioritizations([ComparisonReviewPrioritization(label_id=label_id) for label_id in label_ids])


def remove_reviews_from_db(db_lock: threading.Lock, label_ids: List[str]) -> None:
    with duration_perf_recorder(PerfCategory.UTILS, "remove_reviews_from_db"):
        with db_lock:
            remove_reviews(label_ids)


def check_and_sync_db(db_lock: threading.Lock):
    logging.info("Starting sync loop")
    db_path = os.path.join(DATA_DIR, DB_NAME)
    tmp_db_path = os.path.join(DATA_DIR, f"tmp_{DB_NAME}")
    s3 = boto3.client("s3")
    while True:
        try:
            previous_label_count, previous_review_count = get_label_review_counts()
            logging.debug("Success initially getting counts")
            break
        except Exception as e:
            logging.debug(f"Error getting counts, {e}")
            time.sleep(10)

    while True:
        time.sleep(60)

        current_label_count, current_review_count = get_label_review_counts()
        if (current_label_count != previous_label_count) or (current_review_count != previous_review_count):
            logging.debug("Uploading DB to S3")
            copied_file = False
            with db_lock:
                shutil.copyfile(db_path, tmp_db_path)
                copied_file = True
            if copied_file:
                with open(tmp_db_path, "rb") as f:
                    s3.upload_fileobj(f, S3_BUCKET, os.path.join(S3_KEY_PREFIX, DB_NAME))
        previous_label_count = current_label_count
        previous_review_count = current_review_count


def check_and_clean_images(display_lock: threading.Lock):
    logging.info("Starting cleanup loop")
    media_path = os.path.join(DATA_DIR, "media")

    while True:
        files_to_remove = []
        now = time.time()

        with display_lock:
            for (path, _, files) in os.walk(media_path):
                for filename in files:
                    filepath = os.path.join(path, filename)
                    mod_time = os.path.getmtime(filepath)

                    # If now - mod_time is greater than three hours, delete
                    if ((now - mod_time) / 60) > 3 * 60 and os.path.splitext(filepath)[1] == ".png":
                        files_to_remove.append(filepath)

            for filepath in files_to_remove:
                os.remove(filepath)

        logging.info(f"Removed {len(files_to_remove)} files")

        time.sleep(60 * 60)


def download(filepath, bucket_name, key):
    bucket = boto3.resource("s3").Bucket(bucket_name)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    bucket.download_file(key, filepath)


def get_count(db_lock: threading.Lock, user_filter: Optional[str] = None):
    db_path = os.path.join(DATA_DIR, DB_NAME)
    if os.path.exists(db_path):
        with db_lock:
            counts = get_label_count(user_filter)

    return counts


def get_user_scoreboard(db_lock: threading.Lock):
    with duration_perf_recorder(PerfCategory.UTILS, "get_user_scoreboard"):
        db_path = os.path.join(DATA_DIR, DB_NAME)
        if os.path.exists(db_path):
            with db_lock:
                grouped_values = get_user_scores()

        users = {}
        for user, match, valid, count in grouped_values:
            if user not in users:
                users[user] = {}
            if valid:
                users[user]["match_true" if match else "match_false"] = count
            else:
                users[user]["invalid"] = count

        user_tuples = [
            (
                key,
                value.get("match_true", 0),
                value.get("match_false", 0),
                (value.get("match_true", 0) + value.get("match_false", 0)),
                value.get("invalid", 0),
            )
            for key, value in users.items()
        ]

        df = pd.DataFrame(columns=["User", "Match True", "Match False", "Total Matches", "Invalid"], data=user_tuples)

        return df


def message_reviews_to_dict(review_priorities: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    return {review["label_id"]: review for review in review_priorities}


def get_labels_for_reviews(
    db_lock: threading.Lock,
    user: str,
    match: Optional[bool],
    suspicious: bool,
    valid: bool,
    skip_reviewed: bool,
    label_id: str,
    first_category: str,
    second_category: str,
    start_date: Optional[datetime.date] = None,
    end_date: Optional[datetime.date] = None,
):
    with duration_perf_recorder(PerfCategory.UTILS, "get_labels"):
        db_path = os.path.join(DATA_DIR, DB_NAME)
        if os.path.exists(db_path):
            with db_lock:
                all_items_unreviewed_items = get_label_items(
                    user, match, suspicious, valid, skip_reviewed, label_id, first_category, second_category, start_date, end_date
                )

        review_priorities = todo_review_priorities(db_lock)
        id_to_review_status = message_reviews_to_dict(review_priorities)
        labels_to_review = [item.to_dict() for item in all_items_unreviewed_items]

        prioritized_labels_to_review = []
        unprioritized_labels_to_review = []
        for label in labels_to_review:
            if label["id"] in id_to_review_status:
                label["review_created"] = id_to_review_status[label["id"]]["created"]
                prioritized_labels_to_review.append(label)
            else:
                unprioritized_labels_to_review.append(label)

        logging.info(
            f"Number of prioritized {len(prioritized_labels_to_review)}, unprioritized {len(unprioritized_labels_to_review)}"
        )

        return sorted(
            [item for item in prioritized_labels_to_review], key=lambda x: x["review_created"], reverse=True
        ) + sorted([item for item in unprioritized_labels_to_review], key=lambda x: x["created"], reverse=True)
