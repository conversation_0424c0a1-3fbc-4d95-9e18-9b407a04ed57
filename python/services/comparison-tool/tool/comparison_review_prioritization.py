import json
import streamlit as st
import threading
from tool.utils import add_review_prioritization_to_db, remove_reviews_from_db, todo_review_priorities
import pandas as pd

def comparison_review_prioritization_page(
    username: str, dataset_id: str, size_category_filepath: str, db_lock: threading.Lock, display_lock: threading.Lock
):
    st.title("Comparison Review Prioritization")
    st.text("""
        This page allows you to prioritize reviews of items based on their label id. Items with the matching label id are put
        at the front of the review queue. This is helpful if you want labelers to double-check their work.
    """)
    
    review_priorities = todo_review_priorities(db_lock)
    
    st.header(f"There are {len(review_priorities)} prioritized reviews left")
    if len(review_priorities) > 0:
        st.dataframe(pd.DataFrame(review_priorities))
        
    uploaded_file = st.file_uploader("Label files", type=['json'])
    label_id = st.text_input("Label IDs")
    ids = None
    if uploaded_file is not None:
        label_contents = json.load(uploaded_file)
        
        st.header(f"Labels uploaded: {len(label_contents['ids'])}")
        
        metadata = label_contents.copy()
        del metadata["ids"]
        st.json(metadata)
        ids = label_contents['ids']
    elif label_id:
        ids = [id.strip() for id in label_id.split(",")]
                
    if ids is not None:
        st.dataframe(pd.DataFrame(ids, columns=["label id"]))
        with st.form("review_submission_form"):
            action = st.radio("Action", ["Reject Reviews", "Reject and Prioritize"])
            
            submit = st.form_submit_button("Submit")
            if submit:
                if action == "Reject Reviews":
                    remove_reviews_from_db(db_lock, ids)
                elif action == "Reject and Prioritize":
                    remove_reviews_from_db(db_lock, ids)
                    add_review_prioritization_to_db(db_lock, ids)
                    
                    st.success(f"Sucessfully added {len(ids)} prioritized reviews. Please navigate away and back to tab to see the count updated")
    