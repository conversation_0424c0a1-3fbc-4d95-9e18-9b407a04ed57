import logging
import threading

import pandas as pd
import streamlit as st

from tool.utils import Datapoint, add_reviewed_labels_to_db
from tool.comparison_prioritization import <PERSON>er<PERSON>abe<PERSON>, HEADERS, make_empty


def verify_and_save_labels_and_reviews(data: pd.DataFrame, db_lock: threading.Lock, username: str):
    records = data.to_dict("records")
    to_commit = []
    for record in records:
        for header in [HeaderLabel("match", bool)] + HEADERS:
            if type(record[header.header]) is None:
                record[header.header] = header.default_value
            try:
                header.expected_type(record[header.header])
            except Exception as e:
                st.warning(
                    f"Error, {header.header}: {type(record[header.header])}, expected {header.expected_type}, {e}"
                )
                raise e
            
        assert record["match"] is not None, "Record must have a match value"
            
        to_commit.append(
            (
                Datapoint(
                    image_id=str(record["image_one"]),
                    label_id=record["label_one"] if record["label_one"] is not None else "",
                    category=str(record["category_one"]),
                    radius=float(record["radius_one"]),
                    x=float(record["x_one"]),
                    y=float(record["y_one"]),
                ),
                Datapoint(
                    image_id=str(record["image_two"]),
                    label_id=record["label_two"] if record["label_two"] is not None else "",
                    category=str(record["category_two"]),
                    radius=float(record["radius_two"]),
                    x=float(record["x_two"]),
                    y=float(record["y_two"]),
                ),
                record["match"]
            )
        )
        
    if len(to_commit) > 0:
        add_reviewed_labels_to_db(db_lock, username, to_commit)        

def add_reviewed_labels_page(
    username: str, dataset_id: str, size_category_filepath: str, db_lock: threading.Lock, display_lock: threading.Lock
):
    st.title("Add Reviewed Labels")
    st.text("""
        This page allows you to add labels with match values. Upon submission it will create labels and reviews for the labeled items
    """)

    initial_rows, headers = make_empty(1, include_match=True)
    uploaded_file = st.file_uploader("Upload a csv file", key="add_review_label_file")
    if uploaded_file is not None:
        initial_rows = pd.read_csv(uploaded_file, dtype={header.header: header.expected_type for header in headers})

    with st.form("add_data", clear_on_submit=True):
        st.write("Add Data")
        data = st.data_editor(initial_rows, num_rows="dynamic")

        submitted = st.form_submit_button("Save")
        if submitted:
            try:
                verify_and_save_labels_and_reviews(data, db_lock=db_lock, username=username)
                    
                st.success("Successfully saved labels")
            except Exception as e:
                st.warning(f"Could not save reviewed labels: {e}")
                logging.error(f"Couldn't save reviewed labels: {e}")
