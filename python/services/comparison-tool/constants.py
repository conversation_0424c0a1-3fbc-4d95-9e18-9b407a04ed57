import os

MIN_SIZE = 350
MIN_ITEM_SIZE = 2.5
VESELKA_URL = os.environ.get("VESELKA_URL", "http://veselka.production.svc.cluster.local:8080")

CONNECTION_STRING = os.getenv("CONNECTION_STRING", None)
DATA_DIR = "/data"

DB_NAME = "comparison_labels.db"

S3_BUCKET = "carbon-cloud-app"
S3_KEY_PREFIX = os.getenv("S3_KEY_PREFIX", "comparison/staging")

SIZE_BUCKETS = [0, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233]
ADMIN_MODE = int(os.getenv("ADMIN_MODE", "0"))
DEFAULT_DATASET = os.getenv("DEFAULT_DATASET", "")
SIZE_MULTIPLE = 1.35

SOURCE_SAMPLED = "sampled"
SOURCE_PRIORITIZED = "prioritized"

WEED_CATEGORIES = ["broadleaf", "purslane", "offshoot", "grass"]

DEVMODE = os.environ.get("DEVMODE", False)
