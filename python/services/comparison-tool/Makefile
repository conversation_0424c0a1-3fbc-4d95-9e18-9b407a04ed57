SHELL = /bin/sh

IMAGE_NAME=comparison-tool
PROJECT_CONTEXT=./services/comparison-tool
DOCKERFILE=Dockerfile
REMOTE_IMAGE=ghcr.io/carbonrobotics/cloud/comparison-tool
VESELKA_URL=http://veselka.testing.svc.cluster.local:8080

MAKEFILE_PATH := $(abspath $(lastword $(MAKEFILE_LIST)))
MAKEFILE_DIR := $(dir $(MAKEFILE_PATH))

ADMIN_MODE ?= 0
S3_KEY_PREFIX ?= comparison/staging
DEFAULT_DATASET ?= 392ea03b-ed21-4826-9e5e-d79bb8a86327
DEVMODE ?= true

dbuild:
	docker buildx build --platform linux/amd64 --build-arg PROJECT_CONTEXT=$(PROJECT_CONTEXT) -f $(DOCKERFILE) -t $(IMAGE_NAME):dev ../../

dlocal:
	docker build --build-arg PROJECT_CONTEXT=$(PROJECT_CONTEXT) -f $(DOCKER<PERSON>LE) -t $(IMAGE_NAME):dev ../../

drun: dbuild
	docker run \
		--rm \
		--network host \
		--env CONNECTION_STRING=$(CONNECTION_STRING) \
		--env ADMIN_MODE=$(ADMIN_MODE) \
		--env S3_KEY_PREFIX=$(S3_KEY_PREFIX) \
		--env DEFAULT_DATASET=$(DEFAULT_DATASET) \
		--env DEVMODE=$(DEVMODE) \
		--env VESELKA_URL=$(VESELKA_URL) \
		-v $(HOME)/.aws:/root/.aws:ro \
		-v $(abspath ./):/app \
		-v /tmp:/data \
		--name comparison_tool \
		$(IMAGE_NAME):dev

test: dbuild
	docker run \
                --rm \
                -v $(abspath ./):/app \
                --name comparison_tool_test \
                $(IMAGE_NAME):dev python -m pytest

lint: dbuild
	docker run \
		--rm \
		--mount type=bind,source=$(MAKEFILE_DIR),target=/app \
		$(IMAGE_NAME):dev \
		isort --profile black --recursive --ignore-whitespace .
	docker run \
		--rm \
		--mount type=bind,source=$(MAKEFILE_DIR),target=/app \
		$(IMAGE_NAME):dev \
		black -l 120 .
	docker run \
		--rm \
		--mount type=bind,source=$(MAKEFILE_DIR),target=/app \
		$(IMAGE_NAME):dev \
		flake8 . 
