import argparse
import datetime
import json
import logging
import os
import random
from typing import Any, Callable, Dict, List, Set, Tuple, Type

import torch

from deeplearning.model_io.tensorrt import load_tensorrt_model
from deeplearning.p2p.data_utils import get_splits
from deeplearning.p2p.datasets import P2PDataset
from deeplearning.p2p.metadata import ImageMetadata, LabelMetadata
from deeplearning.p2p.models.utils import P2PModelOutputFactory
from deeplearning.scripts.p2p.train import PROD_DATA_ROOTS
from deeplearning.scripts.utils.utils import TrainingInfo, add_job_creator_arguments
from deeplearning.tools import download_makannotations_data

DTYPE_TO_COMPARATOR: Dict[Any, Callable[[Any, Any], bool]] = {
    str: lambda x, y: x == y,
    int: lambda x, y: x == y,
    datetime.datetime: lambda x, y: x - y < datetime.timedelta(days=1),
    "geohash": lambda x, y: x[:5] == y[:5],  # Compare first 5 characters of geohash
}


def get_data_from_dataset(
    dataset: P2PDataset, idx: int
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, ImageMetadata]:

    with torch.autocast("cuda", enabled=False):
        perspective, image, label_point, metadata = dataset[idx]

        perspective, image, label, reconstruction_metadata = dataset.preprocess_datapoint(
            perspective, image, label_point, metadata
        )
        return perspective, image, label, metadata


def print_buckets(data: Dict[Any, Any], level: int = 0) -> None:
    indent = "  " * level
    if isinstance(data, dict):
        for key, value in data.items():
            logging.info(f"{indent}{key}: {len(value) if isinstance(value, list) else 'N/A'}")
            print_buckets(value, level + 1)
    elif isinstance(data, list):
        logging.info(f"{indent}List of length: {len(data)}")
    else:
        logging.info(f"{indent}Value: {data}")


def bucket_by(dataset: P2PDataset, indexes: List[int], key: str) -> Dict[str, List[int]]:
    buckets: Dict[Any, List[int]] = {}
    none_bucket = []

    for idx in indexes:

        image_meta = dataset.get_metadata(idx)

        bucket_key = getattr(image_meta, key)
        if bucket_key is None:
            none_bucket.append(idx)
        else:
            found = False
            for bucket in buckets.keys():
                comparator_type = type(bucket_key) if key != "geohash" else "geohash"
                if DTYPE_TO_COMPARATOR[comparator_type](bucket_key, bucket):
                    buckets[bucket].append(idx)
                    found = True
                    break
            if not found:
                buckets[bucket_key] = [idx]
    buckets["None"] = none_bucket
    return buckets


def bucket_by_hierarchical(dataset: P2PDataset, indexes: List[int], keys: List[str]) -> Dict[str, Any]:
    if not keys:
        return {"all": indexes}

    key = keys[0]
    buckets: Dict[str, Any] = bucket_by(dataset, indexes, key)
    for bucket_key, bucket_data in buckets.items():
        buckets[bucket_key] = bucket_by_hierarchical(dataset, bucket_data, keys[1:])
    return buckets


def combine_image_metadata(perspective_image: ImageMetadata, target_image: ImageMetadata) -> ImageMetadata:
    metadata = ImageMetadata(
        image_path=target_image.image_path,
        perspective_paths=perspective_image.perspective_paths,
        label=LabelMetadata(filepath=None, certified=True),
    )
    metadata._perspective_ppi = perspective_image.perspective_ppi
    metadata._ppi = target_image.ppi
    return metadata


def create_fake_examples(
    dataset: P2PDataset,
    data: Dict[Any, Any],
    model: Any,
    output_factory: Type[P2PModelOutputFactory],
    num_examples: int = 100,
    override_attempts: int = 1000000,
    score_threshold: float = 0.5,
) -> List[ImageMetadata]:
    fake_examples: List[ImageMetadata] = []
    duplicates: Set[Tuple[str, str]] = set()  # To avoid duplicates

    created = 0
    attempts = 0
    while created < num_examples:
        curr = data
        while isinstance(curr, dict):
            keys = list(curr.keys())
            if not keys:
                break
            key = random.choice(keys)
            curr = curr[key]

        if curr and len(curr) > 1:
            perspective_idx = random.choice(curr)
            target_idx = random.choice(curr)

            if (perspective_idx, target_idx) in duplicates or perspective_idx == target_idx:
                continue  # Avoid duplicates

            perspective_image, _, _, perspective_image_metadata = get_data_from_dataset(dataset, perspective_idx)
            _, target_image, _, target_image_metadata = get_data_from_dataset(dataset, target_idx)

            perspective_image = perspective_image.unsqueeze(0).cuda()
            target_image = target_image.unsqueeze(0).cuda()
            output = output_factory.unpack(model(perspective_image, target_image))
            duplicates.add((perspective_idx, target_idx))

            if output.score(0) > score_threshold:
                fake_examples.append(combine_image_metadata(perspective_image_metadata, target_image_metadata))
                created += 1

        attempts += 1
        if attempts > override_attempts:
            logging.info(f"Exceeded {override_attempts} attempts to create fake examples. Stopping.")
            break
    return fake_examples


def construct_json_results(data: List[ImageMetadata], path: str) -> None:
    results: Dict[str, Any] = {
        "images": [],
    }
    for item in data:
        results["images"].append(
            {
                "image_path": item.image_path,
                "perspective_paths": item.perspective_paths,
                "label": {"filepath": item.label.filepath, "certified": item.label.certified},
                "ppi": item.ppi,
                "_perspective_ppi": item._perspective_ppi,
            }
        )
    with open(path, "w") as f:
        json.dump(results, f, indent=4, sort_keys=True, default=str)


def main() -> None:  # noqa

    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str, required=True)
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--bucket-by", type=str, default="crop_id", help="Keys to bucket the data by")
    parser.add_argument("--num-examples", type=int, default=1000, help="Number of fake examples to create")
    parser.add_argument(
        "--score-threshold", type=float, default=0.5, help="Score threshold to consider an example as hard"
    )

    add_job_creator_arguments(parser)

    args = parser.parse_args()

    logging.info("Starting hard example generation...")
    # Creating Fake Examples
    data_roots = PROD_DATA_ROOTS
    if args.fast_run:
        data_roots = PROD_DATA_ROOTS[0:1]

    logging.info("Syncing images with masks...")
    download_makannotations_data.sync_images_with_masks(data_roots, dest_path="/data/deeplearning/p2p")
    logging.info("Images synced successfully.")

    data_roots_with_path = [os.path.join("/data/deeplearning/p2p", x) for x in data_roots]

    data: List[ImageMetadata] = get_splits(
        data_roots_with_path, train_split_percentage=1, validation_split_percentage=0
    )["train"]
    dataset = P2PDataset(files=data)

    logging.info(f"Loaded {len(data)} images from the dataset.")

    logging.info("Bucketing data...")
    bucketed_data = bucket_by_hierarchical(
        dataset, list(range(len(dataset))), args.bucket_by.replace(" ", "").split(",")
    )
    logging.info("Buckets created:")

    print_buckets(bucketed_data)

    # Loading the model
    logging.info("Loading model...")
    training_info = TrainingInfo(args.model_id)  # Use Trt Path here

    trt_model_path = training_info.trt_path
    assert trt_model_path is not None, "TensorRT model path is None. Please check the model ID."

    model, model_metadata = load_tensorrt_model(trt_model_path)
    model.set_cache_context(True)

    # Running inference
    output_factory = P2PModelOutputFactory

    fake_examples = create_fake_examples(
        dataset,
        bucketed_data,
        model,
        output_factory,
        num_examples=args.num_examples,
        override_attempts=1000000,
        score_threshold=args.score_threshold,
    )
    logging.info(f"Created {len(fake_examples)} fake examples.")

    # Saving the results in the model folder
    results_path = os.path.join(training_info.data_dir, "hard_examples.json")
    logging.info(f"Saving results to {results_path}")

    construct_json_results(fake_examples, results_path)


if __name__ == "__main__":
    main()
