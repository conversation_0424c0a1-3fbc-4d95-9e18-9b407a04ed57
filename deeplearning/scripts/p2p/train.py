import argparse
import datetime
import logging
import os

from deeplearning.p2p.config import P2PConfig
from deeplearning.p2p.data_utils import get_fake_negative_examples, get_splits
from deeplearning.p2p.models_dict import MODELS_DICT
from deeplearning.p2p.trainer import P2PTrainer
from deeplearning.scripts.utils.utils import (
    CONTAINER_VERSION,
    TrainingInfo,
    add_job_creator_arguments,
    autodetect_pretrained_model,
    generate_model_id,
    run_debuggable,
)
from deeplearning.tools import download_makannotations_data
from deeplearning.utils.trainer import Environment

PROD_DATA_ROOTS = [
    "media/treadkillv5-2021-11-17-p2p",
    "media/treadkill-2021-06-16/p2p",
    "media/mercer50-2021-08-31",
    "media/onions-carzalia-2021-11-06-p2p/success_p2p",
    "media/lambsquarter-treadkill-2021-12-06-fail_p2p",
    "media/lambsquarter-treadkill-2021-12-06-success_p2p",
    "media/onions-carzalia-2021-12-03-p2p",
    "media/crosses-2021-12-15-success_p2p",
    "media/mustard-lamb-test3-success_p2p",
    "media/mustard-lamb-test3-p2p_fail",
    "media/mustard-2022-01-07-p2ps",
    "media/mercer-06-07-2022/p2p",
    "media/grimmway-2022-06-18",
    "media/grimmway-2022-06-20",
    "media/mercer-2022-06-21",
    "media/mercer-2022-06-24-dark",
    "media/p2p",
]


def get_arguments() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument("--fast-run", action="store_true")
    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--pretrained-deepweed-model", type=str, default=None)
    parser.add_argument("--resume-from", type=str, default=None)
    parser.add_argument("--train-batch-size", type=int, default=4)
    parser.add_argument("--split-model-id", type=str, default=None)
    parser.add_argument("--num-balanced-examples", type=int, default=1500)
    parser.add_argument("--autodetect-pretrained-model", action="store_true")
    parser.add_argument("--no-autodetect-pretrained-model", dest="autodetect_pretrained_model", action="store_false")
    parser.add_argument("--model", type=str, default="P2PModelV1")
    parser.add_argument("--fake-negative-model-id", type=str, default=None)
    parser.add_argument("--fake-negative-sample-rate", type=float, default=0.1)
    parser.set_defaults(autodetect_pretrained_model=True)

    add_job_creator_arguments(parser)
    return parser.parse_args()


def main() -> None:  # noqa: C901

    args = get_arguments()

    model_id = args.job_id
    if args.job_id is None:
        model_id = generate_model_id()

    if not args.pipeline_id:
        args.pipeline_id = "89148f4d-51bf-4ed3-85ed-091b67406002"  # Default to p2p pipeline id

    dl_config_dict = {
        **args.dl_config,
    }
    if args.fast_run:
        dl_config_dict["wandb_project"] = "p2p-fast-run"
    dl_config = P2PConfig.from_dict(dl_config_dict)

    trainer = P2PTrainer()

    environment = Environment.DEVELOPMENT
    if args.fast_run:
        environment = Environment.DEVELOPMENT
    elif args.preview:
        environment = Environment.PREVIEW
    elif args.production:
        environment = Environment.PRODUCTION

    if (args.production or args.preview) and not CONTAINER_VERSION:
        assert CONTAINER_VERSION, "CONTAINER_VERSION env var is not defined"
    elif CONTAINER_VERSION:
        if not args.pretrained_deepweed_model and args.autodetect_pretrained_model:
            args.pretrained_deepweed_model = autodetect_pretrained_model(environment)

    pretrained_deepweed_model = None
    if (
        args.pretrained_deepweed_model is not None
        and args.pretrained_deepweed_model != ""
        and args.pretrained_deepweed_model != "none"
    ):
        training_info = TrainingInfo(args.pretrained_deepweed_model)
        pretrained_deepweed_model = training_info.model_weights

    if args.description is None:
        description = f"(train) Development run {datetime.datetime.now()}"
    else:
        description = args.description

    tags = []
    config = {}
    if args.pretrained_deepweed_model:
        tags.append("deepweed-pretrained")
        config["pretrained_deepweed_model_id"] = args.pretrained_deepweed_model

    data_roots = PROD_DATA_ROOTS
    if args.fast_run:
        data_roots = PROD_DATA_ROOTS[0:1]
    download_makannotations_data.sync_images_with_masks(data_roots, dest_path="/data/deeplearning/p2p")

    data_roots_with_path = [os.path.join("/data/deeplearning/p2p", x) for x in data_roots]

    splits = get_splits(
        data_roots_with_path, save_dir=f"/data/deeplearning/models/{model_id}", use_model_id=args.split_model_id
    )

    fake_negatives = []
    if args.fake_negative_model_id:
        fake_negatives = get_fake_negative_examples(args.fake_negative_model_id)
        logging.info(f"Loaded {len(fake_negatives)} fake negatives from {args.fake_negative_model_id}")

    trainer.explicit_split_dataset_files(
        train_files=splits["train"],
        validation_files=splits["validation"],
        test_files=splits["test"],
        fake_negatives=fake_negatives,
        fake_negatives_sample_rate=args.fake_negative_sample_rate,
        num_samples=args.num_balanced_examples,
    )

    epochs = 300
    if args.fast_run:
        epochs = 1

    trainer.train(
        config=dl_config,
        pipeline_id=args.pipeline_id,
        epochs=epochs,
        initial_lr=0.004,
        tags=tuple(tags),
        log_experiment=True,
        train_batch_size=args.train_batch_size,
        lr_milestones=tuple([60, 120, 180, 240, 300]),
        checkpoint_dir=f"/data/deeplearning/models/{model_id}",
        resume_from=args.resume_from,
        description=description,
        deploy=environment == Environment.PRODUCTION,
        environment=environment,
        additional_wandb_config=config,
        fast_run=args.fast_run,
        model=MODELS_DICT[args.model](pretrained_deepweed_model=pretrained_deepweed_model, config=dl_config),
    )


if __name__ == "__main__":
    run_debuggable(main)
