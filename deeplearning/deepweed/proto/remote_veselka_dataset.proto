syntax = "proto3";

package deeplearning.deepweed.proto;

message Request {
  string json = 1;
}
message Response {
  string json = 1;
}

service RemoteDatasetService {
  rpc Sample(Request) returns (Response) {}
  rpc Config(Request) returns (Response) {}
  rpc NumImages(Request) returns (Response) {}
  rpc Ping(Request) returns (Response) {}
  rpc NewDatapoints(Request) returns (Response) {}
  rpc OldDatapoints(Request) returns (Response) {}
  rpc NewDataCapturedAts(Request) returns (Response) {}
  rpc Filepath(Request) returns (Response) {}
  rpc LoadComparisonEmbeddings(Request) returns (Response) {}
  rpc ImageUrl2Id(Request) returns (Response) {}
  rpc LoadSamplingEmbeddings(Request) returns (Response) {}
  rpc SamplingEmbeddingsLoaded(Request) returns (Response) {}
}