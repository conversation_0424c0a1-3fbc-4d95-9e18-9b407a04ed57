import io
import json
import logging
import os
import shutil
import time
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Set, Tuple, cast

import grpc
import matplotlib
import numpy as np
import numpy.typing as npt
import torch
import torchvision.transforms.functional as TF
from matplotlib import pyplot as plt
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential

import deeplearning.deepweed.transforms as T
from deeplearning.comparison.data_utils import (
    get_comparison_evaluation_bucket,
    get_comparison_evaluation_dir,
    load_embeddings_from_torch,
)
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import REMOTE_VESELKA_DATASET_SERVER_PORT
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.deepweed.dataset_transforms import (
    PADDING,
    bare_normalization_transforms,
    calibration_transforms,
    evaluation_transforms,
    training_transforms,
)
from deeplearning.deepweed.datasets_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, DeepweedDatapoint
from deeplearning.deepweed.metadata import ImageMetadata, LabelMetadata, Point, Polygon
from deeplearning.utils.dataset import DatasetType, pil_loader
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from generated.deeplearning.deepweed.proto.remote_veselka_dataset_pb2 import Request
from generated.deeplearning.deepweed.proto.remote_veselka_dataset_pb2_grpc import RemoteDatasetServiceStub
from lib.common.perf.perf_tracker import (
    duration_perf_recorder,
    duration_perf_recorder_decorator,
    set_autowrite_filename,
    set_verbosity,
)
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue
set_verbosity(False)
LOG = logging.getLogger(__name__)


class RemoteVeselkaDatasetClient(torch.utils.data.Dataset[DeepweedDatapoint]):
    def __init__(
        self,
        mode: DatasetType,
        dl_config: DeepweedConfig,
        skip_normalization: bool = False,
        host: str = "localhost",
        port: int = REMOTE_VESELKA_DATASET_SERVER_PORT,
        model_id: Optional[str] = None,
    ) -> None:
        self._mode = mode
        self._config = dl_config
        self._host = host
        self._port = port

        self._s3_cache_proxy_client = S3CacheProxyClient(
            s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"), timeout=30
        )

        self.model_id = model_id

        stub = self._await_connection(timeout_s=2000)

        config = cast(
            Dict[str, Any], json.loads(stub.Config(Request(json=json.dumps({"mode": self._mode.value}))).json)
        )
        self._segm_classes = cast(Tuple[str, ...], config["segm_classes"])
        self._weed_classes = cast(Tuple[str, ...], config["weed_classes"])
        self._crop_classes = cast(Tuple[str, ...], config["crop_classes"])
        self._crop_ids = cast(List[str], config["crop_ids"])
        self._num_samples = int(config["num_samples"]) if config["num_samples"] is not None else None

        self._train_ppi = int(config["train_ppi"]) if config["train_ppi"] is not None else None
        self._dilate_mask = int(config["dilate_mask"]) if config["dilate_mask"] is not None else 0
        self._keep_low_confidence = bool(config["keep_low_confidence"])
        self._new_loss_multiplier = float(config["new_loss_multiplier"])
        self._positive_sample_percentage = float(config["positive_sample_percentage"])
        self._discard_points_border_px = int(config["discard_points_border_px"])
        self._recency_split = int(config["recency_split"]) if config["recency_split"] is not None else None
        self._recency_split_age = int(config["recency_split_age"]) if config["recency_split_age"] is not None else None

        self._comparison_padding = (
            int(config["comparison_padding"]) if config["comparison_padding"] is not None else PADDING
        )

        num_images_json = json.loads(str(stub.NumImages(Request(json=json.dumps({"mode": self._mode.value}))).json))
        self._num_images = int(num_images_json["num_images"])
        self._num_new_images = int(num_images_json["num_new_images"])
        self._num_old_images = int(num_images_json["num_old_images"])

        self._segm_classes = tuple(category.upper() for category in self._segm_classes)
        self._weed_classes = tuple(category.upper() for category in self._weed_classes)
        self._crop_classes = tuple(category.upper() for category in self._crop_classes)
        self.carbon_cache_host: Optional[str] = None

        self.dataset_version = dl_config.dataset_version
        if os.getenv("DISABLE_S3_CACHE_PROXY"):
            LOG.warning("Running without s3 cache proxy!")
        else:
            self.carbon_cache_host = os.getenv("S3_CACHE_PROXY_SERVICE_HOST")

        if self.carbon_cache_host is None:
            self._tmp_data = "/data/temp_stream_for_datasets"
            try:
                os.mkdir(self._tmp_data)
            except FileExistsError:
                pass

        self._enabled_classes = list(set(self._weed_classes + self._crop_classes + self._segm_classes))

        if self._mode == DatasetType.TRAIN:
            self._transforms = training_transforms(
                height=self._config.training_image_height,
                width=self._config.training_image_width,
                dilate_mask_iterations=self._dilate_mask,
                discard_points_border_px=self._discard_points_border_px,
                random_rotation=self._config.enable_random_rotation,
            )
        elif self._mode == DatasetType.VALIDATION or self._mode == DatasetType.TEST:
            self._transforms = evaluation_transforms(self._dilate_mask)
        elif self._mode == DatasetType.CALIBRATION:
            self._transforms = calibration_transforms(
                self._config.evaluation_image_height,
                self._config.evaluation_image_width,
                self._dilate_mask,
                self._discard_points_border_px,
            )
        elif self._mode == DatasetType.BARE_TRANSFORM:
            self._transforms = bare_normalization_transforms(padding=self._comparison_padding)
        else:
            raise RuntimeError("Unsupported dataset type")
        self._initialized = True

    def _get_stub(self) -> RemoteDatasetServiceStub:
        options = [
            ("grpc.max_send_message_length", -1),
            ("grpc.max_receive_message_length", -1),
        ]
        channel = grpc.insecure_channel(f"{self._host}:{self._port}", options)
        return RemoteDatasetServiceStub(channel)

    def _await_connection(self, timeout_s: int = 480) -> RemoteDatasetServiceStub:
        remaining_time_s = timeout_s
        first = True
        while remaining_time_s > 0:
            try:
                stub = self._get_stub()
                stub.Ping(Request(json=json.dumps({"mode": DatasetType.TRAIN.value})), timeout=5)
                return stub
            except grpc.RpcError as rpc_error:
                if rpc_error.code() != grpc.StatusCode.UNAVAILABLE and first:
                    LOG.error(rpc_error)
                    first = False
            time.sleep(1)
            remaining_time_s -= 1
        raise RuntimeError(f"Failed to connect to remote dataset server within {timeout_s}s")

    @property
    def recency_split_age(self) -> Optional[int]:
        if self._recency_split_age is None:
            return None

        return self._recency_split_age

    @property
    def recency_split(self) -> Optional[int]:
        if self._recency_split is None:
            return None

        return self._recency_split

    def __len__(self) -> int:
        if self._num_samples is not None:
            length = self._num_samples
        else:
            length = self._num_images
        return length

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _sample(self, index: int) -> Dict[str, Any]:
        try:
            stub = self._await_connection()
            response = cast(
                Dict[str, Any],
                json.loads(
                    stub.Sample(Request(json=json.dumps({"mode": self._mode.value, "index": index})), timeout=30).json
                ),
            )
        except Exception as e:
            LOG.error(f"Sample call failed with: {e}")
            raise e
        return response

    def _load_comparison_embeddings(self, metadata: Dict[str, Any]) -> None:
        comparison_embedding_keys = set()
        if self.dataset_version == 1:
            annotations = metadata["annotations"]
        else:
            annotations = metadata["points"]

        for annotation in annotations:
            if annotation.get("annotation_type", "point") == "point":
                if "comparison_embedding_key" in annotation:
                    comparison_embedding_keys.add(annotation["comparison_embedding_key"])

        try:
            loaded_comparison_torch_items = {}
            for key in comparison_embedding_keys:
                embeddings = self._load_embedding_pt_file(get_comparison_evaluation_bucket(), key)
                if embeddings is not None:
                    loaded_comparison_torch_items[key] = embeddings

            for annotation in annotations:
                if annotation.get("annotation_type", "point") == "point":
                    if (
                        "comparison_embedding_key" in annotation
                        and annotation["comparison_embedding_key"] in loaded_comparison_torch_items
                    ):
                        torch_item = loaded_comparison_torch_items[annotation["comparison_embedding_key"]]
                        if self.dataset_version == 1:
                            assert torch_item["image_meta"]["image_id"] == annotation["image_id"]
                        else:
                            assert torch_item["image_meta"]["image_id"] == metadata["image_id"]
                        for ind in range(len(torch_item["embeddings_data"])):
                            if int(torch_item["embeddings_data"][ind]["x"]) == int(
                                annotation["relocation_info"]["x"]
                            ) and int(torch_item["embeddings_data"][ind]["y"]) == int(
                                annotation["relocation_info"]["y"]
                            ):
                                annotation["comparison_embedding"] = torch_item["embeddings"][ind].half()
                                break

        except Exception as e:
            logging.warning(f"Could not load comparison embeddings: {e}")

    def _load_embedding_pt_file(self, bucket: str, key: str) -> Optional[Dict[str, Any]]:
        local_path = os.path.join(get_comparison_evaluation_dir(), key)
        if os.path.exists(local_path):
            try:
                return load_embeddings_from_torch(local_path)
            except Exception as e:
                logging.warning(f"Couldn't load embeddings from {local_path}, defaulting to cache: {e}")
        try:
            data = io.BytesIO(self._s3_cache_proxy_client.get(bucket, key, with_retry=False))
            embeddings = load_embeddings_from_torch(data)
            return embeddings
        except Exception as e:
            logging.debug(f"Embedding {key} not able to be loaded: {e}")
            return None

    @duration_perf_recorder_decorator("Training")
    def __getitem__(self, index: int) -> DeepweedDatapoint:
        worker_info = torch.utils.data.get_worker_info()
        if worker_info:
            set_autowrite_filename(
                f"{CARBON_DATA_DIR}/deeplearning/models/{self.model_id}/timing/loader_{worker_info.id}_{os.getpid()}"
            )
        metadata = self._sample(
            index
        )  # Retrieve the metadata first, then extract the corresponding datapoint based on that metadata.

        return self.get_datapoint_from_metadata(metadata=metadata)

    @duration_perf_recorder_decorator("Training")
    def _load_image_and_targets(
        self, image_meta: ImageMetadata, segm_classes: torch.Tensor
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point]]:
        with duration_perf_recorder("Training", "Loading image from S3"):
            if image_meta.filepath.startswith("s3"):

                split_s3_path = image_meta.filepath.split("/", maxsplit=3)
                assert len(split_s3_path) == 4  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']

                image = self._s3_cache_proxy_client.get_image(split_s3_path[2], split_s3_path[3])
            else:
                image = pil_loader(image_meta.filepath)

        with duration_perf_recorder("Training", "Tensor Conversion and grabbing metadata"):
            image_tensor = TF.to_tensor(image)
            shape = (image_tensor.shape[1], image_tensor.shape[2])
            points = self._get_points(image_meta)

        with duration_perf_recorder("Training", "Loading targets from S3"):
            masks: List[Optional[torch.Tensor]] = []
            if image_meta.filepath.startswith("s3"):
                for index, class_name in enumerate(self._segm_classes):
                    if segm_classes[index]:
                        target = self._get_mask_target(shape, class_name, image_meta)
                        masks.append(target)
                    else:
                        masks.append(None)
            else:
                for index, class_name in enumerate(self._segm_classes):
                    if segm_classes[index]:
                        masks.append(self._render_labels_for_class_tree(shape, class_name, image_meta))
                    else:
                        masks.append(None)

        return image_tensor, masks, points

    @duration_perf_recorder_decorator("Training")
    def _polygons_to_mask(self, mask: npt.NDArray[Any], polygons: Any,) -> npt.NDArray[Any]:
        clockwise_holes = False
        for i, polygon in enumerate(polygons.coordinates):
            if len(polygon) == 0:
                continue
            points = [(point["x"], point["y"]) for point in polygon]
            figure = plt.figure(0, figsize=mask.shape[::-1], dpi=1)
            plt.axis("off")
            plt.axis("image")
            plt.axis([0, mask.shape[1], mask.shape[0], 0])
            plt.gca().add_artist(matplotlib.patches.Polygon(points, color="black", fill=True))
            figure.tight_layout(pad=0)
            figure.canvas.draw()
            canvas_bytes = figure.canvas.tostring_rgb()
            canvas_shape = figure.canvas.get_width_height()[::-1]
            plt.close("all")
            data = np.frombuffer(canvas_bytes, dtype=np.uint8)
            data = data.reshape(canvas_shape + (3,))
            area = self._compute_polygon_area(points)
            if i == 0 and area < 0:
                area = -area
                clockwise_holes = True
            elif clockwise_holes:
                area = -area
            if area > 0:
                mask[data[..., 0] < 255] = 255
            elif area < 0:
                mask[data[..., 0] < 255] = 0

        return mask

    @duration_perf_recorder_decorator("Training")
    def _compute_polygon_area(self, points: Any) -> float:
        # Inspired by
        # https://github.com/Doodle3D/clipper-lib/blob/5765b14a82da45c4fc687b79e70a0d3078d74fca/clipper.js#L6431
        a = 0
        j = len(points) - 1
        for i in range(len(points)):
            a += (points[j][0] + points[i][0]) * (points[j][1] - points[i][1])
            j = i
        return -a * 0.5

    @duration_perf_recorder_decorator("Training")
    def _get_mask_target(self, shape: Tuple[int, int], class_name: str, image_meta: ImageMetadata) -> torch.Tensor:
        image_filename = os.path.basename(image_meta.filepath)
        mask_filepath = f"/tmp/carbon/masks/mask_{image_filename}"

        if os.path.exists(mask_filepath):
            mask_image = pil_loader(mask_filepath)
        else:
            mask = np.zeros(shape)
            for class_name in image_meta.labels.keys():
                polygons = image_meta.labels[class_name].polygons
                if polygons is not None:
                    for polygon in polygons:
                        mask = self._polygons_to_mask(mask, polygon)

            mask_image = Image.fromarray(mask).convert("L")
            os.makedirs(os.path.dirname(mask_filepath), exist_ok=True)
            mask_image.save(f"/tmp/carbon/{os.getpid()}.png")
            shutil.move(f"/tmp/carbon/{os.getpid()}.png", mask_filepath)

        target_tensor = TF.to_tensor(mask_image)[0]
        target_tensor = torch.max(target_tensor, torch.zeros(shape))
        return cast(torch.Tensor, target_tensor)

    @duration_perf_recorder_decorator("Training")
    def _get_points(self, image_meta: ImageMetadata) -> List[Point]:
        points: List[Point] = []
        if image_meta.points_label is not None:
            points = image_meta.points_label.points
            for point in points:
                if point.clz in self.weed_classes:
                    point.hit_clz = HitClass.WEED
                elif point.clz in self.crop_classes:
                    point.hit_clz = HitClass.CROP
                else:
                    point.hit_clz = HitClass.CROP
                    point.confidence = 0

        return points

    @duration_perf_recorder_decorator("Training")
    def _render_labels_for_class_tree(
        self, shape: Tuple[int, int], class_name: str, image_meta: ImageMetadata
    ) -> torch.Tensor:
        result = torch.zeros(shape)
        image_filepath = cast(str, image_meta.labels[class_name].filepath)
        target = pil_loader(image_filepath, grayscale=True)
        target_tensor = TF.to_tensor(target)[0]
        result = torch.max(result, target_tensor)
        return result

    @duration_perf_recorder_decorator("Training")
    def get_datapoint_from_metadata(self, metadata: Dict[str, Any]) -> DeepweedDatapoint:  # noqa: C901
        selected_category = metadata["selected_category"]
        selected_embedding = metadata["selected_embedding"]
        certified_classes = set(metadata["certified_classes"])
        is_new = metadata["is_new"]

        segmentation_labels: Dict[str, LabelMetadata] = {}
        enabled_segmentation_classes = []
        points: List[Point] = []
        polygons: List[Polygon] = []

        if self.dataset_version == 1:
            if self._segm_classes is not None:
                for class_name in self._segm_classes:
                    polygons = []
                    for annotation in metadata["annotations"]:
                        if annotation["annotation_type"] == "polygon" and annotation["label"].upper() == class_name:
                            polygons.append(Polygon(annotation))

                    segmentation_labels[class_name] = LabelMetadata(
                        filepath=metadata["uri"],
                        clazz=class_name,
                        certified=class_name in certified_classes,
                        polygons=polygons,
                        points=[],
                    )

                    enabled_segmentation_classes.append(segmentation_labels[class_name].certified)

                enabled_segmentation_classes_tensor = torch.tensor(enabled_segmentation_classes)

            # Load comparison embeddings and deepweed embedding buckets
            self._load_comparison_embeddings(metadata)

            points = []
            for annotation in metadata["annotations"]:
                if annotation["annotation_type"] == "point":
                    points.append(Point.from_json(annotation))
            points_certified_categories = []
            for class_name in self._weed_classes + self._crop_classes:
                if class_name in certified_classes:
                    points_certified_categories.append(class_name)

            enabled_weed_point_classes = torch.tensor(
                [
                    c in certified_classes
                    and (
                        self._config.train_all_points_every_step or selected_category is None or selected_category == c
                    )
                    for c in self._weed_classes
                ]
            )

            point_labels = LabelMetadata(
                filepath=metadata["uri"],
                clazz="crown",
                certified=True,
                points=points,
                points_certified_categories=points_certified_categories,
            )

            enabled_hits = torch.tensor(
                [
                    any(
                        [
                            c in certified_classes
                            and (
                                self._config.train_all_points_every_step
                                or selected_category is None
                                or selected_category == c
                            )
                            for c in self._weed_classes
                        ]
                    ),
                    any(
                        [
                            c in certified_classes and (selected_category is None or selected_category == c)
                            for c in self._crop_classes
                        ]
                    ),
                    True,
                ]
            )

        elif self.dataset_version == 2:
            all_points = metadata["points"]
            all_polygons = metadata["polygons"]

            if self._segm_classes is not None:
                for class_name in self._segm_classes:
                    polygons = []
                    for annotation in all_polygons:
                        if annotation["label"].upper() == class_name:
                            polygons.append(Polygon(annotation))

                    segmentation_labels[class_name] = LabelMetadata(
                        filepath=metadata["uri"],
                        clazz=class_name,
                        certified=class_name in certified_classes,
                        polygons=polygons,
                        points=[],
                    )

                    enabled_segmentation_classes.append(segmentation_labels[class_name].certified)

                enabled_segmentation_classes_tensor = torch.tensor(enabled_segmentation_classes)

            self._load_comparison_embeddings(metadata)

            points = [Point.from_json(annotation) for annotation in all_points]
            points_certified_categories = []
            for class_name in self._weed_classes + self._crop_classes:
                if class_name in certified_classes:
                    points_certified_categories.append(class_name)

            enabled_weed_point_classes = torch.tensor(
                [
                    c in certified_classes
                    and (
                        self._config.train_all_points_every_step or selected_category is None or selected_category == c
                    )
                    for c in self._weed_classes
                ]
            )
            point_labels = LabelMetadata(
                filepath=metadata["uri"],
                clazz="crown",
                certified=True,
                points=points,
                points_certified_categories=points_certified_categories,
            )

            enabled_hits = torch.tensor(
                [
                    any(
                        [
                            c in certified_classes
                            and (
                                self._config.train_all_points_every_step
                                or selected_category is None
                                or selected_category == c
                            )
                            for c in self._weed_classes
                        ]
                    ),
                    any(
                        [
                            c in certified_classes and (selected_category is None or selected_category == c)
                            for c in self._crop_classes
                        ]
                    ),
                    True,
                ]
            )

        image_meta = ImageMetadata(
            filepath=metadata["uri"],
            ppi=200,  # NOTE: (ZACH) Hardcoded on all predict images so I'm just setting it here.
            labels=segmentation_labels,
            points_label=point_labels,
            timestamp=metadata["captured_at"],
            city="s000",  # Null island
            crop=metadata["crop"],
            crop_id=metadata["crop_id"],
            robot_id=metadata.get("robot_id"),
            row_id=metadata.get("row_id"),
            cam_id=metadata.get("cam_id"),
            new_datapoint=is_new,
            session_name=metadata["session_name"] if "session_name" in metadata else "",
            geohash=metadata.get("geohash"),
            deepweed_embeddings=metadata.get("embeddings"),
            image_id=metadata.get("id"),
        )

        image, masks, pts = self._load_image_and_targets(image_meta, enabled_segmentation_classes_tensor)

        datapoint = DeepweedDatapoint(
            image=image,
            target=None,
            target_list=masks,
            points=pts,
            enabled_weed_point_classes=enabled_weed_point_classes,
            enabled_segm_classes=enabled_segmentation_classes_tensor,
            enabled_hits=enabled_hits,
            filepath=image_meta.filepath,
            image_meta=image_meta,
            new_datapoint=is_new,
            loss_multiplier=self._new_loss_multiplier if is_new else 1.0,
            dataset_type=self._mode,
            enabled_embedding_bucket=selected_embedding,
            enabled_category=selected_category,
        )

        return datapoint

    @duration_perf_recorder_decorator("Training")
    def preprocess_datapoint(
        self,
        image: torch.Tensor,
        target_list: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms_list: Optional[List[Dict[str, Any]]] = None,
    ) -> Tuple[torch.Tensor, DatasetLabel, List[Dict[str, Any]]]:
        if previous_transforms_list:  # Use the previous transforms if the previous transforms is not None.
            self._previous_transforms, previous_transforms_dict = T.get_previous_transforms(
                previous_transforms=previous_transforms_list
            )
            image, target_list, points, total_transforms = self._previous_transforms(
                image,
                target_list,
                points,
                image_meta,
                enabled_weed_classes,
                enabled_hit_classes,
                enabled_segm_classes,
                enabled_embedding_bucket,
                previous_transforms=previous_transforms_dict,
            )  # previous_transforms contains the transforms parameters.
        elif self._transforms:
            image, target_list, points, total_transforms = self._transforms(
                image,
                target_list,
                points,
                image_meta,
                enabled_weed_classes,
                enabled_hit_classes,
                enabled_segm_classes,
                enabled_embedding_bucket,
                previous_transforms=None,
            )

        if not self._keep_low_confidence:
            points = [p for p in points if p.confidence != 0]

        if len(target_list) == 0:
            target_mask = torch.zeros((1, image.shape[1], image.shape[2]), device=image.device)
        else:
            target_mask = torch.stack(
                [t if t is not None else torch.zeros(image.shape[1:], device=image.device) for t in target_list]
            )
        target = DatasetLabel(target_mask, [points])

        return image, target, total_transforms

    def get_train_ppi(self) -> Optional[int]:
        return self._train_ppi

    def get_discard_points_border_px(self) -> int:
        return self._discard_points_border_px

    @property
    def segm_classes(self) -> List[str]:
        return list(self._segm_classes)

    @property
    def weed_classes(self) -> List[str]:
        return list(self._weed_classes)

    @property
    def crop_classes(self) -> List[str]:
        return list(self._crop_classes)

    @property
    def distinct_examples(self) -> int:
        return self._num_images

    @property
    def keep_low_confidence(self) -> bool:
        return self._keep_low_confidence

    @property
    def enabled_classes(self) -> List[str]:
        return self._enabled_classes

    @property
    def distinct_new_examples(self) -> int:
        return self._num_new_images

    @property
    def distinct_old_examples(self) -> int:
        return self._num_old_images

    @property
    def positive_sample_percentage(self) -> float:
        return self._positive_sample_percentage

    def set_positive_percentage(self, percentage: float) -> None:
        self._positive_sample_percentage = percentage

    @property
    def mode(self) -> DatasetType:
        return self._mode

    @property
    def new_data_captured_ats(self) -> List[DatapointTimestamps]:
        stub = self._await_connection()
        new_data_captured_ats_json = json.loads(
            stub.NewDataCapturedAts(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json
        )
        new_data_captured_ats = [DatapointTimestamps(**x) for x in new_data_captured_ats_json["new_data_captured_ats"]]
        return new_data_captured_ats

    @property
    def new_datapoints(self) -> List[str]:
        stub = self._await_connection()
        return cast(
            List[str],
            json.loads(stub.NewDatapoints(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json)[
                "new_datapoints"
            ],
        )

    @property
    def old_datapoints(self) -> List[str]:
        stub = self._await_connection()
        return cast(
            List[str],
            json.loads(stub.OldDatapoints(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json)[
                "old_datapoints"
            ],
        )

    @property
    def filepath(self) -> str:
        stub = self._await_connection()
        return cast(
            str,
            json.loads(stub.Filepath(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json)[
                "filepath"
            ],
        )

    @duration_perf_recorder_decorator("Training")
    def load_comparison_embeddings(self) -> None:
        stub = self._await_connection()
        stub.LoadComparisonEmbeddings(Request(json=json.dumps({"mode": self._mode.value})), timeout=30 * 60)

    @duration_perf_recorder_decorator("Training")
    def load_sampling_embeddings(self) -> None:
        stub = self._await_connection()
        stub.LoadSamplingEmbeddings(
            Request(json=json.dumps({"mode": self._mode.value})), timeout=6 * 60 * 60
        )  # Increase timeout for embedding.

    @duration_perf_recorder_decorator("Training")
    def sampling_embeddings_loaded(self) -> bool:
        stub = self._await_connection()
        response = json.loads(
            stub.SamplingEmbeddingsLoaded(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json
        )
        return cast(bool, response["loaded"])

    @property
    def image_url2id(self) -> Dict[str, str]:
        stub = self._await_connection()
        return cast(
            Dict[str, str],
            json.loads(stub.ImageUrl2Id(Request(json=json.dumps({"mode": self._mode.value})), timeout=30).json)[
                "image_url2id"
            ],
        )

    @property
    def crop_ids(self) -> List[str]:
        return self._crop_ids
