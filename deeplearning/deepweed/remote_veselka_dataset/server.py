import json
import logging
import signal
import traceback
from concurrent import futures
from typing import Dict

import grpc
import torch

from deeplearning.deepweed.constants import REMOTE_VESELKA_DATASET_SERVER_PORT
from deeplearning.deepweed.remote_veselka_dataset.remote_dataset import RemoteVeselkaDataset
from deeplearning.utils.dataset import DatasetType
from generated.deeplearning.deepweed.proto.remote_veselka_dataset_pb2 import Request, Response
from generated.deeplearning.deepweed.proto.remote_veselka_dataset_pb2_grpc import (
    RemoteDatasetServiceServicer,
    add_RemoteDatasetServiceServicer_to_server,
)

LOG = logging.getLogger(__name__)


class RemoteVeselkaDatasetServer(RemoteDatasetServiceServicer):
    def __init__(self, mode_to_dataset: Dict[DatasetType, RemoteVeselkaDataset]) -> None:
        self._mode_to_dataset = mode_to_dataset

        logging.basicConfig()
        logging.getLogger().setLevel(logging.INFO)
        LOG.info("Starting up remote veselka dataset server")

    def run(self, port: int = REMOTE_VESELKA_DATASET_SERVER_PORT, blocking: bool = True) -> None:
        try:
            self._port = port
            self._server = grpc.server(
                futures.ThreadPoolExecutor(max_workers=2),
                options=[
                    ("grpc.max_send_message_length", -1),
                    ("grpc.max_receive_message_length", -1),
                    ("grpc.so_reuseport", 1),
                    ("grpc.use_local_subchannel_pool", 1),
                ],
            )
            add_RemoteDatasetServiceServicer_to_server(self, self._server)
            self._server.add_insecure_port(f"[::]:{self._port}")
            self._server.start()
            LOG.info("veselka remote server waiting for termination")
            if blocking:
                self._server.wait_for_termination()
        except torch.distributed.elastic.multiprocessing.api.SignalException as e:
            if e.sigval == signal.SIGTERM:
                LOG.debug("Received SIGTERM, shutting down")
            else:
                LOG.info(f"Received signal {e.sigval}, shutting down")
        except Exception as e:
            LOG.info(traceback.format_exc())
            LOG.info(f"Remote dataset server exception: {e}")

    def NumImages(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(
            json=json.dumps(
                {
                    "num_images": len(dataset),
                    "num_new_images": dataset.distinct_new_examples,
                    "num_old_images": dataset.distinct_old_examples,
                }
            )
        )

    def Config(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(
            json=json.dumps(
                {
                    "segm_classes": dataset.segm_classes,
                    "weed_classes": dataset.weed_classes,
                    "crop_classes": dataset.crop_classes,
                    "crop_ids": dataset.crop_ids,
                    "num_samples": dataset._num_samples,
                    "train_ppi": dataset._train_ppi,
                    "dilate_mask": dataset._dilate_mask,
                    "keep_low_confidence": dataset._keep_low_confidence,
                    "new_loss_multiplier": dataset._new_loss_multiplier,
                    "goal_percentage_new": dataset._goal_percentage_new,
                    "positive_sample_percentage": dataset._positive_sample_percentage,
                    "discard_points_border_px": dataset._discard_points_border_px,
                    "recency_split": dataset._recency_split,
                    "recency_split_age": dataset._recency_split_age,
                    "new_datapoints": dataset.new_datapoints,
                    "old_datapoints": dataset.old_datapoints,
                    "comparison_padding": dataset.comparison_padding,
                    "new_data_captured_ats": [
                        {
                            "image_captured_timestamp_ms": x.image_captured_timestamp_ms,
                            "label_updated_timestamp_ms": x.label_updated_timestamp_ms,
                        }
                        for x in dataset.new_data_captured_ats
                    ],
                }
            )
        )

    def Sample(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        item = dataset[int(args["index"])]
        response = json.dumps(item)

        return Response(json=response)

    def Ping(self, request: Request, context: grpc.ServicerContext) -> Response:
        return Response(json=request.json)

    def NewDataCapturedAts(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(
            json=json.dumps(
                {
                    "new_data_captured_ats": [
                        {
                            "image_captured_timestamp_ms": x.image_captured_timestamp_ms,
                            "label_updated_timestamp_ms": x.label_updated_timestamp_ms,
                        }
                        for x in dataset.new_data_captured_ats
                    ]
                }
            )
        )

    def NewDatapoints(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(json=json.dumps({"new_datapoints": dataset.new_datapoints}))

    def OldDatapoints(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(json=json.dumps({"old_datapoints": dataset.old_datapoints}))

    def Filepath(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(json=json.dumps({"filepath": dataset.filepath}))

    def LoadSamplingEmbeddings(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        self._mode_to_dataset[DatasetType(args["mode"])].load_sampling_embeddings()
        return Response(json=json.dumps({}))

    def SamplingEmbeddingsLoaded(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        loaded = self._mode_to_dataset[DatasetType(args["mode"])].sampling_embeddings_loaded
        return Response(json=json.dumps({"loaded": loaded}))

    def ImageUrl2Id(self, request: Request, context: grpc.ServicerContext) -> Response:
        args = json.loads(request.json)
        dataset = self._mode_to_dataset[DatasetType(args["mode"])]
        return Response(json=json.dumps({"image_url2id": dataset.image_url2id}))
