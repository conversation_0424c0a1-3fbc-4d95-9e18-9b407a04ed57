import io
import logging
import os
import random
from typing import Any, List, Optional, Sized, Tuple

import cv2
import numpy as np
import numpy.typing as npt
import pandas
import requests
import torch
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import torchvision.transforms.v2 as T
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential
from torch.utils.data import Dataset

from deeplearning.p2p.metadata import ImageMetadata, PredictionReconstructionMetadata, list_files
from deeplearning.utils.dataset import get_carbon_cache_host, index_split, pil_loader
from deeplearning.utils.images import (
    IMAGENET_MEANS,
    IMAGENET_STDS,
    crop_or_pad,
    random_color_jitter,
    random_gaussian_noise,
)
from deeplearning.utils.resize_utils import interpolate
from lib.common.image.resize import adjust_size_ppi

DATASET_ELEMENT = Tuple[torch.Tensor, torch.Tensor, Optional[Tuple[float, float]], ImageMetadata]
LABEL_THRESHOLD = 0.5

LOG = logging.getLogger(__name__)

IMAGE_TRANSFORMS = T.Compose(
    [
        T.ColorJitter(brightness=(0.75, 1.5), saturation=(0.75, 1.5), hue=(-0.083, 0.083)),
        T.Lambda(lambda x: TF.adjust_gamma(x, gamma=random.uniform(0.75, 1.5))),
        T.Lambda(lambda x: random_gaussian_noise(x, stddev=0.05)[0]),
    ]
)


class P2PDataset(Dataset[DATASET_ELEMENT], Sized):
    def __init__(
        self,
        roots: Optional[List[str]] = None,
        files: Optional[List[ImageMetadata]] = None,
        perspective_crop_size_inches: float = 1,
        image_crop_size_inches: float = 6,
        model_ppi: int = 100,
        training: bool = False,
        fake_negatives: Optional[List[ImageMetadata]] = None,
        fake_negatives_sample_rate: float = 0.1,
        splits: Optional[Tuple[int, ...]] = None,
        split_idx: Optional[int] = None,
        smearing: Optional[int] = None,
        num_samples: Optional[int] = None,
        seed: int = 1,
    ) -> None:
        super().__init__()
        self._perspective_crop_size_inches = perspective_crop_size_inches
        self._image_crop_size_inches = image_crop_size_inches
        self._model_ppi = model_ppi
        self._training = training
        assert smearing is None or smearing % 2 == 0, f"Smearing must be divisible by 2: {smearing}"
        self._half_smearing = int(smearing / 2) if smearing is not None else 0
        self._files: List[ImageMetadata] = []
        self._num_samples = num_samples
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)
        self._carbon_cache_host = get_carbon_cache_host()
        self._fake_negatives: Optional[List[ImageMetadata]] = fake_negatives
        self._fake_negatives_sample_rate: float = fake_negatives_sample_rate

        if files is not None:
            self._files = files
        else:
            assert roots is not None, "Either roots or files must be supplied to P2PDataset"
            for root in roots:
                assert os.path.exists(root), f"{root} does not exist"
                assert os.path.isdir(root), f"{root} is not a directory"
                found_at_least_one_certified_image = False
                for dirname, _, _ in os.walk(root + "/"):
                    for image_meta in list_files(dirname):
                        if image_meta.has_certified_label():
                            self._files.append(image_meta)
                            found_at_least_one_certified_image = True
                assert found_at_least_one_certified_image, f"{root} does not have any certified images"

            # Sort files for consistent results across machines
            self._files = sorted(self._files, key=lambda m: m.image_path)

            if splits is not None:
                assert split_idx is not None
                splits_indices = index_split(len(self._files), splits)
                self._files = [self._files[idx] for idx in splits_indices[split_idx]]

        metadata = []
        for index, file in enumerate(self._files):
            metadata.append(
                (
                    index,
                    file.crop_id,
                    file.robot_id,
                    file.captured_at.date().isoformat(),
                    file.label.filepath is not None,
                )
            )

        self._metadata_df = pandas.DataFrame(metadata, columns=["index", "crop_id", "robot_id", "date", "is_match"])

    def _find_centroid_xy(self, mask: npt.NDArray[Any], filepath: str) -> Optional[Tuple[float, float]]:
        _, _, stats, centroids = cv2.connectedComponentsWithStats((mask > 127).astype(np.uint8))

        # Delete centroid of largest component (background)
        centroids = np.delete(centroids, np.argmax(stats[:, -1]), 0)
        stats = np.delete(stats, np.argmax(stats[:, -1]), 0)

        assert (
            len(centroids) <= 1
        ), f"Expected none or a single annotation in {filepath}, got: {len(centroids)}: {centroids}"
        if len(centroids):
            cx, cy = centroids[0]
            return (cx, cy)
        else:
            return None

    def _random_transform(self, image: torch.Tensor) -> torch.Tensor:
        # Random color jitter
        image = random_color_jitter(
            image, brightness=(0.75, 1.5), saturation=(0.75, 1.5), hue=(-30, 30), gamma=(0.75, 1.5)
        )

        # Random gaussian noise
        image, _ = random_gaussian_noise(image, stddev=0.05)

        return image

    def pad_to_square(self, image: torch.Tensor) -> torch.Tensor:
        _, h, w = image.shape
        if h == w:
            return image  # already square

        diff = abs(h - w)
        pad1 = diff // 2
        pad2 = diff - pad1

        if h < w:
            padding = (0, pad1, 0, pad2)
        else:
            padding = (pad1, 0, pad2, 0)

        return F.pad(image, padding)

    def _transform_point(self, point: Tuple[float, float], M: npt.NDArray[Any]) -> Tuple[float, float]:
        return cv2.transform(np.array([[point]]), M)[0, 0]  # type: ignore

    def _is_within(self, image: npt.NDArray[Any], point: Tuple[float, float]) -> bool:
        return point[0] >= 0 and point[0] < image.shape[1] and point[1] < image.shape[0] and point[1] >= 0

    def _is_within_tensor(self, image: torch.Tensor, point: Tuple[float, float]) -> bool:
        return point[0] >= 0 and point[0] < image.shape[2] and point[1] < image.shape[1] and point[1] >= 0

    @retry(
        wait=wait_exponential(multiplier=1, min=60, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _get_image_from_cache(self, image_s3_path: str, grayscale: bool = False) -> Image.Image:
        response = requests.get(f"http://{self._carbon_cache_host}/maka-pono/{image_s3_path}", timeout=30)
        assert response.ok, f"Failed to retrieve image ({image_s3_path}) from cache: {response.text}"
        return Image.open(io.BytesIO(response.content), formats=["png"]).convert("L" if grayscale else "RGB")

    def _load_image(self, path: str, grayscale: bool = False) -> Image.Image:
        if self._carbon_cache_host is not None:
            return self._get_image_from_cache(path.replace("/data/deeplearning/p2p/", ""), grayscale=grayscale)
        else:
            return pil_loader(path, grayscale=grayscale)

    def get_metadata(self, idx: int) -> ImageMetadata:
        """
        Get metadata for the given index.
        """
        if idx < 0 or idx >= len(self._files):
            raise IndexError(f"Index {idx} out of bounds for dataset with {len(self._files)} files.")
        return self._files[idx]

    def preprocess_datapoint(
        self,
        perspective: torch.Tensor,
        image: torch.Tensor,
        point: Optional[Tuple[float, float]],
        metadata: ImageMetadata,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, PredictionReconstructionMetadata]:
        # Creating Label tensor based on point
        image_label = torch.zeros((1, image.shape[1], image.shape[2]), device=image.device)
        if point is not None and self._is_within_tensor(image, point):
            image_label[:, int(point[1]), int(point[0])] = 1.0

        if image.shape[1] != image.shape[2]:
            image = self.pad_to_square(image)
            image_label = self.pad_to_square(image_label)

        if self._training:
            angle = random.uniform(-180, 180)
            scale = random.uniform(0.8, 1.2)
            tx, ty = (
                random.randint(int(-image.shape[2] // 1.8), int(image.shape[2] // 1.8)),
                random.randint(int(-image.shape[1] // 1.8), int(image.shape[1] // 1.8)),
            )

            image = TF.affine(
                image, angle=angle, translate=(tx, ty), scale=scale, shear=0, interpolation=T.InterpolationMode.BILINEAR
            )
            image = IMAGE_TRANSFORMS(image)

            perspective = TF.affine(
                perspective,
                angle=angle,
                translate=(0, 0),
                scale=scale,
                shear=0,
                interpolation=T.InterpolationMode.BILINEAR,
            )
            perspective = IMAGE_TRANSFORMS(perspective)

            image_label = TF.affine(
                image_label,
                angle=angle,
                translate=(tx, ty),
                scale=scale,
                shear=0,
                interpolation=T.InterpolationMode.BILINEAR,
            )
            # Make sure image label is still a binary mask with one single non-zero pixel

        # Adjusting PPI
        perspective_size = adjust_size_ppi(
            (perspective.shape[1], perspective.shape[2]), metadata.perspective_ppi, self._model_ppi
        )
        perspective = interpolate(perspective.unsqueeze(0), perspective_size).squeeze(0)

        image_size = adjust_size_ppi((image.shape[1], image.shape[2]), metadata.ppi, self._model_ppi)
        image = interpolate(image.unsqueeze(0), image_size).squeeze(0)
        image_label = interpolate(image_label.unsqueeze(0), image_size).squeeze(0)
        image_label = (image_label >= max(torch.max(image_label).item(), 0.01)).float()

        # Crop Pad
        image, cropped_padded_to_original_shift_x, cropped_padded_to_original_shift_y = crop_or_pad(
            image, (int(image.shape[2] / 2), int(image.shape[1] / 2)), (self.input_size, self.input_size)
        )
        perspective, _, _ = crop_or_pad(
            perspective,
            (int(perspective.shape[2] / 2), int(perspective.shape[1] / 2)),
            (self.perspective_input_size, self.perspective_input_size),
        )

        image_label, _, _ = crop_or_pad(
            image_label,
            (int(image_label.shape[2] / 2), int(image_label.shape[1] / 2)),
            (self.input_size, self.input_size),
        )

        # Normalize tensors
        perspective = TF.normalize(perspective, mean=IMAGENET_MEANS[:3], std=IMAGENET_STDS[:3])
        image = TF.normalize(image, mean=IMAGENET_MEANS[:3], std=IMAGENET_STDS[:3])

        # Introduce smearing
        image = F.avg_pool2d(
            image.unsqueeze(0), (1 + self._half_smearing * 2, 1), padding=(self._half_smearing, 0), stride=1
        ).squeeze(0)

        reconstruction_metadata = PredictionReconstructionMetadata(
            shift_x=cropped_padded_to_original_shift_x,
            shift_y=cropped_padded_to_original_shift_y,
            model_ppi=self.model_ppi,
        )

        return perspective, image, image_label, reconstruction_metadata

    def _open_image(self, meta: ImageMetadata) -> Tuple[torch.Tensor, Optional[Tuple[float, float]]]:
        image = TF.to_tensor(self._load_image(meta.image_path))

        if meta.label.filepath is not None:
            image_label = np.array(self._load_image(meta.label.filepath, grayscale=True))
            image_point = self._find_centroid_xy(image_label, meta.label.filepath)
        else:
            image_point = None

        return (image, image_point)

    def _open_perspective(self, meta: ImageMetadata) -> torch.Tensor:
        assert len(meta.perspective_paths) > 0, f"Image {meta.image_path} does not have any perspectives"

        # TODO(asergeev): For now we're only using first perspective. Add support for multiple.
        perspective: torch.Tensor = TF.to_tensor(np.array(self._load_image(meta.perspective_paths[0])))

        return perspective

    def set_seed(self, seed: int) -> None:
        self._seed = seed
        self._rng = np.random.default_rng(self._seed)

    def _sample_index(self) -> int:
        df = self._metadata_df

        # Sample the datapoint index
        index: int = self._rng.choice(df["index"])

        return index

    def __getitem__(self, index: int) -> DATASET_ELEMENT:
        if self._num_samples:
            index = self._sample_index()

        if self._rng.random() < self._fake_negatives_sample_rate and self._fake_negatives:
            # Sample a fake negative example
            index = self._rng.integers(0, len(self._fake_negatives))
            image_meta = self._fake_negatives[index]
        else:
            image_meta = self._files[index]

        # open the image
        image_t, image_point = self._open_image(image_meta)

        # open perspective
        perspective_t = self._open_perspective(image_meta)

        return (perspective_t, image_t, image_point, image_meta)

    def __len__(self) -> int:
        if self._num_samples is None:
            return len(self._files)
        else:
            return self._num_samples

    @property
    def num_files(self) -> int:
        return len(self._files)

    @property
    def input_size(self) -> int:
        return int(self._image_crop_size_inches * self._model_ppi)

    @property
    def perspective_input_size(self) -> int:
        return int(self._perspective_crop_size_inches * self._model_ppi)

    @property
    def model_ppi(self) -> int:
        return self._model_ppi

    @property
    def smearing(self) -> int:
        return self._half_smearing * 2


def label_coord(label: torch.Tensor, n: int) -> Optional[Tuple[int, int]]:
    assert len(label.shape) == 4, f"Wrong label shape: {label.shape}"

    coords = (label[n, 0] > LABEL_THRESHOLD).nonzero()
    if not len(coords):
        return None

    y, x = coords[0]
    return (int(x), int(y))


class P2PTrainValDatasets:
    def __init__(
        self,
        train_dataset: P2PDataset,
        validation_dataset: P2PDataset,
        test_dataset: P2PDataset,
        calibration_dataset: P2PDataset,
    ) -> None:
        self.train_dataset = train_dataset
        self.validation_dataset = validation_dataset
        self.test_dataset = test_dataset
        self.calibration_dataset = calibration_dataset

    def get_training(self) -> P2PDataset:
        return self.train_dataset

    def get_validation(self) -> P2PDataset:
        return self.validation_dataset

    def get_test(self) -> P2PDataset:
        return self.test_dataset

    def get_calibration(self) -> P2PDataset:
        return self.calibration_dataset
