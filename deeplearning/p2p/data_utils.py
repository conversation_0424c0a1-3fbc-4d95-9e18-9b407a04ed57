import json
import os
import random
from collections import defaultdict
from typing import Dict, List, Optional

from deeplearning.p2p.metadata import ImageMetadata, LabelMetadata, list_files
from deeplearning.utils.download_utils import download_records


def get_splits(
    data_roots_with_path: List[str],
    train_split_percentage: float = 0.7,
    validation_split_percentage: float = 0.1,
    save_dir: Optional[str] = None,
    use_model_id: Optional[str] = None,
) -> Dict[str, List[ImageMetadata]]:

    if use_model_id is not None:
        download_records(use_model_id, s3_directory="models")
        return load_splits(f"/data/deeplearning/models/{use_model_id}/splits.json")
    else:
        return split_labels(
            data_roots_with_path,
            save_dir=save_dir,
            train_split_percentage=train_split_percentage,
            validation_split_percentage=validation_split_percentage,
        )


def save_splits(split_dict: Dict[str, List[ImageMetadata]], save_dir: str) -> None:
    dict_to_save = {}
    for name, images in split_dict.items():
        dict_to_save[name] = [image.image_path for image in images]

    os.makedirs(save_dir, exist_ok=True)
    with open(os.path.join(save_dir, "splits.json"), "w") as f:
        json.dump(dict_to_save, f, indent=4)


def load_splits(path: str) -> Dict[str, List[ImageMetadata]]:
    assert os.path.exists(path), f"Model does not have splits: {path}"

    with open(path, "r") as f:
        json_data = json.load(f)

    split_dict = {}
    for name, images in json_data.items():
        split_dict[name] = [ImageMetadata.from_file(image) for image in images]

    return split_dict


def split_labels(
    data_roots_with_path: List[str],
    train_split_percentage: float = 0.7,
    validation_split_percentage: float = 0.1,
    save_dir: Optional[str] = None,
) -> Dict[str, List[ImageMetadata]]:
    labels = []
    for root in data_roots_with_path:
        assert os.path.exists(root), f"{root} does not exist"
        assert os.path.isdir(root), f"{root} is not a directory"
        found_at_least_one_certified_image = False
        for dirname, _, _ in os.walk(root + "/"):
            for image_meta in list_files(dirname):
                if image_meta.has_certified_label():
                    labels.append(image_meta)
                    found_at_least_one_certified_image = True
        assert found_at_least_one_certified_image, f"{root} does not have any certified images"

    random.seed(1)
    label_buckets = defaultdict(list)
    label_set = set()

    for label in labels:
        key = label.captured_at.date().isoformat()
        if label.image_path not in label_set:
            label_buckets[key].append(label)
            label_set.add(label.image_path)

    train: List[ImageMetadata] = []
    validation: List[ImageMetadata] = []
    test: List[ImageMetadata] = []

    labels_to_split = []
    for key, label_ids in label_buckets.items():
        labels_to_split.extend(label_ids)

        if len(labels_to_split) < 20:
            continue
        random.shuffle(labels_to_split)

        train_upper = int(len(labels_to_split) * train_split_percentage)
        validation_upper = int(train_upper + len(labels_to_split) * validation_split_percentage)

        train.extend(labels_to_split[:train_upper])
        validation.extend(labels_to_split[train_upper:validation_upper])
        test.extend(labels_to_split[validation_upper:])
        labels_to_split = []

    split_dict = {
        "train": train,
        "validation": validation,
        "test": test,
    }

    if save_dir is not None:
        save_splits(split_dict, save_dir)

    return split_dict


def get_fake_negative_examples(model_id: str) -> List[ImageMetadata]:
    hard_examples_path = f"/data/deeplearning/models/{model_id}/hard_examples.json"

    if not os.path.exists(hard_examples_path):
        return []

    with open(hard_examples_path, "r") as f:
        data = json.load(f)

    hard_examples = []
    for item in data["images"]:
        # Create LabelMetadata from the stored label data
        label = LabelMetadata(filepath=None, certified=True)

        # Create ImageMetadata
        metadata = ImageMetadata(
            image_path=item["image_path"], perspective_paths=item["perspective_paths"], label=label
        )

        # Set the private attributes that were stored
        metadata._perspective_ppi = item.get("_perspective_ppi")
        metadata._ppi = item.get("ppi")

        hard_examples.append(metadata)
    return hard_examples
