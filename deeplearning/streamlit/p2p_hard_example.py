import io
import json
import os

import cv2
import numpy as np
import requests
import streamlit as st
import torchvision.transforms.functional as TF
from PIL import Image

from deeplearning.p2p.metadata import ImageMetadata, LabelMetadata
from deeplearning.scripts.utils.utils import TrainingInfo
from deeplearning.utils.dataset import get_carbon_cache_host, pil_loader
from deeplearning.utils.images import crop_or_pad
from deeplearning.utils.resize_utils import interpolate
from lib.common.image.resize import adjust_size_ppi

# ---- CONSTANTS ----
MODEL_PPI = 100
IMAGE_CROP_SIZE_INCHES = 6
PERSPECTIVE_CROP_SIZE_INCHES = 2

CARBON_CACHE_HOST = get_carbon_cache_host()


def draw_circle_on_image(pil_img, coord, color=(255, 0, 0), radius=12, thickness=3):
    """Draw a circle on a PIL image at the given (x, y) coordinate."""
    img_np = np.array(pil_img)
    # Convert RGB to BGR for OpenCV
    img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
    x, y = int(coord[0]), int(coord[1])
    cv2.circle(img_bgr, (x, y), radius, color, thickness)
    # Convert back to RGB for PIL
    img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
    return Image.fromarray(img_rgb)


def _get_image_from_cache(image_s3_path: str, grayscale: bool = False) -> Image.Image:
    response = requests.get(f"http://{CARBON_CACHE_HOST}/maka-pono/{image_s3_path}", timeout=30)
    assert response.ok, f"Failed to retrieve image ({image_s3_path}) from cache: {response.text}"
    return Image.open(io.BytesIO(response.content), formats=["png"]).convert("L" if grayscale else "RGB")


def _load_image(path: str, grayscale: bool = False) -> Image.Image:
    if CARBON_CACHE_HOST is not None:
        return _get_image_from_cache(path.replace("/data/deeplearning/p2p/", ""), grayscale=grayscale)
    else:
        return pil_loader(path, grayscale=grayscale)


def preprocess_and_crop_image(image_path, ppi, model_ppi, crop_size_inches):
    # Load image (RGB)
    image = np.array(_load_image(image_path, grayscale=False))
    image_t = TF.to_tensor(image)
    # Resize to model_ppi
    new_size = adjust_size_ppi((image_t.shape[1], image_t.shape[2]), ppi, model_ppi)
    image_t_resized = interpolate(image_t.unsqueeze(0), new_size).squeeze(0)
    # Center crop or pad to (crop_size, crop_size)
    crop_size = int(crop_size_inches * model_ppi)
    center_x = int(image_t_resized.shape[2] / 2)
    center_y = int(image_t_resized.shape[1] / 2)
    image_crop_t, shift_x, shift_y = crop_or_pad(image_t_resized, (center_x, center_y), (crop_size, crop_size))
    # Return both the cropped image and the shift values
    return image_crop_t, shift_x, shift_y


st.title("P2P Hard Example Visualizer")

model_id = st.text_input("Enter model-id", "")

if model_id:
    training_info = TrainingInfo(model_id)
    json_path = os.path.join(training_info.data_dir, "hard_examples.json")

    if os.path.exists(json_path):
        with open(json_path, "r") as f:
            data = json.load(f)
        images = data.get("images", [])

        st.write(f"{len(images)} examples.")

        if images:
            idx = st.sidebar.slider("Example index", 0, len(images) - 1, 0)
            example = images[idx]
            st.write(f"Score: {example['score']}")

            # Reconstruct ImageMetadata
            image_meta = ImageMetadata(
                image_path=example["image_path"],
                perspective_paths=example.get("perspective_paths", []),
                label=LabelMetadata.empty(),
            )
            image_meta._ppi = example.get("ppi", 182)
            image_meta._perspective_ppi = example.get("perspective_ppi", 200)

            # Target image
            image_crop_t, shift_x, shift_y = preprocess_and_crop_image(
                image_meta.image_path, image_meta.ppi, MODEL_PPI, IMAGE_CROP_SIZE_INCHES
            )
            image_np = TF.to_pil_image(image_crop_t)

            # Draw circle if coord is present
            if (
                "coord" in example
                and example["coord"] is not None
                and st.sidebar.checkbox("Annotate Coord", value=True)
            ):
                coord = example["coord"]
                if isinstance(coord, list):
                    coord = tuple(coord)

                adjusted_coord = (coord[0] + shift_x, coord[1] + shift_y)

                st.write(f"Coord: {adjusted_coord}")

                # Ensure the adjusted coordinate is within bounds
                img_height, img_width = image_np.size[1], image_np.size[0]
                if 0 <= adjusted_coord[0] < img_width and 0 <= adjusted_coord[1] < img_height:
                    image_np = draw_circle_on_image(image_np, adjusted_coord)
                else:
                    st.warning(
                        f"Adjusted coordinate {adjusted_coord} is out of bounds for image size {(img_width, img_height)}"
                    )

            st.subheader("Target Image")
            st.image(image_np, caption="Target Image", use_column_width=True)

            # Perspective image(s)
            if image_meta.perspective_paths:
                perspective_crop_t, _, _ = preprocess_and_crop_image(
                    image_meta.perspective_paths[0], image_meta.perspective_ppi, MODEL_PPI, PERSPECTIVE_CROP_SIZE_INCHES
                )
                perspective_np = TF.to_pil_image(perspective_crop_t)
                st.subheader("Perspective Image")
                st.image(perspective_np, caption="Perspective Image", use_column_width=True)
            else:
                st.info("No perspective images found for this example.")

        else:
            st.info("No images found in the JSON.")
    else:
        st.warning(f"File not found: {json_path}")
else:
    st.info("Please enter a model-id.")
