# Cloud

## [Ansible](./ansible)

Playbooks for host configuration

## [Terraform](./terraform)

- [aws](terraform/aws) - higher order aws stack for permissions and dependencies
- [clout-v1](terraform/cloud-v1) - cloud-v1 stack manages most active aws resources relating for carbon's cloud infrastructure (depends on aws)

## [Clusters](./clusters)

Kubernetes instances managed by this repository

- [dc1](./clusters/dc1-v1)

## [K8 Cluster Infrastructure](./infrastructure)

Kubernetes cluster infrastructure, typically operators or controllers for on prem clusters.

## [K8 Cluster Applications](./apps)

Kubernetes applications for deploy via kustomize, helm, or manifest files

## Secrets

We use kubeseal in order to check secrets safely into the repo. To seal a new secret, follow these instruction

[Sealed Secrets](./docs/REFERENCE.md)

## Cloud Conventions

See the established [Conventions](./docs/Conventions.md)

## Golang Services

- [api-verifier](golang/services/api-verifier)
- [auth-forwarder](golang/services/auth-forwarder)
- [carbon-data](golang/services/carbon-data)
- [credential-proxy](golang/services/credential-proxy)
- [idm](golang/services/idm)
- [infra-mgr](golang/services/infra-mgr)
- [ingest](golang/services/ingest)
- [job-creator](golang/services/job-creator)
- [metrics-exporter](golang/services/metrics-exporter)
- [portal](golang/services/portal)
- [robot-syncer](golang/services/robot-syncer)
- [s3-cache-proxy](golang/services/s3-cache-proxy)
- [slackbot](golang/services/slackbot)
- [version-metadata](golang/services/version-metadata)
- [moco](golang/services/moco)

## Lambda Services

- [image-service-lambda](golang/lambda/image_service)

## Javascript Projects (ui)

- [job-creator-ui](golang/services/job-creator/ui)
- [portal](golang/services/portal/ui)
- [ingest-ui](golang/services/ingest/ui)

## Python Projects

- [carbon-analytics](python/services/carbon-analytics)
- [comparison-tool](python/services/comparison-tool)

## Releasing Applications

Applications have a specific tag format for release. The general format is `<appid>-vX.Y.Z` app id being a shortcode for the application followd by a dash (-) followed by the application's semantic version.
The app shortcodes are as follows

| Application                                            | AppID      | Example Release Tag |
|--------------------------------------------------------|------------|---------------------|
| [api-verifier](golang/services/api-verifier)           | apivfy     | apivfy-v1.2.3       |
| [credential-proxy](golang/services/credential-proxy)   | crdpxy     | crdpxy-v1.2.3       |
| [idm](golang/services/idm)                             | idm        | idm-v1.2.3          |
| [infra-mgr](golang/services/infra-mgr)                 | infmgr     | infmgr-v1.2.3       |
| [ingest](golang/services/ingest)                       | ingest     | ingest-v1.2.3       |
| [job-creator](golang/services/job-creator)             | jocr       | jocr-v1.2.3         |
| [metrics-exporter](golang/services/metrics-exporter)   | mtrex      | mtrex-v1.2.3        |
| [portal](golang/services/portal)                       | portal     | portal-v1.2.3       |
| [robot-syncer](golang/services/robot-syncer)           | rosy       | rosy-v1.2.3         |
| [s3-cache-proxy](golang/services/s3-cache-proxy)       | s3cpxy     | s3cpxy-v1.2.3       |
| [slackbot](golang/services/slackbot)                   | slackbot   | slackbot-v1.2.3     |
| [version-metadata](golang/services/version-metadata)   | vermta     | vermta-v1.2.3       |
| [crtool](golang/cmd/crtool)                            | crtool     | crtool-v1.2.3       |
| [carbon-analytics](python/services/carbon-analytics)   | analytics  | analytics-v1.2.3    |
| [carbon-panelytics](python/services/carbon-panelytics) | panelytics | panelytics-v1.2.3   |
| [comparison-tool](python/services/comparison-tool)     | cmptool    | cmptool-v1.2.3      |
| [moco](python/services/moco)                           | moco       | moco-v1.2.3         |
| [cloud-image](golang/services/cloud-image)             | imgsvc     | imgsvc-v1.2.3       |
| [rtc-ui](ui/rtc)                                       | rtc-ui     | rtc-ui-v1.2.3       |

| Lambda Application                                  | AppID         | Example Release Tag  |
|-----------------------------------------------------|---------------|----------------------|
| [image-service-lambda](golang/lambda/image_service) | imgsvc-lambda | imgsvc-lambda-v1.2.3 |

## Cluster Bootstrapping

### OnPrem Cluster

- [DC1](clusters/dc1-v1/README.md) - cluster definition
- [Ansible](ansible/README.md) - ansible host setup.

### Carbon EKS (carbon-cloud)

See [terraform](terraform/README.md) and review existing terraform stacks
In order to deploy to aws, the cloud-v1 stack must be multiple subsequent runs following the steps noted in the locals.tf file.
