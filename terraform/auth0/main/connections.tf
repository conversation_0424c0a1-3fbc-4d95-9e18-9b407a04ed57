resource "auth0_connection" "robots" {
  name     = "RobotAuthDB"
  realms   = ["RobotAuthDB"]
  strategy = "auth0"
  options {
    brute_force_protection = true
    requires_username = true
    disable_signup = true
    validation {
      username {
        max = 15
        min = 1
      }
    }
  }
}

resource "auth0_connection_clients" "robots" {
  connection_id = auth0_connection.robots.id
  enabled_clients = [
    auth0_client.idm.id,
  ]
}

resource "auth0_connection" "users" {
  name     = "Username-Password-Authentication"
  realms   = ["Username-Password-Authentication"]
  strategy = "auth0"
  options {
    brute_force_protection = true
    disable_signup = true
  }
}

resource "auth0_connection_clients" "users" {
  connection_id = auth0_connection.users.id
  enabled_clients = concat([
    auth0_client.companion.id,
    auth0_client.idm.id,
    auth0_client.operator.id,
    auth0_client.portal_robots.id,
    auth0_client.portal_users.id,
    auth0_client.portal_management.id,
    auth0_client.rtc.id,
    auth0_client.rtc_muxer.id,
    auth0_client.veselka.id,
    auth0_client.veselka2.id,
  ], var.rtc.extra_clients)
}

resource "auth0_connection" "apple" {
  name     = "apple"
  strategy = "apple"
  options {
    client_id = var.apple.client_id
    client_secret = var.apple_client_secret
    key_id    = var.apple.key_id
    scopes    = ["email", "name"]
    team_id   = var.apple.team_id
    disable_signup = false
    set_user_root_attributes = "on_first_login"
  }
}

resource "auth0_connection_clients" "apple" {
  connection_id = auth0_connection.apple.id
  enabled_clients = [
    auth0_client.companion.id,
    auth0_client.operator.id,
  ]
}

resource "auth0_connection" "google" {
  name     = "google-oauth2"
  strategy = "google-oauth2"
  
  options {
    scopes = ["email", "profile"]
    client_id = var.google.client_id
    client_secret = var.google_client_secret
    set_user_root_attributes = "on_first_login"
  }
}

resource "auth0_connection_clients" "google" {
  connection_id = auth0_connection.google.id
  enabled_clients = concat([
    auth0_client.companion.id,
    auth0_client.operator.id,
    auth0_client.portal_users.id,
    auth0_client.rtc.id,
    auth0_client.veselka.id,
    auth0_client.veselka2.id,
  ], var.rtc.extra_clients)
}
