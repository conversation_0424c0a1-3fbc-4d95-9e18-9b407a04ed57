###
# COMPANION APP
###

resource "auth0_client" "companion" {
  app_type                      = "native"
  name                          = "Carbon Robotics Companion"
  description                   = "Application for the Carbon Robotics Companion mobile app"
  logo_uri                      = var.companion.logo_uri
  cross_origin_auth             = true
  organization_require_behavior = "no_prompt"
  organization_usage            = "deny"
  allowed_logout_urls = concat(
    [for id in var.companion.native_ids : "carbon://${var.auth0_domain}/android/${id}/callback"],
    [for id in var.companion.native_ids : "${id}://${var.auth0_domain}/ios/${id}/callback"],
    [for id in var.companion.native_ids : "${id}://${var.auth0_domain}/macos/${id}/callback"],
  )
  callbacks = concat(
    [for id in var.companion.native_ids : "carbon://${var.auth0_domain}/android/${id}/callback"],
    [for id in var.companion.native_ids : "${id}://${var.auth0_domain}/ios/${id}/callback"],
    [for id in var.companion.native_ids : "${id}://${var.auth0_domain}/macos/${id}/callback"],
  )
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token"
  ]
  mobile {
    android {
      app_package_name = var.companion.native_ids[0]
    }
    ios {
      app_bundle_identifier = var.companion.native_ids[0]
      team_id               = var.apple.team_id
    }
  }
  native_social_login {
    apple {
      enabled = true
    }
    facebook {
      enabled = false
    }
  }
}

resource "auth0_client_credentials" "companion" {
  authentication_method = "none"
  client_id             = auth0_client.companion.client_id
}

###
# OPERATOR APP
###

resource "auth0_client" "operator" {
  app_type          = "native"
  name              = "Carbon Robotics Operator"
  description       = "Application for the Carbon Robotics Operator tablet app"
  logo_uri          = var.operator.logo_uri
  cross_origin_auth = true
  allowed_logout_urls = concat(
    [for id in var.operator.native_ids : "${id}://${var.auth0_domain}/ios/${id}/callback"],
    [for id in var.operator.native_ids : "${id}://${var.auth0_domain}/macos/${id}/callback"],
  )
  callbacks = concat(
    [for id in var.operator.native_ids : "${id}://${var.auth0_domain}/ios/${id}/callback"],
    [for id in var.operator.native_ids : "${id}://${var.auth0_domain}/macos/${id}/callback"],
  )
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token"
  ]
  mobile {
    android {
      app_package_name = var.operator.native_ids[0]
    }
    ios {
      app_bundle_identifier = var.operator.native_ids[0]
      team_id               = var.apple.team_id
    }
  }
  native_social_login {
    apple {
      enabled = false
    }
    facebook {
      enabled = false
    }
  }
}

resource "auth0_client_credentials" "operator" {
  authentication_method = "none"
  client_id             = auth0_client.operator.client_id
}

###
# RTC
###
resource "auth0_client" "rtc_wss_management" {
  app_type          = "non_interactive"
  name              = "RTC WSS (Management)"
  description       = "Application for RTC web-socket-server"
  logo_uri          = var.rtc.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "rtc_wss_management" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.rtc_wss_management.client_id
}

resource "auth0_client_grant" "rtc_wss_management-auth0_management" {
  audience  = auth0_resource_server.auth0_management.identifier
  client_id = auth0_client.rtc_wss_management.client_id
  scopes = [
    "read:users",
    "read:roles",
    "read:role_members"
  ]
}

resource "auth0_client" "rtc_muxer" {
  app_type          = "non_interactive"
  name              = "RTC Muxer"
  description       = "Muxer service for RTC Laserweeders and AutoTractors"
  logo_uri          = var.rtc.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "rtc_muxer" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.rtc_muxer.client_id
}

resource "auth0_client_grant" "rtc_muxer-robot" {
  audience  = auth0_resource_server.robot.identifier
  client_id = auth0_client.rtc_muxer.client_id
  scopes = []
}
resource "auth0_client" "rtc" {
  app_type          = "spa"
  name              = "RTC"
  description       = "RTC Application for Laserweeders and AutoTractors"
  logo_uri          = var.rtc.logo_uri
  cross_origin_auth = false
  allowed_origins = concat(
    [for url in var.rtc.urls : url],
  )
  allowed_logout_urls = concat(
    [for url in var.rtc.urls : url],
    [for url in var.rtc.urls : "${url}/rtc/ui"],
  )
  callbacks = concat(
    [for url in var.rtc.urls : "${url}/callback/"],
    [for url in var.rtc.urls : "${url}/rtc/ui/callback/"],
  )
  web_origins        = [for url in var.rtc.urls : url]
  initiate_login_uri = "${var.rtc.urls[0]}/login"
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token"
  ]
}

resource "auth0_client_credentials" "rtc" {
  authentication_method = "none"
  client_id             = auth0_client.rtc.client_id
}

###
# Veselka
###

resource "auth0_client" "veselka_management" {
  app_type          = "non_interactive"
  name              = "Veselka (Management)"
  description       = "Application for Veselka backend"
  logo_uri          = var.veselka.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "veselka_management" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.veselka_management.client_id
}

resource "auth0_client_grant" "veselka_management-auth0_management" {
  audience  = auth0_resource_server.auth0_management.identifier
  client_id = auth0_client.veselka_management.client_id
  scopes = [
    "read:users",
    "update:users",
    "read:roles",
    "update:roles",
    "create:role_members",
    "read:role_members"
  ]
}

resource "auth0_client" "veselka" {
  app_type            = "regular_web"
  name                = "Veselka Backend"
  description         = "Veselka frontend for 1.0 and backend for both 1.0 and 2.0"
  logo_uri            = var.veselka.logo_uri
  cross_origin_auth   = true
  allowed_logout_urls = [for url in var.veselka.urls : url]
  allowed_origins     = [for url in var.veselka.urls : url]
  callbacks           = [for url in var.veselka.urls : "${url}/callback"]
  web_origins         = [for url in var.veselka.urls : url]
  sso                 = false
  initiate_login_uri  = "${var.veselka.urls[0]}/login/"
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
}

resource "auth0_client_credentials" "veselka" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.veselka.client_id
}

resource "auth0_client" "veselka2" {
  app_type            = "spa"
  name                = "Veselka 2.0"
  description         = "Application for Veselka 2.0 UI"
  logo_uri            = var.veselka.logo_uri
  allowed_logout_urls = [for url in var.veselka.urls : "${url}/react/"]
  allowed_origins     = [for url in var.veselka.urls : url]
  callbacks           = [for url in var.veselka.urls : "${url}/react/callback/"]
  web_origins         = [for url in var.veselka.urls : url]
  initiate_login_uri  = "${var.veselka.urls[0]}/react/login/"
  cross_origin_auth   = false
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
}

resource "auth0_client_credentials" "veselka2" {
  authentication_method = "none"
  client_id             = auth0_client.veselka2.client_id
}

resource "auth0_client" "veselka_cv" {
  app_type          = "non_interactive"
  name              = "Veselka CV"
  logo_uri          = var.veselka.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_grant" "veselka_cv-machine_learning" {
  audience  = auth0_resource_server.machine_learning.identifier
  client_id = auth0_client.veselka_cv.id
  scopes = []
}

###
# Ops Center
###

resource "auth0_client" "portal_management" {
  app_type          = "non_interactive"
  name              = "Ops Center (Management)"
  description       = "Application for the Carbon Robotics Ops Center backend"
  logo_uri          = var.portal.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "portal_management" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.portal_management.client_id
}

resource "auth0_client_grant" "portal_management-auth0_management" {
  audience  = auth0_resource_server.auth0_management.identifier
  client_id = auth0_client.portal_management.client_id
  scopes = [
    "create:role_members",
    "create:users",
    "create:user_tickets",
    "create:users_app_metadata",
    "delete:role_members",
    "delete:users",
    "delete:users_app_metadata",
    "read:role_members",
    "read:roles",
    "read:users",
    "read:users_app_metadata",
    "update:roles",
    "update:users",
    "update:users_app_metadata",
  ]
}

resource "auth0_client" "portal_robots" {
  app_type          = "non_interactive"
  name              = "Ops Center (Robots)"
  description       = "Application for robots to communicate with Ops Center"
  logo_uri          = var.portal.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "portal_robots" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.portal_robots.client_id
}

resource "auth0_client_grant" "portal_robots-robot" {
  audience  = auth0_resource_server.robot.identifier
  client_id = auth0_client.portal_robots.client_id
  scopes = []
}

resource "auth0_client" "portal_users" {
  app_type            = "spa"
  name                = "Ops Center"
  description         = "Application for Ops Center front-end and other user clients"
  logo_uri            = var.portal.logo_uri
  cross_origin_auth   = true
  allowed_logout_urls = [for url in var.portal.urls : url]
  allowed_origins     = [for url in var.portal.urls : url]
  callbacks           = [for url in var.portal.urls : "${url}/callback/"]
  web_origins         = [for url in var.portal.urls : url]
  initiate_login_uri  = "${var.portal.urls[0]}/login/"
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
}

resource "auth0_client_credentials" "portal_users" {
  authentication_method = "none"
  client_id             = auth0_client.portal_users.client_id
}

resource "auth0_client" "portal_tableau" {
  app_type          = "non_interactive"
  name              = "Ops Center (Tableau)"
  description       = "Application for Tableau Web Data Connector for Ops Center"
  cross_origin_auth = true
  callbacks = ["http://localhost:55555/Callback"]
  grant_types = [
    "authorization_code",
    "refresh_token"
  ]
  logo_uri = "https://carbon-dist.s3.amazonaws.com/icons/tableau.png"
}

resource "auth0_client_credentials" "portal_tableau" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.portal_tableau.client_id
}

resource "auth0_client_grant" "portal_tableau-portal" {
  audience  = auth0_resource_server.portal.identifier
  client_id = auth0_client.portal_tableau.client_id
  scopes = [
    "read:lasers:all",
    "read:metrics:all",
    "read:metrics_customer:all",
    "read:metrics_internal:all",
    "read:robots:all",
  ]
}

###
# IDM
###

resource "auth0_client" "idm" {
  app_type          = "non_interactive"
  name              = "IDM"
  description       = "Application for the Carbon Robotics identity management service"
  logo_uri          = var.idm.logo_uri
  cross_origin_auth = true
  allowed_origins   = [for url in var.idm.urls : url]
  grant_types = [
    "client_credentials",
    "password",
    "http://auth0.com/oauth/grant-type/password-realm"
  ]
}

resource "auth0_client_credentials" "idm" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.idm.client_id
}

resource "auth0_client_grant" "idm-robot" {
  audience  = auth0_resource_server.robot.identifier
  client_id = auth0_client.idm.client_id
  scopes = []
}

resource "auth0_client_grant" "idm-auth0_management" {
  audience  = auth0_resource_server.auth0_management.identifier
  client_id = auth0_client.idm.client_id
  scopes = [
    "read:role_members",
    "read:roles",
    "read:users",
    "read:users_app_metadata",
  ]
}

###
# Datacenter
###

resource "auth0_client" "datacenter" {
  app_type          = "non_interactive"
  name              = "Datacenter"
  description       = "Application for the interactions with services in the datacenter"
  logo_uri          = var.datacenter.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_credentials" "datacenter" {
  authentication_method = "client_secret_post"
  client_id             = auth0_client.datacenter.client_id
}

resource "auth0_client_grant" "datacenter-datacenter" {
  audience  = auth0_resource_server.datacenter.identifier
  client_id = auth0_client.datacenter.client_id
  scopes = []
}

resource "auth0_client_grant" "datacenter-machine_learning" {
  audience  = auth0_resource_server.machine_learning.identifier
  client_id = auth0_client.datacenter.client_id
  scopes = []
}

###
# Deep Learning
###

resource "auth0_client" "machine_learning" {
  app_type          = "non_interactive"
  name              = "Deep Learning"
  description       = "Application for Deep Learning pipeline services"
  logo_uri          = var.ml.logo_uri
  cross_origin_auth = true
}

resource "auth0_client_grant" "machine_learning-machine_learning" {
  audience  = auth0_resource_server.machine_learning.identifier
  client_id = auth0_client.machine_learning.client_id
  scopes = []
}
