locals {
  rtc-env-test = "testing"
}

// RTC WebSocketServer test
resource "aws_iam_policy" "WebSocketServerPolicyTest" {
  name        = "WebSocketServerPolicyTest"
  description = "policy for rtc web socket server"
  tags        = local.common_tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "autoscaling:SetDesiredCapacity"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:autoscaling:us-west-2:645516868483:autoScalingGroup:*"]
        "Condition" : {
          "StringEquals" : {
            "autoscaling:ResourceTag/rtc" : ["muxer"],
            "autoscaling:ResourceTag/environment" : [local.rtc-env-test]
          }
        }
      }
    ]
  })
}

// RTC Locator test
resource "aws_iam_policy" "LocatorPolicyTest" {
  name        = "LocatorPolicyTest"
  description = "policy for rtc locator"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.LocatorPolicyDocumentTest.json
}

data "aws_iam_policy_document" "LocatorPolicyDocumentTest" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:PutObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-tableau-exports/testing/rtc-locator/*"
    ]
  }
}

// RTC Jobs test
resource "aws_iam_policy" "JobsPolicyRtcTest" {
  name        = "JobsPolicyTest"
  description = "policy for rtc jobs service"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.JobsPolicyDocumentRtcTest.json
}

data "aws_iam_policy_document" "JobsPolicyDocumentRtcTest" {
  version = "2012-10-17"
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:lambda:us-west-2:645516868483:function:path-planner-test"
    ]
  }
}

// RTC Muxer test
resource "aws_iam_instance_profile" "RTCMuxerInstanceProfileTest" {
  name = "RTCMuxerInstanceProfileTest"
  role = aws_iam_role.RTCMuxerInstanceRoleTest.name
}

resource "aws_iam_role" "RTCMuxerInstanceRoleTest" {
  name = "RTCMuxerInstanceRoleTest"
  tags = local.common_tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentTest_SSMManagedInstanceCore" {
  role       = aws_iam_role.RTCMuxerInstanceRoleTest.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentTest" {
  role       = aws_iam_role.RTCMuxerInstanceRoleTest.name
  policy_arn = aws_iam_policy.RTCMuxerInstancePolicyTest.arn
}

resource "aws_iam_policy" "RTCMuxerInstancePolicyTest" {
  name = "RTCMuxerInstancePolicyTest"
  tags = local.common_tags

  policy = data.aws_iam_policy_document.RTCMuxerInstancePolicyTestDocument.json
}

data "aws_iam_policy_document" "RTCMuxerInstancePolicyTestDocument" {
  statement {
    sid = "S3Access"
    effect = "Allow"
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::carbon-rtc-video-test",
      "arn:aws:s3:::carbon-rtc-video-test/*"
    ]
  }

  statement {
    sid = "ECRImagePull"
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ]
    resources = [
      "arn:*:ecr:*:*:repository/rtc/muxer"
    ]
  }

  statement {
    sid = "GrantECRAuthAccess"
    effect = "Allow"
    actions = [
      "ecr:GetAuthorizationToken"
    ]
    resources = ["*"]
  }

  statement {
    sid = "SecretAccess"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue"
    ]
    resources = [
      "arn:aws:secretsmanager:*:*:secret:${local.rtc-env-test}/muxer/*"
    ]
  }

  statement {
    actions = [
      "logs:DescribeLogGroups"
    ]
    resources = ["arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-test}"]
  }

  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:DescribeLogStreams",
      "logs:PutLogEvents",
    ]
    resources = [
      "arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-test}:*",
    ]
  }
}
