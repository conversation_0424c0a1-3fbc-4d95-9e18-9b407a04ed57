# RTC Path Planner Test
resource "aws_iam_role" "rtc_path_planner_test" {
  name = "RtcPathPlannerTest"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "rtc_path_planner_test_attach" {
  role       = aws_iam_role.rtc_path_planner_test.name
  policy_arn = aws_iam_policy.rtc_path_planner_test.arn
}

resource "aws_iam_policy" "rtc_path_planner_test" {
  name = "RtcPathPlannerTestPolicy"
  policy = data.aws_iam_policy_document.rtc_path_planner_test.json
}

data "aws_iam_policy_document" "rtc_path_planner_test" {
  version = "2012-10-17"
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:logs:*:*:log-group:/aws/lambda/path-planner-test:*:*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer",
    ]
    resources = ["arn:aws:ecr:*:*:repository/rtc/path-planner-lambda"]
  }
}

# RTC Path Planner Stage
resource "aws_iam_role" "rtc_path_planner_stage" {
  name = "RtcPathPlannerStage"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "rtc_path_planner_stage_attach" {
  role       = aws_iam_role.rtc_path_planner_stage.name
  policy_arn = aws_iam_policy.rtc_path_planner_stage.arn
}

resource "aws_iam_policy" "rtc_path_planner_stage" {
  name = "RtcPathPlannerStagePolicy"
  policy = data.aws_iam_policy_document.rtc_path_planner_stage.json
}

data "aws_iam_policy_document" "rtc_path_planner_stage" {
  version = "2012-10-17"
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:logs:*:*:log-group:/aws/lambda/path-planner-stage:*:*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer",
    ]
    resources = ["arn:aws:ecr:*:*:repository/rtc/path-planner-lambda"]
  }
}

# RTC Path Planner Prod
resource "aws_iam_role" "rtc_path_planner_prod" {
  name = "RtcPathPlannerProd"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
  tags = local.common_tags
}

resource "aws_iam_role_policy_attachment" "rtc_path_planner_prod_attach" {
  role       = aws_iam_role.rtc_path_planner_prod.name
  policy_arn = aws_iam_policy.rtc_path_planner_prod.arn
}

resource "aws_iam_policy" "rtc_path_planner_prod" {
  name = "RtcPathPlannerProdPolicy"
  policy = data.aws_iam_policy_document.rtc_path_planner_prod.json
}

data "aws_iam_policy_document" "rtc_path_planner_prod" {
  version = "2012-10-17"
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:logs:*:*:log-group:/aws/lambda/path-planner-prod:*:*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer",
    ]
    resources = ["arn:aws:ecr:*:*:repository/rtc/path-planner-lambda"]
  }
}

# Cloud Image Lambda Test (deprecated)
resource "aws_iam_role" "cloud_image_lambda_test" {
  name               = "CloudImageLambdaTest"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
}

resource "aws_iam_role_policy_attachment" "LambdaRoleAttach" {
  role       = aws_iam_role.cloud_image_lambda_test.name
  policy_arn = aws_iam_policy.cloud_image_service_test.arn
}

data "aws_iam_policy_document" "LambdaAssumeRolePolicyDocument" {
  version = "2012-10-17"
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

# Shared Image Service Resources
data "aws_iam_policy_document" "image_service_lambda_resource_access" {
  version = "2012-10-17"
  statement {
    effect = "Allow"
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:BatchCheckLayerAvailability",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage"
    ]
    resources = ["arn:aws:ecr:*:*:repository/image-service-lambda"]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:List*",
      "s3:Get*"
    ]
    resources = [
      "*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectTagging"
    ]
    resources = [
      "arn:aws:s3:::carbon-image-transforms",
      "arn:aws:s3:::carbon-image-transforms/*",
    ]
  }
}

# Image Service Test
resource "aws_iam_role" "image_service_lambda_test" {
  name               = "image_service_lambda_test"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
}

data "aws_iam_policy_document" "image_service_lambda_test" {
  version = "2012-10-17"
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["arn:aws:logs:*:*:log-group:/aws/lambda/image-service-testing:*:*"]
  }
}

resource "aws_iam_role_policy" "image_service_lambda_test_logs" {
  name = "image_service_lambda_test_logs"
  role = aws_iam_role.image_service_lambda_test.id

  policy = data.aws_iam_policy_document.image_service_lambda_test.json
}

resource "aws_iam_role_policy" "image_service_lambda_test_resources" {
  name = "image_service_lambda_test_ecr"
  role = aws_iam_role.image_service_lambda_test.id

  policy = data.aws_iam_policy_document.image_service_lambda_resource_access.json
}

# Image Service Stage
resource "aws_iam_role" "image_service_lambda_stage" {
  name               = "image_service_lambda_stage"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
}

data "aws_iam_policy_document" "image_service_lambda_stage" {
  version = "2012-10-17"
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["arn:aws:logs:*:*:log-group:/aws/lambda/image-service-staging:*:*"]
  }
}

resource "aws_iam_role_policy" "image_service_lambda_stage_logs" {
  name = "image_service_lambda_stage_logs"
  role = aws_iam_role.image_service_lambda_stage.id

  policy = data.aws_iam_policy_document.image_service_lambda_stage.json
}

resource "aws_iam_role_policy" "image_service_lambda_stage_resources" {
  name = "image_service_lambda_stage_ecr"
  role = aws_iam_role.image_service_lambda_stage.id

  policy = data.aws_iam_policy_document.image_service_lambda_resource_access.json
}

# Image Service Prod
resource "aws_iam_role" "image_service_lambda" {
  name               = "image_service_lambda"
  assume_role_policy = data.aws_iam_policy_document.LambdaAssumeRolePolicyDocument.json
}

data "aws_iam_policy_document" "image_service_lambda" {
  version = "2012-10-17"
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["arn:aws:logs:*:*:log-group:/aws/lambda/image-service:*:*"]
  }
}

resource "aws_iam_role_policy" "image_service_lambda_logs" {
  name = "image_service_lambda_logs"
  role = aws_iam_role.image_service_lambda.id

  policy = data.aws_iam_policy_document.image_service_lambda.json
}

resource "aws_iam_role_policy" "image_service_lambda_resources" {
  name = "image_service_lambda_ecr"
  role = aws_iam_role.image_service_lambda.id

  policy = data.aws_iam_policy_document.image_service_lambda_resource_access.json
}
