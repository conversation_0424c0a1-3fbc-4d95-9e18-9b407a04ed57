// AwsAdminGroup - AWS Administrator group
resource "aws_iam_group" "AwsAdminGroup" {
  name = "AwsAdminGroup"
}

# resource "aws_iam_group_policy" "AwsAdminGroupPolicy" {
#   name = "AwsAdminGroupPolicy"
#   group = aws_iam_group.AwsAdminGroup.name
#
#   policy = jsonencode({
#     "Version" = "2012-10-17"
#       "Statement": [
#       ]
#   })
# }

resource "aws_iam_group_policy_attachment" "AwsAdminGroupPolicy_IAMFullAccess_attach" {
  group      = aws_iam_group.AwsAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMFullAccess"
}

resource "aws_iam_group_policy_attachment" "AwsAdminGroupPolicy_IAMAccessAnalyzerFullAccess_attach" {
  group      = aws_iam_group.AwsAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMAccessAnalyzerFullAccess"
}

resource "aws_iam_group_policy_attachment" "AwsAdminGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.AwsAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "AwsAdminGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.AwsAdminGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// SoftwareCloudAdminGroup - AWS Cloud Admin (EKS) group
resource "aws_iam_group" "SoftwareCloudAdminGroup" {
  name = "SoftwareCloudAdminGroup"
}

resource "aws_iam_group_policy" "SoftwareCloudAdminGroupPolicy" {
  name  = "SoftwareCloudAdminGroupPolicy"
  group = aws_iam_group.SoftwareCloudAdminGroup.name

  policy = data.aws_iam_policy_document.SoftwareCloudAdminGroupPolicyDocument.json
}

data "aws_iam_policy_document" "SoftwareCloudAdminGroupPolicyDocument" {
  version = "2012-10-17"
  statement {
    actions = [
      "ssm:*",
      "eks:*",
      "cloudformation:*",
      "acm-pca:*"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AmazonRoute53FullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonRoute53FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AmazonEC2ContainerRegistryFullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AWSCertificateManagerFullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCertificateManagerFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AmazonEC2FullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AmazonElasticFileSystemFullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonElasticFileSystemFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AmazonVPCFullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonVPCFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_AWSLambda_FullAccess_attach" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AWSLambda_FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareCloudAdminGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.SoftwareCloudAdminGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// SoftwareServiceAdminGroup - AWS Services admin, supplemental group for managing aws services used.
resource "aws_iam_group" "SoftwareServiceAdminGroup" {
  name = "SoftwareServiceAdminGroup"
}

# resource "aws_iam_group_policy" "SoftwareServiceAdminGroupPolicy" {
#   name = "SoftwareServiceAdminGroupPolicy"
#   group = aws_iam_group.SoftwareServiceAdminGroup.name
#
#   policy = jsonencode({
#     "Version" = "2012-10-17"
#       "Statement": [
#       ]
#   })
# }

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_CloudFrontFullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/CloudFrontFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_AmazonS3FullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_AmazonSQSFullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_AWSLambda_FullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AWSLambda_FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_AmazonElastiCacheFullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonElastiCacheFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_AmazonRDSFullAccess_attach" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonRDSFullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareServiceAdminGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.SoftwareServiceAdminGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// SoftwareUserGroup - Software users group
resource "aws_iam_group" "SoftwareUserGroup" {
  name = "SoftwareUserGroup"
}

resource "aws_iam_group_policy" "SoftwareGroupPolicy" {
  name   = "SoftwareGroupPolicy"
  group  = aws_iam_group.SoftwareUserGroup.name
  policy = data.aws_iam_policy_document.SoftwareGroupPolicyDocument.json
}

data "aws_iam_policy_document" "SoftwareGroupPolicyDocument" {
  version = "2012-10-17"
  statement {
    sid = "IAMRead"
    actions = [
      "iam:GetPolicy",
      "iam:GetPolicyVersion",
      "iam:GetRole",
      "iam:GetRolePolicy",
      "iam:ListAttachedRolePolicies",
      "iam:ListRolePolicies",
      "iam:ListRoles",
    ]
    resources = [
      "*"
    ]
  }

  statement {
    sid = "KMSRead"
    actions = [
      "kms:ListAliases",
    ]
    resources = [
      "*"
    ]
  }

  statement {
    sid = "CloudformationRead"
    actions = [
      "cloudformation:DescribeStacks",
      "cloudformation:ListStacks",
      "cloudformation:ListStackResources",
    ]
    resources = [
      "*"
    ]
  }

  statement {
    sid = "EC2Read"
    actions = [
      "ec2:Describe*",
    ]
    resources = [
      "*"
    ]
  }

  statement {
    actions = [
      "s3:List*",
      "s3:Get*"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::*",
      "arn:aws:s3:::*/*",
    ]
  }
  statement {
    actions = [
      "s3:PutObject",
      "s3:PutObjectTagging"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-automation-testing/*",
      "arn:aws:s3:::carbon-cloud-app/*",
      "arn:aws:s3:::carbon-diagnostics/*",
      "arn:aws:s3:::carbon-image-transforms/*",
      "arn:aws:s3:::carbon-images/*",
      "arn:aws:s3:::carbon-ingest/*",
      "arn:aws:s3:::carbon-logs/*",
      "arn:aws:s3:::carbon-portal/*",
      "arn:aws:s3:::carbon-portal-staging/*",
      "arn:aws:s3:::carbon-portal-testing/*",
      "arn:aws:s3:::carbon-robot-bud/*",
      "arn:aws:s3:::carbon-robot-syncer/*",
      "arn:aws:s3:::carbon-robot-syncer-staging/*",
      "arn:aws:s3:::carbon-robot-syncer-testing/*",
      "arn:aws:s3:::carbon-rtc-video/*",
      "arn:aws:s3:::carbon-rtc-video-test/*",
      "arn:aws:s3:::carbon-version-metadata/*",
      "arn:aws:s3:::carbon-grimmway-export/*",
      "arn:aws:s3:::maka-build-artifacts/*",
    ]
  }
  statement {
    actions = [
      "s3:DeleteObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-automation-testing/*",
      "arn:aws:s3:::carbon-portal-staging/*",
      "arn:aws:s3:::carbon-portal-testing/*",
      "arn:aws:s3:::carbon-robot-syncer-staging/*",
      "arn:aws:s3:::carbon-robot-syncer-testing/*",
      "arn:aws:s3:::carbon-rtc-video-test/*",
    ]
  }

  statement {
    actions = [
      "eks:DescribeCluster",
      "eks:ListClusters"
    ]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    actions = [
      "rds:Describe*",
      "rds:DownloadDBLogFile",
      "rds:DownloadDBLogFilePortion"
    ]
    effect = "Allow"
    resources = ["*"]
  }

  statement {
    sid = "ElasticaceRead"
    actions = [
      "elasticache:Describe*"
    ]
    effect = "Allow"
    resources = ["*"]
  }

  statement {
    actions = [
      "sqs:CancelMessageMoveTask",
      "sqs:ChangeMessageVisibility",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
      "sqs:GetQueueUrl",
      "sqs:ListDeadLetterSourceQueues",
      "sqs:ListMessageMoveTasks",
      "sqs:ListQueueTags",
      "sqs:ListQueues",
      "sqs:PurgeQueue",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
      "sqs:SetQueueAttributes",
      "sqs:StartMessageMoveTask"
    ]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    sid = "ApplicaitonRead"
    actions = [
      "application-autoscaling:DescribeScalingPolicies",
      "application-signals:BatchGet*",
      "application-signals:Get*",
      "application-signals:List*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "AutoscalingRead"
    actions = [
      "autoscaling:Describe*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "SNSRead"
    actions = [
      "sns:Get*",
      "sns:List*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "RUMRead"
    actions = [
      "rum:BatchGet*",
      "rum:Get*",
      "rum:List*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "SyntheticsRead"
    actions = [
      "synthetics:Describe*",
      "synthetics:Get*",
      "synthetics:List*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "XRAYRead"
    actions = [
      "xray:BatchGet*",
      "xray:Get*",
      "xray:List*",
      "xray:StartTraceRetrieval",
      "xray:CancelTraceRetrieval"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "CloudwatchRead"
    actions = [
      "cloudwatch:BatchGet*",
      "cloudwatch:Describe*",
      "cloudwatch:GenerateQuery",
      "cloudwatch:Get*",
      "cloudwatch:List*",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }
  statement {
    sid = "CloudwatchLogsRead"
    actions = [
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
      "logs:FilterLogEvents",
      "logs:GetLogEvents",
      "logs:GetLogGroupFields",
      "logs:GetLogRecord",
      "logs:GetQueryResults",
      "logs:DescribeQueries",
      "logs:GetQueryResults",
      "logs:StartQuery",
      "logs:StopQuery",
      "logs:StartLiveTail",
      "logs:StopLiveTail"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    actions = [
      "sts:AssumeRole"
    ]
    effect = "Allow"
    resources = [
      aws_iam_role.eks_developer.arn
    ]
  }

  statement {
    sid = "ECRReadPull"
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:Get*",
      "ecr:BatchGet*",
      "ecr:List*",
      "ecr:Describe*",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchImportUpstreamImage",
      "ecr:BatchCheckLayerAvailability",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "ECRWrite"
    actions = [
      "ecr:CompleteLayerUpload",
      "ecr:InitiateLayerUpload",
      "ecr:PutImage",
      "ecr:UploadLayerPart",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:ecr:*:*:repository/image-service-lambda",
      "arn:aws:ecr:*:*:repository/rtc/*"
    ]
  }

  statement {
    sid = "OAMReadPermissions"
    actions = [
      "oam:ListAttachedLinks"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:oam:*:*:sink/*"
    ]
  }

  statement {
    sid = "LambdaRead"
    actions = [
      "lambda:Get*",
      "lambda:List*",
      "states:DescribeStateMachine",
      "states:ListStateMachines",
      "tag:GetResources",
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    sid = "LambdaWrite"
    actions = [
      "lambda:InvokeFunction",
      "lambda:Update*",
      "lambda:Publish*",
      "lambda:Put*",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:lambda:*:*:function:*",
    ]
  }

  statement {
    sid = "SecretsRead"
    actions = [
      "secretsmanager:ListSecrets",
      "secretsmanager:DescribeSecret",
      "secretsmanager:GetSecretValue"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }
  statement {
    sid = "SecretsWrite"
    actions = [
      "secretsmanager:PutSecretValue"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_AmazonRDSPerformanceInsightsReadOnly" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonRDSPerformanceInsightsReadOnly"
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_AmazonEC2FullAccess_attach" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_RtcSSMInstanceAccessPolicy_attach" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = aws_iam_policy.RtcSSMInstanceAccessPolicy.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareUserGroup_CostExplorerReadOnly_attach" {
  group      = aws_iam_group.SoftwareUserGroup.name
  policy_arn = aws_iam_policy.cost_explorer_readonly.arn
}

// SoftwareInternUserGroup - Software Intern users group
resource "aws_iam_group" "SoftwareInternUserGroup" {
  name = "SoftwareInternUserGroup"
}

resource "aws_iam_group_policy" "SoftwareInternGroupPolicy" {
  name   = "SoftwareInternGroupPolicy"
  group  = aws_iam_group.SoftwareInternUserGroup.name
  policy = data.aws_iam_policy_document.SoftwareInternGroupPolicyDocument.json
}

data "aws_iam_policy_document" "SoftwareInternGroupPolicyDocument" {
  version = "2012-10-17"

  statement {
    actions = [
      "s3:List*",
      "s3:Get*"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-diagnostics",
      "arn:aws:s3:::carbon-diagnostics/*",
    ]
  }
}

resource "aws_iam_group_policy_attachment" "SoftwareInternUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.SoftwareInternUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "SoftwareInternUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.SoftwareInternUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "SoftwareInternUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.SoftwareInternUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// SupportUserGroup
resource "aws_iam_group" "SupportUserGroup" {
  name = "SupportUserGroup"
}

resource "aws_iam_group_policy" "SupportGroupPolicy" {
  name  = "SupportGroupPolicy"
  group = aws_iam_group.SupportUserGroup.name

  policy = data.aws_iam_policy_document.SupportGroupPolicyDocument.json
}

data "aws_iam_policy_document" "SupportGroupPolicyDocument" {
  version = "2012-10-17"
  statement {
    actions = [
      "cloudwatch:DescribeAlarms"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }
  statement {
    actions = [
      "logs:GetLogRecord",
      "logs:StartQuery",
      "logs:StopQuery",
      "logs:GetQueryResults",
      "logs:FilterLogEvents",
      "logs:GetLogEvents",
      "logs:DescribeLogGroups"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:logs:*:*:log-group:*:log-stream:*"
    ]
  }
  statement {
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::maka-pono",
      "arn:aws:s3:::maka-pono/*",
      "arn:aws:s3:::carbon-robot-bud",
      "arn:aws:s3:::carbon-robot-bud/*",
      "arn:aws:s3:::carbon-robot-syncer",
      "arn:aws:s3:::carbon-robot-syncer/*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "SupportUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.SupportUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "SupportUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.SupportUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "SupportUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.SupportUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}


// MechanicalUserGroup
resource "aws_iam_group" "MechanicalUserGroup" {
  name = "MechanicalUserGroup"
}

resource "aws_iam_group_policy" "MechanicalGroupPolicy" {
  name  = "MechanicalGroupPolicy"
  group = aws_iam_group.MechanicalUserGroup.name

  policy = data.aws_iam_policy_document.MechanicalGroupPolicyDocument.json
}

data "aws_iam_policy_document" "MechanicalGroupPolicyDocument" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:*"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::solidworkspda",
      "arn:aws:s3:::solidworkspda/*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "MechanicalUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.MechanicalUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "MechanicalUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.MechanicalUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "MechanicalUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.MechanicalUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// ProductUserGroup
resource "aws_iam_group" "ProductUserGroup" {
  name = "ProductUserGroup"
}

resource "aws_iam_group_policy" "ProductGroupPolicy" {
  name  = "ProductGroupPolicy"
  group = aws_iam_group.ProductUserGroup.name

  policy = data.aws_iam_policy_document.ProductGroupPolicyDocument.json
}

data "aws_iam_policy_document" "ProductGroupPolicyDocument" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:ListBucket",
      "s3:GetBucket",
      "s3:GetObject",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::maka-pono",
      "arn:aws:s3:::maka-pono/*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "ProductUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.ProductUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "ProductUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.ProductUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "ProductUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.ProductUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// DeepLearningUserGroup
resource "aws_iam_group" "DeepLearningUserGroup" {
  name = "DeepLearningUserGroup"
}

resource "aws_iam_group_policy" "DeepLearningGroupPolicy" {
  name  = "DeepLearningGroupPolicy"
  group = aws_iam_group.DeepLearningUserGroup.name

  policy = data.aws_iam_policy_document.DeepLearningGroupPolicy.json
}

data "aws_iam_policy_document" "DeepLearningGroupPolicy" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:ListAllMyBuckets",
      "s3:ListBucket",
      "s3:GetObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::maka-build-artifacts",
      "arn:aws:s3:::maka-build-artifacts/*"
    ]
  }
  statement {
    actions = [
      "s3:List*",
      "s3:Get*"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::maka-pono",
      "arn:aws:s3:::maka-pono/*",
      "arn:aws:s3:::carbon-cloud-app",
      "arn:aws:s3:::carbon-cloud-app/*",
      "arn:aws:s3:::carbon-comparison-evaluations",
      "arn:aws:s3:::carbon-comparison-evaluations/*",
      "arn:aws:s3:::carbon-plant-captcha",
      "arn:aws:s3:::carbon-plant-captcha/*",
      "arn:aws:s3:::carbon-ml",
      "arn:aws:s3:::carbon-ml/*",
      "arn:aws:s3:::carbon-logs",
      "arn:aws:s3:::carbon-logs/*",
    ]
  }
  statement {
    actions = [
      "s3:PutObject",
      "s3:PutObjectTagging"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::maka-pono/models/*",
      "arn:aws:s3:::maka-pono/evaluations/*",
      "arn:aws:s3:::maka-pono/media/p2p/*",    // This should be temporary
      "arn:aws:s3:::maka-pono/media/v2-p2p/*", // This should be temporary
      "arn:aws:s3:::carbon-ml/*",
      "arn:aws:s3:::carbon-plant-captcha/*",
      "arn:aws:s3:::carbon-comparison-evaluations/*",
      // access for pushing to comparison tool
      "arn:aws:s3:::carbon-cloud-app/comparison/production/size_category_options/*",
      "arn:aws:s3:::carbon-cloud-app/comparison/staging/size_category_options/*",
      "arn:aws:s3:::carbon-cloud-app/comparison/testing/size_category_options/*",
    ]
  }
  statement {
    actions = [
      "s3:DeleteObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-ml/*",
      "arn:aws:s3:::carbon-automation-testing/*",
    ]
  }
  statement {
    actions = [
      "rds:Describe*",
      "elasticache:Describe*"
    ]
    resources = ["*"]
  }
  statement {
    actions = [
      "eks:DescribeCluster",
      "eks:ListClusters"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
  statement {
    actions = [
      "ecr:BatchCheckLayerAvailability",
      "ecr:BatchGetImage",
      "ecr:CompleteLayerUpload",
      "ecr:DescribeImages",
      "ecr:GetDownloadUrlForLayer",
      "ecr:InitiateLayerUpload",
      "ecr:PutImage",
      "ecr:UploadLayerPart",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:ecr:*:*:repository/image-service-lambda"
    ]
  }

  statement {
    actions = [
      "ecr:GetAuthorizationToken"
    ]
    effect = "Allow"
    resources = [
      "*"
    ]
  }

  statement {
    actions = [
      "lambda:GetFunction",
      "lambda:InvokeFunction",
      "lambda:ListFunctions",
      "lambda:Update*",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:lambda:*:*:function:image-service",
      "arn:aws:lambda:*:*:function:image-service-staging",
      "arn:aws:lambda:*:*:function:image-service-testing",
    ]
  }
  statement {
    actions = [
      "logs:GetLogEvents",
      "logs:GetLogGroup",
      "logs:ListLogGroups"
    ]
    resources = ["*"]
  }
  statement {
    actions = [
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
      "logs:FilterLogEvents",
      "logs:GetLogEvents",
      "logs:GetLogGroupFields",
      "logs:GetLogRecord",
      "logs:GetQueryResults",
      "logs:StartQuery",
      "logs:StopQuery"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:logs:*:*:log-group:robot-config-audit:*",
      "arn:aws:logs:*:*:log-group:robot-config-audit-staging:*",
      "arn:aws:logs:*:*:log-group:robot-config-audit-testing:*"
    ]
  }
}

resource "aws_iam_group_policy_attachment" "DeepLearningUserGroup_AmazonRDSPerformanceInsightsReadOnly" {
  group      = aws_iam_group.DeepLearningUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonRDSPerformanceInsightsReadOnly"
}

resource "aws_iam_group_policy_attachment" "DeepLearningUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.DeepLearningUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "DeepLearningUserGroup_NoMakaMediaDeletion_attach" {
  group      = aws_iam_group.DeepLearningUserGroup.name
  policy_arn = aws_iam_policy.NoMakaMediaDeletion.arn
}

resource "aws_iam_group_policy_attachment" "DeepLearningUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.DeepLearningUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

resource "aws_iam_group_policy_attachment" "DeepLearningUserGroup_CostExplorerReadOnly_attach" {
  group      = aws_iam_group.DeepLearningUserGroup.name
  policy_arn = aws_iam_policy.cost_explorer_readonly.arn
}

// AWS Billing Admin Group
resource "aws_iam_group" "BillingUserGroup" {
  name = "BillingUserGroup"
}

#resource "aws_iam_group_policy" "BillingUserGroupPolicy" {
#  name  = "BillingUserGroupPolicy"
#  group = aws_iam_group.BillingUserGroup.name
#
#  policy = jsonencode({
#    "Version" : "2012-10-17",
#    "Statement" : [
#      {
#        "Effect" : "Allow",
#        "Action" : [
#        ],
#        "Resource" : [
#        ]
#      }
#    ]
#  })
#}

resource "aws_iam_group_policy_attachment" "BillingUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.BillingUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "BillingUserGroup_AWSBillingReadOnlyAccess_attach" {
  group      = aws_iam_group.BillingUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/AWSBillingReadOnlyAccess"
}

resource "aws_iam_group_policy_attachment" "BillingUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.BillingUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// QA Group
resource "aws_iam_group" "QAUserGroup" {
  name = "QAUserGroup"
}

resource "aws_iam_group_policy" "QAUserGroupPolicy" {
  name  = "QAUserGroupPolicy"
  group = aws_iam_group.QAUserGroup.name

  policy = data.aws_iam_policy_document.QAUserGroupPolicy_document.json
}

data "aws_iam_policy_document" "QAUserGroupPolicy_document" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:DeleteObject",
      "s3:ListBucket",
      "s3:PutObject",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-portal-staging",
      "arn:aws:s3:::carbon-portal-staging/*",
      "arn:aws:s3:::carbon-portal-testing",
      "arn:aws:s3:::carbon-portal-testing/*",
      "arn:aws:s3:::carbon-automation-testing",
      "arn:aws:s3:::carbon-automation-testing/*",
    ]
  }
}

resource "aws_iam_group_policy_attachment" "QAUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.QAUserGroup.name
  policy_arn = "arn:aws:iam::aws:policy/IAMUserChangePassword"
}

resource "aws_iam_group_policy_attachment" "QAUserGroup_UserSelfManagePolicy" {
  group      = aws_iam_group.QAUserGroup.name
  policy_arn = aws_iam_policy.UserSelfManagePolicy.arn
}

// Terraform Group
resource "aws_iam_group" "TFUserGroup" {
  name = "TFUserGroup"
}

resource "aws_iam_group_policy" "TFUserGroupPolicy" {
  name  = "TFUserGroupPolicy"
  group = aws_iam_group.TFUserGroup.name

  policy = data.aws_iam_policy_document.TFUserGroupPolicy_document.json
}

data "aws_iam_policy_document" "TFUserGroupPolicy_document" {
  statement {
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:PutObject",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-terraform",
      "arn:aws:s3:::carbon-terraform/*",
    ]
  }
  statement {
    actions = [
      "s3:DeleteObject",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-terraform/terraform/auth0/production.tfstate.tflock",
      "arn:aws:s3:::carbon-terraform/terraform/auth0/staging.tfstate.tflock",
      "arn:aws:s3:::carbon-terraform/terraform/auth0/development.tfstate.tflock",
    ]
  }
}


// AWS Require MFA Group - probably everyone but admins
resource "aws_iam_group" "RequireMFAUserGroup" {
  name = "RequireMFAUserGroup"
}

resource "aws_iam_group_policy_attachment" "RequireMFAUserGroup_IAMUserChangePassword_attach" {
  group      = aws_iam_group.RequireMFAUserGroup.name
  policy_arn = aws_iam_policy.UserRequireMFAPolicy.arn
}


resource "aws_iam_policy" "cost_explorer_readonly" {
  name        = "CostExplorerReadOnly"
  description = "Grants read-only access to AWS Cost Explorer"
  policy      = data.aws_iam_policy_document.cost_explorer_readonly_document.json
}

data "aws_iam_policy_document" "cost_explorer_readonly_document" {
  version = "2012-10-17"

  statement {
    actions = [
      "ce:DescribeCostCategoryDefinition",
      "ce:DescribeNotificationSubscription",
      "ce:DescribeReport",
      "ce:Get*",
      "ce:ListCostAllocationTags",
      "ce:ListCostCategoryDefinitions",
      "ce:ListTagsForResource"
    ]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    actions = [
      "organizations:ListAccounts",
      "organizations:DescribeAccount"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}
