locals {
  rtc-env-stage = "staging"
}

// RTC WebSocketServer stage
resource "aws_iam_policy" "WebSocketServerPolicyStage" {
  name        = "WebSocketServerPolicyStage"
  description = "policy for rtc web socket server"
  tags        = local.common_tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "autoscaling:SetDesiredCapacity"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:autoscaling:us-west-2:645516868483:autoScalingGroup:*"]
        "Condition" : {
          "StringEquals" : {
            "autoscaling:ResourceTag/rtc" : ["muxer"],
            "autoscaling:ResourceTag/environment" : [local.rtc-env-stage]
          }
        }
      }
    ]
  })
}

// RTC Locator stage
resource "aws_iam_policy" "LocatorPolicyStage" {
  name        = "LocatorPolicyStage"
  description = "policy for rtc locator"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.LocatorPolicyDocumentStage.json
}

data "aws_iam_policy_document" "LocatorPolicyDocumentStage" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:PutObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-tableau-exports/staging/rtc-locator/*"
    ]
  }
}

// RTC Jobs stage
resource "aws_iam_policy" "JobsPolicyRtcStage" {
  name        = "JobsPolicyStage"
  description = "policy for rtc jobs service"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.JobsPolicyDocumentRtcStage.json
}

data "aws_iam_policy_document" "JobsPolicyDocumentRtcStage" {
  version = "2012-10-17"
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:lambda:us-west-2:645516868483:function:path-planner-stage"
    ]
  }
}

// RTC Muxer stage
resource "aws_iam_instance_profile" "RTCMuxerInstanceProfileStage" {
  name = "RTCMuxerInstanceProfileStage"
  role = aws_iam_role.RTCMuxerInstanceRoleStage.name
}

resource "aws_iam_role" "RTCMuxerInstanceRoleStage" {
  name = "RTCMuxerInstanceRoleStage"
  tags = local.common_tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentStage_SSMManagedInstanceCore" {
  role       = aws_iam_role.RTCMuxerInstanceRoleStage.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentStage" {
  role       = aws_iam_role.RTCMuxerInstanceRoleStage.name
  policy_arn = aws_iam_policy.RTCMuxerInstancePolicyStage.arn
}

resource "aws_iam_policy" "RTCMuxerInstancePolicyStage" {
  name = "RTCMuxerInstancePolicyStage"
  tags = local.common_tags

  policy = data.aws_iam_policy_document.RTCMuxerInstancePolicyStageDocument.json
}

data "aws_iam_policy_document" "RTCMuxerInstancePolicyStageDocument" {
  statement {
    sid = "ECRImagePull"
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ]
    resources = [
      "arn:*:ecr:*:*:repository/rtc/muxer"
    ]
  }

  statement {
    sid = "GrantECRAuthAccess"
    effect = "Allow"
    actions = [
      "ecr:GetAuthorizationToken"
    ]
    resources = ["*"]
  }

  statement {
    sid = "SecretAccess"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue"
    ]
    resources = [
      "arn:aws:secretsmanager:*:*:secret:${local.rtc-env-stage}/muxer/*"
    ]
  }

  statement {
    actions = [
      "logs:DescribeLogGroups"
    ]
    resources = ["arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-stage}"]
  }

  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:DescribeLogStreams",
      "logs:PutLogEvents",
    ]
    resources = [
      "arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-stage}:*",
    ]
  }
}
