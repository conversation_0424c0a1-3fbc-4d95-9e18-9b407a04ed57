locals {
  rtc-env-rtc-prod = "rtc_prod"
}

// RTC WebSocketServer rtc_prod
resource "aws_iam_policy" "WebSocketServerPolicyRtcProd" {
  name        = "WebSocketServerPolicyRtcProd"
  description = "policy for rtc web socket server"
  tags        = local.common_tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "autoscaling:SetDesiredCapacity"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:autoscaling:us-west-2:645516868483:autoScalingGroup:*"]
        "Condition" : {
          "StringEquals" : {
            "autoscaling:ResourceTag/rtc" : ["muxer"],
            "autoscaling:ResourceTag/environment" : [local.rtc-env-rtc-prod]
          }
        }
      }
    ]
  })
}

// RTC Locator rtc_prod
resource "aws_iam_policy" "LocatorPolicyRtcProd" {
  name        = "LocatorPolicyRtcProd"
  description = "policy for rtc locator"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.LocatorPolicyDocumentRtcProd.json
}

data "aws_iam_policy_document" "LocatorPolicyDocumentRtcProd" {
  version = "2012-10-17"
  statement {
    actions = [
      "s3:PutObject"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:s3:::carbon-tableau-exports/rtc-prod/rtc-locator/*"
    ]
  }
}

// RTC Jobs rtc_prod
resource "aws_iam_policy" "JobsPolicyRtcProd" {
  name        = "JobsPolicyRtcProd"
  description = "policy for rtc jobs service"
  tags        = local.common_tags

  policy = data.aws_iam_policy_document.JobsPolicyDocumentRtcProd.json
}

data "aws_iam_policy_document" "JobsPolicyDocumentRtcProd" {
  version = "2012-10-17"
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect = "Allow"
    resources = [
      "arn:aws:lambda:us-west-2:645516868483:function:path-planner-prod"
    ]
  }
}

// RTC Muxer rtc_prod
resource "aws_iam_instance_profile" "RTCMuxerInstanceProfileRtcProd" {
  name = "RTCMuxerInstanceProfileRtcProd"
  role = aws_iam_role.RTCMuxerInstanceRoleRtcProd.name
}

resource "aws_iam_role" "RTCMuxerInstanceRoleRtcProd" {
  name = "RTCMuxerInstanceRoleRtcProd"
  tags = local.common_tags

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentRtcProd_SSMManagedInstanceCore" {
  role       = aws_iam_role.RTCMuxerInstanceRoleRtcProd.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "MuxerInstancePolicyAttachmentRtcProd" {
  role       = aws_iam_role.RTCMuxerInstanceRoleRtcProd.name
  policy_arn = aws_iam_policy.RTCMuxerInstancePolicyRtcProd.arn
}

resource "aws_iam_policy" "RTCMuxerInstancePolicyRtcProd" {
  name = "RTCMuxerInstancePolicyRtcProd"
  tags = local.common_tags

  policy = data.aws_iam_policy_document.RTCMuxerInstancePolicyRtcProdDocument.json
}

data "aws_iam_policy_document" "RTCMuxerInstancePolicyRtcProdDocument" {
  statement {
    sid = "ECRImagePull"
    effect = "Allow"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ]
    resources = [
      "arn:*:ecr:*:*:repository/rtc/muxer"
    ]
  }

  statement {
    sid = "GrantECRAuthAccess"
    effect = "Allow"
    actions = [
      "ecr:GetAuthorizationToken"
    ]
    resources = ["*"]
  }

  statement {
    sid = "SecretAccess"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue"
    ]
    resources = [
      "arn:aws:secretsmanager:*:*:secret:${local.rtc-env-rtc-prod}/muxer/*"
    ]
  }

  statement {
    actions = [
      "logs:DescribeLogGroups"
    ]
    resources = ["arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-rtc-prod}"]
  }

  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:DescribeLogStreams",
      "logs:PutLogEvents",
    ]
    resources = [
      "arn:aws:logs:*:*:log-group:rtc-muxer-${local.rtc-env-rtc-prod}:*",
    ]
  }
}
