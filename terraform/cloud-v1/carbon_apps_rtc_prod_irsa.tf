// ==== RTC_PROD ====

// RTC
data "aws_iam_policy" "web_socket_server_rtc_prod" {
  name = "WebSocketServerPolicyRtcProd"
}
module "web_socket_server_irsa_rtc_prod" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.rtc_prod.metadata[0].name
  kubernetes_service_account  = "web-socket-server-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-rtc-prod-web-socket-server-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.web_socket_server_rtc_prod.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "locator_rtc_prod" {
  name = "LocatorPolicyRtcProd"
}
module "locator_rtc_prod_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.rtc_prod.metadata[0].name
  kubernetes_service_account  = "locator-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-rtc-prod-locator-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.locator_rtc_prod.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "jobs_rtc_prod" {
  name = "JobsPolicyRtcProd"
}
module "jobs_rtc_prod_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.rtc_prod.metadata[0].name
  kubernetes_service_account  = "rtc-jobs-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-rtc-prod-jobs-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.jobs_rtc_prod.arn]

  depends_on = [module.eks]
}
