// ==== TESTING ====
data "aws_iam_policy" "ingest_test" {
  name = "IngestTestUploadPolicy"
}
module "ingest_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "ingest-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-ingest-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.ingest_test.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "veselka_test" {
  name = "TestVeselkaServicePolicy"
}
module "veselka_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "veselka-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-veselka-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.veselka_test.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "veselka_analytics_test" {
  name = "TestVeselkaAnalyticsServicePolicy"
}

module "veselka_analytics_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "veselka-analytics-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-veselka-analytics-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.veselka_analytics_test.arn]

  depends_on = [module.eks]
}

// RTC
data "aws_iam_policy" "web_socket_server_test" {
  name = "WebSocketServerPolicyTest"
}
module "web_socket_server_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "web-socket-server-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-web-socket-server-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.web_socket_server_test.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "locator_test" {
  name = "LocatorPolicyTest"
}
module "locator_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "locator-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-locator-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.locator_test.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "jobs_test" {
  name = "JobsPolicyTest"
}
module "jobs_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "rtc-jobs-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-rtc-test-jobs-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.jobs_test.arn]

  depends_on = [module.eks]
}


data "aws_iam_policy" "portal_test" {
  name = "PortalTestServicePolicy"
}
module "portal_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "portal-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-portal-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.portal_test.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "robot_syncer_test" {
  name = "RobotSyncerTestServicePolicy"
}
module "robot_syncer_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "robot-syncer-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-robot-syncer-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.robot_syncer_test.arn]

  depends_on = [module.eks]
}

// Carbon Cloud Image
data "aws_iam_policy" "carbon_cloud_image_test" {
  name = "CloudImageServiceTest"
}
module "carbon_cloud_image_test_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "carbon-cloud-image-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-test-carbon-cloud-image-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.carbon_cloud_image_test.arn]

  depends_on = [module.eks]
}

# Version Metadata Service
data "aws_iam_policy" "version_metadata_testing" {
  name = "TestingVersionMetadataServicePolicy"
}
module "version_metadata_testing_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.test.metadata[0].name
  kubernetes_service_account  = "version-metadata-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-testing-version-metadata-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.version_metadata_testing.arn]

  depends_on = [module.eks]
}
