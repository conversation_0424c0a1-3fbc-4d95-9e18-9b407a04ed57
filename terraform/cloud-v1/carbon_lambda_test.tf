data "aws_iam_role" "path_planner_test" {
  name = "RtcPathPlannerTest"
}

resource "aws_lambda_function" "path_planner_test" {
  function_name = "path-planner-test"
  role          = data.aws_iam_role.path_planner_test.arn
  timeout       = 30
  memory_size   = 128
  package_type  = "Image"
  image_uri     = "${aws_ecr_repository.path_planner.repository_url}:latest"

  environment {
    variables = {
      ENVIRONMENT = "testing"
    }
  }

  tags = local.tags

  lifecycle {
    ignore_changes = [
      image_uri,
      description
    ]
  }
}

resource "aws_cloudwatch_log_group" "path_planner_test" {
  name              = "/aws/lambda/${aws_lambda_function.path_planner_test.function_name}"
  retention_in_days = 90
  tags              = local.tags
}
