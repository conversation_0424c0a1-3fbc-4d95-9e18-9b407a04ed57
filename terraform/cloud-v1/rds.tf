resource "aws_security_group" "rds_sg" {
  name        = "${local.name}-rds-vpc-acces"
  description = "allow rds access from ${local.name} vpc"
  vpc_id      = module.vpc.vpc_id
  ingress {
    from_port = 5432
    to_port   = 5432
    protocol  = "TCP"
    cidr_blocks = [module.vpc.vpc_cidr_block]
  }

  tags = local.tags
}

// Veselka Prod Aurora Cluster
resource "aws_rds_cluster" "veselka_prod" {
  cluster_identifier                  = "veselka-prod"
  engine                              = "aurora-postgresql"
  engine_version                      = "13.18"
  allow_major_version_upgrade         = false
  backup_retention_period             = 10
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false
  storage_type                        = "aurora-iopt1"
  #  snapshot_identifier                 = "arn:aws:rds:us-west-2:645516868483:snapshot:prod-aurora-migration"

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  database_name               = "carbondb"
  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

#resource "aws_rds_cluster_instance" "veselka_prod_serverless" {
#  cluster_identifier           = aws_rds_cluster.veselka_prod.id
#  identifier                   = "veselka-prod-severless-001"
#  instance_class               = "db.serverless"
#  engine                       = aws_rds_cluster.veselka_prod.engine
#  engine_version               = aws_rds_cluster.veselka_prod.engine_version
#  performance_insights_enabled = true
#}

resource "aws_rds_cluster_instance" "veselka_prod_instance_002" {
  cluster_identifier           = aws_rds_cluster.veselka_prod.id
  identifier                   = "veselka-prod-instance-002"
  instance_class               = "db.r6g.xlarge"
  engine                       = aws_rds_cluster.veselka_prod.engine
  engine_version               = aws_rds_cluster.veselka_prod.engine_version
  performance_insights_enabled = true
}

// Aurora Portal Prod
resource "aws_rds_cluster" "portal_prod" {
  cluster_identifier                  = "portal-prod"
  engine                              = "aurora-postgresql"
  engine_version                      = "13.18"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false
  #  snapshot_identifier                 = "arn:aws:rds:us-west-2:645516868483:snapshot:aurora-migration-snap-2024-06-02"

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  database_name               = "carbondb"
  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

resource "aws_rds_cluster_instance" "portal_prod_instance_001" {
  cluster_identifier           = aws_rds_cluster.portal_prod.id
  identifier                   = "portal-prod-instance-001"
  instance_class               = "db.r5.large"
  engine                       = aws_rds_cluster.portal_prod.engine
  engine_version               = aws_rds_cluster.portal_prod.engine_version
  performance_insights_enabled = true
}

// Aurora Portal Staging
resource "aws_rds_cluster" "portal_stage" {
  cluster_identifier                  = "portal-stage"
  engine                              = "aurora-postgresql"
  engine_version                      = "13.18"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false
  #  snapshot_identifier                 = "arn:aws:rds:us-west-2:645516868483:snapshot:rds:portal-db-stage-2024-05-06-14-37"

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  database_name               = "carbondb"
  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

resource "aws_rds_cluster_instance" "portal_stage_instance_001" {
  cluster_identifier           = aws_rds_cluster.portal_stage.id
  identifier                   = "portal-stage-instance-001"
  instance_class               = "db.t3.medium"
  engine                       = aws_rds_cluster.portal_stage.engine
  engine_version               = aws_rds_cluster.portal_stage.engine_version
  performance_insights_enabled = true
}

// Aurora Robot Syncer Production
resource "aws_rds_cluster" "rosy_prod" {
  cluster_identifier                  = "rosy-prod"
  engine                              = "aurora-postgresql"
  engine_version                      = "17.4"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

resource "aws_rds_cluster_instance" "rosy_prod_instance_001" {
  cluster_identifier           = aws_rds_cluster.rosy_prod.id
  identifier                   = "rosy-prod-instance-001"
  instance_class               = "db.t3.medium"
  engine                       = aws_rds_cluster.rosy_prod.engine
  engine_version               = aws_rds_cluster.rosy_prod.engine_version
  performance_insights_enabled = true
}

// Aurora RTC Production
resource "aws_rds_cluster" "rtc_prod" {
  cluster_identifier                  = "rtc-prod"
  engine                              = "aurora-postgresql"
  engine_version                      = "15.10"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  database_name               = "rtc"
  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

resource "aws_rds_cluster_instance" "rtc_prod_instance_001" {
  cluster_identifier           = aws_rds_cluster.rtc_prod.id
  identifier                   = "rtc-prod-instance-001"
  instance_class               = "db.t3.medium"
  engine                       = aws_rds_cluster.rtc_prod.engine
  engine_version               = aws_rds_cluster.rtc_prod.engine_version
  performance_insights_enabled = true
}

// rtc_prod_2 is RTC Prod, not to be confused with RTC Production
resource "aws_rds_cluster" "rtc_prod_2" {
  cluster_identifier                  = "rtc-prod-2"
  engine                              = "aurora-postgresql"
  engine_version                      = "15.10"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  database_name               = "rtc"
  manage_master_user_password = true
  master_username             = "carbon"
  tags                        = local.tags
}

// rtc_prod_2 is RTC Prod, not to be confused with RTC Production
resource "aws_rds_cluster_instance" "rtc_prod_2_instance_002" {
  cluster_identifier           = aws_rds_cluster.rtc_prod_2.id
  identifier                   = "rtc-prod-2-instance-002"
  instance_class               = "db.r5.large"
  engine                       = aws_rds_cluster.rtc_prod_2.engine
  engine_version               = aws_rds_cluster.rtc_prod_2.engine_version
  performance_insights_enabled = true
}

resource "aws_db_instance" "grafana_db" {
  allocated_storage                   = 20
  max_allocated_storage               = 100
  engine                              = "postgres"
  engine_version                      = "13.20"
  instance_class                      = "db.t3.micro"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  final_snapshot_identifier           = "${local.name}-db-grafana-final"
  backup_window                       = "14:30-15:00"
  backup_retention_period             = 10
  identifier                          = "${local.name}-grafana"
  apply_immediately                   = false

  db_name                     = "grafanadb"
  manage_master_user_password = true
  username                    = "carbon"
  tags                        = local.tags
}

// Carbon Tools Production
resource "aws_rds_cluster" "carbon_tools_prod" {
  cluster_identifier                  = "carbon-tools-prod"
  engine                              = "aurora-postgresql"
  engine_version                      = "17.4"
  allow_major_version_upgrade         = true
  backup_retention_period             = 5
  preferred_backup_window             = "14:30-15:00"
  db_subnet_group_name                = module.vpc.database_subnet_group_name
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  iam_database_authentication_enabled = true
  deletion_protection                 = true
  skip_final_snapshot                 = false
  apply_immediately                   = false

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 1
  }

  master_username             = "carbon"
  manage_master_user_password = true
  tags                        = local.tags
}

resource "aws_rds_cluster_instance" "carbon_tools_prod_instance_001" {
  cluster_identifier           = aws_rds_cluster.carbon_tools_prod.id
  identifier                   = "carbon-tools-instance-001"
  instance_class               = "db.t3.medium"
  engine                       = aws_rds_cluster.carbon_tools_prod.engine
  engine_version               = aws_rds_cluster.carbon_tools_prod.engine_version
  performance_insights_enabled = true
}
