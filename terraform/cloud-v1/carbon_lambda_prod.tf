data "aws_iam_role" "path_planner_prod" {
  name = "RtcPathPlannerProd"
}

resource "aws_lambda_function" "path_planner_prod" {
  function_name = "path-planner-prod"
  role          = data.aws_iam_role.path_planner_prod.arn
  timeout       = 30
  memory_size   = 128
  package_type  = "Image"
  image_uri     = "${aws_ecr_repository.path_planner.repository_url}:latest"

  environment {
    variables = {
      ENVIRONMENT = "production"
    }
  }

  tags = local.tags

  lifecycle {
    ignore_changes = [
      image_uri,
      description
    ]
  }
}

resource "aws_cloudwatch_log_group" "path_planner_prod" {
  name              = "/aws/lambda/${aws_lambda_function.path_planner_prod.function_name}"
  retention_in_days = 90
  tags              = local.tags
}
