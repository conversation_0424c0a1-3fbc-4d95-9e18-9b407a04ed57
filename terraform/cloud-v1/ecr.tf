resource "aws_ecr_repository" "path_planner" {
  name         = "rtc/path-planner-lambda"
  force_delete = true
  image_scanning_configuration {
    scan_on_push = true
  }
  tags = local.tags
}

resource "aws_ecr_repository_policy" "example" {
  repository = aws_ecr_repository.path_planner.name
  policy     = data.aws_iam_policy_document.LambdaECRImageRetrievalPolicy.json
}

data "aws_iam_policy_document" "LambdaECRImageRetrievalPolicy" {
  version = "2012-10-17"
  statement {
    principals {
      identifiers = ["lambda.amazonaws.com"]
      type = "Service"
    }
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer",
    ]
  }
}
