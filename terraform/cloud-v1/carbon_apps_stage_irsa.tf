// ==== STAGING ====
data "aws_iam_policy" "ingest_stage" {
  name = "IngestStageUploadPolicy"
}
module "ingest_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "ingest-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-ingest-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.ingest_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "portal_stage" {
  name = "PortalStageServicePolicy"
}
module "portal_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "portal-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-portal-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.portal_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "robot_syncer_stage" {
  name = "RobotSyncerStageServicePolicy"
}
module "robot_syncer_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "robot-syncer-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-robot-syncer-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.robot_syncer_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "metrics_exporter_stage" {
  name = "StageMetricsExporterServicePolicy"
}
module "metrics_exporter_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "metrics-exporter-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-metrics-exporter-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.metrics_exporter_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "version_metadata_stage" {
  name = "StageVersionMetadataServicePolicy"
}
module "version_metadata_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "version-metadata-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-version-metadata-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.version_metadata_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "infra_mgr_stage" {
  name = "StageInfraMgrServicePolicy"
}
module "infra_mgr_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "infra-mgr-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-infra-mgr-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.infra_mgr_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "carbon_analytics_stage" {
  name = "StageCarbonAnalyticsServicePolicy"
}
module "carbon_analytics_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "carbon-analytics-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-carbon-analytics-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.carbon_analytics_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "veselka_analytics_stage" {
  name = "StageVeselkaAnalyticsServicePolicy"
}

module "veselka_analytics_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "veselka-analytics-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-veselka-analytics-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.veselka_analytics_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "comparison_tool_stage" {
  name = "StageCompareToolPolicy"
}
module "comparison_tool_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "comparison-tool-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-comparison-tool-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.comparison_tool_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "carbon_data_stage" {
  name = "StageCarbonDataPolicy"
}
module "carbon_data_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "carbon-data-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-carbon-data-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.carbon_data_stage.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "veselka_stage" {
  name = "StageVeselkaServicePolicy"
}
module "veselka_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "veselka-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-veselka-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.veselka_stage.arn]

  depends_on = [module.eks]
}

// RTC
data "aws_iam_policy" "web_socket_server_stg" {
  name = "WebSocketServerPolicyStage"
}
module "web_socket_server_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "web-socket-server-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-web-socket-server-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.web_socket_server_stg.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "locator_stg" {
  name = "LocatorPolicyStage"
}
module "locator_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "locator-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-locator-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.locator_stg.arn]

  depends_on = [module.eks]
}

data "aws_iam_policy" "jobs_stg" {
  name = "JobsPolicyStage"
}
module "jobs_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "rtc-jobs-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-rtc-stage-jobs-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.jobs_stg.arn]

  depends_on = [module.eks]
}


// Carbon Panelytics
data "aws_iam_policy" "carbon_panelytics_stage" {
  name = "CarbonPanelyticsServicePolicyStage"
}
module "carbon_panelytics_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "carbon-panelytics-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-carbon-panelytics-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.carbon_analytics_stage.arn]

  depends_on = [module.eks]
}

// Carbon Cloud Image
data "aws_iam_policy" "carbon_cloud_image_stage" {
  name = "CloudImageServiceStage"
}
module "carbon_cloud_image_stage_irsa" {
  count = local.enable_carbon_apps ? 1 : 0

  source                      = "../modules/irsa"
  eks_cluster_id              = module.eks.cluster_name
  eks_oidc_provider_arn       = module.eks.oidc_provider_arn
  kubernetes_namespace        = kubernetes_namespace.stage.metadata[0].name
  kubernetes_service_account  = "carbon-cloud-image-sa"
  create_kubernetes_namespace = false
  irsa_iam_role_name          = "${local.name}-stage-carbon-cloud-image-sa-irsa"
  irsa_iam_policies           = [data.aws_iam_policy.carbon_cloud_image_stage.arn]

  depends_on = [module.eks]
}
