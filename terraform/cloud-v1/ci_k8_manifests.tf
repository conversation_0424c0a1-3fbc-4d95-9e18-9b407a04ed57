#
# Killing the autoscaler for now as it depends on the cluster autoscaler.
# It is important that if we start auto scaling, we need to remove the `replicas` field from the runnerset
#resource "kubernetes_manifest" "robot_ci_horizontal_autoscaler" {
#  count = local.enable_arc_ci ? 1 : 0
#
#  manifest = yamldecode(<<-EOF
#    apiVersion: actions.summerwind.dev/v1alpha1
#    kind: HorizontalRunnerAutoscaler
#    metadata:
#      name: robot-ci-runner-scaler
#      namespace: actions-runner-controller
#    spec:
#      maxReplicas: 5
#      metrics:
#      - scaleDownFactor: "0.5"
#        scaleDownThreshold: "0.25"
#        scaleUpFactor: "1"
#        scaleUpThreshold: "0.75"
#        type: PercentageRunnersBusy
#      minReplicas: 1
#      scaleDownDelaySecondsAfterScaleOut: 600
#      scaleTargetRef:
#        kind: RunnerSet
#        name: robot-ci
#    EOF
#  )
#}

# Until we are autoscaling, runners in the RunnerSet should match the number of desired nodes for the related node group.
# Likely we need to set replicas here and desired nodes manually in aws console as desired nodes in the eks managed group module
# doesn't seem to have any effect.
resource "kubernetes_manifest" "robot_ci_runnerset" {
  count = local.enable_arc_ci ? 1 : 0
  field_manager {
    force_conflicts = true
  }
  manifest = yamldecode(<<-EOF
    apiVersion: actions.summerwind.dev/v1alpha1
    kind: RunnerSet
    metadata:
      labels:
        runner: robot-ci
      name: robot-ci
      namespace: actions-runner-controller
    spec:
      labels:
      - arc-robot-ci
      replicas: 8
      repository: carbonrobotics/robot
      selector:
        matchLabels:
          runner: robot-ci
      serviceName: robot-ci
      template:
        metadata:
          labels:
            runner: robot-ci
        spec:
          affinity:
            podAntiAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                  - key: runner
                    operator: In
                    values:
                    - robot-ci
                topologyKey: kubernetes.io/hostname
          containers:
          - name: runner
            volumeMounts:
            - mountPath: /runner/_work
              name: work
            - mountPath: /cache
              name: cache
          - name: docker
            volumeMounts:
            - mountPath: /var/lib/docker
              name: var-lib-docker
            - mountPath: /cache
              name: cache
          nodeSelector:
            carbon_ci: "true"
            ci_group: robot_0
          securityContext:
            fsGroup: 1000
          volumes:
          - ephemeral:
              volumeClaimTemplate:
                spec:
                  accessModes:
                  - ReadWriteOnce
                  resources:
                    requests:
                      storage: 200Gi
                  storageClassName: ebs-resize-sc
            name: work
      volumeClaimTemplates:
      - metadata:
          name: var-lib-docker
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 1000Gi
          storageClassName: arc-docker-cache
      - metadata:
          name: cache
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 500Gi
          storageClassName: arc-cache
    EOF
  )
}

resource "kubernetes_manifest" "robot_ci_runnerset_arm" {
  count = local.enable_arc_ci_arm ? 1 : 0
  field_manager {
    force_conflicts = true
  }
  manifest = yamldecode(<<-EOF
    apiVersion: actions.summerwind.dev/v1alpha1
    kind: RunnerSet
    metadata:
      labels:
        runner: robot-ci-arm
      name: robot-ci-arm
      namespace: actions-runner-controller
    spec:
      labels:
      - arc-robot-ci-arm
      replicas: 2
      repository: carbonrobotics/robot
      selector:
        matchLabels:
          runner: robot-ci-arm
      serviceName: robot-ci-arm
      template:
        metadata:
          labels:
            runner: robot-ci-arm
        spec:
          affinity:
            podAntiAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                  - key: runner
                    operator: In
                    values:
                    - robot-ci-arm
                topologyKey: kubernetes.io/hostname
          containers:
          - name: runner
            volumeMounts:
            - mountPath: /runner/_work
              name: work
            - mountPath: /cache
              name: cache
          - name: docker
            volumeMounts:
            - mountPath: /var/lib/docker
              name: var-lib-docker
            - mountPath: /cache
              name: cache
          nodeSelector:
            carbon_ci: "true"
            ci_group: robot_0_arm
          securityContext:
            fsGroup: 1000
          volumes:
          - ephemeral:
              volumeClaimTemplate:
                spec:
                  accessModes:
                  - ReadWriteOnce
                  resources:
                    requests:
                      storage: 200Gi
                  storageClassName: ebs-resize-sc
            name: work
      volumeClaimTemplates:
      - metadata:
          name: var-lib-docker
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 1000Gi
          storageClassName: arc-docker-cache
      - metadata:
          name: cache
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 500Gi
          storageClassName: arc-cache
    EOF
  )
}
