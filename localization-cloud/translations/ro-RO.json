{"components": {"AlarmTable": {"export": "{{robots}} Istoric alarme {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Indicatorii spațiali sunt disponibili gratuit în timpul evaluării, dar pot face obiectul modificărilor, eliminărilor sau necesității de a trece la un plan superior în orice moment. Datele trebuie să fie confirmate independent.", "description#description": "", "title": "Indicatori spațiali, versiune beta", "title#description": ""}, "tooltip": "Această funcție este în curs de evaluare și poate face obiectul modificărilor sau eliminării în orice moment.", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Nu s-a reușit încărcarea chat-ului:  {{message}}", "failed#description": ""}, "machineTranslated": "Traducere automată", "machineTranslated#description": "", "machineTranslatedFrom": "Traducere automată din {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Acest mesaj a fost șters.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} va fi șters în mod permanent.", "description#description": "", "descriptionActive": "{{subject}} este activ, deci nu poate fi șters.", "descriptionActive#description": ""}, "title": "<PERSON><PERSON><PERSON><PERSON>(ă)?", "title#description": ""}, "CopyToClipboardButton": {"click": "<PERSON>ț<PERSON> clic pentru a copia", "click#description": "", "copied": "Copiat!", "copied#description": ""}, "CropEditor": {"failed": "Nu s-a putut încărca editorul pentru culturi", "failed#description": "", "viewIn": "Vizualizați în Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Golire", "clear#description": "", "endDate": "Data de sfârșit", "endDate#description": "", "error": "Eroare la selectorul intervalului de date", "error#description": "", "invalid": "<PERSON><PERSON><PERSON>", "invalid#description": "", "last7days": "Ultimele 7 zile", "last7days#description": "", "lastMonth": "Ultima lună", "lastMonth#description": "", "lastWeek": "Ultima săptămână", "lastWeek#description": "", "minusDays": "Cu {{days}} zile în urmă", "minusDays#description": "", "plusDays": "În {{days}} zile", "plusDays#description": "", "startDate": "Data de început", "startDate#description": "", "thisMonth": "Luna aceasta", "thisMonth#description": "", "thisWeek": "Săptămâna aceasta", "thisWeek#description": "", "today": "<PERSON><PERSON><PERSON><PERSON>", "today#description": "", "tomorrow": "M<PERSON><PERSON>", "tomorrow#description": "", "yesterday": "<PERSON><PERSON>", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "Ne pare rău, a intervenit o eroare neprevăzută", "error#description": "", "queryLimitReached": "Setul de date redat este parțial, deoarece volumul de date găsit este prea mare. Contactați asistența", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Ce s-a întâmplat?", "comment#description": "", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "", "submit": "Trimiteți și reîncărcați", "submit#description": ""}, "GdprConsent": {"description": "Vă rugăm să citiți și să acceptați condițiile pentru a continua", "description#description": "", "statement": "Accept <0>Condițiile de utilizare</0> și <1>Politica de confidențialitate</1>", "statement#description": "", "title": "Condiții de utilizare și Politica de confidențialitate", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Specificare client obligatorie", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Activați/dezactivați acest meniu", "help#description": "", "title": "Comenzi rapide de pe tastatură", "title#description": ""}, "LaserTable": {"export": "{{robots}} Lasere {{date}}", "export#description": "", "installedOnly": "Numai cele instalate", "installedOnly#description": "", "warnings": {"duplicate": "Acest robot are mai multe lasere înregistrate în următoarel<PERSON> sloturi: {{slots}}", "duplicate#description": "", "emptySlot": "Acest robot nu are laser înregistrat în următoarele sloturi: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Cod nou", "new#description": ""}, "Loading": {"failed": "Ne pare rău dar Centrul de operații Carbon nu s-a încărcat.", "failed#description": "", "placeholder": "Se încarcă...", "placeholder#description": ""}, "ModelName": {"warning": "Avertisment: model cu fiabilitate scăzută", "warning#description": ""}, "PendingActivationOverlay": {"description": "Vă activăm contul. Veți primi un e-mail la finalizarea acestei operațiuni!", "description#description": "", "errors": {"carbon": {"description": "S-a detectat un e-mail Carbon, dar acesta nu a fost confirmat din cauza conectării cu numele de utilizator/parola. Deconectați-vă și folosiți opțiunea „Conectare cu Google” pentru activarea automată.", "description#description": "", "title": "Cont Carbon neconfirmat", "title#description": ""}}, "hi": "<PERSON><PERSON><PERSON> ziua, {{name}}!", "hi#description": "", "logOut": "V-ați conectat cu contul greșit? <0>Deconectare</0>.", "logOut#description": "", "title": "În așteptarea activării", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON> mult", "more#description": ""}, "RobotImplementationSelector": {"status": "Starea <PERSON>ării", "status#description": "", "title": "Schimbați starea implementării", "title#description": "", "warning": "Schimbarea stării implementării poate declanșa fluxuri de lucru automatizate, care pot afecta experiența clientului. NU EFECTUAȚI ACEASTĂ OPERAȚIUNE DECÂT DACĂ SUNTEȚI SIGUR(Ă)!", "warning#description": ""}, "ShowLabelsButton": {"text": "Etichete", "text#description": "", "tooltip": "Afișează etichetele", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Afișează metainformațiile", "tooltip#description": ""}, "almanac": {"crops": {"new": "Adăugați o nouă cultură", "new#description": "", "none": "Nu există categorii de culturi", "none#description": "", "sync#description": ""}, "cropsSynced": "Toate culturile", "cropsSynced#description": "", "delete": {"description": "Această operațiune este ireversibilă", "description#description": ""}, "discard": {"description": "Renunțați la modificările aduse la {{title}}?", "description#description": "", "title": "Renunțați la modificări?", "title#description": ""}, "fineTuneDescription": "Valoarea implicită este 5, aceasta poate scădea sau crește timpul de tragere a laserului cu cca. 20% per treaptă", "fineTuneDescription#description": "", "fineTuneTitle": "<PERSON>plu pentru regla<PERSON> fine", "fineTuneTitle#description": "", "formulas": {"all": "Toate dimensiunile", "all#description": "", "copyFormula": "Copiați formula", "copyFormula#description": "", "copySize": "Copiați dimensiunile", "copySize#description": "", "exponent": {"description": "Crește raza în mm până la acest exponent", "description#description": "", "label": "Exponent (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Număr de la 1 la 10, valoarea implicită este 5, aceasta poate scădea sau crește timpul de tragere a laserului cu cca. 20% per treaptă Acesta este numărul utilizat în modul de bază", "description#description": "", "label": "Indice reglaje fine (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Multiplu global pentru creșterea/scăderea indicelui pentru reglaje fine", "description#description": "", "label": "Valoarea indicelui reglaje fine (FM)", "label#description": ""}, "laserTime": "Timp laser", "laserTime#description": "", "maxTime": {"description": "Stabiliți limita la intervalul de timp necesar pentru tragere în ms", "description#description": "", "label": "<PERSON><PERSON> maxim", "label#description": ""}, "multiplier": {"description": "Se multiplică cu raza în mm", "description#description": "", "label": "Multiplu (A)", "label#description": ""}, "offset": {"description": "<PERSON><PERSON> <PERSON>, în mi<PERSON><PERSON><PERSON><PERSON>, indiferent de rază", "description#description": "", "label": "<PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "Lipiți formula", "pasteFormula#description": "", "pasteSize": "Lipiți dimensiunile", "pasteSize#description": "", "sync": "Sincronizați toate dimensiunile", "sync#description": "", "thresholds": "Praguri de dimensiune", "thresholds#description": "", "title": "Formule", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Comutați la Modul Avansat", "switchModeAdvanced#description": "", "switchModeBasic": "Comutați la Modul de bază", "switchModeBasic#description": "", "warnings": {"admin": "Modificarea acestui almanah va sincroniza toate unitățile de producție prezente și viitoare.", "admin#description": "", "carbon": "Acesta este un almanah pus la dispoziție de Carbon. Numai indicele pentru reglaje fine poate fi modificat.", "carbon#description": "", "production": "Acest almanah rulează pe un robot. Editarea sa va avea efecte imediate pe teren.", "production#description": ""}, "weeds": {"new": "Adăugați o nouă buruiană", "new#description": "", "none": "Nu există categorii de buruieni", "none#description": "", "sync#description": ""}, "weedsSynced": "Toate buru<PERSON><PERSON>", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} salvat. Activare prin Reglajul rapid din aplicația de operator", "savedLong#description": "", "testResults": "Previzualizare rezultate", "testResults#description": ""}, "filters": {"capturedAt": "Data capturii", "capturedAt#description": "", "diameter": "Diametr<PERSON>", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "Toate imaginile", "allImages#description": "", "categorized": "Împărțit pe categorii", "categorized#description": "", "scrollToTop": "Înapoi sus", "scrollToTop#description": "", "sortBy": {"latest": "Cel mai recent", "latest#description": ""}, "sortedBy": "<PERSON><PERSON><PERSON> dup<PERSON>: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Eroare la preluarea stării", "error#description": "", "ready": "Rezultate gata", "ready#description": "", "session#description": "", "session_few": "<PERSON><PERSON><PERSON>", "session_one": "<PERSON><PERSON><PERSON>", "session_other": "<PERSON><PERSON><PERSON>", "showResults": "Afișează rezultatele", "showResults#description": "", "status": "{{processed}} / {{total}} rezultate prelucrate", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Modificarea acestui profil de fabrică se va sincroniza pe toate unitățile de producție actuale și viitoare.", "admin#description": "", "adminMeta": "Toate profilurile de admin vor fi disponibile tuturor clienților. Nu creați profiluri redundante!", "adminMeta#description": "", "production": "Acest profil de plantă rulează activ pe un robot. Operatorul va fi informat privind actualizările și poate alege să folosească cele mai recente modificări.", "production#description": "", "protected": "Acesta este un profil furnizat de Carbon. Nu poate fi modificat.", "protected#description": "", "unsavedChanges": "Modificări nesalvate. Apăsați pentru a implementa modificările.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_few": "Chei modificate", "changedKey_one": "<PERSON>eie modificat<PERSON>", "changedKey_other": "Chei modificate", "newKey": "denumirea nouă a {{key}}", "newKey#description": "", "stringReqs": "Aceasta poate conține doar litere de la a la z, cifre de la 0 la 9, . și _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Această cheie a fost adăugată pe lângă cea implicită.", "description#description": ""}, "keyMissing": {"description": "<PERSON><PERSON> implicite lips<PERSON>: {{keys}}", "description#description": ""}, "valueChanged": {"description": "Această valoare a fost modificată de la valoarea implicită ({{default}})", "description#description": "", "title": "Configurație modificată", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Nu s-a putut încărca editorul clientului", "load#description": ""}}, "CustomerSelector": {"empty": "Neatribuit", "empty#description": "", "title": "Schimbați clientul", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Trage vs ignoră", "description#description": "", "label": "Trage", "label#description": ""}, "copy": "Copiați", "copy#description": "", "ignorable": {"description": "Trageți numai dacă timpul o permite, nu se ia în calcul la recomandarea privind viteza", "description#description": "", "label": "Poate fi ignorat", "label#description": ""}, "paste": "Lipiți configurațiile", "paste#description": ""}, "warnings": {"production": "Acest profil Deosebire țintă rulează deja pe un robot. Editarea sa va avea efecte imediate pe teren.", "production#description": ""}}, "drawer": {"customerMode": "Mod Client", "customerMode#description": "", "error": "Navigația nu a putut fi încărcată", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max ({{units}})", "max#description": "", "min": "Min ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "Headerul nu a putut fi încărcat", "failed#description": "", "mascot": "Mascotă pui Carbon Robotics", "mascot#description": "", "search": {"failed": "Nu s-a putut încărca căutarea", "failed#description": "", "focus": "<PERSON><PERSON><PERSON><PERSON>", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Mărime", "label#description": "", "larger": "mai mare", "larger#description": "", "smaller": "mai mic", "smaller#description": ""}}, "map": {"bounds": {"reset": "Resetare vizualizare", "reset#description": ""}, "errors": {"empty": "Nu s-au raportat date cu privire la locație", "empty#description": "", "failed": "Nu s-a putut încărca harta", "failed#description": ""}, "filters": {"customer_office": "Biroul clientului", "customer_office#description": "", "hq": "Sediul central Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Căsuță poștală", "po_box#description": "", "shop": "Atelier", "shop#description": "", "storage": "<PERSON><PERSON><PERSON><PERSON>", "storage#description": "", "support_base": "<PERSON><PERSON><PERSON> pentru asistență", "support_base#description": ""}, "fullscreen": "<PERSON><PERSON>ran complet", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Eroare legendă strat", "legend#description": "", "notThinning": "NU RĂREȘTE", "notThinning#description": "", "notWeeding": "NU PLIVEȘTE", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "EROARE HARTĂ CALORICĂ", "unknown#description": ""}, "fields": {"block": "Locație: {{block}}", "block#description": "", "location": "Locație: {{latitude}}, {{longitude}}", "location#description": "", "size": "Dimensiuni: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "<PERSON><PERSON><PERSON>", "name#description": "", "rangeType#description": "", "relative": "Utilizare interval relativ", "relative#description": "", "relativeRange#description": ""}, "map": "Hartă", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "Copiere din care categorie?", "copyFromWhich#description": "", "splitCrops": "Împărțire pe culturi", "splitCrops#description": "", "splitWeeds": "Împărțire pe buruieni", "splitWeeds#description": "", "syncCrops": "Sincronizați toate culturile", "syncCrops#description": "", "syncWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate b<PERSON><PERSON><PERSON>", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Prag de încredere pentru detectare pentru utilizarea unei detecții în cadrul operațiunii Creare brazdă dinamică", "description#description": "", "label": "<PERSON><PERSON> <PERSON><PERSON>", "label#description": ""}, "minDoo": {"description": "Nivel minim detecție/oportunitate", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Prag de încredere pentru detectare pentru utilizarea unei detecții în cadrul operațiunii de rărire", "description#description": "", "label": "<PERSON><PERSON>", "label#description": ""}, "weed": {"description": "Prag de încredere pentru detectare pentru utilizarea unei detecții în cadrul operațiunii de protecție cultură inversată", "description#description": "", "label": "Prag de protecție cultură inversată", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Prag de încredere pentru detectare pentru utilizarea unei detecții pentru protecția culturii", "description#description": "", "label": "Prag de protecție cultură", "label#description": ""}, "weed": {"description": "Prag de încredere pentru detectare pentru a ține cont de o buruiană", "description#description": "", "label": "<PERSON><PERSON>", "label#description": ""}}}, "errors": {"sync": "Setă<PERSON>e pentru acest model nu au fost încă sincronizate cu Laserweeder. Așteptați să se efectueze sincronizarea pentru a vizualiza și actualiza setările.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Împărțire pe dimensiuni", "splitSizesLong#description": "", "splitSizesShort": "Împărț<PERSON>", "splitSizesShort#description": "", "syncSizesLong": "Sincronizare dimensiuni", "syncSizesLong#description": "", "syncSizesShort": "Sincronizare", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Avertisment:{{stopEmphasis}} <PERSON><PERSON> setări includ modificări nesalvate care nu sunt implementate pe robot.", "exportingUnsavedChanges#description": "", "production": "Acest model ruleaz<PERSON> pe un robot. Editarea sa va avea efecte imediate pe teren.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Alarme ne<PERSON>", "unknown#description": ""}, "almanac": {"unknown": "Almanah ne<PERSON>", "unknown#description": "", "withName": "Almanah: {{name}}", "withName#description": ""}, "autofixing": "Eroare Soluționare automată", "autofixing#description": "", "banding": {"disabled": "<PERSON><PERSON><PERSON> braz<PERSON> dezacti<PERSON>", "disabled#description": "", "enabled": "Creare brazdă activată", "enabled#description": "", "none": "<PERSON><PERSON><PERSON><PERSON>", "none#description": "", "static": "(STATIC)", "static#description": "", "withName": "<PERSON><PERSON><PERSON> bra<PERSON>: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Nu s-a putut încărca starea la înregistrare", "failed#description": "", "never": "Nu s-a înregistrat", "never#description": "", "withTime": "Înregistrat la {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} culturi activate ({{pinned}} fixate)", "summary#description": ""}, "delivery": "Liv<PERSON><PERSON>", "delivery#description": "", "disconnected": "Deconectat", "disconnected#description": "", "discriminator": {"unknown": "Deosebire țintă necunoscută", "unknown#description": "", "withName": "Deosebire țintă: {{name}}", "withName#description": ""}, "failed": "Nu s-a putut încărca starea robotului", "failed#description": "", "failedShort": "<PERSON><PERSON><PERSON><PERSON>", "failedShort#description": "", "implementation": "Implementare", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventar", "inventory#description": "", "job": {"none": "Nu există nicio lucrare", "none#description": "", "withName": "Lucrare: {{name}}", "withName#description": ""}, "lasers": "Lasere online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Activ:", "lifetime#description": "", "lifted": "Pregătit (Ridicat)", "lifted#description": "", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON> în curs", "loading#description": "", "location": {"known": "Locație: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Locație necunoscută", "unknown#description": ""}, "manufacturing": "În curs de fabricație", "manufacturing#description": "", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Încărcare model în curs", "modelLoading#description": "", "notArmed": "Neactivate", "notArmed#description": "", "off_season": "În afara se<PERSON>ului", "off_season#description": "", "offline": "Offline pentru {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P necunoscut", "unknown#description": ""}, "poweringDown": "<PERSON><PERSON><PERSON> în curs", "poweringDown#description": "", "poweringUp": "Pornire în curs", "poweringUp#description": "", "pre_manufacturing": "În etapa de prefabricație", "pre_manufacturing#description": "", "stale": "Învechit", "stale#description": "", "staleDescription": "Ultima valoare cunoscută. Robotul este offline.", "staleDescription#description": "", "standby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standby#description": "", "thinning": {"disabled": "<PERSON><PERSON><PERSON><PERSON>", "disabled#description": "", "enabled": "<PERSON><PERSON><PERSON><PERSON> activat<PERSON>", "enabled#description": "", "none": "<PERSON><PERSON><PERSON><PERSON>", "none#description": "", "withName": "<PERSON><PERSON><PERSON><PERSON>: {{name}}", "withName#description": ""}, "today": {"none": "<PERSON>ă<PERSON><PERSON> plivire <PERSON>", "none#description": ""}, "unknown": "Stare necunoscută", "unknown#description": "", "updating": "Instalare actualizare în curs", "updating#description": "", "version": {"values": {"unknown": "Versiune necunoscută", "unknown#description": "", "updateDownloading": "({{version}} se descar<PERSON><PERSON>)", "updateDownloading#description": "", "updateReady": "({{version}} pregătită)", "updateReady#description": ""}}, "weeding": "Se plivește {{crop}}", "weeding#description": "", "weedingDisabled": "<PERSON><PERSON><PERSON><PERSON>", "weedingDisabled#description": "", "weedingThinning": "Se plivește și rărește {{crop}}", "weedingThinning#description": "", "winterized": "<PERSON><PERSON><PERSON><PERSON> pentru iarn<PERSON>", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Exist<PERSON> deja", "exists#description": "", "unknownClass": "Clasă robot necunoscută", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Nu crea o nouă configurație", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "Machetă {{class}}", "templateForClass#description": "", "templateGeneric": "Machetă Robot", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Vă recomandăm să continuați numai dacă există deja o configurație pentru {{serial}} sau dacă o veți crea manual", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "<PERSON><PERSON><PERSON>", "advancedFormulaTitle#description": "", "formulaTitle": "Formulă calcul", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Scădeți automat viteza sugerată cu valoarea introdusă. De exemplu, o valoare de 5% va reduce viteza sugerată de 1 mph la 0,95 mph.", "description#description": "", "label": "<PERSON><PERSON><PERSON> vite<PERSON>", "label#description": ""}, "decreaseSmoothing": {"description": "Personalizați rata cu care scade viteza. Cu cât este mai mare valoarea, cu atât este mai probabil ca tahometrul să înregistreze fluctuații", "description#description": "", "label": "Încetinire progresivă", "label#description": ""}, "increaseSmoothing": {"description": "Personalizați rata cu care crește viteza. Cu cât este mai mare valoarea, cu atât este mai probabil ca tahometrul să înregistreze fluctuații", "description#description": "", "label": "Accelerare progresivă", "label#description": ""}, "maxVelMph": {"description": "Introduceți viteza maximă absolută de deplasare. Recomandările de viteză nu vor depăși această valoare", "description#description": "", "label": "Viteză maximă", "label#description": ""}, "minVelMph": {"description": "Introduceți viteza minimă absolută de deplasare. Recomandările de viteză nu vor fi sub această valoare", "description#description": "", "label": "Viteză minim<PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Valoarea este procentul dorit de buruieni exterminate", "description#description": "", "label": "Rată de exterminare ideală", "label#description": ""}, "primaryRange": {"description": "Creșteți această valoare dacă doriți să atingeți rata de exterminare ideală, indiferent de impactul vitezei", "description#description": "", "label": "Zonă-tampon de culoare verde", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "allRows#description": "", "row1": "Rândul 1", "row1#description": "", "row2": "Rândul 2", "row2#description": "", "row3": "Rândul 3", "row3#description": ""}, "secondaryKillRate": {"description": "Această valoare este cel mai scăzut procent acceptabil de buruieni exterminate", "description#description": "", "label": "Rată de exterminare minimă", "label#description": ""}, "secondaryRange": {"description": "Creșteți această valoare dacă doriți să adăugați o marjă înainte de primirea unei notificări cu privire la viteza redusă", "description#description": "", "label": "Zonă-tampon de culoare galbenă", "label#description": ""}, "sync": "Sincronizați toate rân<PERSON>ril<PERSON>", "sync#description": "", "warnings": {"admin": "Modificarea acestui estimator de viteză va fi sincronizată pe toate unitățile de producție actuale și viitoare.", "admin#description": "", "production": "Acest estimator de viteză rulează pe un robot. Editarea acestuia va avea efecte imediate pe teren.", "production#description": "", "protected": "Acesta este un profil pus la dispoziție de Carbon. Nu se pot opera modificări.", "protected#description": "", "unsavedChanges": "Modificări nesalvate. Apăsați pentru a aplica modificările.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Progresiv", "gradual#description": "", "immediate": "Imediat", "immediate#description": ""}, "visualization": {"targetSpeed": "Viteza optimă", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_few": "alerte", "alarm_one": "alertă", "alarm_other": "alerte", "fields": {"code": "Cod", "code#description": "", "description": "Des<PERSON><PERSON><PERSON>", "description#description": "", "duration": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"ongoing": "ÎN CURS", "ongoing#description": ""}}, "identifier": "Identificator", "identifier#description": "", "impact": {"name": "Impact", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Începere", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_few": "<PERSON><PERSON><PERSON>", "almanac_one": "almanah", "almanac_other": "<PERSON><PERSON><PERSON>", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_few": "<PERSON><PERSON><PERSON><PERSON>", "assignment_one": "alocare", "assignment_other": "<PERSON><PERSON><PERSON><PERSON>", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instrucțiuni", "instructions#description": ""}, "intervention#description": "", "intervention_few": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "Plivire cu laser", "laserWeed#description": "", "unrecognized": "tip necunoscut ({{value}})", "unrecognized#description": ""}, "job_few": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "<PERSON><PERSON><PERSON>", "laserWeedRow#description": ""}, "objective_few": "obiective", "objective_one": "obiectiv", "objective_other": "obiective", "states": {"acknowledged": "confirmat", "acknowledged#description": "", "cancelled": "anulat", "cancelled#description": "", "completed": "finalizat", "completed#description": "", "failed": "<PERSON><PERSON><PERSON><PERSON>", "failed#description": "", "inProgress": "în des<PERSON>", "inProgress#description": "", "new": "nou", "new#description": "", "paused": "oprit", "paused#description": "", "pending": "<PERSON>n <PERSON>", "pending#description": "", "ready": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ready#description": "", "unrecognized": "stare necunoscută ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Operațiune #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_few": "operațiuni", "task_one": "operațiune", "task_other": "operațiuni"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_few": "profiluri de plante", "categoryCollectionProfile_one": "profil de plantă", "categoryCollectionProfile_other": "profiluri de plante", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON><PERSON>", "disregard#description": "", "name": "Categorii", "name#description": "", "requiredBaseCategories": "Trebuie să aveți exact următoare categorii: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Actualizat", "updatedAt#description": ""}, "metadata": {"capturedAt": "<PERSON><PERSON><PERSON>", "capturedAt#description": "", "categoryId": "ID categorie", "categoryId#description": "", "imageId": "ID imagine", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "ID punct", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "rază", "radius#description": "", "updatedAt": "Primit", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_few": "configu<PERSON><PERSON><PERSON>", "config_one": "configurare", "config_other": "configu<PERSON><PERSON><PERSON>", "key#description": "", "key_few": "chei", "key_one": "cheie", "key_other": "chei", "template#description": "", "template_few": "machete de config", "template_one": "machetă de config", "template_other": "machete de config", "value#description": "", "value_few": "valori", "value_one": "valoare", "value_other": "valori"}, "crops": {"categories": {"unknown": "Cultură necunoscută", "unknown#description": ""}, "crop#description": "", "crop_few": "culturi", "crop_one": "cultură", "crop_other": "culturi", "fields": {"confidence": {"fields": {"regionalImages": "Imagini regionale:", "regionalImages#description": "", "totalImages": "Imagini totale:", "totalImages#description": ""}, "name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "<PERSON><PERSON><PERSON><PERSON>", "archived#description": "", "unknown": "<PERSON>vel de încredere necunoscut", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Note", "notes#description": "", "pinned": "Fixat", "pinned#description": "", "recommended": "Recomandat", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_few": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customer_one": "client", "customer_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": {"emails": {"errors": {"formatting": "O adresă de e-mail pe rând", "formatting#description": ""}, "name": "Adrese de e-mail", "name#description": ""}, "featureFlags": {"almanac": {"description": "Activează filele Almanah și Deosebire țintă pentru roboți (robotul trebuie să accepte almanahul și deosebirea țintei)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Activează fila Profil plantă pentru roboți", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Semnalizatoarele pentru funcții activează funcționalitatea beta pentru toți utilizatorii la nivelul unui client", "description#description": "", "jobs": {"description": "Activează Lucrările (robotul trebuie să și accepte lucrări)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Afișează indicatorii de robot în formă nouă", "description#description": "", "name": "Design nou indicatori", "name#description": ""}, "name": "Semnalizatoare pentru funcții", "name#description": "", "off": "INACTIV", "off#description": "", "on": "ACTIV", "on#description": "", "reports": {"description": "Activează fila Rapoarte și funcțiile acesteia", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Afișează datele spațiale, inclusiv hărțile calorice și graficele", "description#description": "", "name": "Date spațiale", "name#description": ""}, "summary": "{{enabled}}/{{total}} Semnalizatoare pentru funcții activate", "summary#description": "", "unvalidatedMetrics": {"description": "Afișează statisticile beta în așteptarea validării pe teren pentru statistici certificate", "description#description": "", "name": "Statistici beta", "name#description": ""}, "velocityEstimator": {"description": "Activează vizualizarea și editarea profilurilor pentru estimatorul vitezei optime (robotul trebuie să și accepte estimatorul vitezei optime)", "description#description": "", "name": "estimator velocitate <PERSON><PERSON>ă", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Efectuat în ziua de", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Dacă este activat, rapoartele sunt realizate săptămânal, cu următoarele setări pentru toți roboții activi", "description#description": "", "name": "Rapoarte săptămânale", "name#description": ""}, "weeklyReportHour": "Efectuat la ora", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Perioada de raportare", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Locul de raportare", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_few": "deosebire ținte", "discriminator_one": "deosebire țintă", "discriminator_other": "deosebire ținte", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_few": "ferme", "farm_one": "fermă", "farm_other": "ferme", "obstacle#description": "", "obstacle_few": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_few": "puncte", "point_one": "punct", "point_other": "puncte", "zone#description": "", "zone_few": "zone", "zone_one": "zonă", "zone_other": "zone"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_few": "definiții teren", "fieldDefinition_one": "definiție teren", "fieldDefinition_other": "definiții teren", "fields": {"boundary": "Limite teren", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Direcție de plantare", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_few": "valori globale", "global_one": "valoare globală", "global_other": "valori globale", "values": {"plantProfileModelId": {"description": "Model de bază utilizat de toate profilurile de clienți și de admin pentru '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "ID model $t(components.categoryCollectionProfile.actions.testResults)", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Dată / Oră", "capturedAt#description": "", "geoJson": "Loc", "geoJson#description": "", "url": "Deschidere imagine", "url#description": ""}, "image#description": "", "image_few": "imagini", "image_one": "imagine", "image_other": "imagini"}, "jobs": {"job#description": "", "job_few": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "job_one": "lucrare", "job_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lasers": {"fields": {"cameraId": "Număr identificare cameră", "cameraId#description": "", "error": {"values": {"false": "Nominal", "false#description": ""}}, "installedAt": "Instalat", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Număr de serie necunoscut", "unknown#description": ""}}, "lifetimeSec": "<PERSON><PERSON><PERSON>", "lifetimeSec#description": "", "powerLevel": "Nivelul puterii", "powerLevel#description": "", "removedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removedAt#description": "", "rowNumber": "Rând", "rowNumber#description": "", "totalFireCount": "<PERSON><PERSON><PERSON><PERSON>", "totalFireCount#description": "", "totalFireTimeMs": "Timpul de tragere", "totalFireTimeMs#description": "", "warranty": {"name": "Garanție", "name#description": "", "values": {"expired": "Expirată", "expired#description": "", "hours": "Ore: {{installed}}/{{total}} ({{percent}} remaining)", "hours#description": "", "hoursUnknown": "Ore: necunoscut", "hoursUnknown#description": "", "months": "Luni: {{installed}}/{{total}} ({{percent}} remaining)", "months#description": "", "monthsUnknown": "Luni: necunoscut", "monthsUnknown#description": "", "unknown": "Garanție necunoscută", "unknown#description": ""}}}, "laser#description": "", "laser_few": "lasere", "laser_one": "laser", "laser_other": "lasere"}, "models": {"model#description": "", "model_few": "modele", "model_one": "model", "model_other": "modele", "none": "Fără model", "none#description": "", "p2p#description": "", "p2p_few": "Modele P2P", "p2p_one": "Model P2P", "p2p_other": "Modele P2P", "unknown": "Model necunoscut", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "Efectuat de către", "authorId#description": "", "createdAt": "Publicat", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_few": "perioade raportare", "run_one": "perioadă raportare", "run_other": "perioade raportare"}, "reports": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatizat", "name#description": "", "values": {"weekly": "Săptămânal", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_few": "rapoarte", "report_one": "raport", "report_other": "rapoarte"}, "robots": {"classes": {"buds#description": "", "buds_few": "Roboți Bud", "buds_one": "Robot Bud", "buds_other": "Roboți Bud", "moduleValidationStations#description": "", "moduleValidationStations_few": "Stații validare modul", "moduleValidationStations_one": "Stație validare modul", "moduleValidationStations_other": "Stații validare modul", "reapersCarbon#description": "", "reapersCarbon_few": "Roboți Reaper", "reapersCarbon_one": "Robot Reaper", "reapersCarbon_other": "Roboți Reaper", "reapersCustomer_few": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_few": "Tractoare", "rtcs_one": "Tractor", "rtcs_other": "Tractoare", "simulators#description": "", "simulators_few": "Roboți Simulator", "simulators_one": "Robot Simulator", "simulators_other": "Roboți Simulator", "slayersCarbon#description": "", "slayersCarbon_few": "Roboți Slayer", "slayersCarbon_one": "Robot Slayer", "slayersCarbon_other": "Roboți Slayer", "slayersCustomer#description": "", "slayersCustomer_few": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_one": "Robot Laserweeder", "slayersCustomer_other": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Clasă necunoscută", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasere offline", "lasersOffline#description": "", "lifetimeArea": "<PERSON><PERSON> prelucrat", "lifetimeArea#description": "", "lifetimeTime": "<PERSON><PERSON>ț<PERSON>", "lifetimeTime#description": "", "localTime": "Ora locală", "localTime#description": "", "reportedAt": "Ultima actualizare", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Versiune software", "softwareVersion#description": "", "supportSlack": "Canal pentru Slack", "supportSlack#description": "", "targetVersion": "Versiune direcționare", "targetVersion#description": ""}, "robot#description": "", "robot_few": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "robot_one": "robot", "robot_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Robot necunoscut", "unknown#description": ""}, "users": {"activated": "Activat", "activated#description": "", "fields": {"email": "E-mail", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Activare", "name#description": "", "values": {"false": "ÎN AȘTEPTARE", "false#description": ""}}}, "operator#description": "", "operator_few": "operatori", "operator_one": "operator", "operator_other": "operatori", "role#description": "", "role_few": "<PERSON><PERSON><PERSON>", "role_one": "Rol", "role_other": "<PERSON><PERSON><PERSON>", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Tehnic)", "carbon_tech#description": "", "farm_manager": "Manager fer<PERSON><PERSON>", "farm_manager#description": "", "operator_advanced": "Operator (Avansat)", "operator_advanced#description": "", "operator_basic": "Operator", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Rol necunoscut", "unknown_role#description": ""}, "staff": "Echipă", "staff#description": "", "user#description": "", "user_few": "utilizatori", "user_one": "utilizator", "user_other": "utilizatori"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_few": "estimatoare velocitate", "velocityEstimator_one": "estimator velocitate", "velocityEstimator_other": "estimatoare velocitate"}, "weeds": {"categories": {"blossom": "Înflorire", "blossom#description": "", "broadleaf": "Frunză lată", "broadleaf#description": "", "fruit": "<PERSON><PERSON><PERSON>", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "Dicotiledonată", "offshoot#description": "", "preblossom": "Pre-înflorire", "preblossom#description": "", "purslane": "Iarbă-grasă", "purslane#description": "", "runner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runner#description": "", "unknown": "Buruiană necunocută", "unknown#description": ""}, "weed#description": "", "weed_few": "b<PERSON><PERSON><PERSON>", "weed_one": "buruiană", "weed_other": "b<PERSON><PERSON><PERSON>"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "add#description": "", "addLong": "Adaugă {{subject}}", "addLong#description": "", "apply": "Implementează", "apply#description": "", "applyLong": "Implementează {{subject}}", "applyLong#description": "", "backLong": "revenire la {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "An<PERSON>e {{subject}}", "cancelLong#description": "", "clear": "Golire", "clear#description": "", "confirm": "Confirmare", "confirm#description": "", "continue": "Con<PERSON><PERSON><PERSON><PERSON>", "continue#description": "", "copy": "<PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON>re {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} creat", "createdLong#description": "", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete#description": "", "deleteLong": "Ștergeți {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} șters", "deletedLong#description": "", "disableLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "disableLong#description": "", "discard": "Resetare", "discard#description": "", "edit": "Editare", "edit#description": "", "editLong": "<PERSON>a<PERSON><PERSON> {{subject}}", "editLong#description": "", "enableLong": "Permite<PERSON>i {{subject}}", "enableLong#description": "", "exit": "Ieșire", "exit#description": "", "exitLong": "<PERSON><PERSON><PERSON><PERSON> din {{subject}}", "exitLong#description": "", "goToLong": "Accesați {{subject}}", "goToLong#description": "", "invite": "Invitație", "invite#description": "", "inviteLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} a fost invitat", "invitedLong#description": "", "leaveUnchanged": "Păstrează nemodificat", "leaveUnchanged#description": "", "new": "Nou", "new#description": "", "newLong": "Nou {{subject}}", "newLong#description": "", "next": "<PERSON>e", "next#description": "", "pause": "Pauză", "pause#description": "", "play": "Pornire", "play#description": "", "previous": "Înapoi", "previous#description": "", "ranLong": "{{subject}} în funcț<PERSON>ne", "ranLong#description": "", "reload": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "reload#description": "", "resetLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "resetLong#description": "", "retry": "Reîncercați", "retry#description": "", "run": "Pornire", "run#description": "", "runLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "runLong#description": "", "save": "Înregistrare", "save#description": "", "saveLong": "Înregistrați {{subject}}", "saveLong#description": "", "saved": "Înregistrat", "saved#description": "", "savedLong": "{{subject}} înregistrat", "savedLong#description": "", "search": "<PERSON><PERSON><PERSON><PERSON>", "search#description": "", "searchLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "searchLong#description": "", "selectAll": "Selectare toate", "selectAll#description": "", "selectLong": "Selectați {{subject}}", "selectLong#description": "", "selectNone": "Selectare niciuna", "selectNone#description": "", "send": "Trimitere", "send#description": "", "showLong": "Prezentați {{subject}}", "showLong#description": "", "submit": "Transmitere", "submit#description": "", "toggle": "Comutare", "toggle#description": "", "toggleLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "toggleLong#description": "", "update": "Actualizare", "update#description": "", "updated": "Actualizat", "updated#description": "", "updatedLong": "{{subject}} actualizat", "updatedLong#description": "", "uploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploaded#description": "", "viewLong": "Vizualizați {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Activ", "active#description": "", "critical": "Critic", "critical#description": "", "default": "Implicit", "default#description": "", "degraded": "Degradat", "degraded#description": "", "dense": "Dens", "dense#description": "", "disabled": "Dezactivat", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Activat", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Eroare", "error#description": "", "estopOff": "Execuție în curs", "estopOff#description": "", "estopOn": "<PERSON><PERSON>", "estopOn#description": "", "fast": "Rapid", "fast#description": "", "few": "<PERSON><PERSON><PERSON><PERSON>", "few#description": "", "good": "<PERSON>e", "good#description": "", "hidden": "<PERSON><PERSON><PERSON>", "hidden#description": "", "high": "<PERSON><PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inactiv", "inactive#description": "", "interlockSafe": "<PERSON><PERSON><PERSON> permis<PERSON>", "interlockSafe#description": "", "interlockUnsafe": "Tragere interzisă", "interlockUnsafe#description": "", "large": "Larg", "large#description": "", "laserKeyOff": "Blocat", "laserKeyOff#description": "", "laserKeyOn": "Pornit", "laserKeyOn#description": "", "liftedOff": "<PERSON>us", "liftedOff#description": "", "liftedOn": "<PERSON><PERSON><PERSON><PERSON>", "liftedOn#description": "", "loading": "Se încarcă", "loading#description": "", "low": "Mic", "low#description": "", "majority": "Majoritate", "majority#description": "", "medium": "Me<PERSON>u", "medium#description": "", "minority": "Minoritate", "minority#description": "", "name": "Nume", "name#description": "", "no": "<PERSON>u", "no#description": "", "none": "Niciuna", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "<PERSON><PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Progres", "progress#description": "", "serial": "<PERSON>um<PERSON>r de se<PERSON>", "serial#description": "", "slow": "<PERSON><PERSON><PERSON>", "slow#description": "", "small": "Mic", "small#description": "", "sparse": "<PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_few": "Tipuri", "type_one": "Tip", "type_other": "Tipuri", "unknown": "Necunoscut", "unknown#description": "", "waterProtectNormal": "Umiditate normală", "waterProtectNormal#description": "", "waterProtectTriggered": "Apă <PERSON>", "waterProtectTriggered#description": "", "yes": "Da", "yes#description": ""}, "form": {"booleanType": "Trebuie să fie un număr boolean", "booleanType#description": "", "copyConfigFrom": "Co<PERSON>z<PERSON> config din...", "copyConfigFrom#description": "", "integerType": "Trebuie să fie un număr întreg", "integerType#description": "", "maxLessThanMin": "Max trebuie să fie mai mare decât min", "maxLessThanMin#description": "", "maxSize": "Nu poate conține mai mult de {{limit}} caractere", "maxSize#description": "", "minGreaterThanMax": "Min trebuie să fie mai mic decât max", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON><PERSON>n jos", "moveDown#description": "", "moveUp": "<PERSON><PERSON><PERSON> în sus", "moveUp#description": "", "noOptions": "<PERSON><PERSON>", "noOptions#description": "", "numberType": "Trebuie să fie un număr", "numberType#description": "", "optional": "(opțional)", "optional#description": "", "required": "Obligator<PERSON>", "required#description": "", "stringType": "Trebuie să fie o serie de caractere", "stringType#description": ""}, "lists": {"+3": "{{b}} și {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} și {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Încarcă mai multe", "loadMore#description": "", "noMoreResults": "Nu mai sunt alte rezultate", "noMoreResults#description": "", "noResults": "Fără rezultate", "noResults#description": ""}, "metrics": {"aggregates": {"max": "<PERSON>.", "max#description": "", "min": "<PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Raza medie a culturii", "avgCropSizeMm#description": "", "avgSpeedMph": "Viteza medie de deplasare", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Interval mediu de tragere", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Interval mediu de tragere (nețintit)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Raza medie a zonei acoperite cu buruieni", "avgWeedSizeMm#description": "", "bandingConfigName": "Configurare creație brazdă", "bandingConfigName#description": "", "bandingEnabled": "<PERSON><PERSON><PERSON>", "bandingEnabled#description": "", "bandingPercentage": "Procentaj bră<PERSON>", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Viteza medie de acoperire", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Densitate cultură", "cropDensitySqFt#description": "", "distanceWeededMeters": "Distanța de plivire", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "<PERSON><PERSON><PERSON>", "keptCrops#description": "", "killedWeeds": "Buruieni distruse", "killedWeeds#description": "", "missedCrops": "Culturi neatinse", "missedCrops#description": "", "missedWeeds": "B<PERSON>ieni neatinse", "missedWeeds#description": "", "notThinning": "Culturi neplivite", "notThinning#description": "", "notWeeding": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Randamentul operatorului", "operatorEffectiveness#description": "", "overallEfficiency": "Randament global", "overallEfficiency#description": "", "skippedCrops": "Culturi ignorate", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON><PERSON><PERSON> ignorate", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Durata-țintă de plivire", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Culturi plivite", "thinnedCrops#description": "", "thinningEfficiency": "Randament plivire", "thinningEfficiency#description": "", "timeEfficiency": "Eficiența operațională", "timeEfficiency#description": "", "totalCrops": "<PERSON><PERSON><PERSON> g<PERSON>", "totalCrops#description": "", "totalWeeds": "Buruieni gă<PERSON>", "totalWeeds#description": "", "totalWeedsInBand": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> (în brazdă)", "totalWeedsInBand#description": "", "uptimeSeconds": "Durată funcțion<PERSON>", "uptimeSeconds#description": "", "validCrops": "<PERSON><PERSON><PERSON> g<PERSON>", "validCrops#description": "", "weedDensitySqFt": "Densitate buruiană", "weedDensitySqFt#description": "", "weedingEfficiency": "Randamentul plivirii", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON><PERSON> plivire", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Tip de buruiană: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Tip de b<PERSON>ă: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Tip de buru<PERSON>ă: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Tip de buru<PERSON>ă: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Calculat înainte de rărire dacă rărirea a fost activată", "avgCropSizeMm#description": "", "bandingConfigName": "Cel mai recent profil de brazde selectat", "bandingConfigName#description": "", "crop": "Cea mai recentă cultură selectată", "crop#description": "", "cropDensitySqFt": "Calculat înainte de rărire dacă rărirea a fost activată", "cropDensitySqFt#description": "", "keptCrops": "Numărul estimat de culturi păstrate după rărire", "keptCrops#description": "", "killedWeeds": "LaserWeeder a identifica obiectul ca buruiană și l-a plivit", "killedWeeds#description": "", "missedCrops": "Cultura a fost selectată pentru rărire, dar nu a fost rărită Motivele cele mai frecvent întâlnite includ: depășirea vitezei, nu se încadrează în parametri sau eroare de sistem.", "missedCrops#description": "", "missedWeeds": "Buruiana a fost identificată, dar a fost omisă. Motivele cele mai frecvent întâlnite includ: depășirea vitezei, nu se încadrează în parametri sau eroare de sistem.", "missedWeeds#description": "", "operatorEffectiveness": "Afișează măsura în care viteza de deplasare reală a fost comparabilă cu viteza-țintă recomandată de Estimatorul de viteză", "operatorEffectiveness#description": "", "overallEfficiency": "(Performanță de plivire + Performanță de rărire) / 2, dacă pliviți și răriți", "overallEfficiency#description": "", "skippedCrops": "Cultura a fost omisă în mod intenționat în timpul răririi. Motivele cele mai frecvent întâlnite includ: dezactivarea prin Reglarea rapidă, în afara brazdei, bandă de irigare în apropiere.", "skippedCrops#description": "", "skippedWeeds": "Buruiana a fost omisă intenționat. Motivele cele mai frecvent întâlnite includ: dezactivarea prin Reglare rapidă sau în afara brazdei.", "skippedWeeds#description": "", "thinningEfficiency": "(Culturi rărite + Culturi păstrate) / Nr. estimativ de culturi găsite × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(<PERSON>p de funcționare activ / Timp stare pornită) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "Timpul în care LaserWeeder a fost pornit. Include timpul în mod standby și/sau în poziție ridicată.", "uptimeSeconds#description": "", "weedDensitySqFt": "<PERSON><PERSON><PERSON><PERSON> estimativ de buruieni găsite (total) / <PERSON><PERSON>", "weedDensitySqFt#description": "", "weedingEfficiency": "(Buruieni u<PERSON> / Buruieni găsite pe brazdă) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Timp activ petrecut de LaserWeeder plivind sau rărind", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Randament viteză", "operatorEffectiveness#description": "", "timeEfficiency": "U<PERSON><PERSON><PERSON> a<PERSON>", "timeEfficiency#description": "", "totalWeeds": "Buruieni găsite (total)", "totalWeedsInBand": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> (în brazdă)", "totalWeedsInBand#description": "", "uptimeSeconds": "Timp <PERSON>n stare pornită", "uptimeSeconds#description": "", "validCrops": "<PERSON><PERSON><PERSON>r estimativ de culturi găsite", "validCrops#description": "", "weedingUptimeSeconds": "Timp activ de funcționare", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Acoperire", "coverage#description": "", "field": "Câmp", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Randament", "performance#description": "", "speed": "Viteză", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Durată de utilizare", "usage#description": ""}, "metric#description": "", "metric_few": "parametri", "metric_one": "parametru", "metric_other": "parametri", "spatial": {"heatmapWarning": "per „bloc” de ~20×20 ft", "heatmapWarning#description": "", "metrics": {"altitude": "Altit<PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "<PERSON><PERSON><PERSON>", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interblocare", "interlock#description": "", "keptCropDensity": "Păstrarea densității culturii", "keptCropDensity#description": "", "laserKey": "Cheie laser", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Randament viteză", "speed#description": "", "speedTargetMinimum": "Viteza medie optimă (minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Viteza medie optimă (rândul 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Viteza medie optimă (rândul 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Viteza medie optimă (rândul 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Viteza medie optimă", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Plivire", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "<PERSON>a", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "<PERSON><PERSON><PERSON><PERSON>", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Plivire", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Selectat", "selected#description": "", "showAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toți/toate {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "watt", "W#description": "", "WLong#description": "", "WLong_few": "<PERSON><PERSON><PERSON>", "WLong_one": "watt", "WLong_other": "<PERSON><PERSON><PERSON>", "ac": "acri", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_few": "acri", "acLong_one": "acru", "acLong_other": "acri", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "zi", "d#description": "", "dLong#description": "", "dLong_few": "zile", "dLong_one": "zi", "dLong_other": "zile", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "feet (picior)", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_few": "picioare", "ftLong_one": "picior", "ftLong_other": "picioare", "h": "h", "h#description": "", "hLong#description": "", "hLong_few": "ore", "hLong_one": "oră", "hLong_other": "ore", "ha": "hectare", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_few": "hectare", "haLong_one": "hectar", "haLong_other": "hectare", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_few": "metri", "mLong_one": "metru", "mLong_other": "metri", "mi": "mile", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_few": "minute", "minLong_one": "minut", "minLong_other": "minute", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "lună", "month#description": "", "monthLong#description": "", "monthLong_few": "luni", "monthLong_one": "lună", "monthLong_other": "luni", "mph": "mile/h", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_few": "secunde", "sLong_one": "secundă", "sLong_other": "secunde", "seconds#description": "", "watts#description": "", "week": "săpt", "week#description": "", "weekLong#description": "", "weekLong_few": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weekLong_one": "săptămână", "weekLong_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yd#description": "", "year": "an", "year#description": "", "yearLong#description": "", "yearLong_few": "ani", "yearLong_one": "an", "yearLong_other": "ani"}}, "views": {"admin": {"alarms": {"allowWarning": "Adăugarea de coduri la lista cu permisiuni va permite alarmelor să trimită semnale prin canalele Slack echipelor de asistență", "allowWarning#description": "", "blockWarning": "Adăugarea de coduri la lista de permisiuni blocate va bloca alarmele care trimit semnale prin canalele Slack echipelor de asistență", "blockWarning#description": "", "lists": "Liste", "lists#description": "", "title": "Lista globală de alarme permise", "title#description": "", "titleAllow": "Lista de alarme permise", "titleAllow#description": "", "titleBlock": "Lista de alarme blocate", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Setează", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> pe <PERSON>, <tt>{row1,row2,row3}</tt> pe Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_few": "operațiuni", "operation_one": "operațiune", "operation_other": "operațiuni", "operationsCount": "Operațiuni ({{count}})", "operationsCount#description": "", "operationsHint": "Selectați un nod din schema de configurare pentru a adăuga o operațiune.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_few": "{{count}} er<PERSON>", "encounteredErrors_one": "{{count}} <PERSON><PERSON><PERSON>", "encounteredErrors_other": "{{count}} er<PERSON>", "noChanges": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON>", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_few": "{{count}} taste actualizate", "updatedKeys_one": "{{count}} tast<PERSON> actualizată", "updatedKeys_other": "{{count}} taste actualizate"}, "outcomes": {"failure": "<PERSON><PERSON><PERSON><PERSON>", "failure#description": "", "partial": "Succes parțial", "partial#description": "", "success": "Succes", "success#description": ""}, "title": "Con<PERSON><PERSON><PERSON><PERSON><PERSON> în bloc", "title#description": ""}, "clearCaches": {"action": "Reîmpros<PERSON><PERSON><PERSON><PERSON>", "action#description": "", "description": "Probleme? Întâi încercați să reîmprospătați memoria cache din Robot Syncer", "description#description": ""}, "warnings": {"global": "Modificarea acestei configurații va afecta valorile implicite și recomandările pentru toate {{class}} actuale și viitoare", "global#description": "", "notSimon": "<PERSON><PERSON> <PERSON><PERSON>, a<PERSON>a că probabil nu ar trebui să modifici asta... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Următoarele modificări nu au fost încă sincronizate în {{serial}}:", "description#description": "", "title": "Taste nesincronizate", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Goli<PERSON>i memoria cache", "action#description": "", "description": "Golește memoriile cache pentru Centrul de operațiuni. Această operațiune **va încetini** anumite interogări pe termen scurt, dar **poate** soluționa probleme legate de date mai vechi", "description#description": "", "details": "Apăsați dacă ați editat manual permisiunile unui utilizator în Auth0 (nu prin Ops Center) sau dacă ați făcut modificări la o aplicație integrată de la o terță parte, cum ar fi Stream sau Slack, care nu sunt reflectate.", "details#description": ""}, "title": "Centrul de operațiuni", "title#description": "", "warnings": {"global": "Opțiunile de pe această pagină vor afecta funcționarea în timp real a Centrului de operațiuni în producție.", "global#description": "", "notPortalAdmin": "<PERSON>u sunte<PERSON><PERSON> sau <PERSON>, probabil că nu ar trebui să editați această opțiune... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Denumirea canalului pentru Slack trebuie să înceapă cu \"#\": de ex., \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Administrare", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Ascundere istoric pivot", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Alocare pe tractoare", "orchestrateView#description": "", "showPivotHistory": "<PERSON><PERSON><PERSON><PERSON> istoric pivot", "showPivotHistory#description": ""}, "fetchFailed": "Încărcarea datelor de localizare a eșuat", "fetchFailed#description": "", "goLive": "actualizare activă", "goLive#description": "", "hideRows": "Ascunde rândurile", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "Alocare eșuată, încercați din nou?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Lucrarea nu va mai putea fi alocată pe tractoare și trebuie creată din nou.", "description#description": ""}, "customer": {"unknown": "Client necunoscut", "unknown#description": "", "withName": "Client: {{name}}", "withName#description": ""}, "farm": {"unknown": "Fermă necunoscută", "unknown#description": "", "withName": "Fermă: {{name}}", "withName#description": ""}, "field": {"unknown": "Câmp necunoscut", "unknown#description": "", "withName": "Câmp: {{name}}", "withName#description": ""}, "jobFinished": "Lucrarea s-a terminat la {{time}}", "jobFinished#description": "", "jobStarted": "Lucrarea a început la {{time}}", "jobStarted#description": "", "openInFarmView": "Deschide în vizualizare fermă", "openInFarmView#description": "", "state": "Stare: {{state}}", "state#description": "", "type": "Tip lucrare: {{type}}", "type#description": ""}, "lastPolled": "Ultima interogare", "lastPolled#description": "", "live": "Activ", "live#description": "", "objectiveFromOtherJob": "Obiectiv de la altă lucrare", "objectiveFromOtherJob#description": "", "rowWidthUnits": "Lățime rând {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "<PERSON><PERSON><PERSON>", "farms#description": "", "tractors": "Tractoare", "tractors#description": ""}, "showRows": "Afișează rândurile", "showRows#description": "", "stalePivots": "S-ar putea ca datele din pivot să fie învechite", "stalePivots#description": "", "suggestedAssignments": "Al<PERSON><PERSON><PERSON> recomandate", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "Ascunde punctele", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "Afișează numai cele selectate", "onlyShowSelected#description": "", "showAllPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate punctele", "showAllPoints#description": "", "showThesePoints": "Afișea<PERSON><PERSON> punctele", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Centru", "center#description": "", "centerPivot": "Pivot central", "centerPivot#description": "", "endpointId": "Id punct final", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "Lungime", "length#description": "", "plantingHeading": "Direcție de plantare", "plantingHeading#description": "", "point": "Punct", "point#description": "", "points": "Puncte", "points#description": "", "width": "Lățime", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "Fermă", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Nefixat", "none#description": "", "rtkFixed": "RTK fixat", "rtkFixed#description": "", "rtkFloat": "RTK în curs", "rtkFloat#description": "", "unknown": "Tip fixare necunoscut", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "Toate punctele", "allPoints#description": "", "boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Centru", "center#description": "", "centerPivot": "Pivot central", "centerPivot#description": "", "endpointId": "Id punct final", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "Lungime", "length#description": "", "plantingHeading": "Direcție de plantare", "plantingHeading#description": "", "point": "Punct", "point#description": "", "points": "Puncte", "points#description": "", "width": "Lățime", "width#description": ""}, "unnamedPoint": "Punct <0>{{pointId}}</0> f<PERSON><PERSON><PERSON> denumire", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "<PERSON><PERSON><PERSON> ferm<PERSON>", "farmBoundary#description": "", "field": "Câmp", "field#description": "", "headland": "Promontoriu", "headland#description": "", "obstacle": "Obstacol", "obstacle#description": "", "privateRoad": "Drum privat", "privateRoad#description": "", "unknown": "Tip zonă necunoscut", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "Trasare <PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "Linia trebuie să fie definită de exact două puncte", "exactlyTwoPoints#description": "", "wrongFieldType": "<PERSON><PERSON><PERSON><PERSON> „{{field}}” trebuie să fie {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geometria trebuie să fie de tip {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON trebuie să fie un obiect", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Niciun robot online", "empty#description": ""}, "title": "Misiune de control", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Afișează istoricul modificărilor", "open#description": "", "title": "Istor<PERSON> mod<PERSON><PERSON>", "title#description": ""}, "errors": {"failed": "Încărcarea arborescenței de configurare a eșuat", "failed#description": ""}, "onlyChanged": "Prezentați numai modificat", "onlyChanged#description": ""}, "errors": {"empty": "Niciun robot atribuit", "empty#description": ""}, "hardware": {"errors": {"old": "Robotul nu raportează numărul de serie al calculatorului (probabil prea vechi)", "old#description": ""}, "fields": {"hostname": "<PERSON>ume gaz<PERSON>", "hostname#description": ""}, "installedVersion": "Versiune instalată:", "installedVersion#description": "", "ready": {"name": "Gata de instalare:", "name#description": "", "values": {"false": "<PERSON><PERSON><PERSON><PERSON><PERSON> în curs", "false#description": "", "installed": "Instalat", "installed#description": "", "true": "Gata!", "true#description": ""}}, "tabs": {"computers": "Calculatoare", "computers#description": "", "versions": "Versiuni", "versions#description": ""}, "targetVersion": "Versiune direcționare:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Istoricul actualizării versiunii disponibil <0>în curând™️</0>", "updateHistory#description": ""}, "history": {"borders": "Limite", "borders#description": "", "errors": {"invalidDate": "Selectare perioadă validă", "invalidDate#description": "", "noJobs": "<PERSON><PERSON> lucrare raportată în intervalul selectat", "noJobs#description": "", "noMetrics": "Niciun parametru raportat", "noMetrics#description": ""}, "moreMetrics": "Vizualizați mai mulți parametri", "moreMetrics#description": "", "navTitle": "Istoric", "navTitle#description": "", "placeholder": "Selectați o lucrare sau o dată pentru a vizualiza informațiile", "placeholder#description": "", "points": "Puncte", "points#description": "", "warnings": {"beta": {"description": "Parametrii în așteptarea validării sunt prezentați în albastru", "description#description": ""}, "ongoing": "Parametrii pentru această dată nu sunt încă finalizați", "ongoing#description": ""}}, "status": "Stare", "status#description": "", "summary": {"banding": {"definition": "Defini<PERSON><PERSON>", "definition#description": "", "dynamic": "Dinamic", "dynamic#description": "", "dynamicDisabled": "(<PERSON><PERSON><PERSON> dinami<PERSON> a brazdelor este dezactivată în configurare)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON><PERSON><PERSON>", "rows#description": "", "static": "Static", "static#description": "", "type": "Tip", "type#description": "", "unknown": "<PERSON><PERSON><PERSON> braz<PERSON> necunoscută", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Versiune", "version#description": ""}, "config": {"changes#description": "", "changes_few": "{{count}} s<PERSON><PERSON><PERSON><PERSON>nac", "changes_one": "{{count}} schimbare Almanac", "changes_other": "{{count}} s<PERSON><PERSON><PERSON><PERSON>nac", "cpt": "Prag definire cultură", "cpt#description": "", "default": "(IMPLICIT: {{value}})", "default#description": "", "wpt": "<PERSON>rag definire b<PERSON>i", "wpt#description": ""}, "encoders": {"backLeft": "Spate stânga", "backLeft#description": "", "backRight": "Spate dreapta", "backRight#description": "", "frontLeft": "Față stânga", "frontLeft#description": "", "frontRight": "Față dreapta", "frontRight#description": "", "title": "<PERSON><PERSON><PERSON> r<PERSON>", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Încărcarea rezumatului robotului a eșuat", "failed#description": "", "lasers": {"disabled#description": "", "disabled_few": "{{count}} lasere dezactivate", "disabled_one": "{{count}} laser dezactivat", "disabled_other": "{{count}} lasere dezactivate", "row": "<PERSON><PERSON><PERSON><PERSON> {{row}}", "row#description": ""}, "machineHealth": "<PERSON><PERSON>", "machineHealth#description": "", "navTitle": "<PERSON><PERSON>", "navTitle#description": "", "safetyRadius": {"driptape": "Bandă de picurare", "driptape#description": "", "title": "Rază de siguranță", "title#description": ""}, "sections": {"management": "Gestionare", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Hartă fragmentată", "chipChart#description": "", "datasetVisualization": "Vizualizare set de date", "datasetVisualization#description": "", "title": "<PERSON><PERSON> de asistență", "title#description": ""}}, "support": {"carbon": "Asistență Carbon", "carbon#description": "", "chatMode": {"legacy": "Chat vechi", "legacy#description": "", "new": "Chat nou", "new#description": ""}, "errors": {"failed": "Încărcarea mesajului a eșuat", "failed#description": "", "old": {"description": "{{serial}} utilizează un software cu versiunea {{version}}. Trebuie să folosească versiunea {{target}} pentru a utiliza chat-ul de asistență.", "description#description": "", "title": "Versiune robot prea veche", "title#description": ""}}, "localTime": "Ora locală: {{time}}", "localTime#description": "", "navTitle": "Asistenț<PERSON>", "navTitle#description": "", "toCarbon": "Mesaj către $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Mesaj către $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} este efectuat offline. Operatorul va primi mesaj numai când robotul va avea conectivitate.", "description#description": "", "title": "Robot offline", "title#description": ""}}}, "toggleable": {"internal": "Intern", "internal#description": ""}, "uploads": {"errors": {"empty": "<PERSON><PERSON>", "empty#description": ""}}}, "title": "Flot<PERSON>", "title#description": "", "views": {"fields": {"name": "<PERSON><PERSON><PERSON> filtru", "name#description": "", "otherRobots": "<PERSON><PERSON><PERSON> r<PERSON> ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Roboți fixați", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_few": "filtre", "fleetView_one": "filtru", "fleetView_other": "filtre", "tableOnly": "Unele coloane sunt disponibile numai în afișarea în formă de tabel", "tableOnly#description": ""}}, "knowledge": {"title": "Baza de cunoștințe", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Încheiat", "closed#description": "", "description": "Stare lucrare", "description#description": "", "open": "<PERSON><PERSON><PERSON>", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Indicatori de câmp estimativi", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Modelul nostru utilizează date experimentale privind culturile; aceste date pot conține inexactități. Lucrăm la îmbunătățirea preciziei.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Indicatori de performanță și indicatori ai echipamentului", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Trageți aici fișierele din memoria USB (sau din alt loc)", "drop#description": "", "file#description": "", "file_few": "fișier<PERSON>", "file_one": "<PERSON><PERSON><PERSON>", "file_other": "fișier<PERSON>", "ingestDescription": "Salariații Carbon trebuie să utilizeze serviciul Ingest", "ingestDescription#description": "", "ingestLink": "Încărcați în Ingest", "ingestLink#description": "", "select": "Selectare fișiere", "select#description": "", "title": "Încă<PERSON><PERSON><PERSON>", "title#description": "", "upload": "Încărcați în Carbon", "upload#description": "", "uploading": "Înc<PERSON><PERSON>re {{subject}} în curs", "uploading#description": ""}, "reports": {"explore": {"graph": "<PERSON><PERSON>", "graph#description": "", "groupBy": "Grupaț<PERSON> du<PERSON>ă", "groupBy#description": "", "title": "Explorați", "title#description": ""}, "scheduled": {"authorCarbonBot": "Robot interactiv Carbon", "authorCarbonBot#description": "", "authorUnknown": "Autor <PERSON>", "authorUnknown#description": "", "automation": {"customerReports": "Rapoarte client", "customerReports#description": "", "errorTitle": "<PERSON>ort automat invalid", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Niciun client selectat", "none#description": ""}}, "reportDay": {"errors": {"none": "<PERSON><PERSON> z<PERSON>", "none#description": ""}, "name": "Ziua de raportare", "name#description": ""}, "reportEmails": {"errors": {"none": "Niciun e-mail atribuit", "none#description": ""}, "name": "E-mailuri client", "name#description": ""}, "reportHour": {"errors": {"none": "<PERSON>cio oră selecționată", "none#description": ""}, "name": "<PERSON><PERSON>", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON><PERSON> definită", "none#description": ""}, "name": "Perioadă de raportare", "name#description": ""}, "reportTimezone": {"errors": {"none": "Niciun fus orar selectat", "none#description": ""}, "name": "<PERSON><PERSON> orar raport", "name#description": ""}, "warningDescription": "Este efectuat în fiecare {{day}} la {{hour}} în {{timezone}} pentru o perioadă de {{lookback}} zile, pentru toți roboții activi ai clientului {{customer}}.", "warningDescription#description": "", "warningTitle": "Acesta este un raport automat.", "warningTitle#description": ""}, "byline": "<PERSON> {{author}}", "byline#description": "", "editor": {"columnsHidden": "Coloane as<PERSON>nse", "columnsHidden#description": "", "columnsVisible": "<PERSON><PERSON><PERSON> vizibile", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_few": "Avertisment: există {{count}} alte rapoarte cu acest nume", "duplicateNames_one": "Atenție: există un alt raport cu acest nume", "duplicateNames_other": "Avertisment: există {{count}} alte rapoarte cu acest nume", "fields": {"automateWeekly": "Automatizat săptămânal", "automateWeekly#description": "", "name": "Nume raport", "name#description": "", "showAverages": "Prezentare medii", "showAverages#description": "", "showTotals": "Prezentare totaluri", "showTotals#description": ""}}, "errors": {"noReport": "Raportul nu există sau nu aveți acces la el", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} va fi șters în mod definitiv.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Nu aveți autorizația de a șterge {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-mailul nu va fi trimis din nou", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Raportul va fi trimis la aceste adrese de e-mail", "publishEmailsHelperNew#description": ""}, "runAgain": "Efectuați din nou", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Selectați una sau mai multe coloane", "noColumns#description": "", "noEndDate": "Selectați data de final", "noEndDate#description": "", "noRobots": "Selectați roboții", "noRobots#description": "", "noStartDate": "Selectați data de început", "noStartDate#description": ""}, "fields": {"average": "Me<PERSON>", "average#description": "", "averageShort": "Me<PERSON>", "averageShort#description": "", "date": "Data", "date#description": "", "group": "Număr de serie/Perioadă", "group#description": "", "groupJob": "Serie/Operațiune", "groupJob#description": "", "mixed": "(Amestecat)", "mixed#description": "", "total": "Total", "total#description": "", "totalShort": "SUMĂ", "totalShort#description": ""}, "unknownReport": "Raport necunoscut", "unknownReport#description": ""}, "title": "Programat", "title#description": "", "toLine": "pentru {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "Toți parametrii", "all#description": "", "select": "Selectare parametri", "select#description": ""}, "robotsLabel": {"all": "Toți roboții", "all#description": "", "none": "Ni<PERSON>un robot", "none#description": "", "select": "Selectare roboți", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> prin <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "nume de utilizator și parolă", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "furnizor necunos<PERSON>", "unknown#description": ""}, "cards": {"account": "<PERSON><PERSON>", "account#description": "", "advanced": "Avansați", "advanced#description": "", "localization": "Localizare", "localization#description": ""}, "delete": {"deleteAccount": "Ștergeți contul", "deleteAccount#description": "", "dialog": {"description": "ATENȚIE: Această acțiune nu poate fi anulată. <PERSON><PERSON><PERSON><PERSON> pierde toate datele.", "description#description": ""}}, "fields": {"language": "Limbă", "language#description": "", "measurement": {"name": "Unitate de măsură", "name#description": "", "values": {"imperial": "Imperial (in, mph, acri, fahrenheit)", "imperial#description": "", "metric": "Metric (mm, km/h, hectare, Celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Deconectare", "logOut#description": "", "title": "<PERSON><PERSON><PERSON>", "title#description": "", "version": "Versiunea Carbon Ops Center {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Utilizatorul nu există sau nu aveți permisiunea să-l vizualizați.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Gestionare utilizator în Auth0", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Rol și autorizații", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Contract<PERSON><PERSON><PERSON>", "contractors#description": ""}}}}