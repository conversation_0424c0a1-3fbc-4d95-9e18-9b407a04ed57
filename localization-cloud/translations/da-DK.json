{"components": {"AlarmTable": {"export": "{{robots}} Alarmhistorik {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Geospatiale data kan til enhver tid ændres, fjernes eller kræve opgradering af abonnementet. Data bør verificeres uafhængigt af hinanden.", "description#description": "", "title": "Geospatiale data i beta", "title#description": ""}, "tooltip": "Denne funktion er under evaluering og kan til enhver tid blive ændret eller fjernet.", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Chat kunne ikke indlæses: {{message}}", "failed#description": ""}, "machineTranslated": "Maskinoversat", "machineTranslated#description": "", "machineTranslatedFrom": "Maskinoversat fra {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "<PERSON>ne besked er blevet slettet", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} vil blive slettet permanent.", "description#description": "", "descriptionActive": "{{subject}} er aktiv, så den kan ikke slettes.", "descriptionActive#description": ""}, "title": "<PERSON>r du sikker?", "title#description": ""}, "CopyToClipboardButton": {"click": "<PERSON><PERSON> for at kopiere", "click#description": "", "copied": "<PERSON><PERSON><PERSON>!", "copied#description": ""}, "CropEditor": {"failed": "Afgrødeeditoren kunne ikke indlæses", "failed#description": "", "viewIn": "Se i Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Slet", "clear#description": "", "endDate": "<PERSON><PERSON><PERSON><PERSON>", "endDate#description": "", "error": "Fejl i valg af datointerval", "error#description": "", "invalid": "<PERSON><PERSON><PERSON><PERSON>", "invalid#description": "", "last7days": "De sidste 7 dage", "last7days#description": "", "lastMonth": "Den sidste måned", "lastMonth#description": "", "lastWeek": "Den sidste uge", "lastWeek#description": "", "minusDays": "{{days}} dage siden", "minusDays#description": "", "plusDays": "om {{days}} dage", "plusDays#description": "", "startDate": "Startdato", "startDate#description": "", "thisMonth": "<PERSON><PERSON>", "thisMonth#description": "", "thisWeek": "<PERSON><PERSON> uge", "thisWeek#description": "", "today": "I dag", "today#description": "", "tomorrow": "Imorgen", "tomorrow#description": "", "yesterday": "<PERSON> går", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON><PERSON>ger, uventet fejl", "error#description": "", "queryLimitReached": "Gengiver delvist datasæt, fordi der blev returneret for mange data. Kontakt support for hjælp", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Hvad skete der?", "comment#description": "", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "", "submit": "Send og genindlæs", "submit#description": ""}, "GdprConsent": {"description": "Gennemse og acceptér for at fortsætte", "description#description": "", "statement": "<PERSON><PERSON> accepterer <0>Brugsbetingelserne</0> og <1>Privatlivspolitikken</1>.", "statement#description": "", "title": "Brugsbetingelser og Privatlivspolitik", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Kunde p<PERSON>krævet", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Vis/skjul denne menu", "help#description": "", "title": "Tastaturgenveje", "title#description": ""}, "LaserTable": {"export": "{{robots}} Lasere {{date}}", "export#description": "", "installedOnly": "Kun installeret", "installedOnly#description": "", "warnings": {"duplicate": "<PERSON><PERSON> robot har flere lasere registreret i følgende tidsrum: {{slots}}", "duplicate#description": "", "emptySlot": "<PERSON><PERSON> robot har ingen laser registreret i følgende tidsrum: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Ny kode", "new#description": ""}, "Loading": {"failed": "<PERSON><PERSON><PERSON>, Carbon Ops Center kunne ikke indlæses.", "failed#description": "", "placeholder": "Indlæser ...", "placeholder#description": ""}, "ModelName": {"warning": "Advarsel: Model med lav pålidelighed", "warning#description": ""}, "PendingActivationOverlay": {"description": "Vi aktiverer din konto. Du får en e-mail, når den er klar!", "description#description": "", "errors": {"carbon": {"description": "Carbon-e-mail registreret, men ikke verificeret på grund af login med brugernavn og adgangskode. Log ud, og brug muligheden \"Log ind med Google\" for at blive aktiveret automatisk.", "description#description": "", "title": "Ikke-verificeret carbon-konto", "title#description": ""}}, "hi": "Hej {{name}}!", "hi#description": "", "logOut": "Har du logget ind med den forkerte konto? <0>Log ud</0>.", "logOut#description": "", "title": "Afventer aktivering", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON>", "more#description": ""}, "RobotImplementationSelector": {"status": "Status for implementering", "status#description": "", "title": "Skift implementeringsstatus", "title#description": "", "warning": "Ændring af implementeringsstatus kan udløse automatiserede workflows, der påvirker kundeoplevelsen. LAD VÆRE MED AT GØRE DET, HVIS DU IKKE ER SIKKER!", "warning#description": ""}, "ShowLabelsButton": {"text": "Labels", "text#description": "", "tooltip": "Vis labels", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Vis metadata", "tooltip#description": ""}, "almanac": {"crops": {"new": "Tilføj ny afgrøde", "new#description": "", "none": "Ingen afgrødekategorier", "none#description": "", "sync#description": ""}, "cropsSynced": "Alle afgrøder", "cropsSynced#description": "", "delete": {"description": "Handlingen kan ikke fortrydes", "description#description": ""}, "discard": {"description": "<PERSON><PERSON><PERSON> p<PERSON> {{title}}?", "description#description": "", "title": "Vil du fortryde ænd<PERSON>erne?", "title#description": ""}, "fineTuneDescription": "Standard er 5, kan reducere eller øge laseroptagelsestiden med ~20 % pr. trin.", "fineTuneDescription#description": "", "fineTuneTitle": "Finjustering af multiplikator", "fineTuneTitle#description": "", "formulas": {"all": "<PERSON><PERSON> st<PERSON><PERSON><PERSON>", "all#description": "", "copyFormula": "<PERSON><PERSON><PERSON><PERSON>", "copyFormula#description": "", "copySize": "<PERSON><PERSON><PERSON><PERSON>", "copySize#description": "", "exponent": {"description": "Øger radius i mm til denne eksponent", "description#description": "", "label": "Eksponent (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Tal fra 1-10, standard er 5, kan reducere eller øge laseroptagelsestiden med ~20 % pr. trin. Dette er det antal, der bruges i basistilstand", "description#description": "", "label": "Finju<PERSON>ingsindeks (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Samlet multiplikator for forøgelse/formindskelse af finjusteringsindekset", "description#description": "", "label": "Finjustering af muliplikatorværdi (FM)", "label#description": ""}, "laserTime": "<PERSON><PERSON><PERSON>", "laserTime#description": "", "maxTime": {"description": "Loft for den tid, der skal skydes i ms", "description#description": "", "label": "Maks. tid", "label#description": ""}, "multiplier": {"description": "Multipliceres med radius i mm", "description#description": "", "label": "Multiplikator (A)", "label#description": ""}, "offset": {"description": "<PERSON><PERSON>, der skal tilføjes uanset radius", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "Indsæt formel", "pasteFormula#description": "", "pasteSize": "Indsæt størrelser", "pasteSize#description": "", "sync": "Synkroniser alle størrelser", "sync#description": "", "thresholds": "Tærskelværdier for størrelse", "thresholds#description": "", "title": "<PERSON><PERSON>", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Skift til avanceret tilstand", "switchModeAdvanced#description": "", "switchModeBasic": "Skift til basistilstand", "switchModeBasic#description": "", "warnings": {"admin": "Ændring af denne almanak vil synkronisere med alle nuværende og fremtidige produktionsenheder.", "admin#description": "", "carbon": "Dette er en almanak leveret af Carbon. Det er kun finindstillingsindekset, der kan ændres.", "carbon#description": "", "production": "Denne almanak kører aktivt på en robot. Hvis du ændrer den, vil det straks træde i kraft i marken.", "production#description": ""}, "weeds": {"new": "Tilføj nyt udkrudt", "new#description": "", "none": "Ingen ukrudtkategorier", "none#description": "", "sync#description": ""}, "weedsSynced": "Al ukrudt", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} gemt. Aktiver via Operator App Quick Tune", "savedLong#description": "", "testResults": "Forhåndsvis resultater", "testResults#description": ""}, "filters": {"capturedAt": "<PERSON><PERSON><PERSON> dato", "capturedAt#description": "", "diameter": "Diameter", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "<PERSON>e billeder", "allImages#description": "", "categorized": "Kategoriseret", "categorized#description": "", "scrollToTop": "Tilbage til top", "scrollToTop#description": "", "sortBy": {"latest": "Seneste", "latest#description": ""}, "sortedBy": "Sorteret efter: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Kunne ikke hente status", "error#description": "", "ready": "Resultater er klar", "ready#description": "", "session#description": "", "session_one": "Session", "session_other": "Sessioner", "showResults": "Vis resultater", "showResults#description": "", "status": "behandlet {{processed}} / {{total}} resultater", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Ændringer i denne planteprofil synkroniseres med alle nuværende og fremtidige produktionsenheder.", "admin#description": "", "adminMeta": "Alle admin-profiler bliver tilg<PERSON><PERSON>ge for alle kunder. Undgå roderi!", "adminMeta#description": "", "production": "<PERSON><PERSON> planteprofil kører aktivt på en robot. Operatøren vil blive underrettet om opdateringer og kan vælge at bruge de seneste ændringer.", "production#description": "", "protected": "Dette er en profil fra carbon. Intet kan ændres.", "protected#description": "", "unsavedChanges": "<PERSON><PERSON><PERSON><PERSON>, som ikke er gemt. Tryk Gem for at aktivere ændringer.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_one": "<PERSON><PERSON><PERSON>", "changedKey_other": "<PERSON><PERSON><PERSON><PERSON>", "newKey": "nyt navn på {{key}}", "newKey#description": "", "stringReqs": "<PERSON>n kun indeholde a-z, 0-9, . og _.", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "<PERSON><PERSON> nøgleværdi er blevet føjet til standardnøgleværdien.", "description#description": ""}, "keyMissing": {"description": "Manglende standard(er): {{keys}}", "description#description": ""}, "valueChanged": {"description": "<PERSON><PERSON> væ<PERSON> er blevet ændret fra sin standardværdi ({{default}})", "description#description": "", "title": "Konfigurationen er ændret", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Kunne ikke indlæse kundeeditoren", "load#description": ""}}, "CustomerSelector": {"empty": "<PERSON><PERSON><PERSON> til<PERSON>", "empty#description": "", "title": "Skift kunde", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Skyd vs. ignorer", "description#description": "", "label": "Skyd", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "ignorable": {"description": "<PERSON><PERSON> k<PERSON>, h<PERSON> tiden till<PERSON> det, det er ikke taget i betragtning i hastighedsanbefaling", "description#description": "", "label": "Kan <PERSON>res", "label#description": ""}, "paste": "<PERSON><PERSON><PERSON><PERSON><PERSON> konfigurationer", "paste#description": ""}, "warnings": {"production": "<PERSON>ne diskriminator kører aktivt på en robot. H<PERSON> du ændrer den, træder den i kraft i marken med det samme.", "production#description": ""}}, "drawer": {"customerMode": "Kundetilstand", "customerMode#description": "", "error": "Navigationen kunne ikke indlæses", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max ({{units}})", "max#description": "", "min": "Min ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "Kunne ikke indlæse overskrift", "failed#description": "", "mascot": "Carbon Robotics kyllingemaskot", "mascot#description": "", "search": {"failed": "Søgningen kunne ikke indlæses", "failed#description": "", "focus": "Fokuseret søgning", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Str.", "label#description": "", "larger": "<PERSON><PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "mindre", "smaller#description": ""}}, "map": {"bounds": {"reset": "Nulstil visning", "reset#description": ""}, "errors": {"empty": "Ingen lokationsdata rapporteret", "empty#description": "", "failed": "Kortet kunne ikke indlæses", "failed#description": ""}, "filters": {"customer_office": "Kundekontor", "customer_office#description": "", "hq": "Carbon HQ", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Postboks", "po_box#description": "", "shop": "Værksted", "shop#description": "", "storage": "Lager", "storage#description": "", "support_base": "Supportbase", "support_base#description": ""}, "fullscreen": "<PERSON><PERSON> skæ<PERSON>", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Fejl i lagforklaring", "legend#description": "", "notThinning": "UDFØRTE IKKE UDTYNDING", "notThinning#description": "", "notWeeding": "FJERNEDE IKKE UKRUDT", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "FEJL I VARMEKORT", "unknown#description": ""}, "fields": {"block": "Lokation: {{block}}", "block#description": "", "location": "Lokation: {{latitude}}, {{longitude}}", "location#description": "", "size": "Størrelse: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Lag", "name#description": "", "rangeType#description": "", "relative": "Brug relativt område", "relative#description": "", "relativeRange#description": ""}, "map": "<PERSON><PERSON>", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "<PERSON><PERSON><PERSON>r fra hvilken kategori?", "copyFromWhich#description": "", "splitCrops": "Opdel afgrøder", "splitCrops#description": "", "splitWeeds": "<PERSON>del ukrudt", "splitWeeds#description": "", "syncCrops": "Synkroniser alle afgrøder", "syncCrops#description": "", "syncWeeds": "Synkroniser alt ukrudt", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Beregning af tillidstærskel for at bruge en detektion til dynamisk benyttelse af baner", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> for benyttelsen af baner", "label#description": ""}, "minDoo": {"description": "Minimumsdetektion over mulighed", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Beregning af tillidstærskel for at bruge en detektion til udtynding", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> for udtynding", "label#description": ""}, "weed": {"description": "Beregning af tillidstærskel for at bruge en detektion af omvendt afgrødebeskyttelse", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> for omvendt afgrødebeskyttelse", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Beregning af tillidstærskel for at bruge en detektion af afgrødebeskyttelse", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> for afgrødebeskyttelse", "label#description": ""}, "weed": {"description": "Beregning af tillidstærskel for at vurdere en ukrudtsplante", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> for lugning", "label#description": ""}}}, "errors": {"sync": "Indstillingerne for denne model er endnu ikke synkroniseret fra LaserWeeder. Vent på synkronisering for at se og opdatere indstillinger.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "<PERSON><PERSON>", "splitSizesLong#description": "", "splitSizesShort": "Opdel", "splitSizesShort#description": "", "syncSizesLong": "Synkroniser størrelser", "syncSizesLong#description": "", "syncSizesShort": "Synkroniser", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}<PERSON><PERSON><PERSON>:{{stopEmphasis}} Disse indstillinger inkluderer ikke-gemte ænd<PERSON>, som ikke afspejles på robotten.", "exportingUnsavedChanges#description": "", "production": "<PERSON><PERSON> model kører aktivt på en robot. H<PERSON> du ændrer den, træder den i kraft i marken med det samme.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Ukendte alarmer", "unknown#description": ""}, "almanac": {"unknown": "Ukendt almanak", "unknown#description": "", "withName": "Almanak: {{name}}", "withName#description": ""}, "autofixing": "Fejl på automatisk udbedring", "autofixing#description": "", "banding": {"disabled": "<PERSON><PERSON><PERSON><PERSON> af baner deaktiveret", "disabled#description": "", "enabled": "Benyttelsen af baner aktiveret", "enabled#description": "", "none": "Ingen benyttelse af baner", "none#description": "", "static": "(STATISK)", "static#description": "", "withName": "Benyttelse af baner: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Kunne ikke indlæse check-in-status", "failed#description": "", "never": "Aldrig checket ind", "never#description": "", "withTime": "Checket ind {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Afgrøder aktiveret ({{pinned}} pinned)", "summary#description": ""}, "delivery": "Under levering", "delivery#description": "", "disconnected": "Frakoblet", "disconnected#description": "", "discriminator": {"unknown": "Ukendt diskriminator", "unknown#description": "", "withName": "Diskriminator: {{name}}", "withName#description": ""}, "failed": "Indlæsning af robotstatus mislykkedes", "failed#description": "", "failedShort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "failedShort#description": "", "implementation": "Implementering", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Lagerbeholdning", "inventory#description": "", "job": {"none": "Intet job", "none#description": "", "withName": "Job: {{name}}", "withName#description": ""}, "lasers": "Lasere online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Levetid:", "lifetime#description": "", "lifted": "Standby (løftet)", "lifted#description": "", "loading": "<PERSON><PERSON><PERSON><PERSON>", "loading#description": "", "location": {"known": "Lokation: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Ukendt lokation", "unknown#description": ""}, "manufacturing": "Under produktion", "manufacturing#description": "", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Model indlæses", "modelLoading#description": "", "notArmed": "<PERSON><PERSON><PERSON> arm<PERSON>t", "notArmed#description": "", "off_season": "<PERSON><PERSON> for sæsonen", "off_season#description": "", "offline": "Offline i {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "Ukendt P2P", "unknown#description": ""}, "poweringDown": "Nedlukning", "poweringDown#description": "", "poweringUp": "Opstart", "poweringUp#description": "", "pre_manufacturing": "Forproduktion", "pre_manufacturing#description": "", "stale": "<PERSON><PERSON><PERSON><PERSON>", "stale#description": "", "staleDescription": "<PERSON>st kendte værdi. Robotten er offline.", "staleDescription#description": "", "standby": "Standby", "standby#description": "", "thinning": {"disabled": "<PERSON><PERSON><PERSON><PERSON> deak<PERSON>", "disabled#description": "", "enabled": "Udtynding aktiveret", "enabled#description": "", "none": "Ingen udtynding", "none#description": "", "withName": "Udtynding: {{name}}", "withName#description": ""}, "today": {"none": "Ingen lugning i dag", "none#description": ""}, "unknown": "Ukendt status", "unknown#description": "", "updating": "Installering af opdatering", "updating#description": "", "version": {"values": {"unknown": "Ukendt version", "unknown#description": "", "updateDownloading": "({{version}} downloader)", "updateDownloading#description": "", "updateReady": "({{version}} er klar)", "updateReady#description": ""}}, "weeding": "Lugning af {{crop}}", "weeding#description": "", "weedingDisabled": "Lugning er deaktiveret", "weedingDisabled#description": "", "weedingThinning": "Lugning og udtyndning af {{crop}}", "weedingThinning#description": "", "winterized": "<PERSON>vin<PERSON><PERSON>", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Findes allerede", "exists#description": "", "unknownClass": "Ukendt robotklasse", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Opret ikke en ny konfiguration", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}} skabelon", "templateForClass#description": "", "templateGeneric": "Robot-skabelon", "templateGeneric#description": "", "warnings": {"ignoreConfig": "<PERSON> bør kun fortsætte, hvis der allerede findes en konfiguration for {{serial}}, eller hvis du plan<PERSON>gger at oprette den manuelt.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "<PERSON><PERSON><PERSON><PERSON> for speedometer", "advancedFormulaTitle#description": "", "formulaTitle": "Formel", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Reducerer automatisk den foreslåede hastighed med den indtastede værdi. For eksempel vil en indtastning på 5 % reducere den foreslåede hastighed på 1 mph til 0,95 mph.", "description#description": "", "label": "Hastighedsforskydning", "label#description": ""}, "decreaseSmoothing": {"description": "Til<PERSON> den hastighed, h<PERSON>med hastigheden falder. <PERSON> højere værdien er, jo mere sandsyn<PERSON>gt er det, at speedometeret har udsving.", "description#description": "", "label": "Udjævning af deceleration", "label#description": ""}, "increaseSmoothing": {"description": "Til<PERSON> den hastighed, hvormed hastigheden øges. <PERSON> højere værdien er, jo mere sandsyn<PERSON>gt er det, at speedometeret har udsving.", "description#description": "", "label": "Udjævning af acceleration", "label#description": ""}, "maxVelMph": {"description": "Indtast den absolut højeste hastighed, du er villig til at køre med. Hastighedsanbefalinger vil ikke være over denne værdi", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "minVelMph": {"description": "Indtast den absolut laveste hastighed, du er villig til at køre med. Hastighedsanbefalinger vil ikke være under denne værdi", "description#description": "", "label": "<PERSON><PERSON><PERSON> hastighed", "label#description": ""}, "primaryKillRate": {"description": "<PERSON>ne værdi er den ønskede procentdel af ukrudt, der bekæmpes.", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "primaryRange": {"description": "<PERSON><PERSON> denne væ<PERSON>, hvis du vil ramme din idéelle bekæmpelsesrate uanset hastigheden.", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON> rækker", "allRows#description": "", "row1": "Række 1", "row1#description": "", "row2": "Række 2", "row2#description": "", "row3": "Række 3", "row3#description": ""}, "secondaryKillRate": {"description": "<PERSON>ne værdi er den laveste acceptable procentdel af ukrudt, der bekæmpes.", "description#description": "", "label": "<PERSON><PERSON>", "label#description": ""}, "secondaryRange": {"description": "<PERSON><PERSON> den<PERSON> væ<PERSON>, hvis du vil have mere spillerum, før du modtager en meddelelse om lav hastighed.", "description#description": "", "label": "Gul buffer", "label#description": ""}, "sync": "Synkroniser alle rækker", "sync#description": "", "warnings": {"admin": "Ændring af denne hastighedsberegner vil synkronisere med alle nuværende og fremtidige produktionsenheder.", "admin#description": "", "production": "<PERSON><PERSON> hastighedsberegner kører aktivt på en robot. Hvis du redigerer i den, træder den straks i kraft i marken.", "production#description": "", "protected": "Dette er en profil, der er oprettet af Carbon. Der er intet, der kan ændres.", "protected#description": "", "unsavedChanges": "<PERSON><PERSON><PERSON><PERSON>, der ikke er gemt. Tryk på Gem for at anvende ændringerne.", "unsavedChanges#description": ""}}, "slider": {"gradual": "<PERSON><PERSON><PERSON>", "gradual#description": "", "immediate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immediate#description": ""}, "visualization": {"targetSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_one": "alarm", "alarm_other": "alarmer", "fields": {"code": "<PERSON><PERSON>", "code#description": "", "description": "Beskrivelse", "description#description": "", "duration": {"name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"ongoing": "IGANGVÆRENDE", "ongoing#description": ""}}, "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identifier#description": "", "impact": {"name": "Påvirkning", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Niveau", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Alarm udløst", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_one": "almanak", "almanac_other": "<PERSON><PERSON><PERSON>", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_one": "opgave", "assignment_other": "opgaver", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instruktioner", "instructions#description": ""}, "intervention#description": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "<PERSON>er uk<PERSON>t", "laserWeed#description": "", "unrecognized": "ukendt type ({{value}})", "unrecognized#description": ""}, "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "<PERSON><PERSON>", "laserWeedRow#description": ""}, "objective_one": "<PERSON><PERSON><PERSON>", "objective_other": "<PERSON><PERSON><PERSON>", "states": {"acknowledged": "k<PERSON>tteret", "acknowledged#description": "", "cancelled": "annulleret", "cancelled#description": "", "completed": "afsluttet", "completed#description": "", "failed": "mislykket", "failed#description": "", "inProgress": "i gang", "inProgress#description": "", "new": "ny", "new#description": "", "paused": "på pause", "paused#description": "", "pending": "a<PERSON><PERSON><PERSON>", "pending#description": "", "ready": "klar", "ready#description": "", "unrecognized": "ukendt status ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Opgave #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_one": "opgave", "task_other": "opgaver"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_one": "planteprofil", "categoryCollectionProfile_other": "planteprofiler", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON>", "disregard#description": "", "name": "<PERSON><PERSON><PERSON>", "name#description": "", "requiredBaseCategories": "<PERSON> skal have præcist disse kategorier: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "<PERSON><PERSON><PERSON>", "updatedAt#description": ""}, "metadata": {"capturedAt": "Regis<PERSON>ret", "capturedAt#description": "", "categoryId": "Kategori ID", "categoryId#description": "", "imageId": "Billed ID", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "Punkt ID", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "radius", "radius#description": "", "updatedAt": "Modtaget", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_one": "konfiguration", "config_other": "konfigurationer", "key#description": "", "key_one": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template#description": "", "template_one": "konfig skabelon", "template_other": "konfig skabeloner", "value#description": "", "value_one": "væ<PERSON>", "value_other": "v<PERSON><PERSON><PERSON>"}, "crops": {"categories": {"unknown": "Ukendt afgrøde", "unknown#description": ""}, "crop#description": "", "crop_one": "afgrøde", "crop_other": "afgr<PERSON><PERSON>", "fields": {"confidence": {"fields": {"regionalImages": "Regionale billeder:", "regionalImages#description": "", "totalImages": "Bill<PERSON>r i alt:", "totalImages#description": ""}, "name": "<PERSON><PERSON>", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Arkiveret", "archived#description": "", "unknown": "Ukendt tillid", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Noter", "notes#description": "", "pinned": "Fastsat", "pinned#description": "", "recommended": "An<PERSON><PERSON><PERSON>", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_one": "kunde", "customer_other": "kunder", "fields": {"emails": {"errors": {"formatting": "En e-mail pr. linje", "formatting#description": ""}, "name": "E-mails", "name#description": ""}, "featureFlags": {"almanac": {"description": "Aktiverer fanerne Almanak og Diskriminator for robotter (robotten skal også understøtte almanak og diskriminator)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Aktiverer fanen Planteprofil til robotter", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Funktionsflag aktiverer betafunktionalitet for alle brugere hos en kunde", "description#description": "", "jobs": {"description": "Aktiverer job (<PERSON>ten skal og<PERSON> understø<PERSON> job)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Viser et nyt visuelt design til robotmålinger", "description#description": "", "name": "Redesign m<PERSON><PERSON>", "name#description": ""}, "name": "Funktionsflag", "name#description": "", "off": "OFF", "off#description": "", "on": "ON", "on#description": "", "reports": {"description": "Aktiverer fanen Rapporter og funktioner i", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Vis geografiske data, herunder varmekort og grafer", "description#description": "", "name": "Geografiske data", "name#description": ""}, "summary": "{{enabled}}/{{total}} funktionsflag aktiveret", "summary#description": "", "unvalidatedMetrics": {"description": "<PERSON>is <PERSON><PERSON>m<PERSON><PERSON>, der afventer feltvalidering i certificerede målinger", "description#description": "", "name": "Beta-m<PERSON><PERSON>", "name#description": ""}, "velocityEstimator": {"description": "<PERSON><PERSON><PERSON> det muligt at se og redigere profiler for målhastighedsberegner (robotten skal også understøtte målhastighedsberegner)", "description#description": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ghedsberegner", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "<PERSON><PERSON> på", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "<PERSON><PERSON>, vil rapporterne oprettes ugentligt med følgende indstillinger for alle aktive robotter", "description#description": "", "name": "Ugentlige rapporter", "name#description": ""}, "weeklyReportHour": "Oprettes kl.", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Tilbageblik", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Oprettes i", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_one": "diskriminator", "discriminator_other": "diskriminatorer", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_one": "<PERSON><PERSON><PERSON>", "farm_other": "gårde", "obstacle#description": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_one": "punkt", "point_other": "punkter", "zone#description": "", "zone_one": "zone", "zone_other": "zoner"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_one": "markdefinition", "fieldDefinition_other": "markdefinitioner", "fields": {"boundary": "Markgrænse", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Planteretning", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_one": "global værdi", "global_other": "globale værdier", "values": {"plantProfileModelId": {"description": "Basismodel, som bruges af alle kunde- og administratorprofiler til'$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) Model ID", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Dato/tidspunkt", "capturedAt#description": "", "geoJson": "Lokation", "geoJson#description": "", "url": "Åbn billede", "url#description": ""}, "image#description": "", "image_one": "<PERSON>e", "image_other": "billeder"}, "jobs": {"job#description": "", "job_one": "opgave", "job_other": "opgaver"}, "lasers": {"fields": {"cameraId": "Kamera-ID", "cameraId#description": "", "error": {"values": {"false": "Nominal", "false#description": ""}}, "installedAt": "Installeret", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Ukendt serienummer", "unknown#description": ""}}, "lifetimeSec": "<PERSON><PERSON>n", "lifetimeSec#description": "", "powerLevel": "Effektniveau", "powerLevel#description": "", "removedAt": "<PERSON><PERSON><PERSON>", "removedAt#description": "", "rowNumber": "<PERSON><PERSON><PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "<PERSON><PERSON>", "totalFireCount#description": "", "totalFireTimeMs": "Affyringstid", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "<PERSON><PERSON><PERSON><PERSON>", "expired#description": "", "hours": "Timer: {{installed}}/{{total}} ({{percent}} tilbage)", "hours#description": "", "hoursUnknown": "Timer: uk<PERSON>t", "hoursUnknown#description": "", "months": "Måneder: {{installed}}/{{total}} ({{percent}} tilbage)", "months#description": "", "monthsUnknown": "Måneder: uk<PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "Ukendt garanti", "unknown#description": ""}}}, "laser#description": "", "laser_one": "laser", "laser_other": "lasere"}, "models": {"model#description": "", "model_one": "model", "model_other": "modeller", "none": "Ingen model", "none#description": "", "p2p#description": "", "p2p_one": "P2P-model", "p2p_other": "P2P-modeller", "unknown": "Ukendt model", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "Oprettet af", "authorId#description": "", "createdAt": "Udgivet", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_one": "rapportkørsel", "run_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reports": {"fields": {"authorId": "<PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatiseret", "name#description": "", "values": {"weekly": "Ugentlig", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_one": "rapport", "report_other": "rapporter"}, "robots": {"classes": {"buds#description": "", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_one": "Modul validering station", "moduleValidationStations_other": "Modul validering stationer", "reapersCarbon#description": "", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_one": "Traktor", "rtcs_other": "Traktorer", "simulators#description": "", "simulators_one": "Simulator", "simulators_other": "Simulatorer", "slayersCarbon#description": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Ukendt klassificering", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasere offline", "lasersOffline#description": "", "lifetimeArea": "Tid laseren har været i brug på området", "lifetimeArea#description": "", "lifetimeTime": "Tid <PERSON>en har været i brug i tiden", "lifetimeTime#description": "", "localTime": "Lokal tid", "localTime#description": "", "reportedAt": "Sidst opdateret", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Softwareversion", "softwareVersion#description": "", "supportSlack": "<PERSON><PERSON><PERSON><PERSON>kanal", "supportSlack#description": "", "targetVersion": "Målversion", "targetVersion#description": ""}, "robot#description": "", "robot_one": "robot", "robot_other": "<PERSON>ter", "unknown": "Ukendt robot", "unknown#description": ""}, "users": {"activated": "Aktiveret", "activated#description": "", "fields": {"email": "E-mail", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktivering", "name#description": "", "values": {"false": "AFVENTER", "false#description": ""}}}, "operator#description": "", "operator_one": "operatør", "operator_other": "operatører", "role#description": "", "role_one": "<PERSON><PERSON>", "role_other": "Roller", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Teknisk)", "carbon_tech#description": "", "farm_manager": "Gårdens admin", "farm_manager#description": "", "operator_advanced": "Operatør (Avanceret)", "operator_advanced#description": "", "operator_basic": "Operatør", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Ukendt rolle", "unknown_role#description": ""}, "staff": "Personale", "staff#description": "", "user#description": "", "user_one": "bruger", "user_other": "brugere"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_one": "has<PERSON><PERSON><PERSON>beregner", "velocityEstimator_other": "hastighedsberegnere"}, "weeds": {"categories": {"blossom": "B<PERSON>mst", "blossom#description": "", "broadleaf": "Bredbladet ukrudt", "broadleaf#description": "", "fruit": "<PERSON><PERSON><PERSON>", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "<PERSON><PERSON><PERSON>", "offshoot#description": "", "preblossom": "<PERSON><PERSON><PERSON>", "preblossom#description": "", "purslane": "Portulak", "purslane#description": "", "runner": "<PERSON><PERSON><PERSON><PERSON>", "runner#description": "", "unknown": "Ukendt ukrudt", "unknown#description": ""}, "weed#description": "", "weed_one": "ukrudt", "weed_other": "ukrudt"}}, "utils": {"actions": {"add": "Tilføj", "add#description": "", "addLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "addLong#description": "", "apply": "<PERSON><PERSON><PERSON>", "apply#description": "", "applyLong": "Anvend {{subject}}", "applyLong#description": "", "backLong": "tilbage til {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "<PERSON><PERSON><PERSON> {{subject}}", "cancelLong#description": "", "clear": "Slet", "clear#description": "", "confirm": "Bekræft", "confirm#description": "", "continue": "Fortsæt", "continue#description": "", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} er oprettet", "createdLong#description": "", "delete": "Slet", "delete#description": "", "deleteLong": "Slet {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} er slettet", "deletedLong#description": "", "disableLong": "Deaktiver {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON><PERSON>", "discard#description": "", "edit": "<PERSON><PERSON>", "edit#description": "", "editLong": "Rediger {{subject}}", "editLong#description": "", "enableLong": "Akt<PERSON><PERSON><PERSON> {{subject}}", "enableLong#description": "", "exit": "A<PERSON>lut", "exit#description": "", "exitLong": "Afslut {{subject}}", "exitLong#description": "", "goToLong": "Gå til {{subject}}", "goToLong#description": "", "invite": "Inviter", "invite#description": "", "inviteLong": "Inviter {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} er inviteret", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON><PERSON>", "leaveUnchanged#description": "", "new": "Ny", "new#description": "", "newLong": "Ny {{subject}}", "newLong#description": "", "next": "<PERSON><PERSON><PERSON>", "next#description": "", "pause": "Pause", "pause#description": "", "play": "Afspil", "play#description": "", "previous": "<PERSON><PERSON><PERSON>", "previous#description": "", "ranLong": "{{subject}} kørte", "ranLong#description": "", "reload": "<PERSON><PERSON><PERSON><PERSON> igen", "reload#description": "", "resetLong": "Nulstil {{subject}}", "resetLong#description": "", "retry": "<PERSON><PERSON><PERSON><PERSON> igen", "retry#description": "", "run": "<PERSON><PERSON><PERSON>", "run#description": "", "runLong": "<PERSON><PERSON><PERSON> {{subject}}", "runLong#description": "", "save": "Gem", "save#description": "", "saveLong": "Gem {{subject}}", "saveLong#description": "", "saved": "Gemt", "saved#description": "", "savedLong": "{{subject}} er gemt", "savedLong#description": "", "search": "<PERSON><PERSON><PERSON>", "search#description": "", "searchLong": "<PERSON><PERSON>g efter {{subject}}", "searchLong#description": "", "selectAll": "<PERSON><PERSON><PERSON><PERSON> alle", "selectAll#description": "", "selectLong": "Vælg {{subject}}", "selectLong#description": "", "selectNone": "<PERSON><PERSON><PERSON><PERSON> ingen", "selectNone#description": "", "send": "Send", "send#description": "", "showLong": "Vis {{subject}}", "showLong#description": "", "submit": "Indsend", "submit#description": "", "toggle": "Slå til/fra", "toggle#description": "", "toggleLong": "Slå {{subject}} til/fra", "toggleLong#description": "", "update": "Opdater", "update#description": "", "updated": "<PERSON><PERSON><PERSON>", "updated#description": "", "updatedLong": "{{subject}} er opdateret", "updatedLong#description": "", "uploaded": "Indlæst", "uploaded#description": "", "viewLong": "Vis {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Aktiv", "active#description": "", "critical": "Kritisk", "critical#description": "", "default": "Standard", "default#description": "", "degraded": "Forringet", "degraded#description": "", "dense": "<PERSON><PERSON><PERSON>", "dense#description": "", "disabled": "Slukket", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "<PERSON><PERSON><PERSON><PERSON>", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "<PERSON><PERSON><PERSON>", "error#description": "", "estopOff": "<PERSON><PERSON><PERSON>", "estopOff#description": "", "estopOn": "Nødbremse aktiveret", "estopOn#description": "", "fast": "<PERSON><PERSON><PERSON>", "fast#description": "", "few": "Få", "few#description": "", "good": "God", "good#description": "", "hidden": "Skjult", "hidden#description": "", "high": "<PERSON><PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inaktiv", "inactive#description": "", "interlockSafe": "Skydning tilladt", "interlockSafe#description": "", "interlockUnsafe": "Skydning forhindret", "interlockUnsafe#description": "", "large": "<PERSON><PERSON>", "large#description": "", "laserKeyOff": "<PERSON><PERSON><PERSON>", "laserKeyOff#description": "", "laserKeyOn": "Aktiveret", "laserKeyOn#description": "", "liftedOff": "Sænket", "liftedOff#description": "", "liftedOn": "<PERSON><PERSON><PERSON>", "liftedOn#description": "", "loading": "<PERSON><PERSON><PERSON><PERSON>", "loading#description": "", "low": "Lav", "low#description": "", "majority": "St<PERSON><PERSON><PERSON><PERSON>", "majority#description": "", "medium": "Medium", "medium#description": "", "minority": "Mindretal", "minority#description": "", "name": "Navn", "name#description": "", "no": "No", "no#description": "", "none": "Ingen", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "<PERSON><PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Status", "progress#description": "", "serial": "Serienummer", "serial#description": "", "slow": "Langsomt", "slow#description": "", "small": "Lille", "small#description": "", "sparse": "Sparsom", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_one": "Type", "type_other": "Typer", "unknown": "Ukendt", "unknown#description": "", "waterProtectNormal": "Normal fugtighed", "waterProtectNormal#description": "", "waterProtectTriggered": "<PERSON>d detekteret", "waterProtectTriggered#description": "", "yes": "<PERSON>a", "yes#description": ""}, "form": {"booleanType": "Skal være en boolsk værdi", "booleanType#description": "", "copyConfigFrom": "Ko<PERSON>r konfig fra...", "copyConfigFrom#description": "", "integerType": "Skal være et heltal", "integerType#description": "", "maxLessThanMin": "Max skal være større end min", "maxLessThanMin#description": "", "maxSize": "<PERSON><PERSON> ikke overstige {{limit}} tegn", "maxSize#description": "", "minGreaterThanMax": "Min skal være mindre end max", "minGreaterThanMax#description": "", "moveDown": "Flyt ned", "moveDown#description": "", "moveUp": "Flyt op", "moveUp#description": "", "noOptions": "Ingen valgmuligheder", "noOptions#description": "", "numberType": "Skal være et tal", "numberType#description": "", "optional": "(valgfrit)", "optional#description": "", "required": "Påkrævet", "required#description": "", "stringType": "Skal være en streng", "stringType#description": ""}, "lists": {"+3": "{{b}} og {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} og {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "<PERSON><PERSON><PERSON><PERSON> flere", "loadMore#description": "", "noMoreResults": "Ikke flere resultater", "noMoreResults#description": "", "noResults": "Ingen resultater", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Ma<PERSON>.", "max#description": "", "min": "<PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Gennemsnitlig afgrøderadius", "avgCropSizeMm#description": "", "avgSpeedMph": "Gennemsnitlig kørehastighed", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Gennemsnitlig skydetid", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Gennemsnitlig skydetid (uden mål)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Gennemsnitlig ukrudtsradius", "avgWeedSizeMm#description": "", "bandingConfigName": "Konfiguration af ben<PERSON><PERSON>sen af baner", "bandingConfigName#description": "", "bandingEnabled": "<PERSON><PERSON><PERSON><PERSON> af baner", "bandingEnabled#description": "", "bandingPercentage": "Procentvis bane indstillet", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Gennemsnitlig dækningsha<PERSON>ighed", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "T<PERSON><PERSON>d af afgrøder", "cropDensitySqFt#description": "", "distanceWeededMeters": "Lugningsafstand", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Gemte afgrøder", "keptCrops#description": "", "killedWeeds": "Ukrudtsbekæmpelse", "killedWeeds#description": "", "missedCrops": "Oversete afgrøder", "missedCrops#description": "", "missedWeeds": "Overset ukrudt", "missedWeeds#description": "", "notThinning": "Ingen udtynding af afgrøder", "notThinning#description": "", "notWeeding": "Ingen lugning af ukrudt", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Operatørens effektivitet", "operatorEffectiveness#description": "", "overallEfficiency": "Overordnet resultat", "overallEfficiency#description": "", "skippedCrops": "Ignorerede afgrøder", "skippedCrops#description": "", "skippedWeeds": "Ignoreret ukrudt", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "<PERSON><PERSON><PERSON> for lugningstid", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Udtyndede afgrøder", "thinnedCrops#description": "", "thinningEfficiency": "Resultat af udtynding", "thinningEfficiency#description": "", "timeEfficiency": "Driftsmæssig effektivitet", "timeEfficiency#description": "", "totalCrops": "Fundne afgrøder", "totalCrops#description": "", "totalWeeds": "Fundet ukrudt", "totalWeeds#description": "", "totalWeedsInBand": "Fundet ukrudt (på banen)", "totalWeedsInBand#description": "", "uptimeSeconds": "Oppet<PERSON>", "uptimeSeconds#description": "", "validCrops": "Fundne afgrøder", "validCrops#description": "", "weedDensitySqFt": "<PERSON><PERSON><PERSON><PERSON> af ukrudt", "weedDensitySqFt#description": "", "weedingEfficiency": "Lugningseffektivitet", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Lu<PERSON>ngst<PERSON>", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Ukrudtstype: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Ukrudtstype: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Ukrudtstype: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Ukrudtstype: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "<PERSON><PERSON> be<PERSON> før u<PERSON>, hvis udtynding var aktiveret", "avgCropSizeMm#description": "", "bandingConfigName": "<PERSON> senest valgte banding-profil", "bandingConfigName#description": "", "crop": "<PERSON> senest valgte afgrøde", "crop#description": "", "cropDensitySqFt": "<PERSON><PERSON> be<PERSON> før u<PERSON>, hvis udtynding var aktiveret", "cropDensitySqFt#description": "", "keptCrops": "Det estimerede antal afgrøder, der bevares efter udtyndingen", "keptCrops#description": "", "killedWeeds": "LaserWeeder identificerede emnet som ukrudt og skød det", "killedWeeds#description": "", "missedCrops": "Afgr<PERSON><PERSON> var markeret til udtynding, men blev ikke ramt. Almindelige årsager inkluderer: for høj hastighed, uden for rækkevidde eller systemfejl.", "missedCrops#description": "", "missedWeeds": "<PERSON>k<PERSON>t blev identificeret, men ikke ramt. Almindelige årsager inkluderer: for høj hastighed, uden for rækkevidde eller systemfejl.", "missedWeeds#description": "", "operatorEffectiveness": "<PERSON><PERSON>, hvor godt den faktiske kørehastighed stemte overens med den målhastighed, der anbefales af Velocity Estimator (hastighedsestimator).", "operatorEffectiveness#description": "", "overallEfficiency": "(<PERSON>ge-ydelse + <PERSON><PERSON><PERSON><PERSON><PERSON>-ydelse) / 2, hvis du både luger og udtynder", "overallEfficiency#description": "", "skippedCrops": "Afgrøden blev bevidst sprunget over under udtyndingen. Almindelige årsager inkluderer: deaktive<PERSON> i <PERSON> Tune, uden for båndet eller nær drypvanding.", "skippedCrops#description": "", "skippedWeeds": "Ukrudtet blev bevidst sprunget over. Almindelige årsager inkluderer: deaktiveret i Quick Tune eller uden for båndet.", "skippedWeeds#description": "", "thinningEfficiency": "(Udtyndede afgrøder + Bevarede afgrøder) / Estimerede fundne afgrøder × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(Aktiv arbejdstid / Tændt tid) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "Det komplette tidsrum, LaserWeeder var tændt. Inkluderer når den er i standbytilstand og/eller løftet.", "uptimeSeconds#description": "", "weedDensitySqFt": "Estimeret ukrudt fundet (i alt) / Dækning", "weedDensitySqFt#description": "", "weedingEfficiency": "(Ukrudt dræbt / Ukrudt fundet i båndet) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Den tid LaserWeeder aktivt har luget eller udtyndet", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Hastighed Effektivitet", "operatorEffectiveness#description": "", "timeEfficiency": "Maskinudnyttelse", "timeEfficiency#description": "", "totalWeeds": "Ukrudt fundet (i alt)", "totalWeedsInBand": "Ukrudt fundet (i bånd)", "totalWeedsInBand#description": "", "uptimeSeconds": "<PERSON><PERSON><PERSON><PERSON> tid", "uptimeSeconds#description": "", "validCrops": "Estimerede fundne afgrøder", "validCrops#description": "", "weedingUptimeSeconds": "Aktiv arbejdstid", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "D<PERSON><PERSON>ning", "coverage#description": "", "field": "<PERSON>", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Effektivitet", "performance#description": "", "speed": "<PERSON><PERSON>ghed", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Brug", "usage#description": ""}, "metric#description": "", "metric_one": "<PERSON><PERSON><PERSON>", "metric_other": "<PERSON><PERSON><PERSON>", "spatial": {"heatmapWarning": "pr. ~20×20ft 'blok'", "heatmapWarning#description": "", "metrics": {"altitude": "<PERSON><PERSON><PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "Nødstop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interlock", "interlock#description": "", "keptCropDensity": "Gemt afgrødetæthed", "keptCropDensity#description": "", "laserKey": "<PERSON><PERSON><PERSON><PERSON>", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Hastighedseffektivitet", "speed#description": "", "speedTargetMinimum": "Gennemsnitl<PERSON> m<PERSON>gh<PERSON> (minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Gennemsnitlig målhastighed (række 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Gennemsnitlig målhastighed (række 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Gennemsnitlig målhastighed (række 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Gennemsnitlig må<PERSON>has<PERSON>ghed", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "<PERSON><PERSON><PERSON><PERSON>", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Tidpunkt", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Vandbeskyttelse", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Lugning", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "<PERSON><PERSON>", "selected#description": "", "showAll": "Vis alle {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_one": "watt", "WLong_other": "watt", "ac": "m2", "ac#description": "", "ac/h": "ac/t", "ac/h#description": "", "acLong#description": "", "acLong_one": "acre", "acLong_other": "acres", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_one": "dag", "dLong_other": "dage", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "ft", "ft#description": "", "ft/s": "ft/sek.", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_one": "fod", "ftLong_other": "<PERSON><PERSON><PERSON>", "h": "h", "h#description": "", "hLong#description": "", "hLong_one": "time", "hLong_other": "timer", "ha": "ha", "ha#description": "", "ha/h": "ha/t", "ha/h#description": "", "haLong#description": "", "haLong_one": "<PERSON>ktar", "haLong_other": "<PERSON>ktar", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/sek.", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_one": "meter", "mLong_other": "meter", "mi": "miles", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_one": "minut", "minLong_other": "minutter", "minutes#description": "", "mm": "mm.", "mm#description": "", "month": "md", "month#description": "", "monthLong#description": "", "monthLong_one": "må<PERSON>", "monthLong_other": "<PERSON><PERSON><PERSON><PERSON>", "mph": "mph", "mph#description": "", "ms": "ms.", "ms#description": "", "s": "sek.", "s#description": "", "sLong#description": "", "sLong_one": "sekund", "sLong_other": "<PERSON><PERSON>nder", "seconds#description": "", "watts#description": "", "week": "w", "week#description": "", "weekLong#description": "", "weekLong_one": "uge", "weekLong_other": "uger", "yd#description": "", "year": "<PERSON>r", "year#description": "", "yearLong#description": "", "yearLong_one": "<PERSON>r", "yearLong_other": "<PERSON>r"}}, "views": {"admin": {"alarms": {"allowWarning": "Tilføjelse af koder til tilladelseslisten vil tillade alarmer at pinge supportens Slack-kanaler", "allowWarning#description": "", "blockWarning": "Tilføjelse af koder til blokeringslisten vil forhindre alarmer i at pinge supportens Slack-kanaler", "blockWarning#description": "", "lists": "Lister", "lists#description": "", "title": "Global tilladelsesliste over alarmer", "title#description": "", "titleAllow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> over alarmer", "titleAllow#description": "", "titleBlock": "Blokeringsliste over alarmer", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Ang<PERSON>", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> på <PERSON>, <tt>{row1,row2,row3}</tt> på <PERSON>", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_one": "handling", "operation_other": "handlinger", "operationsCount": "<PERSON>linger ({{count}})", "operationsCount#description": "", "operationsHint": "Vælg et segment i konfigurationsskemaet for at tilføje en handling.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_one": "fandt {{count}} fejl", "encounteredErrors_other": "fandt {{count}} fejl", "noChanges": "ingen ænd<PERSON>er", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_one": "opdateret {{count}} indstilling", "updatedKeys_other": "opdateret {{count}} indstillinger"}, "outcomes": {"failure": "<PERSON><PERSON><PERSON> g<PERSON>", "failure#description": "", "partial": "<PERSON><PERSON>", "partial#description": "", "success": "Gennemført", "success#description": ""}, "title": "Masseændringer konfig", "title#description": ""}, "clearCaches": {"action": "Opdater cache", "action#description": "", "description": "Problemer? <PERSON><PERSON><PERSON><PERSON> først at opdatere Robot Syncers cache.", "description#description": ""}, "warnings": {"global": "Ændring af denne konfiguration vil påvirke standardindstillingerne og anbefalingerne for alle nuværende og fremtidige {{class}}", "global#description": "", "notSimon": "Du er ik<PERSON>, så du bør nok ikke redigere i det her ... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Følgende ændringer er endnu ikke synkroniseret til {{serial}}:", "description#description": "", "title": "Indstillinger som ikke er synkroniseret", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Ryd caches", "action#description": "", "description": "R<PERSON><PERSON> de interne caches for Ops Center. Dette **vil** sænke nogle forespørgsler på kort sigt, men **kan** løse problemer med fastsiddende forrældede data", "description#description": "", "details": "<PERSON>k på denne knap, hvis du har redigeret en brugers tilladelser manuelt i Auth0 (ikke via Ops Center) eller foretaget ændringer i en tredjepartsintegration, som Stream eller Slack, der ikke afspejles.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Valgmuligheder på denne side vil påvirke livedriften af Ops Center i produktionen.", "global#description": "", "notPortalAdmin": "Du er ikke <PERSON>, så du burde nok ikke redigere dette... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Under<PERSON><PERSON><PERSON>-kanal bør starte med \"#\": e.g., \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Gem pivotets historik", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Tildel traktorer", "orchestrateView#description": "", "showPivotHistory": "Vis pivotets historik", "showPivotHistory#description": ""}, "fetchFailed": "Kunne ikke indlæse lokationsdata", "fetchFailed#description": "", "goLive": "liveopdatering", "goLive#description": "", "hideRows": "<PERSON><PERSON>", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "Kunne ikke hente op<PERSON>, prøve igen?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Jobbet vil ikke længere kunne tildeles traktorer og skal genskabes.", "description#description": ""}, "customer": {"unknown": "Ukendt kunde", "unknown#description": "", "withName": "Kunde: {{name}}", "withName#description": ""}, "farm": {"unknown": "Ukendt gård", "unknown#description": "", "withName": "Gård: {{name}}", "withName#description": ""}, "field": {"unknown": "Ukendt mark", "unknown#description": "", "withName": "Mark: {{name}}", "withName#description": ""}, "jobFinished": "Job afsluttet kl. {{time}}", "jobFinished#description": "", "jobStarted": "Job startet kl. {{time}}", "jobStarted#description": "", "openInFarmView": "<PERSON><PERSON> gå<PERSON>, der kigges på", "openInFarmView#description": "", "state": "Status: {{state}}", "state#description": "", "type": "Jobtype: {{type}}", "type#description": ""}, "lastPolled": "Sen<PERSON> aflæst", "lastPolled#description": "", "live": "Live", "live#description": "", "objectiveFromOtherJob": "M", "objectiveFromOtherJob#description": "", "rowWidthUnits": "Rækkebredde {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "G<PERSON><PERSON>", "farms#description": "", "tractors": "Traktorer", "tractors#description": ""}, "showRows": "<PERSON><PERSON>", "showRows#description": "", "stalePivots": "Pivotets oplysninger kan være forældede", "stalePivots#description": "", "suggestedAssignments": "Anbefalede opgaver", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "Gem disse punkter", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "Vis kun valgte", "onlyShowSelected#description": "", "showAllPoints": "Vis alle punkter", "showAllPoints#description": "", "showThesePoints": "Vis disse punkter", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Grænse", "boundary#description": "", "center": "Center", "center#description": "", "centerPivot": "Center pivot", "centerPivot#description": "", "endpointId": "Slutpunkt ID", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Planteretning", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "Bredde", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "<PERSON><PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Intet fikspunkt", "none#description": "", "rtkFixed": "RTF fikspunkt", "rtkFixed#description": "", "rtkFloat": "RT<PERSON> flyder", "rtkFloat#description": "", "unknown": "ukendt type fikspunkt", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "Alle punkter", "allPoints#description": "", "boundary": "Grænse", "boundary#description": "", "center": "Center", "center#description": "", "centerPivot": "Center pivot", "centerPivot#description": "", "endpointId": "Slutpunkt Id", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Planteretning", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "Bredde", "width#description": ""}, "unnamedPoint": "Punkt uden navn <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "<PERSON><PERSON><PERSON><PERSON> græ<PERSON>r", "farmBoundary#description": "", "field": "<PERSON>", "field#description": "", "headland": "Brakmark", "headland#description": "", "obstacle": "Forhindring", "obstacle#description": "", "privateRoad": "Privat vej", "privateRoad#description": "", "unknown": "Ukendt type zone", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "Tegn", "draw#description": ""}, "errors": {"exactlyTwoPoints": "<PERSON><PERSON><PERSON> skal have præcis to punkter", "exactlyTwoPoints#description": "", "wrongFieldType": "Feltet \"{{field}}\" skal være {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geometri skal være af typen {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON skal være et objekt", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Ingen robotter online", "empty#description": ""}, "title": "Missionskontrol", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Se historik over æ<PERSON><PERSON><PERSON>", "open#description": "", "title": "Historik over æ<PERSON><PERSON><PERSON>", "title#description": ""}, "errors": {"failed": "Konfigurationstræet kunne ikke indlæses", "failed#description": ""}, "onlyChanged": "<PERSON>is kun ændringer", "onlyChanged#description": ""}, "errors": {"empty": "<PERSON>gen robotter er tildelt", "empty#description": ""}, "hardware": {"errors": {"old": "Robotten rapporterer ikke computerserienummer (sandsynligvis for gammel)", "old#description": ""}, "fields": {"hostname": "Værtsnavn", "hostname#description": ""}, "installedVersion": "Installet version:", "installedVersion#description": "", "ready": {"name": "Klar til installation:", "name#description": "", "values": {"false": "Downloader ...", "false#description": "", "installed": "Installeret", "installed#description": "", "true": "Klar!", "true#description": ""}}, "tabs": {"computers": "<PERSON>e", "computers#description": "", "versions": "Versioner", "versions#description": ""}, "targetVersion": "Målversion:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Historik over versionsopdatering <0>coming soon™️</0>", "updateHistory#description": ""}, "history": {"borders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "borders#description": "", "errors": {"invalidDate": "<PERSON><PERSON><PERSON><PERSON> et gyldigt datointerval", "invalidDate#description": "", "noJobs": "Ingen job rapporteret i det valgte område", "noJobs#description": "", "noMetrics": "Ingen målinger rapporteret", "noMetrics#description": ""}, "moreMetrics": "Se flere målinger", "moreMetrics#description": "", "navTitle": "Historik", "navTitle#description": "", "placeholder": "<PERSON><PERSON><PERSON><PERSON> et job eller en dato for at se dato", "placeholder#description": "", "points": "Point", "points#description": "", "warnings": {"beta": {"description": "<PERSON><PERSON><PERSON>, der afventer validering, vises med blåt", "description#description": ""}, "ongoing": "<PERSON><PERSON><PERSON><PERSON> for denne dato er ikke endelige endnu", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "Definition", "definition#description": "", "dynamic": "Dynamisk", "dynamic#description": "", "dynamicDisabled": "(Dynamisk benyttelse af baner er deaktiveret i konfigurationen)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON><PERSON>", "rows#description": "", "static": "Statisk", "static#description": "", "type": "Type", "type#description": "", "unknown": "Ukendt benyttelse af baner", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Version", "version#description": ""}, "config": {"changes#description": "", "changes_one": "{{count}} <PERSON>nakændring", "changes_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cpt": "<PERSON><PERSON><PERSON><PERSON> for afgrødepoint", "cpt#description": "", "default": "(STANDARD: {{value}})", "default#description": "", "wpt": "<PERSON><PERSON><PERSON><PERSON> for ukrudtspoint", "wpt#description": ""}, "encoders": {"backLeft": "Bagerste venstre", "backLeft#description": "", "backRight": "Bagerste højre", "backRight#description": "", "frontLeft": "<PERSON><PERSON> venstre", "frontLeft#description": "", "frontRight": "<PERSON><PERSON>ø<PERSON>", "frontRight#description": "", "title": "Enkodere til hjul", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Robotoversigten kunne ikke indlæses", "failed#description": "", "lasers": {"disabled#description": "", "disabled_one": "{{count}} deaktiveret laser", "disabled_other": "{{count}} deak<PERSON><PERSON>e lasere", "row": "<PERSON><PERSON><PERSON><PERSON> {{row}}", "row#description": ""}, "machineHealth": "<PERSON><PERSON><PERSON> sundhed", "machineHealth#description": "", "navTitle": "Oversigt", "navTitle#description": "", "safetyRadius": {"driptape": "Dryptape", "driptape#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "sections": {"management": "Administration", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Opdelt kort", "chipChart#description": "", "datasetVisualization": "Datasetvisualisering", "datasetVisualization#description": "", "title": "Supportlinks", "title#description": ""}}, "support": {"carbon": "Carbon support", "carbon#description": "", "chatMode": {"legacy": "<PERSON><PERSON><PERSON> chat", "legacy#description": "", "new": "Ny chat", "new#description": ""}, "errors": {"failed": "Meddelelsen kunne ikke indlæses", "failed#description": "", "old": {"description": "{{serial}} kører med softwareversion {{version}}. Den skal køre med {{target}} for at kunne bruge supportchatten.", "description#description": "", "title": "Utilstrækkelig robotversion", "title#description": ""}}, "localTime": "Lokal tid: {{time}}", "localTime#description": "", "navTitle": "Support", "navTitle#description": "", "toCarbon": "Besked til $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Besked til $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} kører offline. Operatøren vil ikke modtage beskeden, før robotten har forbindelse.", "description#description": "", "title": "Robotten er offline", "title#description": ""}}}, "toggleable": {"internal": "Intern", "internal#description": ""}, "uploads": {"errors": {"empty": "Ingen indlæsninger", "empty#description": ""}}}, "title": "Flåde", "title#description": "", "views": {"fields": {"name": "Navn på filter", "name#description": "", "otherRobots": "<PERSON> ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Fastsatte robotter", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_one": "filter", "fleetView_other": "ffiltre", "tableOnly": "Nogle kolonner er kun tilgængelige i tabelvisning", "tableOnly#description": ""}}, "knowledge": {"title": "Vidensbank", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Lukket", "closed#description": "", "description": "<PERSON><PERSON><PERSON>", "description#description": "", "open": "<PERSON><PERSON>", "open#description": ""}, "sections": {"estimatedFieldMetrics": "<PERSON><PERSON><PERSON><PERSON><PERSON> mark-m<PERSON><PERSON>", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Vores model bruger eksperimentelle afgrødedata, der kan rumme unøjagtigheder. Vi forbedrer pålideligheden løbende.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "M<PERSON>ling af ydeevne og maskine", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "<PERSON><PERSON><PERSON><PERSON> filer her<PERSON> fra <PERSON> (eller andre steder)", "drop#description": "", "file#description": "", "file_one": "fil", "file_other": "filer", "ingestDescription": "Carbon-medarbejdere bør bruge Ingest-tjenesten", "ingestDescription#description": "", "ingestLink": "Upload til Ingest", "ingestLink#description": "", "select": "<PERSON><PERSON><PERSON><PERSON> filer", "select#description": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "title#description": "", "upload": "Upload til Carbon", "upload#description": "", "uploading": "Indl<PERSON>ser {{subject}} ...", "uploading#description": ""}, "reports": {"explore": {"graph": "<PERSON>", "graph#description": "", "groupBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "groupBy#description": "", "title": "Udforsk", "title#description": ""}, "scheduled": {"authorCarbonBot": "Carbon Bot", "authorCarbonBot#description": "", "authorUnknown": "Ukendt forfatter", "authorUnknown#description": "", "automation": {"customerReports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerReports#description": "", "errorTitle": "Ugyldig automatiseret rapport", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Ingen kunde valgt", "none#description": ""}}, "reportDay": {"errors": {"none": "Ingen dag valgt", "none#description": ""}, "name": "Rapporteringsdag", "name#description": ""}, "reportEmails": {"errors": {"none": "Ingen e-mails tildelt", "none#description": ""}, "name": "Kunde-e-mails", "name#description": ""}, "reportHour": {"errors": {"none": "Ingen timer valgt", "none#description": ""}, "name": "Rapporteringstidspunkt", "name#description": ""}, "reportLookback": {"errors": {"none": "Der er ikke defineret noget tilbageblik", "none#description": ""}, "name": "Tilbagebliksrapport", "name#description": ""}, "reportTimezone": {"errors": {"none": "Der er ikke valgt nogen tidszone", "none#description": ""}, "name": "Tidsperiode for rapport", "name#description": ""}, "warningDescription": "<PERSON>ø<PERSON> hver {{day}} kl. {{hour}} i {{timezone}} med {{lookback}} dages tilbageblik for alle aktive {{customer}} robotter.", "warningDescription#description": "", "warningTitle": "Det er en automatiseret rapport!", "warningTitle#description": ""}, "byline": "Af {{author}}", "byline#description": "", "editor": {"columnsHidden": "Skjulte kolonner", "columnsHidden#description": "", "columnsVisible": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_one": "Advarsel: der er en anden rapport med dette navn", "duplicateNames_other": "Advar<PERSON>: der er {{count}} andre rapporter med dette navn", "fields": {"automateWeekly": "Automatiser ugentligt", "automateWeekly#description": "", "name": "Rapportnavn", "name#description": "", "showAverages": "<PERSON><PERSON> g<PERSON>", "showAverages#description": "", "showTotals": "Vis totaler", "showTotals#description": ""}}, "errors": {"noReport": "Rapporten findes ikke, eller du har ikke adgang.", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} vil blive slettet permanent.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Du er ikke bemyndiget til at slette {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-mailen vil ikke blive sendt igen", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Rapporten vil blive sendt til disse e-mails", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON><PERSON><PERSON>gen", "runAgain#description": ""}, "table": {"errors": {"noColumns": "<PERSON>æ<PERSON>g en eller flere kolonner", "noColumns#description": "", "noEndDate": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "noEndDate#description": "", "noRobots": "<PERSON>æ<PERSON>g en eller flere robotter", "noRobots#description": "", "noStartDate": "<PERSON><PERSON><PERSON><PERSON>", "noStartDate#description": ""}, "fields": {"average": "Gennemsnit", "average#description": "", "averageShort": "Gns.", "averageShort#description": "", "date": "Da<PERSON>", "date#description": "", "group": "Serienummer/dato", "group#description": "", "groupJob": "Seriel/job", "groupJob#description": "", "mixed": "(<PERSON><PERSON><PERSON>)", "mixed#description": "", "total": "Total", "total#description": "", "totalShort": "SUM", "totalShort#description": ""}, "unknownReport": "Ukendt rapport", "unknownReport#description": ""}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "toLine": "for {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "<PERSON><PERSON> m<PERSON>", "all#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}, "robotsLabel": {"all": "<PERSON>e robotter", "all#description": "", "none": "Ingen robotter", "none#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "brugernavn og adgangskode", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "ukendt udbyder", "unknown#description": ""}, "cards": {"account": "Ko<PERSON>", "account#description": "", "advanced": "<PERSON><PERSON><PERSON>", "advanced#description": "", "localization": "Placering", "localization#description": ""}, "delete": {"deleteAccount": "Slet konto", "deleteAccount#description": "", "dialog": {"description": "ADVARSEL: <PERSON><PERSON> handling kan ikke fortrydes. Alle data vil være tabt.", "description#description": ""}}, "fields": {"language": "Sp<PERSON>", "language#description": "", "measurement": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"imperial": "Imperial (tommer, mph, acres, fahrenheit)", "imperial#description": "", "metric": "Metrisk (mm, km/t, hektar, celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Log ud", "logOut#description": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "title#description": "", "version": "Carbon Ops Center version {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "<PERSON><PERSON><PERSON> findes ikke, eller du har ikke tilladelse til at se vedkommende.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Administrer bruger i Auth0", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Roller og tilladelser", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Entreprenører", "contractors#description": ""}}}}