{"components": {"AlarmTable": {"export": "{{robots}} alarmgeschiedenis {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Ruimtelijke statistieken zijn gratis beschik<PERSON>ar gedurende de proefperiode, maar kunnen te allen tijde worden gewij<PERSON>d of verwijderd of een opwaardering van het abonnement vereisen. Gegevens moeten onafhankelijk worden geverifieerd.", "description#description": "", "title": "Ruimtelijke statistieken Beta", "title#description": ""}, "tooltip": "Deze functie wordt momenteel geëvalueerd en kan op elk moment worden gewijzigd of verwijderd.", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Kan chat niet laden {{message}}", "failed#description": ""}, "machineTranslated": "Automatisch vertaald", "machineTranslated#description": "", "machineTranslatedFrom": "Automatisch vertaald uit het {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Dit bericht werd verwijderd.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} wordt permanent verwijderd.", "description#description": "", "descriptionActive": "{{subject}} is actief en kan dus niet worden verwijderd.", "descriptionActive#description": ""}, "title": "Weet je het zeker?", "title#description": ""}, "CopyToClipboardButton": {"click": "Klik om te kopiëren", "click#description": "", "copied": "Gekopieerd!", "copied#description": ""}, "CropEditor": {"failed": "Gewas-editor kon niet worden geladen", "failed#description": "", "viewIn": "Weergeven in Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Wissen", "clear#description": "", "endDate": "Einddatum", "endDate#description": "", "error": "Fout in datumbereikiezer", "error#description": "", "invalid": "Ongeldig", "invalid#description": "", "last7days": "Afgelopen 7 dagen", "last7days#description": "", "lastMonth": "Afgelopen maand", "lastMonth#description": "", "lastWeek": "Afgelopen week", "lastWeek#description": "", "minusDays": "{{days}} dagen geleden", "minusDays#description": "", "plusDays": "over {{days}} dagen", "plusDays#description": "", "startDate": "Begindatum", "startDate#description": "", "thisMonth": "Deze maand", "thisMonth#description": "", "thisWeek": "Deze week", "thisWeek#description": "", "today": "Vandaag", "today#description": "", "tomorrow": "<PERSON><PERSON>", "tomorrow#description": "", "yesterday": "Gisteren", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "Sorry, onverwachte fout", "error#description": "", "queryLimitReached": "Gedeeltelijke dataset weergeven omdat er te veel gegevens zijn geretourneerd. Contacteer support voor assistentie", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Wat is er gebeurd?", "comment#description": "", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "", "submit": "Verzenden en opnieuw laden", "submit#description": ""}, "GdprConsent": {"description": "<PERSON><PERSON><PERSON> te lezen en akkoord te gaan om verder te gaan", "description#description": "", "statement": "Ik ga akkoord met de <0>gebruiksvoorwaarden</0> en het <1>privacybeleid</1>.", "statement#description": "", "title": "Gebruiksvoorwaarden en privacybeleid", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "<PERSON><PERSON> ve<PERSON>t", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Dit menu in- en uitschakelen", "help#description": "", "title": "Snel<PERSON><PERSON><PERSON>", "title#description": ""}, "LaserTable": {"export": "{{robots}} lasers {{date}}", "export#description": "", "installedOnly": "Alleen g<PERSON>ï<PERSON>alleerd", "installedOnly#description": "", "warnings": {"duplicate": "Deze robot heeft meerdere lasers geregistreerd in de volgende sleuf (sleuven): {{slots}}", "duplicate#description": "", "emptySlot": "Deze robot heeft geen laser geregistreerd in de volgende sleuf (sleuven): {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Nieuwe code", "new#description": ""}, "Loading": {"failed": "Sorry, het laden van Carbon Ops Center is mislukt.", "failed#description": "", "placeholder": "Laden...", "placeholder#description": ""}, "ModelName": {"warning": "Waar<PERSON>uwing: model met lage <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning#description": ""}, "PendingActivationOverlay": {"description": "De activering van je account is in behandeling. Je krijgt een e-mail wanneer dit klaar is!", "description#description": "", "errors": {"carbon": {"description": "Carbon-e-mail gedetecteerd maar niet g<PERSON><PERSON><PERSON><PERSON> inloggen met gebruikersnaam/wachtwoord. Log uit en gebruik de optie 'Aanmelden met Google' voor automatische activering.", "description#description": "", "title": "Niet-g<PERSON><PERSON><PERSON>erd Carbon-account", "title#description": ""}}, "hi": "Beste {{name}}!", "hi#description": "", "logOut": "<PERSON><PERSON><PERSON><PERSON> met de verkeerde account? <0>Uitloggen</0>.", "logOut#description": "", "title": "In afwachting van activering", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON>", "more#description": ""}, "RobotImplementationSelector": {"status": "Implementatiestatus", "status#description": "", "title": "Implementatiestatus wij<PERSON>en", "title#description": "", "warning": "Het wijzigen van de implementatiestatus kan leiden tot geautomatiseerde workflows die de klantervaring kunnen beïnvloeden. DOE HET NIET ALS JE HET NIET ZEKER WEET!", "warning#description": ""}, "ShowLabelsButton": {"text": "Labels", "text#description": "", "tooltip": "Labels weergeven", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Metagegevens weergeven", "tooltip#description": ""}, "almanac": {"crops": {"new": "<PERSON>eu<PERSON> gewas <PERSON>n", "new#description": "", "none": "<PERSON><PERSON> g<PERSON>", "none#description": "", "sync#description": ""}, "cropsSynced": "Alle gewassen", "cropsSynced#description": "", "delete": {"description": "Dit kan niet ongedaan worden gemaakt", "description#description": ""}, "discard": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in {{title}} ongedaan maken?", "description#description": "", "title": "Wijzigen ongedaan maken?", "title#description": ""}, "fineTuneDescription": "Standaard is 5, kan de laserschiettijd verlagen of verhogen met ~20% per stap", "fineTuneDescription#description": "", "fineTuneTitle": "Fijnafstelling vermenigvuldiger", "fineTuneTitle#description": "", "formulas": {"all": "Alle grootten", "all#description": "", "copyFormula": "Formule kopiëren", "copyFormula#description": "", "copySize": "Grootten kopiëren", "copySize#description": "", "exponent": {"description": "Vergroot de straal in mm met deze exponent", "description#description": "", "label": "Exponent (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "<PERSON><PERSON> van 1-10, standaard is 5, kan de laserschiettijd verlagen of verhogen met ~20% per stap. Dit is het nummer dat wordt gebruikt in de basismodus", "description#description": "", "label": "Fijnafstemmingsindex (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Totale vermenigvuldigingsfactor voor het verhogen/verl<PERSON><PERSON> van de fijnafstemmingsindex", "description#description": "", "label": "Vermenigvuldigingswaarde fijnafstemming (FM)", "label#description": ""}, "laserTime": "Lasert<PERSON>j<PERSON>", "laserTime#description": "", "maxTime": {"description": "Maximale schiettijd in ms", "description#description": "", "label": "Maximale tijd", "label#description": ""}, "multiplier": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met de straal in mm", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (A)", "label#description": ""}, "offset": {"description": "Toe te voegen aantal milliseconden ongeacht de straal", "description#description": "", "label": "Offset (b)", "label#description": ""}, "pasteFormula": "Formule plakken", "pasteFormula#description": "", "pasteSize": "Grootten plakken", "pasteSize#description": "", "sync": "Alle grootten synchroniseren", "sync#description": "", "thresholds": "Drempels voor grootte", "thresholds#description": "", "title": "Formules", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Overschakelen naar de geavanceerde modus", "switchModeAdvanced#description": "", "switchModeBasic": "Overschakelen naar de basismodus", "switchModeBasic#description": "", "warnings": {"admin": "Als je deze almanak wi<PERSON><PERSON>, worden alle huidige en toekomstige productie-eenheden gesynchroniseerd.", "admin#description": "", "carbon": "Dit is een door Carbon geleverde almanak. Alleen de fijnafstemmingsindex kan worden gewijzigd.", "carbon#description": "", "production": "Deze almanak draait actief op een robot. Het bewerken zal onmiddellijk effect hebben in het veld.", "production#description": ""}, "weeds": {"new": "<PERSON><PERSON>w <PERSON>", "new#description": "", "none": "<PERSON><PERSON>gor<PERSON>", "none#description": "", "sync#description": ""}, "weedsSynced": "Alle onkruid", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} opgeslagen. Activeren via Operator App Quick Tune", "savedLong#description": "", "testResults": "Voorbeeld resultaten", "testResults#description": ""}, "filters": {"capturedAt": "<PERSON><PERSON> van <PERSON>", "capturedAt#description": "", "diameter": "Diameter", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "Alle afbeeldingen", "allImages#description": "", "categorized": "Gecategoriseerd", "categorized#description": "", "scrollToTop": "Terug naar boven", "scrollToTop#description": "", "sortBy": {"latest": "Nieuwste", "latest#description": ""}, "sortedBy": "Gesorteerd op: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Fout bij het ophalen van status", "error#description": "", "ready": "Resultaten zijn k<PERSON>ar", "ready#description": "", "session#description": "", "session_one": "<PERSON><PERSON>", "session_other": "<PERSON><PERSON><PERSON>", "showResults": "Resultaten weergeven", "showResults#description": "", "status": "{{processed}} / {{total}} resultaten verwerkt", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "De wij<PERSON> van dit plantenprofiel wordt gesynchroniseerd met alle huidige en toekomstige productie-eenheden.", "admin#description": "", "adminMeta": "Alle beheerprofielen zijn beschik<PERSON>ar voor alle klanten. Vermijd rommel!", "adminMeta#description": "", "production": "Dit plantenprofiel wordt actief uitgevoerd op een robot. De bediener wordt op de hoogte gebracht van updates en kan ervoor kiezen om de laatste wijzigingen te gebruiken.", "production#description": "", "protected": "<PERSON><PERSON> is een systeemeigen profiel. Niets kan worden gewijzigd.", "protected#description": "", "unsavedChanges": "Niet-opgeslagen wijzigingen. Druk op 'Opslaan' om de wijzigingen toe te passen.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_one": "Gewijzig<PERSON> sleutel", "changedKey_other": "Gewijzigde sleutels", "newKey": "nieuwe {{key}} naam", "newKey#description": "", "stringReqs": "Mag alleen a-z, 0-9, . en _ bevatten", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "<PERSON><PERSON> sleutel is toegevoegd aan de standaard.", "description#description": ""}, "keyMissing": {"description": "Ontbrekende standaard(en): {{keys}}", "description#description": ""}, "valueChanged": {"description": "<PERSON><PERSON> waarde is gewij<PERSON>d ten opzichte van de standaardwaarde ({{default}}).", "description#description": "", "title": "Configurat<PERSON> g<PERSON>", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "<PERSON><PERSON><PERSON> kon niet worden geladen", "load#description": ""}}, "CustomerSelector": {"empty": "<PERSON><PERSON>", "empty#description": "", "title": "<PERSON><PERSON> wij<PERSON>en", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "<PERSON><PERSON><PERSON><PERSON> vs negeren", "description#description": "", "label": "Schieten", "label#description": ""}, "copy": "Configs kopiëren", "copy#description": "", "ignorable": {"description": "<PERSON><PERSON><PERSON> alleen als de tijd het toe<PERSON>at, niet meegenomen in snelheidsaanbeveling", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "paste": "Configs plakken", "paste#description": ""}, "warnings": {"production": "Deze discriminator draait op een actieve robot. Een eventuele wijziging ervan zal onmiddellijk van kracht worden op het veld.", "production#description": ""}}, "drawer": {"customerMode": "<PERSON><PERSON><PERSON><PERSON>", "customerMode#description": "", "error": "Navigatie kon niet worden geladen", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max. ({{units}})", "max#description": "", "min": "Min. ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "Header kon niet worden geladen", "failed#description": "", "mascot": "<PERSON><PERSON><PERSON><PERSON> van Carbon Robotics", "mascot#description": "", "search": {"failed": "Zoekbalk kon niet worden geladen", "failed#description": "", "focus": "Focus zoeken", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Grootte", "label#description": "", "larger": "groter", "larger#description": "", "smaller": "kleiner", "smaller#description": ""}}, "map": {"bounds": {"reset": "Weergave terugzetten", "reset#description": ""}, "errors": {"empty": "<PERSON><PERSON> locatiege<PERSON><PERSON><PERSON> gera<PERSON>orteerd", "empty#description": "", "failed": "<PERSON><PERSON> kon niet worden geladen", "failed#description": ""}, "filters": {"customer_office": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customer_office#description": "", "hq": "Carbon hoofdkantoor", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Postbus", "po_box#description": "", "shop": "Werkplaats", "shop#description": "", "storage": "Opslag", "storage#description": "", "support_base": "Supportlocatie", "support_base#description": ""}, "fullscreen": "Volledig scherm", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Fout in lagenlegenda", "legend#description": "", "notThinning": "GEEN UITDUNTAAK", "notThinning#description": "", "notWeeding": "GEEN WIEDTAAK", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "FOUT IN HEATMAP", "unknown#description": ""}, "fields": {"block": "Locatie: {{block}}", "block#description": "", "location": "Locatie: {{latitude}}, {{longitude}}", "location#description": "", "size": "Grootte: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Lagen", "name#description": "", "rangeType#description": "", "relative": "Gebruik relatief kleurengamma", "relative#description": "", "relativeRange#description": ""}, "map": "<PERSON><PERSON>", "map#description": "", "measure": {"name": "<PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "<PERSON><PERSON><PERSON><PERSON> uit welke categorie?", "copyFromWhich#description": "", "splitCrops": "Gewassen splitsen", "splitCrops#description": "", "splitWeeds": "Onkruid splitsen", "splitWeeds#description": "", "syncCrops": "Alle gewassen synchroniseren", "syncCrops#description": "", "syncWeeds": "Alle onkruid synchroniseren", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Voorspellende betrouwbaarheidsdrempel om een detectie te gebruiken in dynamische banding", "description#description": "", "label": "Drempel voor banding", "label#description": ""}, "minDoo": {"description": "Minimale detectie over kans", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Voorspellingsbetrouwbaarheidsdrempel om een detectie te gebruiken bij uitdunning", "description#description": "", "label": "Uit<PERSON>nningsdrempel", "label#description": ""}, "weed": {"description": "Voorspellingsbetrouwbaarheidsdrempel om een detectie te gebruiken voor omgekeerde gewasbescherming", "description#description": "", "label": "Omgekeerde gewasbeschermingsdrempel", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Voorspellingsbetrouwbaarheidsdrempel om een detectie te gebruiken voor gewasbescherming", "description#description": "", "label": "Gewasbeschermingsdrempel", "label#description": ""}, "weed": {"description": "Drempelwaarde voor voorspellingsbetrouwbaarheid voor onkruidherkenning", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}}}, "errors": {"sync": "De instellingen voor dit model zijn nog niet gesynchroniseerd met <PERSON> LaserWeeder. Wacht op synchronisatie om de instellingen te bekijken en aan te passen.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Groot<PERSON> splitsen", "splitSizesLong#description": "", "splitSizesShort": "<PERSON><PERSON>", "splitSizesShort#description": "", "syncSizesLong": "Groottes synchroniseren", "syncSizesLong#description": "", "syncSizesShort": "Synchroniseren", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Waarschuwing:{{stopEmphasis}} Deze instellingen omvatten niet-opgeslagen wijzigingen die niet worden weergegeven op de robot.", "exportingUnsavedChanges#description": "", "production": "Dit model draait actief op een robot. Een eventuele wijziging ervan zal onmiddellijk van kracht worden op het veld.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Onbekende alarmen", "unknown#description": ""}, "almanac": {"unknown": "Onbekende almanak", "unknown#description": "", "withName": "Almanak: {{name}}", "withName#description": ""}, "autofixing": "Autofixing-fout", "autofixing#description": "", "banding": {"disabled": "Banding uitgeschakeld", "disabled#description": "", "enabled": "Banding ingeschakeld", "enabled#description": "", "none": "<PERSON><PERSON> banding", "none#description": "", "static": "(STATISCH)", "static#description": "", "withName": "Banding: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Meldstatus kon niet worden geladen", "failed#description": "", "never": "<PERSON><PERSON> g<PERSON>", "never#description": "", "withTime": "Gemeld {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} ingeschakelde gewassen ({{pinned}} vastgepind)", "summary#description": ""}, "delivery": "Levering", "delivery#description": "", "disconnected": "Losgekoppeld", "disconnected#description": "", "discriminator": {"unknown": "Onbekende discriminator", "unknown#description": "", "withName": "Discriminator: {{name}}", "withName#description": ""}, "failed": "Robotstatus kon niet worden geladen", "failed#description": "", "failedShort": "Mislukt", "failedShort#description": "", "implementation": "Implementatie", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventaris", "inventory#description": "", "job": {"none": "<PERSON><PERSON> taken", "none#description": "", "withName": "Taak: {{name}}", "withName#description": ""}, "lasers": "Lasers online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Levensduur:", "lifetime#description": "", "lifted": "Standby (omhoog)", "lifted#description": "", "loading": "Laden", "loading#description": "", "location": {"known": "Locatie: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "<PERSON><PERSON><PERSON> on<PERSON>end", "unknown#description": ""}, "manufacturing": "In productie", "manufacturing#description": "", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Model wordt geladen", "modelLoading#description": "", "notArmed": "<PERSON><PERSON>", "notArmed#description": "", "off_season": "<PERSON><PERSON>en het seizoen", "off_season#description": "", "offline": "Offline sinds {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P onbekend", "unknown#description": ""}, "poweringDown": "Wordt uitgeschakeld", "poweringDown#description": "", "poweringUp": "<PERSON>t ingeschakeld", "poweringUp#description": "", "pre_manufacturing": "Prefabricage", "pre_manufacturing#description": "", "stale": "Over de datum", "stale#description": "", "staleDescription": "Laatst bekende waarde. Robot is offline.", "staleDescription#description": "", "standby": "Standby", "standby#description": "", "thinning": {"disabled": "Uitdunnen uitgeschakeld", "disabled#description": "", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> ingescha<PERSON>d", "enabled#description": "", "none": "<PERSON><PERSON>", "none#description": "", "withName": "Uitdunning: {{name}}", "withName#description": ""}, "today": {"none": "<PERSON><PERSON> gewied vandaag", "none#description": ""}, "unknown": "Status onbekend", "unknown#description": "", "updating": "Update wordt geïnstalleerd", "updating#description": "", "version": {"values": {"unknown": "<PERSON><PERSON><PERSON><PERSON> versie", "unknown#description": "", "updateDownloading": "({{version}} wordt gedownload)", "updateDownloading#description": "", "updateReady": "({{version}} klaar)", "updateReady#description": ""}}, "weeding": "<PERSON><PERSON> met wied<PERSON> van {{crop}}", "weeding#description": "", "weedingDisabled": "Wieden uitgeschakeld", "weedingDisabled#description": "", "weedingThinning": "<PERSON><PERSON> met wieden en uitdunnen van {{crop}}", "weedingThinning#description": "", "winterized": "<PERSON><PERSON><PERSON>", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Bestaat al", "exists#description": "", "unknownClass": "Onbekende robotklasse", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "<PERSON><PERSON> nieuwe config maken", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}}-sja<PERSON><PERSON>on", "templateForClass#description": "", "templateGeneric": "Robotsjabloon", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Je moet alleen verdergaan als er al een configuratie voor {{serial}} bestaat of als je van plan bent om deze handmatig aan te maken.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Geavanceerde instellingen voor snelheidsmeter", "advancedFormulaTitle#description": "", "formulaTitle": "Formule", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Verlaag de voorgestelde snelheid automatisch met de door jou ingevoerde waarde. Als je bijvoorbeeld 5% invoert, wordt de voorgestelde snelheid van 1 km/u verlaagd naar 0,95 km/u.", "description#description": "", "label": "Snelheidsoffset", "label#description": ""}, "decreaseSmoothing": {"description": "Pas het percentage aan waarmee de snelheid afneemt. Hoe hoger de waarde, hoe groter de kans op schommelingen in de snelheidsmeter.", "description#description": "", "label": "Vertraging afvlakken", "label#description": ""}, "increaseSmoothing": {"description": "Pas het percentage aan waarmee de snelheid toeneemt. Hoe hoger de waarde, hoe groter de kans op schommelingen in de snelheidsmeter.", "description#description": "", "label": "Versnelling afvlakken", "label#description": ""}, "maxVelMph": {"description": "<PERSON><PERSON><PERSON> de allerhoogste snelheid in die je bereid bent te rijden. Snelheidsaanbevelingen zullen deze waarde niet overschrijden", "description#description": "", "label": "Maximale snelheid", "label#description": ""}, "minVelMph": {"description": "<PERSON><PERSON><PERSON> de allerlaagste snelheid in die je bereid bent te rijden. Snelheidsaanbevelingen zullen niet lager zijn dan deze waarde", "description#description": "", "label": "Minimumsnelheid", "label#description": ""}, "primaryKillRate": {"description": "Deze waarde is het door jou gewenste percentage onkruid dat wordt gedood.", "description#description": "", "label": "Ideaal doodpercentage", "label#description": ""}, "primaryRange": {"description": "Verhoog deze waarde als je je ideale doodpercentage wilt halen, ongeacht het effect op de rijsnelheid", "description#description": "", "label": "Groene buffer", "label#description": ""}, "rows": {"allRows": "Alle rijen", "allRows#description": "", "row1": "Rij 1", "row1#description": "", "row2": "Rij 2", "row2#description": "", "row3": "Rij 3", "row3#description": ""}, "secondaryKillRate": {"description": "Deze waarde is het laagste aanvaardbare percentage onkruid dat wordt gedood.", "description#description": "", "label": "Minimale doodpercentage", "label#description": ""}, "secondaryRange": {"description": "Verhoog deze waarde als je meer speling wilt hebben voordat je een melding voor lage snelheid ontvangt", "description#description": "", "label": "Gele buffer", "label#description": ""}, "sync": "Alle rijen synchroniseren", "sync#description": "", "warnings": {"admin": "Als je deze snelheidsschatter wij<PERSON>t, worden alle huidige en toekomstige productie-eenheden gesynchroniseerd.", "admin#description": "", "production": "Deze snelheidsschatter draait actief op een robot. Het bewerken ervan zal onmiddellijk van kracht worden op het veld.", "production#description": "", "protected": "Dit is een door Carbon meegeleverd profiel. Er kan niets worden gewijzigd.", "protected#description": "", "unsavedChanges": "Niet-opgeslagen wijzigingen. Druk op 'Opslaan' om de wijzigingen toe te passen.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Geleidelijk", "gradual#description": "", "immediate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immediate#description": ""}, "visualization": {"targetSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_one": "alarm", "alarm_other": "alarmen", "fields": {"code": "Code", "code#description": "", "description": "Beschrijving", "description#description": "", "duration": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"ongoing": "ACTIEF", "ongoing#description": ""}}, "identifier": "Identificatie", "identifier#description": "", "impact": {"name": "Impact", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Niveau", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Afgegaan", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_one": "almanak", "almanac_other": "Almanakken", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_one": "<PERSON><PERSON><PERSON><PERSON>", "assignment_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instructies", "instructions#description": ""}, "intervention#description": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "<PERSON><PERSON><PERSON> met laser", "laserWeed#description": "", "unrecognized": "onbekend type ({{value}})", "unrecognized#description": ""}, "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "<PERSON><PERSON><PERSON> voor laserwieder", "laserWeedRow#description": ""}, "objective_one": "<PERSON><PERSON><PERSON><PERSON>", "objective_other": "doelstellingen", "states": {"acknowledged": "bevestigd", "acknowledged#description": "", "cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelled#description": "", "completed": "voltooid", "completed#description": "", "failed": "mislukt", "failed#description": "", "inProgress": "in uitvoering", "inProgress#description": "", "new": "<PERSON><PERSON>w", "new#description": "", "paused": "g<PERSON><PERSON><PERSON><PERSON>", "paused#description": "", "pending": "in afwachting", "pending#description": "", "ready": "klaar", "ready#description": "", "unrecognized": "onbekende staat ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Taak #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_one": "taak", "task_other": "taken"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_one": "plantenprofiel", "categoryCollectionProfile_other": "plantenprofielen", "fields": {"categories": {"disregard": "Negeren", "disregard#description": "", "name": "Categorieën", "name#description": "", "requiredBaseCategories": "Je moet precies deze categorieën hebben: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Bijgewerkt", "updatedAt#description": ""}, "metadata": {"capturedAt": "Vastgelegd", "capturedAt#description": "", "categoryId": "Categorie-id", "categoryId#description": "", "imageId": "Afbeeldings-ID", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "Punt-ID", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "radius", "radius#description": "", "updatedAt": "Ontvangen", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_one": "config", "config_other": "configs", "key#description": "", "key_one": "sleutel", "key_other": "sleutels", "template#description": "", "template_one": "configurat<PERSON><PERSON><PERSON><PERSON><PERSON>", "template_other": "configurat<PERSON><PERSON><PERSON><PERSON><PERSON>", "value#description": "", "value_one": "waarde", "value_other": "waarden"}, "crops": {"categories": {"unknown": "On<PERSON>end gewas", "unknown#description": ""}, "crop#description": "", "crop_one": "gewas", "crop_other": "gewassen", "fields": {"confidence": {"fields": {"regionalImages": "Regionale beelden", "regionalImages#description": "", "totalImages": "Totaal aantal afbeeldingen:", "totalImages#description": ""}, "name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Gearchiveerd", "archived#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON> onbekend", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Opmerkingen", "notes#description": "", "pinned": "Vastgepind", "pinned#description": "", "recommended": "Aanbevolen", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_one": "klant", "customer_other": "klanten", "fields": {"emails": {"errors": {"formatting": "Eén e-mailadres per regel", "formatting#description": ""}, "name": "E-mailadressen", "name#description": ""}, "featureFlags": {"almanac": {"description": "<PERSON><PERSON><PERSON><PERSON> de tabbladen 'Almanak' en 'Discriminator' in voor robots (de robot moet ook ondersteuning bieden voor almanak en discriminator)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Hier<PERSON> wordt het tabblad 'Plantenprofiel' ingeschakeld voor robots", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Functievlaggen maken beta-functionaliteit mogelijk voor alle gebruikers bij een klant", "description#description": "", "jobs": {"description": "<PERSON><PERSON><PERSON><PERSON> taken in (robot moet ook ondersteuning bieden voor taken )", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Toont een nieuw visueel ontwerp voor robotgegevens", "description#description": "", "name": "Nieuw statistiekenontwerp", "name#description": ""}, "name": "Functievlaggen", "name#description": "", "off": "UIT", "off#description": "", "on": "AAN", "on#description": "", "reports": {"description": "<PERSON><PERSON> het tabblad 'Rapporten' en de functies daarbinnen", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Ruimtelijke gegevens weergeven, inclusief heatmaps en grafieken", "description#description": "", "name": "Ruimtelijke gegevens", "name#description": ""}, "summary": "{{enabled}}/{{total}} functievlaggen geactiveerd", "summary#description": "", "unvalidatedMetrics": {"description": "Toon beta statistieken in afwachting van veldvalidatie in gecertificeerde statistieken", "description#description": "", "name": "Beta statistieken", "name#description": ""}, "velocityEstimator": {"description": "Maakt het bekijken en bewerken van doelsnelheidschatterprofielen mogelijk (de robot moet ook ondersteuning bieden voor de doelsnelheidschatter)", "description#description": "", "name": "Doelsnelheidsschatter", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Uitvoeren op", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Als dit is ingeschakeld, worden er wekelijks rapporten uitgevoerd met de volgende instellingen voor alle actieve robots", "description#description": "", "name": "Wekelijkse rapporten", "name#description": ""}, "weeklyReportHour": "Uitvoeren om", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Terugblik", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Uitvoeren in", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_one": "uitzondering aanbrengen", "discriminator_other": "uitzonderingen aanbrengen", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farm_other": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "obstacle#description": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_one": "punt", "point_other": "punten", "zone#description": "", "zone_one": "zone", "zone_other": "zones"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_one": "veld definitie", "fieldDefinition_other": "veld definities", "fields": {"boundary": "<PERSON><PERSON><PERSON> van het veld", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Richting aanplanting", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_one": "<PERSON>le waarde", "global_other": "universele waarden", "values": {"plantProfileModelId": {"description": "Basismodel gebruikt door alle klant- en administratieprofielen voor '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) model-id", "label#description": ""}}}, "images": {"fields": {"camera": "Camera", "camera#description": "", "capturedAt": "Datum/Tijd", "capturedAt#description": "", "geoJson": "Locatie", "geoJson#description": "", "url": "Afbeelding openen", "url#description": ""}, "image#description": "", "image_one": "foto", "image_other": "foto's"}, "jobs": {"job#description": "", "job_one": "taak", "job_other": "taken"}, "lasers": {"fields": {"cameraId": "Camera ID", "cameraId#description": "", "error": {"values": {"false": "Nominaal", "false#description": ""}}, "installedAt": "Installatiedatum", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Serienummer onbekend", "unknown#description": ""}}, "lifetimeSec": "Gebruikstijd", "lifetimeSec#description": "", "powerLevel": "Vermogensniveau", "powerLevel#description": "", "removedAt": "Verwijderd op", "removedAt#description": "", "rowNumber": "<PERSON><PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Aantal keer gelaserd", "totalFireCount#description": "", "totalFireTimeMs": "<PERSON><PERSON><PERSON><PERSON> gelase<PERSON>", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "Vervallen", "expired#description": "", "hours": "Uren: {{installed}}/{{total}} ({{percent}} remaining)", "hours#description": "", "hoursUnknown": "Uren: Onbekend", "hoursUnknown#description": "", "months": "Maanden: {{installed}}/{{total}} ({{percent}} remaining)", "months#description": "", "monthsUnknown": "Maanden: Onbekend", "monthsUnknown#description": "", "unknown": "<PERSON><PERSON><PERSON> on<PERSON>end", "unknown#description": ""}}}, "laser#description": "", "laser_one": "laser", "laser_other": "Lasers"}, "models": {"model#description": "", "model_one": "model", "model_other": "modellen", "none": "Geen model", "none#description": "", "p2p#description": "", "p2p_one": "P2P-model", "p2p_other": "P2P-modellen", "unknown": "Onbekend model", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "Uitgevoerd door", "authorId#description": "", "createdAt": "Publicatiedatum", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_one": "rapport uitvoeren", "run_other": "rapport aan het uitvoeren"}, "reports": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Geautomatiseerd", "name#description": "", "values": {"weekly": "Wekelijks", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_one": "rapport", "report_other": "rapporten"}, "robots": {"classes": {"buds#description": "", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_one": "Modulevalidatiestation", "moduleValidationStations_other": "Modulevalidatiestations", "reapersCarbon#description": "", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_one": "Trekker", "rtcs_other": "Trekkers", "simulators#description": "", "simulators_one": "Simulator", "simulators_other": "Simulators", "slayersCarbon#description": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Onbekende klasse", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasers offline", "lasersOffline#description": "", "lifetimeArea": "Levens<PERSON><PERSON> geb<PERSON>", "lifetimeArea#description": "", "lifetimeTime": "Levens<PERSON>ur", "lifetimeTime#description": "", "localTime": "Lokale tijd", "localTime#description": "", "reportedAt": "Laatst bijgewerkt", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Softwareversie", "softwareVersion#description": "", "supportSlack": "Slack-ondersteuningskanaal", "supportSlack#description": "", "targetVersion": "Target<PERSON>ie", "targetVersion#description": ""}, "robot#description": "", "robot_one": "robot", "robot_other": "robots", "unknown": "<PERSON><PERSON><PERSON><PERSON> robot", "unknown#description": ""}, "users": {"activated": "Gea<PERSON>erd", "activated#description": "", "fields": {"email": "E-mailadres", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Activatie", "name#description": "", "values": {"false": "IN BEHANDELING", "false#description": ""}}}, "operator#description": "", "operator_one": "operator", "operator_other": "operatoren", "role#description": "", "role_one": "Rol", "role_other": "<PERSON><PERSON>", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (technisch)", "carbon_tech#description": "", "farm_manager": "Boerderijmanager", "farm_manager#description": "", "operator_advanced": "<PERSON><PERSON><PERSON> (geavanceerd)", "operator_advanced#description": "", "operator_basic": "<PERSON><PERSON><PERSON>", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Onbekende rol", "unknown_role#description": ""}, "staff": "<PERSON><PERSON>", "staff#description": "", "user#description": "", "user_one": "g<PERSON><PERSON><PERSON><PERSON>", "user_other": "gebruikers"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_one": "snelheidsschatter", "velocityEstimator_other": "snelheidsschatters"}, "weeds": {"categories": {"blossom": "Bloei", "blossom#description": "", "broadleaf": "Breedbladig onkruid", "broadleaf#description": "", "fruit": "Vrucht", "fruit#description": "", "grass": "Dunbladig onkruid", "grass#description": "", "offshoot": "<PERSON><PERSON><PERSON><PERSON>", "offshoot#description": "", "preblossom": "V<PERSON>b<PERSON><PERSON>", "preblossom#description": "", "purslane": "<PERSON><PERSON><PERSON>", "purslane#description": "", "runner": "<PERSON><PERSON>", "runner#description": "", "unknown": "Onbekend onkruid", "unknown#description": ""}, "weed#description": "", "weed_one": "<PERSON><PERSON><PERSON><PERSON>", "weed_other": "<PERSON><PERSON><PERSON><PERSON>"}}, "utils": {"actions": {"add": "Toevoegen", "add#description": "", "addLong": "Toevoegen {{subject}}", "addLong#description": "", "apply": "Toepassen", "apply#description": "", "applyLong": "{{subject}} toepassen", "applyLong#description": "", "backLong": "terug naar {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "{{subject}} annuleren", "cancelLong#description": "", "clear": "Wissen", "clear#description": "", "confirm": "Bevestigen", "confirm#description": "", "continue": "Doorgaan", "continue#description": "", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON> maken van {{subject}}", "copyLong#description": "", "create": "Aanmaken", "create#description": "", "createdLong": "{{subject}} aangemaakt", "createdLong#description": "", "delete": "Verwijderen", "delete#description": "", "deleteLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} verwi<PERSON><PERSON>d", "deletedLong#description": "", "disableLong": "<PERSON><PERSON><PERSON> {{subject}} uit", "disableLong#description": "", "discard": "Ongedaan maken", "discard#description": "", "edit": "Bewerken", "edit#description": "", "editLong": "{{subject}} bewerken", "editLong#description": "", "enableLong": "{{subject}} activeren", "enableLong#description": "", "exit": "Sluiten", "exit#description": "", "exitLong": "{{subject}} sluiten", "exitLong#description": "", "goToLong": "Ga naar {{subject}}", "goToLong#description": "", "invite": "Uitnodigen", "invite#description": "", "inviteLong": "{{subject}} uitnodigen", "inviteLong#description": "", "invitedLong": "{{subject}} uitgenodigd", "invitedLong#description": "", "leaveUnchanged": "Ongewij<PERSON>d laten", "leaveUnchanged#description": "", "new": "<PERSON><PERSON><PERSON>", "new#description": "", "newLong": "<PERSON><PERSON><PERSON>(e) {{subject}}", "newLong#description": "", "next": "Volgende", "next#description": "", "pause": "<PERSON><PERSON><PERSON>", "pause#description": "", "play": "Afspelen", "play#description": "", "previous": "Vorige", "previous#description": "", "ranLong": "{{subject}} is uitgevoerd", "ranLong#description": "", "reload": "<PERSON><PERSON><PERSON>", "reload#description": "", "resetLong": "{{subject}} resetten", "resetLong#description": "", "retry": "Opnieuw", "retry#description": "", "run": "Uitvoeren", "run#description": "", "runLong": "{{subject}} uitvoeren", "runLong#description": "", "save": "Opsla<PERSON>", "save#description": "", "saveLong": "{{subject}} opslaan", "saveLong#description": "", "saved": "Opgeslagen", "saved#description": "", "savedLong": "{{subject}} is opgeslagen", "savedLong#description": "", "search": "<PERSON><PERSON>", "search#description": "", "searchLong": "{{subject}} zoeken", "searchLong#description": "", "selectAll": "Alles selecteren", "selectAll#description": "", "selectLong": "{{subject}} selecteren", "selectLong#description": "", "selectNone": "<PERSON><PERSON> selecteren", "selectNone#description": "", "send": "Verzenden", "send#description": "", "showLong": "{{subject}} weergeven", "showLong#description": "", "submit": "Opsturen", "submit#description": "", "toggle": "In- of uitschakelen", "toggle#description": "", "toggleLong": "{{subject}} in- of uitschakelen", "toggleLong#description": "", "update": "Bijwerken", "update#description": "", "updated": "Bijgewerkt", "updated#description": "", "updatedLong": "{{subject}} is bijgewerkt", "updatedLong#description": "", "uploaded": "Geüpload", "uploaded#description": "", "viewLong": "{{subject}} be<PERSON><PERSON>en", "viewLong#description": ""}, "descriptors": {"active": "Actief", "active#description": "", "critical": "K<PERSON>iek", "critical#description": "", "default": "Standaard", "default#description": "", "degraded": "Verslechterd", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "Uitgeschakeld", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Ingeschakeld", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Fout", "error#description": "", "estopOff": "<PERSON><PERSON>", "estopOff#description": "", "estopOn": "E-gestopt", "estopOn#description": "", "fast": "Snel", "fast#description": "", "few": "<PERSON><PERSON><PERSON>", "few#description": "", "good": "Goed", "good#description": "", "hidden": "Verborgen", "hidden#description": "", "high": "<PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inactief", "inactive#description": "", "interlockSafe": "Schi<PERSON>n toe<PERSON>an", "interlockSafe#description": "", "interlockUnsafe": "Sc<PERSON><PERSON><PERSON> verhinderd", "interlockUnsafe#description": "", "large": "Groot", "large#description": "", "laserKeyOff": "Vergrendeld", "laserKeyOff#description": "", "laserKeyOn": "Ingeschakeld", "laserKeyOn#description": "", "liftedOff": "Omlaag", "liftedOff#description": "", "liftedOn": "Omhoog", "liftedOn#description": "", "loading": "Laden", "loading#description": "", "low": "Laag", "low#description": "", "majority": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "majority#description": "", "medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medium#description": "", "minority": "<PERSON><PERSON><PERSON><PERSON>", "minority#description": "", "name": "<PERSON><PERSON>", "name#description": "", "no": "<PERSON><PERSON>", "no#description": "", "none": "<PERSON><PERSON>", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "Slecht", "poor#description": "", "progress": "Voortgang", "progress#description": "", "serial": "Serienummer", "serial#description": "", "slow": "<PERSON><PERSON><PERSON>", "slow#description": "", "small": "<PERSON>", "small#description": "", "sparse": "Laag", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_one": "Type", "type_other": "Types", "unknown": "Onbekend", "unknown#description": "", "waterProtectNormal": "Normale vochtigheid", "waterProtectNormal#description": "", "waterProtectTriggered": "Water gedetecteerd", "waterProtectTriggered#description": "", "yes": "<PERSON>a", "yes#description": ""}, "form": {"booleanType": "<PERSON>t een boolean zijn", "booleanType#description": "", "copyConfigFrom": "Configuratie <PERSON> van...", "copyConfigFrom#description": "", "integerType": "<PERSON>t een geheel getal zijn", "integerType#description": "", "maxLessThanMin": "<PERSON> moet groter zijn dan min", "maxLessThanMin#description": "", "maxSize": "De lengte mag niet meer dan {{limit}} tekens zijn", "maxSize#description": "", "minGreaterThanMax": "<PERSON> moet kleiner zijn dan max", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON>", "moveDown#description": "", "moveUp": "<PERSON>ar boven", "moveUp#description": "", "noOptions": "Geen opties", "noOptions#description": "", "numberType": "<PERSON>t een getal zijn", "numberType#description": "", "optional": "(optioneel)", "optional#description": "", "required": "Vere<PERSON>", "required#description": "", "stringType": "<PERSON>t een tekenreeks zijn", "stringType#description": ""}, "lists": {"+3": "{{b}}, en {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} en {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "<PERSON><PERSON> <PERSON>", "loadMore#description": "", "noMoreResults": "<PERSON><PERSON> resultaten meer", "noMoreResults#description": "", "noResults": "<PERSON><PERSON> resultaten", "noResults#description": ""}, "metrics": {"aggregates": {"max": "<PERSON>.", "max#description": "", "min": "<PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Gemiddelde straal gewas", "avgCropSizeMm#description": "", "avgSpeedMph": "Gemiddelde verplaatsingssnelheid", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Gemid<PERSON><PERSON> s<PERSON>", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> (genegeerd)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Gemiddelde straal on<PERSON>id", "avgWeedSizeMm#description": "", "bandingConfigName": "<PERSON><PERSON><PERSON><PERSON> config", "bandingConfigName#description": "", "bandingEnabled": "Waarneming", "bandingEnabled#description": "", "bandingPercentage": "Percentage waarnemen", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Gemiddelde dekkingssnelheid", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "<PERSON><PERSON><PERSON><PERSON> van het gewas", "cropDensitySqFt#description": "", "distanceWeededMeters": "Afgelegde afstand", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Resterend gewas", "keptCrops#description": "", "killedWeeds": "Onkruid verdelgd", "killedWeeds#description": "", "missedCrops": "<PERSON><PERSON><PERSON>id gemist", "missedCrops#description": "", "missedWeeds": "<PERSON><PERSON><PERSON>id gemist", "missedWeeds#description": "", "notThinning": "<PERSON><PERSON><PERSON> tijdens niet uitdu<PERSON>n", "notThinning#description": "", "notWeeding": "<PERSON><PERSON><PERSON><PERSON> tijdens niet wieden", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "operatorEffectiveness#description": "", "overallEfficiency": "<PERSON><PERSON><PERSON><PERSON> presta<PERSON>", "overallEfficiency#description": "", "skippedCrops": "<PERSON><PERSON><PERSON> gene<PERSON>", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON><PERSON><PERSON> genegeerd", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Tijd om de target te wieden", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Gew<PERSON> uitged<PERSON>", "thinnedCrops#description": "", "thinningEfficiency": "Prestatie bij uitdunnen", "thinningEfficiency#description": "", "timeEfficiency": "Operationele efficiency", "timeEfficiency#description": "", "totalCrops": "Gewassen gevonden", "totalCrops#description": "", "totalWeeds": "Onkruid gevonden", "totalWeeds#description": "", "totalWeedsInBand": "Onkruid gevonden (in waarneming)", "totalWeedsInBand#description": "", "uptimeSeconds": "Werktijd", "uptimeSeconds#description": "", "validCrops": "Gewassen gevonden", "validCrops#description": "", "weedDensitySqFt": "Dichtheid <PERSON>", "weedDensitySqFt#description": "", "weedingEfficiency": "<PERSON><PERSON><PERSON> wieden", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON><PERSON><PERSON> wieden", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Onkruid soort: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Onk<PERSON><PERSON> soort: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Onkruid soort: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Onk<PERSON><PERSON> soort: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Dit wordt berekend vóór het uitdunnen als uitdunnen was ingeschakeld", "avgCropSizeMm#description": "", "bandingConfigName": "Uw meest recent geselecteerde banderolleerprofiel", "bandingConfigName#description": "", "crop": "Uw meest recent geselect<PERSON>de gewas", "crop#description": "", "cropDensitySqFt": "Dit wordt berekend vóór het uitdunnen als uitdunnen was ingeschakeld", "cropDensitySqFt#description": "", "keptCrops": "Het geschatte aantal gewassen dat is behouden na het uitdunnen", "keptCrops#description": "", "killedWeeds": "LaserWeeder identificeerde het object als onkruid en vuurde erop", "killedWeeds#description": "", "missedCrops": "<PERSON><PERSON><PERSON> was gemarkeerd voor u<PERSON>, maar werd gemist. <PERSON>eel voorkomende redenen zijn: te hoge snelheid, buiten bereik of systeemfout.", "missedCrops#description": "", "missedWeeds": "Onk<PERSON><PERSON> werd herkend maar gemist. Veel voorkomende redenen zijn: te hoge snelheid, buiten bereik of systeemfout.", "missedWeeds#description": "", "operatorEffectiveness": "Toont hoe goed de werkelijke rijsnelhe<PERSON> overeen<PERSON><PERSON> met de door de snelheidsschatter aanbevolen doelsnelheid", "operatorEffectiveness#description": "", "overallEfficiency": "(wiedprestatie + uitdunprestatie) / 2, als je zowel wiedt als uitdunt", "overallEfficiency#description": "", "skippedCrops": "Gewas werd opzettelijk overgeslagen tijdens het uitdunnen. Veel voorkomende redenen zijn: uitgeschakeld in Quick Tune, buiten de strook of in de buurt van druppelslang.", "skippedCrops#description": "", "skippedWeeds": "Onkruid werd opzettelijk overgeslagen. Veel voorkomende redenen zijn: uitgeschakeld in Quick Tune of buiten de strook.", "skippedWeeds#description": "", "thinningEfficiency": "(Uitgedunde gewassen + behouden gewassen) / geschat aantal gevonden gewassen × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(Actieve werktijd / Inschakeltijd) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "De volledige tijd dat de <PERSON>er was ingeschakeld. Ook wanneer hij in stand-by staat en/of opgetild is.", "uptimeSeconds#description": "", "weedDensitySqFt": "Geschat gevonden onkruid (totaal) / dekking", "weedDensitySqFt#description": "", "weedingEfficiency": "(Onk<PERSON>id gedood / Onkruid gevonden in strook) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "De ho<PERSON>elheid tijd dat de LaserWeeder actief aan het wieden of uitdunnen was.", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Snelheidsefficiëntie", "operatorEffectiveness#description": "", "timeEfficiency": "Machinegebruik", "timeEfficiency#description": "", "totalWeeds": "Gevonden onkruid (totaal)", "totalWeedsInBand": "Weeds Found (binnen de strook)", "totalWeedsInBand#description": "", "uptimeSeconds": "Inschakeltijd", "uptimeSeconds#description": "", "validCrops": "Geschat aantal gevonden gewassen", "validCrops#description": "", "weedingUptimeSeconds": "Actieve werktijd", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Oppervlakte", "coverage#description": "", "field": "Veld", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Prestatie", "performance#description": "", "speed": "<PERSON><PERSON><PERSON><PERSON>", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Gebruikstijd", "usage#description": ""}, "metric#description": "", "metric_one": "meetbare groot<PERSON><PERSON>", "metric_other": "meetbare groot<PERSON><PERSON>", "spatial": {"heatmapWarning": "per blok van ca. 20×20 voet (ca. 6,01x6,01 m)", "heatmapWarning#description": "", "metrics": {"altitude": "<PERSON><PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "Noodstop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interlock", "interlock#description": "", "keptCropDensity": "Resterende dichtheid gewas", "keptCropDensity#description": "", "laserKey": "Lasersle<PERSON>l", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Snelheidsefficiency", "speed#description": "", "speedTargetMinimum": "<PERSON><PERSON><PERSON><PERSON><PERSON> beoogde snelheid (minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Gemid<PERSON><PERSON> beoogde snelheid (rij 1)", "speedTargetRow1#description": "", "speedTargetRow2": "<PERSON><PERSON><PERSON><PERSON><PERSON> beoogde snelhe<PERSON> (rij 2)", "speedTargetRow2#description": "", "speedTargetRow3": "<PERSON><PERSON><PERSON><PERSON><PERSON> beoogde snelhe<PERSON> (rij 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "<PERSON><PERSON><PERSON><PERSON><PERSON> beoogde snelheid", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Uitdu<PERSON><PERSON>", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Tijd", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Beveiliging tegen water", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "<PERSON><PERSON><PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected#description": "", "showAll": "Alle {{objects}} weergeven", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_one": "watt", "WLong_other": "watts", "ac": "ac", "ac#description": "", "ac/h": "ha/u", "ac/h#description": "", "acLong#description": "", "acLong_one": "acre", "acLong_other": "acres", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_one": "dag", "dLong_other": "dagen", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_one": "voet", "ftLong_other": "voet", "h": "h", "h#description": "", "hLong#description": "", "hLong_one": "uur", "hLong_other": "uren", "ha": "ha", "ha#description": "", "ha/h": "ha/u", "ha/h#description": "", "haLong#description": "", "haLong_one": "hectare", "haLong_other": "hectaren", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/u", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_one": "meter", "mLong_other": "meters", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min.", "min#description": "", "minLong#description": "", "minLong_one": "minuut", "minLong_other": "minuten", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "mnd", "month#description": "", "monthLong#description": "", "monthLong_one": "maand", "monthLong_other": "ma<PERSON>en", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_one": "seconde", "sLong_other": "seconden", "seconds#description": "", "watts#description": "", "week": "wkn.", "week#description": "", "weekLong#description": "", "weekLong_one": "week", "weekLong_other": "weken", "yd#description": "", "year": "jr.", "year#description": "", "yearLong#description": "", "yearLong_one": "jaar", "yearLong_other": "jaren"}}, "views": {"admin": {"alarms": {"allowWarning": "Door codes toe te voegen aan de allowlist kunnen alarmen Slack-ondersteuningskanalen pingen", "allowWarning#description": "", "blockWarning": "Door codes aan de blocklist toe te voegen wordt voorkomen dat alarmen Slack-ondersteuningskanalen kunnen pingen", "blockWarning#description": "", "lists": "<PERSON><PERSON><PERSON>", "lists#description": "", "title": "Algemene allowlist voor alarmen", "title#description": "", "titleAllow": "Allowlist voor alarmen", "titleAllow#description": "", "titleBlock": "Blocklist voor alarmen", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Instellen", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> op <PERSON>, <tt>{row1,row2,row3}</tt> op Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_one": "bewerking", "operation_other": "bewerkingen", "operationsCount": "Bewerkingen ({{count}})", "operationsCount#description": "", "operationsHint": "Selecteer een knooppunt in het configuratieschema om een bewerking toe te voegen.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_one": "{{count}} fout tegengekomen", "encounteredErrors_other": "{{count}} fouten tegengekomen", "noChanges": "geen wijzigingen", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_one": "{{count}} sleutel bijgewerkt", "updatedKeys_other": "{{count}} sleutels bijgewerkt"}, "outcomes": {"failure": "Mislukt", "failure#description": "", "partial": "Gedeeltelijk gelukt", "partial#description": "", "success": "Gelukt", "success#description": ""}, "title": "<PERSON><PERSON><PERSON> configuraties", "title#description": ""}, "clearCaches": {"action": "<PERSON><PERSON>", "action#description": "", "description": "Problemen? Pro<PERSON>r eerst de cache van Robot Syncer te verversen.", "description#description": ""}, "warnings": {"global": "Het wij<PERSON>en van deze config heeft invloed op de standaardinstellingen en aanbevelingen voor alle huidige en toekomstige {{class}}", "global#description": "", "notSimon": "Je bent <PERSON> niet, dus je zou dit waarschijnlijk niet moeten bewerken... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "De volgende wijzigingen zijn nog niet gesynchroniseerd met {{serial}}:", "description#description": "", "title": "<PERSON>et gesynchroniseerde sleutels", "title#description": ""}}}, "portal": {"clearCaches": {"action": "<PERSON><PERSON><PERSON>", "action#description": "", "description": "Maakt de interne caches leeg voor Ops Center. Dit **zal** op de korte termijn sommige query's vertragen, maar **kan** problemen met vastzittende verouderde gegevens oplossen", "description#description": "", "details": "<PERSON>uk hierop als je de machtigingen van een gebruiker handmatig hebt aangepast in Auth0 (niet via Ops Center), of wijzigingen hebt aangebracht in een integratie van derden zoals Stream of Slack die niet worden weergegeven.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Opties op deze pagina zijn van invloed op de live werking van Ops Center in productie.", "global#description": "", "notPortalAdmin": "Je bent niet <PERSON> of Willow, dus waarschi<PERSON>jk zou je dit niet moeten bewerken... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Slack-ondersteuningskanaal moet beginnen met \"#\": e.g., \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Draaigeschiedenis verbergen", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan trekkers", "orchestrateView#description": "", "showPivotHistory": "Draaigeschiedenis weergeven", "showPivotHistory#description": ""}, "fetchFailed": "Locatiegegevens zijn niet geladen", "fetchFailed#description": "", "goLive": "live bijwerken", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON><PERSON> verbergen", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> niet op<PERSON>. Opnieuw proberen?", "assignmentsFailed#description": "", "cancelDialog": {"description": "De taak kan niet meer aan trekkers worden toegewezen en moet opnieuw worden aangemaakt.", "description#description": ""}, "customer": {"unknown": "<PERSON><PERSON> on<PERSON>", "unknown#description": "", "withName": "Klant: {{name}}", "withName#description": ""}, "farm": {"unknown": "<PERSON><PERSON><PERSON><PERSON>end", "unknown#description": "", "withName": "Boerderij: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON><PERSON> onbekend", "unknown#description": "", "withName": "Veld: {{name}}", "withName#description": ""}, "jobFinished": "Taak voltooid om {{time}}", "jobFinished#description": "", "jobStarted": "Taak gestart om {{time}}", "jobStarted#description": "", "openInFarmView": "Openen in boerderijweergave", "openInFarmView#description": "", "state": "Staat: {{state}}", "state#description": "", "type": "Type taak: {{type}}", "type#description": ""}, "lastPolled": "Laatst gepeild", "lastPolled#description": "", "live": "Live", "live#description": "", "objectiveFromOtherJob": "<PERSON><PERSON><PERSON><PERSON> uit een andere taak", "objectiveFromOtherJob#description": "", "rowWidthUnits": "Rijbreedte {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Boerderijen", "farms#description": "", "tractors": "Trekkers", "tractors#description": ""}, "showRows": "<PERSON><PERSON><PERSON><PERSON> we<PERSON>", "showRows#description": "", "stalePivots": "Draai-info kan oud zijn", "stalePivots#description": "", "suggestedAssignments": "Voorgestelde toewijzingen", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "Deze punten verbergen", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "<PERSON>een geselect<PERSON>de weergeven", "onlyShowSelected#description": "", "showAllPoints": "Alle punten weergeven", "showAllPoints#description": "", "showThesePoints": "Deze punten weergeven", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Midden", "center#description": "", "centerPivot": "Centrale draaipunt", "centerPivot#description": "", "endpointId": "Eindpunt-id", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Plantrichting", "plantingHeading#description": "", "point": "Punt", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "<PERSON><PERSON><PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "<PERSON><PERSON>", "none#description": "", "rtkFixed": "RTK-gefixeerd", "rtkFixed#description": "", "rtkFloat": "RTK-zwevend", "rtkFloat#description": "", "unknown": "Onbekend fixatietype", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "<PERSON>e punten", "allPoints#description": "", "boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Midden", "center#description": "", "centerPivot": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "centerPivot#description": "", "endpointId": "Eindpunt-id", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Plantrichting", "plantingHeading#description": "", "point": "Punt", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "Naamloos punt <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmBoundary#description": "", "field": "Veld", "field#description": "", "headland": "Landtong", "headland#description": "", "obstacle": "Obstakel", "obstacle#description": "", "privateRoad": "Privéweg", "privateRoad#description": "", "unknown": "Onbekend zonetype", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "Tekenen", "draw#description": ""}, "errors": {"exactlyTwoPoints": "<PERSON>jn moet exact twee punten hebben", "exactlyTwoPoints#description": "", "wrongFieldType": "Veld \"{{field}}\" moet {{want}} zijn", "wrongFieldType#description": "", "wrongGeometryType": "Geometrie moet van het type {{want}} zijn", "wrongGeometryType#description": "", "wrongJsonType": "JSON moet een object zijn", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Geen robots online", "empty#description": ""}, "title": "Controle-overzicht", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Wijzigingsgeschiedenis bekijken", "open#description": "", "title": "Wijzigingsgeschiedenis", "title#description": ""}, "errors": {"failed": "Kon de config tree niet laden", "failed#description": ""}, "onlyChanged": "<PERSON>een gewijzigde tonen", "onlyChanged#description": ""}, "errors": {"empty": "Geen <PERSON> toege<PERSON>zen", "empty#description": ""}, "hardware": {"errors": {"old": "Robot meldt geen computerserienummers (waarschijnlijk te oud)", "old#description": ""}, "fields": {"hostname": "Hostnaam", "hostname#description": ""}, "installedVersion": "Geïnstalleerde versie:", "installedVersion#description": "", "ready": {"name": "<PERSON><PERSON><PERSON> voor installatie:", "name#description": "", "values": {"false": "Downloaden...", "false#description": "", "installed": "Geïnstalleerd", "installed#description": "", "true": "<PERSON><PERSON><PERSON>!", "true#description": ""}}, "tabs": {"computers": "Computers", "computers#description": "", "versions": "Versies", "versions#description": ""}, "targetVersion": "Doelversie:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Versie updategeschiedenis <0>binnenkort beschikbaar™️</0>", "updateHistory#description": ""}, "history": {"borders": "<PERSON><PERSON><PERSON><PERSON>", "borders#description": "", "errors": {"invalidDate": "Selecteer een geldig da<PERSON>", "invalidDate#description": "", "noJobs": "<PERSON><PERSON> taken gemeld in het geselecteerde bereik", "noJobs#description": "", "noMetrics": "<PERSON><PERSON> statist<PERSON><PERSON> gera<PERSON>orteerd", "noMetrics#description": ""}, "moreMetrics": "<PERSON>er statistieken bekijken", "moreMetrics#description": "", "navTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navTitle#description": "", "placeholder": "Selecteer een taak of datum om gegevens te bekijken", "placeholder#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "warnings": {"beta": {"description": "Statistieken die nog gecontroleerd moeten worden, worden in blauw weergegeven", "description#description": ""}, "ongoing": "De statistieken voor deze datum zijn nog niet definitief", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "Definitie", "definition#description": "", "dynamic": "Dynamisch", "dynamic#description": "", "dynamicDisabled": "(Dynamische banding uitgeschakeld in config)", "dynamicDisabled#description": "", "rows": "Rijen", "rows#description": "", "static": "Statisch", "static#description": "", "type": "<PERSON><PERSON>", "type#description": "", "unknown": "Banding onbekend", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "<PERSON><PERSON><PERSON>", "version#description": ""}, "config": {"changes#description": "", "changes_one": "{{count}} <PERSON>na<PERSON><PERSON><PERSON><PERSON>ing", "changes_other": "{{count}} almanakwijzigingen", "cpt": "Gewasherkenningsdrempel", "cpt#description": "", "default": "(STANDAARD: {{value}})", "default#description": "", "wpt": "Onkruidherkenningsdrempel", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON><PERSON>", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backRight#description": "", "frontLeft": "Linksvoor", "frontLeft#description": "", "frontRight": "Rechtsvoor", "frontRight#description": "", "title": "Wiel-encoders", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "<PERSON>n robotsamenvatting niet laden", "failed#description": "", "lasers": {"disabled#description": "", "disabled_one": "{{count}} uitgeschakelde laser", "disabled_other": "{{count}} uitgeschakelde lasers", "row": "Rij {{row}}", "row#description": ""}, "machineHealth": "To<PERSON>nd van de machine", "machineHealth#description": "", "navTitle": "<PERSON><PERSON><PERSON><PERSON>", "navTitle#description": "", "safetyRadius": {"driptape": "<PERSON><PERSON><PERSON><PERSON>", "driptape#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "sections": {"management": "Management", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Chip-overzicht", "chipChart#description": "", "datasetVisualization": "Visualisatie dataset", "datasetVisualization#description": "", "title": "Links voor ondersteuning", "title#description": ""}}, "support": {"carbon": "Carbon Support", "carbon#description": "", "chatMode": {"legacy": "Legacy Chat", "legacy#description": "", "new": "<PERSON><PERSON><PERSON>", "new#description": ""}, "errors": {"failed": "<PERSON>n bericht niet laden", "failed#description": "", "old": {"description": "{{serial}} gebruikt softwareversie {{version}}. Moet {{target}} zijn om de supportchat te kunnen gebruiken.", "description#description": "", "title": "Robotversie niet voldo<PERSON>e", "title#description": ""}}, "localTime": "Plaatselijke tijd: {{time}}", "localTime#description": "", "navTitle": "Support", "navTitle#description": "", "toCarbon": "Bericht naar $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Bericht naar $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} is offline. De bediener ontvangt het bericht pas als de robot verbinding heeft.", "description#description": "", "title": "Robot is offline", "title#description": ""}}}, "toggleable": {"internal": "Intern", "internal#description": ""}, "uploads": {"errors": {"empty": "Gee<PERSON> uploads", "empty#description": ""}}}, "title": "Vloot", "title#description": "", "views": {"fields": {"name": "Filternaam", "name#description": "", "otherRobots": "Andere robots ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Vastgepinde robots", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "Ka<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_one": "filter", "fleetView_other": "filters", "tableOnly": "Sommige kolommen zijn enkel beschik<PERSON>ar in tabelweergave", "tableOnly#description": ""}}, "knowledge": {"title": "Kennisbank", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Gesloten", "closed#description": "", "description": "Status taak", "description#description": "", "open": "Open", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Geschatte veldstatistieken", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Ons model maakt gebruik van experimentele gewasgegevens die onnauwkeurigheden kunnen bevatten. We werken voortdurend aan het verbeteren van de betrouwbaarheid ervan.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Cijfers ivm prestaties en machines", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Sleep bestanden hi<PERSON><PERSON>n van<PERSON> (of elders van<PERSON><PERSON>)", "drop#description": "", "file#description": "", "file_one": "bestand", "file_other": "bestanden", "ingestDescription": "Carbon-medewerkers moeten Ingest gebruiken", "ingestDescription#description": "", "ingestLink": "Uploaden naar Ingest", "ingestLink#description": "", "select": "Bestanden selecteren", "select#description": "", "title": "Uploaden", "title#description": "", "upload": "Uploaden naar Carbon", "upload#description": "", "uploading": "{{subject}} uploaden...", "uploading#description": ""}, "reports": {"explore": {"graph": "Grafische weergave", "graph#description": "", "groupBy": "<PERSON><PERSON><PERSON><PERSON> per", "groupBy#description": "", "title": "Verkennen", "title#description": ""}, "scheduled": {"authorCarbonBot": "Carbon Bot", "authorCarbonBot#description": "", "authorUnknown": "Onbekende auteur", "authorUnknown#description": "", "automation": {"customerReports": "Klantrapport", "customerReports#description": "", "errorTitle": "Ongeldig geautomatiseerd rapport", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "<PERSON><PERSON> k<PERSON> gese<PERSON>d", "none#description": ""}}, "reportDay": {"errors": {"none": "<PERSON><PERSON> dag gese<PERSON>eerd", "none#description": ""}, "name": "Rapport op dag", "name#description": ""}, "reportEmails": {"errors": {"none": "<PERSON>n e-mails toe<PERSON><PERSON><PERSON>", "none#description": ""}, "name": "E-mails van klant", "name#description": ""}, "reportHour": {"errors": {"none": "<PERSON><PERSON> tijd g<PERSON>d", "none#description": ""}, "name": "Rapport om (tijd)", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON>n terugkijktijd gedefinieerd", "none#description": ""}, "name": "Terugkijktijd van rapport", "name#description": ""}, "reportTimezone": {"errors": {"none": "<PERSON><PERSON> tij<PERSON><PERSON> geselecteerd", "none#description": ""}, "name": "Tijdzone van rapport", "name#description": ""}, "warningDescription": "Het wordt elke {{day}} om {{hour}} in {{timezone}} met een <PERSON><PERSON><PERSON><PERSON>j<PERSON> van {{lookback}} dagen aangemaakt voor alle actieve {{customer}} robots.", "warningDescription#description": "", "warningTitle": "Dit is een geautomatiseerd rapport!", "warningTitle#description": ""}, "byline": "Door {{author}}", "byline#description": "", "editor": {"columnsHidden": "Verborgen kolommen", "columnsHidden#description": "", "columnsVisible": "<PERSON><PERSON><PERSON><PERSON><PERSON> kolommen", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_one": "Waarschuwing: er is een ander rapport met deze naam", "duplicateNames_other": "Waarschuwing: er zijn {{count}} andere rapporten met deze naam", "fields": {"automateWeekly": "Wekelijks automatiseren", "automateWeekly#description": "", "name": "Titel rapport", "name#description": "", "showAverages": "Gemiddelde waarden tonen", "showAverages#description": "", "showTotals": "<PERSON>en tonen", "showTotals#description": ""}}, "errors": {"noReport": "Rapport bestaat niet of je hebt geen toegang", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} wordt definitief verwijderd.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Je hebt geen bevo<PERSON>dh<PERSON>d om {{subject}} te verwijderen.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-mail wordt niet nog eens verzonden", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Rapport wordt verzonden aan deze e-mails", "publishEmailsHelperNew#description": ""}, "runAgain": "Nog eens uitvoeren", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Selecteer een of meer kolommen", "noColumns#description": "", "noEndDate": "Selecteer einddatum", "noEndDate#description": "", "noRobots": "Selecteer een of meer robots", "noRobots#description": "", "noStartDate": "Selecteer begindatum", "noStartDate#description": ""}, "fields": {"average": "<PERSON><PERSON><PERSON><PERSON><PERSON> waarde", "average#description": "", "averageShort": "<PERSON><PERSON><PERSON><PERSON>.", "averageShort#description": "", "date": "Datum", "date#description": "", "group": "Serienummer/datum", "group#description": "", "groupJob": "Serienummer/Taak", "groupJob#description": "", "mixed": "(G<PERSON>engd)", "mixed#description": "", "total": "Totale waarde", "total#description": "", "totalShort": "SOM", "totalShort#description": ""}, "unknownReport": "Onbekend rapport", "unknownReport#description": ""}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "toLine": "voor {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "Alle meetbare grootheden", "all#description": "", "select": "<PERSON><PERSON><PERSON> grootheden selecteren", "select#description": ""}, "robotsLabel": {"all": "Alle robots", "all#description": "", "none": "Geen robots", "none#description": "", "select": "Selecteer robots", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "gebruikersnaam en paswoord", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "onbekende provider", "unknown#description": ""}, "cards": {"account": "Account", "account#description": "", "advanced": "Geavanceerd", "advanced#description": "", "localization": "Lokalisatie", "localization#description": ""}, "delete": {"deleteAccount": "Account verwij<PERSON>en", "deleteAccount#description": "", "dialog": {"description": "LET OP! Deze actie kan niet ongedaan worden gemaakt. Alle gegevens gaan verloren.", "description#description": ""}}, "fields": {"language": "Taal", "language#description": "", "measurement": {"name": "Meeteenheden", "name#description": "", "values": {"imperial": "Imperiaal (in, mph, acres, fahrenheit)", "imperial#description": "", "metric": "Metrisch (mm, km/u, hectare, celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Afmelden", "logOut#description": "", "title": "Instellingen", "title#description": "", "version": "Carbon Ops Center versie {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON><PERSON> bestaat niet of u hebt geen toestemming om deze te bekijken.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "<PERSON><PERSON><PERSON><PERSON><PERSON> beheren in Auth0", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Rol en machtigingen", "title#description": ""}, "profile": {"title": "<PERSON><PERSON>", "title#description": ""}}, "toggleable": {"contractors": "<PERSON><PERSON><PERSON>", "contractors#description": ""}}}}