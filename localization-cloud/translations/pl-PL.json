{"components": {"AlarmTable": {"export": "Historia alarmów dla {{robots}} na {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Mapy ciepła są dostępne bezpłatnie podczas okresu próbnego, ale mogą ulec zmianie, usunięciu lub wymagać aktualizacji planu w dowolnym momencie. Dane powinny zostać zweryfikowane w sposób niezależny", "description#description": "", "title": "Dane beta dla mapy ciepła", "title#description": ""}, "tooltip": "Ta funkcja jest w fazie oceny i może zostać zmieniona lub usunięta w dowolnym momencie", "tooltip#description": ""}, "Chat": {"errors": {"failed": "<PERSON><PERSON> udało się załadować czatu: {{message}}", "failed#description": ""}, "machineTranslated": "Przetłumaczone maszynowo", "machineTranslated#description": "", "machineTranslatedFrom": "Tłumaczenie maszynowe z {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Ta wiadomość została skasowana.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} zost<PERSON>e trwale usunięty.", "description#description": "", "descriptionActive": "{{subject}} jest a<PERSON><PERSON><PERSON><PERSON>, więc nie można go usunąć.", "descriptionActive#description": ""}, "title": "<PERSON><PERSON><PERSON> pewien?", "title#description": ""}, "CopyToClipboardButton": {"click": "<PERSON><PERSON><PERSON><PERSON>, aby sko<PERSON>ć", "click#description": "", "copied": "Skopiowano!", "copied#description": ""}, "CropEditor": {"failed": "<PERSON>e udało się wgrać edytora upraw", "failed#description": "", "viewIn": "Widok w Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear#description": "", "endDate": "Data końcowa", "endDate#description": "", "error": "Błąd selektora zakresu dat", "error#description": "", "invalid": "Nieważny", "invalid#description": "", "last7days": "Zeszłe 7 dni", "last7days#description": "", "lastMonth": "Zeszły miesiąc", "lastMonth#description": "", "lastWeek": "Zeszły tydzień", "lastWeek#description": "", "minusDays": "{{days}} dni temu", "minusDays#description": "", "plusDays": "w ciągu {{days}} dni.", "plusDays#description": "", "startDate": "Data rozpoczęcia", "startDate#description": "", "thisMonth": "Ostatni <PERSON>", "thisMonth#description": "", "thisWeek": "Ostatni tydzień", "thisWeek#description": "", "today": "<PERSON><PERSON><PERSON><PERSON>", "today#description": "", "tomorrow": "<PERSON><PERSON>", "tomorrow#description": "", "yesterday": "<PERSON><PERSON><PERSON><PERSON>", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieoczekiwany błąd", "error#description": "", "queryLimitReached": "Renderowanie częściowego zestawu danych, ponieważ zwrócono zbyt dużo danych. Skontaktuj się ze wsparciem technicznym, aby uzyskać pomoc", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Co się stało?", "comment#description": "", "feedback": "Informacja zwrotna", "feedback#description": "", "submit": "Przekaż i odśwież", "submit#description": ""}, "GdprConsent": {"description": "Sprawdź i zaakceptuj, aby kontynuować", "description#description": "", "statement": "Zobowiązuję się przestrzegać <0>Warunków korzystania</0> oraz <1>Polityki prywatności</1>", "statement#description": "", "title": "Warunki korzystania i Polityka prywatności", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Wymagany klient", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} – {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} – {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Przełącz to menu", "help#description": "", "title": "Skr<PERSON><PERSON>", "title#description": ""}, "LaserTable": {"export": "{{robots}} <PERSON><PERSON> {{date}}", "export#description": "", "installedOnly": "<PERSON><PERSON><PERSON>", "installedOnly#description": "", "warnings": {"duplicate": "Ten robot ma kilka laserów zarejestrowanych w następujących gniazdach: {{slots}}", "duplicate#description": "", "emptySlot": "Ten robot nie ma lasera zarejestrowanego w następujących gniazdach: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Nowy kod", "new#description": ""}, "Loading": {"failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie udało się załadować Carbon Ops Center.", "failed#description": "", "placeholder": "W<PERSON><PERSON><PERSON><PERSON>...", "placeholder#description": ""}, "ModelName": {"warning": "Ostrzeżenie: model o ni<PERSON><PERSON> w<PERSON>go<PERSON>", "warning#description": ""}, "PendingActivationOverlay": {"description": "Jesteśmy w trakcie aktywacji Twojego konta. Po zakończeniu aktywacji otrzymasz od nas e-mail!", "description#description": "", "errors": {"carbon": {"description": "Wykryto e-mail od Carbon, której nie udało się  zweryfikować z powodu loginu/hasła  użytkownika. Wyloguj się i użyj opcji \"Zaloguj się przez Google\", aby aktywować się automatycznie.", "description#description": "", "title": "Niezweryfikowane konto Carbon", "title#description": ""}}, "hi": "<PERSON><PERSON><PERSON> do<PERSON>ry, {{name}}!", "hi#description": "", "logOut": "Zalogowałeś(aś) się na niewłaściwe konto? <0>Wyloguj się</0>.", "logOut#description": "", "title": "W oczekiwaniu na aktywację", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON><PERSON><PERSON>j", "more#description": ""}, "RobotImplementationSelector": {"status": "Status wdrożenia", "status#description": "", "title": "Zmień status wdrożenia", "title#description": "", "warning": "Zmiana statusu wdrożenia może wywołać zautomatyzowane przepływy pracy wpływające na obsługę klienta. NIE RÓB TEGO, JEŻELI NIE JESTEŚ PEWIEN!", "warning#description": ""}, "ShowLabelsButton": {"text": "Etykiety", "text#description": "", "tooltip": "Pokaż etykiety", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Pokaż metadane", "tooltip#description": ""}, "almanac": {"crops": {"new": "<PERSON><PERSON><PERSON> nową uprawę", "new#description": "", "none": "Brak kategorii upraw", "none#description": "", "sync#description": ""}, "cropsSynced": "Wyszystkie uprawy", "cropsSynced#description": "", "delete": {"description": "Tej operacji nie można <PERSON>", "description#description": ""}, "discard": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON> w {{title}}?", "description#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>?", "title#description": ""}, "fineTuneDescription": "Domyślnie 5, może zmniejszyć lub zwi<PERSON><PERSON><PERSON><PERSON> czas działania lasera o ~20% na każdy postęp.", "fineTuneDescription#description": "", "fineTuneTitle": "Mnożnik Fine Tune", "fineTuneTitle#description": "", "formulas": {"all": "Wszystkie rozmiary", "all#description": "", "copyFormula": "Formuła Kopiuj", "copyFormula#description": "", "copySize": "<PERSON><PERSON><PERSON><PERSON>", "copySize#description": "", "exponent": {"description": "Zwiększa promień w mm do tego wykładnika", "description#description": "", "label": "Wykładnik (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Liczba od 1 do 10, domyślnie 5, może zmniejszyć lub z<PERSON><PERSON><PERSON><PERSON> czas działania lasera o ~20% na postęp. Ta liczba jest stosowana w trybie podstawowym", "description#description": "", "label": "<PERSON><PERSON><PERSON> (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Ogólny mnożnik dla zwiększania/zmniejszania indeksu Fine Tune", "description#description": "", "label": "Wartość mnożnika Fine Tune (FM)", "label#description": ""}, "laserTime": "<PERSON>zas zniszczenia przez laser", "laserTime#description": "", "maxTime": {"description": "Górny limit czasu niszczenia w milisek.", "description#description": "", "label": "<PERSON>zas ma<PERSON>ymalny", "label#description": ""}, "multiplier": {"description": "pomnożony przez promień wyrażony w mm", "description#description": "", "label": "M<PERSON>żnik (A)", "label#description": ""}, "offset": {"description": "Licz<PERSON> mi<PERSON>, które należy dodać niezależnie od promienia", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "<PERSON><PERSON><PERSON>", "pasteFormula#description": "", "pasteSize": "<PERSON><PERSON><PERSON>", "pasteSize#description": "", "sync": "Zsynchronizuj wszystkie rozmiary", "sync#description": "", "thresholds": "Rozmiary progów", "thresholds#description": "", "title": "Form<PERSON>ł<PERSON>", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Przełącz na Tryb Zaawansowany", "switchModeAdvanced#description": "", "switchModeBasic": "Przełącz na Tryb Podstawowy", "switchModeBasic#description": "", "warnings": {"admin": "Zmodyfikowanie tego almanachu spowoduje synchronizację ze wszystkimi obecnymi i przyszłymi jednostkami produkcyjnymi.", "admin#description": "", "carbon": "To jest almanach dostarczony przez Carbon Zmiany wolno wprowadzić tylko do ideksu Fine Tune.", "carbon#description": "", "production": "Ten almanach został uruchomiony w robocie. Jego edytowanie wywoła zmiany w pracy robota.", "production#description": ""}, "weeds": {"new": "<PERSON><PERSON><PERSON> ch<PERSON>ta", "new#description": "", "none": "Nowe kategorie chwastów", "none#description": "", "sync#description": ""}, "weedsSynced": "Wszystkie chwasty", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} zapisano. Aktywuj za pomocą aplikacji operatora Quick Tune", "savedLong#description": "", "testResults": "Podgląd wyników", "testResults#description": ""}, "filters": {"capturedAt": "Data przechwycenia", "capturedAt#description": "", "diameter": "Średnica", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "Wszystkie obrazy", "allImages#description": "", "categorized": "Skategoryzowane", "categorized#description": "", "scrollToTop": "Powrót do początku", "scrollToTop#description": "", "sortBy": {"latest": "<PERSON><PERSON><PERSON><PERSON>", "latest#description": ""}, "sortedBy": "Sort<PERSON>nie według: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Błąd pobierania statusu", "error#description": "", "ready": "Wyniki są gotowe", "ready#description": "", "session#description": "", "session_few": "<PERSON><PERSON><PERSON>", "session_many": "<PERSON><PERSON><PERSON>", "session_one": "<PERSON><PERSON><PERSON>", "session_other": "<PERSON><PERSON><PERSON>", "showResults": "Pokaż wyniki", "showResults#description": "", "status": "przetworzone {{processed}} / {{total}} wyniki", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Modyfikacja tego profilu rośliny spowoduje synchronizację ze wszystkimi obecnymi i przyszłymi jednostkami produkcyjnymi.", "admin#description": "", "adminMeta": "Wszystkie profile administratora będą dostępne dla wszystkich klientów. Nie twórz bałaganu!", "adminMeta#description": "", "production": "Ten profil roślin jest aktywnie uruchomiony na robocie. Operator zostanie powiadomiony o aktualizacjach i będzie mógł zdecydować o zastosowaniu najnowszych zmian.", "production#description": "", "protected": "To jest profil wykonany z węgla. Niczego nie można zmienić.", "protected#description": "", "unsavedChanges": "Niezapisane zmiany. Naciś<PERSON>j zapisz, aby zastoso<PERSON>ć zmiany.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_few": "Zmienione wartości kluczowe", "changedKey_many": "Zmienione wartości kluczowe", "changedKey_one": "Zmieniona wartość kluczowa", "changedKey_other": "Zmienione wartości kluczowe", "newKey": "nazwa nowej {{key}}", "newKey#description": "", "stringReqs": "<PERSON><PERSON><PERSON> trylko a-z, 0-9, ., oraz _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Ta wartość kluczowa została dodana oprócz domyślnych.", "description#description": ""}, "keyMissing": {"description": "<PERSON><PERSON> war<PERSON> domyślnej(ych): {{keys}}", "description#description": ""}, "valueChanged": {"description": "Ta wartośc została zmieniona ze swojej wartości domyślnej ({{default}})", "description#description": "", "title": "Konfiguracja została zmieniona", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "<PERSON><PERSON> udało się wgrać edytora klienta", "load#description": ""}}, "CustomerSelector": {"empty": "<PERSON><PERSON>rz<PERSON>", "empty#description": "", "title": "Zmień klienta", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON><PERSON>", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "ignorable": {"description": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> c<PERSON> pozwala - nie jest to uwzględniane w zaleceniach dotyczących prędkości.", "description#description": "", "label": "Ignorowalne", "label#description": ""}, "paste": "<PERSON><PERSON><PERSON> konfigu<PERSON>", "paste#description": ""}, "warnings": {"production": "Ta cecha rozróżniająca jest aktywna w robocie. Jego edytowanie natychmiast wywoła skutek w pracy robota.", "production#description": ""}}, "drawer": {"customerMode": "<PERSON><PERSON>", "customerMode#description": "", "error": "<PERSON>e udało się wgrać nawigacji", "error#description": ""}, "filters": {"NumericalRange": {"max": "Maks. ({{units}})", "max#description": "", "min": "Min ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "<PERSON>e udało się wgrać nagłówka", "failed#description": "", "mascot": "<PERSON><PERSON><PERSON> maskotka Carbon Robotics", "mascot#description": "", "search": {"failed": "<PERSON><PERSON> udało się wgrać wyszukiwania", "failed#description": "", "focus": "Fokus na wyszukiwanie", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "<PERSON><PERSON><PERSON>", "label#description": "", "larger": "w<PERSON><PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "mniejsze", "smaller#description": ""}}, "map": {"bounds": {"reset": "Zresetuj widok", "reset#description": ""}, "errors": {"empty": "Brak zgłoszonych danych o lokalizacji", "empty#description": "", "failed": "Nie udało się wgrać mapy", "failed#description": ""}, "filters": {"customer_office": "Biuro klienta", "customer_office#description": "", "hq": "Centrala Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Skrytka pocztowa", "po_box#description": "", "shop": "Warsztat", "shop#description": "", "storage": "Magazyn", "storage#description": "", "support_base": "<PERSON><PERSON>", "support_base#description": ""}, "fullscreen": "Pełny ekran", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Błąd warstwy legendy", "legend#description": "", "notThinning": "BRAK PRZERZEDZANIA", "notThinning#description": "", "notWeeding": "BRAK ODCHWASZCZANIA", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "BŁĄD MAPY CIEPŁA", "unknown#description": ""}, "fields": {"block": "Lokalizacja: {{block}}", "block#description": "", "location": "Lokalizacja: {{latitude}}, {{longitude}}", "location#description": "", "size": "<PERSON><PERSON><PERSON><PERSON>ć: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Warstwy", "name#description": "", "rangeType#description": "", "relative": "Względny zakres wykorzystania", "relative#description": "", "relativeRange#description": ""}, "map": "Mapa", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "<PERSON> jaki<PERSON> kategor<PERSON> skopiować?", "copyFromWhich#description": "", "splitCrops": "Podziel plony", "splitCrops#description": "", "splitWeeds": "<PERSON><PERSON><PERSON><PERSON> chwasty", "splitWeeds#description": "", "syncCrops": "Zsynchronizuj wszystkie plony", "syncCrops#description": "", "syncWeeds": "Zsynchronizuj wszystkie chwasty", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Przepowiada próg pewności co do zastosowania detekcji w dynamicznym pasmowaniu", "description#description": "", "label": "Próg p<PERSON>", "label#description": ""}, "minDoo": {"description": "Minimalna detekcja w przypadku możliwości", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Przepowiada próg pewności co do zastosowania detekcji w przerzedzaniu", "description#description": "", "label": "Próg przerzedzania", "label#description": ""}, "weed": {"description": "Przepowiada próg pewności co do zastosowania detekcji w odwróconej ochronie upraw", "description#description": "", "label": "Próg odwróconej ochrony upraw", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Przepowiada próg pewności co do zastosowania detekcji w ochronie upraw", "description#description": "", "label": "<PERSON>r<PERSON><PERSON> ochrony upraw", "label#description": ""}, "weed": {"description": "Przepowiada próg pewności co do uznania za chwast", "description#description": "", "label": "Próg odchwaszczania", "label#description": ""}}}, "errors": {"sync": "Ustawienia dla tego modelu nie zostały jeszcze zsynchronizowane z LaserWeeder. Zaczekaj na synchronizację, aby wyświetlić i zaktualizować ustawienia.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Podziel rozmiary", "splitSizesLong#description": "", "splitSizesShort": "Podziel", "splitSizesShort#description": "", "syncSizesLong": "Zsynchroniz<PERSON><PERSON>", "syncSizesLong#description": "", "syncSizesShort": "Zsynchronizuj", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Ostrzeżenie:{{stopEmphasis}} Te ustawienia obejmują niezapisane zmiany, które nie są widoczne w robocie.", "exportingUnsavedChanges#description": "", "production": "Ten model jest teraz aktywny w robocie. Jego edytowanie natychmiast wywoła skutek w pracy robota.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "<PERSON><PERSON><PERSON>", "unknown#description": ""}, "almanac": {"unknown": "Almanach nieznany", "unknown#description": "", "withName": "Almanach: {{name}}", "withName#description": ""}, "autofixing": "Samonaprawa błędu", "autofixing#description": "", "banding": {"disabled": "Pasmowanie wyłączone", "disabled#description": "", "enabled": "Pasmowanie włączone", "enabled#description": "", "none": "Brak pasmowania", "none#description": "", "static": "(STATYCZNE)", "static#description": "", "withName": "Pasmowanie: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Nie udało się załadować statusu połączenia z systemem", "failed#description": "", "never": "Robot nie połączył się z systemem", "never#description": "", "withTime": "Robot połączył się z systemem o {{time}}.", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Uprawy Włączone ({{pinned}} przypięte)", "summary#description": ""}, "delivery": "Robot w trakcie dostawy do klienta.", "delivery#description": "", "disconnected": "Robot odłączony od systemu", "disconnected#description": "", "discriminator": {"unknown": "<PERSON>cha rozróżniająca nieznana", "unknown#description": "", "withName": "<PERSON><PERSON> rozróżniająca: {{name}}", "withName#description": ""}, "failed": "<PERSON>e udało się wgrać statusu robota", "failed#description": "", "failedShort": "<PERSON><PERSON>", "failedShort#description": "", "implementation": "Wdrożenie", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Zapasy", "inventory#description": "", "job": {"none": "Brak zadania", "none#description": "", "withName": "Zadanie: {{name}}", "withName#description": ""}, "lasers": "Lasery Online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Okres życia", "lifetime#description": "", "lifted": "Standby (podniesiony)", "lifted#description": "", "loading": "Ładuje się", "loading#description": "", "location": {"known": "Lokalizacja: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Lokali<PERSON><PERSON>", "unknown#description": ""}, "manufacturing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manufacturing#description": "", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Ładowanie modelu", "modelLoading#description": "", "notArmed": "<PERSON><PERSON>", "notArmed#description": "", "off_season": "Poza sezonem", "off_season#description": "", "offline": "W trybie offline od {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P nieznana", "unknown#description": ""}, "poweringDown": "Wyłączanie zasilania", "poweringDown#description": "", "poweringUp": "Włączanie zasilania", "poweringUp#description": "", "pre_manufacturing": "Produkcja wstępna", "pre_manufacturing#description": "", "stale": "Zestarzały", "stale#description": "", "staleDescription": "Ostan<PERSON> z<PERSON>. Robot jest w trybie offline.", "staleDescription#description": "", "standby": "Standby", "standby#description": "", "thinning": {"disabled": "Przerzedzanie wyłączone", "disabled#description": "", "enabled": "Przerzedzanie włączone", "enabled#description": "", "none": "Brak przerzedzania", "none#description": "", "withName": "Przerzedzanie: {{name}}", "withName#description": ""}, "today": {"none": "Brak odchwaszczania dzisiaj", "none#description": ""}, "unknown": "Status nieznany", "unknown#description": "", "updating": "Instalowanie aktualizacji", "updating#description": "", "version": {"values": {"unknown": "<PERSON><PERSON><PERSON>", "unknown#description": "", "updateDownloading": "({{version}} wg<PERSON>wa się)", "updateDownloading#description": "", "updateReady": "({{version}} gotowa)", "updateReady#description": ""}}, "weeding": "Odchwaszczanie {{crop}}", "weeding#description": "", "weedingDisabled": "Odchwaszczanie wyłączone", "weedingDisabled#description": "", "weedingThinning": "Odchwaszczanie i Przerzedzanie {{crop}}", "weedingThinning#description": "", "winterized": "<PERSON><PERSON><PERSON><PERSON>", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "<PERSON><PERSON>", "exists#description": "", "unknownClass": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a <PERSON>a", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "<PERSON><PERSON> tw<PERSON><PERSON> nowej konfigurac<PERSON>", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "Szablon {{class}}", "templateForClass#description": "", "templateGeneric": "Szablon robota", "templateGeneric#description": "", "warnings": {"ignoreConfig": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, gdy konfiguracja dla{{serial}} już istnieje lub planujesz ją utworzyć manualnie.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Zaawansowane ustawienia prędkościomierza", "advancedFormulaTitle#description": "", "formulaTitle": "Wzór", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Automatycznie zmniejsza sugerowaną prędkość o wprowadzoną wartość. <PERSON> przykład, j<PERSON><PERSON><PERSON> wprow<PERSON><PERSON><PERSON> 5%, sugerowa<PERSON> prędko<PERSON>ć 1 mph zostanie zmniejszona do 0,95 mph.", "description#description": "", "label": "Przesunięcie prędkości", "label#description": ""}, "decreaseSmoothing": {"description": "Dostosuj tempo zmniejszania prędkości. Im wy<PERSON><PERSON>, tym więks<PERSON> prawdopodobieństwo wahań prędkościomierza", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zmniejszania prędkości", "label#description": ""}, "increaseSmoothing": {"description": "Dostosuj tempo zwiększania prędkości. Im wy<PERSON><PERSON>, tym więks<PERSON> prawdopodobieństwo wahań prędkościomierza", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć zwiększania prędkości", "label#description": ""}, "maxVelMph": {"description": "Wprowadź maksymalną najwyższą pręd<PERSON><PERSON>, z jaką chcesz się poruszać. Zalecenia dotyczące prędkości nie będą przekraczać tej wartości", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maksymalna", "label#description": ""}, "minVelMph": {"description": "Wprowadź minimalną najniższą pręd<PERSON><PERSON>, z jaką chcesz się poruszać. Zalecenia dotyczące prędkości nie spadną poniżej tej wartości", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Ta wartość oznacza docelową wielko<PERSON> (%) chwastów, jakie ch<PERSON>z wye<PERSON>ć.", "description#description": "", "label": "Docelowa wielkość odchwaszczenia", "label#description": ""}, "primaryRange": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>ę <PERSON>, je<PERSON><PERSON> ch<PERSON>z osiągnąć pożądane przez Ciebie tempo odchwaszczania niezależnie od pręd<PERSON>ści.", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "rows": {"allRows": "Wszystkie rzędy", "allRows#description": "", "row1": "Rząd 1", "row1#description": "", "row2": "Rząd 2", "row2#description": "", "row3": "Rząd 3", "row3#description": ""}, "secondaryKillRate": {"description": "Ta wartość oznacza najmniejszą (%) wiel<PERSON>ś<PERSON> chwastów, jaki<PERSON> ch<PERSON>z wyeliminować.", "description#description": "", "label": "Minimalna wielkość odchwaszczenia", "label#description": ""}, "secondaryRange": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>ę <PERSON>, je<PERSON><PERSON> ch<PERSON>z mieć więcej luzu, zani<PERSON> otrzymasz informację o niskiej pręd<PERSON>.", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "sync": "Synchronizuj wszystkie wiersze", "sync#description": "", "warnings": {"admin": "Modyfikacja tego estymatora prędkości spowoduje synchronizację ze wszystkimi obecnymi i przyszłymi jednostkami produkcyjnymi.", "admin#description": "", "production": "Ten estymator pręd<PERSON>ści jest uruchommiony w robocie. <PERSON>go edytowanie będzie mieć natychmiastowy skutek w pracy robota.", "production#description": "", "protected": "Jest to profil typu carbon provided. Nic w nim nie można zmienić.", "protected#description": "", "unsavedChanges": "Zmiany niezapisane. <PERSON><PERSON><PERSON><PERSON><PERSON>, aby z<PERSON><PERSON><PERSON> zmian<PERSON>.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Stop<PERSON>wo", "gradual#description": "", "immediate": "Natychmiast", "immediate#description": ""}, "visualization": {"targetSpeed": "Docelowa prędkość", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_few": "alarmy", "alarm_many": "alarmy", "alarm_one": "alarm", "alarm_other": "alarmy", "fields": {"code": "Kod", "code#description": "", "description": "Opis", "description#description": "", "duration": {"name": "Czas trwania", "name#description": "", "values": {"ongoing": "W TOKU", "ongoing#description": ""}}, "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identifier#description": "", "impact": {"name": "Impakt", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Poziom", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_few": "almanachy", "almanac_many": "almanachy", "almanac_one": "almanach", "almanac_other": "almanachy", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_few": "zadania", "assignment_many": "zadania", "assignment_one": "zadanie", "assignment_other": "zadania", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instrukcje", "instructions#description": ""}, "intervention#description": "", "intervention_few": "", "intervention_many": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "<PERSON><PERSON> Weed", "laserWeed#description": "", "unrecognized": "<PERSON><PERSON><PERSON><PERSON> rod<PERSON> ({{value}})", "unrecognized#description": ""}, "job_few": "", "job_many": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Laser Weed do rzędów upraw", "laserWeedRow#description": ""}, "objective_few": "cele", "objective_many": "cele", "objective_one": "cel", "objective_other": "cele", "states": {"acknowledged": "zatwierdzony", "acknowledged#description": "", "cancelled": "an<PERSON><PERSON><PERSON>", "cancelled#description": "", "completed": "zakończony", "completed#description": "", "failed": "ni<PERSON><PERSON><PERSON>", "failed#description": "", "inProgress": "w toku", "inProgress#description": "", "new": "nowy", "new#description": "", "paused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paused#description": "", "pending": "oczekkują<PERSON>", "pending#description": "", "ready": "gotowy", "ready#description": "", "unrecognized": "nieznany stan ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "<PERSON>adanie nr{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_few": "zadania", "task_many": "zadania", "task_one": "zadanie", "task_other": "zadania"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_few": "profile r<PERSON><PERSON><PERSON>", "categoryCollectionProfile_many": "profile r<PERSON><PERSON><PERSON>", "categoryCollectionProfile_one": "profil r<PERSON><PERSON><PERSON><PERSON>", "categoryCollectionProfile_other": "profile r<PERSON><PERSON><PERSON>", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disregard#description": "", "name": "<PERSON><PERSON><PERSON>", "name#description": "", "requiredBaseCategories": "Mu<PERSON>z mieć dokładnie te kategorie: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Zak<PERSON>ali<PERSON>wan<PERSON>", "updatedAt#description": ""}, "metadata": {"capturedAt": "<PERSON><PERSON><PERSON><PERSON>", "capturedAt#description": "", "categoryId": "Identyfikat<PERSON>ii", "categoryId#description": "", "imageId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "Identyfikat<PERSON>", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "promień", "radius#description": "", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_few": "konfiguracje", "config_many": "konfiguracje", "config_one": "konfiguracja", "config_other": "konfiguracje", "key#description": "", "key_few": "klucze", "key_many": "klucze", "key_one": "klucz", "key_other": "klucze", "template#description": "", "template_few": "s<PERSON><PERSON><PERSON><PERSON> k<PERSON>gu<PERSON>ji", "template_many": "s<PERSON><PERSON><PERSON><PERSON> k<PERSON>gu<PERSON>ji", "template_one": "sza<PERSON><PERSON> konfiguracji", "template_other": "s<PERSON><PERSON><PERSON><PERSON> k<PERSON>gu<PERSON>ji", "value#description": "", "value_few": "<PERSON><PERSON><PERSON><PERSON>", "value_many": "<PERSON><PERSON><PERSON><PERSON>", "value_one": "<PERSON><PERSON><PERSON><PERSON>", "value_other": "<PERSON><PERSON><PERSON><PERSON>"}, "crops": {"categories": {"unknown": "<PERSON>lon nieznany", "unknown#description": ""}, "crop#description": "", "crop_few": "plony", "crop_many": "plony", "crop_one": "plon", "crop_other": "plony", "fields": {"confidence": {"fields": {"regionalImages": "Obrazy regionalne:", "regionalImages#description": "", "totalImages": "Wszystkie obrazy:", "totalImages#description": ""}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Zarchiwizowane", "archived#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> niez<PERSON>a", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Notatki", "notes#description": "", "pinned": "Przypię<PERSON>", "pinned#description": "", "recommended": "Zalecany", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_few": "<PERSON><PERSON><PERSON><PERSON>", "customer_many": "<PERSON><PERSON><PERSON><PERSON>", "customer_one": "klient", "customer_other": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"emails": {"errors": {"formatting": "Każdy e-mail w osobnym wierszu", "formatting#description": ""}, "name": "e-maile", "name#description": ""}, "featureFlags": {"almanac": {"description": "Włącza zakładki Almanach i Cecha Rozróżniająca dla robotów (robot musi również obsługiwać Almanach i Cechę Rozróżniającą).", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Włącza kartę Profil rośliny dla robotów", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Flagi funkcji umożliwiają wszystkim użytkownikom klienta korzystanie z funkcji w wersji beta.", "description#description": "", "jobs": {"description": "Włącza Zadania (robot musi również obsługiwać zadania)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Pokazuje nowy wygląd metryk robota.", "description#description": "", "name": "Przeprojektowanie metryk", "name#description": ""}, "name": "<PERSON><PERSON>", "name#description": "", "off": "OFF", "off#description": "", "on": "ON", "on#description": "", "reports": {"description": "Włącza kartę Raporty i funkcje", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Wyświet<PERSON> dane prz<PERSON>rzenne, w tym mapy cieplne i wykresy", "description#description": "", "name": "<PERSON> p<PERSON>", "name#description": ""}, "summary": "{{enabled}}/{{total}} Flagi Funkcji włączone", "summary#description": "", "unvalidatedMetrics": {"description": "Wyświetl metryki w wersji beta oczekujące na walidację pola w certyfikowanych metrykach", "description#description": "", "name": "Metry<PERSON> w wersji beta", "name#description": ""}, "velocityEstimator": {"description": "Umożliwia przeglądanie i edytowanie profili docelowego estymatora prędkości (robot musi również obsługiwać docelowy estymator pręd<PERSON><PERSON>).", "description#description": "", "name": "Docelowy estymator prę<PERSON><PERSON>", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Wygenerowano dnia", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "<PERSON><PERSON><PERSON> opcja ta jest włącz<PERSON>, raporty będą generowane co tydzień z następującymi ustawieniami dla wszystkich aktywnych robotów.", "description#description": "", "name": "<PERSON><PERSON><PERSON> tygo<PERSON>", "name#description": ""}, "weeklyReportHour": "Wygenerowano o godz.", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Status od dnia", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Wygenerowano w strefie czasowej", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_few": "cechy umożliwiające rozróżnienie", "discriminator_many": "cechy umożliwiające rozróżnienie", "discriminator_one": "cecha umożliwiająca rozróżnienie", "discriminator_other": "cechy umożliwiające rozróżnienie", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_few": "gospodarstwa", "farm_many": "gospodarstwa", "farm_one": "<PERSON><PERSON><PERSON>rst<PERSON>", "farm_other": "gospodarstwa", "obstacle#description": "", "obstacle_few": "", "obstacle_many": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_few": "punkty", "point_many": "punkty", "point_one": "punkt", "point_other": "punkty", "zone#description": "", "zone_few": "strefy", "zone_many": "strefy", "zone_one": "strefa", "zone_other": "strefy"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_few": "defini<PERSON><PERSON> pól", "fieldDefinition_many": "defini<PERSON><PERSON> pól", "fieldDefinition_one": "definicja pola", "fieldDefinition_other": "defini<PERSON><PERSON> pól", "fields": {"boundary": "Granica pola", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Kierunek sadzenia", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_few": "wartości globalne", "global_many": "wartości globalne", "global_one": "<PERSON><PERSON><PERSON><PERSON>na", "global_other": "wartości globalne", "values": {"plantProfileModelId": {"description": "Model bazowy używany przez wszystkie profile klientów i administratorów dla '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) ID  Modelu", "label#description": ""}}}, "images": {"fields": {"camera": "Aparat", "camera#description": "", "capturedAt": "Data / godzina", "capturedAt#description": "", "geoJson": "Lokalizacja", "geoJson#description": "", "url": "Otwórz obraz", "url#description": ""}, "image#description": "", "image_few": "obrazy", "image_many": "obrazy", "image_one": "obraz", "image_other": "obrazy"}, "jobs": {"job#description": "", "job_few": "Zadania", "job_many": "Zadania", "job_one": "<PERSON><PERSON><PERSON>", "job_other": "Zadania"}, "lasers": {"fields": {"cameraId": "ID aparatu", "cameraId#description": "", "error": {"values": {"false": "Nominalny", "false#description": ""}}, "installedAt": "Zainstalowany", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Nr seryjny nieznany", "unknown#description": ""}}, "lifetimeSec": "<PERSON><PERSON> pracy", "lifetimeSec#description": "", "powerLevel": "<PERSON><PERSON><PERSON>", "powerLevel#description": "", "removedAt": "Zdjęty dnia", "removedAt#description": "", "rowNumber": "Rząd", "rowNumber#description": "", "totalFireCount": "Liczba odpaleń", "totalFireCount#description": "", "totalFireTimeMs": "Czas trwania odpaleń", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "<PERSON><PERSON>gas<PERSON>", "expired#description": "", "hours": "Godziny: {{installed}}/{{total}} ({{percent}} pozostało)", "hours#description": "", "hoursUnknown": "Liczba godzin: nieznana", "hoursUnknown#description": "", "months": "Miesiące: {{installed}}/{{total}} ({{percent}} pozostało)", "months#description": "", "monthsUnknown": "Liczba miesięcy: nieznana", "monthsUnknown#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}}}, "laser#description": "", "laser_few": "lasery", "laser_many": "lasery", "laser_one": "laser", "laser_other": "lasery"}, "models": {"model#description": "", "model_few": "modele", "model_many": "modele", "model_one": "model", "model_other": "modele", "none": "Brak modelu", "none#description": "", "p2p#description": "", "p2p_few": "Modele P2P", "p2p_many": "Modele P2P", "p2p_one": "Model P2P", "p2p_other": "Modele P2P", "unknown": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "Wygenerowano przez", "authorId#description": "", "createdAt": "Opublikowano", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_few": "Generowanie w toku", "run_many": "Generowanie w toku", "run_one": "<PERSON><PERSON> w<PERSON>", "run_other": "Generowanie w toku"}, "reports": {"fields": {"authorId": "Właściciel", "authorId#description": "", "automateWeekly": {"name": "Automatycznie", "name#description": "", "values": {"weekly": "Tygodniowo", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_few": "raporty", "report_many": "raporty", "report_one": "raport", "report_other": "raporty"}, "robots": {"classes": {"buds#description": "", "buds_few": "<PERSON><PERSON>", "buds_many": "<PERSON><PERSON>", "buds_one": "<PERSON>", "buds_other": "<PERSON><PERSON>", "moduleValidationStations#description": "", "moduleValidationStations_few": "Moduły Validation Stations", "moduleValidationStations_many": "Moduły Validation Stations", "moduleValidationStations_one": "Moduł Validation Stations", "moduleValidationStations_other": "Moduły Validation Stations", "reapersCarbon#description": "", "reapersCarbon_few": "<PERSON><PERSON>", "reapersCarbon_many": "<PERSON><PERSON>", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "<PERSON><PERSON>", "reapersCustomer_few": "", "reapersCustomer_many": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_few": "Ciągniki", "rtcs_many": "Ciągniki", "rtcs_one": "Ciągnik", "rtcs_other": "Ciągniki", "simulators#description": "", "simulators_few": "Symulatory", "simulators_many": "Symulatory", "simulators_one": "Symulator", "simulators_other": "Symulatory", "slayersCarbon#description": "", "slayersCarbon_few": "<PERSON><PERSON>", "slayersCarbon_many": "<PERSON><PERSON>", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "<PERSON><PERSON>", "slayersCustomer#description": "", "slayersCustomer_few": "Laserweedery", "slayersCustomer_many": "Laserweedery", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweedery", "unknown": "<PERSON><PERSON><PERSON>", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasery offline", "lasersOffline#description": "", "lifetimeArea": "O<PERSON>zar od uruchomienia", "lifetimeArea#description": "", "lifetimeTime": "Czas od uruchomienia", "lifetimeTime#description": "", "localTime": "Czas lokalny", "localTime#description": "", "reportedAt": "Ostatnia aktualizacja", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Wersja oprogramowania", "softwareVersion#description": "", "supportSlack": "<PERSON><PERSON><PERSON><PERSON> dla kanału <PERSON>", "supportSlack#description": "", "targetVersion": "<PERSON><PERSON><PERSON>", "targetVersion#description": ""}, "robot#description": "", "robot_few": "roboty", "robot_many": "roboty", "robot_one": "robot", "robot_other": "roboty", "unknown": "Robot nieznany", "unknown#description": ""}, "users": {"activated": "Aktywowane", "activated#description": "", "fields": {"email": "e-mail", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktywacja", "name#description": "", "values": {"false": "W TOKU", "false#description": ""}}}, "operator#description": "", "operator_few": "operatorzy", "operator_many": "operatorzy", "operator_one": "operator", "operator_other": "operatorzy", "role#description": "", "role_few": "Role", "role_many": "Role", "role_one": "Rola", "role_other": "Role", "roles": {"carbon_basic": "<PERSON><PERSON><PERSON>", "carbon_basic#description": "", "carbon_tech": "<PERSON><PERSON><PERSON> (techniczna)", "carbon_tech#description": "", "farm_manager": "Zarządca gospodarstwa", "farm_manager#description": "", "operator_advanced": "Operator (Doświadczony)", "operator_advanced#description": "", "operator_basic": "Operator", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "<PERSON><PERSON><PERSON><PERSON> rola", "unknown_role#description": ""}, "staff": "Pracownicy", "staff#description": "", "user#description": "", "user_few": "użytkownicy", "user_many": "użytkownicy", "user_one": "użytkownik", "user_other": "użytkownicy"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_few": "estymatory prędkości", "velocityEstimator_many": "estymatory prędkości", "velocityEstimator_one": "estymator <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "velocityEstimator_other": "estymatory prędkości"}, "weeds": {"categories": {"blossom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blossom#description": "", "broadleaf": "Szerokolistny", "broadleaf#description": "", "fruit": "Owoc", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "Odrost", "offshoot#description": "", "preblossom": "Przed kwitnieniem", "preblossom#description": "", "purslane": "Portulaka", "purslane#description": "", "runner": "Kłącze", "runner#description": "", "unknown": "<PERSON><PERSON><PERSON>", "unknown#description": ""}, "weed#description": "", "weed_few": "chwasty", "weed_many": "chwasty", "weed_one": "chwast", "weed_other": "chwasty"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON>", "add#description": "", "addLong": "<PERSON><PERSON><PERSON> {{subject}}", "addLong#description": "", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply#description": "", "applyLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "applyLong#description": "", "backLong": "z powrotem do {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "<PERSON><PERSON><PERSON> {{subject}}", "cancelLong#description": "", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear#description": "", "confirm": "Potwierdź", "confirm#description": "", "continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "continue#description": "", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "copyLong#description": "", "create": "Utwórz", "create#description": "", "createdLong": "{{subject}} ut<PERSON><PERSON><PERSON>", "createdLong#description": "", "delete": "Usuń", "delete#description": "", "deleteLong": "<PERSON><PERSON><PERSON> {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} <PERSON><PERSON><PERSON><PERSON>", "deletedLong#description": "", "disableLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON><PERSON>", "discard#description": "", "edit": "<PERSON><PERSON><PERSON><PERSON>", "edit#description": "", "editLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "editLong#description": "", "enableLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "enableLong#description": "", "exit": "Wyjdź", "exit#description": "", "exitLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> z {{subject}}", "exitLong#description": "", "goToLong": "<PERSON><PERSON><PERSON> do {{subject}}", "goToLong#description": "", "invite": "<PERSON><PERSON><PERSON><PERSON>", "invite#description": "", "inviteLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} zaproszony", "invitedLong#description": "", "leaveUnchanged": "Pozostaw bez zmian", "leaveUnchanged#description": "", "new": "Nowy", "new#description": "", "newLong": "Nowy {{subject}}", "newLong#description": "", "next": "<PERSON><PERSON>", "next#description": "", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pause#description": "", "play": "Odtwarzaj", "play#description": "", "previous": "Wstecz", "previous#description": "", "ranLong": "{{subject}} uruchomiony", "ranLong#description": "", "reload": "Załaduj ponownie", "reload#description": "", "resetLong": "Zresetuj {{subject}}", "resetLong#description": "", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ponownie", "retry#description": "", "run": "Uruchom", "run#description": "", "runLong": "Uru<PERSON>m {{subject}}", "runLong#description": "", "save": "<PERSON><PERSON><PERSON><PERSON>", "save#description": "", "saveLong": "Zapisz {{subject}}", "saveLong#description": "", "saved": "Zapisano", "saved#description": "", "savedLong": "{{subject}} zapisano", "savedLong#description": "", "search": "Szukaj", "search#description": "", "searchLong": "Szukaj {{subject}}", "searchLong#description": "", "selectAll": "Zaznacz wszystko", "selectAll#description": "", "selectLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "selectLong#description": "", "selectNone": "Niczego nie zaznaczono", "selectNone#description": "", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send#description": "", "showLong": "<PERSON><PERSON><PERSON> {{subject}}", "showLong#description": "", "submit": "Prześ<PERSON>j", "submit#description": "", "toggle": "Przełącz", "toggle#description": "", "toggleLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "toggleLong#description": "", "update": "Aktualizuj", "update#description": "", "updated": "Zak<PERSON>ali<PERSON>wan<PERSON>", "updated#description": "", "updatedLong": "Zaktualizowano {{subject}}", "updatedLong#description": "", "uploaded": "<PERSON><PERSON><PERSON>", "uploaded#description": "", "viewLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Aktywne", "active#description": "", "critical": "K<PERSON><PERSON><PERSON>ne", "critical#description": "", "default": "Domy<PERSON><PERSON><PERSON>", "default#description": "", "degraded": "Zdegradowane", "degraded#description": "", "dense": "<PERSON><PERSON><PERSON>", "dense#description": "", "disabled": "Wyłączone", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Włą<PERSON><PERSON>", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Błąd", "error#description": "", "estopOff": "W toku", "estopOff#description": "", "estopOn": "Zatrzymanie awaryjne", "estopOn#description": "", "fast": "<PERSON><PERSON><PERSON><PERSON>", "fast#description": "", "few": "Niewiele", "few#description": "", "good": "Dobrze", "good#description": "", "hidden": "<PERSON>k<PERSON><PERSON>", "hidden#description": "", "high": "Wysoka", "high#description": "", "id": "ID", "id#description": "", "inactive": "Nieaktywny", "inactive#description": "", "interlockSafe": "Wystrzał dozwolony", "interlockSafe#description": "", "interlockUnsafe": "<PERSON><PERSON><PERSON><PERSON><PERSON> niedozwolony", "interlockUnsafe#description": "", "large": "<PERSON><PERSON><PERSON>", "large#description": "", "laserKeyOff": "Zablokowany", "laserKeyOff#description": "", "laserKeyOn": "Załączony", "laserKeyOn#description": "", "liftedOff": "Opuszczony", "liftedOff#description": "", "liftedOn": "Podniesiony", "liftedOn#description": "", "loading": "Wczytywanie", "loading#description": "", "low": "<PERSON><PERSON>", "low#description": "", "majority": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "majority#description": "", "medium": "Średni", "medium#description": "", "minority": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minority#description": "", "name": "Nazwa", "name#description": "", "no": "<PERSON><PERSON>", "no#description": "", "none": "Brak", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "<PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Postęp", "progress#description": "", "serial": "<PERSON><PERSON><PERSON>", "serial#description": "", "slow": "Wolno", "slow#description": "", "small": "Ma<PERSON><PERSON>", "small#description": "", "sparse": "Rzad<PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_few": "Rod<PERSON><PERSON>", "type_many": "Rod<PERSON><PERSON>", "type_one": "<PERSON><PERSON><PERSON>", "type_other": "Rod<PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "waterProtectNormal": "Prawidłowa wilgotność", "waterProtectNormal#description": "", "waterProtectTriggered": "Wykryto wodę", "waterProtectTriggered#description": "", "yes": "Tak", "yes#description": ""}, "form": {"booleanType": "Musi to <PERSON><PERSON> war<PERSON>", "booleanType#description": "", "copyConfigFrom": "Ko<PERSON><PERSON>j konfigurację z...", "copyConfigFrom#description": "", "integerType": "Musi to <PERSON><PERSON> licz<PERSON> całkowita", "integerType#description": "", "maxLessThanMin": "Maks. musi być większe od min.", "maxLessThanMin#description": "", "maxSize": "Nie może mieć więcej niż {{limit}} znaków.", "maxSize#description": "", "minGreaterThanMax": "Min musi być mniejsze od maks.", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moveDown#description": "", "moveUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moveUp#description": "", "noOptions": "No options", "noOptions#description": "", "numberType": "<PERSON><PERSON> to <PERSON><PERSON> l<PERSON>", "numberType#description": "", "optional": "(opcjonalna)", "optional#description": "", "required": "<PERSON><PERSON><PERSON><PERSON>", "required#description": "", "stringType": "Musi to być ciąg znaków", "stringType#description": ""}, "lists": {"+3": "{{b}} i {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} i {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Wczytaj więcej", "loadMore#description": "", "noMoreResults": "Brak dalszych wyników", "noMoreResults#description": "", "noResults": "Brak wyników", "noResults#description": ""}, "metrics": {"aggregates": {"max": "maks.", "max#description": "", "min": "min.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Średni promień upraw", "avgCropSizeMm#description": "", "avgSpeedMph": "Średnia prędkość jazdy", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Średni czas trwania strzału", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Średni czas trwania strzału (niecelowany)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Średni promień chwastów", "avgWeedSizeMm#description": "", "bandingConfigName": "Konfiguracja p<PERSON>", "bandingConfigName#description": "", "bandingEnabled": "Pasmowanie", "bandingEnabled#description": "", "bandingPercentage": "Procent spasmowany", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Średnia prędkość zasięgu", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "<PERSON><PERSON><PERSON><PERSON><PERSON> upraw", "cropDensitySqFt#description": "", "distanceWeededMeters": "<PERSON>ys<PERSON>s <PERSON>", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "<PERSON><PERSON><PERSON> zachowane", "keptCrops#description": "", "killedWeeds": "Chwasty zniszczone", "killedWeeds#description": "", "missedCrops": "Uprawy nieosiągnięte", "missedCrops#description": "", "missedWeeds": "<PERSON><PERSON><PERSON>", "missedWeeds#description": "", "notThinning": "Upraw<PERSON> nie prz<PERSON>zedzane", "notThinning#description": "", "notWeeding": "<PERSON><PERSON><PERSON> nie odchwasz<PERSON>ane", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Efektywność operatora", "operatorEffectiveness#description": "", "overallEfficiency": "Wynik całkowity", "overallEfficiency#description": "", "skippedCrops": "Uprawy pominięte", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON><PERSON> pomini<PERSON>", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Docelowy czas odchwaszczania", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Uprawy przerzedzone", "thinnedCrops#description": "", "thinningEfficiency": "Wynik przerzedzania", "thinningEfficiency#description": "", "timeEfficiency": "Efektywność operacyjna", "timeEfficiency#description": "", "totalCrops": "Uprawy znalezione", "totalCrops#description": "", "totalWeeds": "Chwasty znalezione", "totalWeeds#description": "", "totalWeedsInBand": "Chwasty znalezione (w obrębie pasma)", "totalWeedsInBand#description": "", "uptimeSeconds": "<PERSON><PERSON> pracy", "uptimeSeconds#description": "", "validCrops": "Plony znalezione", "validCrops#description": "", "weedDensitySqFt": "<PERSON><PERSON><PERSON><PERSON><PERSON> chwastów", "weedDensitySqFt#description": "", "weedingEfficiency": "Wynik odchwaszczania", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON>zas odchwaszczania", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Typ chwastu: $t(modele.chwasty.kategorie.szerokolistne)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Typ chwastu: $t(modele.chwasty.kategorie.trawa)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Typ chwastu: $t(modele.chwasty.kategorie.odrost)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Typ chwastu: $t(modele.chwasty.kategorie.portulaka)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Wartość ta jest obliczana przed rozrzedzaniem, jeśli rozrzedzanie było włą<PERSON>one", "avgCropSizeMm#description": "", "bandingConfigName": "Ostatnio wybrany profil pasmowania", "bandingConfigName#description": "", "crop": "Ostatnio wybrana uprawa", "crop#description": "", "cropDensitySqFt": "Wartość ta jest obliczana przed rozrzedzaniem, jeśli rozrzedzanie było włą<PERSON>one", "cropDensitySqFt#description": "", "keptCrops": "Szacunkowa liczba upraw zachowanych po przerzedzeniu", "keptCrops#description": "", "killedWeeds": "LaserWeeder zidentyfikował obiekt jako chwast i usunął go", "killedWeeds#description": "", "missedCrops": "Uprawa została oznaczona do przerzedzenia, ale nie została przeprowadzona. Najczęstsze przyczyny to: przekroczenie pręd<PERSON>ści, przekroczenie zasięgu lub błąd systemu.", "missedCrops#description": "", "missedWeeds": "Znaleziono chwast, ale go nie zauważono. Najczęstsze przyczyny to: przekroczenie pręd<PERSON>ci, przekroczenie zasięgu lub błąd systemu.", "missedWeeds#description": "", "operatorEffectiveness": "<PERSON><PERSON><PERSON><PERSON>, w jakim stopniu rzeczywista pręd<PERSON><PERSON>ć podróży odpowiada prędkości docelowej zalecanej przez estymator prędko<PERSON>ci", "operatorEffectiveness#description": "", "overallEfficiency": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odchwaszczania + <PERSON>yd<PERSON><PERSON>ść przerzedzania) / 2, jeś<PERSON> jednocześnie odchwaszczasz i przerzedzasz", "overallEfficiency#description": "", "skippedCrops": "Uprawa została celowo pominięta podczas przerzedzania. Typowe przyczyny: wyłączenie w Quick Tune, poza pasmem lub w pobliżu taśmy kroplującej.", "skippedCrops#description": "", "skippedWeeds": "Chwast został celowo pominięty. Typowe przyczyny: wyłączenie w Quick Tune lub poza pasmem.", "skippedWeeds#description": "", "thinningEfficiency": "(Uprawy przerzedzone + Uprawy zachowane) / Szacunkowa liczba znalezionych upraw × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(Czas aktywnej pracy / <PERSON>zas włączenia) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "Całkowity czas działania LaserWeeder. Obejmuje czas, gdy urządzenie jest w trybie czuwania i/lub jest podniesione.", "uptimeSeconds#description": "", "weedDensitySqFt": "Szacowana liczba znalezionych chwastów (łącznie) / Zasięg", "weedDensitySqFt#description": "", "weedingEfficiency": "(<PERSON><PERSON><PERSON> zabite / Chwasty znalezione w paśmie) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON>, w którym LaserWeeder aktywnie odchwaszczał lub prz<PERSON>ł", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć prędkości pracy", "operatorEffectiveness#description": "", "timeEfficiency": "Wykorzystanie maszyny", "timeEfficiency#description": "", "totalWeeds": "Znalezione chwasty (łącznie)", "totalWeedsInBand": "Znalezione chwasty (w paśmie)", "totalWeedsInBand#description": "", "uptimeSeconds": "<PERSON>zas włączenia", "uptimeSeconds#description": "", "validCrops": "Szacowana liczba znalezionych upraw", "validCrops#description": "", "weedingUptimeSeconds": "Aktywny czas pracy", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "<PERSON><PERSON><PERSON><PERSON> pracy", "coverage#description": "", "field": "Pole", "field#description": "", "hardware": "", "hardware#description": "", "performance": "<PERSON><PERSON><PERSON> pracy", "performance#description": "", "speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Wykorzystanie", "usage#description": ""}, "metric#description": "", "metric_few": "wskaźniki", "metric_many": "wskaźniki", "metric_one": "wskaźnik", "metric_other": "wskaźniki", "spatial": {"heatmapWarning": "wg. \"bloków\" ~20×20 stóp", "heatmapWarning#description": "", "metrics": {"altitude": "Wysokość n.p.m.", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "Zatrzymanie awaryjne", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Blokada", "interlock#description": "", "keptCropDensity": "<PERSON><PERSON><PERSON><PERSON><PERSON> uprawy po przerzedzaniu", "keptCropDensity#description": "", "laserKey": "Klucz do lasera", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Efektywność dopasowania prędkości", "speed#description": "", "speedTargetMinimum": "Średnia (minimalna) prędkość docelowa", "speedTargetMinimum#description": "", "speedTargetRow1": "Średnia prędkość docelowa (rząd 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Średnia prędkość docelowa (rząd 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Średnia prędkość docelowa (rząd 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Średnia prędkość docelowa", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Przerzedzanie", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "<PERSON><PERSON><PERSON>", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Ochrona wody", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Odchwaszczanie", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Wybrano", "selected#description": "", "showAll": "Po<PERSON>ż wszystkie {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_few": "watów", "WLong_many": "watów", "WLong_one": "wat", "WLong_other": "watów", "ac": "ac", "ac#description": "", "ac/h": "akrów na godzinę", "ac/h#description": "", "acLong#description": "", "acLong_few": "akry", "acLong_many": "akry", "acLong_one": "akr", "acLong_other": "akry", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_few": "dni", "dLong_many": "dni", "dLong_one": "dzień", "dLong_other": "dni", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "ft", "ft#description": "", "ft/s": "stóp na sekundę", "ft/s#description": "", "ft2": "stóp kw.", "ft2#description": "", "ftLong#description": "", "ftLong_few": "stóp", "ftLong_many": "stóp", "ftLong_one": "stopa", "ftLong_other": "stóp", "h": "godz.", "h#description": "", "hLong#description": "", "hLong_few": "godzin", "hLong_many": "godzin", "hLong_one": "<PERSON><PERSON><PERSON>", "hLong_other": "godzin", "ha": "ha", "ha#description": "", "ha/h": "hektarów na godz.", "ha/h#description": "", "haLong#description": "", "haLong_few": "hektarów", "haLong_many": "hektarów", "haLong_one": "<PERSON>ktar", "haLong_other": "hektarów", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "cale kwadratowe", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "metrów na sekundę", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_few": "metrów", "mLong_many": "metrów", "mLong_one": "metr", "mLong_other": "metrów", "mi": "mi", "mi#description": "", "mi2": "mile kwadratowe", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_few": "minut", "minLong_many": "minut", "minLong_one": "minuta", "minLong_other": "minut", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "msc", "month#description": "", "monthLong#description": "", "monthLong_few": "<PERSON><PERSON><PERSON><PERSON>", "monthLong_many": "<PERSON><PERSON><PERSON><PERSON>", "monthLong_one": "<PERSON><PERSON><PERSON><PERSON>", "monthLong_other": "<PERSON><PERSON><PERSON><PERSON>", "mph": "mil na godz.", "mph#description": "", "ms": "<PERSON><PERSON><PERSON>.", "ms#description": "", "s": "sek.", "s#description": "", "sLong#description": "", "sLong_few": "sekund", "sLong_many": "sekund", "sLong_one": "sekunda", "sLong_other": "sekund", "seconds#description": "", "watts#description": "", "week": "tydz.", "week#description": "", "weekLong#description": "", "weekLong_few": "tygodni", "weekLong_many": "tygodni", "weekLong_one": "tydzień", "weekLong_other": "tygodni", "yd#description": "", "year": "r.", "year#description": "", "yearLong#description": "", "yearLong_few": "lat", "yearLong_many": "lat", "yearLong_one": "rok", "yearLong_other": "lat"}}, "views": {"admin": {"alarms": {"allowWarning": "Dodanie kodów do listy zezwoleń umożliwi alarmom pingować właściwe kanały Slack.", "allowWarning#description": "", "blockWarning": "Dodanie kodów do listy zablokowań uniemożliwi alarmom pingować właściwe kanały Slack.", "blockWarning#description": "", "lists": "Listy", "lists#description": "", "title": "Globalne listy zezwoleń alarmów", "title#description": "", "titleAllow": "Listy zezwoleń alarmów", "titleAllow#description": "", "titleBlock": "Listy zablokowań alarmów", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Ustawienia", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> on <PERSON>, <tt>{row1,row2,row3}</tt> on Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_few": "<PERSON><PERSON><PERSON>", "operation_many": "<PERSON><PERSON><PERSON>", "operation_one": "<PERSON><PERSON><PERSON>", "operation_other": "<PERSON><PERSON><PERSON>", "operationsCount": "Opera<PERSON><PERSON> ({{count}})", "operationsCount#description": "", "operationsHint": "<PERSON><PERSON> dodać operację, wybierz węzeł w schemacie konfiguracji.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_few": "napotkano {{count}} błędów", "encounteredErrors_many": "napotkano {{count}} błędów", "encounteredErrors_one": "napotkano {{count}} błąd", "encounteredErrors_other": "napotkano {{count}} błędów", "noChanges": "bez zmian", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_few": "zaktualizowano {{count}} kluczy", "updatedKeys_many": "zaktualizowano {{count}} kluczy", "updatedKeys_one": "zaktualizowano {{count}} klucz", "updatedKeys_other": "zaktualizowano {{count}} kluczy"}, "outcomes": {"failure": "<PERSON><PERSON><PERSON>", "failure#description": "", "partial": "Częściowe powodzenie operacji", "partial#description": "", "success": "Powodzenie", "success#description": ""}, "title": "Konfigurac<PERSON>", "title#description": ""}, "clearCaches": {"action": "Odś<PERSON><PERSON><PERSON> pamięć podręczną", "action#description": "", "description": "Problemy? Najpierw spróbuj odświeżyć pamięć podręczną Robot Syncer.", "description#description": ""}, "warnings": {"global": "Modyfikacja tej konfiguracji wpłynie na ustawienia domyślne i zalecenia dla wszystkich obecnych i przyszłych{{class}}.", "global#description": "", "notSimon": "<PERSON><PERSON> <PERSON>, więc nie wolno Ci tego edytować ... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Następujące zmiany nie zostały jeszcze zsynchronizowane z {{serial}}:", "description#description": "", "title": "Niezsynchronizowane klucze", "title#description": ""}}}, "portal": {"clearCaches": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną", "action#description": "", "description": "Czyści wewnętrzną pamięć podręczną Ops Center. **Spowoduje** to spowolnienie niektórych zapytań na krótki czas, ale **<PERSON>rawić** problemy związane z zablokowanymi przedawnionymi danymi.", "description#description": "", "details": "<PERSON><PERSON><PERSON><PERSON> tę opcj<PERSON>, jeśli ręcznie edytowałeś uprawnienia użytkownika w Auth0 (nie za pośrednictwem Centrum operacyjnego) lub wprowadziłeś zmiany w integracji z usługą zewnętrzną, np. <PERSON> lub <PERSON>, które nie są uwzględniane.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Opcje dostępne na tej stronie będą miały wpływ na działanie Ops Center w procesie produkcyjnym.", "global#description": "", "notPortalAdmin": "<PERSON><PERSON> j<PERSON>, wi<PERSON><PERSON> prawdo<PERSON>do<PERSON>nie nie powinieneś tego edytować… 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Kanał pomocy technicznej na Slacku powinien zaczynać się od znaku \"#”, np. \"#support-001-carbon”", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Ukryj historię przestawień", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Przypisz do ciągników", "orchestrateView#description": "", "showPivotHistory": "Pokaż historię przestawień", "showPivotHistory#description": ""}, "fetchFailed": "<PERSON>e udało się załadować aktualizacji danych", "fetchFailed#description": "", "goLive": "lokalizacji na żywo", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON><PERSON>", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "<PERSON>e udało się pobrać zadania, spr<PERSON>bować ponownie?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Tego zadania nie będzie można już przypisać do ciągników i trzeba je będzie utworzyć ponownie", "description#description": ""}, "customer": {"unknown": "Nieznany klient", "unknown#description": "", "withName": "Klient: {{name}}", "withName#description": ""}, "farm": {"unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Gospodarstwo: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON><PERSON><PERSON><PERSON> pole uprawne", "unknown#description": "", "withName": "Pole uprawne: {{name}}", "withName#description": ""}, "jobFinished": "Zadanie ukończone o {{time}}", "jobFinished#description": "", "jobStarted": "Zadanie rozpoczęło się o {{time}}", "jobStarted#description": "", "openInFarmView": "Otwórz w widoku gospodarstwa", "openInFarmView#description": "", "state": "Stan: {{state}}", "state#description": "", "type": "Rodzaj zadania: {{type}}", "type#description": ""}, "lastPolled": "<PERSON><PERSON><PERSON>", "lastPolled#description": "", "live": "Na żywo", "live#description": "", "objectiveFromOtherJob": "Cel z innego zadania", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON><PERSON><PERSON><PERSON><PERSON> wiersza {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Gospodarstwa", "farms#description": "", "tractors": "Ciągniki", "tractors#description": ""}, "showRows": "<PERSON><PERSON><PERSON> w<PERSON>ze", "showRows#description": "", "stalePivots": "Informacje o osi obrotu mogą być nieaktualne", "stalePivots#description": "", "suggestedAssignments": "Sugerowane zadania", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "<PERSON><PERSON><PERSON><PERSON> te <PERSON>", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "Pokaż tylko wybrane", "onlyShowSelected#description": "", "showAllPoints": "Pokaż wszystkie punkty", "showAllPoints#description": "", "showThesePoints": "Pokaż te punkty", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Granica", "boundary#description": "", "center": "Centrum", "center#description": "", "centerPivot": "Centralny punkt obrotu", "centerPivot#description": "", "endpointId": "Identyfikator punktu końcowego", "endpointId#description": "", "holes": "Otwory", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Kierunek sadzenia roś<PERSON>", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "Gospodarstwo", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "<PERSON><PERSON> <PERSON><PERSON>", "none#description": "", "rtkFixed": "RTK Naprawiony", "rtkFixed#description": "", "rtkFloat": "RTK Pływający", "rtkFloat#description": "", "unknown": "Nieznany typ poprawki", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "Wszystkie punkty", "allPoints#description": "", "boundary": "Granica", "boundary#description": "", "center": "Centrum", "center#description": "", "centerPivot": "Centralny punkt obrotu", "centerPivot#description": "", "endpointId": "Identyfikator punktu końcowego", "endpointId#description": "", "holes": "Otwory", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Kierunek sadzenia roś<PERSON>", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "Nienazwany punkt <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Granica gospodarstwa", "farmBoundary#description": "", "field": "Pole", "field#description": "", "headland": "Cy<PERSON>", "headland#description": "", "obstacle": "Przeszkoda", "obstacle#description": "", "privateRoad": "Droga prywatna", "privateRoad#description": "", "unknown": "<PERSON><PERSON><PERSON>y typ strefy", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "<PERSON><PERSON> powinna mieć dokładnie dwa punkty", "exactlyTwoPoints#description": "", "wrongFieldType": "<PERSON> „{{field}}” p<PERSON><PERSON><PERSON> być {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geometria powinna mieć typ {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON powinien być obiektem", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Brak robotów online", "empty#description": ""}, "title": "Centrum kontroli", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Wyświetl historię zmian", "open#description": "", "title": "Historia zmian", "title#description": ""}, "errors": {"failed": "<PERSON>e udało się wgrać drzewka konfiguracji.", "failed#description": ""}, "onlyChanged": "Pokaż tylko zmienione", "onlyChanged#description": ""}, "errors": {"empty": "Żaden robot nie został przydzielony.", "empty#description": ""}, "hardware": {"errors": {"old": "Robot nie zgłasza numerów seryjnych komputera (prawdopodobnie zbyt starych)", "old#description": ""}, "fields": {"hostname": "Nazwa hosta", "hostname#description": ""}, "installedVersion": "Zainstalowana wersja:", "installedVersion#description": "", "ready": {"name": "Gotowa do zainstalowania:", "name#description": "", "values": {"false": "Trwa pobieranie...", "false#description": "", "installed": "Zainstalowano", "installed#description": "", "true": "Gotowe!", "true#description": ""}}, "tabs": {"computers": "Komputery", "computers#description": "", "versions": "<PERSON><PERSON><PERSON>", "versions#description": ""}, "targetVersion": "<PERSON><PERSON><PERSON>:", "targetVersion#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": "", "updateHistory": "Historia aktualizacji wersji <0> wk<PERSON><PERSON><PERSON><PERSON> będzie dostępna™️</0>", "updateHistory#description": ""}, "history": {"borders": "Kontury elementów", "borders#description": "", "errors": {"invalidDate": "Wybierz ważny zakres dat", "invalidDate#description": "", "noJobs": "Nie zgłoszono zadań w wybranym zakresie", "noJobs#description": "", "noMetrics": "<PERSON><PERSON> zgł<PERSON><PERSON>o met<PERSON>k", "noMetrics#description": ""}, "moreMetrics": "Zobacz więcej metryk", "moreMetrics#description": "", "navTitle": "Historia", "navTitle#description": "", "placeholder": "<PERSON><PERSON><PERSON><PERSON> zadanie lub datę, aby zob<PERSON><PERSON><PERSON> dane", "placeholder#description": "", "points": "<PERSON><PERSON>", "points#description": "", "warnings": {"beta": {"description": "Metryki oczekujące na walidację zostały wyświetlone w kolorze niebieskim", "description#description": ""}, "ongoing": "Metryki dla wybranej daty nie są ostateczne", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "<PERSON>fin<PERSON><PERSON>", "definition#description": "", "dynamic": "Dynamiczne", "dynamic#description": "", "dynamicDisabled": "(Pasmowanie dynamiczne wyłączone w konfiguracji)", "dynamicDisabled#description": "", "rows": "Rzędy", "rows#description": "", "static": "Statyczne", "static#description": "", "type": "<PERSON><PERSON>", "type#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "<PERSON><PERSON><PERSON>", "version#description": ""}, "config": {"changes#description": "", "changes_few": "{{count}} z<PERSON>(y) w almanachu", "changes_many": "{{count}} z<PERSON>(y) w almanachu", "changes_one": "{{count}} zmian(a) w almanachu", "changes_other": "{{count}} z<PERSON>(y) w almanachu", "cpt": "Próg punktu upraw", "cpt#description": "", "default": "(DOMYSLNA: {{value}})", "default#description": "", "wpt": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>ta", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON> lewe", "backLeft#description": "", "backRight": "Tylne prawe", "backRight#description": "", "frontLeft": "Prz<PERSON><PERSON> lewe", "frontLeft#description": "", "frontRight": "Przednie prawe", "frontRight#description": "", "title": "Kodery kół", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "<PERSON><PERSON> udało się wgrać podsumowania dla robota", "failed#description": "", "lasers": {"disabled#description": "", "disabled_few": "{{count}} wyłączonych laserów", "disabled_many": "{{count}} wyłączonych laserów", "disabled_one": "{{count}} w<PERSON>łączony laser", "disabled_other": "{{count}} wyłączonych laserów", "row": "R<PERSON>ąd {{row}}", "row#description": ""}, "machineHealth": "Zdrowie maszyny", "machineHealth#description": "", "navTitle": "Podsumowanie", "navTitle#description": "", "safetyRadius": {"driptape": "Taśma kroplująca", "driptape#description": "", "title": "Promień bezpieczeństwa", "title#description": ""}, "sections": {"management": "Zarządzanie", "management#description": "", "software": "Oprogramowanie", "software#description": ""}, "supportLinks": {"chipChart": "<PERSON><PERSON><PERSON>", "chipChart#description": "", "datasetVisualization": "Wizualizacja zbioru danych", "datasetVisualization#description": "", "title": "Linki do pomocy", "title#description": ""}}, "support": {"carbon": "Wsparcie Carbon", "carbon#description": "", "chatMode": {"legacy": "Dotychczasowy czat", "legacy#description": "", "new": "<PERSON><PERSON> czat", "new#description": ""}, "errors": {"failed": "<PERSON>e udało się wgrać komunikatu", "failed#description": "", "old": {"description": "{{serial}} ma oprogramowanie w wersji nr {{version}}. <PERSON><PERSON><PERSON><PERSON> jest {{target}}, aby móc komunikować się z działem wsparcia na czacie.", "description#description": "", "title": "Niewystarczająca wersja robota", "title#description": ""}}, "localTime": "<PERSON>zas lokalny: {{time}}", "localTime#description": "", "navTitle": "<PERSON><PERSON><PERSON><PERSON>", "navTitle#description": "", "toCarbon": "Wiadomość dla $t(widoki.flota.roboty.wsparcie.carbon)", "toCarbon#description": "", "toOperator": "Wiadomość dla $t(modele.użytkownicy.operator_jeden)", "toOperator#description": "", "warnings": {"offline": {"description": "robot {{serial}} jest w trybie offline. Operator nie o<PERSON><PERSON><PERSON> w<PERSON>, dopóki robot nie uzyska łą<PERSON>.", "description#description": "", "title": "Robot jest w trybie offline.", "title#description": ""}}}, "toggleable": {"internal": "Wewnętrzny", "internal#description": ""}, "uploads": {"errors": {"empty": "Brak plików lub danych do wgrania", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Nazwa filtra", "name#description": "", "otherRobots": "<PERSON><PERSON> roboty ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Przypięte roboty", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_few": "filtry", "fleetView_many": "filtry", "fleetView_one": "filtr", "fleetView_other": "filtry", "tableOnly": "Niektóre kolumny są dostępne tylko w widoku tabeli", "tableOnly#description": ""}}, "knowledge": {"title": "<PERSON><PERSON> wiedzy", "title#description": ""}, "metrics": {"jobStatus": {"closed": "<PERSON><PERSON><PERSON><PERSON>", "closed#description": "", "description": "Status zadania", "description#description": "", "open": "<PERSON><PERSON><PERSON><PERSON>", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Szacowane metryki pola", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Nasz model wykorzy<PERSON><PERSON><PERSON> eksperymentalne dane dot<PERSON> upraw, które mogą zaw<PERSON> nieś<PERSON>ł<PERSON>. Ciągle udoskonalamy jego ni<PERSON>.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Wydajność i parametry maszyny", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Przeciągnij tutaj pliki z USB (lub z innego miejsca)", "drop#description": "", "file#description": "", "file_few": "pliki", "file_many": "pliki", "file_one": "plik", "file_other": "pliki", "ingestDescription": "Pracownicy Carbon muszą korzystać z narzędzia Ingest", "ingestDescription#description": "", "ingestLink": "Wgraj do Ingest", "ingestLink#description": "", "select": "Wybierz pliki", "select#description": "", "title": "<PERSON><PERSON><PERSON>", "title#description": "", "upload": "Wgraj do <PERSON>", "upload#description": "", "uploading": "Pliki {{subject}} są wgrywane ...", "uploading#description": ""}, "reports": {"explore": {"graph": "<PERSON><PERSON><PERSON>", "graph#description": "", "groupBy": "<PERSON><PERSON><PERSON><PERSON>", "groupBy#description": "", "title": "Eksploruj", "title#description": ""}, "scheduled": {"authorCarbonBot": "Bot Carbon", "authorCarbonBot#description": "", "authorUnknown": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "authorUnknown#description": "", "automation": {"customerReports": "<PERSON><PERSON><PERSON>", "customerReports#description": "", "errorTitle": "Nieprawidłowy raport automatyczny", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "<PERSON><PERSON> w<PERSON> k<PERSON>a", "none#description": ""}}, "reportDay": {"errors": {"none": "<PERSON>e wybrano dnia", "none#description": ""}, "name": "<PERSON><PERSON><PERSON> dostarczenia raportu", "name#description": ""}, "reportEmails": {"errors": {"none": "Brak przypisanych adresów e-mail", "none#description": ""}, "name": "Adresy e-mail klienta", "name#description": ""}, "reportHour": {"errors": {"none": "<PERSON><PERSON> w<PERSON><PERSON>no godziny", "none#description": ""}, "name": "Godzina wysłania raportu", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON>e zdefiniowano okresu wstecznego", "none#description": ""}, "name": "Okres wsteczny dla raportu", "name#description": ""}, "reportTimezone": {"errors": {"none": "<PERSON><PERSON> wybrano strefy czasowej", "none#description": ""}, "name": "St<PERSON>fa czasowa dla raportu", "name#description": ""}, "warningDescription": "Jest gene<PERSON>any co {{day}} dni o godz. {{hour}} w strefie {{timezone}} dla okresu {{lookback}} dni dla wszystkich aktywnych robotów klienta {{customer}}.", "warningDescription#description": "", "warningTitle": "To jest raport automatyczny!", "warningTitle#description": ""}, "byline": "Autor: {{author}}", "byline#description": "", "editor": {"columnsHidden": "Kolumny ukryte", "columnsHidden#description": "", "columnsVisible": "Kolumny widoczne", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_few": "Ostrzeżenie: istnieje {{count}} innych raportów o tej nazwie", "duplicateNames_many": "Ostrzeżenie: istnieje {{count}} innych raportów o tej nazwie", "duplicateNames_one": "Ostrzeżenie: istnieje inny raport o tej nazwie", "duplicateNames_other": "Ostrzeżenie: istnieje {{count}} innych raportów o tej nazwie", "fields": {"automateWeekly": "Automatyzacja tygodniowa", "automateWeekly#description": "", "name": "<PERSON><PERSON><PERSON> raportu", "name#description": "", "showAverages": "Pokaz wartości średnie", "showAverages#description": "", "showTotals": "Pokaż sumy", "showTotals#description": ""}}, "errors": {"noReport": "Raport nie istnieje lub nie masz do niego dostępu", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "Raport {{list}} zostanie trwale usunięty.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "<PERSON>e masz uprawnień do usunięcia {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "W<PERSON><PERSON>ość e-mail nie będzie wysyłana ponownie", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Raport zostanie wysłany na te adresy e-mail", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON><PERSON><PERSON><PERSON> ponownie", "runAgain#description": ""}, "table": {"errors": {"noColumns": "<PERSON><PERSON><PERSON><PERSON> jedną lub więcej kolumn", "noColumns#description": "", "noEndDate": "<PERSON><PERSON><PERSON><PERSON> datę końcową", "noEndDate#description": "", "noRobots": "<PERSON><PERSON><PERSON><PERSON> jeden lub wię<PERSON><PERSON><PERSON> liczbę robotów", "noRobots#description": "", "noStartDate": "<PERSON><PERSON><PERSON><PERSON> datę p<PERSON>z<PERSON>kową", "noStartDate#description": ""}, "fields": {"average": "Średnia", "average#description": "", "averageShort": "ŚR.", "averageShort#description": "", "date": "Data", "date#description": "", "group": "Numer seryjny/Data", "group#description": "", "groupJob": "Numer serii/<PERSON><PERSON>nie", "groupJob#description": "", "mixed": "(Różne)", "mixed#description": "", "total": "Łącznie", "total#description": "", "totalShort": "SUMA", "totalShort#description": ""}, "unknownReport": "<PERSON><PERSON><PERSON><PERSON> raport", "unknownReport#description": ""}, "title": "Zaplanowany", "title#description": "", "toLine": "dla {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "Wszystkie dane statystyczne", "all#description": "", "select": "<PERSON><PERSON><PERSON><PERSON> dane statystyczne", "select#description": ""}, "robotsLabel": {"all": "Wszystkie roboty", "all#description": "", "none": "<PERSON><PERSON> wy<PERSON>no robota", "none#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>a", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> za pośrednictwem <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "nazwa użytkownika i hasło", "auth0#description": "", "google": "Uwierzytelnianie Google OAuth", "google#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON> dos<PERSON>", "unknown#description": ""}, "cards": {"account": "Ko<PERSON>", "account#description": "", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advanced#description": "", "localization": "Lokalizacja", "localization#description": ""}, "delete": {"deleteAccount": "Us<PERSON>ń konto", "deleteAccount#description": "", "dialog": {"description": "OSTRZEŻENIE: <PERSON><PERSON> nie można cofnąć. Wszystkie dane zostaną utracone.", "description#description": ""}}, "fields": {"language": "Język", "language#description": "", "measurement": {"name": "Jednostki miary", "name#description": "", "values": {"imperial": "imperialne (cale, mile na godz., ak<PERSON>, stopnie Fahrenheita)", "imperial#description": "", "metric": "metryczne (mm, km/h, hektary, stopnie Celsjusza)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logOut#description": "", "title": "Ustawienia", "title#description": "", "version": "Wersja Carbon Ops Center {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Użytkownik nie istnieje, lub nie jeste<PERSON> upoważnio<PERSON>, aby go widzie<PERSON>.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Zarządzaj użytkownikiem w Auth0", "manage#description": "", "title": "Administrator", "title#description": ""}, "permissions": {"title": "Rola i uprawnienia", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Wykonawcy", "contractors#description": ""}}}}