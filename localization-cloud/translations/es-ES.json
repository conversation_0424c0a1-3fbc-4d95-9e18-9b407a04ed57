{"components": {"AlarmTable": {"export": "{{robots}} <PERSON><PERSON> de <PERSON> {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Las métricas espaciales están disponibles de forma gratuita durante la evaluación, pero pueden estar sujetas a cambios, eliminación o requisitos de actualización del plan en cualquier momento. Los datos deben verificarse de forma independiente.", "description#description": "", "title": "Métricas espaciales Beta", "title#description": ""}, "tooltip": "Esta función está en fase de evaluación y puede ser modificada o suprimida en cualquier momento.", "tooltip#description": ""}, "Chat": {"errors": {"failed": "No se ha podido cargar el chat: {{message}}", "failed#description": ""}, "machineTranslated": "Traducción automática", "machineTranslated#description": "", "machineTranslatedFrom": "Traducido automáticamente del {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Este mensaje ha sido eliminado.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} se eliminará permanentemente.", "description#description": "", "descriptionActive": "{{subject}} está activo por lo que no se puede eliminar.", "descriptionActive#description": ""}, "title": "¿E<PERSON>á seguro?", "title#description": ""}, "CopyToClipboardButton": {"click": "<PERSON>cer clic para copiar", "click#description": "", "copied": "¡Copiado!", "copied#description": ""}, "CropEditor": {"failed": "No se ha podido cargar el editor de cultivos", "failed#description": "", "viewIn": "Ver en Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Bo<PERSON>r", "clear#description": "", "endDate": "Fecha de finalización", "endDate#description": "", "error": "Error en el selector de intervalos de fechas", "error#description": "", "invalid": "<PERSON>v<PERSON><PERSON><PERSON>", "invalid#description": "", "last7days": "Últimos 7 días", "last7days#description": "", "lastMonth": "<PERSON><PERSON><PERSON>", "lastMonth#description": "", "lastWeek": "Última semana", "lastWeek#description": "", "minusDays": "Hace {{days}} días", "minusDays#description": "", "plusDays": "en {{days}} días", "plusDays#description": "", "startDate": "Fecha de inicio", "startDate#description": "", "thisMonth": "<PERSON>ste mes", "thisMonth#description": "", "thisWeek": "<PERSON><PERSON> semana", "thisWeek#description": "", "today": "Hoy", "today#description": "", "tomorrow": "<PERSON><PERSON><PERSON>", "tomorrow#description": "", "yesterday": "Ayer", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON> sentimo<PERSON>, error inesperado", "error#description": "", "queryLimitReached": "Procesamiento de conjunto de datos parcial porque se han devuelto demasiados datos. Póngase en contacto con el servicio de asistencia", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "¿Qué ha pasado?", "comment#description": "", "feedback": "Comentarios", "feedback#description": "", "submit": "Enviar y volver a cargar", "submit#description": ""}, "GdprConsent": {"description": "Por favor, revise y acepte continuar", "description#description": "", "statement": "Acepto las <0>Condiciones de uso</0> y la <1>Política de privacidad</1>", "statement#description": "", "title": "Condiciones de uso y Política de privacidad", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Cliente requerido", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Alternar este menú", "help#description": "", "title": "Métodos abreviados de teclado", "title#description": ""}, "LaserTable": {"export": "{{robots}} <PERSON><PERSON><PERSON><PERSON> {{date}}", "export#description": "", "installedOnly": "Instalado solamente", "installedOnly#description": "", "warnings": {"duplicate": "Este robot tiene varios láseres registrados en las siguientes posiciones: {{slots}}", "duplicate#description": "", "emptySlot": "Este robot no tiene ningún láser registrado en las siguientes posiciones: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Nuevo código", "new#description": ""}, "Loading": {"failed": "Lo sentimos, no se pudo cargar Carbon Ops Center.", "failed#description": "", "placeholder": "Cargando...", "placeholder#description": ""}, "ModelName": {"warning": "Advertencia: <PERSON><PERSON> de baja fiabilidad", "warning#description": ""}, "PendingActivationOverlay": {"description": "Estamos activando su cuenta. Recibirá un correo electrónico cuando haya finalizado.", "description#description": "", "errors": {"carbon": {"description": "Correo electrónico de Carbon detectado pero no verificado debido al inicio de sesión con nombre de usuario/contraseña. Cierre la sesión y utilice la opción «Iniciar sesión con Google» para que se active automáticamente.", "description#description": "", "title": "Cuenta de Carbon no verificada", "title#description": ""}}, "hi": "¡<PERSON><PERSON>, {{name}}!", "hi#description": "", "logOut": "¿Ha iniciado sesión con una cuenta incorrecta? <0>Cierre la sesión</0>.", "logOut#description": "", "title": "Esperando activación", "title#description": ""}, "ResponsiveSubnav": {"more": "Más", "more#description": ""}, "RobotImplementationSelector": {"status": "Estado de implementación", "status#description": "", "title": "Cambiar estado de implementación", "title#description": "", "warning": "Cambiar el estado de la implementación puede desencadenar flujos de trabajo automatizados que afecten a la experiencia del cliente. ¡NO LO HAGA SI NO ESTÁ SEGURO!", "warning#description": ""}, "ShowLabelsButton": {"text": "Etiquetas", "text#description": "", "tooltip": "Mostrar etiquetas", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Mostrar metadatos", "tooltip#description": ""}, "almanac": {"crops": {"new": "Añadir nuevo cultivo", "new#description": "", "none": "No hay categorías de cultivo", "none#description": "", "sync#description": ""}, "cropsSynced": "Todos los cultivos", "cropsSynced#description": "", "delete": {"description": "Esta acción no puede deshacerse", "description#description": ""}, "discard": {"description": "¿Descartar los cambios en {{title}}?", "description#description": "", "title": "¿Descartar los cambios?", "title#description": ""}, "fineTuneDescription": "Por defecto es 5, puede disminuir o aumentar el tiempo de disparo del láser en ~20% por incremento", "fineTuneDescription#description": "", "fineTuneTitle": "Multiplicador de ajuste fino", "fineTuneTitle#description": "", "formulas": {"all": "Todos los tamaños", "all#description": "", "copyFormula": "<PERSON><PERSON><PERSON>", "copyFormula#description": "", "copySize": "<PERSON><PERSON><PERSON>", "copySize#description": "", "exponent": {"description": "Eleva el radio en mm a este exponente", "description#description": "", "label": "Exponente (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Número del 1 al 10, por defecto es 5, puede disminuir o aumentar el tiempo de disparo del láser en un ~20% por incremento. Este es el número utilizado en el modo básico", "description#description": "", "label": "Índice de ajuste fino (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Multiplicador global para el incremento / decremento del índice de ajuste fino", "description#description": "", "label": "Valor del multiplicador de ajuste fino (FM)", "label#description": ""}, "laserTime": "Tiempo del láser", "laserTime#description": "", "maxTime": {"description": "Tope a la cantidad de tiempo de disparo en ms", "description#description": "", "label": "Tiempo máx.", "label#description": ""}, "multiplier": {"description": "Multiplica por el radio en mm", "description#description": "", "label": "Multiplicador (A)", "label#description": ""}, "offset": {"description": "Cantidad de milisegundos a añadir independientemente del radio", "description#description": "", "label": "Compensación (b)", "label#description": ""}, "pasteFormula": "<PERSON><PERSON><PERSON>", "pasteFormula#description": "", "pasteSize": "<PERSON><PERSON><PERSON>", "pasteSize#description": "", "sync": "Sincronizar todos los tamaños", "sync#description": "", "thresholds": "Umbrales de tamaño", "thresholds#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Cambiar al modo avanzado", "switchModeAdvanced#description": "", "switchModeBasic": "Cambiar al modo básico", "switchModeBasic#description": "", "warnings": {"admin": "Al modificar este almanaque se sincronizará con todas las unidades de producción actuales y futuras.", "admin#description": "", "carbon": "Este es un almanaque proporcionado por Carbon. Solo puede modificarse el índice de ajuste fino", "carbon#description": "", "production": "Este almanaque está funcionando de forma activa en un robot. Su edición tendrá efecto inmediatamente en el campo.", "production#description": ""}, "weeds": {"new": "<PERSON><PERSON><PERSON> nueva mala hierba", "new#description": "", "none": "No hay categoría de mala hierba", "none#description": "", "sync#description": ""}, "weedsSynced": "Todas las malas hierbas", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} se ha guardado. Activar mediante la aplicación QuickTune del operador", "savedLong#description": "", "testResults": "Vista previa de resultados", "testResults#description": ""}, "filters": {"capturedAt": "<PERSON><PERSON>", "capturedAt#description": "", "diameter": "Diámetro", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "Todas las imágenes", "allImages#description": "", "categorized": "Categorizadas", "categorized#description": "", "scrollToTop": "Volver al principio", "scrollToTop#description": "", "sortBy": {"latest": "Lo más reciente", "latest#description": ""}, "sortedBy": "Ordenado por: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Estado de búsqueda de errores", "error#description": "", "ready": "Los resultados están listos.", "ready#description": "", "session#description": "", "session_many": "", "session_one": "Sesión", "session_other": "Sesiones", "showResults": "Mostrar resultados", "showResults#description": "", "status": "{{processed}} procesados/{{total}} resultados", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Modificar este perfil de planta se sincronizará con todas las unidades de producción actuales y futuras.", "admin#description": "", "adminMeta": "Todos los perfiles de administrador estarán disponibles para todos los clientes. No generar desorden", "adminMeta#description": "", "production": "Este perfil de planta se está ejecutando activamente en un robot. El operario recibirá notificaciones de las actualizaciones y podrá elegir si desea aplicar los últimos cambios", "production#description": "", "protected": "Este es un perfil proporcionado por Carbon. No se puede modificar nada.", "protected#description": "", "unsavedChanges": "Cambios no guardados. Pulse guardar para aplicar los cambios.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_many": "", "changedKey_one": "Tecla cambiada", "changedKey_other": "Teclas camb<PERSON>", "newKey": "nuevo nombre {{key}}", "newKey#description": "", "stringReqs": "Solo puede contener a-z, 0-9, ., y _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Esta tecla se ha añadido sobre la predeterminada.", "description#description": ""}, "keyMissing": {"description": "Falta(n) valores por defecto: {{keys}}", "description#description": ""}, "valueChanged": {"description": "Este valor ha cambiado de su valor por defecto ({{default}})", "description#description": "", "title": "Configuración modificada", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "No se ha podido cargar el editor de cliente", "load#description": ""}}, "CustomerSelector": {"empty": "No asignado", "empty#description": "", "title": "Cambiar cliente", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "<PERSON><PERSON><PERSON><PERSON> vs ignorar", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON> Configs", "copy#description": "", "ignorable": {"description": "Disparar solo si el tiempo lo permite, no se considera en la recomendación de velocidad", "description#description": "", "label": "<PERSON><PERSON><PERSON> igno<PERSON>", "label#description": ""}, "paste": "<PERSON><PERSON><PERSON>", "paste#description": ""}, "warnings": {"production": "Este discriminador está funcionando de manera activa en un robot. Su edición tendrá efecto inmediatamente en el campo.", "production#description": ""}}, "drawer": {"customerMode": "Modo cliente", "customerMode#description": "", "error": "No se ha podido cargar la navegación", "error#description": ""}, "filters": {"NumericalRange": {"max": "Máx. ({{units}})", "max#description": "", "min": "Mín. ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "No se ha podido cargar la cabecera", "failed#description": "", "mascot": "Pollo mascota de Carbon Robotics", "mascot#description": "", "search": {"failed": "No se ha podido cargar la búsqueda", "failed#description": "", "focus": "Búsqueda focalizada", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "<PERSON><PERSON><PERSON>", "label#description": "", "larger": "más grande", "larger#description": "", "smaller": "más pequeño", "smaller#description": ""}}, "map": {"bounds": {"reset": "Restablecer vista", "reset#description": ""}, "errors": {"empty": "No se han notificado datos de localización", "empty#description": "", "failed": "No se ha podido cargar el mapa", "failed#description": ""}, "filters": {"customer_office": "Oficina del cliente", "customer_office#description": "", "hq": "Sede de Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Apartado de correos", "po_box#description": "", "shop": "Taller", "shop#description": "", "storage": "<PERSON><PERSON><PERSON><PERSON>", "storage#description": "", "support_base": "Base del equipo de soporte", "support_base#description": ""}, "fullscreen": "Pantalla completa", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Error de leyenda de capa", "legend#description": "", "notThinning": "NO ACLAREO", "notThinning#description": "", "notWeeding": "NO DESHIERBA", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "ERROR DE MAPA DE CALOR", "unknown#description": ""}, "fields": {"block": "Ubicación: {{block}}", "block#description": "", "location": "Ubicación: {{latitude}}, {{longitude}}", "location#description": "", "size": "Tamaño: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Capas", "name#description": "", "rangeType#description": "", "relative": "U<PERSON><PERSON>r rango relativo", "relative#description": "", "relativeRange#description": ""}, "map": "Mapa", "map#description": "", "measure": {"name": "Medir", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "¿Cuál es la categoría de origen?", "copyFromWhich#description": "", "splitCrops": "Separar cultivos", "splitCrops#description": "", "splitWeeds": "<PERSON><PERSON>r malas hierbas", "splitWeeds#description": "", "syncCrops": "Sincronizar todos los cultivos", "syncCrops#description": "", "syncWeeds": "Sincronizar todas las malas hierbas", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Umbral de confianza de predicción para utilizar una detección en el uso dinámico de bandas", "description#description": "", "label": "Umbral de uso de bandas", "label#description": ""}, "minDoo": {"description": "Detección mínima sobre oportunidad", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Umbral de confianza de predicción para utilizar una detección en el aclareo", "description#description": "", "label": "Umbral de aclareo", "label#description": ""}, "weed": {"description": "Umbral de confianza de predicción para utilizar una detección para la protección inversa de cultivos", "description#description": "", "label": "Umbral inverso de protección de cultivos", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Umbral de confianza de predicción para utilizar una detección para la protección de cultivos", "description#description": "", "label": "Umbral de protección de cultivos", "label#description": ""}, "weed": {"description": "Umbral de confianza de predicción para considerar una mala hierba", "description#description": "", "label": "Umbral de deshierba", "label#description": ""}}}, "errors": {"sync": "Los ajustes de este modelo aún no se han sincronizado desde la LaserWeeder. Espere a la sincronización para ver y actualizar los ajustes.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Separar <PERSON>", "splitSizesLong#description": "", "splitSizesShort": "Separar", "splitSizesShort#description": "", "syncSizesLong": "Sincronizar <PERSON>", "syncSizesLong#description": "", "syncSizesShort": "Sincronizar", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Aviso:{{stopEmphasis}} E<PERSON><PERSON> ajustes incluyen cambios no guardados que no se reflejan en el robot.", "exportingUnsavedChanges#description": "", "production": "Este modelo está funcionando de manera activa en un robot. Su edición tendrá efecto inmediatamente en el campo.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Alarmas desconocidas", "unknown#description": ""}, "almanac": {"unknown": "Almanaque desconocido", "unknown#description": "", "withName": "Almanaque: {{name}}", "withName#description": ""}, "autofixing": "Error de autorreparación", "autofixing#description": "", "banding": {"disabled": "Uso de bandas desactivado", "disabled#description": "", "enabled": "Uso de bandas activado", "enabled#description": "", "none": "Sin uso de bandas", "none#description": "", "static": "(ESTÁTICO)", "static#description": "", "withName": "Uso de bandas: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "No se ha podido cargar el estado de registro", "failed#description": "", "never": "Nunca se ha registrado", "never#description": "", "withTime": "Registrado {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Cultivos activados ({{pinned}} anclados)", "summary#description": ""}, "delivery": "Entrega", "delivery#description": "", "disconnected": "Desconectado", "disconnected#description": "", "discriminator": {"unknown": "Discriminador desconocido", "unknown#description": "", "withName": "Discriminador: {{name}}", "withName#description": ""}, "failed": "No se ha podido cargar el estado del robot", "failed#description": "", "failedShort": "Fallo", "failedShort#description": "", "implementation": "Implementación", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventario", "inventory#description": "", "job": {"none": "Sin tarea", "none#description": "", "withName": "Tarea: {{name}}", "withName#description": ""}, "lasers": "Láseres activos: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Duración", "lifetime#description": "", "lifted": "En espera (elevado)", "lifted#description": "", "loading": "Cargando", "loading#description": "", "location": {"known": "Ubicación: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Ubicación desconocida", "unknown#description": ""}, "manufacturing": "En fabricación", "manufacturing#description": "", "model": {"withName": "Modelo: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Cargando modelo", "modelLoading#description": "", "notArmed": "<PERSON> armar", "notArmed#description": "", "off_season": "Fuera de temporada", "off_season#description": "", "offline": "Sin conexión durante {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P desconocido", "unknown#description": ""}, "poweringDown": "Apagando", "poweringDown#description": "", "poweringUp": "Encendiendo", "poweringUp#description": "", "pre_manufacturing": "En prefabricación", "pre_manufacturing#description": "", "stale": "<PERSON><PERSON><PERSON><PERSON>", "stale#description": "", "staleDescription": "Último valor conocido. El robot está sin conexión.", "staleDescription#description": "", "standby": "En espera", "standby#description": "", "thinning": {"disabled": "Aclareo desactivado", "disabled#description": "", "enabled": "Aclareo activado", "enabled#description": "", "none": "<PERSON> aclareo", "none#description": "", "withName": "Aclareo: {{name}}", "withName#description": ""}, "today": {"none": "Sin deshierba hoy", "none#description": ""}, "unknown": "Estado desconocido", "unknown#description": "", "updating": "Instalando actualización", "updating#description": "", "version": {"values": {"unknown": "Versión desconocida", "unknown#description": "", "updateDownloading": "(descargando {{version}})", "updateDownloading#description": "", "updateReady": "({{version}} lista)", "updateReady#description": ""}}, "weeding": "Deshierbando {{crop}}", "weeding#description": "", "weedingDisabled": "Deshierba desactivada", "weedingDisabled#description": "", "weedingThinning": "Deshierbando y aclareando {{crop}}", "weedingThinning#description": "", "winterized": "Preparado para el invierno", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Ya existe", "exists#description": "", "unknownClass": "Clase de robot desconocida", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "No crear nueva config", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "Plantilla de {{class}}", "templateForClass#description": "", "templateGeneric": "Plantilla de robot", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Solo debe proceder si ya existe una configuración para {{serial}} o si tiene previsto crearla manualmente.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Ajustes avanzados de velocímetro", "advancedFormulaTitle#description": "", "formulaTitle": "<PERSON><PERSON><PERSON><PERSON>", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Disminuye automáticamente la velocidad sugerida en el valor introducido. Por ejemplo, una entrada del 5% reducirá la velocidad sugerida de 1 mph a 0,95 mph", "description#description": "", "label": "Compensación de velocidad", "label#description": ""}, "decreaseSmoothing": {"description": "Personalice el ritmo al que disminuye la velocidad. Cuanto mayor sea el valor, más probable será que el velocímetro fluctúe", "description#description": "", "label": "Suavizado de desaceleración", "label#description": ""}, "increaseSmoothing": {"description": "Personalice el ritmo al que aumenta la velocidad. Cuanto mayor sea el valor, más probable será que el velocímetro fluctúe", "description#description": "", "label": "Suavizado de aceleración", "label#description": ""}, "maxVelMph": {"description": "Introduzca la velocidad máxima absoluta a la que está dispuesto a viajar. Las recomendaciones de velocidad no superarán este valor", "description#description": "", "label": "Velocidad m<PERSON>xi<PERSON>", "label#description": ""}, "minVelMph": {"description": "Introduzca la velocidad mínima absoluta a la que está dispuesto a viajar. Las recomendaciones de velocidad no serán inferiores a este valor", "description#description": "", "label": "Velocidad <PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Este valor es el porcentaje de malas hierbas que desea eliminar", "description#description": "", "label": "Índice de destrucción ideal", "label#description": ""}, "primaryRange": {"description": "Aumente este valor si desea alcanzar su tasa de destrucción ideal independientemente del impacto de la velocidad", "description#description": "", "label": "<PERSON><PERSON><PERSON> verde", "label#description": ""}, "rows": {"allRows": "Todas las filas", "allRows#description": "", "row1": "Fila 1", "row1#description": "", "row2": "Fila 2", "row2#description": "", "row3": "Fila 3", "row3#description": ""}, "secondaryKillRate": {"description": "Este valor es el porcentaje más bajo aceptable de malas hierbas eliminadas", "description#description": "", "label": "Í<PERSON>ce mínimo de eliminación", "label#description": ""}, "secondaryRange": {"description": "Aumente este valor si desea añadir margen antes de recibir una notificación de baja velocidad", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "sync": "Sincronizar todas las filas", "sync#description": "", "warnings": {"admin": "Al modificar este calculador de velocidad se sincronizará con todas las unidades de producción actuales y futuras.", "admin#description": "", "production": "Este calculador de velocidad está funcionando de forma activa en un robot. Su edición tendrá efecto inmediato en el campo.", "production#description": "", "protected": "Este es un perfil proporcionado por Carbon. No se puede realizar ninguna modificación.", "protected#description": "", "unsavedChanges": "Cambios no guardados. Presione guardar para aplicar los cambios.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Gradual", "gradual#description": "", "immediate": "Inmediato", "immediate#description": ""}, "visualization": {"targetSpeed": "Velocidad objetivo", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_many": "", "alarm_one": "alarma", "alarm_other": "alarmas", "fields": {"code": "Código", "code#description": "", "description": "Descripción", "description#description": "", "duration": {"name": "Duración", "name#description": "", "values": {"ongoing": "ACTIVA", "ongoing#description": ""}}, "identifier": "Identificador", "identifier#description": "", "impact": {"name": "Impacto", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Iniciado", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_many": "", "almanac_one": "almanaque", "almanac_other": "almanaques", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_many": "", "assignment_one": "asignación", "assignment_other": "asignaciones", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instrucciones", "instructions#description": ""}, "intervention#description": "", "intervention_many": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "Aplicar láser a malas hierbas", "laserWeed#description": "", "unrecognized": "tipo desconocido ({{value}})", "unrecognized#description": ""}, "job_many": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "<PERSON><PERSON>", "laserWeedRow#description": ""}, "objective_many": "", "objective_one": "objetivo", "objective_other": "objetivos", "states": {"acknowledged": "reconocido", "acknowledged#description": "", "cancelled": "cancelado", "cancelled#description": "", "completed": "completado", "completed#description": "", "failed": "error", "failed#description": "", "inProgress": "en curso", "inProgress#description": "", "new": "nuevo", "new#description": "", "paused": "en pausa", "paused#description": "", "pending": "pendiente", "pending#description": "", "ready": "listo", "ready#description": "", "unrecognized": "estado desconocido ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "<PERSON>rea #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_many": "", "task_one": "tarea", "task_other": "tareas"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_many": "", "categoryCollectionProfile_one": "perfil de planta", "categoryCollectionProfile_other": "perfiles de planta", "fields": {"categories": {"disregard": "Descar<PERSON>", "disregard#description": "", "name": "Categorías", "name#description": "", "requiredBaseCategories": "Debe tener estas categorías exactas: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Actualizado", "updatedAt#description": ""}, "metadata": {"capturedAt": "Capturada", "capturedAt#description": "", "categoryId": "Id. de categoría", "categoryId#description": "", "imageId": "<PERSON>d. de imagen", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "<PERSON>d<PERSON> de punto", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "radio", "radius#description": "", "updatedAt": "Recibida", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_many": "", "config_one": "configuración", "config_other": "configuraciones", "key#description": "", "key_many": "", "key_one": "tecla", "key_other": "teclas", "template#description": "", "template_many": "", "template_one": "plantilla de configuración", "template_other": "plantillas de configuración", "value#description": "", "value_many": "", "value_one": "valor", "value_other": "valores"}, "crops": {"categories": {"unknown": "Cultivo desconocido", "unknown#description": ""}, "crop#description": "", "crop_many": "", "crop_one": "cultivo", "crop_other": "cultivos", "fields": {"confidence": {"fields": {"regionalImages": "Imágenes regionales:", "regionalImages#description": "", "totalImages": "Imágenes totales:", "totalImages#description": ""}, "name": "Confianza", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Archivado", "archived#description": "", "unknown": "Confianza desconocida", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Notas", "notes#description": "", "pinned": "Anclado", "pinned#description": "", "recommended": "Recomendado", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_many": "", "customer_one": "cliente", "customer_other": "clientes", "fields": {"emails": {"errors": {"formatting": "Un correo electrónico por línea", "formatting#description": ""}, "name": "Correos electrónicos", "name#description": ""}, "featureFlags": {"almanac": {"description": "Habilita las pestañas Almanaque y Discriminador para robots (el robot también debe admitir almanaque y discriminador)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Habilita la pestaña de Perfil de planta para los robots", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Los conmutadores de funciones activan la funcionalidad beta para todos los usuarios de un cliente", "description#description": "", "jobs": {"description": "Activa tareas (el robot también debe admitir tareas)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Muestra un nuevo diseño visual de las métricas de los robots.", "description#description": "", "name": "Rediseño de métricas", "name#description": ""}, "name": "Conmutadores de funciones", "name#description": "", "off": "INACTIVO", "off#description": "", "on": "ACTIVO", "on#description": "", "reports": {"description": "Habilita la pestaña Informes y sus funciones", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Mostrar datos espaciales, incluidos mapas térmicos y gráficos", "description#description": "", "name": "Datos espaciales", "name#description": ""}, "summary": "Conmutadores de funciones activos {{enabled}}/{{total}}", "summary#description": "", "unvalidatedMetrics": {"description": "Mostrar métricas beta pendientes de validación de campo en métricas certificadas", "description#description": "", "name": "Métricas beta", "name#description": ""}, "velocityEstimator": {"description": "Permite ver y editar los perfiles del calculador de velocidad objetivo (el robot también debe ser compatible con el calculador de velocidad objetivo)", "description#description": "", "name": "Calculador de velocidad objetivo", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Ejecutarse los", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Si se activa, los informes se ejecutarán semanalmente con los siguientes ajustes para todos los robots activos", "description#description": "", "name": "Informes semanales", "name#description": ""}, "weeklyReportHour": "Ejecutarse a las", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Periodo", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Ejecutar en", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_many": "", "discriminator_one": "discriminador", "discriminator_other": "discriminadores", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_many": "", "farm_one": "explotación agrícola", "farm_other": "explotaciones agrícolas", "obstacle#description": "", "obstacle_many": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_many": "", "point_one": "punto", "point_other": "puntos", "zone#description": "", "zone_many": "", "zone_one": "zona", "zone_other": "zonas"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_many": "", "fieldDefinition_one": "definición de campo", "fieldDefinition_other": "definiciones de campo", "fields": {"boundary": "Límite del campo", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Dirección de plantación", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_many": "", "global_one": "valor global", "global_other": "valores globales", "values": {"plantProfileModelId": {"description": "Modelo base utilizado por todos los perfiles de cliente y administrador para '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "Id. de modelo $t(components.categoryCollectionProfile.actions.testResults)", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "<PERSON><PERSON> y hora", "capturedAt#description": "", "geoJson": "Ubicación", "geoJson#description": "", "url": "<PERSON><PERSON><PERSON> imagen", "url#description": ""}, "image#description": "", "image_many": "", "image_one": "imágen", "image_other": "imágenes"}, "jobs": {"job#description": "", "job_many": "", "job_one": "Tarea", "job_other": "<PERSON><PERSON><PERSON>"}, "lasers": {"fields": {"cameraId": "Identificación de la cámara", "cameraId#description": "", "error": {"values": {"false": "Nominal", "false#description": ""}}, "installedAt": "Instalado", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Serie desconocida", "unknown#description": ""}}, "lifetimeSec": "A tiempo", "lifetimeSec#description": "", "powerLevel": "<PERSON><PERSON> de potencia", "powerLevel#description": "", "removedAt": "<PERSON><PERSON>", "removedAt#description": "", "rowNumber": "<PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Conteo de disparos", "totalFireCount#description": "", "totalFireTimeMs": "Tiempo de disparos", "totalFireTimeMs#description": "", "warranty": {"name": "Garantía", "name#description": "", "values": {"expired": "<PERSON><PERSON><PERSON>", "expired#description": "", "hours": "Horas: {{installed}}/{{total}} ({{percent}} restante)", "hours#description": "", "hoursUnknown": "Horas: Desconocido", "hoursUnknown#description": "", "months": "Meses: {{installed}}/{{total}} ({{percent}} restante)", "months#description": "", "monthsUnknown": "Meses: Desconocido", "monthsUnknown#description": "", "unknown": "Garantía desconocida", "unknown#description": ""}}}, "laser#description": "", "laser_many": "", "laser_one": "l<PERSON>er", "laser_other": "láseres"}, "models": {"model#description": "", "model_many": "", "model_one": "modelo", "model_other": "modelos", "none": "Sin modelo", "none#description": "", "p2p#description": "", "p2p_many": "", "p2p_one": "Modelo P2P", "p2p_other": "Modelos P2P", "unknown": "Modelo desconocido", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "<PERSON><PERSON><PERSON>", "authorId#description": "", "createdAt": "Publicado", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_many": "", "run_one": "Informe realizado", "run_other": "Informes realizados"}, "reports": {"fields": {"authorId": "<PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatizado", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON><PERSON>", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_many": "", "report_one": "informe", "report_other": "informes"}, "robots": {"classes": {"buds#description": "", "buds_many": "", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_many": "", "moduleValidationStations_one": "Estación de validación de módulos", "moduleValidationStations_other": "Estaciones de validación de módulos", "reapersCarbon#description": "", "reapersCarbon_many": "", "reapersCarbon_one": "Segadora", "reapersCarbon_other": "Segadoras", "reapersCustomer_many": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_many": "", "rtcs_one": "Tractor", "rtcs_other": "Tractores", "simulators#description": "", "simulators_many": "", "simulators_one": "<PERSON><PERSON><PERSON><PERSON>", "simulators_other": "Simulad<PERSON>", "slayersCarbon#description": "", "slayersCarbon_many": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_many": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Clase desconocida", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Láseres inactivos", "lasersOffline#description": "", "lifetimeArea": "Zona de vida útil", "lifetimeArea#description": "", "lifetimeTime": "Tiempo de vida útil", "lifetimeTime#description": "", "localTime": "Hora local", "localTime#description": "", "reportedAt": "Última actualización", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Versión de software", "softwareVersion#description": "", "supportSlack": "Canal de asistencia de Slack", "supportSlack#description": "", "targetVersion": "Versión objetivo", "targetVersion#description": ""}, "robot#description": "", "robot_many": "", "robot_one": "robot", "robot_other": "robots", "unknown": "Robot desconocido", "unknown#description": ""}, "users": {"activated": "Activado", "activated#description": "", "fields": {"email": "Email", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Activación", "name#description": "", "values": {"false": "PENDIENTE", "false#description": ""}}}, "operator#description": "", "operator_many": "", "operator_one": "operador", "operator_other": "operadores", "role#description": "", "role_many": "", "role_one": "Rol", "role_other": "Roles", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Técnico)", "carbon_tech#description": "", "farm_manager": "Encargado de la explotación agrícola", "farm_manager#description": "", "operator_advanced": "Operario (Avanzado)", "operator_advanced#description": "", "operator_basic": "Operario", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Rol desconocido", "unknown_role#description": ""}, "staff": "Personal", "staff#description": "", "user#description": "", "user_many": "", "user_one": "usuario", "user_other": "usuarios"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_many": "", "velocityEstimator_one": "Calculador de velocidad", "velocityEstimator_other": "Calculadores de velocidad"}, "weeds": {"categories": {"blossom": "<PERSON><PERSON>", "blossom#description": "", "broadleaf": "De hoja ancha", "broadleaf#description": "", "fruit": "<PERSON><PERSON>", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "<PERSON><PERSON>", "offshoot#description": "", "preblossom": "Antes de la floración", "preblossom#description": "", "purslane": "Verdolaga", "purslane#description": "", "runner": "Tallo rastrero", "runner#description": "", "unknown": "Hierba desconocida", "unknown#description": ""}, "weed#description": "", "weed_many": "", "weed_one": "<PERSON><PERSON>", "weed_other": "<PERSON><PERSON> hierbas"}}, "utils": {"actions": {"add": "Agregar", "add#description": "", "addLong": "Agregar {{subject}}", "addLong#description": "", "apply": "Aplicar", "apply#description": "", "applyLong": "Aplicar {{subject}}", "applyLong#description": "", "backLong": "volver a {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "Cancelar {{subject}}", "cancelLong#description": "", "clear": "Bo<PERSON>r", "clear#description": "", "confirm": "Confirmar", "confirm#description": "", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continue#description": "", "copy": "Copiar", "copy#description": "", "copyLong": "<PERSON><PERSON>r {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} creado", "createdLong#description": "", "delete": "Bo<PERSON>r", "delete#description": "", "deleteLong": "Eliminar {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} eliminado", "deletedLong#description": "", "disableLong": "Deshabilitar {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON>", "discard#description": "", "edit": "<PERSON><PERSON>", "edit#description": "", "editLong": "Editar {{subject}}", "editLong#description": "", "enableLong": "Habilitar {{subject}}", "enableLong#description": "", "exit": "Salida", "exit#description": "", "exitLong": "Salida {{subject}}", "exitLong#description": "", "goToLong": "Ir a {{subject}}", "goToLong#description": "", "invite": "Invitar", "invite#description": "", "inviteLong": "Invitar {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} invitado", "invitedLong#description": "", "leaveUnchanged": "No modificar", "leaveUnchanged#description": "", "new": "Nuevo", "new#description": "", "newLong": "Nuevo {{subject}}", "newLong#description": "", "next": "Próximo", "next#description": "", "pause": "Pausa", "pause#description": "", "play": "Play", "play#description": "", "previous": "Anterior", "previous#description": "", "ranLong": "{{subject}} se ejecutó", "ranLong#description": "", "reload": "Recargar", "reload#description": "", "resetLong": "Restablecer {{subject}}", "resetLong#description": "", "retry": "Reintentar", "retry#description": "", "run": "<PERSON><PERSON><PERSON><PERSON>", "run#description": "", "runLong": "Ejecutar {{subject}}", "runLong#description": "", "save": "Guardar", "save#description": "", "saveLong": "Guardar {{subject}}", "saveLong#description": "", "saved": "Guardado", "saved#description": "", "savedLong": "Guardado {{subject}}", "savedLong#description": "", "search": "Buscar", "search#description": "", "searchLong": "Buscar {{subject}}", "searchLong#description": "", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "selectAll#description": "", "selectLong": "Seleccionar {{subject}}", "selectLong#description": "", "selectNone": "No seleccionar ninguno", "selectNone#description": "", "send": "Enviar", "send#description": "", "showLong": "Enviar {{subject}}", "showLong#description": "", "submit": "<PERSON><PERSON><PERSON>", "submit#description": "", "toggle": "Cambiar", "toggle#description": "", "toggleLong": "Cambiar {{subject}}", "toggleLong#description": "", "update": "Actualizar", "update#description": "", "updated": "Actualizado", "updated#description": "", "updatedLong": "Actualizado {{subject}}", "updatedLong#description": "", "uploaded": "<PERSON><PERSON>", "uploaded#description": "", "viewLong": "Ver {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Activo", "active#description": "", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "critical#description": "", "default": "Por defecto", "default#description": "", "degraded": "Degradado", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "Desactivado", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Activado", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Error", "error#description": "", "estopOff": "<PERSON><PERSON><PERSON><PERSON>", "estopOff#description": "", "estopOn": "E-Stopped", "estopOn#description": "", "fast": "<PERSON><PERSON><PERSON><PERSON>", "fast#description": "", "few": "Pocos", "few#description": "", "good": "Bien", "good#description": "", "hidden": "Oculto", "hidden#description": "", "high": "Alto", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inactivo", "inactive#description": "", "interlockSafe": "Disparo permitido", "interlockSafe#description": "", "interlockUnsafe": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>o", "interlockUnsafe#description": "", "large": "Grande", "large#description": "", "laserKeyOff": "Bloqueado", "laserKeyOff#description": "", "laserKeyOn": "Activado", "laserKeyOn#description": "", "liftedOff": "Bajado", "liftedOff#description": "", "liftedOn": "Levantado", "liftedOn#description": "", "loading": "Cargando", "loading#description": "", "low": "<PERSON><PERSON>", "low#description": "", "majority": "<PERSON><PERSON>", "majority#description": "", "medium": "Medio", "medium#description": "", "minority": "Minoría", "minority#description": "", "name": "Nombre", "name#description": "", "no": "No", "no#description": "", "none": "<PERSON><PERSON><PERSON>", "none#description": "", "offline": "Desconectado", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "Pobre", "poor#description": "", "progress": "Progreso", "progress#description": "", "serial": "De serie", "serial#description": "", "slow": "<PERSON><PERSON>", "slow#description": "", "small": "Pequeño", "small#description": "", "sparse": "<PERSON><PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_many": "", "type_one": "Tipo", "type_other": "Tipos", "unknown": "Desconocido", "unknown#description": "", "waterProtectNormal": "Humedad normal", "waterProtectNormal#description": "", "waterProtectTriggered": "Agua detectada", "waterProtectTriggered#description": "", "yes": "Sí", "yes#description": ""}, "form": {"booleanType": "Debe ser un booleano", "booleanType#description": "", "copyConfigFrom": "Copiar configuración de...", "copyConfigFrom#description": "", "integerType": "Debe ser un entero", "integerType#description": "", "maxLessThanMin": "Máx. debe ser mayor que mín.", "maxLessThanMin#description": "", "maxSize": "No puede exceder los {{limit}} caracteres", "maxSize#description": "", "minGreaterThanMax": "Mín. debe ser menor que máx.", "minGreaterThanMax#description": "", "moveDown": "Mover hacia abajo", "moveDown#description": "", "moveUp": "Mover hacia arriba", "moveUp#description": "", "noOptions": "No hay opciones", "noOptions#description": "", "numberType": "Tiene que ser un número", "numberType#description": "", "optional": "(opcional)", "optional#description": "", "required": "Requerido", "required#description": "", "stringType": "Debe ser una cadena", "stringType#description": ""}, "lists": {"+3": "{{b}} y {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} y {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "<PERSON>gar más", "loadMore#description": "", "noMoreResults": "No hay más resultados", "noMoreResults#description": "", "noResults": "No hay resultados", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Máx.", "max#description": "", "min": "<PERSON><PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Radio de cultivo promedio", "avgCropSizeMm#description": "", "avgSpeedMph": "Velocidad de viaje promedio", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Duración media de disparo", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Duración media de disparo(no dirigido)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Radio promedio de mala hierba", "avgWeedSizeMm#description": "", "bandingConfigName": "Configuración de rayas", "bandingConfigName#description": "", "bandingEnabled": "<PERSON><PERSON>", "bandingEnabled#description": "", "bandingPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON> rayado", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Velocidad de cobertura promedio", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Densidad de cultivos", "cropDensitySqFt#description": "", "distanceWeededMeters": "<PERSON><PERSON><PERSON>be", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Cultivos conservados", "keptCrops#description": "", "killedWeeds": "Malas hierbas eliminadas", "killedWeeds#description": "", "missedCrops": "Cultivos perdidos", "missedCrops#description": "", "missedWeeds": "<PERSON>as hierbas perdidas", "missedWeeds#description": "", "notThinning": "No aclarear los cultivos", "notThinning#description": "", "notWeeding": "No quitar las malas hierbas", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Eficacia del operador", "operatorEffectiveness#description": "", "overallEfficiency": "Rendimiento global", "overallEfficiency#description": "", "skippedCrops": "Cultivos ignorados", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON> igno<PERSON>", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Tiempo de deshierbe objetivo", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Cultivos aclareados", "thinnedCrops#description": "", "thinningEfficiency": "Rendimiento del aclareado", "thinningEfficiency#description": "", "timeEfficiency": "Eficiencia operacional", "timeEfficiency#description": "", "totalCrops": "Cultivos encontrados", "totalCrops#description": "", "totalWeeds": "Malas hierbas encontradas", "totalWeeds#description": "", "totalWeedsInBand": "Malas hierbas encontradas (dentro de raya)", "totalWeedsInBand#description": "", "uptimeSeconds": "Tiempo de actividad", "uptimeSeconds#description": "", "validCrops": "Cultivos encontrados", "validCrops#description": "", "weedDensitySqFt": "Densidad de malas hierbas", "weedDensitySqFt#description": "", "weedingEfficiency": "Rendimiento de deshi<PERSON>be", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON>", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Tipo de mala hierba: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Tipo de mala hierba: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Tip<PERSON> de mala hierba: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Tipo de mala hierba: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Se calcula antes del raleo si este está activado.", "avgCropSizeMm#description": "", "bandingConfigName": "Su último perfil de banda seleccionado", "bandingConfigName#description": "", "crop": "Su último cultivo seleccionado", "crop#description": "", "cropDensitySqFt": "Se calcula antes del raleo si este está activado.", "cropDensitySqFt#description": "", "keptCrops": "Número estimado de cultivos conservados tras el raleo", "keptCrops#description": "", "killedWeeds": "LaserWeeder identificó el objeto como una mala hierba y le destruyó.", "killedWeeds#description": "", "missedCrops": "El cultivo se marcó para el raleo, pero no se hizo. Los motivos más comunes son: exceso de velocidad, fuera de cobertura o error del sistema.", "missedCrops#description": "", "missedWeeds": "Se identificó la mala hierba, pero no se detectó. Los motivos más comunes son: exceso de velocidad, fuera de cobertura o error del sistema.", "missedWeeds#description": "", "operatorEffectiveness": "Muestra en qué medida la velocidad de desplazamiento real coincide con la velocidad objetivo recomendada por el Estimador de velocidad.", "operatorEffectiveness#description": "", "overallEfficiency": "(rendimiento de la retirada de malas hierbas + rendimiento del raleo)/2, si retiran las malas hierbas y se ralea a la vez", "overallEfficiency#description": "", "skippedCrops": "El cultivo se omitió a propósito durante el raleo. Las razones más comunes son: desactivado en QuickTune, fuera de banda de goteo o cercano.", "skippedCrops#description": "", "skippedWeeds": "La mala hierba se omitió a propósito. Las razones más comunes son: desactivado en Quick Tune o fuera de banda.", "skippedWeeds#description": "", "thinningEfficiency": "(Cultivos raleados + cultivos conservados)/Estimación de cultivos encontrados × 100 %.", "thinningEfficiency#description": "", "timeEfficiency": "(Tiempo de trabajo activo/Tiempo de encendido) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "La cantidad total de tiempo que LaserWeeder estuvo encendido. Incluye cuando está en modo de espera y/o en modo de elevación.", "uptimeSeconds#description": "", "weedDensitySqFt": "Estimación de malas hierbas encontradas (total)/Cobertura", "weedDensitySqFt#description": "", "weedingEfficiency": "(Malas hierbas eliminadas/Malas hierbas encontradas en la banda) × 100 %", "weedingEfficiency#description": "", "weedingUptimeSeconds": "El tiempo que LaserWeeder estuvo eliminando malas hierbas o raleando de manera activa.", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Eficiencia de la velocidad", "operatorEffectiveness#description": "", "timeEfficiency": "Utilización de la máquina", "timeEfficiency#description": "", "totalWeeds": "Malas hierbas encontradas (total)", "totalWeedsInBand": "Malas hierbas encontradas (en banda)", "totalWeedsInBand#description": "", "uptimeSeconds": "Tiempo de encendido", "uptimeSeconds#description": "", "validCrops": "Estimación de cultivos encontrados", "validCrops#description": "", "weedingUptimeSeconds": "Tiempo de trabajo activo", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Cobertura", "coverage#description": "", "field": "Campo", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Actuación", "performance#description": "", "speed": "Velocidad", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "<PERSON><PERSON>", "usage#description": ""}, "metric#description": "", "metric_many": "", "metric_one": "m<PERSON><PERSON><PERSON>", "metric_other": "m<PERSON><PERSON><PERSON>", "spatial": {"heatmapWarning": "por ~20×20 pies \"bloque\"", "heatmapWarning#description": "", "metrics": {"altitude": "<PERSON><PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interbloqueo", "interlock#description": "", "keptCropDensity": "Densidad de cultivos mantenida", "keptCropDensity#description": "", "laserKey": "<PERSON><PERSON><PERSON>", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Eficiencia de velocidad", "speed#description": "", "speedTargetMinimum": "Velocidad objetivo promedio (mínima)", "speedTargetMinimum#description": "", "speedTargetRow1": "Velocidad objetivo promedio (fila 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Velocidad objetivo promedio (fila 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Velocidad objetivo promedio (fila 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Velocidad objetivo promedio", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "<PERSON><PERSON><PERSON><PERSON>", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Tiempo", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Protección del agua", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "<PERSON><PERSON><PERSON><PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected#description": "", "showAll": "Mostrar todos {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_many": "", "WLong_one": "vatio", "WLong_other": "vatios", "ac": "ac", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_many": "", "acLong_one": "acre", "acLong_other": "acres", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_many": "", "dLong_one": "día", "dLong_other": "días", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_many": "", "ftLong_one": "pie", "ftLong_other": "pies", "h": "h", "h#description": "", "hLong#description": "", "hLong_many": "", "hLong_one": "hora", "hLong_other": "horas", "ha": "ha", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_many": "", "haLong_one": "hectárea", "haLong_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_many": "", "mLong_one": "metro", "mLong_other": "metros", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_many": "", "minLong_one": "minuto", "minLong_other": "minutos", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "mes", "month#description": "", "monthLong#description": "", "monthLong_many": "", "monthLong_one": "mes", "monthLong_other": "meses", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_many": "", "sLong_one": "segundo", "sLong_other": "segundos", "seconds#description": "", "watts#description": "", "week": "sem", "week#description": "", "weekLong#description": "", "weekLong_many": "", "weekLong_one": "semana", "weekLong_other": "semanas", "yd#description": "", "year": "año", "year#description": "", "yearLong#description": "", "yearLong_many": "", "yearLong_one": "año", "yearLong_other": "años"}}, "views": {"admin": {"alarms": {"allowWarning": "Añadir códigos a la lista de permitidas permitirá enviar alarmas a los canales Slack de apoyo", "allowWarning#description": "", "blockWarning": "Añadir códigos a la lista de bloqueo impedirá que las alarmas hagan ping a los canales Slack de apoyo", "blockWarning#description": "", "lists": "Listas", "lists#description": "", "title": "Lista global de alarmas permitidas", "title#description": "", "titleAllow": "Lista de alarmas permitidas", "titleAllow#description": "", "titleBlock": "Lista de alarmas bloqueadas", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Ajustar", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>filas/*</tt> en <PERSON>, <tt>{row1,row2,row3}</tt> en Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_many": "", "operation_one": "operación", "operation_other": "operaciones", "operationsCount": "Operaciones ({{count}})", "operationsCount#description": "", "operationsHint": "Seleccione un nodo en el esquema de configuración para poder añadir una operación.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_many": "", "encounteredErrors_one": "{{count}} error encontrado", "encounteredErrors_other": "{{count}} errores encontrados", "noChanges": "sin cambios", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_many": "", "updatedKeys_one": "{{count}} clave actualizada", "updatedKeys_other": "{{count}} claves actualizadas"}, "outcomes": {"failure": "Fallo", "failure#description": "", "partial": "Éxito parcial", "partial#description": "", "success": "Éxito", "success#description": ""}, "title": "Configuraciones masivas", "title#description": ""}, "clearCaches": {"action": "Actualizar caché", "action#description": "", "description": "¿Problemas? Pruebe a actualizar primero la caché de Robot Syncer", "description#description": ""}, "warnings": {"global": "Modificar esta configuración afectará a los valores predeterminados y a las recomendaciones para todas las {{class}} actuales y futuras", "global#description": "", "notSimon": "No eres Simon, así que probablemente no deberías estar editando esto... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Los siguientes cambios no se han sincronizado con {{serial}}:", "description#description": "", "title": "Claves no sincronizadas", "title#description": ""}}}, "portal": {"clearCaches": {"action": "<PERSON><PERSON><PERSON> ca<PERSON>", "action#description": "", "description": "Borra las cachés internas del Centro de Operaciones. Esto **ralentizará** algunas consultas a corto plazo, pero **podría** solucionar problemas con datos obsoletos y congelados.", "description#description": "", "details": "<PERSON>úlselo si ha editado manualmente los permisos de un usuario en Auth0 (no a través del Centro de Operaciones) o ha realizado cambios en una integración de terceros como Stream o Slack que no se reflejan.", "details#description": ""}, "title": "Centro de Operaciones", "title#description": "", "warnings": {"global": "Las opciones de esta página afectarán al funcionamiento en directo del Centro de Operaciones en producción.", "global#description": "", "notPortalAdmin": "No eres Ansel ni <PERSON>, así que probablemente no deberías editar esto... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "El canal de asistencia de Slack debe comenzar con \"#\": e.g., \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Ocultar historial de pivote", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Asignar a tractores", "orchestrateView#description": "", "showPivotHistory": "Mostrar historial de pivote", "showPivotHistory#description": ""}, "fetchFailed": "Fallo al cargar los datos de ubicación", "fetchFailed#description": "", "goLive": "actualización en tiempo real", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON><PERSON> filas", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "No se ha podido recuperar la asignación, ¿reintentar?", "assignmentsFailed#description": "", "cancelDialog": {"description": "El trabajo ya no podrá asignarse a tractores y deberá volver a crearse.", "description#description": ""}, "customer": {"unknown": "Cliente desconocido", "unknown#description": "", "withName": "Cliente: {{name}}", "withName#description": ""}, "farm": {"unknown": "Explotación desconocida", "unknown#description": "", "withName": "Explotación: {{name}}", "withName#description": ""}, "field": {"unknown": "Campo desconocido", "unknown#description": "", "withName": "Campo: {{name}}", "withName#description": ""}, "jobFinished": "Trabajo terminado a las {{time}}", "jobFinished#description": "", "jobStarted": "Trabajo iniciado a las {{time}}", "jobStarted#description": "", "openInFarmView": "Abrir en vista de explotación", "openInFarmView#description": "", "state": "Estado: {{state}}", "state#description": "", "type": "Tipo de trabajo: {{type}}", "type#description": ""}, "lastPolled": "<PERSON><PERSON><PERSON> sondeo", "lastPolled#description": "", "live": "En tiempo real", "live#description": "", "objectiveFromOtherJob": "Objetivo de otro trabajo", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON><PERSON> {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Explotaciones", "farms#description": "", "tractors": "Tractores", "tractors#description": ""}, "showRows": "<PERSON>rar filas", "showRows#description": "", "stalePivots": "La información de pivote podría estar obsoleta", "stalePivots#description": "", "suggestedAssignments": "Asignaciones recomendadas", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "Ocultar estos puntos", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "Mostrar solo seleccionados", "onlyShowSelected#description": "", "showAllPoints": "Mostrar todos los puntos", "showAllPoints#description": "", "showThesePoints": "Mostrar estos puntos", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Límite", "boundary#description": "", "center": "Centro", "center#description": "", "centerPivot": "Pivote central", "centerPivot#description": "", "endpointId": "Id. de punto final", "endpointId#description": "", "holes": "Agujeros", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Encabezamiento de plantación", "plantingHeading#description": "", "point": "Punt<PERSON>", "point#description": "", "points": "Punt<PERSON>", "points#description": "", "width": "<PERSON><PERSON>", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "Explotación", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Sin señal", "none#description": "", "rtkFixed": "RTK Fijo", "rtkFixed#description": "", "rtkFloat": "RTK Flotante", "rtkFloat#description": "", "unknown": "Tipo de posicionamiento desconocido", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "Todos los puntos", "allPoints#description": "", "boundary": "Límite", "boundary#description": "", "center": "Centro", "center#description": "", "centerPivot": "Pivote central", "centerPivot#description": "", "endpointId": "Id. de punto final", "endpointId#description": "", "holes": "Agujeros", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Encabezamiento de plantación", "plantingHeading#description": "", "point": "Punt<PERSON>", "point#description": "", "points": "Punt<PERSON>", "points#description": "", "width": "<PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "Punto sin nombre <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Límite de explotación", "farmBoundary#description": "", "field": "<PERSON><PERSON><PERSON>", "field#description": "", "headland": "Zona de maniobra", "headland#description": "", "obstacle": "Obstá<PERSON>lo", "obstacle#description": "", "privateRoad": "Carretera privada", "privateRoad#description": "", "unknown": "Tipo de zona desconocido", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "La línea debe tener dos puntos exactamente", "exactlyTwoPoints#description": "", "wrongFieldType": "El campo \"{{field}}\" debería ser {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "La geometría debería ser de tipo {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON debería ser un objeto", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Sin robots conectados", "empty#description": ""}, "title": "Control de la misión", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Ver historial de cambios", "open#description": "", "title": "Cambiar historial", "title#description": ""}, "errors": {"failed": "No se ha podido cargar el árbol de configuración", "failed#description": ""}, "onlyChanged": "Mostrar solo ajustes modificados", "onlyChanged#description": ""}, "errors": {"empty": "No hay robots asignados", "empty#description": ""}, "hardware": {"errors": {"old": "El robot no informa de los números de serie del ordenador (probablemente es demasiado antiguo)", "old#description": ""}, "fields": {"hostname": "Nombre del equipo", "hostname#description": ""}, "installedVersion": "Versión instalada", "installedVersion#description": "", "ready": {"name": "Listo para instalar:", "name#description": "", "values": {"false": "Descargando...", "false#description": "", "installed": "Instalado", "installed#description": "", "true": "¡Listo!", "true#description": ""}}, "tabs": {"computers": "Ordenadores", "computers#description": "", "versions": "Versiones", "versions#description": ""}, "targetVersion": "Versión objetivo:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Historial de actualización de versiones <0>próximamente™️</0>", "updateHistory#description": ""}, "history": {"borders": "<PERSON><PERSON>", "borders#description": "", "errors": {"invalidDate": "Seleccionar intervalo de fechas válido", "invalidDate#description": "", "noJobs": "No se ha informado de ninguna tarea en el intervalo seleccionado", "noJobs#description": "", "noMetrics": "No se ha informado de métricas", "noMetrics#description": ""}, "moreMetrics": "Ver más métricas", "moreMetrics#description": "", "navTitle": "Historial", "navTitle#description": "", "placeholder": "Seleccionar una tarea o fecha para ver los datos", "placeholder#description": "", "points": "Punt<PERSON>", "points#description": "", "warnings": {"beta": {"description": "Las métricas pendientes de validación aparecen en azul", "description#description": ""}, "ongoing": "Las métricas para esta fecha aún no son definitivas", "ongoing#description": ""}}, "status": "Estado", "status#description": "", "summary": {"banding": {"definition": "Definición", "definition#description": "", "dynamic": "Dinámico", "dynamic#description": "", "dynamicDisabled": "(Uso de bandas dinámico desactivado en config)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON>", "rows#description": "", "static": "Está<PERSON><PERSON>", "static#description": "", "type": "Tipo", "type#description": "", "unknown": "Uso de bandas desconocido", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Versión", "version#description": ""}, "config": {"changes#description": "", "changes_many": "", "changes_one": "{{count}} cambio de almanaque", "changes_other": "{{count}} cambios de almanaque", "cpt": "Umbral de punto de cultivo", "cpt#description": "", "default": "(POR DEFECTO: {{value}})", "default#description": "", "wpt": "Umbral de punto de mala hierba", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON>", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON> derecha", "backRight#description": "", "frontLeft": "Delantera izquierda", "frontLeft#description": "", "frontRight": "Delantera derecha", "frontRight#description": "", "title": "Codificadores de rueda", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "No se ha podido cargar el resumen del robot", "failed#description": "", "lasers": {"disabled#description": "", "disabled_many": "", "disabled_one": "{{count}} l<PERSON>er desactivado", "disabled_other": "{{count}} l<PERSON><PERSON>s desactivados", "row": "Fila {{row}}", "row#description": ""}, "machineHealth": "<PERSON>ud de la máquina", "machineHealth#description": "", "navTitle": "Resumen", "navTitle#description": "", "safetyRadius": {"driptape": "<PERSON><PERSON>a de goteo", "driptape#description": "", "title": "Radio de seguridad", "title#description": ""}, "sections": {"management": "Gestión", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Gráfico de fragmentos", "chipChart#description": "", "datasetVisualization": "Visualización de conjunto de datos", "datasetVisualization#description": "", "title": "Enlaces de asistencia", "title#description": ""}}, "support": {"carbon": "Atención al cliente de Carbon", "carbon#description": "", "chatMode": {"legacy": "Chat heredado", "legacy#description": "", "new": "Chat nuevo", "new#description": ""}, "errors": {"failed": "No se ha podido cargar el mensaje", "failed#description": "", "old": {"description": "{{serial}} está ejecutando la versión de software {{version}}. Debe ser {{target}} para utilizar el chat de apoyo.", "description#description": "", "title": "Versión de robot insuficiente", "title#description": ""}}, "localTime": "Hora local: {{time}}", "localTime#description": "", "navTitle": "Atención al cliente", "navTitle#description": "", "toCarbon": "Mensaje para $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Mensaje para $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} está ejecutándose sin conexión. El operador no recibirá el mensaje hasta que el robot tenga conectividad.", "description#description": "", "title": "Robot sin conexión", "title#description": ""}}}, "toggleable": {"internal": "Interno", "internal#description": ""}, "uploads": {"errors": {"empty": "Sin cargas", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Nombre de filtro", "name#description": "", "otherRobots": "Otros robots ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Robots anclados", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "Tarjetas", "cards#description": "", "table": "Tabla", "table#description": ""}}}, "fleetView#description": "", "fleetView_many": "", "fleetView_one": "filtro", "fleetView_other": "filtros", "tableOnly": "Algunas columnas solo están disponibles en la vista de tabla", "tableOnly#description": ""}}, "knowledge": {"title": "Base de conocimiento", "title#description": ""}, "metrics": {"jobStatus": {"closed": "<PERSON><PERSON><PERSON>", "closed#description": "", "description": "Estado del trabajo", "description#description": "", "open": "<PERSON>bie<PERSON>o", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Métricas de campo estimadas", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Nuestro modelo utiliza datos experimentales de cultivos que pueden contener inexactitudes. Perfeccionamos continuamente su fiabilidad.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Rendimiento y métricas de la máquina", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Arrastre los archivos aquí desde el USB (o cualquier otro lugar)", "drop#description": "", "file#description": "", "file_many": "", "file_one": "archivo", "file_other": "archivos", "ingestDescription": "Los empleados de Carbon deben utilizar el servicio Ingest", "ingestDescription#description": "", "ingestLink": "Cargar a Ingest", "ingestLink#description": "", "select": "Seleccionar archivos", "select#description": "", "title": "<PERSON><PERSON>", "title#description": "", "upload": "Cargar a Carbon", "upload#description": "", "uploading": "Cargando {{subject}}...", "uploading#description": ""}, "reports": {"explore": {"graph": "Grafico", "graph#description": "", "groupBy": "Agrupar por", "groupBy#description": "", "title": "Explorar", "title#description": ""}, "scheduled": {"authorCarbonBot": "robot de carbón", "authorCarbonBot#description": "", "authorUnknown": "Autor desconocido", "authorUnknown#description": "", "automation": {"customerReports": "Informes de clientes", "customerReports#description": "", "errorTitle": "Informe automatizado no válido", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Ningún cliente seleccionado", "none#description": ""}}, "reportDay": {"errors": {"none": "Ningún día seleccionado", "none#description": ""}, "name": "Día del informe", "name#description": ""}, "reportEmails": {"errors": {"none": "No hay correos electrónicos asignados", "none#description": ""}, "name": "Correos electrónicos de clientes", "name#description": ""}, "reportHour": {"errors": {"none": "Ninguna hora seleccionada", "none#description": ""}, "name": "Hora del informe", "name#description": ""}, "reportLookback": {"errors": {"none": "No hay mirada atrás definida", "none#description": ""}, "name": "Informe retrospectivo", "name#description": ""}, "reportTimezone": {"errors": {"none": "No se seleccionó ninguna zona horaria", "none#description": ""}, "name": "Informar zona horaria", "name#description": ""}, "warningDescription": "Se ejecuta cada {{day}} a las {{hour}} en {{timezone}} con {{lookback}} días anteriores para todos los robots {{customer}} activos.", "warningDescription#description": "", "warningTitle": "¡Este es un informe automatizado!", "warningTitle#description": ""}, "byline": "Por {{author}}", "byline#description": "", "editor": {"columnsHidden": "Columnas ocultas", "columnsHidden#description": "", "columnsVisible": "Columnas visibles", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_many": "", "duplicateNames_one": "Advertencia: hay otro informe con este nombre", "duplicateNames_other": "Advertencia: hay {{count}} informes con este nombre", "fields": {"automateWeekly": "Automatizar <PERSON>", "automateWeekly#description": "", "name": "Nombre del informe", "name#description": "", "showAverages": "Mostrar promedios", "showAverages#description": "", "showTotals": "Mostrar totales", "showTotals#description": ""}}, "errors": {"noReport": "El informe no existe o no tienes acceso", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} se eliminará permanentemente.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "No estás autorizado a eliminar {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "El correo electrónico no será reenviado", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "El informe se enviará a estos correos electrónicos.", "publishEmailsHelperNew#description": ""}, "runAgain": "Ejecutar de nuevo", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Seleccionar una o más columnas", "noColumns#description": "", "noEndDate": "Seleccionar fecha de finalización", "noEndDate#description": "", "noRobots": "Seleccionar uno o más robots", "noRobots#description": "", "noStartDate": "Seleccionar fecha de inicio", "noStartDate#description": ""}, "fields": {"average": "Promedio", "average#description": "", "averageShort": "AVG", "averageShort#description": "", "date": "<PERSON><PERSON>", "date#description": "", "group": "Serie/Fecha", "group#description": "", "groupJob": "Serie/Tarea", "groupJob#description": "", "mixed": "(V<PERSON><PERSON>)", "mixed#description": "", "total": "Total", "total#description": "", "totalShort": "SUM", "totalShort#description": ""}, "unknownReport": "Informe desconocido", "unknownReport#description": ""}, "title": "Planificador", "title#description": "", "toLine": "para {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "Todas las métricas", "all#description": "", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select#description": ""}, "robotsLabel": {"all": "Todos los robots", "all#description": "", "none": "Ningún robot", "none#description": "", "select": "Seleccionar robots", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> mediante <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "nombre de usuario y contraseña", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "<PERSON>edor desconocido", "unknown#description": ""}, "cards": {"account": "C<PERSON><PERSON>", "account#description": "", "advanced": "Avanzados", "advanced#description": "", "localization": "Localización", "localization#description": ""}, "delete": {"deleteAccount": "<PERSON><PERSON><PERSON> cuenta", "deleteAccount#description": "", "dialog": {"description": "ADVERTENCIA: Esta acción no se puede deshacer. Todos los datos se perderán.", "description#description": ""}}, "fields": {"language": "Idioma", "language#description": "", "measurement": {"name": "Unidades de medida", "name#description": "", "values": {"imperial": "Imperial (pulgadas, mph, acres, fahrenheit)", "imperial#description": "", "metric": "Métrica (mm, km/h, hectáreas, Celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "<PERSON><PERSON><PERSON>", "logOut#description": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "title#description": "", "version": "Versión de Carbon Ops Center {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "El usuario no existe o no tiene permiso para verlo.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Administrar usuario en Auth0", "manage#description": "", "title": "ADmin", "title#description": ""}, "permissions": {"title": "Rol y permisos", "title#description": ""}, "profile": {"title": "Perfil", "title#description": ""}}, "toggleable": {"contractors": "Contratistas", "contractors#description": ""}}}}