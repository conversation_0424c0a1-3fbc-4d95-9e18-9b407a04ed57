{"components": {"AlarmTable": {"export": "{{robots}} Историја аларма {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Просторна метрика је доступна без наплате током евалуације, али се може изменити, уклонити и добити захтев за надоградњу у било које време. Подаци треба да буду појединачно проверени.", "description#description": "", "title": "Просторна метрика Бета верзија", "title#description": ""}, "tooltip": "Ова функција се тренутно оцењује и може се променити или уклонити у било које време", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Učitavanje ćaskanja nije uspelo: {{message}}", "failed#description": ""}, "machineTranslated": "Maš<PERSON>ki prevod", "machineTranslated#description": "", "machineTranslatedFrom": "<PERSON><PERSON><PERSON><PERSON> prevedno sa {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "<PERSON>va poruka je i<PERSON>bri<PERSON>a", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} c<PERSON>e biti trajno <PERSON>.", "description#description": "", "descriptionActive": "{{subject}} je aktivan tako da se ne može izbrisati.", "descriptionActive#description": ""}, "title": "Да ли сте сигурни?", "title#description": ""}, "CopyToClipboardButton": {"click": "Кликни за копирање", "click#description": "", "copied": "Копирано!", "copied#description": ""}, "CropEditor": {"failed": "Учитавање уређивача усева није успело", "failed#description": "", "viewIn": "Pogledati u Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Избриши", "clear#description": "", "endDate": "Датум завршетка", "endDate#description": "", "error": "Грешка у избору опсега датума", "error#description": "", "invalid": "Неважеће", "invalid#description": "", "last7days": "Последњих 7 дана", "last7days#description": "", "lastMonth": "Последњи месец", "lastMonth#description": "", "lastWeek": "Последња недеља", "lastWeek#description": "", "minusDays": "пре {{days}} дана", "minusDays#description": "", "plusDays": "за {{days}} дана", "plusDays#description": "", "startDate": "Датум почетка", "startDate#description": "", "thisMonth": "<PERSON><PERSON><PERSON><PERSON> месец", "thisMonth#description": "", "thisWeek": "Ова недеља", "thisWeek#description": "", "today": "<PERSON><PERSON><PERSON><PERSON>", "today#description": "", "tomorrow": "Сутра", "tomorrow#description": "", "yesterday": "Јуче", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "БЕТА ВЕРЗИЈА", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "Жао нам је, неочекивана грешка", "error#description": "", "queryLimitReached": "Prikazivanje delimičnog skupa podataka jer je vraćeno previše podataka. Obratite se podršci za pomoć", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Шта се догодило?", "comment#description": "", "feedback": "Повратна информација", "feedback#description": "", "submit": "Пошаљи и поново учитај", "submit#description": ""}, "GdprConsent": {"description": "Прегледајте и прихватите да бисте наставили", "description#description": "", "statement": "Прихватам <0>Услове коришћења</0> и <1>Политику заштите приватности</1>", "statement#description": "", "title": "Услови коришћења и Политика заштите приватности", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Potreban je klijent", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Укљ./Искљ. овај мени", "help#description": "", "title": "Пречице тастатуре", "title#description": ""}, "LaserTable": {"export": "{{robots}} Ла<PERSON><PERSON><PERSON>и {{date}}", "export#description": "", "installedOnly": "Само инсталирано", "installedOnly#description": "", "warnings": {"duplicate": "Ovaj robot ima više lasera ​​registrovanih u sledećem slotu / slede<PERSON>im slotovima: {{slots}}", "duplicate#description": "", "emptySlot": "Ovaj robot nema registrovan laser u sledećem slotu / sledec<PERSON><PERSON> slotovima: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Нови кôд", "new#description": ""}, "Loading": {"failed": "<PERSON><PERSON> j<PERSON>, Carbon Ops Center nije uspeo da se učita.", "failed#description": "", "placeholder": "Учитавање...", "placeholder#description": ""}, "ModelName": {"warning": "Упозорење: Модел ниске поузданости", "warning#description": ""}, "PendingActivationOverlay": {"description": "Активирамо ваш налог. Добићете е-поруку када буде готово!", "description#description": "", "errors": {"carbon": {"description": "Carbon адреса е-поште је детектована, али није верификована због пријаве корисничким именом/лозинком. Одјавите се и користите опцију \"Пријави се услугом Google\" да би се аутоматски активирало.", "description#description": "", "title": "Непотврђени Carbon налог", "title#description": ""}}, "hi": "Здраво, {{name}}!", "hi#description": "", "logOut": "Пријављени сте погрешним налогом? <0>Одјава</0>.", "logOut#description": "", "title": "Чека се активација", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON><PERSON>", "more#description": ""}, "RobotImplementationSelector": {"status": "Статус имплементације", "status#description": "", "title": "Промени статус имплементације", "title#description": "", "warning": "Промена статуса имплементације може да покрене аутоматизоване токове посла који утичу на корисничко искуство. НЕ РАДИТЕ ОВО АКО НИСТЕ ПОТПУНО СИГУРНИ!", "warning#description": ""}, "ShowLabelsButton": {"text": "Oznake", "text#description": "", "tooltip": "Прикажи ознаке", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Prikaži metapodatke", "tooltip#description": ""}, "almanac": {"crops": {"new": "Додај нови усев", "new#description": "", "none": "Нема категорија усева", "none#description": "", "sync#description": ""}, "cropsSynced": "<PERSON><PERSON> <PERSON>vi", "cropsSynced#description": "", "delete": {"description": "Ово се не може опозвати", "description#description": ""}, "discard": {"description": "Желите ли да одбаците промене за {{title}}?", "description#description": "", "title": "Желите ли да одбаците промене?", "title#description": ""}, "fineTuneDescription": "Подразумевано је 5, може смањити или повећати време ласерског снимања за ~20% по кораку", "fineTuneDescription#description": "", "fineTuneTitle": "Прецизно подешено умножавање", "fineTuneTitle#description": "", "formulas": {"all": "Све величине", "all#description": "", "copyFormula": "Копирај формулу", "copyFormula#description": "", "copySize": "Копирај величине", "copySize#description": "", "exponent": {"description": "Повећава радијус у mm на овај степен", "description#description": "", "label": "Степен (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Број од 1-10, подразумевано је 5, може да смањи или повећа време ласерског снимања за ~20% по кораку. Овај број се користи у основном режиму рада", "description#description": "", "label": "Индекс финог подешавања (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Укупни множилац за повећање/смањење индекса финог подешавања", "description#description": "", "label": "Вредност подешавања финог подешавања (FM)", "label#description": ""}, "laserTime": "Време ласера", "laserTime#description": "", "maxTime": {"description": "Временско ограничење снимања у ms", "description#description": "", "label": "Макс. време", "label#description": ""}, "multiplier": {"description": "Множи радијус у mm", "description#description": "", "label": "Мно<PERSON><PERSON><PERSON><PERSON><PERSON> (A)", "label#description": ""}, "offset": {"description": "Број милисекунди које треба додати без обзира на полупречник", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "Налепи формулу", "pasteFormula#description": "", "pasteSize": "Налепи величине", "pasteSize#description": "", "sync": "Синхронизуј све величине", "sync#description": "", "thresholds": "Граничне вредности величине", "thresholds#description": "", "title": "Формуле", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Пребаците се на напредни режим", "switchModeAdvanced#description": "", "switchModeBasic": "Пребаците се на основни режим", "switchModeBasic#description": "", "warnings": {"admin": "Измена овог алманаха ће се синхронизовати са свим тренутним и будућим производним јединицама.", "admin#description": "", "carbon": "Ово је алманах који обезбеђује Carbon. Само индекс финог подешавања може да се измени.", "carbon#description": "", "production": "Овај алманах се активно користи на роботу. Свака измена на њему одразиће се одмах на пољу.", "production#description": ""}, "weeds": {"new": "Додај нови коров", "new#description": "", "none": "Нема категорије корова", "none#description": "", "sync#description": ""}, "weedsSynced": "<PERSON><PERSON> korov", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} sačuvan. Aktiviraj putem aplikacije operatera za brzo pod<PERSON>šavanje", "savedLong#description": "", "testResults": "Pregled rezultata", "testResults#description": ""}, "filters": {"capturedAt": "<PERSON><PERSON> snimka", "capturedAt#description": "", "diameter": "Prečnik", "diameter#description": "", "notUploaded": "", "notUploaded#description": "", "unappliedFilters": "", "unappliedFilters#description": "", "uploaded": "", "uploaded#description": "", "uploadedByOperator": "", "uploadedByOperator#description": ""}, "images": {"allImages": "Све слике", "allImages#description": "", "categorized": "Категоризовано", "categorized#description": "", "scrollToTop": "Nazad na vrh", "scrollToTop#description": "", "sortBy": {"latest": "Najnovije", "latest#description": ""}, "sortedBy": "Sortirano po: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Greška pri preuzimanju statusa", "error#description": "", "ready": "Rezultati su spremni", "ready#description": "", "session#description": "", "session_few": "Sesije", "session_one": "<PERSON><PERSON><PERSON>", "session_other": "Sesije", "showResults": "Prikaži rezultate", "showResults#description": "", "status": "<PERSON><PERSON><PERSON><PERSON> {{processed}} / {{total}} rezultata", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Измена профила ове категорије ће се синхронизовати са свим тренутним и будућим производним јединицама.", "admin#description": "", "adminMeta": "Svi administratorski profili biće dostupni svim korisnicima. Ne stvarajte pometnju!", "adminMeta#description": "", "production": "Овај профил категорије активно ради на роботу. Уређивање ће одмах ступити на снагу на терену.", "production#description": "", "protected": "Ово је профил у услузи carbon. Ништа се не може мењати.", "protected#description": "", "unsavedChanges": "Несачуване промене. Притисни сачувај да би се измене промениле.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_few": "Промењени кључеви", "changedKey_one": "Промењени кључ", "changedKey_other": "Промењени кључеви", "newKey": "ново име за {{key}}", "newKey#description": "", "stringReqs": "Може да садржи само: a-z, 0-9, ., - и _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Овај кључ је додат као први подразумевани.", "description#description": ""}, "keyMissing": {"description": "Подразумевана(е) вредности које недостају: {{keys}}", "description#description": ""}, "valueChanged": {"description": "Ова вредност је промењена у односу на подразумевану ({{default}})", "description#description": "", "title": "Конфигурација је промењена", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Учитавање уређивача клијента није успело", "load#description": ""}}, "CustomerSelector": {"empty": "Није додељено", "empty#description": "", "title": "<PERSON><PERSON><PERSON> k<PERSON>", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "<PERSON><PERSON><PERSON> ili z<PERSON>", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON>", "copy#description": "", "ignorable": {"description": "Pucajte samo ako vreme dozvoljava, ne uzimajući u obzir preporuku brzine", "description#description": "", "label": "Može se zanemariti", "label#description": ""}, "paste": "<PERSON><PERSON><PERSON> k<PERSON>gu<PERSON>", "paste#description": ""}, "warnings": {"production": "Ovaj diskriminator aktivno radi na robotu. Uređivanje će odmah stupiti na snagu na terenu.", "production#description": ""}}, "drawer": {"customerMode": "<PERSON><PERSON><PERSON> za klijente", "customerMode#description": "", "error": "Učitavanje navigacije nije uspelo", "error#description": ""}, "filters": {"NumericalRange": {"max": "Maks. ({{units}})", "max#description": "", "min": "Min. ({{units}})", "min#description": ""}, "false": "", "false#description": "", "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": "", "true": "", "true#description": ""}, "header": {"failed": "Učitavanje zaglavlja nije uspelo", "failed#description": "", "mascot": "Pile maskota Carbon Robotocs", "mascot#description": "", "search": {"failed": "Učitavanje pretrage nije uspelo", "failed#description": "", "focus": "<PERSON><PERSON><PERSON><PERSON> pretragu", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Veličina", "label#description": "", "larger": "веће", "larger#description": "", "smaller": "мање", "smaller#description": ""}}, "map": {"bounds": {"reset": "Reset<PERSON>j p<PERSON>z", "reset#description": ""}, "errors": {"empty": "Nema prijavljenih podataka o lokaciji", "empty#description": "", "failed": "Učitavanje mape nije uspelo", "failed#description": ""}, "filters": {"customer_office": "Kancelarija klijenta", "customer_office#description": "", "hq": "Carbon sedište", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Poštansko sanduče", "po_box#description": "", "shop": "Radionica", "shop#description": "", "storage": "Skladištenje", "storage#description": "", "support_base": "Baza za podršku", "support_base#description": ""}, "fullscreen": "<PERSON><PERSON> celog e<PERSON>na", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Greška legende sloja", "legend#description": "", "notThinning": "BEZ RAZREĐIVANJA", "notThinning#description": "", "notWeeding": "BEZ PLEVLJENJA", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "GREŠKA TOPLOTNE MAPE", "unknown#description": ""}, "fields": {"block": "Blokiraj: {{block}}", "block#description": "", "location": "Lokacija: {{latitude}}, {{longitude}}", "location#description": "", "size": "Veličina: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "S<PERSON>jevi", "name#description": "", "rangeType#description": "", "relative": "Koristiti relativni opseg", "relative#description": "", "relativeRange#description": ""}, "map": "Mapa", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "Kopirati iz koje kategorije?", "copyFromWhich#description": "", "splitCrops": "Razdvojiti useve", "splitCrops#description": "", "splitWeeds": "Razdvojiti korov", "splitWeeds#description": "", "syncCrops": "Sinh. sve useve", "syncCrops#description": "", "syncWeeds": "Sinh. sve korove", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Prag pouzdanosti predviđanja za korišćenje detekcije u dinamičkom pojasu", "description#description": "", "label": "<PERSON><PERSON> pojasa", "label#description": ""}, "minDoo": {"description": "Minimalna detekcija u odnosu na mogućnost", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Prag pouzdanosti predviđanja za korišćenje detekcije u razređivanju", "description#description": "", "label": "<PERSON>rag <PERSON>az<PERSON>đ<PERSON>", "label#description": ""}, "weed": {"description": "Prag pouzdanosti predviđanja za korišćenje detekcije za Reverse Crop zaštitu", "description#description": "", "label": "Reverse Crop prag za<PERSON>", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Prag pouzdanosti predviđanja za korišćenje detekcije za zaštitu useva", "description#description": "", "label": "Prag zaštite useva", "label#description": ""}, "weed": {"description": "Prag pouzdanosti predviđanja za razmatranje korova", "description#description": "", "label": "<PERSON>rag plevl<PERSON>", "label#description": ""}}}, "errors": {"sync": "Podešavanja za ovaj model jo<PERSON> uvek nisu sinhronizovana sa LaserWeeder-a. <PERSON>te sinhronizaciju da biste videli i ažurirali podešavanja.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Razdvojiti ve<PERSON>č<PERSON>", "splitSizesLong#description": "", "splitSizesShort": "Razdvojiti", "splitSizesShort#description": "", "syncSizesLong": "Sinh. <PERSON>", "syncSizesLong#description": "", "syncSizesShort": "Sinh.", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Upozorenje:{{stopEmphasis}} <PERSON>va podešavanja uključuju nesačuvane promene koje se ne odražavaju na robota.", "exportingUnsavedChanges#description": "", "production": "Ovaj model aktivno radi na robotu. Uređivanje će odmah stupiti na snagu na terenu.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Nepoznati alarmi", "unknown#description": ""}, "almanac": {"unknown": "Nepoznati almanah", "unknown#description": "", "withName": "Almanah: {{name}}", "withName#description": ""}, "autofixing": "Greška automatskog popravljanja", "autofixing#description": "", "banding": {"disabled": "Ograničavanje pojasa je onemogućeno", "disabled#description": "", "enabled": "Ograničavanje pojasa je omoguc<PERSON>eno", "enabled#description": "", "none": "Buz ograničavanja pojasa", "none#description": "", "static": "(STATIČNO)", "static#description": "", "withName": "Pojas: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Učitavanje statusa prijave nije uspelo", "failed#description": "", "never": "Nikad nije bilo prijave", "never#description": "", "withTime": "Vreme prijave {{time}}", "withTime#description": ""}, "crop": {"summary": "Omo<PERSON><PERSON><PERSON><PERSON> {{enabled}}/{{total}} useva ({{pinned}} pinned)", "summary#description": ""}, "delivery": "<PERSON><PERSON><PERSON>", "delivery#description": "", "disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disconnected#description": "", "discriminator": {"unknown": "Nepoznati diskriminator", "unknown#description": "", "withName": "Diskriminator: {{name}}", "withName#description": ""}, "failed": "Učitavanje statusa robota nije uspelo", "failed#description": "", "failedShort": "<PERSON><PERSON> us<PERSON>", "failedShort#description": "", "implementation": "Implementacija", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventar", "inventory#description": "", "job": {"none": "<PERSON><PERSON> posla", "none#description": "", "withName": "Posao: {{name}}", "withName#description": ""}, "lasers": "<PERSON><PERSON>: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Životni vek:", "lifetime#description": "", "lifted": "<PERSON><PERSON> (pojačano)", "lifted#description": "", "loading": "Učitavanje", "loading#description": "", "location": {"known": "Lokacija: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Lokacija nepoznata", "unknown#description": ""}, "manufacturing": "Proizvodnja", "manufacturing#description": "", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Učitavanje modela", "modelLoading#description": "", "notArmed": "<PERSON><PERSON>", "notArmed#description": "", "off_season": "<PERSON>", "off_season#description": "", "offline": "Isključeno tokom {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P Nepoznato", "unknown#description": ""}, "poweringDown": "Isključivanje", "poweringDown#description": "", "poweringUp": "Uključivanje", "poweringUp#description": "", "pre_manufacturing": "Pred-proizvodnja", "pre_manufacturing#description": "", "stale": "Zastareo", "stale#description": "", "staleDescription": "Poslednja poznata vrednost. Robot je van mreže.", "staleDescription#description": "", "standby": "<PERSON><PERSON>", "standby#description": "", "thinning": {"disabled": "Razređivan<PERSON>", "disabled#description": "", "enabled": "Razređivanje omogućeno", "enabled#description": "", "none": "Bez razređivanja", "none#description": "", "withName": "Razređivanje: {{name}}", "withName#description": ""}, "today": {"none": "<PERSON><PERSON> p<PERSON> da<PERSON>", "none#description": ""}, "unknown": "Nepoznat status", "unknown#description": "", "updating": "Instaliranje ažuriranja", "updating#description": "", "version": {"values": {"unknown": "Nepoznata verzija", "unknown#description": "", "updateDownloading": "(preuz<PERSON>je {{version}})", "updateDownloading#description": "", "updateReady": "({{version}} spremna)", "updateReady#description": ""}}, "weeding": "Uklanjanje: {{crop}}", "weeding#description": "", "weedingDisabled": "Uklanjanje korova je onemogućeno", "weedingDisabled#description": "", "weedingThinning": "Uklanjanje i proređivanje: {{crop}}", "weedingThinning#description": "", "winterized": "Uskladišten za zimu", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "<PERSON><PERSON><PERSON> post<PERSON>ji", "exists#description": "", "unknownClass": "Nepoznata k<PERSON>a robota", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Ne pravi novu konfiguraciju", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}} Šablon", "templateForClass#description": "", "templateGeneric": "<PERSON><PERSON><PERSON>", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Nastavite samo ako konfiguracija za {{serial}} već postoji ili ako nameravate da je napravite ručno", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Napredna podešavanja <PERSON>", "advancedFormulaTitle#description": "", "formulaTitle": "Formula", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Predložena brzina se automatski smanjuje za unetu vrednost. Na primer, ako unesete 5%, predložena brzina od 1 mph biće smanjena na 0,95 mph", "description#description": "", "label": "Pomeraj brzine", "label#description": ""}, "decreaseSmoothing": {"description": "Prilagodite stopu smanjenja brzine. <PERSON>to je veća vrednost, to je verovatnije da će doći do fluktuacija na brzinomeru", "description#description": "", "label": "Izravnavanje usporavanja", "label#description": ""}, "increaseSmoothing": {"description": "Prilagodite stopu povećanja brzine. <PERSON>to je veća vrednost, to je verovatnije da će doći do fluktuacija na brzinomeru", "description#description": "", "label": "Izravnaavanje ubrzavanja", "label#description": ""}, "maxVelMph": {"description": "Unesite najveću brzinu kojom želite da se krećete. Preporučena brzina neće biti iznad ove vrednosti", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "minVelMph": {"description": "Unesite najmanju brzinu kojom želite da se krećete. Preporučena brzina neće biti ispod ove vrednosti", "description#description": "", "label": "Minimal<PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Ova vrednost je vaš željeni procenat uništenih korova", "description#description": "", "label": "Idealna stopa uništavanja", "label#description": ""}, "primaryRange": {"description": "Povećajte ovu vrednost ako želite da postignete željenu stopu uništavanja bez obzira na uticaj na brzinu", "description#description": "", "label": "Zelena <PERSON>đ<PERSON>", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON>i", "allRows#description": "", "row1": "1. red", "row1#description": "", "row2": "2. red", "row2#description": "", "row3": "3. red", "row3#description": ""}, "secondaryKillRate": {"description": "Ova vrednost je najmanji prihvatljivi procenat uništenih korova", "description#description": "", "label": "Minimalna stopa uništavanja", "label#description": ""}, "secondaryRange": {"description": "Povećajte ovu vrednost ako želite da imate lufta do dobijanja obaveštenja o niskoj brzini", "description#description": "", "label": "Žuta međuz<PERSON>", "label#description": ""}, "sync": "Sinh. sve redove", "sync#description": "", "warnings": {"admin": "Svaka promena ove procene brzine će se sinhronizovati sa svim trenutnim i budućim proizvodnim jedinicama.", "admin#description": "", "production": "Ova procena brzine se aktivno koristi na robotu. Ako je izmenite, biće odmah primenjena u polju.", "production#description": "", "protected": "Autor ovog profila je Carbon. Ništa ne možete da izmenite.", "protected#description": "", "unsavedChanges": "Nesačuvane promene. Pritisnite sačuvaj da biste primenili promene.", "unsavedChanges#description": ""}}, "slider": {"gradual": "<PERSON><PERSON><PERSON>", "gradual#description": "", "immediate": "Trenutno", "immediate#description": ""}, "visualization": {"targetSpeed": "<PERSON><PERSON><PERSON><PERSON>", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_few": "alarmi", "alarm_one": "alarm", "alarm_other": "alarmi", "fields": {"code": "Kod", "code#description": "", "description": "Opis", "description#description": "", "duration": {"name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"ongoing": "U TOKU", "ongoing#description": ""}}, "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identifier#description": "", "impact": {"name": "Uticaj", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Početak", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_few": "almanasi", "almanac_one": "almanah", "almanac_other": "almanasi", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_few": "zadaci robota", "assignment_one": "zadatak", "assignment_other": "zadaci robota", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Упутства", "instructions#description": ""}, "intervention#description": "", "intervention_few": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"groundPrep": "", "groundPrep#description": "", "laserWeed": "Lasersko plevljenje", "laserWeed#description": "", "unrecognized": "nepoznat tip ({{value}})", "unrecognized#description": ""}, "job_few": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Red za lasersko plevljenje", "laserWeedRow#description": ""}, "objective_few": "cilje<PERSON>", "objective_one": "cilj", "objective_other": "cilje<PERSON>", "states": {"acknowledged": "priznato", "acknowledged#description": "", "cancelled": "otkazano", "cancelled#description": "", "completed": "<PERSON>av<PERSON>š<PERSON>", "completed#description": "", "failed": "neus<PERSON>o", "failed#description": "", "inProgress": "u toku", "inProgress#description": "", "new": "novo", "new#description": "", "paused": "pau<PERSON><PERSON>", "paused#description": "", "pending": "na čekanju", "pending#description": "", "ready": "spremno", "ready#description": "", "unrecognized": "nepoznat status ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Задатак #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_few": "задаци", "task_one": "задатак", "task_other": "задаци"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_few": "профили категорије биљке", "categoryCollectionProfile_one": "профил категорије биљке", "categoryCollectionProfile_other": "профили категорије биљке", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON>", "disregard#description": "", "name": "Kategorije", "name#description": "", "requiredBaseCategories": "Morate imati tačno ove kategorije: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt#description": ""}, "metadata": {"capturedAt": "Snimljeno", "capturedAt#description": "", "categoryId": "ID kategorije", "categoryId#description": "", "imageId": "ID slike", "imageId#description": "", "internal": "", "internal#description": "", "pointId": "ID tačke", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "prediction": "", "prediction#description": "", "radius": "opseg", "radius#description": "", "updatedAt": "Primljeno", "updatedAt#description": "", "x": "x", "x#description": "", "y": "z", "y#description": ""}}, "configs": {"config#description": "", "config_few": "konfiguracije", "config_one": "konfiguracija", "config_other": "konfiguracije", "key#description": "", "key_few": "ključevi", "key_one": "ključ", "key_other": "ključevi", "template#description": "", "template_few": "šabloni konfiguracije", "template_one": "šablon konfiguracije", "template_other": "šabloni konfiguracije", "value#description": "", "value_few": "v<PERSON><PERSON>ti", "value_one": "vrednost", "value_other": "v<PERSON><PERSON>ti"}, "crops": {"categories": {"unknown": "Nepoznat usev", "unknown#description": ""}, "crop#description": "", "crop_few": "usevi", "crop_one": "usev", "crop_other": "usevi", "fields": {"confidence": {"fields": {"regionalImages": "Slike regiona:", "regionalImages#description": "", "totalImages": "Ukupne slike:", "totalImages#description": ""}, "name": "Pouzdanost", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archived#description": "", "unknown": "Nepoznata p<PERSON>zdanost", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "<PERSON><PERSON><PERSON><PERSON>", "notes#description": "", "pinned": "<PERSON><PERSON><PERSON><PERSON>", "pinned#description": "", "recommended": "Preporučeno", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_few": "kli<PERSON><PERSON>", "customer_one": "kli<PERSON><PERSON>", "customer_other": "kli<PERSON><PERSON>", "fields": {"emails": {"errors": {"formatting": "Po jedna e-adresa u svakom redu", "formatting#description": ""}, "name": "E-adrese", "name#description": ""}, "featureFlags": {"almanac": {"description": "Omogućava kartice Almanah i Diskriminator za robote (robot mora da podržava almanah i diskriminator)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Омогући картицу Профил категорије биљке за роботе", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Uključivanje funkcija omogućava beta funkcije za sve korisnike u okviru jednog klijenta", "description#description": "", "jobs": {"description": "Omogućava poslove (robot mora da podržava poslove)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Prikazuje novi vizuelni dizajn za metrike robota", "description#description": "", "name": "Redizajniranje metrike", "name#description": ""}, "name": "Uključivanje funkcija", "name#description": "", "off": "ISKLJUČENO", "off#description": "", "on": "UKLJUČENO", "on#description": "", "reports": {"description": "Omogućava karticu Izveštaji i funkcije na njoj", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Prikazuje prostorne podatke, uključujući toplotne mape i grafikone", "description#description": "", "name": "Prostorni podaci", "name#description": ""}, "summary": "{{enabled}}/{{total}} o<PERSON><PERSON><PERSON><PERSON><PERSON>", "summary#description": "", "unvalidatedMetrics": {"description": "Prikazuje beta metriku pre potvrđivanja polja za sertifikovanu metriku", "description#description": "", "name": "Beta metrika", "name#description": ""}, "velocityEstimator": {"description": "Omogućava prikazivanje i menjanje profila za procenu ciljne brzine (robot mora da podržava procenu ciljne brzine)", "description#description": "", "name": "Procena ciljne brzine", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Pokreće se u", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "<PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON>, iz<PERSON>š<PERSON><PERSON> će se pokretati svake nedelje sa sledećim podešavanjima za sve aktivne robote", "description#description": "", "name": "Nedeljni izveštaji", "name#description": ""}, "weeklyReportHour": "Pokrenuti u", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Pogled unazad", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Pokrenuti u", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_few": "diskriminatori", "discriminator_one": "diskriminator", "discriminator_other": "diskriminatori", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_few": "farme", "farm_one": "farma", "farm_other": "farme", "obstacle#description": "", "obstacle_few": "", "obstacle_one": "", "obstacle_other": "", "point#description": "", "point_few": "<PERSON><PERSON><PERSON>", "point_one": "tačka", "point_other": "<PERSON><PERSON><PERSON>", "zone#description": "", "zone_few": "zone", "zone_one": "zona", "zone_other": "zone"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_few": "definicije polja", "fieldDefinition_one": "definicija polja", "fieldDefinition_other": "definicije polja", "fields": {"boundary": "Granica polja", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "<PERSON><PERSON><PERSON>", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_few": "globalne vrednosti", "global_one": "globalna vrednost", "global_other": "globalne vrednosti", "values": {"plantProfileModelId": {"description": "Osnovni model koji koriste svi profili korisnika i administratora za '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) ID modela", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Datum / Vreme", "capturedAt#description": "", "geoJson": "Lokacija", "geoJson#description": "", "url": "Otvori sliku", "url#description": ""}, "image#description": "", "image_few": "slike", "image_one": "slika", "image_other": "slike"}, "jobs": {"job#description": "", "job_few": "<PERSON><PERSON><PERSON><PERSON>", "job_one": "posao", "job_other": "<PERSON><PERSON><PERSON><PERSON>"}, "lasers": {"fields": {"cameraId": "ID kamere", "cameraId#description": "", "error": {"values": {"false": "Nominalni", "false#description": ""}}, "installedAt": "Instaliran", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Nepoznat serijski broj", "unknown#description": ""}}, "lifetimeSec": "Vreme rada", "lifetimeSec#description": "", "powerLevel": "<PERSON>vo snage", "powerLevel#description": "", "removedAt": "Uklonjen", "removedAt#description": "", "rowNumber": "Red", "rowNumber#description": "", "totalFireCount": "<PERSON><PERSON><PERSON>", "totalFireCount#description": "", "totalFireTimeMs": "Vreme pucanja", "totalFireTimeMs#description": "", "warranty": {"name": "Garancija", "name#description": "", "values": {"expired": "Istekla", "expired#description": "", "hours": "Sati: {{installed}}/{{total}} ({{percent}} preostalo)", "hours#description": "", "hoursUnknown": "Sati: nepo<PERSON><PERSON>o", "hoursUnknown#description": "", "months": "Meseci: {{installed}}/{{total}} ({{percent}} preostalo)", "months#description": "", "monthsUnknown": "Meseci: Nepoznato", "monthsUnknown#description": "", "unknown": "Nepoznata garan<PERSON>ja", "unknown#description": ""}}}, "laser#description": "", "laser_few": "laseri", "laser_one": "laser", "laser_other": "laseri"}, "models": {"model#description": "", "model_few": "modeli", "model_one": "model", "model_other": "modeli", "none": "<PERSON>r. modela", "none#description": "", "p2p#description": "", "p2p_few": "P2P Modeli", "p2p_one": "P2P Model", "p2p_other": "P2P Modeli", "unknown": "Nepoznat model", "unknown#description": ""}, "pathPlanning": {"combinedTurnRadius": "", "combinedTurnRadius#description": "", "doHeadlandFirst": "", "doHeadlandFirst#description": "", "headlandPasses": "", "headlandPasses#description": "", "headlandWidth": "", "headlandWidth#description": "", "rowHeading": "", "rowHeading#description": "", "turnDirection": "", "turnDirection#description": ""}, "reportInstances": {"fields": {"authorId": "Kreirao/la", "authorId#description": "", "createdAt": "Objavljeno", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_few": "Kreiranja izveštaja", "run_one": "kreiranje izveštaja", "run_other": "Kreiranja izveštaja"}, "reports": {"fields": {"authorId": "Vlasnik", "authorId#description": "", "automateWeekly": {"name": "Automatizovano", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON><PERSON>", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_few": "izveštaji", "report_one": "izveštaj", "report_other": "izveštaji"}, "robots": {"classes": {"buds#description": "", "buds_few": "Budovi", "buds_one": "<PERSON>", "buds_other": "Budovi", "moduleValidationStations#description": "", "moduleValidationStations_few": "Stanice za validaciju modula", "moduleValidationStations_one": "Stanica za validaciju modula", "moduleValidationStations_other": "Stanice za validaciju modula", "reapersCarbon#description": "", "reapersCarbon_few": "<PERSON><PERSON>", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "<PERSON><PERSON>", "reapersCustomer_few": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_few": "Traktori", "rtcs_one": "Traktor", "rtcs_other": "Traktori", "simulators#description": "", "simulators_few": "<PERSON><PERSON><PERSON><PERSON>", "simulators_one": "Simulator", "simulators_other": "<PERSON><PERSON><PERSON><PERSON>", "slayersCarbon#description": "", "slayersCarbon_few": "<PERSON><PERSON>", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "<PERSON><PERSON>", "slayersCustomer#description": "", "slayersCustomer_few": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Nepoznata k<PERSON>a", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "<PERSON><PERSON>", "lasersOffline#description": "", "lifetimeArea": "Oblast životnog veka", "lifetimeArea#description": "", "lifetimeTime": "Trajanje životnog veka", "lifetimeTime#description": "", "localTime": "Lokalno vreme", "localTime#description": "", "reportedAt": "<PERSON><PERSON><PERSON><PERSON> lista", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Verzija softvera", "softwareVersion#description": "", "supportSlack": "Podrška Slack kanalu", "supportSlack#description": "", "targetVersion": "Ciljna verzija", "targetVersion#description": ""}, "robot#description": "", "robot_few": "roboti", "robot_one": "robot", "robot_other": "roboti", "unknown": "Nepoznat robot", "unknown#description": ""}, "users": {"activated": "Aktiviran", "activated#description": "", "fields": {"email": "E-pošta", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktiviranje", "name#description": "", "values": {"false": "NA ČEKANJU", "false#description": ""}}}, "operator#description": "", "operator_few": "operateri", "operator_one": "operater", "operator_other": "operateri", "role#description": "", "role_few": "<PERSON><PERSON><PERSON>", "role_one": "<PERSON><PERSON><PERSON>", "role_other": "<PERSON><PERSON><PERSON>", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Tehnički)", "carbon_tech#description": "", "farm_manager": "Upravnik farme", "farm_manager#description": "", "operator_advanced": "Operater (napredni)", "operator_advanced#description": "", "operator_basic": "Operater", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Nepoznat<PERSON> uloga", "unknown_role#description": ""}, "staff": "Osoblje", "staff#description": "", "user#description": "", "user_few": "korisnici", "user_one": "korisnik", "user_other": "korisnici"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_few": "procenjivači brzine", "velocityEstimator_one": "procenjivač brzine", "velocityEstimator_other": "procenjivači brzine"}, "weeds": {"categories": {"blossom": "Cveće", "blossom#description": "", "broadleaf": "<PERSON><PERSON><PERSON><PERSON>", "broadleaf#description": "", "fruit": "Plod", "fruit#description": "", "grass": "Trava", "grass#description": "", "offshoot": "Izdanak", "offshoot#description": "", "preblossom": "Pred-cvetanje", "preblossom#description": "", "purslane": "Тушт", "purslane#description": "", "runner": "<PERSON><PERSON><PERSON><PERSON>", "runner#description": "", "unknown": "Непознати коров", "unknown#description": ""}, "weed#description": "", "weed_few": "корови", "weed_one": "<PERSON><PERSON><PERSON><PERSON>", "weed_other": "корови"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add#description": "", "addLong": "До<PERSON><PERSON><PERSON> {{subject}}", "addLong#description": "", "apply": "<PERSON><PERSON>", "apply#description": "", "applyLong": "Primeni {{subject}}", "applyLong#description": "", "backLong": "назад на {{subject}}", "backLong#description": "", "cancel": "Откажи", "cancel#description": "", "cancelLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "cancelLong#description": "", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clear#description": "", "confirm": "Потврди", "confirm#description": "", "continue": "Настави", "continue#description": "", "copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "copyLong#description": "", "create": "Кр<PERSON><PERSON><PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} - креи<PERSON><PERSON><PERSON><PERSON>", "createdLong#description": "", "delete": "Избриши", "delete#description": "", "deleteLong": "Избриши {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} - избрисано", "deletedLong#description": "", "disableLong": "Он<PERSON><PERSON><PERSON>гу<PERSON>и {{subject}}", "disableLong#description": "", "discard": "Одбаци", "discard#description": "", "edit": "Измени", "edit#description": "", "editLong": "Измени {{subject}}", "editLong#description": "", "enableLong": "<PERSON><PERSON><PERSON>гу<PERSON>и {{subject}}", "enableLong#description": "", "exit": "<PERSON><PERSON><PERSON>z", "exit#description": "", "exitLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "exitLong#description": "", "goToLong": "Иди на {{subject}}", "goToLong#description": "", "invite": "Позови", "invite#description": "", "inviteLong": "Позови {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} - позвано", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON><PERSON>", "leaveUnchanged#description": "", "new": "Ново", "new#description": "", "newLong": "Нова(и) {{subject}}", "newLong#description": "", "next": "<PERSON><PERSON><PERSON>ć<PERSON>", "next#description": "", "pause": "<PERSON><PERSON>", "pause#description": "", "play": "<PERSON><PERSON><PERSON>", "play#description": "", "previous": "<PERSON><PERSON><PERSON><PERSON>", "previous#description": "", "ranLong": "{{subject}} - покренуто", "ranLong#description": "", "reload": "Ponovo učitaj", "reload#description": "", "resetLong": "Врати на почетно {{subject}}", "resetLong#description": "", "retry": "Pokušati ponovo", "retry#description": "", "run": "Покрени", "run#description": "", "runLong": "Покрени {{subject}}", "runLong#description": "", "save": "Сачувај", "save#description": "", "saveLong": "Сачувај {{subject}}", "saveLong#description": "", "saved": "Сачувано", "saved#description": "", "savedLong": "{{subject}} - сачувано", "savedLong#description": "", "search": "Претра<PERSON>и", "search#description": "", "searchLong": "Претра<PERSON>и {{subject}}", "searchLong#description": "", "selectAll": "Izaberi Sve", "selectAll#description": "", "selectLong": "Одабери {{subject}}", "selectLong#description": "", "selectNone": "<PERSON><PERSON><PERSON><PERSON>", "selectNone#description": "", "send": "Пошаљи", "send#description": "", "showLong": "Прика<PERSON>и {{subject}}", "showLong#description": "", "submit": "Пошаљи", "submit#description": "", "toggle": "Укљ./Искљ.", "toggle#description": "", "toggleLong": "Укљ./Искљ. {{subject}}", "toggleLong#description": "", "update": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update#description": "", "updated": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated#description": "", "updatedLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(а) {{subject}}", "updatedLong#description": "", "uploaded": "Отпремљено", "uploaded#description": "", "viewLong": "Прика<PERSON>и {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Aktivno", "active#description": "", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "critical#description": "", "default": "Podra<PERSON>mevan<PERSON>", "default#description": "", "degraded": "Smanjeno", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Greška", "error#description": "", "estopOff": "<PERSON><PERSON>", "estopOff#description": "", "estopOn": "E-zaustavljeno", "estopOn#description": "", "fast": "Brz<PERSON>", "fast#description": "", "few": "Malo", "few#description": "", "good": "Dobro", "good#description": "", "hidden": "Sakriveno", "hidden#description": "", "high": "<PERSON><PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Neaktivno", "inactive#description": "", "interlockSafe": "Pucanje dozvoljeno", "interlockSafe#description": "", "interlockUnsafe": "Pucanje spre<PERSON>eno", "interlockUnsafe#description": "", "large": "<PERSON><PERSON><PERSON>", "large#description": "", "laserKeyOff": "Zak<PERSON><PERSON><PERSON><PERSON>", "laserKeyOff#description": "", "laserKeyOn": "Umetnut", "laserKeyOn#description": "", "liftedOff": "Spuštena", "liftedOff#description": "", "liftedOn": "Podignuta", "liftedOn#description": "", "loading": "Учитавање", "loading#description": "", "low": "<PERSON><PERSON>", "low#description": "", "majority": "Većina", "majority#description": "", "medium": "Srednja", "medium#description": "", "minority": "<PERSON><PERSON><PERSON>", "minority#description": "", "name": "Ime", "name#description": "", "no": "Ne", "no#description": "", "none": "<PERSON><PERSON><PERSON>", "none#description": "", "offline": "<PERSON>", "offline#description": "", "ok": "OK", "ok#description": "", "passable": "", "passable#description": "", "poor": "<PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Napredak", "progress#description": "", "serial": "Serijski", "serial#description": "", "slow": "Sporo", "slow#description": "", "small": "Malo", "small#description": "", "sparse": "<PERSON><PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_few": "Tipovi", "type_one": "Tip", "type_other": "Tipovi", "unknown": "Nepoznato", "unknown#description": "", "waterProtectNormal": "Normalna vlaga", "waterProtectNormal#description": "", "waterProtectTriggered": "Voda detektovana", "waterProtectTriggered#description": "", "yes": "Da", "yes#description": ""}, "form": {"booleanType": "Мора бити Булов израз", "booleanType#description": "", "copyConfigFrom": "<PERSON><PERSON><PERSON> konfiguraciju iz...", "copyConfigFrom#description": "", "integerType": "Мора бити цео број", "integerType#description": "", "maxLessThanMin": "<PERSON><PERSON><PERSON><PERSON> mora biti veći od <PERSON>a", "maxLessThanMin#description": "", "maxSize": "Не могу да премаше {{limit}} карактера", "maxSize#description": "", "minGreaterThanMax": "Min. mora biti manji od maks.", "minGreaterThanMax#description": "", "moveDown": "Помери надоле", "moveDown#description": "", "moveUp": "Помери нагоре", "moveUp#description": "", "noOptions": "Нема опција", "noOptions#description": "", "numberType": "Мора бити број", "numberType#description": "", "optional": "(опционално)", "optional#description": "", "required": "Обавезно", "required#description": "", "stringType": "Мора бити ниска", "stringType#description": ""}, "lists": {"+3": "{{b}} и {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} и {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Учитај више", "loadMore#description": "", "noMoreResults": "Више нема резултата", "noMoreResults#description": "", "noResults": "Нема резултата", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Макс.", "max#description": "", "min": "<PERSON><PERSON><PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Просечан радијус усева", "avgCropSizeMm#description": "", "avgSpeedMph": "Просечна брзина путовања", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Prosečno vreme pucanja", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Prosečno vreme pucanja (neciljano)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Просечан радијус корова", "avgWeedSizeMm#description": "", "bandingConfigName": "Конфигурација груписања", "bandingConfigName#description": "", "bandingEnabled": "Гру<PERSON><PERSON><PERSON><PERSON><PERSON>е", "bandingEnabled#description": "", "bandingPercentage": "Груписани проценат", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Просечна брзина покривености", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "<PERSON>ус<PERSON>ина усева", "cropDensitySqFt#description": "", "distanceWeededMeters": "Даљина третирања корова", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Задржани корови", "keptCrops#description": "", "killedWeeds": "Уништени корови", "killedWeeds#description": "", "missedCrops": "Пропуштени усеви", "missedCrops#description": "", "missedWeeds": "Пропуштени корови", "missedWeeds#description": "", "notThinning": "Усеви без проређивања", "notThinning#description": "", "notWeeding": "Корови без плевљења", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Efikasnost operatera", "operatorEffectiveness#description": "", "overallEfficiency": "Укупан учинак", "overallEfficiency#description": "", "skippedCrops": "Занемарени усеви", "skippedCrops#description": "", "skippedWeeds": "Занемарени корови", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Ciljano vreme za uklanjanje korova", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Проређени усеви", "thinnedCrops#description": "", "thinningEfficiency": "Учинак проређивања", "thinningEfficiency#description": "", "timeEfficiency": "Опера<PERSON>ивна ефиксност", "timeEfficiency#description": "", "totalCrops": "Пронађено усева (укупно)", "totalCrops#description": "", "totalWeeds": "Пронађено корова", "totalWeeds#description": "", "totalWeedsInBand": "Пронађено корова (у зони)", "totalWeedsInBand#description": "", "uptimeSeconds": "Време рада", "uptimeSeconds#description": "", "validCrops": "Pronađeni <PERSON>", "validCrops#description": "", "weedDensitySqFt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> корова", "weedDensitySqFt#description": "", "weedingEfficiency": "Учинак уклањања корова", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Време уклањања корова", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Врста корова: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Врста корова: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Врста корова: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Врста корова: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Ovo se izračunava pre proređivanja ako je proređivanje bilo omogućeno", "avgCropSizeMm#description": "", "bandingConfigName": "Vaš poslednji izabrani profil za spajanje", "bandingConfigName#description": "", "crop": "V<PERSON>ša poslednja izabrana kultura", "crop#description": "", "cropDensitySqFt": "Ovo se izračunava pre proređivanja ako je proređivanje bilo omogućeno", "cropDensitySqFt#description": "", "keptCrops": "Procenjeni broj useva koji se očuvaju nakon proređivanja", "keptCrops#description": "", "killedWeeds": "LaserWeeder je identifikovao predmet kao korov i pucao u njega", "killedWeeds#description": "", "missedCrops": "Usev je bio označen za proređivanje, ali je propušten. Uobičajeni razlozi uključuju: prekoračenje brzine, van dometa ili sistemsku grešku.", "missedCrops#description": "", "missedWeeds": "<PERSON><PERSON> je <PERSON> ident<PERSON>, ali <PERSON>. Uobičajeni razlozi uključuju: prekoračenje brzine, van dometa ili sistemsku grešku.", "missedWeeds#description": "", "operatorEffectiveness": "Pokazuje koliko se stvarna brzina kretanja podudara sa ciljanom brzinom koju preporučuje procenjivač brzine", "operatorEffectiveness#description": "", "overallEfficiency": "(Učinak plevljenja + Učinak proređivanja) / 2, ako vršite i plevljenje i proređivanje", "overallEfficiency#description": "", "skippedCrops": "Usev je namerno preskočen tokom proređivanja. Uobičajeni razlozi uključuju: onemogućeno brzo pod<PERSON>je, van opsega ili traka za kapanje u blizini.", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON> je namerno preskočen. Uobičajeni razlozi uključuju: onemogućeno u brzom podešavanju ili van opsega.", "skippedWeeds#description": "", "thinningEfficiency": "(Proređeni usevi + Oč<PERSON><PERSON> usevi) / Procenjeni pronađeni usevi × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(Aktivno radno vreme / Vreme uključivanja) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "Puno vreme koliko je LaserWeeder bio uključen. Uključuje i kada je u režimu pripravnosti i/ili podignuto.", "uptimeSeconds#description": "", "weedDensitySqFt": "Procenjena količina pronađ<PERSON> k<PERSON> (ukupno) / Pokrivenost", "weedDensitySqFt#description": "", "weedingEfficiency": "(Uniš<PERSON> korov / <PERSON>rov pronađen u traci) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Količina vremena koju je LaserWeeder aktivno plevio ili proređivao", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Efikasnost brzine", "operatorEffectiveness#description": "", "timeEfficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "timeEfficiency#description": "", "totalWeeds": "<PERSON><PERSON><PERSON><PERSON> (ukupno)", "totalWeedsInBand": "Pronađ<PERSON> korov (u traci)", "totalWeedsInBand#description": "", "uptimeSeconds": "Vreme uključivanja", "uptimeSeconds#description": "", "validCrops": "Procena broja prona<PERSON><PERSON>", "validCrops#description": "", "weedingUptimeSeconds": "Aktivno radno vreme", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Покривеност", "coverage#description": "", "field": "Поље", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Учинак", "performance#description": "", "speed": "Брзина", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Коришћење", "usage#description": ""}, "metric#description": "", "metric_few": "metrike", "metric_one": "metrika", "metric_other": "metrike", "spatial": {"heatmapWarning": "per ~{{area}} 'block'", "heatmapWarning#description": "", "metrics": {"altitude": "Nadmorska visina", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Blokada", "interlock#description": "", "keptCropDensity": "Zadrž<PERSON> gustina <PERSON>", "keptCropDensity#description": "", "laserKey": "Laserski ključ", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "<PERSON>dn<PERSON> cil<PERSON>e brzine", "speed#description": "", "speedTargetMinimum": "Prosečna ciljna br<PERSON>a (minimalna)", "speedTargetMinimum#description": "", "speedTargetRow1": "Prosečna ciljna brzina (1. red)", "speedTargetRow1#description": "", "speedTargetRow2": "Prosečna ciljna brzina (2. red)", "speedTargetRow2#description": "", "speedTargetRow3": "Prosečna ciljna brzina (3. red)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Prosečna ciljna brzina", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Razređivanje", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Vreme", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Taštita vode", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Plevljen<PERSON> k<PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Izabrano", "selected#description": "", "showAll": "<PERSON><PERSON><PERSON><PERSON> sve {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_few": "vati", "WLong_one": "vat", "WLong_other": "vati", "ac": "ac", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_few": "jutara", "acLong_one": "jutro", "acLong_other": "jutara", "acres#description": "", "ccwLong": "", "ccwLong#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "cwLong": "", "cwLong#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_few": "dana", "dLong_one": "dan", "dLong_other": "dana", "day#description": "", "days#description": "", "deg": "", "deg#description": "", "deg_long": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_few": "stope", "ftLong_one": "stopa", "ftLong_other": "stope", "h": "h", "h#description": "", "hLong#description": "", "hLong_few": "<PERSON><PERSON><PERSON>", "hLong_one": "čas", "hLong_other": "<PERSON><PERSON><PERSON>", "ha": "ha", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_few": "hektara", "haLong_one": "<PERSON>ktar", "haLong_other": "hektara", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_few": "metara", "mLong_one": "metar", "mLong_other": "metara", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_few": "minuta", "minLong_one": "minut", "minLong_other": "minuta", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "me", "month#description": "", "monthLong#description": "", "monthLong_few": "meseci", "monthLong_one": "mesec", "monthLong_other": "meseci", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_few": "sekundi", "sLong_one": "sekunda", "sLong_other": "sekundi", "seconds#description": "", "watts#description": "", "week": "W", "week#description": "", "weekLong#description": "", "weekLong_few": "<PERSON><PERSON><PERSON>", "weekLong_one": "<PERSON><PERSON><PERSON>", "weekLong_other": "<PERSON><PERSON><PERSON>", "yd#description": "", "year": "gd", "year#description": "", "yearLong#description": "", "yearLong_few": "godine", "yearLong_one": "godina", "yearLong_other": "godine"}}, "views": {"admin": {"alarms": {"allowWarning": "Dodavanje kodova na listu dozvoljenih omogućiće alarmima da ping podržavaju Slack kanale", "allowWarning#description": "", "blockWarning": "Dodavanje kodova na listu blokiranja sprečiće alarme da ping podržavaju Slack kanale", "blockWarning#description": "", "lists": "liste", "lists#description": "", "title": "Globalna lista dozvoljenih alarma", "title#description": "", "titleAllow": "Lista dozvoljenih alarma", "titleAllow#description": "", "titleBlock": "Lista blokiranih alarma", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Podesi", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> na <PERSON>-u, <tt>{row1,row2,row3}<tt> na Slayer-u", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_few": "operacije", "operation_one": "operacija", "operation_other": "operacije", "operationsCount": "Operacija ({{count}})", "operationsCount#description": "", "operationsHint": "Izaberite čvor u konfiguracionoj šemi da biste dodali operaciju.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_few": "pronađ<PERSON> {{count}} g<PERSON><PERSON><PERSON>", "encounteredErrors_one": "pronađ<PERSON> {{count}} g<PERSON><PERSON><PERSON>", "encounteredErrors_other": "pronađ<PERSON> {{count}} g<PERSON><PERSON><PERSON>", "noChanges": "bez promena", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_few": "<PERSON><PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedKeys_one": "a<PERSON><PERSON><PERSON> {{count}} k<PERSON><PERSON><PERSON>", "updatedKeys_other": "<PERSON><PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "outcomes": {"failure": "Neuspeh", "failure#description": "", "partial": "<PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "partial#description": "", "success": "Uspeh", "success#description": ""}, "title": "Grupne konfiguracije", "title#description": ""}, "clearCaches": {"action": "Osvežite keš memoriju", "action#description": "", "description": "Problemi Pokušajte prvo da osvežite keš memoriju robota.", "description#description": ""}, "warnings": {"global": "Izmena ove konfiguracije će uticati na podrazumevane vrednosti i preporuke za sve trenutne i buduće {{class}}", "global#description": "", "notSimon": "<PERSON>i nisi <PERSON> il<PERSON>, tako da verovatno ne bi trebalo da uređuješ ovo... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "<PERSON>ledec<PERSON>e promene još uvek nisu sinhronizovane sa {{serial}}:", "description#description": "", "title": "Nesinh. kl<PERSON>i", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Obriši keš memorije", "action#description": "", "description": "Briše interne keš memorije za Ops Center. Ovo **će** usporiti neke upite u kratkom roku, ali **možda** reši probleme sa zaglavljenim zastarelim podacima", "description#description": "", "details": "Pritisnite ovo ako ste ručno izmenili korisničke dozvole u Auth0 (ne preko Operations Center-a) ili ste napravili izmene u integraciji treće strane kao što su Stream ili Slack koje se ne odražavaju.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Opcije na ovoj stranici će uticati na rad Ops Center uživo u proizvodnji.", "global#description": "", "notPortalAdmin": "<PERSON>i niste <PERSON> il<PERSON>, tako da verovatno ne bi trebalo da uređujete ovo... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Podrška za Slack kanal treba da počne sa \"#\": npr., \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Dodeliti traktorima", "orchestrateView#description": "", "showPivotHistory": "Prikaži istoriju <PERSON>", "showPivotHistory#description": ""}, "fetchFailed": "Učitavanje podataka o lokaciji nije uspelo", "fetchFailed#description": "", "goLive": "ažuriranje uživo", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON><PERSON>", "hideRows#description": "", "historyWidthUnits": "", "historyWidthUnits#description": "", "jobDetails": {"assignmentsFailed": "Preuzimanje zadatka nije uspelo, ž<PERSON><PERSON> li da pokušate ponovo?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Posao više neće moći da se dodeli traktorima i mora ponovo da se kreira.", "description#description": ""}, "customer": {"unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Klijent: {{name}}", "withName#description": ""}, "farm": {"unknown": "<PERSON><PERSON> ne<PERSON>a", "unknown#description": "", "withName": "Farma: {{name}}", "withName#description": ""}, "field": {"unknown": "Polje nepoznato", "unknown#description": "", "withName": "Polje: {{name}}", "withName#description": ""}, "jobFinished": "<PERSON><PERSON><PERSON> u {{time}}", "jobFinished#description": "", "jobStarted": "Posao započet u {{time}}", "jobStarted#description": "", "openInFarmView": "Otvori u prikazu farme", "openInFarmView#description": "", "state": "Stanje: {{state}}", "state#description": "", "type": "Vrsta posla: {{type}}", "type#description": ""}, "lastPolled": "Datum poslednjeg prikupljanja podataka", "lastPolled#description": "", "live": "Uživo", "live#description": "", "objectiveFromOtherJob": "Cilj sa <PERSON>og posla", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON><PERSON><PERSON> reda {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Farme", "farms#description": "", "tractors": "Traktori", "tractors#description": ""}, "showRows": "<PERSON><PERSON><PERSON><PERSON> redove", "showRows#description": "", "stalePivots": "Informacije o osovini su možda zastarele", "stalePivots#description": "", "suggestedAssignments": "Predloženi zadaci", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"createFarm": "", "createFarm#description": "", "exportField": "", "exportField#description": "", "hideThesePoints": "<PERSON>k<PERSON><PERSON> ove ta<PERSON>", "hideThesePoints#description": "", "importField": "", "importField#description": "", "onlyShowSelected": "Prikaži samo oda<PERSON>ne", "onlyShowSelected#description": "", "showAllPoints": "Prikaži sve tačke", "showAllPoints#description": "", "showThesePoints": "Prikaži ove ta<PERSON>", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Granica", "boundary#description": "", "center": "Centar", "center#description": "", "centerPivot": "Centralni osovinski položaj", "centerPivot#description": "", "endpointId": "ID krajnje tačke", "endpointId#description": "", "holes": "Rupe", "holes#description": "", "length": "Dužina", "length#description": "", "plantingHeading": "<PERSON><PERSON><PERSON>", "plantingHeading#description": "", "point": "Tačka", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "exportField": {"warning": "", "warning#description": ""}, "farm": "<PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Bez fiksiranja", "none#description": "", "rtkFixed": "RTK finksiran", "rtkFixed#description": "", "rtkFloat": "Plutajući RTK", "rtkFloat#description": "", "unknown": "Nepoznat tip popravke", "unknown#description": ""}, "importField": {"importFailed": "", "importFailed#description": "", "importFailedNameCollision": "", "importFailedNameCollision#description": "", "importFailedNoFields": "", "importFailedNoFields#description": "", "importSuccessful": "", "importSuccessful#description": "", "notAnExportWarning": "", "notAnExportWarning#description": "", "oldExportWarning": "", "oldExportWarning#description": ""}, "selectionPanel": {"allPoints": "<PERSON><PERSON>", "allPoints#description": "", "boundary": "Granica", "boundary#description": "", "center": "Centar", "center#description": "", "centerPivot": "Centralni osovinski položaj", "centerPivot#description": "", "endpointId": "ID krajnje tačke", "endpointId#description": "", "holes": "Rupe", "holes#description": "", "length": "Dužina", "length#description": "", "plantingHeading": "<PERSON><PERSON><PERSON>", "plantingHeading#description": "", "point": "Tačka", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "<PERSON><PERSON><PERSON> bez imena <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Granica farme", "farmBoundary#description": "", "field": "<PERSON><PERSON>", "field#description": "", "headland": "Vrt", "headland#description": "", "obstacle": "Preprek<PERSON>", "obstacle#description": "", "privateRoad": "<PERSON><PERSON><PERSON><PERSON> put", "privateRoad#description": "", "unknown": "Zona nepoznatog tipa", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "<PERSON><PERSON> treba da ima tačno dve tačke", "exactlyTwoPoints#description": "", "wrongFieldType": "<PERSON><PERSON> \"{{field}}\" bi trebalo da bude {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geometrija treba da bude tipa {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON treba da bude predmet", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Nema robota na mreži", "empty#description": ""}, "title": "<PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Прикажи историју промена", "open#description": "", "title": "Историја промена", "title#description": ""}, "errors": {"failed": "Učitavanje stabla konfiguracije nije uspelo", "failed#description": ""}, "onlyChanged": "Prikaži samo Promenjeno", "onlyChanged#description": ""}, "errors": {"empty": "<PERSON><PERSON>dan robot nije dodeljen", "empty#description": ""}, "hardware": {"errors": {"old": "Robot ne prijavljuje kompjuterske serije (verovatno prestare)", "old#description": ""}, "fields": {"hostname": "Ime hosta", "hostname#description": ""}, "installedVersion": "Instalirana verzija:", "installedVersion#description": "", "ready": {"name": "Status ažuriranja softvera", "name#description": "", "values": {"false": "Preuzimanje u toku...", "false#description": "", "installed": "Instalirano", "installed#description": "", "true": "Spremno!", "true#description": ""}}, "tabs": {"computers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "computers#description": "", "versions": "Verzije", "versions#description": ""}, "targetVersion": "Ciljna verzija", "targetVersion#description": "", "title": "Hardver", "title#description": "", "updateHistory": "Istorija ažuriranja verzija <0>coming soon™️</0>", "updateHistory#description": ""}, "history": {"borders": "Granice", "borders#description": "", "errors": {"invalidDate": "Izaberite važeći period", "invalidDate#description": "", "noJobs": "Nema prijavljenih poslova u izabranom opsegu", "noJobs#description": "", "noMetrics": "<PERSON>ema p<PERSON>vl<PERSON><PERSON> p<PERSON>tel<PERSON>", "noMetrics#description": ""}, "moreMetrics": "Pogledati više pokazatelja", "moreMetrics#description": "", "navTitle": "Istorija", "navTitle#description": "", "placeholder": "Izaberite posao ili datum da biste videli podatke", "placeholder#description": "", "points": "<PERSON><PERSON>", "points#description": "", "warnings": {"beta": {"description": "Pokazatelji koji čekaju na validaciju su prikazani plavom bojom", "description#description": ""}, "ongoing": "Pokazatelji za ovaj datum još nisu konačni", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "Definicija", "definition#description": "", "dynamic": "Dinamično", "dynamic#description": "", "dynamicDisabled": "(Dinamično povezivanje je onemogućeno u konfiguraciji)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON><PERSON>", "rows#description": "", "static": "Statično", "static#description": "", "type": "Tip", "type#description": "", "unknown": "Nepoznato <PERSON>", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Verzija", "version#description": ""}, "config": {"changes#description": "", "changes_few": "{{count}} <PERSON><PERSON><PERSON>a", "changes_one": "{{count}} <PERSON><PERSON>a alma<PERSON>a", "changes_other": "{{count}} <PERSON><PERSON><PERSON>a", "cpt": "Prag točke obrezivanja", "cpt#description": "", "default": "(ZADATA VREDNOST: {{value}})", "default#description": "", "wpt": "Prag tač<PERSON> koro<PERSON>", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON> levi", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON>", "backRight#description": "", "frontLeft": "<PERSON><PERSON><PERSON> levi", "frontLeft#description": "", "frontRight": "<PERSON><PERSON><PERSON>", "frontRight#description": "", "title": "Koderi za točkove", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Učitavanje rezimea robota nije uspelo", "failed#description": "", "lasers": {"disabled#description": "", "disabled_few": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "disabled_one": "{{count}} One<PERSON><PERSON><PERSON><PERSON> laser", "disabled_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "row": "Red {{row}}", "row#description": ""}, "machineHealth": "Zdravlje mašina", "machineHealth#description": "", "navTitle": "<PERSON><PERSON><PERSON>", "navTitle#description": "", "safetyRadius": {"driptape": "Kapalica", "driptape#description": "", "title": "Sigurnosni radi<PERSON>", "title#description": ""}, "sections": {"management": "Upravljanje", "management#description": "", "software": "Softver", "software#description": ""}, "supportLinks": {"chipChart": "Graf<PERSON><PERSON> č<PERSON>", "chipChart#description": "", "datasetVisualization": "Vizuelizacija skupa podataka", "datasetVisualization#description": "", "title": "Veze za podršku", "title#description": ""}}, "support": {"carbon": "Carbon podrška", "carbon#description": "", "chatMode": {"legacy": "Star<PERSON>", "legacy#description": "", "new": "Novo ćaskanje", "new#description": ""}, "errors": {"failed": "Učitavanje poruke nije uspelo", "failed#description": "", "old": {"description": "{{serial}} k<PERSON><PERSON> verzi<PERSON> soft<PERSON>a {{version}}. <PERSON><PERSON> da bude {{target}} da biste koristili ćaskanje za pod<PERSON>šku.", "description#description": "", "title": "Nedovoljna verzija robota", "title#description": ""}}, "localTime": "Lokalno vreme: {{time}}", "localTime#description": "", "navTitle": "Podrška", "navTitle#description": "", "toCarbon": "Poruka za $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Poruka za $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} r<PERSON> <PERSON>. Operater neće primiti poruku dok robot ne uspostavi vezu.", "description#description": "", "title": "<PERSON> van mreže", "title#description": ""}}}, "toggleable": {"internal": "<PERSON>ni", "internal#description": ""}, "uploads": {"errors": {"empty": "<PERSON><PERSON>", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "otherRobots": "<PERSON><PERSON> roboti ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Označeni roboti", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "Kartice", "cards#description": "", "table": "<PERSON><PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_few": "<PERSON><PERSON>", "fleetView_one": "filter", "fleetView_other": "<PERSON><PERSON>", "tableOnly": "Neke kolone su dostupne samo u prikazu tabele", "tableOnly#description": ""}}, "knowledge": {"title": "Baza znanja", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Zatvoren", "closed#description": "", "description": "Status posla", "description#description": "", "open": "Otvoren", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Procenjena metrika polja", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Naš model k<PERSON>ti eksperimentalne podatke o usevima koji mogu sadržati nepreciznosti. Neprestano usavršavamo njegovu pouzdanost.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Metrika za učinak i mašinu", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Prevucite datoteke ovde sa USB-a (ili na drugi način)", "drop#description": "", "file#description": "", "file_few": "datote<PERSON>", "file_one": "dato<PERSON><PERSON>", "file_other": "datote<PERSON>", "ingestDescription": "Zaposleni kompanije Carbon treba da koriste Ingest uslugu", "ingestDescription#description": "", "ingestLink": "Otpremi na Ingest", "ingestLink#description": "", "select": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "select#description": "", "title": "Otpremi", "title#description": "", "upload": "Otpremi na Carbon", "upload#description": "", "uploading": "Otpremanje {{subject}}...", "uploading#description": ""}, "reports": {"explore": {"graph": "Grafikon", "graph#description": "", "groupBy": "Grupisati po", "groupBy#description": "", "title": "Istražiti", "title#description": ""}, "scheduled": {"authorCarbonBot": "Carbon Bot", "authorCarbonBot#description": "", "authorUnknown": "Nepoznat autor", "authorUnknown#description": "", "automation": {"customerReports": "Izveštaji klijenata", "customerReports#description": "", "errorTitle": "Nevažeći automatski izveštaj", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "<PERSON><PERSON><PERSON> klijent nije i<PERSON>n", "none#description": ""}}, "reportDay": {"errors": {"none": "<PERSON><PERSON><PERSON> dan nije i<PERSON>n", "none#description": ""}, "name": "Dan izveštaja", "name#description": ""}, "reportEmails": {"errors": {"none": "<PERSON><PERSON>", "none#description": ""}, "name": "E-pošte klijenata", "name#description": ""}, "reportHour": {"errors": {"none": "Vreme nije izabrano", "none#description": ""}, "name": "Vreme izveštaja", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON><PERSON> defini<PERSON> pregled unazad", "none#description": ""}, "name": "Period retrospektive izveštaja", "name#description": ""}, "reportTimezone": {"errors": {"none": "Nijedna vremenska zona nije izabrana", "none#description": ""}, "name": "Vremenska zona izveštaja", "name#description": ""}, "warningDescription": "<PERSON><PERSON><PERSON> se svakog {{day}} u {{hour}} u {{timezone}} sa retrospektivom od {{lookback}} dana za sve aktivne {{customer}} robote.", "warningDescription#description": "", "warningTitle": "Ovo je automatski izveštaj!", "warningTitle#description": ""}, "byline": "Sastavio/la {{author}}", "byline#description": "", "editor": {"columnsHidden": "Skrivene kolone", "columnsHidden#description": "", "columnsVisible": "Vidljive kolone", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_few": "Upozorenje: postoje {{count}} drugi izveštaji sa ovim imenom", "duplicateNames_one": "upozorenje: postoji drugi izveštaj sa ovim imenom", "duplicateNames_other": "Upozorenje: postoje {{count}} drugi izveštaji sa ovim imenom", "fields": {"automateWeekly": "Automatski nedeljno", "automateWeekly#description": "", "name": "Naziv izveštaja", "name#description": "", "showAverages": "Prikaži prosečne vrednosti", "showAverages#description": "", "showTotals": "Prikaži ukupne vrednosti", "showTotals#description": ""}}, "errors": {"noReport": "Izveštaj ne postoji ili nemate pristup", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} će biti trajno izbrisana.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "<PERSON><PERSON> ov<PERSON>š<PERSON> da izbrišete {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-poruka neće biti ponovo poslata", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Izveštaj će biti poslat na ove adrese e-pošte", "publishEmailsHelperNew#description": ""}, "runAgain": "Kreiraj ponovo", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Izaberite jednu ili više kolona", "noColumns#description": "", "noEndDate": "Izaberite datum kraja", "noEndDate#description": "", "noRobots": "Izaberite jednog ili više robota", "noRobots#description": "", "noStartDate": "Izaberite datum poč<PERSON>ka", "noStartDate#description": ""}, "fields": {"average": "Prosek", "average#description": "", "averageShort": "PROS", "averageShort#description": "", "date": "Datum", "date#description": "", "group": "Serijski broj/Datum", "group#description": "", "groupJob": "Serijski broj/posao", "groupJob#description": "", "mixed": "(<PERSON><PERSON><PERSON>)", "mixed#description": "", "total": "Ukupno", "total#description": "", "totalShort": "ZBIR", "totalShort#description": ""}, "unknownReport": "Nepoznat izveštaj", "unknownReport#description": ""}, "title": "Programirano", "title#description": "", "toLine": "za {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "<PERSON><PERSON> metrike", "all#description": "", "select": "Izaberite metrike", "select#description": ""}, "robotsLabel": {"all": "<PERSON><PERSON> <PERSON>i", "all#description": "", "none": "<PERSON><PERSON>a", "none#description": "", "select": "<PERSON><PERSON><PERSON><PERSON> robote", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "korisničko ime i lozinka", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "nepoznat provajder", "unknown#description": ""}, "cards": {"account": "<PERSON><PERSON>", "account#description": "", "advanced": "Napredna", "advanced#description": "", "localization": "Lokalizacija", "localization#description": ""}, "delete": {"deleteAccount": "Izbriši nalog", "deleteAccount#description": "", "dialog": {"description": "UPOZORENJE: <PERSON><PERSON> radnja ne može da se opozove. Svi podaci će biti izbrisani.", "description#description": ""}}, "fields": {"language": "<PERSON><PERSON><PERSON>", "language#description": "", "measurement": {"name": "<PERSON><PERSON> j<PERSON>", "name#description": "", "values": {"imperial": "Imperijalni (in, mph, jutara, farenhajta)", "imperial#description": "", "metric": "Metrički (mm, km/h, hektara, celsijusa)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "<PERSON><PERSON><PERSON><PERSON> se", "logOut#description": "", "title": "Podešavanja", "title#description": "", "version": "Carbon Ops Center verzija {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Korisnik ne postoji ili nemate dozvolu da ga vidite.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Upravljati korisnikom u Auth0", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Uloga i dozvole", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Izvođa<PERSON><PERSON> radova", "contractors#description": ""}}}}