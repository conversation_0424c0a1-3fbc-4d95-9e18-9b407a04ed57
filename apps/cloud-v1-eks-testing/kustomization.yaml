apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: testing
resources:
  # secrets
  - veselka-secret-sealed.yaml
  - veselka-analytics-secret-sealed.yaml
  - web-socket-server-secret-sealed.yaml
  - idm-secret-sealed.yaml
  - portal-secret-sealed.yaml
  - robot-syncer-secret-sealed.yaml
  - rtc-jobs-secret-sealed.yaml
  - rtc-locator-secret-sealed.yaml
  # apps
  - ../base/ingest
  - ../base/veselka
  - ../base/veselka-analytics
  - ../base/rtc-ui
  - ../base/web-socket-server
  - ../base/rtc-jobs
  - ../base/rtc-locator
  - ../base/idm
  - ../base/version-metadata
  - ../base/portal
  - ../base/robot-syncer
  - ../base/cloud-image
  - ../base/celery-exporter
  # ingress
  - veselka-ingress.yaml
  - rtc-ingress.yaml
  - rtc-grpc-ingress.yaml
  - idm-ingress.yaml
  - ingest-ingress.yaml
  - version-metadata-ingress.yaml
  - portal-ingress.yaml
  - portal-grpc-ingress.yaml
  - portal-webhook-ingress.yaml # note: in production, this is part of the slackbot ingress
  - rosy-grpc-ingress.yaml

images:
  - name: ghcr.io/carbonrobotics/rtc/ui
    newName: ghcr.io/carbonrobotics/cloud/rtc-ui

configMapGenerator:
  - name: veselka-cm
    literals:
      - GUNICORN_WORKERS=9
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_ROLE_LABEL_ID=rol_vK6Rj2gKfkeK1Ovn
      - AUTH0_ROLE_REVIEW_ID=rol_A52JjWZ9zpepRDdx
      - AUTH0_ROLE_ADMIN_ID=rol_cnVwrzu31d1AYl1B
      - VESELKA_IMAGE_SERVICE_URL=cloud-image.testing.svc.cluster.local
      - VESELKA_MODE=production
      - VESELKA_GET_TASK_PROBABILITIES_CAPTURED_AT_LIMIT_DAYS="365"
      - REDIS_URL=redis://cloud-v1-test-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - VESELKA_PREDICT_RESULT_QUEUE=veselka-predict-result-queue-test.fifo
      - VESELKA_INTERACTIVE_PREDICT_QUEUE=veselka-predict-queue-test.fifo
      - VESELKA_PREDICT_QUEUE=veselka-batch-predict-queue-test.fifo
      - VESELKA_COMPARISON_PREDICT_QUEUE=veselka-comparison-predict-queue-test.fifo
      - VESELKA_DISABLE_TASK_PROBABILITY_METRICS=1
      - VESELKA_REVIEW_ONLY=1
      - PRELOAD_DISABLED=1
      # Uncomment to enable sentry in Veselka Test
      # - VESELKA_SENTRY_DSN=https://<EMAIL>/4505172929609728
      - VESELKA_SENTRY_TRACE_SAMPLE_RATE=1.0
      - VESELKA_SENTRY_IGNORE_PATHS=/internal/metrics,/health
      - PORT=8080
      - VESELKA_DEFAULT_CROP_POINT_CATEGORY_ID=8bb671f4-0eb7-4778-8262-3fc5445c6ea8
      - VESELKA_IMG_S3_BUCKET="carbon-automation-testing"
      - VESELKA_IMG_S3_KEY_PREFIX="testing"
      - CROP_TRANSLATION_VERSION=5
      - POINT_CATEGORY_TRANSLATION_VERSION=5
      - VESELKA_BYPASS_LABEL_POINTS="1"
      - ANALYTICS_ROSY_ADDRESS=robot-syncer.testing.svc.cluster.local:8080
    files:
      - env.js=configs/veselka-env.js

  - name: celery-exporter
    literals:
      - CE_BUCKETS=1,10,60,600,1000
      - CE_BROKER_URL=redis://cloud-v1-test-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379

  - name: web-socket-server
    literals:
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com
      - AUTH0_MGMT_CLIENT_ID=SsT9TjpdOkDVswttS2EF3hTMiD7OVu5w
      - PORTAL_SERVICE_HOST=portal.testing.svc.cluster.local:8080
      - PORTAL_SERVICE_SECURE=false
      - ENABLE_AUTH=true
      - ENFORCE_HEART_BEAT=true
      - ENABLE_NEW_RTC_MODE=true
      - ECHO_MODE=false
      - RUN_MODE=run-migrate
      - REDIS_ADDR=cloud-v1-test-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - REDIS_DB=1

  - name: rtc-ui
    files:
      - default.conf=configs/rtc-ui-nginx-default.conf
    literals:
      - VITE_AUTH0_AUDIENCE=https://customer-test.cloud.carbonrobotics.com
      - VITE_AUTH0_AUTH_DOMAIN=carbonrobotics-dev.us.auth0.com
      - VITE_AUTH0_CALLBACK_URL=https://rtc-test.cloud.carbonrobotics.com/callback/
      - VITE_AUTH0_CLIENT_ID=m33rZMXs6Ij4cZFYjeECX5b0ptnANYD9
      - VITE_PORTAL_ORIGIN=https://customer-test.cloud.carbonrobotics.com
      - VITE_WS_ORIGIN=wss://signal-rtc-test.cloud.carbonrobotics.com

  - name: rtc-jobs
    literals:
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:7000,https://customer-test.cloud.carbonrobotics.com,https://customer.cloud.carbonrobotics.com,https://rtc-test.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - PORTAL_SERVICE_HOST=portal.testing.svc.cluster.local:8080
      - AUTH0_ROBOT_AUDIENCES=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCES=https://customer-test.cloud.carbonrobotics.com

  - name: rtc-locator
    literals:
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCE=https://customer-test.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:7000,https://customer-test.cloud.carbonrobotics.com,https://rtc-test.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - PEPLINK_CLIENT_ID=2948bbaac8bd5405eeb5148343ef8945
      - PORTAL_SERVICE_HOST=portal.testing.svc.cluster.local:8080
      - DEVICE_TRACKER_UPDATE_INTERVAL=5s

  - name: idm
    literals:
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:7000,https://customer-test.cloud.carbonrobotics.com,https://customer.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - CORS_ALLOW_CREDENTIALS=true
      - AUTH0_ALLOWED_AUDIENCES=https://robot.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com,https://ml.carbonrobotics.com,https://datacenter.carbonrobotics.com,https://veselka.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_CLIENT_ID=TrvUMXlmtGiXJV4DT0Kqeh2jdp5fomAW
      - STREAM_KEY=cxvrkkzhqp66

  - name: ingest
    literals:
      - REDIS_URL=
      - INGEST_SOURCES=s3://carbon-portal-testing/uploads
      - AWS_INGEST_BUCKET=carbon-automation-testing
      - AWS_INGEST_KEY_PREFIX=testing/ingest/upload/
      - AWS_STORAGE_BUCKET=carbon-automation-testing
      - AWS_STORAGE_KEY_PREFIX=testing/media/
      - AWS_LOG_BUCKET=carbon-cloud-app
      - VESELKA_URL=http://veselka.testing.svc.cluster.local:8080
      - CONFIG_FILE=/config/config.json
      - SECURE="true"
      - PROMETHEUS_TARGET_URL=http://prometheus-robot-test.monitoring.svc.cluster.local:9090/api/v1/write
      - ROBOT_PROMETHEUS_TARGET_URL=http://prometheus-robot-test.monitoring.svc.cluster.local:9090/api/v1/write
      - DATACENTER_PROMETHEUS_TARGET_URL=http://prometheus-datacenter.monitoring.svc.cluster.local:9090/api/v1/write
      - AUTHORIZED_DATACENTERS=testing-dc
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DATACENTER_AUDIENCE=https://datacenter.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - GOOGLE_CLIENT_ID=tbd
      - GOOGLE_REDIRECT_URL=tbd
      - DISABLE_INGESTION=false
      - PERF_MODE="true"
    files:
      - config.json=configs/ingest-config.json

  - name: portal
    literals:
      - AUTH0_AUTH_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_GRPC_CLIENT_ID=p27gPTHGb2dfqZaJEb0D7K51i4Kl1S62
      - AUTH0_REST_AUDIENCE=https://customer-test.cloud.carbonrobotics.com
      - AUTH0_REST_CALLBACK_URL=https://customer-test.cloud.carbonrobotics.com/callback/
      - AUTH0_REST_CLIENT_ID=PBz7DgkZ51ALcVMv23KFS9MP0zsW2h5Z
      - AUTH0_SERVER_ID=CdeX4sl7myyLWuaB0Wowcx3s4MrSf903
      - AUTH0_TENANT_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AWS_BUCKET=carbon-portal-testing
      - CONFIG_URL=config.testing.svc.cluster.local:443
      - LOG_LEVEL=debug
      - NO_CONFIDENCE_CHECKS=true
      - PERF_MODE="true"
      - REACT_APP_AUTH0_AUDIENCE=https://customer-test.cloud.carbonrobotics.com
      - REACT_APP_AUTH0_AUTH_DOMAIN=carbonrobotics-dev.us.auth0.com
      - REACT_APP_AUTH0_REST_CALLBACK_URL=https://customer-test.cloud.carbonrobotics.com/callback/
      - REACT_APP_AUTH0_REST_CLIENT_ID=PBz7DgkZ51ALcVMv23KFS9MP0zsW2h5Z
      - REACT_APP_GOOGLE_MEASUREMENT_ID=G-2MTYT4WBLB
      - REACT_APP_IDM_URL=https://idm-test.cloud.carbonrobotics.com
      - REACT_APP_MUI_LICENSE_KEY=************************************************************************************************************************
      - REACT_APP_RTC_JOBS_URL=https://rtc-jobs-test.cloud.carbonrobotics.com
      - REACT_APP_RTC_LOCATOR_URL=https://rtc-locator-test.cloud.carbonrobotics.com
      - REACT_APP_SENTRY_DSN=https://<EMAIL>/6177154
      - REACT_APP_SENTRY_ENVIRONMENT=testing
      - REACT_APP_STREAM_API_KEY=cxvrkkzhqp66
      - REACT_APP_S3_CACHE_PROXY_URL=https://cloud-image-test.cloud.carbonrobotics.com
      - REACT_APP_IMAGE_SERVICE_URL=https://cloud-image-test.cloud.carbonrobotics.com
      - REACT_APP_USE_ROBOT_SYNCER_TEMPLATES=true
      - REACT_APP_VESELKA_URL=https://veselka-test.cloud.carbonrobotics.com
      - ROBOT_SYNCER_URL=robot-syncer.testing.svc.cluster.local:8080
      - ROOT_URL=https://customer-test.cloud.carbonrobotics.com
      - RTC_JOBS_TARGET=rtc-jobs-grpc-test.cloud.carbonrobotics.com:443
      - SERVER_URL=https://customer-test.cloud.carbonrobotics.com
      - TWILIO_SID=SK0279d19217e11ba8c6173df9360cf303
      - UPDATE_ROSY_CONFIGS=true
      - USE_ROBOT_SYNCER_TEMPLATES=true
      - USE_ROBOT_SYNCER_CONFIGS=true
      - VESELKA_URL=http://veselka.testing.svc.cluster.local:8080
      - INSTANCE=TESTING
      - BUILD=RELEASE
    files:
      - spatial-config.json=configs/portal-spatial-config.json

  - name: robot-syncer
    literals:
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_TENANT_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AWS_BUCKET=carbon-robot-syncer-testing
      - AWS_REGION=us-west-2
      - CONFIG_AUDIT_LOG_GROUP_NAME=robot-config-audit-testing
      - PORTAL_URL=http://portal.testing.svc.cluster.local
      - PORTAL_PORT=8080
      - ROBOT_SYNCER_PORT=8080
      - ROBOT_SYNCER_GRPC_PORT=9090
      - SHARED_CONFIG_SCHEMA_PATH=/data/shared_config_schemas
#      - ENVIRONMENT=TESTING

  - name: cloud-image
    literals:
      - GIN_MODE=debug
      - STORAGE_MODE=s3only
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AUTH0_AUDIENCES=https://veselka.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com
      - TRANSFORM_STORAGE_BUCKET=carbon-image-transforms
      - TRANSFORM_STORAGE_KEY_PREFIX=testing/
      - CORS_ALLOWED_ORIGINS=https://veselka-test.cloud.carbonrobotics.com,https://customer-test.cloud.carbonrobotics.com,http://localhost:8081,http://localhost:8000,http://localhost:3000,http://localhost:7000
      - RATE_LIMIT=10
      - SPILLOVER_LAMBDA_FUNCTION_NAME=image-service-testing

  - name: version-metadata
    literals:
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DOMAIN=carbonrobotics-dev.us.auth0.com
      - AWS_VERSION_META_BUCKET=carbon-version-metadata-testing

patches:
  # default affinity patches MUST be first, so all services are scheduled on
  # nodes labeled Worker = default unless otherwise specified (by a following patch)
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: .*
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: StatefulSet
      name: .*
  # END MUST BE FIRST

  # Note, ingest running in solo mode, do not scale up without fixing redis config
  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: ingest

  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-analytics

  - patch: |-
      - op: replace
        path: /spec/strategy/type
        value: Recreate
    target:
      kind: Deployment
      name: veselka

  - patch: |-
      - op: remove
        path: /spec/strategy/rollingUpdate
    target:
      kind: Deployment
      name: veselka

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: veselka-datasets

  - patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/resources
        value:
          requests:
            memory: "5Gi"
          limits:
            memory: "16Gi"
    target:
      kind: Deployment
      name: veselka-datasets

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-datasets

  - path: patches/serviceMonitorPatch.yaml
    target:
      kind: ServiceMonitor
#  - path: nodeAffinityPatch.yaml
#    target:
#      kind: Deployment

#  - path: nodeAffinityPatch.yaml
#    target:
#      kind: StatefulSet
