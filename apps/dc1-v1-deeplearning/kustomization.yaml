apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: deeplearning
resources:
  - pvcs.yaml
  - ../base/api-verifier
  - ../base/job-creator
  - job-creator-ingress.yaml
  - ../base/veselka_cv
  - ../base/credential-proxy
  - credential-proxy-ext
  - ../base/veselka-comparison

configMapGenerator:
  - name: job-creator
    literals:
      - WORK_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/job-creator-work-queue.fifo
      - SLACK_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/slack-message-queue.fifo
      - DL_TRAINING_IMAGE=ghcr.io/carbonrobotics/robot/deeplearning:stable
      - DEFAULT_PRETRAINED_MODEL_ID=bad13b84-05e4-44bb-b4eb-f59f66baa70c
      - PD_INTEGRATION_KEY=664cfacacb384c00d0bc1fc432ca4178
      - ML_TOKEN_URL=http://credential-proxy.deeplearning.svc:8080/ml/oauth/token
      - JOB_ENV_VARS=S3_CACHE_PROXY_SERVICE_HOST:cache-server.deeplearning.svc,CACHE_HOST_A:***********,CACHE_HOST_B:***********

  - name: api-verifier
    literals:
      - ROBOT_TOKEN_URL=http://credential-proxy.deeplearning.svc.cluster.local:8080/robot/oauth/token
      - CHECK_INTERVAL=24h
      - PD_INTEGRATION_KEY_VESELKA=fcc214c7df2f4d0cc040b32304b01afd
      - PD_INTEGRATION_KEY_SOFTWARE=d02fabcb19ee4e00d03ad1fc556fc55a

  - name: veselka-cv
    literals:
      - PYTHONUNBUFFERED=1
      - MAKA_ROLE=veselka_cv
      - MAKA_ROW=0
      - MAKA_GEN=bud
      - MAKA_ROBOT_NAME=weedy
      - MAKA_USER=maka
      # Note this ip is frankenmini02 need to circle back
      - S3_CACHE_PROXY_SERVICE_HOST=***********

  - name: veselka-comparison
    literals:
      - PORT=8081
      - PYTHONUNBUFFERED=1
      - MAKA_MODEL_BUCKET=maka-pono
      - VESELKA_PREDICT_DIR=/data/veselka_images_comparison
      - VESELKA_MODEL_DIR=/data/veselka_models_comparison
      # Note this ip is frankenmini02 need to circle back
      - S3_CACHE_PROXY_SERVICE_HOST=***********

patches:
  - path: patches/securityContextPatch.yaml
    target:
      kind: Deployment
      name: job-creator
  - path: patches/appNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: job-creator

  - path: patches/securityContextPatch.yaml
    target:
      kind: Deployment
      name: credential-proxy
  - path: patches/securityContextPatch.yaml
    target:
      kind: Deployment
      name: api-verifier

  - path: patches/appNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-comparison

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 0
    target:
      kind: Deployment
      name: veselka-comparison

  - path: patches/appNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-cv

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 14
    target:
      kind: Deployment
      name: veselka-cv

images:
  # veselka cv and veselka-comparison
  - name: ghcr.io/carbonrobotics/robot/veselka_cv
    newTag: vcv1.11.2
  - name: ghcr.io/carbonrobotics/cloud/job-creator
    newTag: master
