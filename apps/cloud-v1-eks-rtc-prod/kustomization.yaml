apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: rtc-prod
resources:
  # secrets
  - web-socket-server-secret-sealed.yaml
  - rtc-jobs-secret-sealed.yaml
  - rtc-locator-secret-sealed.yaml
  # configs
  # apps
  - ../base/web-socket-server
  - ../base/rtc-ui
  - ../base/rtc-jobs
  - ../base/rtc-locator
  # ingress
  - rtc-ingress.yaml
  - rtc-grpc-ingress.yaml

images:
  - name: ghcr.io/carbonrobotics/rtc/ui
    newName: ghcr.io/carbonrobotics/cloud/rtc-ui
    newTag: rtc-ui-v1.0.0
  - name: ghcr.io/carbonrobotics/rtc/web-socket-server
    newTag: v0.29.0
  - name: ghcr.io/carbonrobotics/rtc/locator
    newTag: v0.29.7

configMapGenerator:
  - name: web-socket-server
    literals:
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - AUTH0_MGMT_CLIENT_ID=jO8NLnjcrB343BDLcdX3wiZCmBrjsvnF
      - PORTAL_SERVICE_HOST=portal.production.svc.cluster.local:8080
      - PORTAL_SERVICE_SECURE=false
      - ENABLE_AUTH=true
      - ENFORCE_HEART_BEAT=true
      - ENABLE_NEW_RTC_MODE=true
      - ECHO_MODE=false
      - ALLOWED_ROBOTS_BY_PREFIX=slayer
      - RUN_MODE=run-migrate

  - name: rtc-ui
    files:
      - default.conf=configs/rtc-ui-nginx-default.conf
    literals:
      - VITE_AUTH0_AUDIENCE=https://customer.cloud.carbonrobotics.com
      - VITE_AUTH0_AUTH_DOMAIN=carbonrobotics.us.auth0.com
      - VITE_AUTH0_CALLBACK_URL=https://rtc-prod.cloud.carbonrobotics.com/callback/
      - VITE_AUTH0_CLIENT_ID=MxoeCxz1F4AHKgYTcMYisLA9oDG5bYu4
      - VITE_PORTAL_ORIGIN=https://customer.cloud.carbonrobotics.com
      - VITE_WS_ORIGIN=wss://signal-rtc-prod.cloud.carbonrobotics.com

  - name: rtc-jobs
    literals:
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=https://customer.cloud.carbonrobotics.com,https://rtc-prod.cloud.carbonrobotics.com
      - PORTAL_SERVICE_HOST=portal.production.svc.cluster.local:8080
      - AUTH0_ROBOT_AUDIENCES=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCES=https://customer.cloud.carbonrobotics.com

  - name: rtc-locator
    literals:
      - AUTH0_DOMAIN=carbonrobotics.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer.cloud.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCE=https://customer.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=https://customer.cloud.carbonrobotics.com,https://rtc-prod.cloud.carbonrobotics.com
      - PEPLINK_CLIENT_ID=609b50c94abd205401f614463e450121
      - DEVICE_TRACKER_UPDATE_INTERVAL=5s
      - AWS_EXPORT_BUCKET=carbon-tableau-exports
      - AWS_EXPORT_KEY_PREFIX=rtc-prod/rtc-locator
      - PORTAL_SERVICE_HOST=portal.production.svc.cluster.local:8080

patches:
  # default affinity patches MUST be first, so all services are scheduled on
  # nodes labeled Worker = default unless otherwise specified (by a following patch)
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: .*
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: StatefulSet
      name: .*
  # END MUST BE FIRST

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: web-socket-server

  - path: patches/serviceMonitorPatch.yaml
    target:
      kind: ServiceMonitor
