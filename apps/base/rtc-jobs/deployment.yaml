---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rtc-jobs
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: rtc-jobs
  template:
    metadata:
      labels:
        app: rtc-jobs
    spec:
      serviceAccountName: rtc-jobs-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: rtc-jobs
          image: ghcr.io/carbonrobotics/rtc/jobs:master
          envFrom:
            - configMapRef:
                name: rtc-jobs
            - secretRef:
                name: rtc-jobs
                optional: true
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          resources:
            requests:
              memory: "64Mi"
            limits:
              cpu: "1000m"
              memory: "1Gi"
