apiVersion: apps/v1
kind: Deployment
metadata:
  name: robot-syncer
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: robot-syncer
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: robot-syncer
    spec:
      serviceAccountName: robot-syncer-sa
      imagePullSecrets:
        - name: github-registry-secret
      containers:
        - name: robot-syncer
          image: ghcr.io/carbonrobotics/cloud/robot-syncer:unknown
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
            - containerPort: 9090
          envFrom:
            - configMapRef:
                name: robot-syncer
            - secretRef:
                name: robot-syncer
          resources:
            requests:
              memory: "128Mi"
            limits:
              memory: "4Gi"
