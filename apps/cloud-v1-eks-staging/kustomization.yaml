apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: staging
resources:
  # secrets
  - portal-secret-sealed.yaml
  - robot-syncer-secret-sealed.yaml
  - idm-secret-sealed.yaml
  - ingest-secret-sealed.yaml
  - carbon-analytics-secret-sealed.yaml
  - carbon-panelytics-secret-sealed.yaml
  - carbon-data-secret-sealed.yaml
  - comparison-tool-secret-sealed.yaml
  - veselka-secret-sealed.yaml
  - veselka-analytics-secret-sealed.yaml
  - web-socket-server-secret-sealed.yaml
  - rtc-jobs-secret-sealed.yaml
  - rtc-locator-secret-sealed.yaml
  - capp-secret-sealed.yaml
  # cron jobs
  #- cronjobs/nightly-spatial-export-cronjob.yaml
  # apps
  - ../base/config-service
  - ../base/portal
  - ../base/robot-syncer
  - ../base/dart-server
  - ../base/ingest
  - ../base/idm
  - ../base/version-metadata
  - ../base/carbon-analytics
  - ../base/carbon-panelytics
  - ../base/comparison-tool
  - ../base/query-exporter
  - ../base/veselka
  - ../base/celery-exporter
  - ../base/web-socket-server
  - ../base/rtc-ui
  - ../base/rtc-jobs
  - ../base/rtc-locator
  - ../base/cloud-image
  - ../base/veselka-analytics
  - ../base/capp
  # ingress
  - config-service-ingress.yaml
  - portal-ingress.yaml
  - portal-grpc-ingress.yaml
  - portal-webhook-ingress.yaml # note, in production this is part of the slackbot ingres
  - dart-server-ingress.yaml
  - rosy-grpc-ingress.yaml
  - idm-ingress.yaml
  - ingest-ingress.yaml
  - version-metadata-ingress.yaml
  - veselka-ingress.yaml
  - rtc-ingress.yaml
  - rtc-grpc-ingress.yaml
  - capp-ingress.yaml

images:
  - name: ghcr.io/carbonrobotics/cloud/carbon-analytics
    newTag: analytics-v0.1.9
  - name: ghcr.io/carbonrobotics/cloud/carbon-panelytics
    newTag: master
  - name: ghcr.io/carbonrobotics/cloud/comparison-tool
    newTag: v0.0.292
  - name: ghcr.io/carbonrobotics/cloud/idm
    newTag: master
  - name: ghcr.io/carbonrobotics/cloud/ingest
    newTag: ingest-v0.3.1
  - name: ghcr.io/carbonrobotics/cloud/portal
    newTag: portal-v1.3.13
  - name: ghcr.io/carbonrobotics/cloud/robot-syncer
    newTag: rosy-v0.10.0
  - name: ghcr.io/carbonrobotics/cloud/cloud-image
    newTag: imgsvc-v0.1.1
  - name: ghcr.io/carbonrobotics/cloud/version-metadata
    newTag: master
  - name: ghcr.io/carbonrobotics/operator/dart-server
    newTag: master
  - name: ghcr.io/carbonrobotics/robot/common
    newTag: v1.21d.123
  - name: ghcr.io/carbonrobotics/rtc/ui
    newName: ghcr.io/carbonrobotics/cloud/rtc-ui
    newTag: rtc-ui-v1.0.0
  - name: ghcr.io/carbonrobotics/rtc/web-socket-server
    newTag: v0.18.1
  - name: ghcr.io/carbonrobotics/veselka/analytics
    newTag: v1.122.0
  - name: ghcr.io/carbonrobotics/veselka/veselka
    newTag: v1.124.0

configMapGenerator:
  - name: idm
    literals:
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:7000,https://customer-stg.cloud.carbonrobotics.com,https://customer.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - CORS_ALLOW_CREDENTIALS=true
      - AUTH0_ALLOWED_AUDIENCES=https://robot.carbonrobotics.com,https://customer-staging.cloud.carbonrobotics.com,https://ml.carbonrobotics.com,https://datacenter.carbonrobotics.com,https://veselka.carbonrobotics.com
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_CLIENT_ID=3t9yG0hRCUWpb5yfSSC8K2gR2IFuksvP
      - STREAM_KEY=5zpznx355bth

  - name: ingest
    literals:
      - REDIS_URL=cloud-v1-stage-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - INGEST_SOURCES=s3://carbon-portal-staging/uploads
      - AWS_INGEST_BUCKET=carbon-automation-testing
      - AWS_INGEST_KEY_PREFIX=staging/ingest/upload/
      - AWS_STORAGE_BUCKET=carbon-automation-testing
      - AWS_STORAGE_KEY_PREFIX=media/
      - AWS_LOG_BUCKET=carbon-cloud-app
      - VESELKA_URL=http://veselka.staging.svc.cluster.local:8080
      - CONFIG_FILE=/config/config.json
      - SECURE="true"
      - PROMETHEUS_TARGET_URL=http://prometheus-robot-stg.monitoring.svc.cluster.local:9090/api/v1/write
      - ROBOT_PROMETHEUS_TARGET_URL=http://prometheus-robot-stg.monitoring.svc.cluster.local:9090/api/v1/write
      - DATACENTER_PROMETHEUS_TARGET_URL=http://prometheus-datacenter.monitoring.svc.cluster.local:9090/api/v1/write
      - AUTHORIZED_DATACENTERS=test-dc
      - SQS_JOB_Q_URL=https://sqs.us-west-2.amazonaws.com/645516868483/job-creator-work-queue.fifo
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DATACENTER_AUDIENCE=https://datacenter.carbonrobotics.com
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - GOOGLE_CLIENT_ID=576961730789-oiiai5jec0b8gaenh9jlv59f5dve5h00.apps.googleusercontent.com
      - GOOGLE_REDIRECT_URL=https://ingest-stg.cloud.carbonrobotics.com/auth/callback
      - DISABLE_INGESTION=false
      - PERF_MODE="true"
    files:
      - config.json=configs/ingest-config.json

  - name: portal
    literals:
      - AUTH0_AUTH_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_GRPC_CLIENT_ID=zzF8okD4fuot74zXgsYdpr90SfPRUKx2
      - AUTH0_REST_AUDIENCE=https://customer-staging.cloud.carbonrobotics.com
      - AUTH0_REST_CALLBACK_URL=https://customer-stg.cloud.carbonrobotics.com/callback/
      - AUTH0_REST_CLIENT_ID=0mtA1j7fbSjDqTuxifEkFn6J9H9g6IHm
      - AUTH0_SERVER_ID=MYHcBFSW6i48V4m2rJjiRXRvk1OsN3eE
      - AUTH0_TENANT_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AWS_BUCKET=carbon-portal-staging
      - CONFIG_URL=config.staging.svc.cluster.local:443
      - LOG_LEVEL=debug
      - NO_CONFIDENCE_CHECKS=true
      - PERF_MODE="true"
      - R3_ACCESS_KEY_ID=DKBGZJ34FB4HXFIZKPWT
      - R3_ACCOUNT_ID=E8B8B878-B754-4112-A239-86D24178C2A9
      - REACT_APP_AUTH0_AUDIENCE=https://customer-staging.cloud.carbonrobotics.com
      - REACT_APP_AUTH0_AUTH_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - REACT_APP_AUTH0_REST_CALLBACK_URL=https://customer-stg.cloud.carbonrobotics.com/callback/
      - REACT_APP_AUTH0_REST_CLIENT_ID=0mtA1j7fbSjDqTuxifEkFn6J9H9g6IHm
      - REACT_APP_GOOGLE_MEASUREMENT_ID=G-XGTZTFRDCF
      - REACT_APP_RTC_JOBS_URL=https://rtc-jobs-stg.cloud.carbonrobotics.com
      - REACT_APP_RTC_LOCATOR_URL=https://rtc-locator-stg.cloud.carbonrobotics.com
      - REACT_APP_IDM_URL=https://idm-stg.cloud.carbonrobotics.com
      - REACT_APP_MUI_LICENSE_KEY=************************************************************************************************************************
      - REACT_APP_SENTRY_DSN=https://<EMAIL>/6177154
      - REACT_APP_SENTRY_ENVIRONMENT=staging
      - REACT_APP_STREAM_API_KEY=5zpznx355bth
      - REACT_APP_S3_CACHE_PROXY_URL=https://cloud-image-stg.cloud.carbonrobotics.com
      - REACT_APP_IMAGE_SERVICE_URL=https://cloud-image-stg.cloud.carbonrobotics.com
      - REACT_APP_USE_ROBOT_SYNCER_TEMPLATES=true
      - REACT_APP_VESELKA_URL=https://veselka-stg.cloud.carbonrobotics.com
      - ROBOT_SYNCER_URL=robot-syncer.staging.svc.cluster.local:8080
      - ROOT_URL=https://customer-stg.cloud.carbonrobotics.com
      - RTC_JOBS_TARGET=rtc-jobs-grpc-stg.cloud.carbonrobotics.com:443
      - SERVER_URL=https://customer-stg.cloud.carbonrobotics.com
      - TWILIO_SID=SK0279d19217e11ba8c6173df9360cf303
      - UPDATE_ROSY_CONFIGS=true
      - USE_ROBOT_SYNCER_TEMPLATES=true
      - USE_ROBOT_SYNCER_CONFIGS=true
      - VESELKA_URL=http://veselka.staging.svc.cluster.local:8080
      - INSTANCE=STAGING
      - BUILD=RELEASE
    files:
      - spatial-config.json=configs/portal-spatial-config.json

  - name: robot-syncer
    literals:
      - AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_TENANT_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AWS_BUCKET=carbon-robot-syncer-staging
      - AWS_REGION=us-west-2
      - CONFIG_AUDIT_LOG_GROUP_NAME=robot-config-audit-staging
      - PORTAL_URL=http://portal.staging.svc.cluster.local
      - PORTAL_PORT=8080
      - ROBOT_SYNCER_PORT=8080
      - ROBOT_SYNCER_GRPC_PORT=9090
      - SHARED_CONFIG_SCHEMA_PATH=/data/shared_config_schemas
#      - ENVIRONMENT=STAGING

  - name: comparison-tool
    literals:
      - S3_KEY_PREFIX=comparison/staging
      - ADMIN_MODE=0
      - DEFAULT_DATASET=e7808dfc-972a-49f9-b897-3a841d5d843e
      - VESELKA_URL=http://veselka.testing.svc.cluster.local:8080

  - name: version-metadata
    literals:
      - AUTH0_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AWS_VERSION_META_BUCKET=carbon-version-metadata-staging

  - name: veselka-cm
    literals:
      - GUNICORN_WORKERS=9
      - VESELKA_IMAGE_SERVICE_URL=cloud-image.staging.svc.cluster.local
      - VESELKA_MODE=production
      - FLASK_ENV=production
      - REDIS_URL=redis://cloud-v1-stage-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379
      - CROP_TRANSLATION_VERSION=3
      - POINT_CATEGORY_TRANSLATION_VERSION=3
      - VESELKA_GET_TASK_PROBABILITIES_CAPTURED_AT_LIMIT_DAYS="365"
      - VESELKA_INTERACTIVE_PREDICT_QUEUE=veselka-predict-queue-stage.fifo
      - VESELKA_PREDICT_QUEUE=veselka-batch-predict-queue-stage.fifo
      - VESELKA_PREDICT_RESULT_QUEUE=veselka-predict-result-queue-stage.fifo
      - VESELKA_COMPARISON_PREDICT_QUEUE=veselka-comparison-predict-queue-stage.fifo
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_ROLE_LABEL_ID=rol_w2gCDRH446ufWsug
      - AUTH0_ROLE_REVIEW_ID=rol_4d8s71d2L5ceKBD9
      - AUTH0_ROLE_ADMIN_ID=rol_qDAaoJIU9poCLKZZ
      - AWS_DEFAULT_REGION=us-west-2
      - PORT=8080
      - PRELOAD_DISABLED=1
      - SLACK_CHANNEL_ID=C05PHEHNPRB
      - SLACKBOT_URL=http://slackbot.production.svc.cluster.local:8080
      - VESELKA_MODEL_RECOMMENDER_MODEL_LIMIT=100
      - VESELKA_SENTRY_DSN=https://<EMAIL>/4505172929609728
      - VESELKA_SENTRY_TRACE_SAMPLE_RATE=0.0
      - VESELKA_LIMIT_GET_TASK_PROBABILITIES_CAPTURED_AT="1"
      - VESELKA_IMAGE_QUARANTINE_FOCUS_METRIC_THRESHOLD=0.05
      - VESELKA_MODEL_PD_WEEDS_TARGETED_REGRESSION=-0.1
      - VESELKA_REVIEW_ONLY=1
      - VESELKA_TASK_SAMPLING_GEOHASH_PRECISION=6
      - VESELKA_TASK_SAMPLING_GAMMA=1.0
      - VESELKA_PREVIEW_DEEPLEARNING_CONTAINER_TAG=dl-nightly
      - VESELKA_MODEL_RECOMMENDER_ENABLE_V2="1"
      - VESELKA_MODEL_RECOMMENDER_RUNNER_ENABLE_V2="1"
      - VESELKA_PORTAL_URL="http://portal.staging.svc.cluster.local:8080"
      - VESELKA_MODEL_RECOMMENDER_CACHE_TIME_LIMIT_MINUTES="1440"
      - VESELKA_POPULATE_LABEL_POINTS="1"
      - VESELKA_DEFAULT_CROP_POINT_CATEGORY_ID="8bb671f4-0eb7-4778-8262-3fc5445c6ea8"
      - VESELKA_IMG_S3_BUCKET="carbon-automation-testing"
      - VESELKA_IMG_S3_KEY_PREFIX="staging"
      - VESELKA_BYPASS_LABEL_POINTS="1"
    files:
      - env.js=configs/veselka-env.js

  - name: web-socket-server
    literals:
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_MGMT_CLIENT_ID=lZYZyeDo5aIlSGEhuxJkO6e2z4AQTI5l
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-staging.cloud.carbonrobotics.com
      - PORTAL_SERVICE_HOST=portal.staging.svc.cluster.local:8080
      - PORTAL_SERVICE_SECURE=false
      - ENABLE_AUTH=true
      - ENFORCE_HEART_BEAT=true
      - ENABLE_NEW_RTC_MODE=true
      - ECHO_MODE=false
      - RUN_MODE=run-migrate

  - name: rtc-ui
    files:
      - default.conf=configs/rtc-ui-nginx-default.conf
    literals:
      - VITE_AUTH0_AUDIENCE=https://customer-staging.cloud.carbonrobotics.com
      - VITE_AUTH0_AUTH_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - VITE_AUTH0_CALLBACK_URL=https://rtc-stg.cloud.carbonrobotics.com/callback/
      - VITE_AUTH0_CLIENT_ID=zNKmPDzTj0lZALZk3fYCpBH9gr3lAz3M
      - VITE_PORTAL_ORIGIN=https://customer-stg.cloud.carbonrobotics.com
      - VITE_WS_ORIGIN=wss://signal-rtc-stg.cloud.carbonrobotics.com

  - name: rtc-jobs
    literals:
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-staging.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:7000,https://customer-stg.cloud.carbonrobotics.com,https://customer.cloud.carbonrobotics.com,https://rtc-stg.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - AUTH0_ROBOT_AUDIENCES=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCES=https://customer-staging.cloud.carbonrobotics.com
      - PORTAL_SERVICE_HOST=portal.staging.svc.cluster.local:8080

  - name: rtc-locator
    literals:
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_AUDIENCES=https://robot.carbonrobotics.com,https://customer-staging.cloud.carbonrobotics.com
      - AUTH0_ROBOT_AUDIENCE=https://robot.carbonrobotics.com
      - AUTH0_USER_AUDIENCE=https://customer-staging.cloud.carbonrobotics.com
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:7000,https://customer-stg.cloud.carbonrobotics.com,https://rtc-stg.cloud.carbonrobotics.com,https://flechettes.tiger-snake.ts.net
      - PEPLINK_CLIENT_ID=a98f7603ab8995d5fd5bf5a55316c30a
      - PORTAL_SERVICE_HOST=portal.staging.svc.cluster.local:8080

  - name: celery-exporter
    literals:
      - CE_BUCKETS=1,10,60,600,1000
      - CE_BROKER_URL=redis://cloud-v1-stage-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379

  - name: carbon-data
    literals:
      - GIN_MODE=debug

  - name: carbon-analytics
    literals:
      - VESELKA_URL=http://veselka.testing.svc.cluster.local:8080
      - S3_CACHE_PROXY_SERVICE_HOST=cloud-image.production.svc.cluster.local
      - REDIS_URL=redis://cloud-v1-stage-redis-cache.uqlrfa.0001.usw2.cache.amazonaws.com:6379/1
      - DISABLE_USER_PASSWORD=1

  - name: carbon-panelytics
    literals:
      - THANOS_QUERY_FRONTEND=thanos-query-frontend.monitoring.svc.cluster.local

  - name: cloud-image
    literals:
      - GIN_MODE=debug
      - STORAGE_MODE=s3only
      - SPILLOVER_LAMBDA_FUNCTION_NAME=image-service-staging
      - AUTH0_DOMAIN=dev-jx2b6o0d.us.auth0.com
      - AUTH0_AUDIENCES=https://veselka.carbonrobotics.com,https://customer-staging.cloud.carbonrobotics.com
      - TRANSFORM_STORAGE_BUCKET=carbon-image-transforms
      - TRANSFORM_STORAGE_KEY_PREFIX=staging/
      - CORS_ALLOWED_ORIGINS=https://veselka-stg.cloud.carbonrobotics.com,https://veselka-test.cloud.carbonrobotics.com,https://customer-stg.cloud.carbonrobotics.com,http://localhost:8081,http://localhost:8000,http://localhost:3000,http://localhost:7000
      - PERF_MODE=false
      - MODE_RATE_LIMIT=s3only:10

  - name: capp
    literals:
      - NETSUITE_LOG_LEVEL=INFO
      - NETSUITE_ACCOUNT=6693091_SB2
      - NETSUITE_URL=https://6693091-sb2.app.netsuite.com
      - NETSUITE_TOKEN_ID=****************************************************************
      - NETSUITE_CONSUMER_KEY=****************************************************************

  - name: query-exporter
    files:
      - config.yaml=configs/query-exporter-config.yaml

  - name: cron-scripts
    files:
      - scripts/spatial-export-script.sh

patches:
  # default affinity patches MUST be first, so all services are scheduled on
  # nodes labeled Worker = default unless otherwise specified (by a following patch)
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: .*
  - path: patches/defaultNodeAffinityPatch.yaml
    target:
      kind: StatefulSet
      name: .*
  # END MUST BE FIRST

  - path: patches/defaultNodePodAntiAffinityPatch.yaml
    target:
      kind: Deployment
      name: ingest

  - path: patches/defaultNodePodAntiAffinityPatch.yaml
    target:
      kind: Deployment
      name: cloud-image

  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: carbon-analytics
  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: carbon-panelytics

  - path: patches/analyticsNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-analytics

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 0
    target:
      kind: Deployment
      name: carbon-analytics

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: carbon-panelytics

  - patch: |-
      - op: replace
        path: /spec/strategy/type
        value: Recreate
    target:
      kind: Deployment
      name: veselka

  - patch: |-
      - op: remove
        path: /spec/strategy/rollingUpdate
    target:
      kind: Deployment
      name: veselka

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-model-ratio-metrics

  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: veselka-datasets

  - path: patches/veselkaNodeAffinityPatch.yaml
    target:
      kind: Deployment
      name: veselka-datasets

  - path: patches/serviceMonitorPatch.yaml
    target:
      kind: ServiceMonitor
#  - path: nodeAffinityPatch.yaml
#    target:
#      kind: Deployment

#  - path: nodeAffinityPatch.yaml
#    target:
#      kind: StatefulSet
