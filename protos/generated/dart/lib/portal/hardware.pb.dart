///
//  Generated code. Do not modify.
//  source: portal/hardware.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;

class LaserStats extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LaserStats', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.hardware'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowNumber', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laserId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cameraId')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laserSerial')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'firing')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..aOB(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'error')
    ..a<$fixnum.Int64>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalFireCount', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalFireTimeMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deltaTemp', $pb.PbFieldType.OF)
    ..a<$core.double>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'current', $pb.PbFieldType.OF)
    ..a<$core.int>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetTrajectoryId', $pb.PbFieldType.OU3)
    ..a<$fixnum.Int64>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lifetimeSec', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.double>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'powerLevel', $pb.PbFieldType.OF)
    ..a<$fixnum.Int64>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'installedAt', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'removedAt', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  LaserStats._() : super();
  factory LaserStats({
    $75.DB? db,
    $fixnum.Int64? robotId,
    $fixnum.Int64? rowNumber,
    $fixnum.Int64? laserId,
    $core.String? cameraId,
    $core.String? laserSerial,
    $core.bool? firing,
    $core.bool? enabled,
    $core.bool? error,
    $fixnum.Int64? totalFireCount,
    $fixnum.Int64? totalFireTimeMs,
    $core.double? deltaTemp,
    $core.double? current,
    $core.int? targetTrajectoryId,
    $fixnum.Int64? lifetimeSec,
    $core.double? powerLevel,
    $fixnum.Int64? installedAt,
    $fixnum.Int64? removedAt,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (rowNumber != null) {
      _result.rowNumber = rowNumber;
    }
    if (laserId != null) {
      _result.laserId = laserId;
    }
    if (cameraId != null) {
      _result.cameraId = cameraId;
    }
    if (laserSerial != null) {
      _result.laserSerial = laserSerial;
    }
    if (firing != null) {
      _result.firing = firing;
    }
    if (enabled != null) {
      _result.enabled = enabled;
    }
    if (error != null) {
      _result.error = error;
    }
    if (totalFireCount != null) {
      _result.totalFireCount = totalFireCount;
    }
    if (totalFireTimeMs != null) {
      _result.totalFireTimeMs = totalFireTimeMs;
    }
    if (deltaTemp != null) {
      _result.deltaTemp = deltaTemp;
    }
    if (current != null) {
      _result.current = current;
    }
    if (targetTrajectoryId != null) {
      _result.targetTrajectoryId = targetTrajectoryId;
    }
    if (lifetimeSec != null) {
      _result.lifetimeSec = lifetimeSec;
    }
    if (powerLevel != null) {
      _result.powerLevel = powerLevel;
    }
    if (installedAt != null) {
      _result.installedAt = installedAt;
    }
    if (removedAt != null) {
      _result.removedAt = removedAt;
    }
    return _result;
  }
  factory LaserStats.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LaserStats.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LaserStats clone() => LaserStats()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LaserStats copyWith(void Function(LaserStats) updates) => super.copyWith((message) => updates(message as LaserStats)) as LaserStats; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LaserStats create() => LaserStats._();
  LaserStats createEmptyInstance() => create();
  static $pb.PbList<LaserStats> createRepeated() => $pb.PbList<LaserStats>();
  @$core.pragma('dart2js:noInline')
  static LaserStats getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LaserStats>(create);
  static LaserStats? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $fixnum.Int64 get robotId => $_getI64(1);
  @$pb.TagNumber(2)
  set robotId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobotId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobotId() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get rowNumber => $_getI64(2);
  @$pb.TagNumber(3)
  set rowNumber($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRowNumber() => $_has(2);
  @$pb.TagNumber(3)
  void clearRowNumber() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get laserId => $_getI64(3);
  @$pb.TagNumber(4)
  set laserId($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLaserId() => $_has(3);
  @$pb.TagNumber(4)
  void clearLaserId() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get cameraId => $_getSZ(4);
  @$pb.TagNumber(5)
  set cameraId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCameraId() => $_has(4);
  @$pb.TagNumber(5)
  void clearCameraId() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get laserSerial => $_getSZ(5);
  @$pb.TagNumber(6)
  set laserSerial($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLaserSerial() => $_has(5);
  @$pb.TagNumber(6)
  void clearLaserSerial() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get firing => $_getBF(6);
  @$pb.TagNumber(7)
  set firing($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasFiring() => $_has(6);
  @$pb.TagNumber(7)
  void clearFiring() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get enabled => $_getBF(7);
  @$pb.TagNumber(8)
  set enabled($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasEnabled() => $_has(7);
  @$pb.TagNumber(8)
  void clearEnabled() => clearField(8);

  @$pb.TagNumber(9)
  $core.bool get error => $_getBF(8);
  @$pb.TagNumber(9)
  set error($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasError() => $_has(8);
  @$pb.TagNumber(9)
  void clearError() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get totalFireCount => $_getI64(9);
  @$pb.TagNumber(10)
  set totalFireCount($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasTotalFireCount() => $_has(9);
  @$pb.TagNumber(10)
  void clearTotalFireCount() => clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get totalFireTimeMs => $_getI64(10);
  @$pb.TagNumber(11)
  set totalFireTimeMs($fixnum.Int64 v) { $_setInt64(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasTotalFireTimeMs() => $_has(10);
  @$pb.TagNumber(11)
  void clearTotalFireTimeMs() => clearField(11);

  @$pb.TagNumber(12)
  $core.double get deltaTemp => $_getN(11);
  @$pb.TagNumber(12)
  set deltaTemp($core.double v) { $_setFloat(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasDeltaTemp() => $_has(11);
  @$pb.TagNumber(12)
  void clearDeltaTemp() => clearField(12);

  @$pb.TagNumber(13)
  $core.double get current => $_getN(12);
  @$pb.TagNumber(13)
  set current($core.double v) { $_setFloat(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasCurrent() => $_has(12);
  @$pb.TagNumber(13)
  void clearCurrent() => clearField(13);

  @$pb.TagNumber(14)
  $core.int get targetTrajectoryId => $_getIZ(13);
  @$pb.TagNumber(14)
  set targetTrajectoryId($core.int v) { $_setUnsignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasTargetTrajectoryId() => $_has(13);
  @$pb.TagNumber(14)
  void clearTargetTrajectoryId() => clearField(14);

  @$pb.TagNumber(15)
  $fixnum.Int64 get lifetimeSec => $_getI64(14);
  @$pb.TagNumber(15)
  set lifetimeSec($fixnum.Int64 v) { $_setInt64(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasLifetimeSec() => $_has(14);
  @$pb.TagNumber(15)
  void clearLifetimeSec() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get powerLevel => $_getN(15);
  @$pb.TagNumber(16)
  set powerLevel($core.double v) { $_setFloat(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasPowerLevel() => $_has(15);
  @$pb.TagNumber(16)
  void clearPowerLevel() => clearField(16);

  @$pb.TagNumber(17)
  $fixnum.Int64 get installedAt => $_getI64(16);
  @$pb.TagNumber(17)
  set installedAt($fixnum.Int64 v) { $_setInt64(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasInstalledAt() => $_has(16);
  @$pb.TagNumber(17)
  void clearInstalledAt() => clearField(17);

  @$pb.TagNumber(18)
  $fixnum.Int64 get removedAt => $_getI64(17);
  @$pb.TagNumber(18)
  set removedAt($fixnum.Int64 v) { $_setInt64(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasRemovedAt() => $_has(17);
  @$pb.TagNumber(18)
  void clearRemovedAt() => clearField(18);
}

class HardwareResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'HardwareResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.hardware'), createEmptyInstance: create)
    ..pc<LaserStats>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lasers', $pb.PbFieldType.PM, subBuilder: LaserStats.create)
    ..m<$core.String, $core.String>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hostSerials', entryClassName: 'HardwareResponse.HostSerialsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.portal.hardware'))
    ..hasRequiredFields = false
  ;

  HardwareResponse._() : super();
  factory HardwareResponse({
    $core.Iterable<LaserStats>? lasers,
    $core.Map<$core.String, $core.String>? hostSerials,
  }) {
    final _result = create();
    if (lasers != null) {
      _result.lasers.addAll(lasers);
    }
    if (hostSerials != null) {
      _result.hostSerials.addAll(hostSerials);
    }
    return _result;
  }
  factory HardwareResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HardwareResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HardwareResponse clone() => HardwareResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HardwareResponse copyWith(void Function(HardwareResponse) updates) => super.copyWith((message) => updates(message as HardwareResponse)) as HardwareResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static HardwareResponse create() => HardwareResponse._();
  HardwareResponse createEmptyInstance() => create();
  static $pb.PbList<HardwareResponse> createRepeated() => $pb.PbList<HardwareResponse>();
  @$core.pragma('dart2js:noInline')
  static HardwareResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HardwareResponse>(create);
  static HardwareResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<LaserStats> get lasers => $_getList(0);

  @$pb.TagNumber(2)
  $core.Map<$core.String, $core.String> get hostSerials => $_getMap(1);
}

