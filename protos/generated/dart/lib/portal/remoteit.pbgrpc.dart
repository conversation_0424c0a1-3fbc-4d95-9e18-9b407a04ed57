///
//  Generated code. Do not modify.
//  source: portal/remoteit.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'remoteit.pb.dart' as $51;
export 'remoteit.pb.dart';

class RemoteItManagerClient extends $grpc.Client {
  static final _$configure =
      $grpc.ClientMethod<$51.ConfigureRequest, $51.ConfigureResult>(
          '/carbon.portal.remoteit.RemoteItManager/Configure',
          ($51.ConfigureRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $51.ConfigureResult.fromBuffer(value));

  RemoteItManagerClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$51.ConfigureResult> configure(
      $51.ConfigureRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$configure, request, options: options);
  }
}

abstract class RemoteItManagerServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.remoteit.RemoteItManager';

  RemoteItManagerServiceBase() {
    $addMethod($grpc.ServiceMethod<$51.ConfigureRequest, $51.ConfigureResult>(
        'Configure',
        configure_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $51.ConfigureRequest.fromBuffer(value),
        ($51.ConfigureResult value) => value.writeToBuffer()));
  }

  $async.Future<$51.ConfigureResult> configure_Pre($grpc.ServiceCall call,
      $async.Future<$51.ConfigureRequest> request) async {
    return configure(call, await request);
  }

  $async.Future<$51.ConfigureResult> configure(
      $grpc.ServiceCall call, $51.ConfigureRequest request);
}
