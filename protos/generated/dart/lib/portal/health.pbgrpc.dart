///
//  Generated code. Do not modify.
//  source: portal/health.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'health.pb.dart' as $45;
import '../util/util.pb.dart' as $1;
export 'health.pb.dart';

class HealthServiceClient extends $grpc.Client {
  static final _$logHealth = $grpc.ClientMethod<$45.HealthLog, $1.Empty>(
      '/carbon.portal.health.HealthService/LogHealth',
      ($45.HealthLog value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$reportIssue = $grpc.ClientMethod<$45.IssueReport, $1.Empty>(
      '/carbon.portal.health.HealthService/ReportIssue',
      ($45.IssueReport value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  HealthServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> logHealth($45.HealthLog request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$logHealth, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> reportIssue($45.IssueReport request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$reportIssue, request, options: options);
  }
}

abstract class HealthServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.health.HealthService';

  HealthServiceBase() {
    $addMethod($grpc.ServiceMethod<$45.HealthLog, $1.Empty>(
        'LogHealth',
        logHealth_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $45.HealthLog.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$45.IssueReport, $1.Empty>(
        'ReportIssue',
        reportIssue_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $45.IssueReport.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> logHealth_Pre(
      $grpc.ServiceCall call, $async.Future<$45.HealthLog> request) async {
    return logHealth(call, await request);
  }

  $async.Future<$1.Empty> reportIssue_Pre(
      $grpc.ServiceCall call, $async.Future<$45.IssueReport> request) async {
    return reportIssue(call, await request);
  }

  $async.Future<$1.Empty> logHealth(
      $grpc.ServiceCall call, $45.HealthLog request);
  $async.Future<$1.Empty> reportIssue(
      $grpc.ServiceCall call, $45.IssueReport request);
}
