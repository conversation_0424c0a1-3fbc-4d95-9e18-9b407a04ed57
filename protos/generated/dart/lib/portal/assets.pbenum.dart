///
//  Generated code. Do not modify.
//  source: portal/assets.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

// ignore_for_file: UNDEFINED_SHOWN_NAME
import 'dart:core' as $core;
import 'package:protobuf/protobuf.dart' as $pb;

class ReaperModuleAbLocation extends $pb.ProtobufEnum {
  static const ReaperModuleAbLocation LOCATION_UNSPECIFIED = ReaperModuleAbLocation._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LOCATION_UNSPECIFIED');
  static const ReaperModuleAbLocation LOCATION_A = ReaperModuleAbLocation._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LOCATION_A');
  static const ReaperModuleAbLocation LOCATION_B = ReaperModuleAbLocation._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LOCATION_B');

  static const $core.List<ReaperModuleAbLocation> values = <ReaperModuleAbLocation> [
    LOCATION_UNSPECIFIED,
    LOCATION_A,
    LOCATION_B,
  ];

  static final $core.Map<$core.int, ReaperModuleAbLocation> _byValue = $pb.ProtobufEnum.initByValue(values);
  static ReaperModuleAbLocation? valueOf($core.int value) => _byValue[value];

  const ReaperModuleAbLocation._($core.int v, $core.String n) : super(v, n);
}

class SlayerRowComputerType extends $pb.ProtobufEnum {
  static const SlayerRowComputerType COMPUTER_UNKNOWN = SlayerRowComputerType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'COMPUTER_UNKNOWN');
  static const SlayerRowComputerType COMPUTER_SINGLE = SlayerRowComputerType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'COMPUTER_SINGLE');
  static const SlayerRowComputerType COMPUTER_PRIMARY = SlayerRowComputerType._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'COMPUTER_PRIMARY');
  static const SlayerRowComputerType COMPUTER_SECONDARY = SlayerRowComputerType._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'COMPUTER_SECONDARY');

  static const $core.List<SlayerRowComputerType> values = <SlayerRowComputerType> [
    COMPUTER_UNKNOWN,
    COMPUTER_SINGLE,
    COMPUTER_PRIMARY,
    COMPUTER_SECONDARY,
  ];

  static final $core.Map<$core.int, SlayerRowComputerType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static SlayerRowComputerType? valueOf($core.int value) => _byValue[value];

  const SlayerRowComputerType._($core.int v, $core.String n) : super(v, n);
}

