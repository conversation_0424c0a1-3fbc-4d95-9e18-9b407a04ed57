///
//  Generated code. Do not modify.
//  source: portal/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'jobs.pb.dart' as $46;
import '../util/util.pb.dart' as $1;
export 'jobs.pb.dart';

class PortalJobsServiceClient extends $grpc.Client {
  static final _$uploadJob = $grpc.ClientMethod<$46.UploadJobRequest, $1.Empty>(
      '/carbon.portal.jobs.PortalJobsService/UploadJob',
      ($46.UploadJobRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$uploadJobConfigDump =
      $grpc.ClientMethod<$46.UploadJobConfigDumpRequest, $1.Empty>(
          '/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump',
          ($46.UploadJobConfigDumpRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$uploadJobMetrics =
      $grpc.ClientMethod<$46.UploadJobMetricsRequest, $1.Empty>(
          '/carbon.portal.jobs.PortalJobsService/UploadJobMetrics',
          ($46.UploadJobMetricsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  PortalJobsServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> uploadJob($46.UploadJobRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJob, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadJobConfigDump(
      $46.UploadJobConfigDumpRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJobConfigDump, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadJobMetrics(
      $46.UploadJobMetricsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJobMetrics, request, options: options);
  }
}

abstract class PortalJobsServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.jobs.PortalJobsService';

  PortalJobsServiceBase() {
    $addMethod($grpc.ServiceMethod<$46.UploadJobRequest, $1.Empty>(
        'UploadJob',
        uploadJob_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $46.UploadJobRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$46.UploadJobConfigDumpRequest, $1.Empty>(
        'UploadJobConfigDump',
        uploadJobConfigDump_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $46.UploadJobConfigDumpRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$46.UploadJobMetricsRequest, $1.Empty>(
        'UploadJobMetrics',
        uploadJobMetrics_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $46.UploadJobMetricsRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> uploadJob_Pre($grpc.ServiceCall call,
      $async.Future<$46.UploadJobRequest> request) async {
    return uploadJob(call, await request);
  }

  $async.Future<$1.Empty> uploadJobConfigDump_Pre($grpc.ServiceCall call,
      $async.Future<$46.UploadJobConfigDumpRequest> request) async {
    return uploadJobConfigDump(call, await request);
  }

  $async.Future<$1.Empty> uploadJobMetrics_Pre($grpc.ServiceCall call,
      $async.Future<$46.UploadJobMetricsRequest> request) async {
    return uploadJobMetrics(call, await request);
  }

  $async.Future<$1.Empty> uploadJob(
      $grpc.ServiceCall call, $46.UploadJobRequest request);
  $async.Future<$1.Empty> uploadJobConfigDump(
      $grpc.ServiceCall call, $46.UploadJobConfigDumpRequest request);
  $async.Future<$1.Empty> uploadJobMetrics(
      $grpc.ServiceCall call, $46.UploadJobMetricsRequest request);
}
