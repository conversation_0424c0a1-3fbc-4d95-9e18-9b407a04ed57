///
//  Generated code. Do not modify.
//  source: portal/admin.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;

class GlobalAllowedAlarm extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GlobalAllowedAlarm', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'code')
    ..hasRequiredFields = false
  ;

  GlobalAllowedAlarm._() : super();
  factory GlobalAllowedAlarm({
    $75.DB? db,
    $core.String? code,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (code != null) {
      _result.code = code;
    }
    return _result;
  }
  factory GlobalAllowedAlarm.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GlobalAllowedAlarm.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GlobalAllowedAlarm clone() => GlobalAllowedAlarm()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GlobalAllowedAlarm copyWith(void Function(GlobalAllowedAlarm) updates) => super.copyWith((message) => updates(message as GlobalAllowedAlarm)) as GlobalAllowedAlarm; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GlobalAllowedAlarm create() => GlobalAllowedAlarm._();
  GlobalAllowedAlarm createEmptyInstance() => create();
  static $pb.PbList<GlobalAllowedAlarm> createRepeated() => $pb.PbList<GlobalAllowedAlarm>();
  @$core.pragma('dart2js:noInline')
  static GlobalAllowedAlarm getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GlobalAllowedAlarm>(create);
  static GlobalAllowedAlarm? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get code => $_getSZ(1);
  @$pb.TagNumber(2)
  set code($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);
}

class RobotAllowedAlarm extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotAllowedAlarm', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'code')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..hasRequiredFields = false
  ;

  RobotAllowedAlarm._() : super();
  factory RobotAllowedAlarm({
    $75.DB? db,
    $core.String? code,
    $fixnum.Int64? robotId,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (code != null) {
      _result.code = code;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    return _result;
  }
  factory RobotAllowedAlarm.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotAllowedAlarm.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotAllowedAlarm clone() => RobotAllowedAlarm()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotAllowedAlarm copyWith(void Function(RobotAllowedAlarm) updates) => super.copyWith((message) => updates(message as RobotAllowedAlarm)) as RobotAllowedAlarm; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotAllowedAlarm create() => RobotAllowedAlarm._();
  RobotAllowedAlarm createEmptyInstance() => create();
  static $pb.PbList<RobotAllowedAlarm> createRepeated() => $pb.PbList<RobotAllowedAlarm>();
  @$core.pragma('dart2js:noInline')
  static RobotAllowedAlarm getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotAllowedAlarm>(create);
  static RobotAllowedAlarm? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get code => $_getSZ(1);
  @$pb.TagNumber(2)
  set code($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get robotId => $_getI64(2);
  @$pb.TagNumber(3)
  set robotId($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotId() => clearField(3);
}

class RobotBlockedAlarm extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotBlockedAlarm', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'code')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..hasRequiredFields = false
  ;

  RobotBlockedAlarm._() : super();
  factory RobotBlockedAlarm({
    $75.DB? db,
    $core.String? code,
    $fixnum.Int64? robotId,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (code != null) {
      _result.code = code;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    return _result;
  }
  factory RobotBlockedAlarm.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotBlockedAlarm.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotBlockedAlarm clone() => RobotBlockedAlarm()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotBlockedAlarm copyWith(void Function(RobotBlockedAlarm) updates) => super.copyWith((message) => updates(message as RobotBlockedAlarm)) as RobotBlockedAlarm; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotBlockedAlarm create() => RobotBlockedAlarm._();
  RobotBlockedAlarm createEmptyInstance() => create();
  static $pb.PbList<RobotBlockedAlarm> createRepeated() => $pb.PbList<RobotBlockedAlarm>();
  @$core.pragma('dart2js:noInline')
  static RobotBlockedAlarm getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotBlockedAlarm>(create);
  static RobotBlockedAlarm? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get code => $_getSZ(1);
  @$pb.TagNumber(2)
  set code($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get robotId => $_getI64(2);
  @$pb.TagNumber(3)
  set robotId($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotId() => clearField(3);
}

class GlobalAlarmAllowlist extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GlobalAlarmAllowlist', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..pc<GlobalAllowedAlarm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: GlobalAllowedAlarm.create)
    ..hasRequiredFields = false
  ;

  GlobalAlarmAllowlist._() : super();
  factory GlobalAlarmAllowlist({
    $core.Iterable<GlobalAllowedAlarm>? alarms,
  }) {
    final _result = create();
    if (alarms != null) {
      _result.alarms.addAll(alarms);
    }
    return _result;
  }
  factory GlobalAlarmAllowlist.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GlobalAlarmAllowlist.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GlobalAlarmAllowlist clone() => GlobalAlarmAllowlist()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GlobalAlarmAllowlist copyWith(void Function(GlobalAlarmAllowlist) updates) => super.copyWith((message) => updates(message as GlobalAlarmAllowlist)) as GlobalAlarmAllowlist; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GlobalAlarmAllowlist create() => GlobalAlarmAllowlist._();
  GlobalAlarmAllowlist createEmptyInstance() => create();
  static $pb.PbList<GlobalAlarmAllowlist> createRepeated() => $pb.PbList<GlobalAlarmAllowlist>();
  @$core.pragma('dart2js:noInline')
  static GlobalAlarmAllowlist getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GlobalAlarmAllowlist>(create);
  static GlobalAlarmAllowlist? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<GlobalAllowedAlarm> get alarms => $_getList(0);
}

class RobotAlarmAllowlist extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotAlarmAllowlist', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..pc<RobotAllowedAlarm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: RobotAllowedAlarm.create)
    ..hasRequiredFields = false
  ;

  RobotAlarmAllowlist._() : super();
  factory RobotAlarmAllowlist({
    $core.Iterable<RobotAllowedAlarm>? alarms,
  }) {
    final _result = create();
    if (alarms != null) {
      _result.alarms.addAll(alarms);
    }
    return _result;
  }
  factory RobotAlarmAllowlist.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotAlarmAllowlist.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotAlarmAllowlist clone() => RobotAlarmAllowlist()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotAlarmAllowlist copyWith(void Function(RobotAlarmAllowlist) updates) => super.copyWith((message) => updates(message as RobotAlarmAllowlist)) as RobotAlarmAllowlist; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotAlarmAllowlist create() => RobotAlarmAllowlist._();
  RobotAlarmAllowlist createEmptyInstance() => create();
  static $pb.PbList<RobotAlarmAllowlist> createRepeated() => $pb.PbList<RobotAlarmAllowlist>();
  @$core.pragma('dart2js:noInline')
  static RobotAlarmAllowlist getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotAlarmAllowlist>(create);
  static RobotAlarmAllowlist? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<RobotAllowedAlarm> get alarms => $_getList(0);
}

class RobotAlarmBlocklist extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotAlarmBlocklist', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..pc<RobotBlockedAlarm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: RobotBlockedAlarm.create)
    ..hasRequiredFields = false
  ;

  RobotAlarmBlocklist._() : super();
  factory RobotAlarmBlocklist({
    $core.Iterable<RobotBlockedAlarm>? alarms,
  }) {
    final _result = create();
    if (alarms != null) {
      _result.alarms.addAll(alarms);
    }
    return _result;
  }
  factory RobotAlarmBlocklist.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotAlarmBlocklist.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotAlarmBlocklist clone() => RobotAlarmBlocklist()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotAlarmBlocklist copyWith(void Function(RobotAlarmBlocklist) updates) => super.copyWith((message) => updates(message as RobotAlarmBlocklist)) as RobotAlarmBlocklist; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotAlarmBlocklist create() => RobotAlarmBlocklist._();
  RobotAlarmBlocklist createEmptyInstance() => create();
  static $pb.PbList<RobotAlarmBlocklist> createRepeated() => $pb.PbList<RobotAlarmBlocklist>();
  @$core.pragma('dart2js:noInline')
  static RobotAlarmBlocklist getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotAlarmBlocklist>(create);
  static RobotAlarmBlocklist? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<RobotBlockedAlarm> get alarms => $_getList(0);
}

class GlobalAlarmlists extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GlobalAlarmlists', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<GlobalAlarmAllowlist>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'allowed', subBuilder: GlobalAlarmAllowlist.create)
    ..hasRequiredFields = false
  ;

  GlobalAlarmlists._() : super();
  factory GlobalAlarmlists({
    GlobalAlarmAllowlist? allowed,
  }) {
    final _result = create();
    if (allowed != null) {
      _result.allowed = allowed;
    }
    return _result;
  }
  factory GlobalAlarmlists.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GlobalAlarmlists.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GlobalAlarmlists clone() => GlobalAlarmlists()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GlobalAlarmlists copyWith(void Function(GlobalAlarmlists) updates) => super.copyWith((message) => updates(message as GlobalAlarmlists)) as GlobalAlarmlists; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GlobalAlarmlists create() => GlobalAlarmlists._();
  GlobalAlarmlists createEmptyInstance() => create();
  static $pb.PbList<GlobalAlarmlists> createRepeated() => $pb.PbList<GlobalAlarmlists>();
  @$core.pragma('dart2js:noInline')
  static GlobalAlarmlists getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GlobalAlarmlists>(create);
  static GlobalAlarmlists? _defaultInstance;

  @$pb.TagNumber(1)
  GlobalAlarmAllowlist get allowed => $_getN(0);
  @$pb.TagNumber(1)
  set allowed(GlobalAlarmAllowlist v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasAllowed() => $_has(0);
  @$pb.TagNumber(1)
  void clearAllowed() => clearField(1);
  @$pb.TagNumber(1)
  GlobalAlarmAllowlist ensureAllowed() => $_ensure(0);
}

class RobotAlarmlists extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotAlarmlists', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<RobotAlarmAllowlist>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'allowed', subBuilder: RobotAlarmAllowlist.create)
    ..aOM<RobotAlarmBlocklist>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocked', subBuilder: RobotAlarmBlocklist.create)
    ..hasRequiredFields = false
  ;

  RobotAlarmlists._() : super();
  factory RobotAlarmlists({
    RobotAlarmAllowlist? allowed,
    RobotAlarmBlocklist? blocked,
  }) {
    final _result = create();
    if (allowed != null) {
      _result.allowed = allowed;
    }
    if (blocked != null) {
      _result.blocked = blocked;
    }
    return _result;
  }
  factory RobotAlarmlists.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotAlarmlists.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotAlarmlists clone() => RobotAlarmlists()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotAlarmlists copyWith(void Function(RobotAlarmlists) updates) => super.copyWith((message) => updates(message as RobotAlarmlists)) as RobotAlarmlists; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotAlarmlists create() => RobotAlarmlists._();
  RobotAlarmlists createEmptyInstance() => create();
  static $pb.PbList<RobotAlarmlists> createRepeated() => $pb.PbList<RobotAlarmlists>();
  @$core.pragma('dart2js:noInline')
  static RobotAlarmlists getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotAlarmlists>(create);
  static RobotAlarmlists? _defaultInstance;

  @$pb.TagNumber(1)
  RobotAlarmAllowlist get allowed => $_getN(0);
  @$pb.TagNumber(1)
  set allowed(RobotAlarmAllowlist v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasAllowed() => $_has(0);
  @$pb.TagNumber(1)
  void clearAllowed() => clearField(1);
  @$pb.TagNumber(1)
  RobotAlarmAllowlist ensureAllowed() => $_ensure(0);

  @$pb.TagNumber(2)
  RobotAlarmBlocklist get blocked => $_getN(1);
  @$pb.TagNumber(2)
  set blocked(RobotAlarmBlocklist v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasBlocked() => $_has(1);
  @$pb.TagNumber(2)
  void clearBlocked() => clearField(2);
  @$pb.TagNumber(2)
  RobotAlarmBlocklist ensureBlocked() => $_ensure(1);
}

class Maintenance extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Maintenance', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.admin'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'message')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'restrictAccess')
    ..hasRequiredFields = false
  ;

  Maintenance._() : super();
  factory Maintenance({
    $75.DB? db,
    $core.String? message,
    $core.bool? restrictAccess,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (message != null) {
      _result.message = message;
    }
    if (restrictAccess != null) {
      _result.restrictAccess = restrictAccess;
    }
    return _result;
  }
  factory Maintenance.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Maintenance.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Maintenance clone() => Maintenance()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Maintenance copyWith(void Function(Maintenance) updates) => super.copyWith((message) => updates(message as Maintenance)) as Maintenance; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Maintenance create() => Maintenance._();
  Maintenance createEmptyInstance() => create();
  static $pb.PbList<Maintenance> createRepeated() => $pb.PbList<Maintenance>();
  @$core.pragma('dart2js:noInline')
  static Maintenance getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Maintenance>(create);
  static Maintenance? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get message => $_getSZ(1);
  @$pb.TagNumber(2)
  set message($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMessage() => $_has(1);
  @$pb.TagNumber(2)
  void clearMessage() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get restrictAccess => $_getBF(2);
  @$pb.TagNumber(3)
  set restrictAccess($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRestrictAccess() => $_has(2);
  @$pb.TagNumber(3)
  void clearRestrictAccess() => clearField(3);
}

