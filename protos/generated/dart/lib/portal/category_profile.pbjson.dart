///
//  Generated code. Do not modify.
//  source: portal/category_profile.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use metadataDescriptor instead')
const Metadata$json = const {
  '1': 'Metadata',
  '2': const [
    const {'1': 'updated_at', '3': 6, '4': 1, '5': 3, '9': 0, '10': 'updatedAt', '17': true},
  ],
  '8': const [
    const {'1': '_updated_at'},
  ],
};

/// Descriptor for `Metadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List metadataDescriptor = $convert.base64Decode('CghNZXRhZGF0YRIiCgp1cGRhdGVkX2F0GAYgASgDSABSCXVwZGF0ZWRBdIgBAUINCgtfdXBkYXRlZF9hdA==');
@$core.Deprecated('Use savedCategoryCollectionDescriptor instead')
const SavedCategoryCollection$json = const {
  '1': 'SavedCategoryCollection',
  '2': const [
    const {'1': 'profile', '3': 1, '4': 1, '5': 11, '6': '.carbon.category_profile.CategoryCollection', '10': 'profile'},
    const {'1': 'metadata', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.category_profile.Metadata', '10': 'metadata'},
  ],
};

/// Descriptor for `SavedCategoryCollection`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List savedCategoryCollectionDescriptor = $convert.base64Decode('ChdTYXZlZENhdGVnb3J5Q29sbGVjdGlvbhJFCgdwcm9maWxlGAEgASgLMisuY2FyYm9uLmNhdGVnb3J5X3Byb2ZpbGUuQ2F0ZWdvcnlDb2xsZWN0aW9uUgdwcm9maWxlEkQKCG1ldGFkYXRhGAIgASgLMiguY2FyYm9uLnBvcnRhbC5jYXRlZ29yeV9wcm9maWxlLk1ldGFkYXRhUghtZXRhZGF0YQ==');
@$core.Deprecated('Use savedCategoryDescriptor instead')
const SavedCategory$json = const {
  '1': 'SavedCategory',
  '2': const [
    const {'1': 'profile', '3': 1, '4': 1, '5': 11, '6': '.carbon.category_profile.Category', '10': 'profile'},
    const {'1': 'metadata', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.category_profile.Metadata', '10': 'metadata'},
  ],
};

/// Descriptor for `SavedCategory`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List savedCategoryDescriptor = $convert.base64Decode('Cg1TYXZlZENhdGVnb3J5EjsKB3Byb2ZpbGUYASABKAsyIS5jYXJib24uY2F0ZWdvcnlfcHJvZmlsZS5DYXRlZ29yeVIHcHJvZmlsZRJECghtZXRhZGF0YRgCIAEoCzIoLmNhcmJvbi5wb3J0YWwuY2F0ZWdvcnlfcHJvZmlsZS5NZXRhZGF0YVIIbWV0YWRhdGE=');
@$core.Deprecated('Use savedExpandedCategoryCollectionDescriptor instead')
const SavedExpandedCategoryCollection$json = const {
  '1': 'SavedExpandedCategoryCollection',
  '2': const [
    const {'1': 'profile', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.category_profile.SavedCategoryCollection', '10': 'profile'},
    const {'1': 'categories', '3': 2, '4': 3, '5': 11, '6': '.carbon.portal.category_profile.SavedCategory', '10': 'categories'},
  ],
};

/// Descriptor for `SavedExpandedCategoryCollection`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List savedExpandedCategoryCollectionDescriptor = $convert.base64Decode('Ch9TYXZlZEV4cGFuZGVkQ2F0ZWdvcnlDb2xsZWN0aW9uElEKB3Byb2ZpbGUYASABKAsyNy5jYXJib24ucG9ydGFsLmNhdGVnb3J5X3Byb2ZpbGUuU2F2ZWRDYXRlZ29yeUNvbGxlY3Rpb25SB3Byb2ZpbGUSTQoKY2F0ZWdvcmllcxgCIAMoCzItLmNhcmJvbi5wb3J0YWwuY2F0ZWdvcnlfcHJvZmlsZS5TYXZlZENhdGVnb3J5UgpjYXRlZ29yaWVz');
@$core.Deprecated('Use unsavedExpandedCategoryCollectionDescriptor instead')
const UnsavedExpandedCategoryCollection$json = const {
  '1': 'UnsavedExpandedCategoryCollection',
  '2': const [
    const {'1': 'profile', '3': 1, '4': 1, '5': 11, '6': '.carbon.category_profile.CategoryCollection', '10': 'profile'},
    const {'1': 'categories', '3': 2, '4': 3, '5': 11, '6': '.carbon.category_profile.Category', '10': 'categories'},
  ],
};

/// Descriptor for `UnsavedExpandedCategoryCollection`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List unsavedExpandedCategoryCollectionDescriptor = $convert.base64Decode('CiFVbnNhdmVkRXhwYW5kZWRDYXRlZ29yeUNvbGxlY3Rpb24SRQoHcHJvZmlsZRgBIAEoCzIrLmNhcmJvbi5jYXRlZ29yeV9wcm9maWxlLkNhdGVnb3J5Q29sbGVjdGlvblIHcHJvZmlsZRJBCgpjYXRlZ29yaWVzGAIgAygLMiEuY2FyYm9uLmNhdGVnb3J5X3Byb2ZpbGUuQ2F0ZWdvcnlSCmNhdGVnb3JpZXM=');
