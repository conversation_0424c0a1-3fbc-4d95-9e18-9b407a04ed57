///
//  Generated code. Do not modify.
//  source: portal/users.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;
import 'customers.pb.dart' as $80;

import 'auth.pbenum.dart' as $82;
import 'users.pbenum.dart';

export 'users.pbenum.dart';

class AppMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AppMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isActivated')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isInvited')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isRobot')
    ..pPS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'permissions')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sfdcAccountId')
    ..e<$82.UserDisplayRole>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'role', $pb.PbFieldType.OE, defaultOrMaker: $82.UserDisplayRole.unknown_role, valueOf: $82.UserDisplayRole.valueOf, enumValues: $82.UserDisplayRole.values)
    ..hasRequiredFields = false
  ;

  AppMetadata._() : super();
  factory AppMetadata({
    $fixnum.Int64? customerId,
    $core.bool? isActivated,
    $core.bool? isInvited,
    $core.bool? isRobot,
    $core.Iterable<$core.String>? permissions,
    $fixnum.Int64? sfdcAccountId,
    $82.UserDisplayRole? role,
  }) {
    final _result = create();
    if (customerId != null) {
      _result.customerId = customerId;
    }
    if (isActivated != null) {
      _result.isActivated = isActivated;
    }
    if (isInvited != null) {
      _result.isInvited = isInvited;
    }
    if (isRobot != null) {
      _result.isRobot = isRobot;
    }
    if (permissions != null) {
      _result.permissions.addAll(permissions);
    }
    if (sfdcAccountId != null) {
      _result.sfdcAccountId = sfdcAccountId;
    }
    if (role != null) {
      _result.role = role;
    }
    return _result;
  }
  factory AppMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AppMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AppMetadata clone() => AppMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AppMetadata copyWith(void Function(AppMetadata) updates) => super.copyWith((message) => updates(message as AppMetadata)) as AppMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AppMetadata create() => AppMetadata._();
  AppMetadata createEmptyInstance() => create();
  static $pb.PbList<AppMetadata> createRepeated() => $pb.PbList<AppMetadata>();
  @$core.pragma('dart2js:noInline')
  static AppMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AppMetadata>(create);
  static AppMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get customerId => $_getI64(0);
  @$pb.TagNumber(1)
  set customerId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCustomerId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCustomerId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get isActivated => $_getBF(1);
  @$pb.TagNumber(2)
  set isActivated($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIsActivated() => $_has(1);
  @$pb.TagNumber(2)
  void clearIsActivated() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get isInvited => $_getBF(2);
  @$pb.TagNumber(3)
  set isInvited($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIsInvited() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsInvited() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get isRobot => $_getBF(3);
  @$pb.TagNumber(4)
  set isRobot($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIsRobot() => $_has(3);
  @$pb.TagNumber(4)
  void clearIsRobot() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$core.String> get permissions => $_getList(4);

  @$pb.TagNumber(6)
  $fixnum.Int64 get sfdcAccountId => $_getI64(5);
  @$pb.TagNumber(6)
  set sfdcAccountId($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSfdcAccountId() => $_has(5);
  @$pb.TagNumber(6)
  void clearSfdcAccountId() => clearField(6);

  @$pb.TagNumber(7)
  $82.UserDisplayRole get role => $_getN(6);
  @$pb.TagNumber(7)
  set role($82.UserDisplayRole v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasRole() => $_has(6);
  @$pb.TagNumber(7)
  void clearRole() => clearField(7);
}

class UserMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UserMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'consentGiven')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'consentTimestamp')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'experimental')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'language')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showMascot')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'unit')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'defaultFleetViewId')
    ..hasRequiredFields = false
  ;

  UserMetadata._() : super();
  factory UserMetadata({
    $core.bool? consentGiven,
    $fixnum.Int64? consentTimestamp,
    $core.bool? experimental,
    $core.String? language,
    $core.bool? showMascot,
    $core.String? unit,
    $fixnum.Int64? defaultFleetViewId,
  }) {
    final _result = create();
    if (consentGiven != null) {
      _result.consentGiven = consentGiven;
    }
    if (consentTimestamp != null) {
      _result.consentTimestamp = consentTimestamp;
    }
    if (experimental != null) {
      _result.experimental = experimental;
    }
    if (language != null) {
      _result.language = language;
    }
    if (showMascot != null) {
      _result.showMascot = showMascot;
    }
    if (unit != null) {
      _result.unit = unit;
    }
    if (defaultFleetViewId != null) {
      _result.defaultFleetViewId = defaultFleetViewId;
    }
    return _result;
  }
  factory UserMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UserMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UserMetadata clone() => UserMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UserMetadata copyWith(void Function(UserMetadata) updates) => super.copyWith((message) => updates(message as UserMetadata)) as UserMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UserMetadata create() => UserMetadata._();
  UserMetadata createEmptyInstance() => create();
  static $pb.PbList<UserMetadata> createRepeated() => $pb.PbList<UserMetadata>();
  @$core.pragma('dart2js:noInline')
  static UserMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UserMetadata>(create);
  static UserMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get consentGiven => $_getBF(0);
  @$pb.TagNumber(1)
  set consentGiven($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasConsentGiven() => $_has(0);
  @$pb.TagNumber(1)
  void clearConsentGiven() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get consentTimestamp => $_getI64(1);
  @$pb.TagNumber(2)
  set consentTimestamp($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasConsentTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearConsentTimestamp() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get experimental => $_getBF(2);
  @$pb.TagNumber(3)
  set experimental($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasExperimental() => $_has(2);
  @$pb.TagNumber(3)
  void clearExperimental() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get language => $_getSZ(3);
  @$pb.TagNumber(4)
  set language($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLanguage() => $_has(3);
  @$pb.TagNumber(4)
  void clearLanguage() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get showMascot => $_getBF(4);
  @$pb.TagNumber(5)
  set showMascot($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasShowMascot() => $_has(4);
  @$pb.TagNumber(5)
  void clearShowMascot() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get unit => $_getSZ(5);
  @$pb.TagNumber(6)
  set unit($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasUnit() => $_has(5);
  @$pb.TagNumber(6)
  void clearUnit() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get defaultFleetViewId => $_getI64(6);
  @$pb.TagNumber(7)
  set defaultFleetViewId($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasDefaultFleetViewId() => $_has(6);
  @$pb.TagNumber(7)
  void clearDefaultFleetViewId() => clearField(7);
}

class Auth0Profile extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Auth0Profile', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'email')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'emailVerified')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'username')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'givenName')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneNumber')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneVerified')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'familyName')
    ..hasRequiredFields = false
  ;

  Auth0Profile._() : super();
  factory Auth0Profile({
    $core.String? email,
    $core.bool? emailVerified,
    $core.String? name,
    $core.String? username,
    $core.String? givenName,
    $core.String? phoneNumber,
    $core.bool? phoneVerified,
    $core.String? familyName,
  }) {
    final _result = create();
    if (email != null) {
      _result.email = email;
    }
    if (emailVerified != null) {
      _result.emailVerified = emailVerified;
    }
    if (name != null) {
      _result.name = name;
    }
    if (username != null) {
      _result.username = username;
    }
    if (givenName != null) {
      _result.givenName = givenName;
    }
    if (phoneNumber != null) {
      _result.phoneNumber = phoneNumber;
    }
    if (phoneVerified != null) {
      _result.phoneVerified = phoneVerified;
    }
    if (familyName != null) {
      _result.familyName = familyName;
    }
    return _result;
  }
  factory Auth0Profile.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Auth0Profile.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Auth0Profile clone() => Auth0Profile()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Auth0Profile copyWith(void Function(Auth0Profile) updates) => super.copyWith((message) => updates(message as Auth0Profile)) as Auth0Profile; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Auth0Profile create() => Auth0Profile._();
  Auth0Profile createEmptyInstance() => create();
  static $pb.PbList<Auth0Profile> createRepeated() => $pb.PbList<Auth0Profile>();
  @$core.pragma('dart2js:noInline')
  static Auth0Profile getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Auth0Profile>(create);
  static Auth0Profile? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get email => $_getSZ(0);
  @$pb.TagNumber(1)
  set email($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEmail() => $_has(0);
  @$pb.TagNumber(1)
  void clearEmail() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get emailVerified => $_getBF(1);
  @$pb.TagNumber(2)
  set emailVerified($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasEmailVerified() => $_has(1);
  @$pb.TagNumber(2)
  void clearEmailVerified() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get username => $_getSZ(3);
  @$pb.TagNumber(4)
  set username($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUsername() => $_has(3);
  @$pb.TagNumber(4)
  void clearUsername() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get givenName => $_getSZ(4);
  @$pb.TagNumber(5)
  set givenName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGivenName() => $_has(4);
  @$pb.TagNumber(5)
  void clearGivenName() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get phoneNumber => $_getSZ(5);
  @$pb.TagNumber(6)
  set phoneNumber($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPhoneNumber() => $_has(5);
  @$pb.TagNumber(6)
  void clearPhoneNumber() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get phoneVerified => $_getBF(6);
  @$pb.TagNumber(7)
  set phoneVerified($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasPhoneVerified() => $_has(6);
  @$pb.TagNumber(7)
  void clearPhoneVerified() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get familyName => $_getSZ(7);
  @$pb.TagNumber(8)
  set familyName($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasFamilyName() => $_has(7);
  @$pb.TagNumber(8)
  void clearFamilyName() => clearField(8);
}

class Auth0Identity extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Auth0Identity', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connection')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'provider')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isSocial')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'accessToken')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'accessTokenSecret')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'refreshToken')
    ..aOM<Auth0Profile>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profileData', protoName: 'profileData', subBuilder: Auth0Profile.create)
    ..hasRequiredFields = false
  ;

  Auth0Identity._() : super();
  factory Auth0Identity({
    $core.String? connection,
    $core.String? userId,
    $core.String? provider,
    $core.bool? isSocial,
    $core.String? accessToken,
    $core.String? accessTokenSecret,
    $core.String? refreshToken,
    Auth0Profile? profileData,
  }) {
    final _result = create();
    if (connection != null) {
      _result.connection = connection;
    }
    if (userId != null) {
      _result.userId = userId;
    }
    if (provider != null) {
      _result.provider = provider;
    }
    if (isSocial != null) {
      _result.isSocial = isSocial;
    }
    if (accessToken != null) {
      _result.accessToken = accessToken;
    }
    if (accessTokenSecret != null) {
      _result.accessTokenSecret = accessTokenSecret;
    }
    if (refreshToken != null) {
      _result.refreshToken = refreshToken;
    }
    if (profileData != null) {
      _result.profileData = profileData;
    }
    return _result;
  }
  factory Auth0Identity.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Auth0Identity.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Auth0Identity clone() => Auth0Identity()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Auth0Identity copyWith(void Function(Auth0Identity) updates) => super.copyWith((message) => updates(message as Auth0Identity)) as Auth0Identity; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Auth0Identity create() => Auth0Identity._();
  Auth0Identity createEmptyInstance() => create();
  static $pb.PbList<Auth0Identity> createRepeated() => $pb.PbList<Auth0Identity>();
  @$core.pragma('dart2js:noInline')
  static Auth0Identity getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Auth0Identity>(create);
  static Auth0Identity? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get connection => $_getSZ(0);
  @$pb.TagNumber(1)
  set connection($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasConnection() => $_has(0);
  @$pb.TagNumber(1)
  void clearConnection() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get userId => $_getSZ(1);
  @$pb.TagNumber(2)
  set userId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserId() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get provider => $_getSZ(2);
  @$pb.TagNumber(3)
  set provider($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasProvider() => $_has(2);
  @$pb.TagNumber(3)
  void clearProvider() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get isSocial => $_getBF(3);
  @$pb.TagNumber(4)
  set isSocial($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIsSocial() => $_has(3);
  @$pb.TagNumber(4)
  void clearIsSocial() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get accessToken => $_getSZ(4);
  @$pb.TagNumber(5)
  set accessToken($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasAccessToken() => $_has(4);
  @$pb.TagNumber(5)
  void clearAccessToken() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get accessTokenSecret => $_getSZ(5);
  @$pb.TagNumber(6)
  set accessTokenSecret($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAccessTokenSecret() => $_has(5);
  @$pb.TagNumber(6)
  void clearAccessTokenSecret() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get refreshToken => $_getSZ(6);
  @$pb.TagNumber(7)
  set refreshToken($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRefreshToken() => $_has(6);
  @$pb.TagNumber(7)
  void clearRefreshToken() => clearField(7);

  @$pb.TagNumber(8)
  Auth0Profile get profileData => $_getN(7);
  @$pb.TagNumber(8)
  set profileData(Auth0Profile v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasProfileData() => $_has(7);
  @$pb.TagNumber(8)
  void clearProfileData() => clearField(8);
  @$pb.TagNumber(8)
  Auth0Profile ensureProfileData() => $_ensure(7);
}

class Auth0User extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Auth0User', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'email')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'emailVerified')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'username')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneNumber')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneVerified')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'createdAt')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updatedAt')
    ..pc<Auth0Identity>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'identities', $pb.PbFieldType.PM, subBuilder: Auth0Identity.create)
    ..aOM<AppMetadata>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'appMetadata', subBuilder: AppMetadata.create)
    ..aOM<UserMetadata>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userMetadata', subBuilder: UserMetadata.create)
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'picture')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nickname')
    ..pPS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'multifactor')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastIp')
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastLogin')
    ..aInt64(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'loginsCount')
    ..aOB(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocked')
    ..aOS(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'givenName')
    ..aOS(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'familyName')
    ..hasRequiredFields = false
  ;

  Auth0User._() : super();
  factory Auth0User({
    $core.String? userId,
    $core.String? email,
    $core.bool? emailVerified,
    $core.String? username,
    $core.String? phoneNumber,
    $core.bool? phoneVerified,
    $core.String? createdAt,
    $core.String? updatedAt,
    $core.Iterable<Auth0Identity>? identities,
    AppMetadata? appMetadata,
    UserMetadata? userMetadata,
    $core.String? picture,
    $core.String? name,
    $core.String? nickname,
    $core.Iterable<$core.String>? multifactor,
    $core.String? lastIp,
    $core.String? lastLogin,
    $fixnum.Int64? loginsCount,
    $core.bool? blocked,
    $core.String? givenName,
    $core.String? familyName,
  }) {
    final _result = create();
    if (userId != null) {
      _result.userId = userId;
    }
    if (email != null) {
      _result.email = email;
    }
    if (emailVerified != null) {
      _result.emailVerified = emailVerified;
    }
    if (username != null) {
      _result.username = username;
    }
    if (phoneNumber != null) {
      _result.phoneNumber = phoneNumber;
    }
    if (phoneVerified != null) {
      _result.phoneVerified = phoneVerified;
    }
    if (createdAt != null) {
      _result.createdAt = createdAt;
    }
    if (updatedAt != null) {
      _result.updatedAt = updatedAt;
    }
    if (identities != null) {
      _result.identities.addAll(identities);
    }
    if (appMetadata != null) {
      _result.appMetadata = appMetadata;
    }
    if (userMetadata != null) {
      _result.userMetadata = userMetadata;
    }
    if (picture != null) {
      _result.picture = picture;
    }
    if (name != null) {
      _result.name = name;
    }
    if (nickname != null) {
      _result.nickname = nickname;
    }
    if (multifactor != null) {
      _result.multifactor.addAll(multifactor);
    }
    if (lastIp != null) {
      _result.lastIp = lastIp;
    }
    if (lastLogin != null) {
      _result.lastLogin = lastLogin;
    }
    if (loginsCount != null) {
      _result.loginsCount = loginsCount;
    }
    if (blocked != null) {
      _result.blocked = blocked;
    }
    if (givenName != null) {
      _result.givenName = givenName;
    }
    if (familyName != null) {
      _result.familyName = familyName;
    }
    return _result;
  }
  factory Auth0User.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Auth0User.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Auth0User clone() => Auth0User()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Auth0User copyWith(void Function(Auth0User) updates) => super.copyWith((message) => updates(message as Auth0User)) as Auth0User; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Auth0User create() => Auth0User._();
  Auth0User createEmptyInstance() => create();
  static $pb.PbList<Auth0User> createRepeated() => $pb.PbList<Auth0User>();
  @$core.pragma('dart2js:noInline')
  static Auth0User getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Auth0User>(create);
  static Auth0User? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get userId => $_getSZ(0);
  @$pb.TagNumber(1)
  set userId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUserId() => $_has(0);
  @$pb.TagNumber(1)
  void clearUserId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get email => $_getSZ(1);
  @$pb.TagNumber(2)
  set email($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasEmail() => $_has(1);
  @$pb.TagNumber(2)
  void clearEmail() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get emailVerified => $_getBF(2);
  @$pb.TagNumber(3)
  set emailVerified($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasEmailVerified() => $_has(2);
  @$pb.TagNumber(3)
  void clearEmailVerified() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get username => $_getSZ(3);
  @$pb.TagNumber(4)
  set username($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUsername() => $_has(3);
  @$pb.TagNumber(4)
  void clearUsername() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get phoneNumber => $_getSZ(4);
  @$pb.TagNumber(5)
  set phoneNumber($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPhoneNumber() => $_has(4);
  @$pb.TagNumber(5)
  void clearPhoneNumber() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get phoneVerified => $_getBF(5);
  @$pb.TagNumber(6)
  set phoneVerified($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPhoneVerified() => $_has(5);
  @$pb.TagNumber(6)
  void clearPhoneVerified() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get createdAt => $_getSZ(6);
  @$pb.TagNumber(7)
  set createdAt($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasCreatedAt() => $_has(6);
  @$pb.TagNumber(7)
  void clearCreatedAt() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get updatedAt => $_getSZ(7);
  @$pb.TagNumber(8)
  set updatedAt($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasUpdatedAt() => $_has(7);
  @$pb.TagNumber(8)
  void clearUpdatedAt() => clearField(8);

  @$pb.TagNumber(9)
  $core.List<Auth0Identity> get identities => $_getList(8);

  @$pb.TagNumber(10)
  AppMetadata get appMetadata => $_getN(9);
  @$pb.TagNumber(10)
  set appMetadata(AppMetadata v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasAppMetadata() => $_has(9);
  @$pb.TagNumber(10)
  void clearAppMetadata() => clearField(10);
  @$pb.TagNumber(10)
  AppMetadata ensureAppMetadata() => $_ensure(9);

  @$pb.TagNumber(11)
  UserMetadata get userMetadata => $_getN(10);
  @$pb.TagNumber(11)
  set userMetadata(UserMetadata v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasUserMetadata() => $_has(10);
  @$pb.TagNumber(11)
  void clearUserMetadata() => clearField(11);
  @$pb.TagNumber(11)
  UserMetadata ensureUserMetadata() => $_ensure(10);

  @$pb.TagNumber(12)
  $core.String get picture => $_getSZ(11);
  @$pb.TagNumber(12)
  set picture($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPicture() => $_has(11);
  @$pb.TagNumber(12)
  void clearPicture() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get name => $_getSZ(12);
  @$pb.TagNumber(13)
  set name($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasName() => $_has(12);
  @$pb.TagNumber(13)
  void clearName() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get nickname => $_getSZ(13);
  @$pb.TagNumber(14)
  set nickname($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasNickname() => $_has(13);
  @$pb.TagNumber(14)
  void clearNickname() => clearField(14);

  @$pb.TagNumber(15)
  $core.List<$core.String> get multifactor => $_getList(14);

  @$pb.TagNumber(16)
  $core.String get lastIp => $_getSZ(15);
  @$pb.TagNumber(16)
  set lastIp($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasLastIp() => $_has(15);
  @$pb.TagNumber(16)
  void clearLastIp() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get lastLogin => $_getSZ(16);
  @$pb.TagNumber(17)
  set lastLogin($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasLastLogin() => $_has(16);
  @$pb.TagNumber(17)
  void clearLastLogin() => clearField(17);

  @$pb.TagNumber(18)
  $fixnum.Int64 get loginsCount => $_getI64(17);
  @$pb.TagNumber(18)
  set loginsCount($fixnum.Int64 v) { $_setInt64(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasLoginsCount() => $_has(17);
  @$pb.TagNumber(18)
  void clearLoginsCount() => clearField(18);

  @$pb.TagNumber(19)
  $core.bool get blocked => $_getBF(18);
  @$pb.TagNumber(19)
  set blocked($core.bool v) { $_setBool(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasBlocked() => $_has(18);
  @$pb.TagNumber(19)
  void clearBlocked() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get givenName => $_getSZ(19);
  @$pb.TagNumber(20)
  set givenName($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasGivenName() => $_has(19);
  @$pb.TagNumber(20)
  void clearGivenName() => clearField(20);

  @$pb.TagNumber(21)
  $core.String get familyName => $_getSZ(20);
  @$pb.TagNumber(21)
  set familyName($core.String v) { $_setString(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasFamilyName() => $_has(20);
  @$pb.TagNumber(21)
  void clearFamilyName() => clearField(21);
}

class FleetView extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FleetView', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userAuth0Id')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pPS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'columns')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'search')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showInternal')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showMap')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showOffline')
    ..pPS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statuses')
    ..p<$fixnum.Int64>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pinnedRobotIds', $pb.PbFieldType.K6)
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId')
    ..e<ViewMode>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'viewMode', $pb.PbFieldType.OE, defaultOrMaker: ViewMode.VIEW_MODE_UNSPECIFIED, valueOf: ViewMode.valueOf, enumValues: ViewMode.values)
    ..p<$fixnum.Int64>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerIds', $pb.PbFieldType.K6)
    ..hasRequiredFields = false
  ;

  FleetView._() : super();
  factory FleetView({
    $75.DB? db,
    $core.String? userAuth0Id,
    $core.String? name,
    $core.Iterable<$core.String>? columns,
    $core.String? search,
    $core.bool? showInternal,
    $core.bool? showMap,
    $core.bool? showOffline,
    $core.Iterable<$core.String>? statuses,
    $core.Iterable<$fixnum.Int64>? pinnedRobotIds,
  @$core.Deprecated('This field is deprecated.')
    $fixnum.Int64? customerId,
    ViewMode? viewMode,
    $core.Iterable<$fixnum.Int64>? customerIds,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (userAuth0Id != null) {
      _result.userAuth0Id = userAuth0Id;
    }
    if (name != null) {
      _result.name = name;
    }
    if (columns != null) {
      _result.columns.addAll(columns);
    }
    if (search != null) {
      _result.search = search;
    }
    if (showInternal != null) {
      _result.showInternal = showInternal;
    }
    if (showMap != null) {
      _result.showMap = showMap;
    }
    if (showOffline != null) {
      _result.showOffline = showOffline;
    }
    if (statuses != null) {
      _result.statuses.addAll(statuses);
    }
    if (pinnedRobotIds != null) {
      _result.pinnedRobotIds.addAll(pinnedRobotIds);
    }
    if (customerId != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.customerId = customerId;
    }
    if (viewMode != null) {
      _result.viewMode = viewMode;
    }
    if (customerIds != null) {
      _result.customerIds.addAll(customerIds);
    }
    return _result;
  }
  factory FleetView.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FleetView.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FleetView clone() => FleetView()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FleetView copyWith(void Function(FleetView) updates) => super.copyWith((message) => updates(message as FleetView)) as FleetView; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FleetView create() => FleetView._();
  FleetView createEmptyInstance() => create();
  static $pb.PbList<FleetView> createRepeated() => $pb.PbList<FleetView>();
  @$core.pragma('dart2js:noInline')
  static FleetView getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FleetView>(create);
  static FleetView? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get userAuth0Id => $_getSZ(1);
  @$pb.TagNumber(2)
  set userAuth0Id($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserAuth0Id() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserAuth0Id() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.String> get columns => $_getList(3);

  @$pb.TagNumber(5)
  $core.String get search => $_getSZ(4);
  @$pb.TagNumber(5)
  set search($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSearch() => $_has(4);
  @$pb.TagNumber(5)
  void clearSearch() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get showInternal => $_getBF(5);
  @$pb.TagNumber(6)
  set showInternal($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasShowInternal() => $_has(5);
  @$pb.TagNumber(6)
  void clearShowInternal() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get showMap => $_getBF(6);
  @$pb.TagNumber(7)
  set showMap($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasShowMap() => $_has(6);
  @$pb.TagNumber(7)
  void clearShowMap() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get showOffline => $_getBF(7);
  @$pb.TagNumber(8)
  set showOffline($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasShowOffline() => $_has(7);
  @$pb.TagNumber(8)
  void clearShowOffline() => clearField(8);

  @$pb.TagNumber(9)
  $core.List<$core.String> get statuses => $_getList(8);

  @$pb.TagNumber(10)
  $core.List<$fixnum.Int64> get pinnedRobotIds => $_getList(9);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $fixnum.Int64 get customerId => $_getI64(10);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  set customerId($fixnum.Int64 v) { $_setInt64(10, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.bool hasCustomerId() => $_has(10);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  void clearCustomerId() => clearField(11);

  @$pb.TagNumber(12)
  ViewMode get viewMode => $_getN(11);
  @$pb.TagNumber(12)
  set viewMode(ViewMode v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasViewMode() => $_has(11);
  @$pb.TagNumber(12)
  void clearViewMode() => clearField(12);

  @$pb.TagNumber(13)
  $core.List<$fixnum.Int64> get customerIds => $_getList(12);
}

class UserResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UserResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOM<Auth0User>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'user', subBuilder: Auth0User.create)
    ..aOM<$80.CustomerResponse>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customer', subBuilder: $80.CustomerResponse.create)
    ..pc<FleetView>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fleetViews', $pb.PbFieldType.PM, protoName: 'fleetViews', subBuilder: FleetView.create)
    ..aOM<Permissions>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'permissions', subBuilder: Permissions.create)
    ..hasRequiredFields = false
  ;

  UserResponse._() : super();
  factory UserResponse({
    Auth0User? user,
    $80.CustomerResponse? customer,
    $core.Iterable<FleetView>? fleetViews,
    Permissions? permissions,
  }) {
    final _result = create();
    if (user != null) {
      _result.user = user;
    }
    if (customer != null) {
      _result.customer = customer;
    }
    if (fleetViews != null) {
      _result.fleetViews.addAll(fleetViews);
    }
    if (permissions != null) {
      _result.permissions = permissions;
    }
    return _result;
  }
  factory UserResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UserResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UserResponse clone() => UserResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UserResponse copyWith(void Function(UserResponse) updates) => super.copyWith((message) => updates(message as UserResponse)) as UserResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UserResponse create() => UserResponse._();
  UserResponse createEmptyInstance() => create();
  static $pb.PbList<UserResponse> createRepeated() => $pb.PbList<UserResponse>();
  @$core.pragma('dart2js:noInline')
  static UserResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UserResponse>(create);
  static UserResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Auth0User get user => $_getN(0);
  @$pb.TagNumber(1)
  set user(Auth0User v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasUser() => $_has(0);
  @$pb.TagNumber(1)
  void clearUser() => clearField(1);
  @$pb.TagNumber(1)
  Auth0User ensureUser() => $_ensure(0);

  @$pb.TagNumber(2)
  $80.CustomerResponse get customer => $_getN(1);
  @$pb.TagNumber(2)
  set customer($80.CustomerResponse v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasCustomer() => $_has(1);
  @$pb.TagNumber(2)
  void clearCustomer() => clearField(2);
  @$pb.TagNumber(2)
  $80.CustomerResponse ensureCustomer() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.List<FleetView> get fleetViews => $_getList(2);

  @$pb.TagNumber(4)
  Permissions get permissions => $_getN(3);
  @$pb.TagNumber(4)
  set permissions(Permissions v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasPermissions() => $_has(3);
  @$pb.TagNumber(4)
  void clearPermissions() => clearField(4);
  @$pb.TagNumber(4)
  Permissions ensurePermissions() => $_ensure(3);
}

class Permissions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Permissions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'names')
    ..hasRequiredFields = false
  ;

  Permissions._() : super();
  factory Permissions({
    $core.Iterable<$core.String>? names,
  }) {
    final _result = create();
    if (names != null) {
      _result.names.addAll(names);
    }
    return _result;
  }
  factory Permissions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Permissions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Permissions clone() => Permissions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Permissions copyWith(void Function(Permissions) updates) => super.copyWith((message) => updates(message as Permissions)) as Permissions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Permissions create() => Permissions._();
  Permissions createEmptyInstance() => create();
  static $pb.PbList<Permissions> createRepeated() => $pb.PbList<Permissions>();
  @$core.pragma('dart2js:noInline')
  static Permissions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Permissions>(create);
  static Permissions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get names => $_getList(0);
}

class IsCarbonRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IsCarbonRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userIds')
    ..hasRequiredFields = false
  ;

  IsCarbonRequest._() : super();
  factory IsCarbonRequest({
    $core.Iterable<$core.String>? userIds,
  }) {
    final _result = create();
    if (userIds != null) {
      _result.userIds.addAll(userIds);
    }
    return _result;
  }
  factory IsCarbonRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IsCarbonRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IsCarbonRequest clone() => IsCarbonRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IsCarbonRequest copyWith(void Function(IsCarbonRequest) updates) => super.copyWith((message) => updates(message as IsCarbonRequest)) as IsCarbonRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IsCarbonRequest create() => IsCarbonRequest._();
  IsCarbonRequest createEmptyInstance() => create();
  static $pb.PbList<IsCarbonRequest> createRepeated() => $pb.PbList<IsCarbonRequest>();
  @$core.pragma('dart2js:noInline')
  static IsCarbonRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IsCarbonRequest>(create);
  static IsCarbonRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get userIds => $_getList(0);
}

class IsCarbonResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IsCarbonResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..pc<IsCarbonUser>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'users', $pb.PbFieldType.PM, subBuilder: IsCarbonUser.create)
    ..hasRequiredFields = false
  ;

  IsCarbonResponse._() : super();
  factory IsCarbonResponse({
    $core.Iterable<IsCarbonUser>? users,
  }) {
    final _result = create();
    if (users != null) {
      _result.users.addAll(users);
    }
    return _result;
  }
  factory IsCarbonResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IsCarbonResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IsCarbonResponse clone() => IsCarbonResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IsCarbonResponse copyWith(void Function(IsCarbonResponse) updates) => super.copyWith((message) => updates(message as IsCarbonResponse)) as IsCarbonResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IsCarbonResponse create() => IsCarbonResponse._();
  IsCarbonResponse createEmptyInstance() => create();
  static $pb.PbList<IsCarbonResponse> createRepeated() => $pb.PbList<IsCarbonResponse>();
  @$core.pragma('dart2js:noInline')
  static IsCarbonResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IsCarbonResponse>(create);
  static IsCarbonResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<IsCarbonUser> get users => $_getList(0);
}

class IsCarbonUser extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IsCarbonUser', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.users'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isCarbon')
    ..hasRequiredFields = false
  ;

  IsCarbonUser._() : super();
  factory IsCarbonUser({
    $core.String? userId,
    $core.bool? isCarbon,
  }) {
    final _result = create();
    if (userId != null) {
      _result.userId = userId;
    }
    if (isCarbon != null) {
      _result.isCarbon = isCarbon;
    }
    return _result;
  }
  factory IsCarbonUser.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IsCarbonUser.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IsCarbonUser clone() => IsCarbonUser()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IsCarbonUser copyWith(void Function(IsCarbonUser) updates) => super.copyWith((message) => updates(message as IsCarbonUser)) as IsCarbonUser; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IsCarbonUser create() => IsCarbonUser._();
  IsCarbonUser createEmptyInstance() => create();
  static $pb.PbList<IsCarbonUser> createRepeated() => $pb.PbList<IsCarbonUser>();
  @$core.pragma('dart2js:noInline')
  static IsCarbonUser getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IsCarbonUser>(create);
  static IsCarbonUser? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get userId => $_getSZ(0);
  @$pb.TagNumber(1)
  set userId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUserId() => $_has(0);
  @$pb.TagNumber(1)
  void clearUserId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get isCarbon => $_getBF(1);
  @$pb.TagNumber(2)
  set isCarbon($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIsCarbon() => $_has(1);
  @$pb.TagNumber(2)
  void clearIsCarbon() => clearField(2);
}

