///
//  Generated code. Do not modify.
//  source: portal/metrics.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use dailyMetricResponseDescriptor instead')
const DailyMetricResponse$json = const {
  '1': 'DailyMetricResponse',
  '2': const [
    const {'1': 'db', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.db.DB', '10': 'db'},
    const {'1': 'serial', '3': 2, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'robot_id', '3': 3, '4': 1, '5': 4, '10': 'robotId'},
    const {'1': 'date', '3': 25, '4': 1, '5': 9, '10': 'date'},
    const {'1': 'job_id', '3': 44, '4': 1, '5': 9, '10': 'jobId'},
    const {'1': 'job_name', '3': 45, '4': 1, '5': 9, '10': 'jobName'},
    const {'1': 'acres_weeded', '3': 4, '4': 1, '5': 2, '10': 'acresWeeded'},
    const {'1': 'avg_speed_mph', '3': 5, '4': 1, '5': 2, '10': 'avgSpeedMph'},
    const {'1': 'avg_weed_size_mm', '3': 6, '4': 1, '5': 2, '10': 'avgWeedSizeMm'},
    const {'1': 'banding_config_name', '3': 7, '4': 1, '5': 9, '10': 'bandingConfigName'},
    const {'1': 'banding_enabled', '3': 8, '4': 1, '5': 8, '10': 'bandingEnabled'},
    const {'1': 'coverage_speed_acres_hr', '3': 9, '4': 1, '5': 2, '10': 'coverageSpeedAcresHr'},
    const {'1': 'distance_weeded_meters', '3': 10, '4': 1, '5': 2, '10': 'distanceWeededMeters'},
    const {'1': 'killed_weeds', '3': 11, '4': 1, '5': 3, '10': 'killedWeeds'},
    const {'1': 'missed_weeds', '3': 12, '4': 1, '5': 3, '10': 'missedWeeds'},
    const {'1': 'skipped_weeds', '3': 13, '4': 1, '5': 3, '10': 'skippedWeeds'},
    const {'1': 'time_efficiency', '3': 14, '4': 1, '5': 2, '10': 'timeEfficiency'},
    const {'1': 'total_weeds', '3': 15, '4': 1, '5': 3, '10': 'totalWeeds'},
    const {'1': 'uptime_seconds', '3': 16, '4': 1, '5': 2, '10': 'uptimeSeconds'},
    const {'1': 'weed_density_sq_ft', '3': 17, '4': 1, '5': 2, '10': 'weedDensitySqFt'},
    const {'1': 'weeding_uptime_seconds', '3': 18, '4': 1, '5': 2, '10': 'weedingUptimeSeconds'},
    const {'1': 'weeding_efficiency', '3': 19, '4': 1, '5': 2, '10': 'weedingEfficiency'},
    const {'1': 'weeds_type_count_broadleaf', '3': 20, '4': 1, '5': 3, '10': 'weedsTypeCountBroadleaf'},
    const {'1': 'weeds_type_count_grass', '3': 21, '4': 1, '5': 3, '10': 'weedsTypeCountGrass'},
    const {'1': 'weeds_type_count_offshoot', '3': 22, '4': 1, '5': 3, '10': 'weedsTypeCountOffshoot'},
    const {'1': 'weeds_type_count_purslane', '3': 23, '4': 1, '5': 3, '10': 'weedsTypeCountPurslane'},
    const {'1': 'not_weeding_weeds', '3': 24, '4': 1, '5': 3, '10': 'notWeedingWeeds'},
    const {'1': 'kept_crops', '3': 26, '4': 1, '5': 3, '10': 'keptCrops'},
    const {'1': 'missed_crops', '3': 27, '4': 1, '5': 3, '10': 'missedCrops'},
    const {'1': 'not_thinning', '3': 28, '4': 1, '5': 3, '10': 'notThinning'},
    const {'1': 'not_weeding', '3': 29, '4': 1, '5': 3, '10': 'notWeeding'},
    const {'1': 'skipped_crops', '3': 30, '4': 1, '5': 3, '10': 'skippedCrops'},
    const {'1': 'thinned_crops', '3': 31, '4': 1, '5': 3, '10': 'thinnedCrops'},
    const {'1': 'total_crops', '3': 32, '4': 1, '5': 3, '10': 'totalCrops'},
    const {'1': 'banding_percentage', '3': 33, '4': 1, '5': 2, '10': 'bandingPercentage'},
    const {'1': 'thinning_efficiency', '3': 34, '4': 1, '5': 2, '10': 'thinningEfficiency'},
    const {'1': 'crop_id', '3': 35, '4': 1, '5': 9, '10': 'cropId'},
    const {'1': 'crop', '3': 36, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'crop_density_sq_ft', '3': 37, '4': 1, '5': 2, '10': 'cropDensitySqFt'},
    const {'1': 'avg_crop_size_mm', '3': 38, '4': 1, '5': 2, '10': 'avgCropSizeMm'},
    const {'1': 'avg_targetable_req_laser_time', '3': 39, '4': 1, '5': 3, '10': 'avgTargetableReqLaserTime'},
    const {'1': 'avg_untargetable_req_laser_time', '3': 40, '4': 1, '5': 3, '10': 'avgUntargetableReqLaserTime'},
    const {'1': 'valid_crops', '3': 41, '4': 1, '5': 3, '10': 'validCrops'},
    const {'1': 'operator_effectiveness', '3': 42, '4': 1, '5': 2, '10': 'operatorEffectiveness'},
    const {'1': 'target_weeding_time_seconds', '3': 43, '4': 1, '5': 3, '10': 'targetWeedingTimeSeconds'},
    const {'1': 'embeddings_active_uptime_seconds', '3': 46, '4': 1, '5': 2, '10': 'embeddingsActiveUptimeSeconds'},
  ],
};

/// Descriptor for `DailyMetricResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dailyMetricResponseDescriptor = $convert.base64Decode('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');
@$core.Deprecated('Use dailyMetricsByDateResponseDescriptor instead')
const DailyMetricsByDateResponse$json = const {
  '1': 'DailyMetricsByDateResponse',
  '2': const [
    const {'1': 'metrics', '3': 1, '4': 3, '5': 11, '6': '.carbon.portal.metrics.DailyMetricsByDateResponse.MetricsEntry', '10': 'metrics'},
  ],
  '3': const [DailyMetricsByDateResponse_MetricsEntry$json],
};

@$core.Deprecated('Use dailyMetricsByDateResponseDescriptor instead')
const DailyMetricsByDateResponse_MetricsEntry$json = const {
  '1': 'MetricsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.metrics.DailyMetricResponse', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `DailyMetricsByDateResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dailyMetricsByDateResponseDescriptor = $convert.base64Decode('ChpEYWlseU1ldHJpY3NCeURhdGVSZXNwb25zZRJYCgdtZXRyaWNzGAEgAygLMj4uY2FyYm9uLnBvcnRhbC5tZXRyaWNzLkRhaWx5TWV0cmljc0J5RGF0ZVJlc3BvbnNlLk1ldHJpY3NFbnRyeVIHbWV0cmljcxpmCgxNZXRyaWNzRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSQAoFdmFsdWUYAiABKAsyKi5jYXJib24ucG9ydGFsLm1ldHJpY3MuRGFpbHlNZXRyaWNSZXNwb25zZVIFdmFsdWU6AjgB');
@$core.Deprecated('Use dailyMetricsByDateByRobotResponseDescriptor instead')
const DailyMetricsByDateByRobotResponse$json = const {
  '1': 'DailyMetricsByDateByRobotResponse',
  '2': const [
    const {'1': 'metrics', '3': 1, '4': 3, '5': 11, '6': '.carbon.portal.metrics.DailyMetricsByDateByRobotResponse.MetricsEntry', '10': 'metrics'},
  ],
  '3': const [DailyMetricsByDateByRobotResponse_MetricsEntry$json],
};

@$core.Deprecated('Use dailyMetricsByDateByRobotResponseDescriptor instead')
const DailyMetricsByDateByRobotResponse_MetricsEntry$json = const {
  '1': 'MetricsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.metrics.DailyMetricsByDateResponse', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `DailyMetricsByDateByRobotResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dailyMetricsByDateByRobotResponseDescriptor = $convert.base64Decode('CiFEYWlseU1ldHJpY3NCeURhdGVCeVJvYm90UmVzcG9uc2USXwoHbWV0cmljcxgBIAMoCzJFLmNhcmJvbi5wb3J0YWwubWV0cmljcy5EYWlseU1ldHJpY3NCeURhdGVCeVJvYm90UmVzcG9uc2UuTWV0cmljc0VudHJ5UgdtZXRyaWNzGm0KDE1ldHJpY3NFbnRyeRIQCgNrZXkYASABKAlSA2tleRJHCgV2YWx1ZRgCIAEoCzIxLmNhcmJvbi5wb3J0YWwubWV0cmljcy5EYWlseU1ldHJpY3NCeURhdGVSZXNwb25zZVIFdmFsdWU6AjgB');
