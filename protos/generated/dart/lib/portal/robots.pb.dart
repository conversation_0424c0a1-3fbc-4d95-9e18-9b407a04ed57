///
//  Generated code. Do not modify.
//  source: portal/robots.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'health.pb.dart' as $45;
import 'db.pb.dart' as $75;
import 'alarms.pb.dart' as $81;
import 'customers.pb.dart' as $80;
import 'metrics.pb.dart' as $79;
import '../config/api/config_service.pb.dart' as $2;

import '../frontend/status_bar.pbenum.dart' as $34;

class CachedRobotHealth extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CachedRobotHealth', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.robots'), createEmptyInstance: create)
    ..aOM<$45.Location>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: $45.Location.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'model')
    ..aOM<$45.Performance>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'performance', subBuilder: $45.Performance.create)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reportedAt')
    ..e<$34.Status>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: $34.Status.STATUS_ERROR, valueOf: $34.Status.valueOf, enumValues: $34.Status.values)
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusChangedAt')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2p')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'softwareVersion')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVersion')
    ..aOB(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVersionReady')
    ..m<$core.String, $fixnum.Int64>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metricTotals', entryClassName: 'CachedRobotHealth.MetricTotalsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OU6, packageName: const $pb.PackageName('carbon.portal.robots'))
    ..aOM<$45.FieldConfig>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fieldConfig', subBuilder: $45.FieldConfig.create)
    ..aOS(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..a<$core.int>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotRuntime240v', $pb.PbFieldType.OU3, protoName: 'robot_runtime_240v')
    ..aInt64(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeAlarmCount')
    ..aOS(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timezone')
    ..hasRequiredFields = false
  ;

  CachedRobotHealth._() : super();
  factory CachedRobotHealth({
    $45.Location? location,
    $core.String? model,
    $45.Performance? performance,
    $fixnum.Int64? reportedAt,
    $34.Status? status,
    $fixnum.Int64? statusChangedAt,
  @$core.Deprecated('This field is deprecated.')
    $core.String? crop,
    $core.String? p2p,
    $core.String? softwareVersion,
    $core.String? targetVersion,
    $core.bool? targetVersionReady,
    $core.Map<$core.String, $fixnum.Int64>? metricTotals,
    $45.FieldConfig? fieldConfig,
    $core.String? cropId,
    $core.int? robotRuntime240v,
    $fixnum.Int64? activeAlarmCount,
    $core.String? timezone,
  }) {
    final _result = create();
    if (location != null) {
      _result.location = location;
    }
    if (model != null) {
      _result.model = model;
    }
    if (performance != null) {
      _result.performance = performance;
    }
    if (reportedAt != null) {
      _result.reportedAt = reportedAt;
    }
    if (status != null) {
      _result.status = status;
    }
    if (statusChangedAt != null) {
      _result.statusChangedAt = statusChangedAt;
    }
    if (crop != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.crop = crop;
    }
    if (p2p != null) {
      _result.p2p = p2p;
    }
    if (softwareVersion != null) {
      _result.softwareVersion = softwareVersion;
    }
    if (targetVersion != null) {
      _result.targetVersion = targetVersion;
    }
    if (targetVersionReady != null) {
      _result.targetVersionReady = targetVersionReady;
    }
    if (metricTotals != null) {
      _result.metricTotals.addAll(metricTotals);
    }
    if (fieldConfig != null) {
      _result.fieldConfig = fieldConfig;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (robotRuntime240v != null) {
      _result.robotRuntime240v = robotRuntime240v;
    }
    if (activeAlarmCount != null) {
      _result.activeAlarmCount = activeAlarmCount;
    }
    if (timezone != null) {
      _result.timezone = timezone;
    }
    return _result;
  }
  factory CachedRobotHealth.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CachedRobotHealth.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CachedRobotHealth clone() => CachedRobotHealth()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CachedRobotHealth copyWith(void Function(CachedRobotHealth) updates) => super.copyWith((message) => updates(message as CachedRobotHealth)) as CachedRobotHealth; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CachedRobotHealth create() => CachedRobotHealth._();
  CachedRobotHealth createEmptyInstance() => create();
  static $pb.PbList<CachedRobotHealth> createRepeated() => $pb.PbList<CachedRobotHealth>();
  @$core.pragma('dart2js:noInline')
  static CachedRobotHealth getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CachedRobotHealth>(create);
  static CachedRobotHealth? _defaultInstance;

  @$pb.TagNumber(2)
  $45.Location get location => $_getN(0);
  @$pb.TagNumber(2)
  set location($45.Location v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(0);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  $45.Location ensureLocation() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.String get model => $_getSZ(1);
  @$pb.TagNumber(3)
  set model($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasModel() => $_has(1);
  @$pb.TagNumber(3)
  void clearModel() => clearField(3);

  @$pb.TagNumber(5)
  $45.Performance get performance => $_getN(2);
  @$pb.TagNumber(5)
  set performance($45.Performance v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasPerformance() => $_has(2);
  @$pb.TagNumber(5)
  void clearPerformance() => clearField(5);
  @$pb.TagNumber(5)
  $45.Performance ensurePerformance() => $_ensure(2);

  @$pb.TagNumber(6)
  $fixnum.Int64 get reportedAt => $_getI64(3);
  @$pb.TagNumber(6)
  set reportedAt($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasReportedAt() => $_has(3);
  @$pb.TagNumber(6)
  void clearReportedAt() => clearField(6);

  @$pb.TagNumber(9)
  $34.Status get status => $_getN(4);
  @$pb.TagNumber(9)
  set status($34.Status v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasStatus() => $_has(4);
  @$pb.TagNumber(9)
  void clearStatus() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get statusChangedAt => $_getI64(5);
  @$pb.TagNumber(10)
  set statusChangedAt($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(10)
  $core.bool hasStatusChangedAt() => $_has(5);
  @$pb.TagNumber(10)
  void clearStatusChangedAt() => clearField(10);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.String get crop => $_getSZ(6);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  set crop($core.String v) { $_setString(6, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.bool hasCrop() => $_has(6);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  void clearCrop() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get p2p => $_getSZ(7);
  @$pb.TagNumber(12)
  set p2p($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(12)
  $core.bool hasP2p() => $_has(7);
  @$pb.TagNumber(12)
  void clearP2p() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get softwareVersion => $_getSZ(8);
  @$pb.TagNumber(13)
  set softwareVersion($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(13)
  $core.bool hasSoftwareVersion() => $_has(8);
  @$pb.TagNumber(13)
  void clearSoftwareVersion() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get targetVersion => $_getSZ(9);
  @$pb.TagNumber(14)
  set targetVersion($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(14)
  $core.bool hasTargetVersion() => $_has(9);
  @$pb.TagNumber(14)
  void clearTargetVersion() => clearField(14);

  @$pb.TagNumber(15)
  $core.bool get targetVersionReady => $_getBF(10);
  @$pb.TagNumber(15)
  set targetVersionReady($core.bool v) { $_setBool(10, v); }
  @$pb.TagNumber(15)
  $core.bool hasTargetVersionReady() => $_has(10);
  @$pb.TagNumber(15)
  void clearTargetVersionReady() => clearField(15);

  @$pb.TagNumber(17)
  $core.Map<$core.String, $fixnum.Int64> get metricTotals => $_getMap(11);

  @$pb.TagNumber(19)
  $45.FieldConfig get fieldConfig => $_getN(12);
  @$pb.TagNumber(19)
  set fieldConfig($45.FieldConfig v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasFieldConfig() => $_has(12);
  @$pb.TagNumber(19)
  void clearFieldConfig() => clearField(19);
  @$pb.TagNumber(19)
  $45.FieldConfig ensureFieldConfig() => $_ensure(12);

  @$pb.TagNumber(21)
  $core.String get cropId => $_getSZ(13);
  @$pb.TagNumber(21)
  set cropId($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(21)
  $core.bool hasCropId() => $_has(13);
  @$pb.TagNumber(21)
  void clearCropId() => clearField(21);

  @$pb.TagNumber(22)
  $core.int get robotRuntime240v => $_getIZ(14);
  @$pb.TagNumber(22)
  set robotRuntime240v($core.int v) { $_setUnsignedInt32(14, v); }
  @$pb.TagNumber(22)
  $core.bool hasRobotRuntime240v() => $_has(14);
  @$pb.TagNumber(22)
  void clearRobotRuntime240v() => clearField(22);

  @$pb.TagNumber(23)
  $fixnum.Int64 get activeAlarmCount => $_getI64(15);
  @$pb.TagNumber(23)
  set activeAlarmCount($fixnum.Int64 v) { $_setInt64(15, v); }
  @$pb.TagNumber(23)
  $core.bool hasActiveAlarmCount() => $_has(15);
  @$pb.TagNumber(23)
  void clearActiveAlarmCount() => clearField(23);

  @$pb.TagNumber(24)
  $core.String get timezone => $_getSZ(16);
  @$pb.TagNumber(24)
  set timezone($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(24)
  $core.bool hasTimezone() => $_has(16);
  @$pb.TagNumber(24)
  void clearTimezone() => clearField(24);
}

class RobotResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.robots'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOM<CachedRobotHealth>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'health', subBuilder: CachedRobotHealth.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'implementationStatus')
    ..m<$core.String, $core.bool>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'featureFlags', entryClassName: 'RobotResponse.FeatureFlagsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OB, packageName: const $pb.PackageName('carbon.portal.robots'))
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerSerial')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'supportSlack')
    ..hasRequiredFields = false
  ;

  RobotResponse._() : super();
  factory RobotResponse({
    $75.DB? db,
    CachedRobotHealth? health,
    $core.String? serial,
    $core.String? implementationStatus,
    $core.Map<$core.String, $core.bool>? featureFlags,
    $core.String? customerSerial,
    $core.String? supportSlack,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (health != null) {
      _result.health = health;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    if (implementationStatus != null) {
      _result.implementationStatus = implementationStatus;
    }
    if (featureFlags != null) {
      _result.featureFlags.addAll(featureFlags);
    }
    if (customerSerial != null) {
      _result.customerSerial = customerSerial;
    }
    if (supportSlack != null) {
      _result.supportSlack = supportSlack;
    }
    return _result;
  }
  factory RobotResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotResponse clone() => RobotResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotResponse copyWith(void Function(RobotResponse) updates) => super.copyWith((message) => updates(message as RobotResponse)) as RobotResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotResponse create() => RobotResponse._();
  RobotResponse createEmptyInstance() => create();
  static $pb.PbList<RobotResponse> createRepeated() => $pb.PbList<RobotResponse>();
  @$core.pragma('dart2js:noInline')
  static RobotResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotResponse>(create);
  static RobotResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  CachedRobotHealth get health => $_getN(1);
  @$pb.TagNumber(2)
  set health(CachedRobotHealth v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasHealth() => $_has(1);
  @$pb.TagNumber(2)
  void clearHealth() => clearField(2);
  @$pb.TagNumber(2)
  CachedRobotHealth ensureHealth() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get serial => $_getSZ(2);
  @$pb.TagNumber(3)
  set serial($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSerial() => $_has(2);
  @$pb.TagNumber(3)
  void clearSerial() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get implementationStatus => $_getSZ(3);
  @$pb.TagNumber(4)
  set implementationStatus($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasImplementationStatus() => $_has(3);
  @$pb.TagNumber(4)
  void clearImplementationStatus() => clearField(4);

  @$pb.TagNumber(5)
  $core.Map<$core.String, $core.bool> get featureFlags => $_getMap(4);

  @$pb.TagNumber(6)
  $core.String get customerSerial => $_getSZ(5);
  @$pb.TagNumber(6)
  set customerSerial($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCustomerSerial() => $_has(5);
  @$pb.TagNumber(6)
  void clearCustomerSerial() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get supportSlack => $_getSZ(6);
  @$pb.TagNumber(7)
  set supportSlack($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSupportSlack() => $_has(6);
  @$pb.TagNumber(7)
  void clearSupportSlack() => clearField(7);
}

class RobotSummaryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotSummaryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.robots'), createEmptyInstance: create)
    ..aOM<RobotResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robot', subBuilder: RobotResponse.create)
    ..pc<$81.AlarmResponse>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: $81.AlarmResponse.create)
    ..pc<$45.HealthLog>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'healthLogs', $pb.PbFieldType.PM, subBuilder: $45.HealthLog.create)
    ..aOM<$80.CustomerResponse>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customer', subBuilder: $80.CustomerResponse.create)
    ..pc<$81.AlarmResponse>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarmList', $pb.PbFieldType.PM, subBuilder: $81.AlarmResponse.create)
    ..aOM<$79.DailyMetricResponse>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dailyMetrics', subBuilder: $79.DailyMetricResponse.create)
    ..aOM<$2.ConfigNode>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $2.ConfigNode.create)
    ..hasRequiredFields = false
  ;

  RobotSummaryResponse._() : super();
  factory RobotSummaryResponse({
    RobotResponse? robot,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$81.AlarmResponse>? alarms,
    $core.Iterable<$45.HealthLog>? healthLogs,
    $80.CustomerResponse? customer,
    $core.Iterable<$81.AlarmResponse>? alarmList,
    $79.DailyMetricResponse? dailyMetrics,
    $2.ConfigNode? config,
  }) {
    final _result = create();
    if (robot != null) {
      _result.robot = robot;
    }
    if (alarms != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.alarms.addAll(alarms);
    }
    if (healthLogs != null) {
      _result.healthLogs.addAll(healthLogs);
    }
    if (customer != null) {
      _result.customer = customer;
    }
    if (alarmList != null) {
      _result.alarmList.addAll(alarmList);
    }
    if (dailyMetrics != null) {
      _result.dailyMetrics = dailyMetrics;
    }
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory RobotSummaryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotSummaryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotSummaryResponse clone() => RobotSummaryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotSummaryResponse copyWith(void Function(RobotSummaryResponse) updates) => super.copyWith((message) => updates(message as RobotSummaryResponse)) as RobotSummaryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotSummaryResponse create() => RobotSummaryResponse._();
  RobotSummaryResponse createEmptyInstance() => create();
  static $pb.PbList<RobotSummaryResponse> createRepeated() => $pb.PbList<RobotSummaryResponse>();
  @$core.pragma('dart2js:noInline')
  static RobotSummaryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotSummaryResponse>(create);
  static RobotSummaryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  RobotResponse get robot => $_getN(0);
  @$pb.TagNumber(1)
  set robot(RobotResponse v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobot() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobot() => clearField(1);
  @$pb.TagNumber(1)
  RobotResponse ensureRobot() => $_ensure(0);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.List<$81.AlarmResponse> get alarms => $_getList(1);

  @$pb.TagNumber(4)
  $core.List<$45.HealthLog> get healthLogs => $_getList(2);

  @$pb.TagNumber(5)
  $80.CustomerResponse get customer => $_getN(3);
  @$pb.TagNumber(5)
  set customer($80.CustomerResponse v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasCustomer() => $_has(3);
  @$pb.TagNumber(5)
  void clearCustomer() => clearField(5);
  @$pb.TagNumber(5)
  $80.CustomerResponse ensureCustomer() => $_ensure(3);

  @$pb.TagNumber(6)
  $core.List<$81.AlarmResponse> get alarmList => $_getList(4);

  @$pb.TagNumber(7)
  $79.DailyMetricResponse get dailyMetrics => $_getN(5);
  @$pb.TagNumber(7)
  set dailyMetrics($79.DailyMetricResponse v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasDailyMetrics() => $_has(5);
  @$pb.TagNumber(7)
  void clearDailyMetrics() => clearField(7);
  @$pb.TagNumber(7)
  $79.DailyMetricResponse ensureDailyMetrics() => $_ensure(5);

  @$pb.TagNumber(8)
  $2.ConfigNode get config => $_getN(6);
  @$pb.TagNumber(8)
  set config($2.ConfigNode v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasConfig() => $_has(6);
  @$pb.TagNumber(8)
  void clearConfig() => clearField(8);
  @$pb.TagNumber(8)
  $2.ConfigNode ensureConfig() => $_ensure(6);
}

class RobotSummaryListReponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotSummaryListReponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.robots'), createEmptyInstance: create)
    ..pc<RobotSummaryResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robots', $pb.PbFieldType.PM, subBuilder: RobotSummaryResponse.create)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'total')
    ..hasRequiredFields = false
  ;

  RobotSummaryListReponse._() : super();
  factory RobotSummaryListReponse({
    $core.Iterable<RobotSummaryResponse>? robots,
    $fixnum.Int64? total,
  }) {
    final _result = create();
    if (robots != null) {
      _result.robots.addAll(robots);
    }
    if (total != null) {
      _result.total = total;
    }
    return _result;
  }
  factory RobotSummaryListReponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotSummaryListReponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotSummaryListReponse clone() => RobotSummaryListReponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotSummaryListReponse copyWith(void Function(RobotSummaryListReponse) updates) => super.copyWith((message) => updates(message as RobotSummaryListReponse)) as RobotSummaryListReponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotSummaryListReponse create() => RobotSummaryListReponse._();
  RobotSummaryListReponse createEmptyInstance() => create();
  static $pb.PbList<RobotSummaryListReponse> createRepeated() => $pb.PbList<RobotSummaryListReponse>();
  @$core.pragma('dart2js:noInline')
  static RobotSummaryListReponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotSummaryListReponse>(create);
  static RobotSummaryListReponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<RobotSummaryResponse> get robots => $_getList(0);

  @$pb.TagNumber(2)
  $fixnum.Int64 get total => $_getI64(1);
  @$pb.TagNumber(2)
  set total($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTotal() => $_has(1);
  @$pb.TagNumber(2)
  void clearTotal() => clearField(2);
}

class RemoteResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RemoteResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.robots'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'host')
    ..hasRequiredFields = false
  ;

  RemoteResponse._() : super();
  factory RemoteResponse({
    $core.String? host,
  }) {
    final _result = create();
    if (host != null) {
      _result.host = host;
    }
    return _result;
  }
  factory RemoteResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RemoteResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RemoteResponse clone() => RemoteResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RemoteResponse copyWith(void Function(RemoteResponse) updates) => super.copyWith((message) => updates(message as RemoteResponse)) as RemoteResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RemoteResponse create() => RemoteResponse._();
  RemoteResponse createEmptyInstance() => create();
  static $pb.PbList<RemoteResponse> createRepeated() => $pb.PbList<RemoteResponse>();
  @$core.pragma('dart2js:noInline')
  static RemoteResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RemoteResponse>(create);
  static RemoteResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get host => $_getSZ(0);
  @$pb.TagNumber(1)
  set host($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHost() => $_has(0);
  @$pb.TagNumber(1)
  void clearHost() => clearField(1);
}

