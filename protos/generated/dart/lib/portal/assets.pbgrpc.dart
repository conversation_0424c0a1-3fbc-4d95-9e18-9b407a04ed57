///
//  Generated code. Do not modify.
//  source: portal/assets.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'assets.pb.dart' as $42;
import '../google/protobuf/empty.pb.dart' as $43;
export 'assets.pb.dart';

class AssetManifestServiceClient extends $grpc.Client {
  static final _$uploadAssetManifest =
      $grpc.ClientMethod<$42.AssetManifest, $43.Empty>(
          '/carbon.portal.assets.AssetManifestService/UploadAssetManifest',
          ($42.AssetManifest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $43.Empty.fromBuffer(value));

  AssetManifestServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$43.Empty> uploadAssetManifest($42.AssetManifest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadAssetManifest, request, options: options);
  }
}

abstract class AssetManifestServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.assets.AssetManifestService';

  AssetManifestServiceBase() {
    $addMethod($grpc.ServiceMethod<$42.AssetManifest, $43.Empty>(
        'UploadAssetManifest',
        uploadAssetManifest_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $42.AssetManifest.fromBuffer(value),
        ($43.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$43.Empty> uploadAssetManifest_Pre(
      $grpc.ServiceCall call, $async.Future<$42.AssetManifest> request) async {
    return uploadAssetManifest(call, await request);
  }

  $async.Future<$43.Empty> uploadAssetManifest(
      $grpc.ServiceCall call, $42.AssetManifest request);
}
