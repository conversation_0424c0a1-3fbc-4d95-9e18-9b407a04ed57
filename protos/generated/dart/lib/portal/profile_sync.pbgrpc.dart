///
//  Generated code. Do not modify.
//  source: portal/profile_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'profile_sync.pb.dart' as $49;
import '../util/util.pb.dart' as $1;
export 'profile_sync.pb.dart';

class PortalProfileSyncServiceClient extends $grpc.Client {
  static final _$getProfilesData = $grpc.ClientMethod<
          $49.GetProfilesDataRequest, $49.GetProfilesDataResponse>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
      ($49.GetProfilesDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $49.GetProfilesDataResponse.fromBuffer(value));
  static final _$uploadProfile =
      $grpc.ClientMethod<$49.UploadProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
          ($49.UploadProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getProfile =
      $grpc.ClientMethod<$49.GetProfileRequest, $49.GetProfileResponse>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
          ($49.GetProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $49.GetProfileResponse.fromBuffer(value));
  static final _$deleteProfile =
      $grpc.ClientMethod<$49.DeleteProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
          ($49.DeleteProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$purgeProfile =
      $grpc.ClientMethod<$49.PurgeProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
          ($49.PurgeProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getSetActiveProfileCommands = $grpc.ClientMethod<
          $49.GetSetActiveProfileCommandsRequest,
          $49.GetSetActiveProfileCommandsResponse>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
      ($49.GetSetActiveProfileCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $49.GetSetActiveProfileCommandsResponse.fromBuffer(value));
  static final _$purgeSetActiveProfileCommands = $grpc.ClientMethod<
          $49.PurgeSetActiveProfileCommandsRequest, $1.Empty>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
      ($49.PurgeSetActiveProfileCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  PortalProfileSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$49.GetProfilesDataResponse> getProfilesData(
      $49.GetProfilesDataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfilesData, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadProfile($49.UploadProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadProfile, request, options: options);
  }

  $grpc.ResponseFuture<$49.GetProfileResponse> getProfile(
      $49.GetProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteProfile($49.DeleteProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeProfile($49.PurgeProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeProfile, request, options: options);
  }

  $grpc.ResponseFuture<$49.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands(
          $49.GetSetActiveProfileCommandsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSetActiveProfileCommands, request,
        options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeSetActiveProfileCommands(
      $49.PurgeSetActiveProfileCommandsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeSetActiveProfileCommands, request,
        options: options);
  }
}

abstract class PortalProfileSyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.portal.profile_sync.PortalProfileSyncService';

  PortalProfileSyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$49.GetProfilesDataRequest,
            $49.GetProfilesDataResponse>(
        'GetProfilesData',
        getProfilesData_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $49.GetProfilesDataRequest.fromBuffer(value),
        ($49.GetProfilesDataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$49.UploadProfileRequest, $1.Empty>(
        'UploadProfile',
        uploadProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $49.UploadProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$49.GetProfileRequest, $49.GetProfileResponse>(
            'GetProfile',
            getProfile_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $49.GetProfileRequest.fromBuffer(value),
            ($49.GetProfileResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$49.DeleteProfileRequest, $1.Empty>(
        'DeleteProfile',
        deleteProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $49.DeleteProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$49.PurgeProfileRequest, $1.Empty>(
        'PurgeProfile',
        purgeProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $49.PurgeProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$49.GetSetActiveProfileCommandsRequest,
            $49.GetSetActiveProfileCommandsResponse>(
        'GetSetActiveProfileCommands',
        getSetActiveProfileCommands_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $49.GetSetActiveProfileCommandsRequest.fromBuffer(value),
        ($49.GetSetActiveProfileCommandsResponse value) =>
            value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$49.PurgeSetActiveProfileCommandsRequest, $1.Empty>(
            'PurgeSetActiveProfileCommands',
            purgeSetActiveProfileCommands_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $49.PurgeSetActiveProfileCommandsRequest.fromBuffer(value),
            ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$49.GetProfilesDataResponse> getProfilesData_Pre(
      $grpc.ServiceCall call,
      $async.Future<$49.GetProfilesDataRequest> request) async {
    return getProfilesData(call, await request);
  }

  $async.Future<$1.Empty> uploadProfile_Pre($grpc.ServiceCall call,
      $async.Future<$49.UploadProfileRequest> request) async {
    return uploadProfile(call, await request);
  }

  $async.Future<$49.GetProfileResponse> getProfile_Pre($grpc.ServiceCall call,
      $async.Future<$49.GetProfileRequest> request) async {
    return getProfile(call, await request);
  }

  $async.Future<$1.Empty> deleteProfile_Pre($grpc.ServiceCall call,
      $async.Future<$49.DeleteProfileRequest> request) async {
    return deleteProfile(call, await request);
  }

  $async.Future<$1.Empty> purgeProfile_Pre($grpc.ServiceCall call,
      $async.Future<$49.PurgeProfileRequest> request) async {
    return purgeProfile(call, await request);
  }

  $async.Future<$49.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands_Pre($grpc.ServiceCall call,
          $async.Future<$49.GetSetActiveProfileCommandsRequest> request) async {
    return getSetActiveProfileCommands(call, await request);
  }

  $async.Future<$1.Empty> purgeSetActiveProfileCommands_Pre(
      $grpc.ServiceCall call,
      $async.Future<$49.PurgeSetActiveProfileCommandsRequest> request) async {
    return purgeSetActiveProfileCommands(call, await request);
  }

  $async.Future<$49.GetProfilesDataResponse> getProfilesData(
      $grpc.ServiceCall call, $49.GetProfilesDataRequest request);
  $async.Future<$1.Empty> uploadProfile(
      $grpc.ServiceCall call, $49.UploadProfileRequest request);
  $async.Future<$49.GetProfileResponse> getProfile(
      $grpc.ServiceCall call, $49.GetProfileRequest request);
  $async.Future<$1.Empty> deleteProfile(
      $grpc.ServiceCall call, $49.DeleteProfileRequest request);
  $async.Future<$1.Empty> purgeProfile(
      $grpc.ServiceCall call, $49.PurgeProfileRequest request);
  $async.Future<$49.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands($grpc.ServiceCall call,
          $49.GetSetActiveProfileCommandsRequest request);
  $async.Future<$1.Empty> purgeSetActiveProfileCommands(
      $grpc.ServiceCall call, $49.PurgeSetActiveProfileCommandsRequest request);
}
