///
//  Generated code. Do not modify.
//  source: portal/customers.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;

class FeatureFlags extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FeatureFlags', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.customers'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reports')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobs')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'unvalidatedMetrics')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'spatial')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'velocityEstimator')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'explore')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollection')
    ..aOB(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metricsRedesign')
    ..hasRequiredFields = false
  ;

  FeatureFlags._() : super();
  factory FeatureFlags({
    $core.bool? reports,
    $core.bool? almanac,
    $core.bool? jobs,
    $core.bool? unvalidatedMetrics,
    $core.bool? spatial,
    $core.bool? velocityEstimator,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? explore,
    $core.bool? categoryCollection,
    $core.bool? metricsRedesign,
  }) {
    final _result = create();
    if (reports != null) {
      _result.reports = reports;
    }
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (jobs != null) {
      _result.jobs = jobs;
    }
    if (unvalidatedMetrics != null) {
      _result.unvalidatedMetrics = unvalidatedMetrics;
    }
    if (spatial != null) {
      _result.spatial = spatial;
    }
    if (velocityEstimator != null) {
      _result.velocityEstimator = velocityEstimator;
    }
    if (explore != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.explore = explore;
    }
    if (categoryCollection != null) {
      _result.categoryCollection = categoryCollection;
    }
    if (metricsRedesign != null) {
      _result.metricsRedesign = metricsRedesign;
    }
    return _result;
  }
  factory FeatureFlags.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FeatureFlags.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FeatureFlags clone() => FeatureFlags()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FeatureFlags copyWith(void Function(FeatureFlags) updates) => super.copyWith((message) => updates(message as FeatureFlags)) as FeatureFlags; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FeatureFlags create() => FeatureFlags._();
  FeatureFlags createEmptyInstance() => create();
  static $pb.PbList<FeatureFlags> createRepeated() => $pb.PbList<FeatureFlags>();
  @$core.pragma('dart2js:noInline')
  static FeatureFlags getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FeatureFlags>(create);
  static FeatureFlags? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get reports => $_getBF(0);
  @$pb.TagNumber(1)
  set reports($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasReports() => $_has(0);
  @$pb.TagNumber(1)
  void clearReports() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get almanac => $_getBF(1);
  @$pb.TagNumber(2)
  set almanac($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAlmanac() => $_has(1);
  @$pb.TagNumber(2)
  void clearAlmanac() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get jobs => $_getBF(2);
  @$pb.TagNumber(3)
  set jobs($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasJobs() => $_has(2);
  @$pb.TagNumber(3)
  void clearJobs() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get unvalidatedMetrics => $_getBF(3);
  @$pb.TagNumber(4)
  set unvalidatedMetrics($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUnvalidatedMetrics() => $_has(3);
  @$pb.TagNumber(4)
  void clearUnvalidatedMetrics() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get spatial => $_getBF(4);
  @$pb.TagNumber(5)
  set spatial($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSpatial() => $_has(4);
  @$pb.TagNumber(5)
  void clearSpatial() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get velocityEstimator => $_getBF(5);
  @$pb.TagNumber(6)
  set velocityEstimator($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasVelocityEstimator() => $_has(5);
  @$pb.TagNumber(6)
  void clearVelocityEstimator() => clearField(6);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(7)
  $core.bool get explore => $_getBF(6);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(7)
  set explore($core.bool v) { $_setBool(6, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(7)
  $core.bool hasExplore() => $_has(6);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(7)
  void clearExplore() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get categoryCollection => $_getBF(7);
  @$pb.TagNumber(8)
  set categoryCollection($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasCategoryCollection() => $_has(7);
  @$pb.TagNumber(8)
  void clearCategoryCollection() => clearField(8);

  @$pb.TagNumber(9)
  $core.bool get metricsRedesign => $_getBF(8);
  @$pb.TagNumber(9)
  set metricsRedesign($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasMetricsRedesign() => $_has(8);
  @$pb.TagNumber(9)
  void clearMetricsRedesign() => clearField(9);
}

class CustomerResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CustomerResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.customers'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sfdcAccountId')
    ..pPS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'emails')
    ..aOM<FeatureFlags>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'featureFlags', protoName: 'featureFlags', subBuilder: FeatureFlags.create)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeklyReportHour')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeklyReportDay')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeklyReportEnabled')
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeklyReportLookbackDays')
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeklyReportTimezone')
    ..hasRequiredFields = false
  ;

  CustomerResponse._() : super();
  factory CustomerResponse({
    $75.DB? db,
    $core.String? name,
  @$core.Deprecated('This field is deprecated.')
    $core.String? sfdcAccountId,
    $core.Iterable<$core.String>? emails,
    FeatureFlags? featureFlags,
    $fixnum.Int64? weeklyReportHour,
    $fixnum.Int64? weeklyReportDay,
    $core.bool? weeklyReportEnabled,
    $fixnum.Int64? weeklyReportLookbackDays,
    $core.String? weeklyReportTimezone,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (name != null) {
      _result.name = name;
    }
    if (sfdcAccountId != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.sfdcAccountId = sfdcAccountId;
    }
    if (emails != null) {
      _result.emails.addAll(emails);
    }
    if (featureFlags != null) {
      _result.featureFlags = featureFlags;
    }
    if (weeklyReportHour != null) {
      _result.weeklyReportHour = weeklyReportHour;
    }
    if (weeklyReportDay != null) {
      _result.weeklyReportDay = weeklyReportDay;
    }
    if (weeklyReportEnabled != null) {
      _result.weeklyReportEnabled = weeklyReportEnabled;
    }
    if (weeklyReportLookbackDays != null) {
      _result.weeklyReportLookbackDays = weeklyReportLookbackDays;
    }
    if (weeklyReportTimezone != null) {
      _result.weeklyReportTimezone = weeklyReportTimezone;
    }
    return _result;
  }
  factory CustomerResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CustomerResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CustomerResponse clone() => CustomerResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CustomerResponse copyWith(void Function(CustomerResponse) updates) => super.copyWith((message) => updates(message as CustomerResponse)) as CustomerResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CustomerResponse create() => CustomerResponse._();
  CustomerResponse createEmptyInstance() => create();
  static $pb.PbList<CustomerResponse> createRepeated() => $pb.PbList<CustomerResponse>();
  @$core.pragma('dart2js:noInline')
  static CustomerResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CustomerResponse>(create);
  static CustomerResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.String get sfdcAccountId => $_getSZ(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  set sfdcAccountId($core.String v) { $_setString(2, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.bool hasSfdcAccountId() => $_has(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  void clearSfdcAccountId() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.String> get emails => $_getList(3);

  @$pb.TagNumber(5)
  FeatureFlags get featureFlags => $_getN(4);
  @$pb.TagNumber(5)
  set featureFlags(FeatureFlags v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasFeatureFlags() => $_has(4);
  @$pb.TagNumber(5)
  void clearFeatureFlags() => clearField(5);
  @$pb.TagNumber(5)
  FeatureFlags ensureFeatureFlags() => $_ensure(4);

  @$pb.TagNumber(6)
  $fixnum.Int64 get weeklyReportHour => $_getI64(5);
  @$pb.TagNumber(6)
  set weeklyReportHour($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasWeeklyReportHour() => $_has(5);
  @$pb.TagNumber(6)
  void clearWeeklyReportHour() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get weeklyReportDay => $_getI64(6);
  @$pb.TagNumber(7)
  set weeklyReportDay($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasWeeklyReportDay() => $_has(6);
  @$pb.TagNumber(7)
  void clearWeeklyReportDay() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get weeklyReportEnabled => $_getBF(7);
  @$pb.TagNumber(8)
  set weeklyReportEnabled($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasWeeklyReportEnabled() => $_has(7);
  @$pb.TagNumber(8)
  void clearWeeklyReportEnabled() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get weeklyReportLookbackDays => $_getI64(8);
  @$pb.TagNumber(9)
  set weeklyReportLookbackDays($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWeeklyReportLookbackDays() => $_has(8);
  @$pb.TagNumber(9)
  void clearWeeklyReportLookbackDays() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get weeklyReportTimezone => $_getSZ(9);
  @$pb.TagNumber(10)
  set weeklyReportTimezone($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasWeeklyReportTimezone() => $_has(9);
  @$pb.TagNumber(10)
  void clearWeeklyReportTimezone() => clearField(10);
}

class Customer extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Customer', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.customers'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  Customer._() : super();
  factory Customer({
    $core.String? uuid,
    $core.String? name,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory Customer.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Customer.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Customer clone() => Customer()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Customer copyWith(void Function(Customer) updates) => super.copyWith((message) => updates(message as Customer)) as Customer; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Customer create() => Customer._();
  Customer createEmptyInstance() => create();
  static $pb.PbList<Customer> createRepeated() => $pb.PbList<Customer>();
  @$core.pragma('dart2js:noInline')
  static Customer getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Customer>(create);
  static Customer? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);
}

class ListCustomersRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListCustomersRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.customers'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..hasRequiredFields = false
  ;

  ListCustomersRequest._() : super();
  factory ListCustomersRequest({
    $core.String? pageToken,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    return _result;
  }
  factory ListCustomersRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListCustomersRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListCustomersRequest clone() => ListCustomersRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListCustomersRequest copyWith(void Function(ListCustomersRequest) updates) => super.copyWith((message) => updates(message as ListCustomersRequest)) as ListCustomersRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListCustomersRequest create() => ListCustomersRequest._();
  ListCustomersRequest createEmptyInstance() => create();
  static $pb.PbList<ListCustomersRequest> createRepeated() => $pb.PbList<ListCustomersRequest>();
  @$core.pragma('dart2js:noInline')
  static ListCustomersRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListCustomersRequest>(create);
  static ListCustomersRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);
}

class ListCustomersResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListCustomersResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.customers'), createEmptyInstance: create)
    ..pc<Customer>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customers', $pb.PbFieldType.PM, subBuilder: Customer.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..hasRequiredFields = false
  ;

  ListCustomersResponse._() : super();
  factory ListCustomersResponse({
    $core.Iterable<Customer>? customers,
    $core.String? nextPageToken,
  }) {
    final _result = create();
    if (customers != null) {
      _result.customers.addAll(customers);
    }
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    return _result;
  }
  factory ListCustomersResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListCustomersResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListCustomersResponse clone() => ListCustomersResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListCustomersResponse copyWith(void Function(ListCustomersResponse) updates) => super.copyWith((message) => updates(message as ListCustomersResponse)) as ListCustomersResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListCustomersResponse create() => ListCustomersResponse._();
  ListCustomersResponse createEmptyInstance() => create();
  static $pb.PbList<ListCustomersResponse> createRepeated() => $pb.PbList<ListCustomersResponse>();
  @$core.pragma('dart2js:noInline')
  static ListCustomersResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListCustomersResponse>(create);
  static ListCustomersResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Customer> get customers => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get nextPageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set nextPageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasNextPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearNextPageToken() => clearField(2);
}

