///
//  Generated code. Do not modify.
//  source: portal/customers.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use featureFlagsDescriptor instead')
const FeatureFlags$json = const {
  '1': 'FeatureFlags',
  '2': const [
    const {'1': 'reports', '3': 1, '4': 1, '5': 8, '10': 'reports'},
    const {'1': 'almanac', '3': 2, '4': 1, '5': 8, '10': 'almanac'},
    const {'1': 'jobs', '3': 3, '4': 1, '5': 8, '10': 'jobs'},
    const {'1': 'unvalidated_metrics', '3': 4, '4': 1, '5': 8, '10': 'unvalidatedMetrics'},
    const {'1': 'spatial', '3': 5, '4': 1, '5': 8, '10': 'spatial'},
    const {'1': 'velocity_estimator', '3': 6, '4': 1, '5': 8, '10': 'velocityEstimator'},
    const {
      '1': 'explore',
      '3': 7,
      '4': 1,
      '5': 8,
      '8': const {'3': true},
      '10': 'explore',
    },
    const {'1': 'category_collection', '3': 8, '4': 1, '5': 8, '10': 'categoryCollection'},
    const {'1': 'metrics_redesign', '3': 9, '4': 1, '5': 8, '10': 'metricsRedesign'},
  ],
};

/// Descriptor for `FeatureFlags`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List featureFlagsDescriptor = $convert.base64Decode('CgxGZWF0dXJlRmxhZ3MSGAoHcmVwb3J0cxgBIAEoCFIHcmVwb3J0cxIYCgdhbG1hbmFjGAIgASgIUgdhbG1hbmFjEhIKBGpvYnMYAyABKAhSBGpvYnMSLwoTdW52YWxpZGF0ZWRfbWV0cmljcxgEIAEoCFISdW52YWxpZGF0ZWRNZXRyaWNzEhgKB3NwYXRpYWwYBSABKAhSB3NwYXRpYWwSLQoSdmVsb2NpdHlfZXN0aW1hdG9yGAYgASgIUhF2ZWxvY2l0eUVzdGltYXRvchIcCgdleHBsb3JlGAcgASgIQgIYAVIHZXhwbG9yZRIvChNjYXRlZ29yeV9jb2xsZWN0aW9uGAggASgIUhJjYXRlZ29yeUNvbGxlY3Rpb24SKQoQbWV0cmljc19yZWRlc2lnbhgJIAEoCFIPbWV0cmljc1JlZGVzaWdu');
@$core.Deprecated('Use customerResponseDescriptor instead')
const CustomerResponse$json = const {
  '1': 'CustomerResponse',
  '2': const [
    const {'1': 'db', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.db.DB', '10': 'db'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {
      '1': 'sfdc_account_id',
      '3': 3,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'sfdcAccountId',
    },
    const {'1': 'emails', '3': 4, '4': 3, '5': 9, '10': 'emails'},
    const {'1': 'featureFlags', '3': 5, '4': 1, '5': 11, '6': '.carbon.portal.customers.FeatureFlags', '10': 'featureFlags'},
    const {'1': 'weekly_report_hour', '3': 6, '4': 1, '5': 3, '10': 'weeklyReportHour'},
    const {'1': 'weekly_report_day', '3': 7, '4': 1, '5': 3, '10': 'weeklyReportDay'},
    const {'1': 'weekly_report_enabled', '3': 8, '4': 1, '5': 8, '10': 'weeklyReportEnabled'},
    const {'1': 'weekly_report_lookback_days', '3': 9, '4': 1, '5': 3, '10': 'weeklyReportLookbackDays'},
    const {'1': 'weekly_report_timezone', '3': 10, '4': 1, '5': 9, '10': 'weeklyReportTimezone'},
  ],
};

/// Descriptor for `CustomerResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List customerResponseDescriptor = $convert.base64Decode('ChBDdXN0b21lclJlc3BvbnNlEiQKAmRiGAEgASgLMhQuY2FyYm9uLnBvcnRhbC5kYi5EQlICZGISEgoEbmFtZRgCIAEoCVIEbmFtZRIqCg9zZmRjX2FjY291bnRfaWQYAyABKAlCAhgBUg1zZmRjQWNjb3VudElkEhYKBmVtYWlscxgEIAMoCVIGZW1haWxzEkkKDGZlYXR1cmVGbGFncxgFIAEoCzIlLmNhcmJvbi5wb3J0YWwuY3VzdG9tZXJzLkZlYXR1cmVGbGFnc1IMZmVhdHVyZUZsYWdzEiwKEndlZWtseV9yZXBvcnRfaG91chgGIAEoA1IQd2Vla2x5UmVwb3J0SG91chIqChF3ZWVrbHlfcmVwb3J0X2RheRgHIAEoA1IPd2Vla2x5UmVwb3J0RGF5EjIKFXdlZWtseV9yZXBvcnRfZW5hYmxlZBgIIAEoCFITd2Vla2x5UmVwb3J0RW5hYmxlZBI9Cht3ZWVrbHlfcmVwb3J0X2xvb2tiYWNrX2RheXMYCSABKANSGHdlZWtseVJlcG9ydExvb2tiYWNrRGF5cxI0ChZ3ZWVrbHlfcmVwb3J0X3RpbWV6b25lGAogASgJUhR3ZWVrbHlSZXBvcnRUaW1lem9uZQ==');
@$core.Deprecated('Use customerDescriptor instead')
const Customer$json = const {
  '1': 'Customer',
  '2': const [
    const {'1': 'uuid', '3': 1, '4': 1, '5': 9, '10': 'uuid'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
  ],
};

/// Descriptor for `Customer`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List customerDescriptor = $convert.base64Decode('CghDdXN0b21lchISCgR1dWlkGAEgASgJUgR1dWlkEhIKBG5hbWUYAiABKAlSBG5hbWU=');
@$core.Deprecated('Use listCustomersRequestDescriptor instead')
const ListCustomersRequest$json = const {
  '1': 'ListCustomersRequest',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
  ],
};

/// Descriptor for `ListCustomersRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listCustomersRequestDescriptor = $convert.base64Decode('ChRMaXN0Q3VzdG9tZXJzUmVxdWVzdBIdCgpwYWdlX3Rva2VuGAEgASgJUglwYWdlVG9rZW4=');
@$core.Deprecated('Use listCustomersResponseDescriptor instead')
const ListCustomersResponse$json = const {
  '1': 'ListCustomersResponse',
  '2': const [
    const {'1': 'customers', '3': 1, '4': 3, '5': 11, '6': '.carbon.portal.customers.Customer', '10': 'customers'},
    const {'1': 'next_page_token', '3': 2, '4': 1, '5': 9, '10': 'nextPageToken'},
  ],
};

/// Descriptor for `ListCustomersResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listCustomersResponseDescriptor = $convert.base64Decode('ChVMaXN0Q3VzdG9tZXJzUmVzcG9uc2USPwoJY3VzdG9tZXJzGAEgAygLMiEuY2FyYm9uLnBvcnRhbC5jdXN0b21lcnMuQ3VzdG9tZXJSCWN1c3RvbWVycxImCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAlSDW5leHRQYWdlVG9rZW4=');
