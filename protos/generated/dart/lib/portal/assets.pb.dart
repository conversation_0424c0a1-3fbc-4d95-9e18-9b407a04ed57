///
//  Generated code. Do not modify.
//  source: portal/assets.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/timestamp.pb.dart' as $71;

import 'assets.pbenum.dart';

export 'assets.pbenum.dart';

class ReaperModuleAbAssetLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ReaperModuleAbAssetLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingModuleSerial')
    ..e<ReaperModuleAbLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'abLocation', $pb.PbFieldType.OE, defaultOrMaker: ReaperModuleAbLocation.LOCATION_UNSPECIFIED, valueOf: ReaperModuleAbLocation.valueOf, enumValues: ReaperModuleAbLocation.values)
    ..hasRequiredFields = false
  ;

  ReaperModuleAbAssetLocation._() : super();
  factory ReaperModuleAbAssetLocation({
    $core.String? weedingModuleSerial,
    ReaperModuleAbLocation? abLocation,
  }) {
    final _result = create();
    if (weedingModuleSerial != null) {
      _result.weedingModuleSerial = weedingModuleSerial;
    }
    if (abLocation != null) {
      _result.abLocation = abLocation;
    }
    return _result;
  }
  factory ReaperModuleAbAssetLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReaperModuleAbAssetLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReaperModuleAbAssetLocation clone() => ReaperModuleAbAssetLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReaperModuleAbAssetLocation copyWith(void Function(ReaperModuleAbAssetLocation) updates) => super.copyWith((message) => updates(message as ReaperModuleAbAssetLocation)) as ReaperModuleAbAssetLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ReaperModuleAbAssetLocation create() => ReaperModuleAbAssetLocation._();
  ReaperModuleAbAssetLocation createEmptyInstance() => create();
  static $pb.PbList<ReaperModuleAbAssetLocation> createRepeated() => $pb.PbList<ReaperModuleAbAssetLocation>();
  @$core.pragma('dart2js:noInline')
  static ReaperModuleAbAssetLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReaperModuleAbAssetLocation>(create);
  static ReaperModuleAbAssetLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get weedingModuleSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set weedingModuleSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedingModuleSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedingModuleSerial() => clearField(1);

  @$pb.TagNumber(2)
  ReaperModuleAbLocation get abLocation => $_getN(1);
  @$pb.TagNumber(2)
  set abLocation(ReaperModuleAbLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAbLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearAbLocation() => clearField(2);
}

class ReaperModuleSingleAssetLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ReaperModuleSingleAssetLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingModuleSerial')
    ..hasRequiredFields = false
  ;

  ReaperModuleSingleAssetLocation._() : super();
  factory ReaperModuleSingleAssetLocation({
    $core.String? weedingModuleSerial,
  }) {
    final _result = create();
    if (weedingModuleSerial != null) {
      _result.weedingModuleSerial = weedingModuleSerial;
    }
    return _result;
  }
  factory ReaperModuleSingleAssetLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReaperModuleSingleAssetLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReaperModuleSingleAssetLocation clone() => ReaperModuleSingleAssetLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReaperModuleSingleAssetLocation copyWith(void Function(ReaperModuleSingleAssetLocation) updates) => super.copyWith((message) => updates(message as ReaperModuleSingleAssetLocation)) as ReaperModuleSingleAssetLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ReaperModuleSingleAssetLocation create() => ReaperModuleSingleAssetLocation._();
  ReaperModuleSingleAssetLocation createEmptyInstance() => create();
  static $pb.PbList<ReaperModuleSingleAssetLocation> createRepeated() => $pb.PbList<ReaperModuleSingleAssetLocation>();
  @$core.pragma('dart2js:noInline')
  static ReaperModuleSingleAssetLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReaperModuleSingleAssetLocation>(create);
  static ReaperModuleSingleAssetLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get weedingModuleSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set weedingModuleSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedingModuleSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedingModuleSerial() => clearField(1);
}

class SlayerRowAssetLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SlayerRowAssetLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'row', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  SlayerRowAssetLocation._() : super();
  factory SlayerRowAssetLocation({
    $core.String? robotSerial,
    $core.int? row,
    $core.int? id,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (row != null) {
      _result.row = row;
    }
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SlayerRowAssetLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SlayerRowAssetLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SlayerRowAssetLocation clone() => SlayerRowAssetLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SlayerRowAssetLocation copyWith(void Function(SlayerRowAssetLocation) updates) => super.copyWith((message) => updates(message as SlayerRowAssetLocation)) as SlayerRowAssetLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SlayerRowAssetLocation create() => SlayerRowAssetLocation._();
  SlayerRowAssetLocation createEmptyInstance() => create();
  static $pb.PbList<SlayerRowAssetLocation> createRepeated() => $pb.PbList<SlayerRowAssetLocation>();
  @$core.pragma('dart2js:noInline')
  static SlayerRowAssetLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SlayerRowAssetLocation>(create);
  static SlayerRowAssetLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get row => $_getIZ(1);
  @$pb.TagNumber(2)
  set row($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRow() => $_has(1);
  @$pb.TagNumber(2)
  void clearRow() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get id => $_getIZ(2);
  @$pb.TagNumber(3)
  set id($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasId() => $_has(2);
  @$pb.TagNumber(3)
  void clearId() => clearField(3);
}

class Scanner extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Scanner', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<ScannerLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: ScannerLocation.create)
    ..hasRequiredFields = false
  ;

  Scanner._() : super();
  factory Scanner({
    $core.String? serial,
    ScannerLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory Scanner.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Scanner.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Scanner clone() => Scanner()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Scanner copyWith(void Function(Scanner) updates) => super.copyWith((message) => updates(message as Scanner)) as Scanner; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Scanner create() => Scanner._();
  Scanner createEmptyInstance() => create();
  static $pb.PbList<Scanner> createRepeated() => $pb.PbList<Scanner>();
  @$core.pragma('dart2js:noInline')
  static Scanner getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Scanner>(create);
  static Scanner? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  ScannerLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(ScannerLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  ScannerLocation ensureLocation() => $_ensure(1);
}

enum ScannerLocation_Generation {
  slayer, 
  reaper, 
  notSet
}

class ScannerLocation extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, ScannerLocation_Generation> _ScannerLocation_GenerationByTag = {
    1 : ScannerLocation_Generation.slayer,
    2 : ScannerLocation_Generation.reaper,
    0 : ScannerLocation_Generation.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ScannerLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<SlayerRowAssetLocation>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slayer', subBuilder: SlayerRowAssetLocation.create)
    ..aOM<ReaperModuleAbAssetLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reaper', subBuilder: ReaperModuleAbAssetLocation.create)
    ..hasRequiredFields = false
  ;

  ScannerLocation._() : super();
  factory ScannerLocation({
    SlayerRowAssetLocation? slayer,
    ReaperModuleAbAssetLocation? reaper,
  }) {
    final _result = create();
    if (slayer != null) {
      _result.slayer = slayer;
    }
    if (reaper != null) {
      _result.reaper = reaper;
    }
    return _result;
  }
  factory ScannerLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ScannerLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ScannerLocation clone() => ScannerLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ScannerLocation copyWith(void Function(ScannerLocation) updates) => super.copyWith((message) => updates(message as ScannerLocation)) as ScannerLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ScannerLocation create() => ScannerLocation._();
  ScannerLocation createEmptyInstance() => create();
  static $pb.PbList<ScannerLocation> createRepeated() => $pb.PbList<ScannerLocation>();
  @$core.pragma('dart2js:noInline')
  static ScannerLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ScannerLocation>(create);
  static ScannerLocation? _defaultInstance;

  ScannerLocation_Generation whichGeneration() => _ScannerLocation_GenerationByTag[$_whichOneof(0)]!;
  void clearGeneration() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SlayerRowAssetLocation get slayer => $_getN(0);
  @$pb.TagNumber(1)
  set slayer(SlayerRowAssetLocation v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSlayer() => $_has(0);
  @$pb.TagNumber(1)
  void clearSlayer() => clearField(1);
  @$pb.TagNumber(1)
  SlayerRowAssetLocation ensureSlayer() => $_ensure(0);

  @$pb.TagNumber(2)
  ReaperModuleAbAssetLocation get reaper => $_getN(1);
  @$pb.TagNumber(2)
  set reaper(ReaperModuleAbAssetLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasReaper() => $_has(1);
  @$pb.TagNumber(2)
  void clearReaper() => clearField(2);
  @$pb.TagNumber(2)
  ReaperModuleAbAssetLocation ensureReaper() => $_ensure(1);
}

class Laser extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Laser', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<LaserLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: LaserLocation.create)
    ..hasRequiredFields = false
  ;

  Laser._() : super();
  factory Laser({
    $core.String? serial,
    LaserLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory Laser.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Laser.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Laser clone() => Laser()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Laser copyWith(void Function(Laser) updates) => super.copyWith((message) => updates(message as Laser)) as Laser; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Laser create() => Laser._();
  Laser createEmptyInstance() => create();
  static $pb.PbList<Laser> createRepeated() => $pb.PbList<Laser>();
  @$core.pragma('dart2js:noInline')
  static Laser getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Laser>(create);
  static Laser? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  LaserLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(LaserLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  LaserLocation ensureLocation() => $_ensure(1);
}

enum LaserLocation_Generation {
  slayer, 
  reaper, 
  notSet
}

class LaserLocation extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, LaserLocation_Generation> _LaserLocation_GenerationByTag = {
    1 : LaserLocation_Generation.slayer,
    2 : LaserLocation_Generation.reaper,
    0 : LaserLocation_Generation.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LaserLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<SlayerRowAssetLocation>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slayer', subBuilder: SlayerRowAssetLocation.create)
    ..aOM<ReaperModuleAbAssetLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reaper', subBuilder: ReaperModuleAbAssetLocation.create)
    ..hasRequiredFields = false
  ;

  LaserLocation._() : super();
  factory LaserLocation({
    SlayerRowAssetLocation? slayer,
    ReaperModuleAbAssetLocation? reaper,
  }) {
    final _result = create();
    if (slayer != null) {
      _result.slayer = slayer;
    }
    if (reaper != null) {
      _result.reaper = reaper;
    }
    return _result;
  }
  factory LaserLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LaserLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LaserLocation clone() => LaserLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LaserLocation copyWith(void Function(LaserLocation) updates) => super.copyWith((message) => updates(message as LaserLocation)) as LaserLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LaserLocation create() => LaserLocation._();
  LaserLocation createEmptyInstance() => create();
  static $pb.PbList<LaserLocation> createRepeated() => $pb.PbList<LaserLocation>();
  @$core.pragma('dart2js:noInline')
  static LaserLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LaserLocation>(create);
  static LaserLocation? _defaultInstance;

  LaserLocation_Generation whichGeneration() => _LaserLocation_GenerationByTag[$_whichOneof(0)]!;
  void clearGeneration() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SlayerRowAssetLocation get slayer => $_getN(0);
  @$pb.TagNumber(1)
  set slayer(SlayerRowAssetLocation v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSlayer() => $_has(0);
  @$pb.TagNumber(1)
  void clearSlayer() => clearField(1);
  @$pb.TagNumber(1)
  SlayerRowAssetLocation ensureSlayer() => $_ensure(0);

  @$pb.TagNumber(2)
  ReaperModuleAbAssetLocation get reaper => $_getN(1);
  @$pb.TagNumber(2)
  set reaper(ReaperModuleAbAssetLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasReaper() => $_has(1);
  @$pb.TagNumber(2)
  void clearReaper() => clearField(2);
  @$pb.TagNumber(2)
  ReaperModuleAbAssetLocation ensureReaper() => $_ensure(1);
}

class TargetCam extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TargetCam', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<TargetCamLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: TargetCamLocation.create)
    ..hasRequiredFields = false
  ;

  TargetCam._() : super();
  factory TargetCam({
    $core.String? serial,
    TargetCamLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory TargetCam.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TargetCam.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TargetCam clone() => TargetCam()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TargetCam copyWith(void Function(TargetCam) updates) => super.copyWith((message) => updates(message as TargetCam)) as TargetCam; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TargetCam create() => TargetCam._();
  TargetCam createEmptyInstance() => create();
  static $pb.PbList<TargetCam> createRepeated() => $pb.PbList<TargetCam>();
  @$core.pragma('dart2js:noInline')
  static TargetCam getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TargetCam>(create);
  static TargetCam? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  TargetCamLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(TargetCamLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  TargetCamLocation ensureLocation() => $_ensure(1);
}

class TargetCamLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TargetCamLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scannerSerial')
    ..hasRequiredFields = false
  ;

  TargetCamLocation._() : super();
  factory TargetCamLocation({
    $core.String? scannerSerial,
  }) {
    final _result = create();
    if (scannerSerial != null) {
      _result.scannerSerial = scannerSerial;
    }
    return _result;
  }
  factory TargetCamLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TargetCamLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TargetCamLocation clone() => TargetCamLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TargetCamLocation copyWith(void Function(TargetCamLocation) updates) => super.copyWith((message) => updates(message as TargetCamLocation)) as TargetCamLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TargetCamLocation create() => TargetCamLocation._();
  TargetCamLocation createEmptyInstance() => create();
  static $pb.PbList<TargetCamLocation> createRepeated() => $pb.PbList<TargetCamLocation>();
  @$core.pragma('dart2js:noInline')
  static TargetCamLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TargetCamLocation>(create);
  static TargetCamLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get scannerSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set scannerSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScannerSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearScannerSerial() => clearField(1);
}

class PredictCam extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictCam', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<PredictCamLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: PredictCamLocation.create)
    ..hasRequiredFields = false
  ;

  PredictCam._() : super();
  factory PredictCam({
    $core.String? serial,
    PredictCamLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory PredictCam.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictCam.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictCam clone() => PredictCam()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictCam copyWith(void Function(PredictCam) updates) => super.copyWith((message) => updates(message as PredictCam)) as PredictCam; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictCam create() => PredictCam._();
  PredictCam createEmptyInstance() => create();
  static $pb.PbList<PredictCam> createRepeated() => $pb.PbList<PredictCam>();
  @$core.pragma('dart2js:noInline')
  static PredictCam getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictCam>(create);
  static PredictCam? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  PredictCamLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(PredictCamLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  PredictCamLocation ensureLocation() => $_ensure(1);
}

enum PredictCamLocation_Generation {
  slayer, 
  reaper, 
  notSet
}

class PredictCamLocation extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, PredictCamLocation_Generation> _PredictCamLocation_GenerationByTag = {
    1 : PredictCamLocation_Generation.slayer,
    2 : PredictCamLocation_Generation.reaper,
    0 : PredictCamLocation_Generation.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictCamLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<SlayerRowAssetLocation>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slayer', subBuilder: SlayerRowAssetLocation.create)
    ..aOM<ReaperModuleSingleAssetLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reaper', subBuilder: ReaperModuleSingleAssetLocation.create)
    ..hasRequiredFields = false
  ;

  PredictCamLocation._() : super();
  factory PredictCamLocation({
    SlayerRowAssetLocation? slayer,
    ReaperModuleSingleAssetLocation? reaper,
  }) {
    final _result = create();
    if (slayer != null) {
      _result.slayer = slayer;
    }
    if (reaper != null) {
      _result.reaper = reaper;
    }
    return _result;
  }
  factory PredictCamLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictCamLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictCamLocation clone() => PredictCamLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictCamLocation copyWith(void Function(PredictCamLocation) updates) => super.copyWith((message) => updates(message as PredictCamLocation)) as PredictCamLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictCamLocation create() => PredictCamLocation._();
  PredictCamLocation createEmptyInstance() => create();
  static $pb.PbList<PredictCamLocation> createRepeated() => $pb.PbList<PredictCamLocation>();
  @$core.pragma('dart2js:noInline')
  static PredictCamLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictCamLocation>(create);
  static PredictCamLocation? _defaultInstance;

  PredictCamLocation_Generation whichGeneration() => _PredictCamLocation_GenerationByTag[$_whichOneof(0)]!;
  void clearGeneration() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SlayerRowAssetLocation get slayer => $_getN(0);
  @$pb.TagNumber(1)
  set slayer(SlayerRowAssetLocation v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSlayer() => $_has(0);
  @$pb.TagNumber(1)
  void clearSlayer() => clearField(1);
  @$pb.TagNumber(1)
  SlayerRowAssetLocation ensureSlayer() => $_ensure(0);

  @$pb.TagNumber(2)
  ReaperModuleSingleAssetLocation get reaper => $_getN(1);
  @$pb.TagNumber(2)
  set reaper(ReaperModuleSingleAssetLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasReaper() => $_has(1);
  @$pb.TagNumber(2)
  void clearReaper() => clearField(2);
  @$pb.TagNumber(2)
  ReaperModuleSingleAssetLocation ensureReaper() => $_ensure(1);
}

class CommandComputer extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CommandComputer', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<CommandComputerLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: CommandComputerLocation.create)
    ..hasRequiredFields = false
  ;

  CommandComputer._() : super();
  factory CommandComputer({
    $core.String? serial,
    CommandComputerLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory CommandComputer.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CommandComputer.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CommandComputer clone() => CommandComputer()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CommandComputer copyWith(void Function(CommandComputer) updates) => super.copyWith((message) => updates(message as CommandComputer)) as CommandComputer; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CommandComputer create() => CommandComputer._();
  CommandComputer createEmptyInstance() => create();
  static $pb.PbList<CommandComputer> createRepeated() => $pb.PbList<CommandComputer>();
  @$core.pragma('dart2js:noInline')
  static CommandComputer getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CommandComputer>(create);
  static CommandComputer? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  CommandComputerLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(CommandComputerLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  CommandComputerLocation ensureLocation() => $_ensure(1);
}

class CommandComputerLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CommandComputerLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  CommandComputerLocation._() : super();
  factory CommandComputerLocation({
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory CommandComputerLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CommandComputerLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CommandComputerLocation clone() => CommandComputerLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CommandComputerLocation copyWith(void Function(CommandComputerLocation) updates) => super.copyWith((message) => updates(message as CommandComputerLocation)) as CommandComputerLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CommandComputerLocation create() => CommandComputerLocation._();
  CommandComputerLocation createEmptyInstance() => create();
  static $pb.PbList<CommandComputerLocation> createRepeated() => $pb.PbList<CommandComputerLocation>();
  @$core.pragma('dart2js:noInline')
  static CommandComputerLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CommandComputerLocation>(create);
  static CommandComputerLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);
}

class RowComputer extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowComputer', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<RowComputerLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: RowComputerLocation.create)
    ..hasRequiredFields = false
  ;

  RowComputer._() : super();
  factory RowComputer({
    $core.String? serial,
    RowComputerLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory RowComputer.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowComputer.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowComputer clone() => RowComputer()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowComputer copyWith(void Function(RowComputer) updates) => super.copyWith((message) => updates(message as RowComputer)) as RowComputer; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowComputer create() => RowComputer._();
  RowComputer createEmptyInstance() => create();
  static $pb.PbList<RowComputer> createRepeated() => $pb.PbList<RowComputer>();
  @$core.pragma('dart2js:noInline')
  static RowComputer getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowComputer>(create);
  static RowComputer? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  RowComputerLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(RowComputerLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  RowComputerLocation ensureLocation() => $_ensure(1);
}

enum RowComputerLocation_Generation {
  slayer, 
  reaper, 
  notSet
}

class RowComputerLocation extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, RowComputerLocation_Generation> _RowComputerLocation_GenerationByTag = {
    1 : RowComputerLocation_Generation.slayer,
    2 : RowComputerLocation_Generation.reaper,
    0 : RowComputerLocation_Generation.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowComputerLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<SlayerRowComputerLocation>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slayer', subBuilder: SlayerRowComputerLocation.create)
    ..aOM<ReaperModuleSingleAssetLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reaper', subBuilder: ReaperModuleSingleAssetLocation.create)
    ..hasRequiredFields = false
  ;

  RowComputerLocation._() : super();
  factory RowComputerLocation({
    SlayerRowComputerLocation? slayer,
    ReaperModuleSingleAssetLocation? reaper,
  }) {
    final _result = create();
    if (slayer != null) {
      _result.slayer = slayer;
    }
    if (reaper != null) {
      _result.reaper = reaper;
    }
    return _result;
  }
  factory RowComputerLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowComputerLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowComputerLocation clone() => RowComputerLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowComputerLocation copyWith(void Function(RowComputerLocation) updates) => super.copyWith((message) => updates(message as RowComputerLocation)) as RowComputerLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowComputerLocation create() => RowComputerLocation._();
  RowComputerLocation createEmptyInstance() => create();
  static $pb.PbList<RowComputerLocation> createRepeated() => $pb.PbList<RowComputerLocation>();
  @$core.pragma('dart2js:noInline')
  static RowComputerLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowComputerLocation>(create);
  static RowComputerLocation? _defaultInstance;

  RowComputerLocation_Generation whichGeneration() => _RowComputerLocation_GenerationByTag[$_whichOneof(0)]!;
  void clearGeneration() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SlayerRowComputerLocation get slayer => $_getN(0);
  @$pb.TagNumber(1)
  set slayer(SlayerRowComputerLocation v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSlayer() => $_has(0);
  @$pb.TagNumber(1)
  void clearSlayer() => clearField(1);
  @$pb.TagNumber(1)
  SlayerRowComputerLocation ensureSlayer() => $_ensure(0);

  @$pb.TagNumber(2)
  ReaperModuleSingleAssetLocation get reaper => $_getN(1);
  @$pb.TagNumber(2)
  set reaper(ReaperModuleSingleAssetLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasReaper() => $_has(1);
  @$pb.TagNumber(2)
  void clearReaper() => clearField(2);
  @$pb.TagNumber(2)
  ReaperModuleSingleAssetLocation ensureReaper() => $_ensure(1);
}

class SlayerRowComputerLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SlayerRowComputerLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'row', $pb.PbFieldType.O3)
    ..e<SlayerRowComputerType>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: SlayerRowComputerType.COMPUTER_UNKNOWN, valueOf: SlayerRowComputerType.valueOf, enumValues: SlayerRowComputerType.values)
    ..hasRequiredFields = false
  ;

  SlayerRowComputerLocation._() : super();
  factory SlayerRowComputerLocation({
    $core.String? robotSerial,
    $core.int? row,
    SlayerRowComputerType? type,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (row != null) {
      _result.row = row;
    }
    if (type != null) {
      _result.type = type;
    }
    return _result;
  }
  factory SlayerRowComputerLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SlayerRowComputerLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SlayerRowComputerLocation clone() => SlayerRowComputerLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SlayerRowComputerLocation copyWith(void Function(SlayerRowComputerLocation) updates) => super.copyWith((message) => updates(message as SlayerRowComputerLocation)) as SlayerRowComputerLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SlayerRowComputerLocation create() => SlayerRowComputerLocation._();
  SlayerRowComputerLocation createEmptyInstance() => create();
  static $pb.PbList<SlayerRowComputerLocation> createRepeated() => $pb.PbList<SlayerRowComputerLocation>();
  @$core.pragma('dart2js:noInline')
  static SlayerRowComputerLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SlayerRowComputerLocation>(create);
  static SlayerRowComputerLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get row => $_getIZ(1);
  @$pb.TagNumber(2)
  set row($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRow() => $_has(1);
  @$pb.TagNumber(2)
  void clearRow() => clearField(2);

  @$pb.TagNumber(3)
  SlayerRowComputerType get type => $_getN(2);
  @$pb.TagNumber(3)
  set type(SlayerRowComputerType v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasType() => $_has(2);
  @$pb.TagNumber(3)
  void clearType() => clearField(3);
}

class WeedingModule extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedingModule', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<WeedingModuleLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: WeedingModuleLocation.create)
    ..hasRequiredFields = false
  ;

  WeedingModule._() : super();
  factory WeedingModule({
    $core.String? serial,
    WeedingModuleLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory WeedingModule.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedingModule.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedingModule clone() => WeedingModule()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedingModule copyWith(void Function(WeedingModule) updates) => super.copyWith((message) => updates(message as WeedingModule)) as WeedingModule; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedingModule create() => WeedingModule._();
  WeedingModule createEmptyInstance() => create();
  static $pb.PbList<WeedingModule> createRepeated() => $pb.PbList<WeedingModule>();
  @$core.pragma('dart2js:noInline')
  static WeedingModule getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedingModule>(create);
  static WeedingModule? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  WeedingModuleLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(WeedingModuleLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  WeedingModuleLocation ensureLocation() => $_ensure(1);
}

class WeedingModuleLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedingModuleLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'moduleNumber', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  WeedingModuleLocation._() : super();
  factory WeedingModuleLocation({
    $core.String? robotSerial,
    $core.int? moduleNumber,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (moduleNumber != null) {
      _result.moduleNumber = moduleNumber;
    }
    return _result;
  }
  factory WeedingModuleLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedingModuleLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedingModuleLocation clone() => WeedingModuleLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedingModuleLocation copyWith(void Function(WeedingModuleLocation) updates) => super.copyWith((message) => updates(message as WeedingModuleLocation)) as WeedingModuleLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedingModuleLocation create() => WeedingModuleLocation._();
  WeedingModuleLocation createEmptyInstance() => create();
  static $pb.PbList<WeedingModuleLocation> createRepeated() => $pb.PbList<WeedingModuleLocation>();
  @$core.pragma('dart2js:noInline')
  static WeedingModuleLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedingModuleLocation>(create);
  static WeedingModuleLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get moduleNumber => $_getIZ(1);
  @$pb.TagNumber(2)
  set moduleNumber($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasModuleNumber() => $_has(1);
  @$pb.TagNumber(2)
  void clearModuleNumber() => clearField(2);
}

class Starlink extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Starlink', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<StarlinkLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: StarlinkLocation.create)
    ..hasRequiredFields = false
  ;

  Starlink._() : super();
  factory Starlink({
    $core.String? serial,
    StarlinkLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory Starlink.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Starlink.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Starlink clone() => Starlink()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Starlink copyWith(void Function(Starlink) updates) => super.copyWith((message) => updates(message as Starlink)) as Starlink; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Starlink create() => Starlink._();
  Starlink createEmptyInstance() => create();
  static $pb.PbList<Starlink> createRepeated() => $pb.PbList<Starlink>();
  @$core.pragma('dart2js:noInline')
  static Starlink getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Starlink>(create);
  static Starlink? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  StarlinkLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(StarlinkLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  StarlinkLocation ensureLocation() => $_ensure(1);
}

class StarlinkLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StarlinkLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  StarlinkLocation._() : super();
  factory StarlinkLocation({
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory StarlinkLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StarlinkLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StarlinkLocation clone() => StarlinkLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StarlinkLocation copyWith(void Function(StarlinkLocation) updates) => super.copyWith((message) => updates(message as StarlinkLocation)) as StarlinkLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StarlinkLocation create() => StarlinkLocation._();
  StarlinkLocation createEmptyInstance() => create();
  static $pb.PbList<StarlinkLocation> createRepeated() => $pb.PbList<StarlinkLocation>();
  @$core.pragma('dart2js:noInline')
  static StarlinkLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StarlinkLocation>(create);
  static StarlinkLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);
}

class Modem extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Modem', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<ModemLocation>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: ModemLocation.create)
    ..hasRequiredFields = false
  ;

  Modem._() : super();
  factory Modem({
    $core.String? serial,
    ModemLocation? location,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory Modem.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Modem.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Modem clone() => Modem()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Modem copyWith(void Function(Modem) updates) => super.copyWith((message) => updates(message as Modem)) as Modem; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Modem create() => Modem._();
  Modem createEmptyInstance() => create();
  static $pb.PbList<Modem> createRepeated() => $pb.PbList<Modem>();
  @$core.pragma('dart2js:noInline')
  static Modem getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Modem>(create);
  static Modem? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  ModemLocation get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(ModemLocation v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  ModemLocation ensureLocation() => $_ensure(1);
}

class ModemLocation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ModemLocation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  ModemLocation._() : super();
  factory ModemLocation({
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory ModemLocation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ModemLocation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ModemLocation clone() => ModemLocation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ModemLocation copyWith(void Function(ModemLocation) updates) => super.copyWith((message) => updates(message as ModemLocation)) as ModemLocation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ModemLocation create() => ModemLocation._();
  ModemLocation createEmptyInstance() => create();
  static $pb.PbList<ModemLocation> createRepeated() => $pb.PbList<ModemLocation>();
  @$core.pragma('dart2js:noInline')
  static ModemLocation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ModemLocation>(create);
  static ModemLocation? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);
}

class AssetManifest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AssetManifest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.assets'), createEmptyInstance: create)
    ..aOM<$71.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'createTime', subBuilder: $71.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..pc<WeedingModule>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingModules', $pb.PbFieldType.PM, subBuilder: WeedingModule.create)
    ..aOM<CommandComputer>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'commandComputer', subBuilder: CommandComputer.create)
    ..pc<RowComputer>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowComputers', $pb.PbFieldType.PM, subBuilder: RowComputer.create)
    ..pc<Scanner>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scanners', $pb.PbFieldType.PM, subBuilder: Scanner.create)
    ..pc<Laser>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lasers', $pb.PbFieldType.PM, subBuilder: Laser.create)
    ..pc<TargetCam>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCams', $pb.PbFieldType.PM, subBuilder: TargetCam.create)
    ..pc<PredictCam>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCams', $pb.PbFieldType.PM, subBuilder: PredictCam.create)
    ..aOM<Modem>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modem', subBuilder: Modem.create)
    ..aOM<Starlink>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'starlink', subBuilder: Starlink.create)
    ..hasRequiredFields = false
  ;

  AssetManifest._() : super();
  factory AssetManifest({
    $71.Timestamp? createTime,
    $core.String? robotSerial,
    $core.Iterable<WeedingModule>? weedingModules,
    CommandComputer? commandComputer,
    $core.Iterable<RowComputer>? rowComputers,
    $core.Iterable<Scanner>? scanners,
    $core.Iterable<Laser>? lasers,
    $core.Iterable<TargetCam>? targetCams,
    $core.Iterable<PredictCam>? predictCams,
    Modem? modem,
    Starlink? starlink,
  }) {
    final _result = create();
    if (createTime != null) {
      _result.createTime = createTime;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (weedingModules != null) {
      _result.weedingModules.addAll(weedingModules);
    }
    if (commandComputer != null) {
      _result.commandComputer = commandComputer;
    }
    if (rowComputers != null) {
      _result.rowComputers.addAll(rowComputers);
    }
    if (scanners != null) {
      _result.scanners.addAll(scanners);
    }
    if (lasers != null) {
      _result.lasers.addAll(lasers);
    }
    if (targetCams != null) {
      _result.targetCams.addAll(targetCams);
    }
    if (predictCams != null) {
      _result.predictCams.addAll(predictCams);
    }
    if (modem != null) {
      _result.modem = modem;
    }
    if (starlink != null) {
      _result.starlink = starlink;
    }
    return _result;
  }
  factory AssetManifest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AssetManifest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AssetManifest clone() => AssetManifest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AssetManifest copyWith(void Function(AssetManifest) updates) => super.copyWith((message) => updates(message as AssetManifest)) as AssetManifest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AssetManifest create() => AssetManifest._();
  AssetManifest createEmptyInstance() => create();
  static $pb.PbList<AssetManifest> createRepeated() => $pb.PbList<AssetManifest>();
  @$core.pragma('dart2js:noInline')
  static AssetManifest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AssetManifest>(create);
  static AssetManifest? _defaultInstance;

  @$pb.TagNumber(1)
  $71.Timestamp get createTime => $_getN(0);
  @$pb.TagNumber(1)
  set createTime($71.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCreateTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearCreateTime() => clearField(1);
  @$pb.TagNumber(1)
  $71.Timestamp ensureCreateTime() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get robotSerial => $_getSZ(1);
  @$pb.TagNumber(2)
  set robotSerial($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobotSerial() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobotSerial() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<WeedingModule> get weedingModules => $_getList(2);

  @$pb.TagNumber(4)
  CommandComputer get commandComputer => $_getN(3);
  @$pb.TagNumber(4)
  set commandComputer(CommandComputer v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasCommandComputer() => $_has(3);
  @$pb.TagNumber(4)
  void clearCommandComputer() => clearField(4);
  @$pb.TagNumber(4)
  CommandComputer ensureCommandComputer() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.List<RowComputer> get rowComputers => $_getList(4);

  @$pb.TagNumber(6)
  $core.List<Scanner> get scanners => $_getList(5);

  @$pb.TagNumber(7)
  $core.List<Laser> get lasers => $_getList(6);

  @$pb.TagNumber(8)
  $core.List<TargetCam> get targetCams => $_getList(7);

  @$pb.TagNumber(9)
  $core.List<PredictCam> get predictCams => $_getList(8);

  @$pb.TagNumber(10)
  Modem get modem => $_getN(9);
  @$pb.TagNumber(10)
  set modem(Modem v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasModem() => $_has(9);
  @$pb.TagNumber(10)
  void clearModem() => clearField(10);
  @$pb.TagNumber(10)
  Modem ensureModem() => $_ensure(9);

  @$pb.TagNumber(11)
  Starlink get starlink => $_getN(10);
  @$pb.TagNumber(11)
  set starlink(Starlink v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasStarlink() => $_has(10);
  @$pb.TagNumber(11)
  void clearStarlink() => clearField(11);
  @$pb.TagNumber(11)
  Starlink ensureStarlink() => $_ensure(10);
}

