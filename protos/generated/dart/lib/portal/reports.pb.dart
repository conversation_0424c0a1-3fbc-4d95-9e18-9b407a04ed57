///
//  Generated code. Do not modify.
//  source: portal/reports.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;
import 'customers.pb.dart' as $80;

import 'reports.pbenum.dart';

export 'reports.pbenum.dart';

class ReportInstanceResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ReportInstanceResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.reports'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'managerId')
    ..a<$fixnum.Int64>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reportId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..p<$fixnum.Int64>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotIds', $pb.PbFieldType.KU6)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startDate')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endDate')
    ..pPS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'publishEmails')
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slug')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pPS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'visibleColumns')
    ..aOB(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'orderAsc')
    ..aOS(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'orderBy')
    ..aOB(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showAverage')
    ..aOB(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showTotal')
    ..aOS(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorName')
    ..aOB(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'automated')
    ..e<ReportMode>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mode', $pb.PbFieldType.OE, defaultOrMaker: ReportMode.REPORT_MODE_UNSPECIFIED, valueOf: ReportMode.valueOf, enumValues: ReportMode.values)
    ..hasRequiredFields = false
  ;

  ReportInstanceResponse._() : super();
  factory ReportInstanceResponse({
    $75.DB? db,
    $core.String? authorId,
    $core.String? managerId,
    $fixnum.Int64? reportId,
    $core.Iterable<$fixnum.Int64>? robotIds,
    $fixnum.Int64? startDate,
    $fixnum.Int64? endDate,
    $core.Iterable<$core.String>? publishEmails,
    $core.String? slug,
    $core.String? name,
    $core.Iterable<$core.String>? visibleColumns,
    $core.bool? orderAsc,
    $core.String? orderBy,
    $core.bool? showAverage,
    $core.bool? showTotal,
    $core.String? authorName,
    $core.bool? automated,
    ReportMode? mode,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (authorId != null) {
      _result.authorId = authorId;
    }
    if (managerId != null) {
      _result.managerId = managerId;
    }
    if (reportId != null) {
      _result.reportId = reportId;
    }
    if (robotIds != null) {
      _result.robotIds.addAll(robotIds);
    }
    if (startDate != null) {
      _result.startDate = startDate;
    }
    if (endDate != null) {
      _result.endDate = endDate;
    }
    if (publishEmails != null) {
      _result.publishEmails.addAll(publishEmails);
    }
    if (slug != null) {
      _result.slug = slug;
    }
    if (name != null) {
      _result.name = name;
    }
    if (visibleColumns != null) {
      _result.visibleColumns.addAll(visibleColumns);
    }
    if (orderAsc != null) {
      _result.orderAsc = orderAsc;
    }
    if (orderBy != null) {
      _result.orderBy = orderBy;
    }
    if (showAverage != null) {
      _result.showAverage = showAverage;
    }
    if (showTotal != null) {
      _result.showTotal = showTotal;
    }
    if (authorName != null) {
      _result.authorName = authorName;
    }
    if (automated != null) {
      _result.automated = automated;
    }
    if (mode != null) {
      _result.mode = mode;
    }
    return _result;
  }
  factory ReportInstanceResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReportInstanceResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReportInstanceResponse clone() => ReportInstanceResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReportInstanceResponse copyWith(void Function(ReportInstanceResponse) updates) => super.copyWith((message) => updates(message as ReportInstanceResponse)) as ReportInstanceResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ReportInstanceResponse create() => ReportInstanceResponse._();
  ReportInstanceResponse createEmptyInstance() => create();
  static $pb.PbList<ReportInstanceResponse> createRepeated() => $pb.PbList<ReportInstanceResponse>();
  @$core.pragma('dart2js:noInline')
  static ReportInstanceResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReportInstanceResponse>(create);
  static ReportInstanceResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get authorId => $_getSZ(1);
  @$pb.TagNumber(2)
  set authorId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAuthorId() => $_has(1);
  @$pb.TagNumber(2)
  void clearAuthorId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get managerId => $_getSZ(2);
  @$pb.TagNumber(3)
  set managerId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasManagerId() => $_has(2);
  @$pb.TagNumber(3)
  void clearManagerId() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get reportId => $_getI64(3);
  @$pb.TagNumber(4)
  set reportId($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasReportId() => $_has(3);
  @$pb.TagNumber(4)
  void clearReportId() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$fixnum.Int64> get robotIds => $_getList(4);

  @$pb.TagNumber(6)
  $fixnum.Int64 get startDate => $_getI64(5);
  @$pb.TagNumber(6)
  set startDate($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasStartDate() => $_has(5);
  @$pb.TagNumber(6)
  void clearStartDate() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get endDate => $_getI64(6);
  @$pb.TagNumber(7)
  set endDate($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasEndDate() => $_has(6);
  @$pb.TagNumber(7)
  void clearEndDate() => clearField(7);

  @$pb.TagNumber(8)
  $core.List<$core.String> get publishEmails => $_getList(7);

  @$pb.TagNumber(15)
  $core.String get slug => $_getSZ(8);
  @$pb.TagNumber(15)
  set slug($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(15)
  $core.bool hasSlug() => $_has(8);
  @$pb.TagNumber(15)
  void clearSlug() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get name => $_getSZ(9);
  @$pb.TagNumber(16)
  set name($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(16)
  $core.bool hasName() => $_has(9);
  @$pb.TagNumber(16)
  void clearName() => clearField(16);

  @$pb.TagNumber(17)
  $core.List<$core.String> get visibleColumns => $_getList(10);

  @$pb.TagNumber(18)
  $core.bool get orderAsc => $_getBF(11);
  @$pb.TagNumber(18)
  set orderAsc($core.bool v) { $_setBool(11, v); }
  @$pb.TagNumber(18)
  $core.bool hasOrderAsc() => $_has(11);
  @$pb.TagNumber(18)
  void clearOrderAsc() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get orderBy => $_getSZ(12);
  @$pb.TagNumber(19)
  set orderBy($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(19)
  $core.bool hasOrderBy() => $_has(12);
  @$pb.TagNumber(19)
  void clearOrderBy() => clearField(19);

  @$pb.TagNumber(20)
  $core.bool get showAverage => $_getBF(13);
  @$pb.TagNumber(20)
  set showAverage($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(20)
  $core.bool hasShowAverage() => $_has(13);
  @$pb.TagNumber(20)
  void clearShowAverage() => clearField(20);

  @$pb.TagNumber(21)
  $core.bool get showTotal => $_getBF(14);
  @$pb.TagNumber(21)
  set showTotal($core.bool v) { $_setBool(14, v); }
  @$pb.TagNumber(21)
  $core.bool hasShowTotal() => $_has(14);
  @$pb.TagNumber(21)
  void clearShowTotal() => clearField(21);

  @$pb.TagNumber(22)
  $core.String get authorName => $_getSZ(15);
  @$pb.TagNumber(22)
  set authorName($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(22)
  $core.bool hasAuthorName() => $_has(15);
  @$pb.TagNumber(22)
  void clearAuthorName() => clearField(22);

  @$pb.TagNumber(23)
  $core.bool get automated => $_getBF(16);
  @$pb.TagNumber(23)
  set automated($core.bool v) { $_setBool(16, v); }
  @$pb.TagNumber(23)
  $core.bool hasAutomated() => $_has(16);
  @$pb.TagNumber(23)
  void clearAutomated() => clearField(23);

  @$pb.TagNumber(24)
  ReportMode get mode => $_getN(17);
  @$pb.TagNumber(24)
  set mode(ReportMode v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasMode() => $_has(17);
  @$pb.TagNumber(24)
  void clearMode() => clearField(24);
}

class ReportResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ReportResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.reports'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorId')
    ..pPS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'visibleColumns')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'orderAsc')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'orderBy')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showAverage')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'showTotal')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'slug')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$fixnum.Int64>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..p<$fixnum.Int64>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotIds', $pb.PbFieldType.KU6)
    ..aInt64(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startDate')
    ..aInt64(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endDate')
    ..aOB(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'automateWeekly')
    ..aOS(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorName')
    ..aOM<$80.CustomerResponse>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customer', subBuilder: $80.CustomerResponse.create)
    ..e<ReportMode>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mode', $pb.PbFieldType.OE, defaultOrMaker: ReportMode.REPORT_MODE_UNSPECIFIED, valueOf: ReportMode.valueOf, enumValues: ReportMode.values)
    ..hasRequiredFields = false
  ;

  ReportResponse._() : super();
  factory ReportResponse({
    $75.DB? db,
    $core.String? authorId,
    $core.Iterable<$core.String>? visibleColumns,
    $core.bool? orderAsc,
    $core.String? orderBy,
    $core.bool? showAverage,
    $core.bool? showTotal,
    $core.String? slug,
    $core.String? name,
    $fixnum.Int64? customerId,
    $core.Iterable<$fixnum.Int64>? robotIds,
    $fixnum.Int64? startDate,
    $fixnum.Int64? endDate,
    $core.bool? automateWeekly,
    $core.String? authorName,
    $80.CustomerResponse? customer,
    ReportMode? mode,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (authorId != null) {
      _result.authorId = authorId;
    }
    if (visibleColumns != null) {
      _result.visibleColumns.addAll(visibleColumns);
    }
    if (orderAsc != null) {
      _result.orderAsc = orderAsc;
    }
    if (orderBy != null) {
      _result.orderBy = orderBy;
    }
    if (showAverage != null) {
      _result.showAverage = showAverage;
    }
    if (showTotal != null) {
      _result.showTotal = showTotal;
    }
    if (slug != null) {
      _result.slug = slug;
    }
    if (name != null) {
      _result.name = name;
    }
    if (customerId != null) {
      _result.customerId = customerId;
    }
    if (robotIds != null) {
      _result.robotIds.addAll(robotIds);
    }
    if (startDate != null) {
      _result.startDate = startDate;
    }
    if (endDate != null) {
      _result.endDate = endDate;
    }
    if (automateWeekly != null) {
      _result.automateWeekly = automateWeekly;
    }
    if (authorName != null) {
      _result.authorName = authorName;
    }
    if (customer != null) {
      _result.customer = customer;
    }
    if (mode != null) {
      _result.mode = mode;
    }
    return _result;
  }
  factory ReportResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReportResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReportResponse clone() => ReportResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReportResponse copyWith(void Function(ReportResponse) updates) => super.copyWith((message) => updates(message as ReportResponse)) as ReportResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ReportResponse create() => ReportResponse._();
  ReportResponse createEmptyInstance() => create();
  static $pb.PbList<ReportResponse> createRepeated() => $pb.PbList<ReportResponse>();
  @$core.pragma('dart2js:noInline')
  static ReportResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReportResponse>(create);
  static ReportResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get authorId => $_getSZ(1);
  @$pb.TagNumber(2)
  set authorId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAuthorId() => $_has(1);
  @$pb.TagNumber(2)
  void clearAuthorId() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.String> get visibleColumns => $_getList(2);

  @$pb.TagNumber(4)
  $core.bool get orderAsc => $_getBF(3);
  @$pb.TagNumber(4)
  set orderAsc($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasOrderAsc() => $_has(3);
  @$pb.TagNumber(4)
  void clearOrderAsc() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get orderBy => $_getSZ(4);
  @$pb.TagNumber(5)
  set orderBy($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasOrderBy() => $_has(4);
  @$pb.TagNumber(5)
  void clearOrderBy() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get showAverage => $_getBF(5);
  @$pb.TagNumber(6)
  set showAverage($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasShowAverage() => $_has(5);
  @$pb.TagNumber(6)
  void clearShowAverage() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get showTotal => $_getBF(6);
  @$pb.TagNumber(7)
  set showTotal($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasShowTotal() => $_has(6);
  @$pb.TagNumber(7)
  void clearShowTotal() => clearField(7);

  @$pb.TagNumber(13)
  $core.String get slug => $_getSZ(7);
  @$pb.TagNumber(13)
  set slug($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(13)
  $core.bool hasSlug() => $_has(7);
  @$pb.TagNumber(13)
  void clearSlug() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get name => $_getSZ(8);
  @$pb.TagNumber(14)
  set name($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(14)
  $core.bool hasName() => $_has(8);
  @$pb.TagNumber(14)
  void clearName() => clearField(14);

  @$pb.TagNumber(15)
  $fixnum.Int64 get customerId => $_getI64(9);
  @$pb.TagNumber(15)
  set customerId($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(15)
  $core.bool hasCustomerId() => $_has(9);
  @$pb.TagNumber(15)
  void clearCustomerId() => clearField(15);

  @$pb.TagNumber(16)
  $core.List<$fixnum.Int64> get robotIds => $_getList(10);

  @$pb.TagNumber(17)
  $fixnum.Int64 get startDate => $_getI64(11);
  @$pb.TagNumber(17)
  set startDate($fixnum.Int64 v) { $_setInt64(11, v); }
  @$pb.TagNumber(17)
  $core.bool hasStartDate() => $_has(11);
  @$pb.TagNumber(17)
  void clearStartDate() => clearField(17);

  @$pb.TagNumber(18)
  $fixnum.Int64 get endDate => $_getI64(12);
  @$pb.TagNumber(18)
  set endDate($fixnum.Int64 v) { $_setInt64(12, v); }
  @$pb.TagNumber(18)
  $core.bool hasEndDate() => $_has(12);
  @$pb.TagNumber(18)
  void clearEndDate() => clearField(18);

  @$pb.TagNumber(19)
  $core.bool get automateWeekly => $_getBF(13);
  @$pb.TagNumber(19)
  set automateWeekly($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(19)
  $core.bool hasAutomateWeekly() => $_has(13);
  @$pb.TagNumber(19)
  void clearAutomateWeekly() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get authorName => $_getSZ(14);
  @$pb.TagNumber(20)
  set authorName($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(20)
  $core.bool hasAuthorName() => $_has(14);
  @$pb.TagNumber(20)
  void clearAuthorName() => clearField(20);

  @$pb.TagNumber(21)
  $80.CustomerResponse get customer => $_getN(15);
  @$pb.TagNumber(21)
  set customer($80.CustomerResponse v) { setField(21, v); }
  @$pb.TagNumber(21)
  $core.bool hasCustomer() => $_has(15);
  @$pb.TagNumber(21)
  void clearCustomer() => clearField(21);
  @$pb.TagNumber(21)
  $80.CustomerResponse ensureCustomer() => $_ensure(15);

  @$pb.TagNumber(22)
  ReportMode get mode => $_getN(16);
  @$pb.TagNumber(22)
  set mode(ReportMode v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasMode() => $_has(16);
  @$pb.TagNumber(22)
  void clearMode() => clearField(22);
}

