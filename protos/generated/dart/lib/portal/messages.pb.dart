///
//  Generated code. Do not modify.
//  source: portal/messages.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;

class Message extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Message', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.messages'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'message')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorUserId')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorRobotId')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recipientUserId')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recipientCustomerId')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recipientRobotId')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorUserEmail')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'authorUserName')
    ..hasRequiredFields = false
  ;

  Message._() : super();
  factory Message({
    $75.DB? db,
    $core.String? message,
    $core.String? authorUserId,
    $fixnum.Int64? authorRobotId,
    $core.String? recipientUserId,
    $fixnum.Int64? recipientCustomerId,
    $fixnum.Int64? recipientRobotId,
    $core.String? authorUserEmail,
    $core.String? authorUserName,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (message != null) {
      _result.message = message;
    }
    if (authorUserId != null) {
      _result.authorUserId = authorUserId;
    }
    if (authorRobotId != null) {
      _result.authorRobotId = authorRobotId;
    }
    if (recipientUserId != null) {
      _result.recipientUserId = recipientUserId;
    }
    if (recipientCustomerId != null) {
      _result.recipientCustomerId = recipientCustomerId;
    }
    if (recipientRobotId != null) {
      _result.recipientRobotId = recipientRobotId;
    }
    if (authorUserEmail != null) {
      _result.authorUserEmail = authorUserEmail;
    }
    if (authorUserName != null) {
      _result.authorUserName = authorUserName;
    }
    return _result;
  }
  factory Message.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Message.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Message clone() => Message()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Message copyWith(void Function(Message) updates) => super.copyWith((message) => updates(message as Message)) as Message; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Message create() => Message._();
  Message createEmptyInstance() => create();
  static $pb.PbList<Message> createRepeated() => $pb.PbList<Message>();
  @$core.pragma('dart2js:noInline')
  static Message getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Message>(create);
  static Message? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get message => $_getSZ(1);
  @$pb.TagNumber(2)
  set message($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMessage() => $_has(1);
  @$pb.TagNumber(2)
  void clearMessage() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get authorUserId => $_getSZ(2);
  @$pb.TagNumber(3)
  set authorUserId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAuthorUserId() => $_has(2);
  @$pb.TagNumber(3)
  void clearAuthorUserId() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get authorRobotId => $_getI64(3);
  @$pb.TagNumber(4)
  set authorRobotId($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAuthorRobotId() => $_has(3);
  @$pb.TagNumber(4)
  void clearAuthorRobotId() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get recipientUserId => $_getSZ(4);
  @$pb.TagNumber(5)
  set recipientUserId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRecipientUserId() => $_has(4);
  @$pb.TagNumber(5)
  void clearRecipientUserId() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get recipientCustomerId => $_getI64(5);
  @$pb.TagNumber(6)
  set recipientCustomerId($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRecipientCustomerId() => $_has(5);
  @$pb.TagNumber(6)
  void clearRecipientCustomerId() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get recipientRobotId => $_getI64(6);
  @$pb.TagNumber(7)
  set recipientRobotId($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRecipientRobotId() => $_has(6);
  @$pb.TagNumber(7)
  void clearRecipientRobotId() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get authorUserEmail => $_getSZ(7);
  @$pb.TagNumber(8)
  set authorUserEmail($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasAuthorUserEmail() => $_has(7);
  @$pb.TagNumber(8)
  void clearAuthorUserEmail() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get authorUserName => $_getSZ(8);
  @$pb.TagNumber(9)
  set authorUserName($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAuthorUserName() => $_has(8);
  @$pb.TagNumber(9)
  void clearAuthorUserName() => clearField(9);
}

class MessagesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MessagesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.messages'), createEmptyInstance: create)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'page')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'limit')
    ..pc<Message>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'messages', $pb.PbFieldType.PM, subBuilder: Message.create)
    ..hasRequiredFields = false
  ;

  MessagesResponse._() : super();
  factory MessagesResponse({
    $fixnum.Int64? page,
    $fixnum.Int64? limit,
    $core.Iterable<Message>? messages,
  }) {
    final _result = create();
    if (page != null) {
      _result.page = page;
    }
    if (limit != null) {
      _result.limit = limit;
    }
    if (messages != null) {
      _result.messages.addAll(messages);
    }
    return _result;
  }
  factory MessagesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MessagesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MessagesResponse clone() => MessagesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MessagesResponse copyWith(void Function(MessagesResponse) updates) => super.copyWith((message) => updates(message as MessagesResponse)) as MessagesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MessagesResponse create() => MessagesResponse._();
  MessagesResponse createEmptyInstance() => create();
  static $pb.PbList<MessagesResponse> createRepeated() => $pb.PbList<MessagesResponse>();
  @$core.pragma('dart2js:noInline')
  static MessagesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MessagesResponse>(create);
  static MessagesResponse? _defaultInstance;

  @$pb.TagNumber(2)
  $fixnum.Int64 get page => $_getI64(0);
  @$pb.TagNumber(2)
  set page($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasPage() => $_has(0);
  @$pb.TagNumber(2)
  void clearPage() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get limit => $_getI64(1);
  @$pb.TagNumber(3)
  set limit($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasLimit() => $_has(1);
  @$pb.TagNumber(3)
  void clearLimit() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<Message> get messages => $_getList(2);
}

