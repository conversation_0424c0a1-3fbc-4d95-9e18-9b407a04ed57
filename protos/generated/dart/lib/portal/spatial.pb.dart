///
//  Generated code. Do not modify.
//  source: portal/spatial.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $75;
import '../metrics/metrics.pb.dart' as $78;

class BlockResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BlockResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.spatial'), createEmptyInstance: create)
    ..aOM<$75.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $75.DB.create)
    ..aOM<$78.SpatialMetricBlock>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'block', subBuilder: $78.SpatialMetricBlock.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..hasRequiredFields = false
  ;

  BlockResponse._() : super();
  factory BlockResponse({
    $75.DB? db,
    $78.SpatialMetricBlock? block,
    $core.String? serial,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (block != null) {
      _result.block = block;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    return _result;
  }
  factory BlockResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BlockResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BlockResponse clone() => BlockResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BlockResponse copyWith(void Function(BlockResponse) updates) => super.copyWith((message) => updates(message as BlockResponse)) as BlockResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BlockResponse create() => BlockResponse._();
  BlockResponse createEmptyInstance() => create();
  static $pb.PbList<BlockResponse> createRepeated() => $pb.PbList<BlockResponse>();
  @$core.pragma('dart2js:noInline')
  static BlockResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BlockResponse>(create);
  static BlockResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($75.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $75.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $78.SpatialMetricBlock get block => $_getN(1);
  @$pb.TagNumber(2)
  set block($78.SpatialMetricBlock v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasBlock() => $_has(1);
  @$pb.TagNumber(2)
  void clearBlock() => clearField(2);
  @$pb.TagNumber(2)
  $78.SpatialMetricBlock ensureBlock() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get serial => $_getSZ(2);
  @$pb.TagNumber(3)
  set serial($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSerial() => $_has(2);
  @$pb.TagNumber(3)
  void clearSerial() => clearField(3);
}

class BlocksResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BlocksResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.spatial'), createEmptyInstance: create)
    ..pc<BlockResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocks', $pb.PbFieldType.PM, subBuilder: BlockResponse.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'queryLimitReached')
    ..hasRequiredFields = false
  ;

  BlocksResponse._() : super();
  factory BlocksResponse({
    $core.Iterable<BlockResponse>? blocks,
    $core.bool? queryLimitReached,
  }) {
    final _result = create();
    if (blocks != null) {
      _result.blocks.addAll(blocks);
    }
    if (queryLimitReached != null) {
      _result.queryLimitReached = queryLimitReached;
    }
    return _result;
  }
  factory BlocksResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BlocksResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BlocksResponse clone() => BlocksResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BlocksResponse copyWith(void Function(BlocksResponse) updates) => super.copyWith((message) => updates(message as BlocksResponse)) as BlocksResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BlocksResponse create() => BlocksResponse._();
  BlocksResponse createEmptyInstance() => create();
  static $pb.PbList<BlocksResponse> createRepeated() => $pb.PbList<BlocksResponse>();
  @$core.pragma('dart2js:noInline')
  static BlocksResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BlocksResponse>(create);
  static BlocksResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<BlockResponse> get blocks => $_getList(0);

  @$pb.TagNumber(2)
  $core.bool get queryLimitReached => $_getBF(1);
  @$pb.TagNumber(2)
  set queryLimitReached($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasQueryLimitReached() => $_has(1);
  @$pb.TagNumber(2)
  void clearQueryLimitReached() => clearField(2);
}

class BlocksByDateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BlocksByDateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.spatial'), createEmptyInstance: create)
    ..m<$core.String, BlocksResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocks', entryClassName: 'BlocksByDateResponse.BlocksEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: BlocksResponse.create, packageName: const $pb.PackageName('carbon.portal.spatial'))
    ..hasRequiredFields = false
  ;

  BlocksByDateResponse._() : super();
  factory BlocksByDateResponse({
    $core.Map<$core.String, BlocksResponse>? blocks,
  }) {
    final _result = create();
    if (blocks != null) {
      _result.blocks.addAll(blocks);
    }
    return _result;
  }
  factory BlocksByDateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BlocksByDateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BlocksByDateResponse clone() => BlocksByDateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BlocksByDateResponse copyWith(void Function(BlocksByDateResponse) updates) => super.copyWith((message) => updates(message as BlocksByDateResponse)) as BlocksByDateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BlocksByDateResponse create() => BlocksByDateResponse._();
  BlocksByDateResponse createEmptyInstance() => create();
  static $pb.PbList<BlocksByDateResponse> createRepeated() => $pb.PbList<BlocksByDateResponse>();
  @$core.pragma('dart2js:noInline')
  static BlocksByDateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BlocksByDateResponse>(create);
  static BlocksByDateResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, BlocksResponse> get blocks => $_getMap(0);
}

class BlocksByDateByRobotResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BlocksByDateByRobotResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.spatial'), createEmptyInstance: create)
    ..m<$core.String, BlocksByDateResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocks', entryClassName: 'BlocksByDateByRobotResponse.BlocksEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: BlocksByDateResponse.create, packageName: const $pb.PackageName('carbon.portal.spatial'))
    ..hasRequiredFields = false
  ;

  BlocksByDateByRobotResponse._() : super();
  factory BlocksByDateByRobotResponse({
    $core.Map<$core.String, BlocksByDateResponse>? blocks,
  }) {
    final _result = create();
    if (blocks != null) {
      _result.blocks.addAll(blocks);
    }
    return _result;
  }
  factory BlocksByDateByRobotResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BlocksByDateByRobotResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BlocksByDateByRobotResponse clone() => BlocksByDateByRobotResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BlocksByDateByRobotResponse copyWith(void Function(BlocksByDateByRobotResponse) updates) => super.copyWith((message) => updates(message as BlocksByDateByRobotResponse)) as BlocksByDateByRobotResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BlocksByDateByRobotResponse create() => BlocksByDateByRobotResponse._();
  BlocksByDateByRobotResponse createEmptyInstance() => create();
  static $pb.PbList<BlocksByDateByRobotResponse> createRepeated() => $pb.PbList<BlocksByDateByRobotResponse>();
  @$core.pragma('dart2js:noInline')
  static BlocksByDateByRobotResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BlocksByDateByRobotResponse>(create);
  static BlocksByDateByRobotResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, BlocksByDateResponse> get blocks => $_getMap(0);
}

