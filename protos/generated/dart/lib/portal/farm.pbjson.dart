///
//  Generated code. Do not modify.
//  source: portal/farm.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use farmDescriptor instead')
const Farm$json = const {
  '1': 'Farm',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Id', '10': 'id'},
    const {'1': 'version', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.farm.VersionInfo', '10': 'version'},
    const {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'customer_id', '3': 4, '4': 1, '5': 3, '10': 'customerId'},
    const {'1': 'point_defs', '3': 5, '4': 3, '5': 11, '6': '.carbon.portal.farm.PointDefinition', '10': 'pointDefs'},
    const {'1': 'zones', '3': 6, '4': 3, '5': 11, '6': '.carbon.portal.farm.Zone', '10': 'zones'},
  ],
};

/// Descriptor for `Farm`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List farmDescriptor = $convert.base64Decode('CgRGYXJtEh4KAmlkGAEgASgLMg4uY2FyYm9uLmdlby5JZFICaWQSOQoHdmVyc2lvbhgCIAEoCzIfLmNhcmJvbi5wb3J0YWwuZmFybS5WZXJzaW9uSW5mb1IHdmVyc2lvbhISCgRuYW1lGAMgASgJUgRuYW1lEh8KC2N1c3RvbWVyX2lkGAQgASgDUgpjdXN0b21lcklkEkIKCnBvaW50X2RlZnMYBSADKAsyIy5jYXJib24ucG9ydGFsLmZhcm0uUG9pbnREZWZpbml0aW9uUglwb2ludERlZnMSLgoFem9uZXMYBiADKAsyGC5jYXJib24ucG9ydGFsLmZhcm0uWm9uZVIFem9uZXM=');
@$core.Deprecated('Use versionInfoDescriptor instead')
const VersionInfo$json = const {
  '1': 'VersionInfo',
  '2': const [
    const {'1': 'ordinal', '3': 1, '4': 1, '5': 3, '10': 'ordinal'},
    const {'1': 'update_time', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'updateTime'},
    const {'1': 'deleted', '3': 3, '4': 1, '5': 8, '10': 'deleted'},
    const {'1': 'changed', '3': 4, '4': 1, '5': 8, '10': 'changed'},
  ],
};

/// Descriptor for `VersionInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List versionInfoDescriptor = $convert.base64Decode('CgtWZXJzaW9uSW5mbxIYCgdvcmRpbmFsGAEgASgDUgdvcmRpbmFsEjsKC3VwZGF0ZV90aW1lGAIgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcFIKdXBkYXRlVGltZRIYCgdkZWxldGVkGAMgASgIUgdkZWxldGVkEhgKB2NoYW5nZWQYBCABKAhSB2NoYW5nZWQ=');
@$core.Deprecated('Use pointDefinitionDescriptor instead')
const PointDefinition$json = const {
  '1': 'PointDefinition',
  '2': const [
    const {'1': 'point', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'point'},
    const {'1': 'version', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.farm.VersionInfo', '10': 'version'},
  ],
};

/// Descriptor for `PointDefinition`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pointDefinitionDescriptor = $convert.base64Decode('Cg9Qb2ludERlZmluaXRpb24SJwoFcG9pbnQYASABKAsyES5jYXJib24uZ2VvLlBvaW50UgVwb2ludBI5Cgd2ZXJzaW9uGAIgASgLMh8uY2FyYm9uLnBvcnRhbC5mYXJtLlZlcnNpb25JbmZvUgd2ZXJzaW9u');
@$core.Deprecated('Use zoneDescriptor instead')
const Zone$json = const {
  '1': 'Zone',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Id', '10': 'id'},
    const {'1': 'version', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.farm.VersionInfo', '10': 'version'},
    const {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'areas', '3': 4, '4': 3, '5': 11, '6': '.carbon.portal.farm.Area', '10': 'areas'},
    const {'1': 'contents', '3': 5, '4': 1, '5': 11, '6': '.carbon.portal.farm.ZoneContents', '10': 'contents'},
  ],
};

/// Descriptor for `Zone`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zoneDescriptor = $convert.base64Decode('CgRab25lEh4KAmlkGAEgASgLMg4uY2FyYm9uLmdlby5JZFICaWQSOQoHdmVyc2lvbhgCIAEoCzIfLmNhcmJvbi5wb3J0YWwuZmFybS5WZXJzaW9uSW5mb1IHdmVyc2lvbhISCgRuYW1lGAMgASgJUgRuYW1lEi4KBWFyZWFzGAQgAygLMhguY2FyYm9uLnBvcnRhbC5mYXJtLkFyZWFSBWFyZWFzEjwKCGNvbnRlbnRzGAUgASgLMiAuY2FyYm9uLnBvcnRhbC5mYXJtLlpvbmVDb250ZW50c1IIY29udGVudHM=');
@$core.Deprecated('Use zoneContentsDescriptor instead')
const ZoneContents$json = const {
  '1': 'ZoneContents',
  '2': const [
    const {'1': 'farm_boundary', '3': 9, '4': 1, '5': 11, '6': '.carbon.portal.farm.FarmBoundaryData', '9': 0, '10': 'farmBoundary'},
    const {'1': 'field', '3': 5, '4': 1, '5': 11, '6': '.carbon.portal.farm.FieldData', '9': 0, '10': 'field'},
    const {'1': 'headland', '3': 6, '4': 1, '5': 11, '6': '.carbon.portal.farm.HeadlandData', '9': 0, '10': 'headland'},
    const {'1': 'private_road', '3': 7, '4': 1, '5': 11, '6': '.carbon.portal.farm.PrivateRoadData', '9': 0, '10': 'privateRoad'},
    const {'1': 'obstacle', '3': 8, '4': 1, '5': 11, '6': '.carbon.portal.farm.ObstacleData', '9': 0, '10': 'obstacle'},
  ],
  '8': const [
    const {'1': 'data'},
  ],
};

/// Descriptor for `ZoneContents`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zoneContentsDescriptor = $convert.base64Decode('Cgxab25lQ29udGVudHMSSwoNZmFybV9ib3VuZGFyeRgJIAEoCzIkLmNhcmJvbi5wb3J0YWwuZmFybS5GYXJtQm91bmRhcnlEYXRhSABSDGZhcm1Cb3VuZGFyeRI1CgVmaWVsZBgFIAEoCzIdLmNhcmJvbi5wb3J0YWwuZmFybS5GaWVsZERhdGFIAFIFZmllbGQSPgoIaGVhZGxhbmQYBiABKAsyIC5jYXJib24ucG9ydGFsLmZhcm0uSGVhZGxhbmREYXRhSABSCGhlYWRsYW5kEkgKDHByaXZhdGVfcm9hZBgHIAEoCzIjLmNhcmJvbi5wb3J0YWwuZmFybS5Qcml2YXRlUm9hZERhdGFIAFILcHJpdmF0ZVJvYWQSPgoIb2JzdGFjbGUYCCABKAsyIC5jYXJib24ucG9ydGFsLmZhcm0uT2JzdGFjbGVEYXRhSABSCG9ic3RhY2xlQgYKBGRhdGE=');
@$core.Deprecated('Use areaDescriptor instead')
const Area$json = const {
  '1': 'Area',
  '2': const [
    const {'1': 'buffer_meters', '3': 1, '4': 1, '5': 1, '10': 'bufferMeters'},
    const {'1': 'point', '3': 2, '4': 1, '5': 11, '6': '.carbon.geo.Point', '9': 0, '10': 'point'},
    const {'1': 'line_string', '3': 3, '4': 1, '5': 11, '6': '.carbon.geo.LineString', '9': 0, '10': 'lineString'},
    const {'1': 'polygon', '3': 4, '4': 1, '5': 11, '6': '.carbon.geo.Polygon', '9': 0, '10': 'polygon'},
  ],
  '8': const [
    const {'1': 'geometry'},
  ],
};

/// Descriptor for `Area`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List areaDescriptor = $convert.base64Decode('CgRBcmVhEiMKDWJ1ZmZlcl9tZXRlcnMYASABKAFSDGJ1ZmZlck1ldGVycxIpCgVwb2ludBgCIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRIAFIFcG9pbnQSOQoLbGluZV9zdHJpbmcYAyABKAsyFi5jYXJib24uZ2VvLkxpbmVTdHJpbmdIAFIKbGluZVN0cmluZxIvCgdwb2x5Z29uGAQgASgLMhMuY2FyYm9uLmdlby5Qb2x5Z29uSABSB3BvbHlnb25CCgoIZ2VvbWV0cnk=');
@$core.Deprecated('Use farmBoundaryDataDescriptor instead')
const FarmBoundaryData$json = const {
  '1': 'FarmBoundaryData',
};

/// Descriptor for `FarmBoundaryData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List farmBoundaryDataDescriptor = $convert.base64Decode('ChBGYXJtQm91bmRhcnlEYXRh');
@$core.Deprecated('Use fieldDataDescriptor instead')
const FieldData$json = const {
  '1': 'FieldData',
  '2': const [
    const {'1': 'planting_heading', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.farm.PlantingHeading', '10': 'plantingHeading'},
    const {'1': 'center_pivot', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.farm.CenterPivot', '10': 'centerPivot'},
  ],
};

/// Descriptor for `FieldData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fieldDataDescriptor = $convert.base64Decode('CglGaWVsZERhdGESTgoQcGxhbnRpbmdfaGVhZGluZxgBIAEoCzIjLmNhcmJvbi5wb3J0YWwuZmFybS5QbGFudGluZ0hlYWRpbmdSD3BsYW50aW5nSGVhZGluZxJCCgxjZW50ZXJfcGl2b3QYAiABKAsyHy5jYXJib24ucG9ydGFsLmZhcm0uQ2VudGVyUGl2b3RSC2NlbnRlclBpdm90');
@$core.Deprecated('Use headlandDataDescriptor instead')
const HeadlandData$json = const {
  '1': 'HeadlandData',
};

/// Descriptor for `HeadlandData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List headlandDataDescriptor = $convert.base64Decode('CgxIZWFkbGFuZERhdGE=');
@$core.Deprecated('Use privateRoadDataDescriptor instead')
const PrivateRoadData$json = const {
  '1': 'PrivateRoadData',
};

/// Descriptor for `PrivateRoadData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List privateRoadDataDescriptor = $convert.base64Decode('Cg9Qcml2YXRlUm9hZERhdGE=');
@$core.Deprecated('Use obstacleDataDescriptor instead')
const ObstacleData$json = const {
  '1': 'ObstacleData',
  '2': const [
    const {'1': 'passable', '3': 1, '4': 1, '5': 8, '10': 'passable'},
  ],
};

/// Descriptor for `ObstacleData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List obstacleDataDescriptor = $convert.base64Decode('CgxPYnN0YWNsZURhdGESGgoIcGFzc2FibGUYASABKAhSCHBhc3NhYmxl');
@$core.Deprecated('Use plantingHeadingDescriptor instead')
const PlantingHeading$json = const {
  '1': 'PlantingHeading',
  '2': const [
    const {'1': 'azimuth_degrees', '3': 1, '4': 1, '5': 1, '9': 0, '10': 'azimuthDegrees'},
    const {'1': 'ab_line', '3': 2, '4': 1, '5': 11, '6': '.carbon.geo.AbLine', '9': 0, '10': 'abLine'},
  ],
  '8': const [
    const {'1': 'heading'},
  ],
};

/// Descriptor for `PlantingHeading`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List plantingHeadingDescriptor = $convert.base64Decode('Cg9QbGFudGluZ0hlYWRpbmcSKQoPYXppbXV0aF9kZWdyZWVzGAEgASgBSABSDmF6aW11dGhEZWdyZWVzEi0KB2FiX2xpbmUYAiABKAsyEi5jYXJib24uZ2VvLkFiTGluZUgAUgZhYkxpbmVCCQoHaGVhZGluZw==');
@$core.Deprecated('Use centerPivotDescriptor instead')
const CenterPivot$json = const {
  '1': 'CenterPivot',
  '2': const [
    const {'1': 'center', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'center'},
    const {'1': 'width_meters', '3': 2, '4': 1, '5': 1, '10': 'widthMeters'},
    const {'1': 'length_meters', '3': 3, '4': 1, '5': 1, '10': 'lengthMeters'},
    const {'1': 'endpoint_device_id', '3': 4, '4': 1, '5': 9, '10': 'endpointDeviceId'},
  ],
};

/// Descriptor for `CenterPivot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List centerPivotDescriptor = $convert.base64Decode('CgtDZW50ZXJQaXZvdBIpCgZjZW50ZXIYASABKAsyES5jYXJib24uZ2VvLlBvaW50UgZjZW50ZXISIQoMd2lkdGhfbWV0ZXJzGAIgASgBUgt3aWR0aE1ldGVycxIjCg1sZW5ndGhfbWV0ZXJzGAMgASgBUgxsZW5ndGhNZXRlcnMSLAoSZW5kcG9pbnRfZGV2aWNlX2lkGAQgASgJUhBlbmRwb2ludERldmljZUlk');
@$core.Deprecated('Use createFarmRequestDescriptor instead')
const CreateFarmRequest$json = const {
  '1': 'CreateFarmRequest',
  '2': const [
    const {'1': 'farm', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.farm.Farm', '10': 'farm'},
  ],
};

/// Descriptor for `CreateFarmRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createFarmRequestDescriptor = $convert.base64Decode('ChFDcmVhdGVGYXJtUmVxdWVzdBIsCgRmYXJtGAEgASgLMhguY2FyYm9uLnBvcnRhbC5mYXJtLkZhcm1SBGZhcm0=');
@$core.Deprecated('Use updateFarmRequestDescriptor instead')
const UpdateFarmRequest$json = const {
  '1': 'UpdateFarmRequest',
  '2': const [
    const {'1': 'farm', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.farm.Farm', '10': 'farm'},
  ],
};

/// Descriptor for `UpdateFarmRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List updateFarmRequestDescriptor = $convert.base64Decode('ChFVcGRhdGVGYXJtUmVxdWVzdBIsCgRmYXJtGAEgASgLMhguY2FyYm9uLnBvcnRhbC5mYXJtLkZhcm1SBGZhcm0=');
@$core.Deprecated('Use listFarmsRequestDescriptor instead')
const ListFarmsRequest$json = const {
  '1': 'ListFarmsRequest',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
  ],
};

/// Descriptor for `ListFarmsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listFarmsRequestDescriptor = $convert.base64Decode('ChBMaXN0RmFybXNSZXF1ZXN0Eh0KCnBhZ2VfdG9rZW4YASABKAlSCXBhZ2VUb2tlbg==');
@$core.Deprecated('Use listFarmsResponseDescriptor instead')
const ListFarmsResponse$json = const {
  '1': 'ListFarmsResponse',
  '2': const [
    const {'1': 'farms', '3': 1, '4': 3, '5': 11, '6': '.carbon.portal.farm.Farm', '10': 'farms'},
    const {'1': 'next_page_token', '3': 2, '4': 1, '5': 9, '10': 'nextPageToken'},
  ],
};

/// Descriptor for `ListFarmsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listFarmsResponseDescriptor = $convert.base64Decode('ChFMaXN0RmFybXNSZXNwb25zZRIuCgVmYXJtcxgBIAMoCzIYLmNhcmJvbi5wb3J0YWwuZmFybS5GYXJtUgVmYXJtcxImCg9uZXh0X3BhZ2VfdG9rZW4YAiABKAlSDW5leHRQYWdlVG9rZW4=');
@$core.Deprecated('Use getFarmRequestDescriptor instead')
const GetFarmRequest$json = const {
  '1': 'GetFarmRequest',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Id', '10': 'id'},
    const {'1': 'if_modified_since', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'ifModifiedSince'},
  ],
};

/// Descriptor for `GetFarmRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getFarmRequestDescriptor = $convert.base64Decode('Cg5HZXRGYXJtUmVxdWVzdBIeCgJpZBgBIAEoCzIOLmNhcmJvbi5nZW8uSWRSAmlkEkYKEWlmX21vZGlmaWVkX3NpbmNlGAIgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcFIPaWZNb2RpZmllZFNpbmNl');
@$core.Deprecated('Use getFarmResponseDescriptor instead')
const GetFarmResponse$json = const {
  '1': 'GetFarmResponse',
  '2': const [
    const {'1': 'farm', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.farm.Farm', '10': 'farm'},
  ],
};

/// Descriptor for `GetFarmResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getFarmResponseDescriptor = $convert.base64Decode('Cg9HZXRGYXJtUmVzcG9uc2USLAoEZmFybRgBIAEoCzIYLmNhcmJvbi5wb3J0YWwuZmFybS5GYXJtUgRmYXJt');
