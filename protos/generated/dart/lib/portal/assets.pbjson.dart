///
//  Generated code. Do not modify.
//  source: portal/assets.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use reaperModuleAbLocationDescriptor instead')
const ReaperModuleAbLocation$json = const {
  '1': 'ReaperModuleAbLocation',
  '2': const [
    const {'1': 'LOCATION_UNSPECIFIED', '2': 0},
    const {'1': 'LOCATION_A', '2': 1},
    const {'1': 'LOCATION_B', '2': 2},
  ],
};

/// Descriptor for `ReaperModuleAbLocation`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List reaperModuleAbLocationDescriptor = $convert.base64Decode('ChZSZWFwZXJNb2R1bGVBYkxvY2F0aW9uEhgKFExPQ0FUSU9OX1VOU1BFQ0lGSUVEEAASDgoKTE9DQVRJT05fQRABEg4KCkxPQ0FUSU9OX0IQAg==');
@$core.Deprecated('Use slayerRowComputerTypeDescriptor instead')
const SlayerRowComputerType$json = const {
  '1': 'SlayerRowComputerType',
  '2': const [
    const {'1': 'COMPUTER_UNKNOWN', '2': 0},
    const {'1': 'COMPUTER_SINGLE', '2': 1},
    const {'1': 'COMPUTER_PRIMARY', '2': 2},
    const {'1': 'COMPUTER_SECONDARY', '2': 3},
  ],
};

/// Descriptor for `SlayerRowComputerType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List slayerRowComputerTypeDescriptor = $convert.base64Decode('ChVTbGF5ZXJSb3dDb21wdXRlclR5cGUSFAoQQ09NUFVURVJfVU5LTk9XThAAEhMKD0NPTVBVVEVSX1NJTkdMRRABEhQKEENPTVBVVEVSX1BSSU1BUlkQAhIWChJDT01QVVRFUl9TRUNPTkRBUlkQAw==');
@$core.Deprecated('Use reaperModuleAbAssetLocationDescriptor instead')
const ReaperModuleAbAssetLocation$json = const {
  '1': 'ReaperModuleAbAssetLocation',
  '2': const [
    const {'1': 'weeding_module_serial', '3': 1, '4': 1, '5': 9, '10': 'weedingModuleSerial'},
    const {'1': 'ab_location', '3': 2, '4': 1, '5': 14, '6': '.carbon.portal.assets.ReaperModuleAbLocation', '10': 'abLocation'},
  ],
};

/// Descriptor for `ReaperModuleAbAssetLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List reaperModuleAbAssetLocationDescriptor = $convert.base64Decode('ChtSZWFwZXJNb2R1bGVBYkFzc2V0TG9jYXRpb24SMgoVd2VlZGluZ19tb2R1bGVfc2VyaWFsGAEgASgJUhN3ZWVkaW5nTW9kdWxlU2VyaWFsEk0KC2FiX2xvY2F0aW9uGAIgASgOMiwuY2FyYm9uLnBvcnRhbC5hc3NldHMuUmVhcGVyTW9kdWxlQWJMb2NhdGlvblIKYWJMb2NhdGlvbg==');
@$core.Deprecated('Use reaperModuleSingleAssetLocationDescriptor instead')
const ReaperModuleSingleAssetLocation$json = const {
  '1': 'ReaperModuleSingleAssetLocation',
  '2': const [
    const {'1': 'weeding_module_serial', '3': 1, '4': 1, '5': 9, '10': 'weedingModuleSerial'},
  ],
};

/// Descriptor for `ReaperModuleSingleAssetLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List reaperModuleSingleAssetLocationDescriptor = $convert.base64Decode('Ch9SZWFwZXJNb2R1bGVTaW5nbGVBc3NldExvY2F0aW9uEjIKFXdlZWRpbmdfbW9kdWxlX3NlcmlhbBgBIAEoCVITd2VlZGluZ01vZHVsZVNlcmlhbA==');
@$core.Deprecated('Use slayerRowAssetLocationDescriptor instead')
const SlayerRowAssetLocation$json = const {
  '1': 'SlayerRowAssetLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
    const {'1': 'row', '3': 2, '4': 1, '5': 5, '10': 'row'},
    const {'1': 'id', '3': 3, '4': 1, '5': 5, '10': 'id'},
  ],
};

/// Descriptor for `SlayerRowAssetLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List slayerRowAssetLocationDescriptor = $convert.base64Decode('ChZTbGF5ZXJSb3dBc3NldExvY2F0aW9uEiEKDHJvYm90X3NlcmlhbBgBIAEoCVILcm9ib3RTZXJpYWwSEAoDcm93GAIgASgFUgNyb3cSDgoCaWQYAyABKAVSAmlk');
@$core.Deprecated('Use scannerDescriptor instead')
const Scanner$json = const {
  '1': 'Scanner',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ScannerLocation', '10': 'location'},
  ],
};

/// Descriptor for `Scanner`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List scannerDescriptor = $convert.base64Decode('CgdTY2FubmVyEhYKBnNlcmlhbBgBIAEoCVIGc2VyaWFsEkEKCGxvY2F0aW9uGAIgASgLMiUuY2FyYm9uLnBvcnRhbC5hc3NldHMuU2Nhbm5lckxvY2F0aW9uUghsb2NhdGlvbg==');
@$core.Deprecated('Use scannerLocationDescriptor instead')
const ScannerLocation$json = const {
  '1': 'ScannerLocation',
  '2': const [
    const {'1': 'slayer', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.assets.SlayerRowAssetLocation', '9': 0, '10': 'slayer'},
    const {'1': 'reaper', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ReaperModuleAbAssetLocation', '9': 0, '10': 'reaper'},
  ],
  '8': const [
    const {'1': 'generation'},
  ],
};

/// Descriptor for `ScannerLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List scannerLocationDescriptor = $convert.base64Decode('Cg9TY2FubmVyTG9jYXRpb24SRgoGc2xheWVyGAEgASgLMiwuY2FyYm9uLnBvcnRhbC5hc3NldHMuU2xheWVyUm93QXNzZXRMb2NhdGlvbkgAUgZzbGF5ZXISSwoGcmVhcGVyGAIgASgLMjEuY2FyYm9uLnBvcnRhbC5hc3NldHMuUmVhcGVyTW9kdWxlQWJBc3NldExvY2F0aW9uSABSBnJlYXBlckIMCgpnZW5lcmF0aW9u');
@$core.Deprecated('Use laserDescriptor instead')
const Laser$json = const {
  '1': 'Laser',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.LaserLocation', '10': 'location'},
  ],
};

/// Descriptor for `Laser`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List laserDescriptor = $convert.base64Decode('CgVMYXNlchIWCgZzZXJpYWwYASABKAlSBnNlcmlhbBI/Cghsb2NhdGlvbhgCIAEoCzIjLmNhcmJvbi5wb3J0YWwuYXNzZXRzLkxhc2VyTG9jYXRpb25SCGxvY2F0aW9u');
@$core.Deprecated('Use laserLocationDescriptor instead')
const LaserLocation$json = const {
  '1': 'LaserLocation',
  '2': const [
    const {'1': 'slayer', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.assets.SlayerRowAssetLocation', '9': 0, '10': 'slayer'},
    const {'1': 'reaper', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ReaperModuleAbAssetLocation', '9': 0, '10': 'reaper'},
  ],
  '8': const [
    const {'1': 'generation'},
  ],
};

/// Descriptor for `LaserLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List laserLocationDescriptor = $convert.base64Decode('Cg1MYXNlckxvY2F0aW9uEkYKBnNsYXllchgBIAEoCzIsLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlNsYXllclJvd0Fzc2V0TG9jYXRpb25IAFIGc2xheWVyEksKBnJlYXBlchgCIAEoCzIxLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlJlYXBlck1vZHVsZUFiQXNzZXRMb2NhdGlvbkgAUgZyZWFwZXJCDAoKZ2VuZXJhdGlvbg==');
@$core.Deprecated('Use targetCamDescriptor instead')
const TargetCam$json = const {
  '1': 'TargetCam',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.TargetCamLocation', '10': 'location'},
  ],
};

/// Descriptor for `TargetCam`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List targetCamDescriptor = $convert.base64Decode('CglUYXJnZXRDYW0SFgoGc2VyaWFsGAEgASgJUgZzZXJpYWwSQwoIbG9jYXRpb24YAiABKAsyJy5jYXJib24ucG9ydGFsLmFzc2V0cy5UYXJnZXRDYW1Mb2NhdGlvblIIbG9jYXRpb24=');
@$core.Deprecated('Use targetCamLocationDescriptor instead')
const TargetCamLocation$json = const {
  '1': 'TargetCamLocation',
  '2': const [
    const {'1': 'scanner_serial', '3': 1, '4': 1, '5': 9, '10': 'scannerSerial'},
  ],
};

/// Descriptor for `TargetCamLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List targetCamLocationDescriptor = $convert.base64Decode('ChFUYXJnZXRDYW1Mb2NhdGlvbhIlCg5zY2FubmVyX3NlcmlhbBgBIAEoCVINc2Nhbm5lclNlcmlhbA==');
@$core.Deprecated('Use predictCamDescriptor instead')
const PredictCam$json = const {
  '1': 'PredictCam',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.PredictCamLocation', '10': 'location'},
  ],
};

/// Descriptor for `PredictCam`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictCamDescriptor = $convert.base64Decode('CgpQcmVkaWN0Q2FtEhYKBnNlcmlhbBgBIAEoCVIGc2VyaWFsEkQKCGxvY2F0aW9uGAIgASgLMiguY2FyYm9uLnBvcnRhbC5hc3NldHMuUHJlZGljdENhbUxvY2F0aW9uUghsb2NhdGlvbg==');
@$core.Deprecated('Use predictCamLocationDescriptor instead')
const PredictCamLocation$json = const {
  '1': 'PredictCamLocation',
  '2': const [
    const {'1': 'slayer', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.assets.SlayerRowAssetLocation', '9': 0, '10': 'slayer'},
    const {'1': 'reaper', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ReaperModuleSingleAssetLocation', '9': 0, '10': 'reaper'},
  ],
  '8': const [
    const {'1': 'generation'},
  ],
};

/// Descriptor for `PredictCamLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictCamLocationDescriptor = $convert.base64Decode('ChJQcmVkaWN0Q2FtTG9jYXRpb24SRgoGc2xheWVyGAEgASgLMiwuY2FyYm9uLnBvcnRhbC5hc3NldHMuU2xheWVyUm93QXNzZXRMb2NhdGlvbkgAUgZzbGF5ZXISTwoGcmVhcGVyGAIgASgLMjUuY2FyYm9uLnBvcnRhbC5hc3NldHMuUmVhcGVyTW9kdWxlU2luZ2xlQXNzZXRMb2NhdGlvbkgAUgZyZWFwZXJCDAoKZ2VuZXJhdGlvbg==');
@$core.Deprecated('Use commandComputerDescriptor instead')
const CommandComputer$json = const {
  '1': 'CommandComputer',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.CommandComputerLocation', '10': 'location'},
  ],
};

/// Descriptor for `CommandComputer`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List commandComputerDescriptor = $convert.base64Decode('Cg9Db21tYW5kQ29tcHV0ZXISFgoGc2VyaWFsGAEgASgJUgZzZXJpYWwSSQoIbG9jYXRpb24YAiABKAsyLS5jYXJib24ucG9ydGFsLmFzc2V0cy5Db21tYW5kQ29tcHV0ZXJMb2NhdGlvblIIbG9jYXRpb24=');
@$core.Deprecated('Use commandComputerLocationDescriptor instead')
const CommandComputerLocation$json = const {
  '1': 'CommandComputerLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
  ],
};

/// Descriptor for `CommandComputerLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List commandComputerLocationDescriptor = $convert.base64Decode('ChdDb21tYW5kQ29tcHV0ZXJMb2NhdGlvbhIhCgxyb2JvdF9zZXJpYWwYASABKAlSC3JvYm90U2VyaWFs');
@$core.Deprecated('Use rowComputerDescriptor instead')
const RowComputer$json = const {
  '1': 'RowComputer',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.RowComputerLocation', '10': 'location'},
  ],
};

/// Descriptor for `RowComputer`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List rowComputerDescriptor = $convert.base64Decode('CgtSb3dDb21wdXRlchIWCgZzZXJpYWwYASABKAlSBnNlcmlhbBJFCghsb2NhdGlvbhgCIAEoCzIpLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlJvd0NvbXB1dGVyTG9jYXRpb25SCGxvY2F0aW9u');
@$core.Deprecated('Use rowComputerLocationDescriptor instead')
const RowComputerLocation$json = const {
  '1': 'RowComputerLocation',
  '2': const [
    const {'1': 'slayer', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.assets.SlayerRowComputerLocation', '9': 0, '10': 'slayer'},
    const {'1': 'reaper', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ReaperModuleSingleAssetLocation', '9': 0, '10': 'reaper'},
  ],
  '8': const [
    const {'1': 'generation'},
  ],
};

/// Descriptor for `RowComputerLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List rowComputerLocationDescriptor = $convert.base64Decode('ChNSb3dDb21wdXRlckxvY2F0aW9uEkkKBnNsYXllchgBIAEoCzIvLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlNsYXllclJvd0NvbXB1dGVyTG9jYXRpb25IAFIGc2xheWVyEk8KBnJlYXBlchgCIAEoCzI1LmNhcmJvbi5wb3J0YWwuYXNzZXRzLlJlYXBlck1vZHVsZVNpbmdsZUFzc2V0TG9jYXRpb25IAFIGcmVhcGVyQgwKCmdlbmVyYXRpb24=');
@$core.Deprecated('Use slayerRowComputerLocationDescriptor instead')
const SlayerRowComputerLocation$json = const {
  '1': 'SlayerRowComputerLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
    const {'1': 'row', '3': 2, '4': 1, '5': 5, '10': 'row'},
    const {'1': 'type', '3': 3, '4': 1, '5': 14, '6': '.carbon.portal.assets.SlayerRowComputerType', '10': 'type'},
  ],
};

/// Descriptor for `SlayerRowComputerLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List slayerRowComputerLocationDescriptor = $convert.base64Decode('ChlTbGF5ZXJSb3dDb21wdXRlckxvY2F0aW9uEiEKDHJvYm90X3NlcmlhbBgBIAEoCVILcm9ib3RTZXJpYWwSEAoDcm93GAIgASgFUgNyb3cSPwoEdHlwZRgDIAEoDjIrLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlNsYXllclJvd0NvbXB1dGVyVHlwZVIEdHlwZQ==');
@$core.Deprecated('Use weedingModuleDescriptor instead')
const WeedingModule$json = const {
  '1': 'WeedingModule',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.WeedingModuleLocation', '10': 'location'},
  ],
};

/// Descriptor for `WeedingModule`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List weedingModuleDescriptor = $convert.base64Decode('Cg1XZWVkaW5nTW9kdWxlEhYKBnNlcmlhbBgBIAEoCVIGc2VyaWFsEkcKCGxvY2F0aW9uGAIgASgLMisuY2FyYm9uLnBvcnRhbC5hc3NldHMuV2VlZGluZ01vZHVsZUxvY2F0aW9uUghsb2NhdGlvbg==');
@$core.Deprecated('Use weedingModuleLocationDescriptor instead')
const WeedingModuleLocation$json = const {
  '1': 'WeedingModuleLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
    const {'1': 'module_number', '3': 2, '4': 1, '5': 5, '10': 'moduleNumber'},
  ],
};

/// Descriptor for `WeedingModuleLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List weedingModuleLocationDescriptor = $convert.base64Decode('ChVXZWVkaW5nTW9kdWxlTG9jYXRpb24SIQoMcm9ib3Rfc2VyaWFsGAEgASgJUgtyb2JvdFNlcmlhbBIjCg1tb2R1bGVfbnVtYmVyGAIgASgFUgxtb2R1bGVOdW1iZXI=');
@$core.Deprecated('Use starlinkDescriptor instead')
const Starlink$json = const {
  '1': 'Starlink',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.StarlinkLocation', '10': 'location'},
  ],
};

/// Descriptor for `Starlink`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List starlinkDescriptor = $convert.base64Decode('CghTdGFybGluaxIWCgZzZXJpYWwYASABKAlSBnNlcmlhbBJCCghsb2NhdGlvbhgCIAEoCzImLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlN0YXJsaW5rTG9jYXRpb25SCGxvY2F0aW9u');
@$core.Deprecated('Use starlinkLocationDescriptor instead')
const StarlinkLocation$json = const {
  '1': 'StarlinkLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
  ],
};

/// Descriptor for `StarlinkLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List starlinkLocationDescriptor = $convert.base64Decode('ChBTdGFybGlua0xvY2F0aW9uEiEKDHJvYm90X3NlcmlhbBgBIAEoCVILcm9ib3RTZXJpYWw=');
@$core.Deprecated('Use modemDescriptor instead')
const Modem$json = const {
  '1': 'Modem',
  '2': const [
    const {'1': 'serial', '3': 1, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'location', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.assets.ModemLocation', '10': 'location'},
  ],
};

/// Descriptor for `Modem`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List modemDescriptor = $convert.base64Decode('CgVNb2RlbRIWCgZzZXJpYWwYASABKAlSBnNlcmlhbBI/Cghsb2NhdGlvbhgCIAEoCzIjLmNhcmJvbi5wb3J0YWwuYXNzZXRzLk1vZGVtTG9jYXRpb25SCGxvY2F0aW9u');
@$core.Deprecated('Use modemLocationDescriptor instead')
const ModemLocation$json = const {
  '1': 'ModemLocation',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
  ],
};

/// Descriptor for `ModemLocation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List modemLocationDescriptor = $convert.base64Decode('Cg1Nb2RlbUxvY2F0aW9uEiEKDHJvYm90X3NlcmlhbBgBIAEoCVILcm9ib3RTZXJpYWw=');
@$core.Deprecated('Use assetManifestDescriptor instead')
const AssetManifest$json = const {
  '1': 'AssetManifest',
  '2': const [
    const {'1': 'create_time', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'createTime'},
    const {'1': 'robot_serial', '3': 2, '4': 1, '5': 9, '10': 'robotSerial'},
    const {'1': 'command_computer', '3': 4, '4': 1, '5': 11, '6': '.carbon.portal.assets.CommandComputer', '10': 'commandComputer'},
    const {'1': 'weeding_modules', '3': 3, '4': 3, '5': 11, '6': '.carbon.portal.assets.WeedingModule', '10': 'weedingModules'},
    const {'1': 'row_computers', '3': 5, '4': 3, '5': 11, '6': '.carbon.portal.assets.RowComputer', '10': 'rowComputers'},
    const {'1': 'scanners', '3': 6, '4': 3, '5': 11, '6': '.carbon.portal.assets.Scanner', '10': 'scanners'},
    const {'1': 'lasers', '3': 7, '4': 3, '5': 11, '6': '.carbon.portal.assets.Laser', '10': 'lasers'},
    const {'1': 'target_cams', '3': 8, '4': 3, '5': 11, '6': '.carbon.portal.assets.TargetCam', '10': 'targetCams'},
    const {'1': 'predict_cams', '3': 9, '4': 3, '5': 11, '6': '.carbon.portal.assets.PredictCam', '10': 'predictCams'},
    const {'1': 'modem', '3': 10, '4': 1, '5': 11, '6': '.carbon.portal.assets.Modem', '10': 'modem'},
    const {'1': 'starlink', '3': 11, '4': 1, '5': 11, '6': '.carbon.portal.assets.Starlink', '10': 'starlink'},
  ],
};

/// Descriptor for `AssetManifest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List assetManifestDescriptor = $convert.base64Decode('Cg1Bc3NldE1hbmlmZXN0EjsKC2NyZWF0ZV90aW1lGAEgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcFIKY3JlYXRlVGltZRIhCgxyb2JvdF9zZXJpYWwYAiABKAlSC3JvYm90U2VyaWFsElAKEGNvbW1hbmRfY29tcHV0ZXIYBCABKAsyJS5jYXJib24ucG9ydGFsLmFzc2V0cy5Db21tYW5kQ29tcHV0ZXJSD2NvbW1hbmRDb21wdXRlchJMCg93ZWVkaW5nX21vZHVsZXMYAyADKAsyIy5jYXJib24ucG9ydGFsLmFzc2V0cy5XZWVkaW5nTW9kdWxlUg53ZWVkaW5nTW9kdWxlcxJGCg1yb3dfY29tcHV0ZXJzGAUgAygLMiEuY2FyYm9uLnBvcnRhbC5hc3NldHMuUm93Q29tcHV0ZXJSDHJvd0NvbXB1dGVycxI5CghzY2FubmVycxgGIAMoCzIdLmNhcmJvbi5wb3J0YWwuYXNzZXRzLlNjYW5uZXJSCHNjYW5uZXJzEjMKBmxhc2VycxgHIAMoCzIbLmNhcmJvbi5wb3J0YWwuYXNzZXRzLkxhc2VyUgZsYXNlcnMSQAoLdGFyZ2V0X2NhbXMYCCADKAsyHy5jYXJib24ucG9ydGFsLmFzc2V0cy5UYXJnZXRDYW1SCnRhcmdldENhbXMSQwoMcHJlZGljdF9jYW1zGAkgAygLMiAuY2FyYm9uLnBvcnRhbC5hc3NldHMuUHJlZGljdENhbVILcHJlZGljdENhbXMSMQoFbW9kZW0YCiABKAsyGy5jYXJib24ucG9ydGFsLmFzc2V0cy5Nb2RlbVIFbW9kZW0SOgoIc3RhcmxpbmsYCyABKAsyHi5jYXJib24ucG9ydGFsLmFzc2V0cy5TdGFybGlua1IIc3Rhcmxpbms=');
