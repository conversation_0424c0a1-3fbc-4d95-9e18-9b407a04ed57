///
//  Generated code. Do not modify.
//  source: portal/veselka.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use imageDescriptor instead')
const Image$json = const {
  '1': 'Image',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'location', '3': 2, '4': 1, '5': 9, '10': 'location'},
    const {'1': 'robot_id', '3': 3, '4': 1, '5': 9, '10': 'robotId'},
    const {'1': 'use_case', '3': 4, '4': 1, '5': 9, '10': 'useCase'},
    const {'1': 'role', '3': 5, '4': 1, '5': 9, '10': 'role'},
    const {'1': 'created', '3': 6, '4': 1, '5': 3, '10': 'created'},
    const {'1': 'url', '3': 7, '4': 1, '5': 9, '10': 'url'},
    const {'1': 'height', '3': 8, '4': 1, '5': 3, '10': 'height'},
    const {'1': 'width', '3': 9, '4': 1, '5': 3, '10': 'width'},
    const {'1': 'ppi', '3': 10, '4': 1, '5': 3, '10': 'ppi'},
    const {'1': 'valid', '3': 11, '4': 1, '5': 3, '10': 'valid'},
    const {'1': 'captured_at', '3': 12, '4': 1, '5': 3, '10': 'capturedAt'},
    const {'1': 'detection_json', '3': 13, '4': 1, '5': 9, '10': 'detectionJson'},
    const {'1': 'reason_json', '3': 14, '4': 1, '5': 9, '10': 'reasonJson'},
    const {'1': 'priority', '3': 15, '4': 1, '5': 9, '10': 'priority'},
    const {'1': 'cam_id', '3': 16, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'row_id', '3': 17, '4': 1, '5': 9, '10': 'rowId'},
    const {'1': 'geo_json', '3': 18, '4': 1, '5': 9, '10': 'geoJson'},
    const {'1': 'crop', '3': 19, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'image_type', '3': 20, '4': 1, '5': 9, '10': 'imageType'},
    const {'1': 'city', '3': 21, '4': 1, '5': 9, '10': 'city'},
    const {'1': 'corrected_hw', '3': 22, '4': 1, '5': 8, '10': 'correctedHw'},
    const {'1': 'session_name', '3': 23, '4': 1, '5': 9, '10': 'sessionName'},
    const {'1': 'crop_id', '3': 24, '4': 1, '5': 9, '10': 'cropId'},
    const {'1': 'focus_metric', '3': 25, '4': 1, '5': 3, '10': 'focusMetric'},
    const {'1': 'geohash', '3': 26, '4': 1, '5': 9, '10': 'geohash'},
    const {'1': 'quarantine_reason', '3': 27, '4': 1, '5': 9, '10': 'quarantineReason'},
  ],
};

/// Descriptor for `Image`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imageDescriptor = $convert.base64Decode('CgVJbWFnZRIOCgJpZBgBIAEoCVICaWQSGgoIbG9jYXRpb24YAiABKAlSCGxvY2F0aW9uEhkKCHJvYm90X2lkGAMgASgJUgdyb2JvdElkEhkKCHVzZV9jYXNlGAQgASgJUgd1c2VDYXNlEhIKBHJvbGUYBSABKAlSBHJvbGUSGAoHY3JlYXRlZBgGIAEoA1IHY3JlYXRlZBIQCgN1cmwYByABKAlSA3VybBIWCgZoZWlnaHQYCCABKANSBmhlaWdodBIUCgV3aWR0aBgJIAEoA1IFd2lkdGgSEAoDcHBpGAogASgDUgNwcGkSFAoFdmFsaWQYCyABKANSBXZhbGlkEh8KC2NhcHR1cmVkX2F0GAwgASgDUgpjYXB0dXJlZEF0EiUKDmRldGVjdGlvbl9qc29uGA0gASgJUg1kZXRlY3Rpb25Kc29uEh8KC3JlYXNvbl9qc29uGA4gASgJUgpyZWFzb25Kc29uEhoKCHByaW9yaXR5GA8gASgJUghwcmlvcml0eRIVCgZjYW1faWQYECABKAlSBWNhbUlkEhUKBnJvd19pZBgRIAEoCVIFcm93SWQSGQoIZ2VvX2pzb24YEiABKAlSB2dlb0pzb24SEgoEY3JvcBgTIAEoCVIEY3JvcBIdCgppbWFnZV90eXBlGBQgASgJUglpbWFnZVR5cGUSEgoEY2l0eRgVIAEoCVIEY2l0eRIhCgxjb3JyZWN0ZWRfaHcYFiABKAhSC2NvcnJlY3RlZEh3EiEKDHNlc3Npb25fbmFtZRgXIAEoCVILc2Vzc2lvbk5hbWUSFwoHY3JvcF9pZBgYIAEoCVIGY3JvcElkEiEKDGZvY3VzX21ldHJpYxgZIAEoA1ILZm9jdXNNZXRyaWMSGAoHZ2VvaGFzaBgaIAEoCVIHZ2VvaGFzaBIrChFxdWFyYW50aW5lX3JlYXNvbhgbIAEoCVIQcXVhcmFudGluZVJlYXNvbg==');
@$core.Deprecated('Use cropConfidenceDescriptor instead')
const CropConfidence$json = const {
  '1': 'CropConfidence',
  '2': const [
    const {'1': 'db', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.db.DB', '10': 'db'},
    const {'1': 'robot_id', '3': 2, '4': 1, '5': 3, '10': 'robotId'},
    const {'1': 'crop_id', '3': 3, '4': 1, '5': 9, '10': 'cropId'},
    const {'1': 'latitude', '3': 4, '4': 1, '5': 2, '10': 'latitude'},
    const {'1': 'longitude', '3': 5, '4': 1, '5': 2, '10': 'longitude'},
    const {'1': 'precision', '3': 6, '4': 1, '5': 3, '10': 'precision'},
    const {'1': 'num_total_images', '3': 7, '4': 1, '5': 3, '10': 'numTotalImages'},
    const {'1': 'num_regional_images', '3': 8, '4': 1, '5': 3, '10': 'numRegionalImages'},
    const {'1': 'confidence', '3': 9, '4': 1, '5': 9, '10': 'confidence'},
  ],
};

/// Descriptor for `CropConfidence`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cropConfidenceDescriptor = $convert.base64Decode('Cg5Dcm9wQ29uZmlkZW5jZRIkCgJkYhgBIAEoCzIULmNhcmJvbi5wb3J0YWwuZGIuREJSAmRiEhkKCHJvYm90X2lkGAIgASgDUgdyb2JvdElkEhcKB2Nyb3BfaWQYAyABKAlSBmNyb3BJZBIaCghsYXRpdHVkZRgEIAEoAlIIbGF0aXR1ZGUSHAoJbG9uZ2l0dWRlGAUgASgCUglsb25naXR1ZGUSHAoJcHJlY2lzaW9uGAYgASgDUglwcmVjaXNpb24SKAoQbnVtX3RvdGFsX2ltYWdlcxgHIAEoA1IObnVtVG90YWxJbWFnZXMSLgoTbnVtX3JlZ2lvbmFsX2ltYWdlcxgIIAEoA1IRbnVtUmVnaW9uYWxJbWFnZXMSHgoKY29uZmlkZW5jZRgJIAEoCVIKY29uZmlkZW5jZQ==');
@$core.Deprecated('Use cropDescriptor instead')
const Crop$json = const {
  '1': 'Crop',
  '2': const [
    const {'1': 'archived', '3': 1, '4': 1, '5': 8, '10': 'archived'},
    const {'1': 'carbon_name', '3': 2, '4': 1, '5': 9, '10': 'carbonName'},
    const {'1': 'common_name', '3': 3, '4': 1, '5': 9, '10': 'commonName'},
    const {'1': 'created', '3': 4, '4': 1, '5': 3, '10': 'created'},
    const {'1': 'description', '3': 5, '4': 1, '5': 9, '10': 'description'},
    const {'1': 'id', '3': 6, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'legacy_crop_name', '3': 7, '4': 1, '5': 9, '10': 'legacyCropName'},
    const {'1': 'notes', '3': 8, '4': 1, '5': 9, '10': 'notes'},
    const {'1': 'updated', '3': 9, '4': 1, '5': 3, '10': 'updated'},
  ],
};

/// Descriptor for `Crop`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cropDescriptor = $convert.base64Decode('CgRDcm9wEhoKCGFyY2hpdmVkGAEgASgIUghhcmNoaXZlZBIfCgtjYXJib25fbmFtZRgCIAEoCVIKY2FyYm9uTmFtZRIfCgtjb21tb25fbmFtZRgDIAEoCVIKY29tbW9uTmFtZRIYCgdjcmVhdGVkGAQgASgDUgdjcmVhdGVkEiAKC2Rlc2NyaXB0aW9uGAUgASgJUgtkZXNjcmlwdGlvbhIOCgJpZBgGIAEoCVICaWQSKAoQbGVnYWN5X2Nyb3BfbmFtZRgHIAEoCVIObGVnYWN5Q3JvcE5hbWUSFAoFbm90ZXMYCCABKAlSBW5vdGVzEhgKB3VwZGF0ZWQYCSABKANSB3VwZGF0ZWQ=');
@$core.Deprecated('Use robotCropDescriptor instead')
const RobotCrop$json = const {
  '1': 'RobotCrop',
  '2': const [
    const {'1': 'crop', '3': 1, '4': 1, '5': 11, '6': '.carbon.portal.veselka.Crop', '10': 'crop'},
    const {'1': 'confidence', '3': 2, '4': 1, '5': 11, '6': '.carbon.portal.veselka.CropConfidence', '10': 'confidence'},
  ],
};

/// Descriptor for `RobotCrop`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List robotCropDescriptor = $convert.base64Decode('CglSb2JvdENyb3ASLwoEY3JvcBgBIAEoCzIbLmNhcmJvbi5wb3J0YWwudmVzZWxrYS5Dcm9wUgRjcm9wEkUKCmNvbmZpZGVuY2UYAiABKAsyJS5jYXJib24ucG9ydGFsLnZlc2Vsa2EuQ3JvcENvbmZpZGVuY2VSCmNvbmZpZGVuY2U=');
@$core.Deprecated('Use modelDescriptor instead')
const Model$json = const {
  '1': 'Model',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'created', '3': 2, '4': 1, '5': 3, '10': 'created'},
    const {'1': 'updated', '3': 3, '4': 1, '5': 3, '10': 'updated'},
    const {'1': 'url', '3': 4, '4': 1, '5': 9, '10': 'url'},
    const {'1': 'customer', '3': 5, '4': 1, '5': 9, '10': 'customer'},
    const {'1': 'crop', '3': 6, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'version', '3': 7, '4': 1, '5': 3, '10': 'version'},
    const {'1': 'training_docker_tag', '3': 8, '4': 1, '5': 9, '10': 'trainingDockerTag'},
    const {'1': 'gitSha', '3': 9, '4': 1, '5': 9, '10': 'gitSha'},
    const {'1': 'checksum', '3': 10, '4': 1, '5': 9, '10': 'checksum'},
    const {'1': 'location', '3': 11, '4': 1, '5': 9, '10': 'location'},
    const {'1': 'trained_at', '3': 12, '4': 1, '5': 3, '10': 'trainedAt'},
    const {'1': 'type', '3': 13, '4': 1, '5': 9, '10': 'type'},
    const {'1': 'description', '3': 14, '4': 1, '5': 9, '10': 'description'},
    const {'1': 'metadata_json', '3': 15, '4': 1, '5': 9, '10': 'metadataJson'},
    const {'1': 'production_container_version', '3': 16, '4': 1, '5': 9, '10': 'productionContainerVersion'},
    const {'1': 'test_results_json', '3': 17, '4': 1, '5': 9, '10': 'testResultsJson'},
    const {'1': 'wandb_json', '3': 18, '4': 1, '5': 9, '10': 'wandbJson'},
    const {'1': 'snapshot_json', '3': 19, '4': 1, '5': 9, '10': 'snapshotJson'},
    const {'1': 'is_stub', '3': 20, '4': 1, '5': 8, '10': 'isStub'},
    const {'1': 'is_good_to_deploy', '3': 21, '4': 1, '5': 8, '10': 'isGoodToDeploy'},
    const {'1': 'robot_name', '3': 22, '4': 1, '5': 9, '10': 'robotName'},
    const {'1': 'environment', '3': 23, '4': 1, '5': 9, '10': 'environment'},
    const {'1': 'deploy', '3': 24, '4': 1, '5': 8, '10': 'deploy'},
    const {'1': 'is_pretraining', '3': 25, '4': 1, '5': 8, '10': 'isPretraining'},
    const {'1': 'sub_type', '3': 26, '4': 1, '5': 9, '10': 'subType'},
    const {'1': 'dataset_id', '3': 27, '4': 1, '5': 9, '10': 'datasetId'},
    const {'1': 'container_version', '3': 28, '4': 1, '5': 9, '10': 'containerVersion'},
    const {'1': 'container_id', '3': 29, '4': 1, '5': 9, '10': 'containerId'},
    const {'1': 'pipeline_id', '3': 30, '4': 1, '5': 9, '10': 'pipelineId'},
    const {'1': 'parent_model_id', '3': 31, '4': 1, '5': 9, '10': 'parentModelId'},
    const {'1': 'viable_crop_ids', '3': 32, '4': 3, '5': 9, '10': 'viableCropIds'},
  ],
};

/// Descriptor for `Model`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List modelDescriptor = $convert.base64Decode('CgVNb2RlbBIOCgJpZBgBIAEoCVICaWQSGAoHY3JlYXRlZBgCIAEoA1IHY3JlYXRlZBIYCgd1cGRhdGVkGAMgASgDUgd1cGRhdGVkEhAKA3VybBgEIAEoCVIDdXJsEhoKCGN1c3RvbWVyGAUgASgJUghjdXN0b21lchISCgRjcm9wGAYgASgJUgRjcm9wEhgKB3ZlcnNpb24YByABKANSB3ZlcnNpb24SLgoTdHJhaW5pbmdfZG9ja2VyX3RhZxgIIAEoCVIRdHJhaW5pbmdEb2NrZXJUYWcSFgoGZ2l0U2hhGAkgASgJUgZnaXRTaGESGgoIY2hlY2tzdW0YCiABKAlSCGNoZWNrc3VtEhoKCGxvY2F0aW9uGAsgASgJUghsb2NhdGlvbhIdCgp0cmFpbmVkX2F0GAwgASgDUgl0cmFpbmVkQXQSEgoEdHlwZRgNIAEoCVIEdHlwZRIgCgtkZXNjcmlwdGlvbhgOIAEoCVILZGVzY3JpcHRpb24SIwoNbWV0YWRhdGFfanNvbhgPIAEoCVIMbWV0YWRhdGFKc29uEkAKHHByb2R1Y3Rpb25fY29udGFpbmVyX3ZlcnNpb24YECABKAlSGnByb2R1Y3Rpb25Db250YWluZXJWZXJzaW9uEioKEXRlc3RfcmVzdWx0c19qc29uGBEgASgJUg90ZXN0UmVzdWx0c0pzb24SHQoKd2FuZGJfanNvbhgSIAEoCVIJd2FuZGJKc29uEiMKDXNuYXBzaG90X2pzb24YEyABKAlSDHNuYXBzaG90SnNvbhIXCgdpc19zdHViGBQgASgIUgZpc1N0dWISKQoRaXNfZ29vZF90b19kZXBsb3kYFSABKAhSDmlzR29vZFRvRGVwbG95Eh0KCnJvYm90X25hbWUYFiABKAlSCXJvYm90TmFtZRIgCgtlbnZpcm9ubWVudBgXIAEoCVILZW52aXJvbm1lbnQSFgoGZGVwbG95GBggASgIUgZkZXBsb3kSJQoOaXNfcHJldHJhaW5pbmcYGSABKAhSDWlzUHJldHJhaW5pbmcSGQoIc3ViX3R5cGUYGiABKAlSB3N1YlR5cGUSHQoKZGF0YXNldF9pZBgbIAEoCVIJZGF0YXNldElkEisKEWNvbnRhaW5lcl92ZXJzaW9uGBwgASgJUhBjb250YWluZXJWZXJzaW9uEiEKDGNvbnRhaW5lcl9pZBgdIAEoCVILY29udGFpbmVySWQSHwoLcGlwZWxpbmVfaWQYHiABKAlSCnBpcGVsaW5lSWQSJgoPcGFyZW50X21vZGVsX2lkGB8gASgJUg1wYXJlbnRNb2RlbElkEiYKD3ZpYWJsZV9jcm9wX2lkcxggIAMoCVINdmlhYmxlQ3JvcElkcw==');
@$core.Deprecated('Use createCategoryCollectionSessionRequestDescriptor instead')
const CreateCategoryCollectionSessionRequest$json = const {
  '1': 'CreateCategoryCollectionSessionRequest',
  '2': const [
    const {'1': 'model_id', '3': 1, '4': 1, '5': 9, '10': 'modelId'},
    const {'1': 'customer_id', '3': 2, '4': 1, '5': 4, '9': 0, '10': 'customerId', '17': true},
    const {'1': 'category_collection_profile', '3': 3, '4': 1, '5': 11, '6': '.carbon.portal.category_profile.UnsavedExpandedCategoryCollection', '10': 'categoryCollectionProfile'},
  ],
  '8': const [
    const {'1': '_customer_id'},
  ],
};

/// Descriptor for `CreateCategoryCollectionSessionRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createCategoryCollectionSessionRequestDescriptor = $convert.base64Decode('CiZDcmVhdGVDYXRlZ29yeUNvbGxlY3Rpb25TZXNzaW9uUmVxdWVzdBIZCghtb2RlbF9pZBgBIAEoCVIHbW9kZWxJZBIkCgtjdXN0b21lcl9pZBgCIAEoBEgAUgpjdXN0b21lcklkiAEBEoEBChtjYXRlZ29yeV9jb2xsZWN0aW9uX3Byb2ZpbGUYAyABKAsyQS5jYXJib24ucG9ydGFsLmNhdGVnb3J5X3Byb2ZpbGUuVW5zYXZlZEV4cGFuZGVkQ2F0ZWdvcnlDb2xsZWN0aW9uUhljYXRlZ29yeUNvbGxlY3Rpb25Qcm9maWxlQg4KDF9jdXN0b21lcl9pZA==');
