///
//  Generated code. Do not modify.
//  source: portal/category_profile.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../category_profile/category_profile.pb.dart' as $65;

class Metadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Metadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updatedAt')
    ..hasRequiredFields = false
  ;

  Metadata._() : super();
  factory Metadata({
    $fixnum.Int64? updatedAt,
  }) {
    final _result = create();
    if (updatedAt != null) {
      _result.updatedAt = updatedAt;
    }
    return _result;
  }
  factory Metadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Metadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Metadata clone() => Metadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Metadata copyWith(void Function(Metadata) updates) => super.copyWith((message) => updates(message as Metadata)) as Metadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Metadata create() => Metadata._();
  Metadata createEmptyInstance() => create();
  static $pb.PbList<Metadata> createRepeated() => $pb.PbList<Metadata>();
  @$core.pragma('dart2js:noInline')
  static Metadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Metadata>(create);
  static Metadata? _defaultInstance;

  @$pb.TagNumber(6)
  $fixnum.Int64 get updatedAt => $_getI64(0);
  @$pb.TagNumber(6)
  set updatedAt($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(6)
  $core.bool hasUpdatedAt() => $_has(0);
  @$pb.TagNumber(6)
  void clearUpdatedAt() => clearField(6);
}

class SavedCategoryCollection extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SavedCategoryCollection', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$65.CategoryCollection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $65.CategoryCollection.create)
    ..aOM<Metadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: Metadata.create)
    ..hasRequiredFields = false
  ;

  SavedCategoryCollection._() : super();
  factory SavedCategoryCollection({
    $65.CategoryCollection? profile,
    Metadata? metadata,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    return _result;
  }
  factory SavedCategoryCollection.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SavedCategoryCollection.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SavedCategoryCollection clone() => SavedCategoryCollection()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SavedCategoryCollection copyWith(void Function(SavedCategoryCollection) updates) => super.copyWith((message) => updates(message as SavedCategoryCollection)) as SavedCategoryCollection; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SavedCategoryCollection create() => SavedCategoryCollection._();
  SavedCategoryCollection createEmptyInstance() => create();
  static $pb.PbList<SavedCategoryCollection> createRepeated() => $pb.PbList<SavedCategoryCollection>();
  @$core.pragma('dart2js:noInline')
  static SavedCategoryCollection getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SavedCategoryCollection>(create);
  static SavedCategoryCollection? _defaultInstance;

  @$pb.TagNumber(1)
  $65.CategoryCollection get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($65.CategoryCollection v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $65.CategoryCollection ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  Metadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata(Metadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  Metadata ensureMetadata() => $_ensure(1);
}

class SavedCategory extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SavedCategory', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$65.Category>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $65.Category.create)
    ..aOM<Metadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: Metadata.create)
    ..hasRequiredFields = false
  ;

  SavedCategory._() : super();
  factory SavedCategory({
    $65.Category? profile,
    Metadata? metadata,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    return _result;
  }
  factory SavedCategory.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SavedCategory.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SavedCategory clone() => SavedCategory()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SavedCategory copyWith(void Function(SavedCategory) updates) => super.copyWith((message) => updates(message as SavedCategory)) as SavedCategory; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SavedCategory create() => SavedCategory._();
  SavedCategory createEmptyInstance() => create();
  static $pb.PbList<SavedCategory> createRepeated() => $pb.PbList<SavedCategory>();
  @$core.pragma('dart2js:noInline')
  static SavedCategory getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SavedCategory>(create);
  static SavedCategory? _defaultInstance;

  @$pb.TagNumber(1)
  $65.Category get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($65.Category v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $65.Category ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  Metadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata(Metadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  Metadata ensureMetadata() => $_ensure(1);
}

class SavedExpandedCategoryCollection extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SavedExpandedCategoryCollection', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<SavedCategoryCollection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: SavedCategoryCollection.create)
    ..pc<SavedCategory>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categories', $pb.PbFieldType.PM, subBuilder: SavedCategory.create)
    ..hasRequiredFields = false
  ;

  SavedExpandedCategoryCollection._() : super();
  factory SavedExpandedCategoryCollection({
    SavedCategoryCollection? profile,
    $core.Iterable<SavedCategory>? categories,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (categories != null) {
      _result.categories.addAll(categories);
    }
    return _result;
  }
  factory SavedExpandedCategoryCollection.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SavedExpandedCategoryCollection.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SavedExpandedCategoryCollection clone() => SavedExpandedCategoryCollection()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SavedExpandedCategoryCollection copyWith(void Function(SavedExpandedCategoryCollection) updates) => super.copyWith((message) => updates(message as SavedExpandedCategoryCollection)) as SavedExpandedCategoryCollection; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SavedExpandedCategoryCollection create() => SavedExpandedCategoryCollection._();
  SavedExpandedCategoryCollection createEmptyInstance() => create();
  static $pb.PbList<SavedExpandedCategoryCollection> createRepeated() => $pb.PbList<SavedExpandedCategoryCollection>();
  @$core.pragma('dart2js:noInline')
  static SavedExpandedCategoryCollection getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SavedExpandedCategoryCollection>(create);
  static SavedExpandedCategoryCollection? _defaultInstance;

  @$pb.TagNumber(1)
  SavedCategoryCollection get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile(SavedCategoryCollection v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  SavedCategoryCollection ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<SavedCategory> get categories => $_getList(1);
}

class UnsavedExpandedCategoryCollection extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UnsavedExpandedCategoryCollection', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$65.CategoryCollection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $65.CategoryCollection.create)
    ..pc<$65.Category>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categories', $pb.PbFieldType.PM, subBuilder: $65.Category.create)
    ..hasRequiredFields = false
  ;

  UnsavedExpandedCategoryCollection._() : super();
  factory UnsavedExpandedCategoryCollection({
    $65.CategoryCollection? profile,
    $core.Iterable<$65.Category>? categories,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (categories != null) {
      _result.categories.addAll(categories);
    }
    return _result;
  }
  factory UnsavedExpandedCategoryCollection.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UnsavedExpandedCategoryCollection.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UnsavedExpandedCategoryCollection clone() => UnsavedExpandedCategoryCollection()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UnsavedExpandedCategoryCollection copyWith(void Function(UnsavedExpandedCategoryCollection) updates) => super.copyWith((message) => updates(message as UnsavedExpandedCategoryCollection)) as UnsavedExpandedCategoryCollection; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UnsavedExpandedCategoryCollection create() => UnsavedExpandedCategoryCollection._();
  UnsavedExpandedCategoryCollection createEmptyInstance() => create();
  static $pb.PbList<UnsavedExpandedCategoryCollection> createRepeated() => $pb.PbList<UnsavedExpandedCategoryCollection>();
  @$core.pragma('dart2js:noInline')
  static UnsavedExpandedCategoryCollection getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UnsavedExpandedCategoryCollection>(create);
  static UnsavedExpandedCategoryCollection? _defaultInstance;

  @$pb.TagNumber(1)
  $65.CategoryCollection get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($65.CategoryCollection v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $65.CategoryCollection ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$65.Category> get categories => $_getList(1);
}

