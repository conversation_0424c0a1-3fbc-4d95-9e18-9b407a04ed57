///
//  Generated code. Do not modify.
//  source: software_manager/software_manager.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'software_manager.pb.dart' as $58;
export 'software_manager.pb.dart';

class SoftwareManagerServiceClient extends $grpc.Client {
  static final _$getSoftwareVersionMetadata = $grpc.ClientMethod<
          $58.SoftwareVersionMetadataRequest, $58.SoftwareVersionMetadata>(
      '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
      ($58.SoftwareVersionMetadataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $58.SoftwareVersionMetadata.fromBuffer(value));
  static final _$getVersionsSummary =
      $grpc.ClientMethod<$58.VersionSummaryRequest, $58.VersionSummaryReply>(
          '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
          ($58.VersionSummaryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $58.VersionSummaryReply.fromBuffer(value));
  static final _$triggerUpdate =
      $grpc.ClientMethod<$58.TriggerUpdateRequest, $58.TriggerUpdateReply>(
          '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
          ($58.TriggerUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $58.TriggerUpdateReply.fromBuffer(value));
  static final _$getIdentity =
      $grpc.ClientMethod<$58.GetIdentityRequest, $58.IdentityInfo>(
          '/carbon.software_manager.SoftwareManagerService/GetIdentity',
          ($58.GetIdentityRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $58.IdentityInfo.fromBuffer(value));
  static final _$clearPackagesCache = $grpc.ClientMethod<
          $58.ClearPackagesCacheRequest, $58.ClearPackagesCacheResponse>(
      '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
      ($58.ClearPackagesCacheRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $58.ClearPackagesCacheResponse.fromBuffer(value));
  static final _$prepareUpdate =
      $grpc.ClientMethod<$58.PrepareUpdateRequest, $58.PrepareUpdateResponse>(
          '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
          ($58.PrepareUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $58.PrepareUpdateResponse.fromBuffer(value));
  static final _$abortUpdate =
      $grpc.ClientMethod<$58.AbortUpdateRequest, $58.AbortUpdateResponse>(
          '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
          ($58.AbortUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $58.AbortUpdateResponse.fromBuffer(value));

  SoftwareManagerServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$58.SoftwareVersionMetadata> getSoftwareVersionMetadata(
      $58.SoftwareVersionMetadataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSoftwareVersionMetadata, request,
        options: options);
  }

  $grpc.ResponseFuture<$58.VersionSummaryReply> getVersionsSummary(
      $58.VersionSummaryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getVersionsSummary, request, options: options);
  }

  $grpc.ResponseFuture<$58.TriggerUpdateReply> triggerUpdate(
      $58.TriggerUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$triggerUpdate, request, options: options);
  }

  $grpc.ResponseFuture<$58.IdentityInfo> getIdentity(
      $58.GetIdentityRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getIdentity, request, options: options);
  }

  $grpc.ResponseFuture<$58.ClearPackagesCacheResponse> clearPackagesCache(
      $58.ClearPackagesCacheRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$clearPackagesCache, request, options: options);
  }

  $grpc.ResponseFuture<$58.PrepareUpdateResponse> prepareUpdate(
      $58.PrepareUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$prepareUpdate, request, options: options);
  }

  $grpc.ResponseFuture<$58.AbortUpdateResponse> abortUpdate(
      $58.AbortUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$abortUpdate, request, options: options);
  }
}

abstract class SoftwareManagerServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.software_manager.SoftwareManagerService';

  SoftwareManagerServiceBase() {
    $addMethod($grpc.ServiceMethod<$58.SoftwareVersionMetadataRequest,
            $58.SoftwareVersionMetadata>(
        'GetSoftwareVersionMetadata',
        getSoftwareVersionMetadata_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $58.SoftwareVersionMetadataRequest.fromBuffer(value),
        ($58.SoftwareVersionMetadata value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$58.VersionSummaryRequest, $58.VersionSummaryReply>(
            'GetVersionsSummary',
            getVersionsSummary_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $58.VersionSummaryRequest.fromBuffer(value),
            ($58.VersionSummaryReply value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$58.TriggerUpdateRequest, $58.TriggerUpdateReply>(
            'TriggerUpdate',
            triggerUpdate_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $58.TriggerUpdateRequest.fromBuffer(value),
            ($58.TriggerUpdateReply value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$58.GetIdentityRequest, $58.IdentityInfo>(
        'GetIdentity',
        getIdentity_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $58.GetIdentityRequest.fromBuffer(value),
        ($58.IdentityInfo value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$58.ClearPackagesCacheRequest,
            $58.ClearPackagesCacheResponse>(
        'ClearPackagesCache',
        clearPackagesCache_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $58.ClearPackagesCacheRequest.fromBuffer(value),
        ($58.ClearPackagesCacheResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$58.PrepareUpdateRequest,
            $58.PrepareUpdateResponse>(
        'PrepareUpdate',
        prepareUpdate_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $58.PrepareUpdateRequest.fromBuffer(value),
        ($58.PrepareUpdateResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$58.AbortUpdateRequest, $58.AbortUpdateResponse>(
            'AbortUpdate',
            abortUpdate_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $58.AbortUpdateRequest.fromBuffer(value),
            ($58.AbortUpdateResponse value) => value.writeToBuffer()));
  }

  $async.Future<$58.SoftwareVersionMetadata> getSoftwareVersionMetadata_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.SoftwareVersionMetadataRequest> request) async {
    return getSoftwareVersionMetadata(call, await request);
  }

  $async.Future<$58.VersionSummaryReply> getVersionsSummary_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.VersionSummaryRequest> request) async {
    return getVersionsSummary(call, await request);
  }

  $async.Future<$58.TriggerUpdateReply> triggerUpdate_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.TriggerUpdateRequest> request) async {
    return triggerUpdate(call, await request);
  }

  $async.Future<$58.IdentityInfo> getIdentity_Pre($grpc.ServiceCall call,
      $async.Future<$58.GetIdentityRequest> request) async {
    return getIdentity(call, await request);
  }

  $async.Future<$58.ClearPackagesCacheResponse> clearPackagesCache_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.ClearPackagesCacheRequest> request) async {
    return clearPackagesCache(call, await request);
  }

  $async.Future<$58.PrepareUpdateResponse> prepareUpdate_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.PrepareUpdateRequest> request) async {
    return prepareUpdate(call, await request);
  }

  $async.Future<$58.AbortUpdateResponse> abortUpdate_Pre($grpc.ServiceCall call,
      $async.Future<$58.AbortUpdateRequest> request) async {
    return abortUpdate(call, await request);
  }

  $async.Future<$58.SoftwareVersionMetadata> getSoftwareVersionMetadata(
      $grpc.ServiceCall call, $58.SoftwareVersionMetadataRequest request);
  $async.Future<$58.VersionSummaryReply> getVersionsSummary(
      $grpc.ServiceCall call, $58.VersionSummaryRequest request);
  $async.Future<$58.TriggerUpdateReply> triggerUpdate(
      $grpc.ServiceCall call, $58.TriggerUpdateRequest request);
  $async.Future<$58.IdentityInfo> getIdentity(
      $grpc.ServiceCall call, $58.GetIdentityRequest request);
  $async.Future<$58.ClearPackagesCacheResponse> clearPackagesCache(
      $grpc.ServiceCall call, $58.ClearPackagesCacheRequest request);
  $async.Future<$58.PrepareUpdateResponse> prepareUpdate(
      $grpc.ServiceCall call, $58.PrepareUpdateRequest request);
  $async.Future<$58.AbortUpdateResponse> abortUpdate(
      $grpc.ServiceCall call, $58.AbortUpdateRequest request);
}
