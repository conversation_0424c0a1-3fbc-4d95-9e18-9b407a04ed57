///
//  Generated code. Do not modify.
//  source: veselka/prediction_point.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use sessionActivityStateDescriptor instead')
const SessionActivityState$json = const {
  '1': 'SessionActivityState',
  '2': const [
    const {'1': 'SESSION_ACTIVITY_STATE_UNSPECIFIED', '2': 0},
    const {'1': 'ACTIVE', '2': 1},
    const {'1': 'INACTIVE', '2': 2},
  ],
};

/// Descriptor for `SessionActivityState`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List sessionActivityStateDescriptor = $convert.base64Decode('ChRTZXNzaW9uQWN0aXZpdHlTdGF0ZRImCiJTRVNTSU9OX0FDVElWSVRZX1NUQVRFX1VOU1BFQ0lGSUVEEAASCgoGQUNUSVZFEAESDAoISU5BQ1RJVkUQAg==');
@$core.Deprecated('Use imageDescriptor instead')
const Image$json = const {
  '1': 'Image',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'url', '3': 2, '4': 1, '5': 9, '10': 'url'},
    const {'1': 'ppcm', '3': 3, '4': 1, '5': 2, '10': 'ppcm'},
    const {'1': 'captured_at', '3': 4, '4': 1, '5': 3, '10': 'capturedAt'},
    const {'1': 'uploaded_by_operator', '3': 5, '4': 1, '5': 8, '10': 'uploadedByOperator'},
  ],
};

/// Descriptor for `Image`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imageDescriptor = $convert.base64Decode('CgVJbWFnZRIOCgJpZBgBIAEoCVICaWQSEAoDdXJsGAIgASgJUgN1cmwSEgoEcHBjbRgDIAEoAlIEcHBjbRIfCgtjYXB0dXJlZF9hdBgEIAEoA1IKY2FwdHVyZWRBdBIwChR1cGxvYWRlZF9ieV9vcGVyYXRvchgFIAEoCFISdXBsb2FkZWRCeU9wZXJhdG9y');
@$core.Deprecated('Use predictionPointDescriptor instead')
const PredictionPoint$json = const {
  '1': 'PredictionPoint',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'x', '3': 2, '4': 1, '5': 2, '10': 'x'},
    const {'1': 'y', '3': 3, '4': 1, '5': 2, '10': 'y'},
    const {'1': 'radius', '3': 4, '4': 1, '5': 2, '10': 'radius'},
    const {'1': 'image', '3': 5, '4': 1, '5': 11, '6': '.carbon.veselka.prediction_point.Image', '10': 'image'},
    const {'1': 'category_id', '3': 6, '4': 1, '5': 9, '10': 'categoryId'},
  ],
};

/// Descriptor for `PredictionPoint`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictionPointDescriptor = $convert.base64Decode('Cg9QcmVkaWN0aW9uUG9pbnQSDgoCaWQYASABKAlSAmlkEgwKAXgYAiABKAJSAXgSDAoBeRgDIAEoAlIBeRIWCgZyYWRpdXMYBCABKAJSBnJhZGl1cxI8CgVpbWFnZRgFIAEoCzImLmNhcmJvbi52ZXNlbGthLnByZWRpY3Rpb25fcG9pbnQuSW1hZ2VSBWltYWdlEh8KC2NhdGVnb3J5X2lkGAYgASgJUgpjYXRlZ29yeUlk');
@$core.Deprecated('Use listPredictionPointsResponseDescriptor instead')
const ListPredictionPointsResponse$json = const {
  '1': 'ListPredictionPointsResponse',
  '2': const [
    const {'1': 'data', '3': 1, '4': 3, '5': 11, '6': '.carbon.veselka.prediction_point.PredictionPoint', '10': 'data'},
    const {'1': 'pagination', '3': 2, '4': 1, '5': 11, '6': '.carbon.veselka.pagination.Pagination', '10': 'pagination'},
  ],
};

/// Descriptor for `ListPredictionPointsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listPredictionPointsResponseDescriptor = $convert.base64Decode('ChxMaXN0UHJlZGljdGlvblBvaW50c1Jlc3BvbnNlEkQKBGRhdGEYASADKAsyMC5jYXJib24udmVzZWxrYS5wcmVkaWN0aW9uX3BvaW50LlByZWRpY3Rpb25Qb2ludFIEZGF0YRJFCgpwYWdpbmF0aW9uGAIgASgLMiUuY2FyYm9uLnZlc2Vsa2EucGFnaW5hdGlvbi5QYWdpbmF0aW9uUgpwYWdpbmF0aW9u');
@$core.Deprecated('Use categoryProfileDescriptor instead')
const CategoryProfile$json = const {
  '1': 'CategoryProfile',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'prediction_point_ids', '3': 2, '4': 3, '5': 9, '10': 'predictionPointIds'},
  ],
};

/// Descriptor for `CategoryProfile`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List categoryProfileDescriptor = $convert.base64Decode('Cg9DYXRlZ29yeVByb2ZpbGUSDgoCaWQYASABKAlSAmlkEjAKFHByZWRpY3Rpb25fcG9pbnRfaWRzGAIgAygJUhJwcmVkaWN0aW9uUG9pbnRJZHM=');
@$core.Deprecated('Use categoryCollectionProfileDescriptor instead')
const CategoryCollectionProfile$json = const {
  '1': 'CategoryCollectionProfile',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'category_profiles', '3': 2, '4': 3, '5': 11, '6': '.carbon.veselka.prediction_point.CategoryProfile', '10': 'categoryProfiles'},
  ],
};

/// Descriptor for `CategoryCollectionProfile`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List categoryCollectionProfileDescriptor = $convert.base64Decode('ChlDYXRlZ29yeUNvbGxlY3Rpb25Qcm9maWxlEg4KAmlkGAEgASgJUgJpZBJdChFjYXRlZ29yeV9wcm9maWxlcxgCIAMoCzIwLmNhcmJvbi52ZXNlbGthLnByZWRpY3Rpb25fcG9pbnQuQ2F0ZWdvcnlQcm9maWxlUhBjYXRlZ29yeVByb2ZpbGVz');
@$core.Deprecated('Use createPredictionPointSessionRequestDescriptor instead')
const CreatePredictionPointSessionRequest$json = const {
  '1': 'CreatePredictionPointSessionRequest',
  '2': const [
    const {'1': 'model_id', '3': 1, '4': 1, '5': 9, '10': 'modelId'},
    const {'1': 'robot_ids', '3': 2, '4': 3, '5': 9, '10': 'robotIds'},
    const {'1': 'category_collection_profile', '3': 3, '4': 1, '5': 11, '6': '.carbon.veselka.prediction_point.CategoryCollectionProfile', '10': 'categoryCollectionProfile'},
  ],
};

/// Descriptor for `CreatePredictionPointSessionRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createPredictionPointSessionRequestDescriptor = $convert.base64Decode('CiNDcmVhdGVQcmVkaWN0aW9uUG9pbnRTZXNzaW9uUmVxdWVzdBIZCghtb2RlbF9pZBgBIAEoCVIHbW9kZWxJZBIbCglyb2JvdF9pZHMYAiADKAlSCHJvYm90SWRzEnoKG2NhdGVnb3J5X2NvbGxlY3Rpb25fcHJvZmlsZRgDIAEoCzI6LmNhcmJvbi52ZXNlbGthLnByZWRpY3Rpb25fcG9pbnQuQ2F0ZWdvcnlDb2xsZWN0aW9uUHJvZmlsZVIZY2F0ZWdvcnlDb2xsZWN0aW9uUHJvZmlsZQ==');
@$core.Deprecated('Use createPredictionPointSessionResponseDescriptor instead')
const CreatePredictionPointSessionResponse$json = const {
  '1': 'CreatePredictionPointSessionResponse',
  '2': const [
    const {'1': 'session_id', '3': 1, '4': 1, '5': 9, '10': 'sessionId'},
    const {'1': 'status', '3': 2, '4': 1, '5': 14, '6': '.carbon.veselka.prediction_point.SessionActivityState', '10': 'status'},
  ],
};

/// Descriptor for `CreatePredictionPointSessionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createPredictionPointSessionResponseDescriptor = $convert.base64Decode('CiRDcmVhdGVQcmVkaWN0aW9uUG9pbnRTZXNzaW9uUmVzcG9uc2USHQoKc2Vzc2lvbl9pZBgBIAEoCVIJc2Vzc2lvbklkEk0KBnN0YXR1cxgCIAEoDjI1LmNhcmJvbi52ZXNlbGthLnByZWRpY3Rpb25fcG9pbnQuU2Vzc2lvbkFjdGl2aXR5U3RhdGVSBnN0YXR1cw==');
@$core.Deprecated('Use sessionInfoDescriptor instead')
const SessionInfo$json = const {
  '1': 'SessionInfo',
  '2': const [
    const {'1': 'model_id', '3': 1, '4': 1, '5': 9, '10': 'modelId'},
    const {'1': 'robot_ids', '3': 2, '4': 3, '5': 9, '10': 'robotIds'},
    const {'1': 'category_collection_profile', '3': 3, '4': 1, '5': 11, '6': '.carbon.veselka.prediction_point.CategoryCollectionProfile', '10': 'categoryCollectionProfile'},
    const {'1': 'session_id', '3': 4, '4': 1, '5': 9, '10': 'sessionId'},
  ],
};

/// Descriptor for `SessionInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sessionInfoDescriptor = $convert.base64Decode('CgtTZXNzaW9uSW5mbxIZCghtb2RlbF9pZBgBIAEoCVIHbW9kZWxJZBIbCglyb2JvdF9pZHMYAiADKAlSCHJvYm90SWRzEnoKG2NhdGVnb3J5X2NvbGxlY3Rpb25fcHJvZmlsZRgDIAEoCzI6LmNhcmJvbi52ZXNlbGthLnByZWRpY3Rpb25fcG9pbnQuQ2F0ZWdvcnlDb2xsZWN0aW9uUHJvZmlsZVIZY2F0ZWdvcnlDb2xsZWN0aW9uUHJvZmlsZRIdCgpzZXNzaW9uX2lkGAQgASgJUglzZXNzaW9uSWQ=');
@$core.Deprecated('Use sessionStatusDescriptor instead')
const SessionStatus$json = const {
  '1': 'SessionStatus',
  '2': const [
    const {'1': 'count_of_results', '3': 1, '4': 1, '5': 3, '10': 'countOfResults'},
    const {'1': 'expected_count', '3': 2, '4': 1, '5': 3, '10': 'expectedCount'},
    const {'1': 'session_status', '3': 3, '4': 1, '5': 14, '6': '.carbon.veselka.prediction_point.SessionActivityState', '10': 'sessionStatus'},
  ],
};

/// Descriptor for `SessionStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sessionStatusDescriptor = $convert.base64Decode('Cg1TZXNzaW9uU3RhdHVzEigKEGNvdW50X29mX3Jlc3VsdHMYASABKANSDmNvdW50T2ZSZXN1bHRzEiUKDmV4cGVjdGVkX2NvdW50GAIgASgDUg1leHBlY3RlZENvdW50ElwKDnNlc3Npb25fc3RhdHVzGAMgASgOMjUuY2FyYm9uLnZlc2Vsa2EucHJlZGljdGlvbl9wb2ludC5TZXNzaW9uQWN0aXZpdHlTdGF0ZVINc2Vzc2lvblN0YXR1cw==');
@$core.Deprecated('Use getPredictionPointSessionResponseDescriptor instead')
const GetPredictionPointSessionResponse$json = const {
  '1': 'GetPredictionPointSessionResponse',
  '2': const [
    const {'1': 'session_info', '3': 1, '4': 1, '5': 11, '6': '.carbon.veselka.prediction_point.SessionInfo', '10': 'sessionInfo'},
    const {'1': 'session_status', '3': 2, '4': 1, '5': 11, '6': '.carbon.veselka.prediction_point.SessionStatus', '10': 'sessionStatus'},
  ],
};

/// Descriptor for `GetPredictionPointSessionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictionPointSessionResponseDescriptor = $convert.base64Decode('CiFHZXRQcmVkaWN0aW9uUG9pbnRTZXNzaW9uUmVzcG9uc2USTwoMc2Vzc2lvbl9pbmZvGAEgASgLMiwuY2FyYm9uLnZlc2Vsa2EucHJlZGljdGlvbl9wb2ludC5TZXNzaW9uSW5mb1ILc2Vzc2lvbkluZm8SVQoOc2Vzc2lvbl9zdGF0dXMYAiABKAsyLi5jYXJib24udmVzZWxrYS5wcmVkaWN0aW9uX3BvaW50LlNlc3Npb25TdGF0dXNSDXNlc3Npb25TdGF0dXM=');
