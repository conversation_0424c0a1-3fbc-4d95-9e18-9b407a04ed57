///
//  Generated code. Do not modify.
//  source: veselka/cv/veselka_cv.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'veselka_cv.pb.dart' as $60;
export 'veselka_cv.pb.dart';

class DLPredictClient extends $grpc.Client {
  static final _$dLPrediction =
      $grpc.ClientMethod<$60.DLPredictionRequest, $60.DLPredictionResponse>(
          '/DLPredict/DLPrediction',
          ($60.DLPredictionRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $60.DLPredictionResponse.fromBuffer(value));

  DLPredictClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$60.DLPredictionResponse> dLPrediction(
      $60.DLPredictionRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$dLPrediction, request, options: options);
  }
}

abstract class DLPredictServiceBase extends $grpc.Service {
  $core.String get $name => 'DLPredict';

  DLPredictServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$60.DLPredictionRequest, $60.DLPredictionResponse>(
            'DLPrediction',
            dLPrediction_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $60.DLPredictionRequest.fromBuffer(value),
            ($60.DLPredictionResponse value) => value.writeToBuffer()));
  }

  $async.Future<$60.DLPredictionResponse> dLPrediction_Pre(
      $grpc.ServiceCall call,
      $async.Future<$60.DLPredictionRequest> request) async {
    return dLPrediction(call, await request);
  }

  $async.Future<$60.DLPredictionResponse> dLPrediction(
      $grpc.ServiceCall call, $60.DLPredictionRequest request);
}
