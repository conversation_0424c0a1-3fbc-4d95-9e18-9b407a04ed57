///
//  Generated code. Do not modify.
//  source: veselka/prediction_point.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'pagination.pb.dart' as $89;

import 'prediction_point.pbenum.dart';

export 'prediction_point.pbenum.dart';

class Image extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Image', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'url')
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ppcm', $pb.PbFieldType.OF)
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'capturedAt')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uploadedByOperator')
    ..hasRequiredFields = false
  ;

  Image._() : super();
  factory Image({
    $core.String? id,
    $core.String? url,
    $core.double? ppcm,
    $fixnum.Int64? capturedAt,
    $core.bool? uploadedByOperator,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (url != null) {
      _result.url = url;
    }
    if (ppcm != null) {
      _result.ppcm = ppcm;
    }
    if (capturedAt != null) {
      _result.capturedAt = capturedAt;
    }
    if (uploadedByOperator != null) {
      _result.uploadedByOperator = uploadedByOperator;
    }
    return _result;
  }
  factory Image.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Image.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Image clone() => Image()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Image copyWith(void Function(Image) updates) => super.copyWith((message) => updates(message as Image)) as Image; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Image create() => Image._();
  Image createEmptyInstance() => create();
  static $pb.PbList<Image> createRepeated() => $pb.PbList<Image>();
  @$core.pragma('dart2js:noInline')
  static Image getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Image>(create);
  static Image? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get url => $_getSZ(1);
  @$pb.TagNumber(2)
  set url($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUrl() => $_has(1);
  @$pb.TagNumber(2)
  void clearUrl() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get ppcm => $_getN(2);
  @$pb.TagNumber(3)
  set ppcm($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPpcm() => $_has(2);
  @$pb.TagNumber(3)
  void clearPpcm() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get capturedAt => $_getI64(3);
  @$pb.TagNumber(4)
  set capturedAt($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCapturedAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearCapturedAt() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get uploadedByOperator => $_getBF(4);
  @$pb.TagNumber(5)
  set uploadedByOperator($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUploadedByOperator() => $_has(4);
  @$pb.TagNumber(5)
  void clearUploadedByOperator() => clearField(5);
}

class PredictionPoint extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictionPoint', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'radius', $pb.PbFieldType.OF)
    ..aOM<Image>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'image', subBuilder: Image.create)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryId')
    ..hasRequiredFields = false
  ;

  PredictionPoint._() : super();
  factory PredictionPoint({
    $core.String? id,
    $core.double? x,
    $core.double? y,
    $core.double? radius,
    Image? image,
    $core.String? categoryId,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    if (radius != null) {
      _result.radius = radius;
    }
    if (image != null) {
      _result.image = image;
    }
    if (categoryId != null) {
      _result.categoryId = categoryId;
    }
    return _result;
  }
  factory PredictionPoint.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictionPoint.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictionPoint clone() => PredictionPoint()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictionPoint copyWith(void Function(PredictionPoint) updates) => super.copyWith((message) => updates(message as PredictionPoint)) as PredictionPoint; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictionPoint create() => PredictionPoint._();
  PredictionPoint createEmptyInstance() => create();
  static $pb.PbList<PredictionPoint> createRepeated() => $pb.PbList<PredictionPoint>();
  @$core.pragma('dart2js:noInline')
  static PredictionPoint getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictionPoint>(create);
  static PredictionPoint? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get x => $_getN(1);
  @$pb.TagNumber(2)
  set x($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasX() => $_has(1);
  @$pb.TagNumber(2)
  void clearX() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get y => $_getN(2);
  @$pb.TagNumber(3)
  set y($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasY() => $_has(2);
  @$pb.TagNumber(3)
  void clearY() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get radius => $_getN(3);
  @$pb.TagNumber(4)
  set radius($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRadius() => $_has(3);
  @$pb.TagNumber(4)
  void clearRadius() => clearField(4);

  @$pb.TagNumber(5)
  Image get image => $_getN(4);
  @$pb.TagNumber(5)
  set image(Image v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasImage() => $_has(4);
  @$pb.TagNumber(5)
  void clearImage() => clearField(5);
  @$pb.TagNumber(5)
  Image ensureImage() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.String get categoryId => $_getSZ(5);
  @$pb.TagNumber(6)
  set categoryId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCategoryId() => $_has(5);
  @$pb.TagNumber(6)
  void clearCategoryId() => clearField(6);
}

class ListPredictionPointsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListPredictionPointsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..pc<PredictionPoint>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'data', $pb.PbFieldType.PM, subBuilder: PredictionPoint.create)
    ..aOM<$89.Pagination>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pagination', subBuilder: $89.Pagination.create)
    ..hasRequiredFields = false
  ;

  ListPredictionPointsResponse._() : super();
  factory ListPredictionPointsResponse({
    $core.Iterable<PredictionPoint>? data,
    $89.Pagination? pagination,
  }) {
    final _result = create();
    if (data != null) {
      _result.data.addAll(data);
    }
    if (pagination != null) {
      _result.pagination = pagination;
    }
    return _result;
  }
  factory ListPredictionPointsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListPredictionPointsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListPredictionPointsResponse clone() => ListPredictionPointsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListPredictionPointsResponse copyWith(void Function(ListPredictionPointsResponse) updates) => super.copyWith((message) => updates(message as ListPredictionPointsResponse)) as ListPredictionPointsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListPredictionPointsResponse create() => ListPredictionPointsResponse._();
  ListPredictionPointsResponse createEmptyInstance() => create();
  static $pb.PbList<ListPredictionPointsResponse> createRepeated() => $pb.PbList<ListPredictionPointsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListPredictionPointsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListPredictionPointsResponse>(create);
  static ListPredictionPointsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<PredictionPoint> get data => $_getList(0);

  @$pb.TagNumber(2)
  $89.Pagination get pagination => $_getN(1);
  @$pb.TagNumber(2)
  set pagination($89.Pagination v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasPagination() => $_has(1);
  @$pb.TagNumber(2)
  void clearPagination() => clearField(2);
  @$pb.TagNumber(2)
  $89.Pagination ensurePagination() => $_ensure(1);
}

class CategoryProfile extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryProfile', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictionPointIds')
    ..hasRequiredFields = false
  ;

  CategoryProfile._() : super();
  factory CategoryProfile({
    $core.String? id,
    $core.Iterable<$core.String>? predictionPointIds,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (predictionPointIds != null) {
      _result.predictionPointIds.addAll(predictionPointIds);
    }
    return _result;
  }
  factory CategoryProfile.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryProfile.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryProfile clone() => CategoryProfile()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryProfile copyWith(void Function(CategoryProfile) updates) => super.copyWith((message) => updates(message as CategoryProfile)) as CategoryProfile; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryProfile create() => CategoryProfile._();
  CategoryProfile createEmptyInstance() => create();
  static $pb.PbList<CategoryProfile> createRepeated() => $pb.PbList<CategoryProfile>();
  @$core.pragma('dart2js:noInline')
  static CategoryProfile getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryProfile>(create);
  static CategoryProfile? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get predictionPointIds => $_getList(1);
}

class CategoryCollectionProfile extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryCollectionProfile', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..pc<CategoryProfile>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryProfiles', $pb.PbFieldType.PM, subBuilder: CategoryProfile.create)
    ..hasRequiredFields = false
  ;

  CategoryCollectionProfile._() : super();
  factory CategoryCollectionProfile({
    $core.String? id,
    $core.Iterable<CategoryProfile>? categoryProfiles,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (categoryProfiles != null) {
      _result.categoryProfiles.addAll(categoryProfiles);
    }
    return _result;
  }
  factory CategoryCollectionProfile.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryCollectionProfile.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryCollectionProfile clone() => CategoryCollectionProfile()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryCollectionProfile copyWith(void Function(CategoryCollectionProfile) updates) => super.copyWith((message) => updates(message as CategoryCollectionProfile)) as CategoryCollectionProfile; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryCollectionProfile create() => CategoryCollectionProfile._();
  CategoryCollectionProfile createEmptyInstance() => create();
  static $pb.PbList<CategoryCollectionProfile> createRepeated() => $pb.PbList<CategoryCollectionProfile>();
  @$core.pragma('dart2js:noInline')
  static CategoryCollectionProfile getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryCollectionProfile>(create);
  static CategoryCollectionProfile? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<CategoryProfile> get categoryProfiles => $_getList(1);
}

class CreatePredictionPointSessionRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreatePredictionPointSessionRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotIds')
    ..aOM<CategoryCollectionProfile>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollectionProfile', subBuilder: CategoryCollectionProfile.create)
    ..hasRequiredFields = false
  ;

  CreatePredictionPointSessionRequest._() : super();
  factory CreatePredictionPointSessionRequest({
    $core.String? modelId,
    $core.Iterable<$core.String>? robotIds,
    CategoryCollectionProfile? categoryCollectionProfile,
  }) {
    final _result = create();
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (robotIds != null) {
      _result.robotIds.addAll(robotIds);
    }
    if (categoryCollectionProfile != null) {
      _result.categoryCollectionProfile = categoryCollectionProfile;
    }
    return _result;
  }
  factory CreatePredictionPointSessionRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreatePredictionPointSessionRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreatePredictionPointSessionRequest clone() => CreatePredictionPointSessionRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreatePredictionPointSessionRequest copyWith(void Function(CreatePredictionPointSessionRequest) updates) => super.copyWith((message) => updates(message as CreatePredictionPointSessionRequest)) as CreatePredictionPointSessionRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreatePredictionPointSessionRequest create() => CreatePredictionPointSessionRequest._();
  CreatePredictionPointSessionRequest createEmptyInstance() => create();
  static $pb.PbList<CreatePredictionPointSessionRequest> createRepeated() => $pb.PbList<CreatePredictionPointSessionRequest>();
  @$core.pragma('dart2js:noInline')
  static CreatePredictionPointSessionRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreatePredictionPointSessionRequest>(create);
  static CreatePredictionPointSessionRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get modelId => $_getSZ(0);
  @$pb.TagNumber(1)
  set modelId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get robotIds => $_getList(1);

  @$pb.TagNumber(3)
  CategoryCollectionProfile get categoryCollectionProfile => $_getN(2);
  @$pb.TagNumber(3)
  set categoryCollectionProfile(CategoryCollectionProfile v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasCategoryCollectionProfile() => $_has(2);
  @$pb.TagNumber(3)
  void clearCategoryCollectionProfile() => clearField(3);
  @$pb.TagNumber(3)
  CategoryCollectionProfile ensureCategoryCollectionProfile() => $_ensure(2);
}

class CreatePredictionPointSessionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreatePredictionPointSessionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionId')
    ..e<SessionActivityState>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: SessionActivityState.SESSION_ACTIVITY_STATE_UNSPECIFIED, valueOf: SessionActivityState.valueOf, enumValues: SessionActivityState.values)
    ..hasRequiredFields = false
  ;

  CreatePredictionPointSessionResponse._() : super();
  factory CreatePredictionPointSessionResponse({
    $core.String? sessionId,
    SessionActivityState? status,
  }) {
    final _result = create();
    if (sessionId != null) {
      _result.sessionId = sessionId;
    }
    if (status != null) {
      _result.status = status;
    }
    return _result;
  }
  factory CreatePredictionPointSessionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreatePredictionPointSessionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreatePredictionPointSessionResponse clone() => CreatePredictionPointSessionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreatePredictionPointSessionResponse copyWith(void Function(CreatePredictionPointSessionResponse) updates) => super.copyWith((message) => updates(message as CreatePredictionPointSessionResponse)) as CreatePredictionPointSessionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreatePredictionPointSessionResponse create() => CreatePredictionPointSessionResponse._();
  CreatePredictionPointSessionResponse createEmptyInstance() => create();
  static $pb.PbList<CreatePredictionPointSessionResponse> createRepeated() => $pb.PbList<CreatePredictionPointSessionResponse>();
  @$core.pragma('dart2js:noInline')
  static CreatePredictionPointSessionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreatePredictionPointSessionResponse>(create);
  static CreatePredictionPointSessionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get sessionId => $_getSZ(0);
  @$pb.TagNumber(1)
  set sessionId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSessionId() => $_has(0);
  @$pb.TagNumber(1)
  void clearSessionId() => clearField(1);

  @$pb.TagNumber(2)
  SessionActivityState get status => $_getN(1);
  @$pb.TagNumber(2)
  set status(SessionActivityState v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatus() => clearField(2);
}

class SessionInfo extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SessionInfo', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotIds')
    ..aOM<CategoryCollectionProfile>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollectionProfile', subBuilder: CategoryCollectionProfile.create)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionId')
    ..hasRequiredFields = false
  ;

  SessionInfo._() : super();
  factory SessionInfo({
    $core.String? modelId,
    $core.Iterable<$core.String>? robotIds,
    CategoryCollectionProfile? categoryCollectionProfile,
    $core.String? sessionId,
  }) {
    final _result = create();
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (robotIds != null) {
      _result.robotIds.addAll(robotIds);
    }
    if (categoryCollectionProfile != null) {
      _result.categoryCollectionProfile = categoryCollectionProfile;
    }
    if (sessionId != null) {
      _result.sessionId = sessionId;
    }
    return _result;
  }
  factory SessionInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SessionInfo clone() => SessionInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SessionInfo copyWith(void Function(SessionInfo) updates) => super.copyWith((message) => updates(message as SessionInfo)) as SessionInfo; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SessionInfo create() => SessionInfo._();
  SessionInfo createEmptyInstance() => create();
  static $pb.PbList<SessionInfo> createRepeated() => $pb.PbList<SessionInfo>();
  @$core.pragma('dart2js:noInline')
  static SessionInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionInfo>(create);
  static SessionInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get modelId => $_getSZ(0);
  @$pb.TagNumber(1)
  set modelId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get robotIds => $_getList(1);

  @$pb.TagNumber(3)
  CategoryCollectionProfile get categoryCollectionProfile => $_getN(2);
  @$pb.TagNumber(3)
  set categoryCollectionProfile(CategoryCollectionProfile v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasCategoryCollectionProfile() => $_has(2);
  @$pb.TagNumber(3)
  void clearCategoryCollectionProfile() => clearField(3);
  @$pb.TagNumber(3)
  CategoryCollectionProfile ensureCategoryCollectionProfile() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.String get sessionId => $_getSZ(3);
  @$pb.TagNumber(4)
  set sessionId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSessionId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSessionId() => clearField(4);
}

class SessionStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SessionStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'countOfResults')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedCount')
    ..e<SessionActivityState>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionStatus', $pb.PbFieldType.OE, defaultOrMaker: SessionActivityState.SESSION_ACTIVITY_STATE_UNSPECIFIED, valueOf: SessionActivityState.valueOf, enumValues: SessionActivityState.values)
    ..hasRequiredFields = false
  ;

  SessionStatus._() : super();
  factory SessionStatus({
    $fixnum.Int64? countOfResults,
    $fixnum.Int64? expectedCount,
    SessionActivityState? sessionStatus,
  }) {
    final _result = create();
    if (countOfResults != null) {
      _result.countOfResults = countOfResults;
    }
    if (expectedCount != null) {
      _result.expectedCount = expectedCount;
    }
    if (sessionStatus != null) {
      _result.sessionStatus = sessionStatus;
    }
    return _result;
  }
  factory SessionStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SessionStatus clone() => SessionStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SessionStatus copyWith(void Function(SessionStatus) updates) => super.copyWith((message) => updates(message as SessionStatus)) as SessionStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SessionStatus create() => SessionStatus._();
  SessionStatus createEmptyInstance() => create();
  static $pb.PbList<SessionStatus> createRepeated() => $pb.PbList<SessionStatus>();
  @$core.pragma('dart2js:noInline')
  static SessionStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionStatus>(create);
  static SessionStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get countOfResults => $_getI64(0);
  @$pb.TagNumber(1)
  set countOfResults($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCountOfResults() => $_has(0);
  @$pb.TagNumber(1)
  void clearCountOfResults() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get expectedCount => $_getI64(1);
  @$pb.TagNumber(2)
  set expectedCount($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasExpectedCount() => $_has(1);
  @$pb.TagNumber(2)
  void clearExpectedCount() => clearField(2);

  @$pb.TagNumber(3)
  SessionActivityState get sessionStatus => $_getN(2);
  @$pb.TagNumber(3)
  set sessionStatus(SessionActivityState v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasSessionStatus() => $_has(2);
  @$pb.TagNumber(3)
  void clearSessionStatus() => clearField(3);
}

class GetPredictionPointSessionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictionPointSessionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.veselka.prediction_point'), createEmptyInstance: create)
    ..aOM<SessionInfo>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionInfo', subBuilder: SessionInfo.create)
    ..aOM<SessionStatus>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionStatus', subBuilder: SessionStatus.create)
    ..hasRequiredFields = false
  ;

  GetPredictionPointSessionResponse._() : super();
  factory GetPredictionPointSessionResponse({
    SessionInfo? sessionInfo,
    SessionStatus? sessionStatus,
  }) {
    final _result = create();
    if (sessionInfo != null) {
      _result.sessionInfo = sessionInfo;
    }
    if (sessionStatus != null) {
      _result.sessionStatus = sessionStatus;
    }
    return _result;
  }
  factory GetPredictionPointSessionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictionPointSessionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictionPointSessionResponse clone() => GetPredictionPointSessionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictionPointSessionResponse copyWith(void Function(GetPredictionPointSessionResponse) updates) => super.copyWith((message) => updates(message as GetPredictionPointSessionResponse)) as GetPredictionPointSessionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictionPointSessionResponse create() => GetPredictionPointSessionResponse._();
  GetPredictionPointSessionResponse createEmptyInstance() => create();
  static $pb.PbList<GetPredictionPointSessionResponse> createRepeated() => $pb.PbList<GetPredictionPointSessionResponse>();
  @$core.pragma('dart2js:noInline')
  static GetPredictionPointSessionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictionPointSessionResponse>(create);
  static GetPredictionPointSessionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  SessionInfo get sessionInfo => $_getN(0);
  @$pb.TagNumber(1)
  set sessionInfo(SessionInfo v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSessionInfo() => $_has(0);
  @$pb.TagNumber(1)
  void clearSessionInfo() => clearField(1);
  @$pb.TagNumber(1)
  SessionInfo ensureSessionInfo() => $_ensure(0);

  @$pb.TagNumber(2)
  SessionStatus get sessionStatus => $_getN(1);
  @$pb.TagNumber(2)
  set sessionStatus(SessionStatus v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasSessionStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearSessionStatus() => clearField(2);
  @$pb.TagNumber(2)
  SessionStatus ensureSessionStatus() => $_ensure(1);
}

