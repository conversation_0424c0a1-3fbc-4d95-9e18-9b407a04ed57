///
//  Generated code. Do not modify.
//  source: robot_syncer/profile_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'profile_sync.pb.dart' as $53;
import '../util/util.pb.dart' as $1;
export 'profile_sync.pb.dart';

class RoSyProfileSyncServiceClient extends $grpc.Client {
  static final _$getProfileSyncData = $grpc.ClientMethod<
          $53.GetProfileSyncDataRequest, $53.GetProfileSyncDataResponse>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
      ($53.GetProfileSyncDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $53.GetProfileSyncDataResponse.fromBuffer(value));
  static final _$uploadProfile = $grpc.ClientMethod<$53.UploadProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
      ($53.UploadProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getProfile =
      $grpc.ClientMethod<$53.GetProfileRequest, $53.GetProfileResponse>(
          '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
          ($53.GetProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $53.GetProfileResponse.fromBuffer(value));
  static final _$deleteProfile = $grpc.ClientMethod<$53.DeleteProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
      ($53.DeleteProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$purgeProfile = $grpc.ClientMethod<$53.PurgeProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
      ($53.PurgeProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  RoSyProfileSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$53.GetProfileSyncDataResponse> getProfileSyncData(
      $53.GetProfileSyncDataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfileSyncData, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadProfile($53.UploadProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadProfile, request, options: options);
  }

  $grpc.ResponseFuture<$53.GetProfileResponse> getProfile(
      $53.GetProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteProfile($53.DeleteProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeProfile($53.PurgeProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeProfile, request, options: options);
  }
}

abstract class RoSyProfileSyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.robot_syncer.profile_sync.RoSyProfileSyncService';

  RoSyProfileSyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$53.GetProfileSyncDataRequest,
            $53.GetProfileSyncDataResponse>(
        'GetProfileSyncData',
        getProfileSyncData_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $53.GetProfileSyncDataRequest.fromBuffer(value),
        ($53.GetProfileSyncDataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$53.UploadProfileRequest, $1.Empty>(
        'UploadProfile',
        uploadProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $53.UploadProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$53.GetProfileRequest, $53.GetProfileResponse>(
            'GetProfile',
            getProfile_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $53.GetProfileRequest.fromBuffer(value),
            ($53.GetProfileResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$53.DeleteProfileRequest, $1.Empty>(
        'DeleteProfile',
        deleteProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $53.DeleteProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$53.PurgeProfileRequest, $1.Empty>(
        'PurgeProfile',
        purgeProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $53.PurgeProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$53.GetProfileSyncDataResponse> getProfileSyncData_Pre(
      $grpc.ServiceCall call,
      $async.Future<$53.GetProfileSyncDataRequest> request) async {
    return getProfileSyncData(call, await request);
  }

  $async.Future<$1.Empty> uploadProfile_Pre($grpc.ServiceCall call,
      $async.Future<$53.UploadProfileRequest> request) async {
    return uploadProfile(call, await request);
  }

  $async.Future<$53.GetProfileResponse> getProfile_Pre($grpc.ServiceCall call,
      $async.Future<$53.GetProfileRequest> request) async {
    return getProfile(call, await request);
  }

  $async.Future<$1.Empty> deleteProfile_Pre($grpc.ServiceCall call,
      $async.Future<$53.DeleteProfileRequest> request) async {
    return deleteProfile(call, await request);
  }

  $async.Future<$1.Empty> purgeProfile_Pre($grpc.ServiceCall call,
      $async.Future<$53.PurgeProfileRequest> request) async {
    return purgeProfile(call, await request);
  }

  $async.Future<$53.GetProfileSyncDataResponse> getProfileSyncData(
      $grpc.ServiceCall call, $53.GetProfileSyncDataRequest request);
  $async.Future<$1.Empty> uploadProfile(
      $grpc.ServiceCall call, $53.UploadProfileRequest request);
  $async.Future<$53.GetProfileResponse> getProfile(
      $grpc.ServiceCall call, $53.GetProfileRequest request);
  $async.Future<$1.Empty> deleteProfile(
      $grpc.ServiceCall call, $53.DeleteProfileRequest request);
  $async.Future<$1.Empty> purgeProfile(
      $grpc.ServiceCall call, $53.PurgeProfileRequest request);
}
