///
//  Generated code. Do not modify.
//  source: robot_syncer/audit.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/timestamp.pb.dart' as $71;
import '../config/api/config_service.pb.dart' as $2;

class ConfigAuditLog extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ConfigAuditLog', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.audit'), createEmptyInstance: create)
    ..aOM<$71.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'createTime', subBuilder: $71.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'method')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'key')
    ..aOM<$2.ConfigValue>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'oldValue', subBuilder: $2.ConfigValue.create)
    ..aOM<$2.ConfigValue>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'newValue', subBuilder: $2.ConfigValue.create)
    ..hasRequiredFields = false
  ;

  ConfigAuditLog._() : super();
  factory ConfigAuditLog({
    $71.Timestamp? createTime,
    $core.String? userId,
    $core.String? method,
    $core.String? serial,
    $core.String? key,
    $2.ConfigValue? oldValue,
    $2.ConfigValue? newValue,
  }) {
    final _result = create();
    if (createTime != null) {
      _result.createTime = createTime;
    }
    if (userId != null) {
      _result.userId = userId;
    }
    if (method != null) {
      _result.method = method;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    if (key != null) {
      _result.key = key;
    }
    if (oldValue != null) {
      _result.oldValue = oldValue;
    }
    if (newValue != null) {
      _result.newValue = newValue;
    }
    return _result;
  }
  factory ConfigAuditLog.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ConfigAuditLog.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ConfigAuditLog clone() => ConfigAuditLog()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ConfigAuditLog copyWith(void Function(ConfigAuditLog) updates) => super.copyWith((message) => updates(message as ConfigAuditLog)) as ConfigAuditLog; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ConfigAuditLog create() => ConfigAuditLog._();
  ConfigAuditLog createEmptyInstance() => create();
  static $pb.PbList<ConfigAuditLog> createRepeated() => $pb.PbList<ConfigAuditLog>();
  @$core.pragma('dart2js:noInline')
  static ConfigAuditLog getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ConfigAuditLog>(create);
  static ConfigAuditLog? _defaultInstance;

  @$pb.TagNumber(1)
  $71.Timestamp get createTime => $_getN(0);
  @$pb.TagNumber(1)
  set createTime($71.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCreateTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearCreateTime() => clearField(1);
  @$pb.TagNumber(1)
  $71.Timestamp ensureCreateTime() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get userId => $_getSZ(1);
  @$pb.TagNumber(2)
  set userId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserId() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get method => $_getSZ(2);
  @$pb.TagNumber(3)
  set method($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMethod() => $_has(2);
  @$pb.TagNumber(3)
  void clearMethod() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get serial => $_getSZ(3);
  @$pb.TagNumber(4)
  set serial($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSerial() => $_has(3);
  @$pb.TagNumber(4)
  void clearSerial() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get key => $_getSZ(4);
  @$pb.TagNumber(5)
  set key($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasKey() => $_has(4);
  @$pb.TagNumber(5)
  void clearKey() => clearField(5);

  @$pb.TagNumber(6)
  $2.ConfigValue get oldValue => $_getN(5);
  @$pb.TagNumber(6)
  set oldValue($2.ConfigValue v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasOldValue() => $_has(5);
  @$pb.TagNumber(6)
  void clearOldValue() => clearField(6);
  @$pb.TagNumber(6)
  $2.ConfigValue ensureOldValue() => $_ensure(5);

  @$pb.TagNumber(7)
  $2.ConfigValue get newValue => $_getN(6);
  @$pb.TagNumber(7)
  set newValue($2.ConfigValue v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasNewValue() => $_has(6);
  @$pb.TagNumber(7)
  void clearNewValue() => clearField(7);
  @$pb.TagNumber(7)
  $2.ConfigValue ensureNewValue() => $_ensure(6);
}

class GetConfigAuditLogsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConfigAuditLogsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.audit'), createEmptyInstance: create)
    ..pc<ConfigAuditLog>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'auditLogs', $pb.PbFieldType.PM, subBuilder: ConfigAuditLog.create)
    ..hasRequiredFields = false
  ;

  GetConfigAuditLogsResponse._() : super();
  factory GetConfigAuditLogsResponse({
    $core.Iterable<ConfigAuditLog>? auditLogs,
  }) {
    final _result = create();
    if (auditLogs != null) {
      _result.auditLogs.addAll(auditLogs);
    }
    return _result;
  }
  factory GetConfigAuditLogsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConfigAuditLogsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConfigAuditLogsResponse clone() => GetConfigAuditLogsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConfigAuditLogsResponse copyWith(void Function(GetConfigAuditLogsResponse) updates) => super.copyWith((message) => updates(message as GetConfigAuditLogsResponse)) as GetConfigAuditLogsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConfigAuditLogsResponse create() => GetConfigAuditLogsResponse._();
  GetConfigAuditLogsResponse createEmptyInstance() => create();
  static $pb.PbList<GetConfigAuditLogsResponse> createRepeated() => $pb.PbList<GetConfigAuditLogsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetConfigAuditLogsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConfigAuditLogsResponse>(create);
  static GetConfigAuditLogsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ConfigAuditLog> get auditLogs => $_getList(0);
}

