///
//  Generated code. Do not modify.
//  source: robot_syncer/profile_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../frontend/profile_sync.pb.dart' as $68;
import '../almanac/almanac.pb.dart' as $64;
import '../frontend/banding.pb.dart' as $10;
import '../thinning/thinning.pb.dart' as $66;
import '../target_velocity_estimator/target_velocity_estimator.pb.dart' as $70;
import '../category_profile/category_profile.pb.dart' as $65;

class GetProfileSyncDataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetProfileSyncDataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  GetProfileSyncDataRequest._() : super();
  factory GetProfileSyncDataRequest({
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory GetProfileSyncDataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetProfileSyncDataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetProfileSyncDataRequest clone() => GetProfileSyncDataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetProfileSyncDataRequest copyWith(void Function(GetProfileSyncDataRequest) updates) => super.copyWith((message) => updates(message as GetProfileSyncDataRequest)) as GetProfileSyncDataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetProfileSyncDataRequest create() => GetProfileSyncDataRequest._();
  GetProfileSyncDataRequest createEmptyInstance() => create();
  static $pb.PbList<GetProfileSyncDataRequest> createRepeated() => $pb.PbList<GetProfileSyncDataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetProfileSyncDataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetProfileSyncDataRequest>(create);
  static GetProfileSyncDataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);
}

class GetProfileSyncDataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetProfileSyncDataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..m<$core.String, $68.ProfileSyncData>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profiles', entryClassName: 'GetProfileSyncDataResponse.ProfilesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: $68.ProfileSyncData.create, packageName: const $pb.PackageName('carbon.robot_syncer.profile_sync'))
    ..hasRequiredFields = false
  ;

  GetProfileSyncDataResponse._() : super();
  factory GetProfileSyncDataResponse({
    $core.Map<$core.String, $68.ProfileSyncData>? profiles,
  }) {
    final _result = create();
    if (profiles != null) {
      _result.profiles.addAll(profiles);
    }
    return _result;
  }
  factory GetProfileSyncDataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetProfileSyncDataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetProfileSyncDataResponse clone() => GetProfileSyncDataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetProfileSyncDataResponse copyWith(void Function(GetProfileSyncDataResponse) updates) => super.copyWith((message) => updates(message as GetProfileSyncDataResponse)) as GetProfileSyncDataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetProfileSyncDataResponse create() => GetProfileSyncDataResponse._();
  GetProfileSyncDataResponse createEmptyInstance() => create();
  static $pb.PbList<GetProfileSyncDataResponse> createRepeated() => $pb.PbList<GetProfileSyncDataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetProfileSyncDataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetProfileSyncDataResponse>(create);
  static GetProfileSyncDataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, $68.ProfileSyncData> get profiles => $_getMap(0);
}

enum UploadProfileRequest_Profile {
  almanac, 
  discriminator, 
  modelinator, 
  banding, 
  thinning, 
  targetVelocityEstimator, 
  categoryCollection, 
  category, 
  notSet
}

class UploadProfileRequest extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, UploadProfileRequest_Profile> _UploadProfileRequest_ProfileByTag = {
    3 : UploadProfileRequest_Profile.almanac,
    4 : UploadProfileRequest_Profile.discriminator,
    5 : UploadProfileRequest_Profile.modelinator,
    6 : UploadProfileRequest_Profile.banding,
    7 : UploadProfileRequest_Profile.thinning,
    8 : UploadProfileRequest_Profile.targetVelocityEstimator,
    9 : UploadProfileRequest_Profile.categoryCollection,
    10 : UploadProfileRequest_Profile.category,
    0 : UploadProfileRequest_Profile.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UploadProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..oo(0, [3, 4, 5, 6, 7, 8, 9, 10])
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastUpdateTimeMs')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..aOM<$64.AlmanacConfig>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac', subBuilder: $64.AlmanacConfig.create)
    ..aOM<$64.DiscriminatorConfig>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'discriminator', subBuilder: $64.DiscriminatorConfig.create)
    ..aOM<$64.ModelinatorConfig>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelinator', subBuilder: $64.ModelinatorConfig.create)
    ..aOM<$10.BandingDef>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'banding', subBuilder: $10.BandingDef.create)
    ..aOM<$66.ConfigDefinition>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinning', subBuilder: $66.ConfigDefinition.create)
    ..aOM<$70.TVEProfile>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVelocityEstimator', subBuilder: $70.TVEProfile.create)
    ..aOM<$65.CategoryCollection>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollection', protoName: 'categoryCollection', subBuilder: $65.CategoryCollection.create)
    ..aOM<$65.Category>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category', subBuilder: $65.Category.create)
    ..hasRequiredFields = false
  ;

  UploadProfileRequest._() : super();
  factory UploadProfileRequest({
    $fixnum.Int64? lastUpdateTimeMs,
    $core.String? robotSerial,
    $64.AlmanacConfig? almanac,
    $64.DiscriminatorConfig? discriminator,
    $64.ModelinatorConfig? modelinator,
    $10.BandingDef? banding,
    $66.ConfigDefinition? thinning,
    $70.TVEProfile? targetVelocityEstimator,
    $65.CategoryCollection? categoryCollection,
    $65.Category? category,
  }) {
    final _result = create();
    if (lastUpdateTimeMs != null) {
      _result.lastUpdateTimeMs = lastUpdateTimeMs;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (discriminator != null) {
      _result.discriminator = discriminator;
    }
    if (modelinator != null) {
      _result.modelinator = modelinator;
    }
    if (banding != null) {
      _result.banding = banding;
    }
    if (thinning != null) {
      _result.thinning = thinning;
    }
    if (targetVelocityEstimator != null) {
      _result.targetVelocityEstimator = targetVelocityEstimator;
    }
    if (categoryCollection != null) {
      _result.categoryCollection = categoryCollection;
    }
    if (category != null) {
      _result.category = category;
    }
    return _result;
  }
  factory UploadProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UploadProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UploadProfileRequest clone() => UploadProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UploadProfileRequest copyWith(void Function(UploadProfileRequest) updates) => super.copyWith((message) => updates(message as UploadProfileRequest)) as UploadProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UploadProfileRequest create() => UploadProfileRequest._();
  UploadProfileRequest createEmptyInstance() => create();
  static $pb.PbList<UploadProfileRequest> createRepeated() => $pb.PbList<UploadProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadProfileRequest>(create);
  static UploadProfileRequest? _defaultInstance;

  UploadProfileRequest_Profile whichProfile() => _UploadProfileRequest_ProfileByTag[$_whichOneof(0)]!;
  void clearProfile() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $fixnum.Int64 get lastUpdateTimeMs => $_getI64(0);
  @$pb.TagNumber(1)
  set lastUpdateTimeMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLastUpdateTimeMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearLastUpdateTimeMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get robotSerial => $_getSZ(1);
  @$pb.TagNumber(2)
  set robotSerial($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobotSerial() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobotSerial() => clearField(2);

  @$pb.TagNumber(3)
  $64.AlmanacConfig get almanac => $_getN(2);
  @$pb.TagNumber(3)
  set almanac($64.AlmanacConfig v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlmanac() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlmanac() => clearField(3);
  @$pb.TagNumber(3)
  $64.AlmanacConfig ensureAlmanac() => $_ensure(2);

  @$pb.TagNumber(4)
  $64.DiscriminatorConfig get discriminator => $_getN(3);
  @$pb.TagNumber(4)
  set discriminator($64.DiscriminatorConfig v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDiscriminator() => $_has(3);
  @$pb.TagNumber(4)
  void clearDiscriminator() => clearField(4);
  @$pb.TagNumber(4)
  $64.DiscriminatorConfig ensureDiscriminator() => $_ensure(3);

  @$pb.TagNumber(5)
  $64.ModelinatorConfig get modelinator => $_getN(4);
  @$pb.TagNumber(5)
  set modelinator($64.ModelinatorConfig v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasModelinator() => $_has(4);
  @$pb.TagNumber(5)
  void clearModelinator() => clearField(5);
  @$pb.TagNumber(5)
  $64.ModelinatorConfig ensureModelinator() => $_ensure(4);

  @$pb.TagNumber(6)
  $10.BandingDef get banding => $_getN(5);
  @$pb.TagNumber(6)
  set banding($10.BandingDef v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasBanding() => $_has(5);
  @$pb.TagNumber(6)
  void clearBanding() => clearField(6);
  @$pb.TagNumber(6)
  $10.BandingDef ensureBanding() => $_ensure(5);

  @$pb.TagNumber(7)
  $66.ConfigDefinition get thinning => $_getN(6);
  @$pb.TagNumber(7)
  set thinning($66.ConfigDefinition v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasThinning() => $_has(6);
  @$pb.TagNumber(7)
  void clearThinning() => clearField(7);
  @$pb.TagNumber(7)
  $66.ConfigDefinition ensureThinning() => $_ensure(6);

  @$pb.TagNumber(8)
  $70.TVEProfile get targetVelocityEstimator => $_getN(7);
  @$pb.TagNumber(8)
  set targetVelocityEstimator($70.TVEProfile v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasTargetVelocityEstimator() => $_has(7);
  @$pb.TagNumber(8)
  void clearTargetVelocityEstimator() => clearField(8);
  @$pb.TagNumber(8)
  $70.TVEProfile ensureTargetVelocityEstimator() => $_ensure(7);

  @$pb.TagNumber(9)
  $65.CategoryCollection get categoryCollection => $_getN(8);
  @$pb.TagNumber(9)
  set categoryCollection($65.CategoryCollection v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasCategoryCollection() => $_has(8);
  @$pb.TagNumber(9)
  void clearCategoryCollection() => clearField(9);
  @$pb.TagNumber(9)
  $65.CategoryCollection ensureCategoryCollection() => $_ensure(8);

  @$pb.TagNumber(10)
  $65.Category get category => $_getN(9);
  @$pb.TagNumber(10)
  set category($65.Category v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasCategory() => $_has(9);
  @$pb.TagNumber(10)
  void clearCategory() => clearField(10);
  @$pb.TagNumber(10)
  $65.Category ensureCategory() => $_ensure(9);
}

class GetProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  GetProfileRequest._() : super();
  factory GetProfileRequest({
    $core.String? uuid,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory GetProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetProfileRequest clone() => GetProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetProfileRequest copyWith(void Function(GetProfileRequest) updates) => super.copyWith((message) => updates(message as GetProfileRequest)) as GetProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetProfileRequest create() => GetProfileRequest._();
  GetProfileRequest createEmptyInstance() => create();
  static $pb.PbList<GetProfileRequest> createRepeated() => $pb.PbList<GetProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static GetProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetProfileRequest>(create);
  static GetProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);
}

enum GetProfileResponse_Profile {
  almanac, 
  discriminator, 
  modelinator, 
  banding, 
  thinning, 
  targetVelocityEstimator, 
  categoryCollection, 
  category, 
  notSet
}

class GetProfileResponse extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, GetProfileResponse_Profile> _GetProfileResponse_ProfileByTag = {
    1 : GetProfileResponse_Profile.almanac,
    2 : GetProfileResponse_Profile.discriminator,
    3 : GetProfileResponse_Profile.modelinator,
    4 : GetProfileResponse_Profile.banding,
    5 : GetProfileResponse_Profile.thinning,
    6 : GetProfileResponse_Profile.targetVelocityEstimator,
    7 : GetProfileResponse_Profile.categoryCollection,
    8 : GetProfileResponse_Profile.category,
    0 : GetProfileResponse_Profile.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3, 4, 5, 6, 7, 8])
    ..aOM<$64.AlmanacConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac', subBuilder: $64.AlmanacConfig.create)
    ..aOM<$64.DiscriminatorConfig>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'discriminator', subBuilder: $64.DiscriminatorConfig.create)
    ..aOM<$64.ModelinatorConfig>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelinator', subBuilder: $64.ModelinatorConfig.create)
    ..aOM<$10.BandingDef>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'banding', subBuilder: $10.BandingDef.create)
    ..aOM<$66.ConfigDefinition>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinning', subBuilder: $66.ConfigDefinition.create)
    ..aOM<$70.TVEProfile>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVelocityEstimator', subBuilder: $70.TVEProfile.create)
    ..aOM<$65.CategoryCollection>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollection', protoName: 'categoryCollection', subBuilder: $65.CategoryCollection.create)
    ..aOM<$65.Category>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category', subBuilder: $65.Category.create)
    ..hasRequiredFields = false
  ;

  GetProfileResponse._() : super();
  factory GetProfileResponse({
    $64.AlmanacConfig? almanac,
    $64.DiscriminatorConfig? discriminator,
    $64.ModelinatorConfig? modelinator,
    $10.BandingDef? banding,
    $66.ConfigDefinition? thinning,
    $70.TVEProfile? targetVelocityEstimator,
    $65.CategoryCollection? categoryCollection,
    $65.Category? category,
  }) {
    final _result = create();
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (discriminator != null) {
      _result.discriminator = discriminator;
    }
    if (modelinator != null) {
      _result.modelinator = modelinator;
    }
    if (banding != null) {
      _result.banding = banding;
    }
    if (thinning != null) {
      _result.thinning = thinning;
    }
    if (targetVelocityEstimator != null) {
      _result.targetVelocityEstimator = targetVelocityEstimator;
    }
    if (categoryCollection != null) {
      _result.categoryCollection = categoryCollection;
    }
    if (category != null) {
      _result.category = category;
    }
    return _result;
  }
  factory GetProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetProfileResponse clone() => GetProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetProfileResponse copyWith(void Function(GetProfileResponse) updates) => super.copyWith((message) => updates(message as GetProfileResponse)) as GetProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetProfileResponse create() => GetProfileResponse._();
  GetProfileResponse createEmptyInstance() => create();
  static $pb.PbList<GetProfileResponse> createRepeated() => $pb.PbList<GetProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static GetProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetProfileResponse>(create);
  static GetProfileResponse? _defaultInstance;

  GetProfileResponse_Profile whichProfile() => _GetProfileResponse_ProfileByTag[$_whichOneof(0)]!;
  void clearProfile() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $64.AlmanacConfig get almanac => $_getN(0);
  @$pb.TagNumber(1)
  set almanac($64.AlmanacConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasAlmanac() => $_has(0);
  @$pb.TagNumber(1)
  void clearAlmanac() => clearField(1);
  @$pb.TagNumber(1)
  $64.AlmanacConfig ensureAlmanac() => $_ensure(0);

  @$pb.TagNumber(2)
  $64.DiscriminatorConfig get discriminator => $_getN(1);
  @$pb.TagNumber(2)
  set discriminator($64.DiscriminatorConfig v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasDiscriminator() => $_has(1);
  @$pb.TagNumber(2)
  void clearDiscriminator() => clearField(2);
  @$pb.TagNumber(2)
  $64.DiscriminatorConfig ensureDiscriminator() => $_ensure(1);

  @$pb.TagNumber(3)
  $64.ModelinatorConfig get modelinator => $_getN(2);
  @$pb.TagNumber(3)
  set modelinator($64.ModelinatorConfig v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasModelinator() => $_has(2);
  @$pb.TagNumber(3)
  void clearModelinator() => clearField(3);
  @$pb.TagNumber(3)
  $64.ModelinatorConfig ensureModelinator() => $_ensure(2);

  @$pb.TagNumber(4)
  $10.BandingDef get banding => $_getN(3);
  @$pb.TagNumber(4)
  set banding($10.BandingDef v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasBanding() => $_has(3);
  @$pb.TagNumber(4)
  void clearBanding() => clearField(4);
  @$pb.TagNumber(4)
  $10.BandingDef ensureBanding() => $_ensure(3);

  @$pb.TagNumber(5)
  $66.ConfigDefinition get thinning => $_getN(4);
  @$pb.TagNumber(5)
  set thinning($66.ConfigDefinition v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasThinning() => $_has(4);
  @$pb.TagNumber(5)
  void clearThinning() => clearField(5);
  @$pb.TagNumber(5)
  $66.ConfigDefinition ensureThinning() => $_ensure(4);

  @$pb.TagNumber(6)
  $70.TVEProfile get targetVelocityEstimator => $_getN(5);
  @$pb.TagNumber(6)
  set targetVelocityEstimator($70.TVEProfile v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasTargetVelocityEstimator() => $_has(5);
  @$pb.TagNumber(6)
  void clearTargetVelocityEstimator() => clearField(6);
  @$pb.TagNumber(6)
  $70.TVEProfile ensureTargetVelocityEstimator() => $_ensure(5);

  @$pb.TagNumber(7)
  $65.CategoryCollection get categoryCollection => $_getN(6);
  @$pb.TagNumber(7)
  set categoryCollection($65.CategoryCollection v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasCategoryCollection() => $_has(6);
  @$pb.TagNumber(7)
  void clearCategoryCollection() => clearField(7);
  @$pb.TagNumber(7)
  $65.CategoryCollection ensureCategoryCollection() => $_ensure(6);

  @$pb.TagNumber(8)
  $65.Category get category => $_getN(7);
  @$pb.TagNumber(8)
  set category($65.Category v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasCategory() => $_has(7);
  @$pb.TagNumber(8)
  void clearCategory() => clearField(8);
  @$pb.TagNumber(8)
  $65.Category ensureCategory() => $_ensure(7);
}

class DeleteProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  DeleteProfileRequest._() : super();
  factory DeleteProfileRequest({
    $core.String? uuid,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory DeleteProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteProfileRequest clone() => DeleteProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteProfileRequest copyWith(void Function(DeleteProfileRequest) updates) => super.copyWith((message) => updates(message as DeleteProfileRequest)) as DeleteProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteProfileRequest create() => DeleteProfileRequest._();
  DeleteProfileRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteProfileRequest> createRepeated() => $pb.PbList<DeleteProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteProfileRequest>(create);
  static DeleteProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);
}

class PurgeProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PurgeProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.profile_sync'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  PurgeProfileRequest._() : super();
  factory PurgeProfileRequest({
    $core.String? uuid,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory PurgeProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PurgeProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PurgeProfileRequest clone() => PurgeProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PurgeProfileRequest copyWith(void Function(PurgeProfileRequest) updates) => super.copyWith((message) => updates(message as PurgeProfileRequest)) as PurgeProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PurgeProfileRequest create() => PurgeProfileRequest._();
  PurgeProfileRequest createEmptyInstance() => create();
  static $pb.PbList<PurgeProfileRequest> createRepeated() => $pb.PbList<PurgeProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static PurgeProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PurgeProfileRequest>(create);
  static PurgeProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);
}

