///
//  Generated code. Do not modify.
//  source: robot_syncer/audit.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use configAuditLogDescriptor instead')
const ConfigAuditLog$json = const {
  '1': 'ConfigAuditLog',
  '2': const [
    const {'1': 'create_time', '3': 1, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'createTime'},
    const {'1': 'user_id', '3': 2, '4': 1, '5': 9, '10': 'userId'},
    const {'1': 'method', '3': 3, '4': 1, '5': 9, '10': 'method'},
    const {'1': 'serial', '3': 4, '4': 1, '5': 9, '10': 'serial'},
    const {'1': 'key', '3': 5, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'old_value', '3': 6, '4': 1, '5': 11, '6': '.carbon.config.proto.ConfigValue', '10': 'oldValue'},
    const {'1': 'new_value', '3': 7, '4': 1, '5': 11, '6': '.carbon.config.proto.ConfigValue', '10': 'newValue'},
  ],
};

/// Descriptor for `ConfigAuditLog`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List configAuditLogDescriptor = $convert.base64Decode('Cg5Db25maWdBdWRpdExvZxI7CgtjcmVhdGVfdGltZRgBIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXBSCmNyZWF0ZVRpbWUSFwoHdXNlcl9pZBgCIAEoCVIGdXNlcklkEhYKBm1ldGhvZBgDIAEoCVIGbWV0aG9kEhYKBnNlcmlhbBgEIAEoCVIGc2VyaWFsEhAKA2tleRgFIAEoCVIDa2V5Ej0KCW9sZF92YWx1ZRgGIAEoCzIgLmNhcmJvbi5jb25maWcucHJvdG8uQ29uZmlnVmFsdWVSCG9sZFZhbHVlEj0KCW5ld192YWx1ZRgHIAEoCzIgLmNhcmJvbi5jb25maWcucHJvdG8uQ29uZmlnVmFsdWVSCG5ld1ZhbHVl');
@$core.Deprecated('Use getConfigAuditLogsResponseDescriptor instead')
const GetConfigAuditLogsResponse$json = const {
  '1': 'GetConfigAuditLogsResponse',
  '2': const [
    const {'1': 'audit_logs', '3': 1, '4': 3, '5': 11, '6': '.carbon.robot_syncer.audit.ConfigAuditLog', '10': 'auditLogs'},
  ],
};

/// Descriptor for `GetConfigAuditLogsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getConfigAuditLogsResponseDescriptor = $convert.base64Decode('ChpHZXRDb25maWdBdWRpdExvZ3NSZXNwb25zZRJICgphdWRpdF9sb2dzGAEgAygLMikuY2FyYm9uLnJvYm90X3N5bmNlci5hdWRpdC5Db25maWdBdWRpdExvZ1IJYXVkaXRMb2dz');
