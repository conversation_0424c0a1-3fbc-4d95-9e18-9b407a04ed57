///
//  Generated code. Do not modify.
//  source: cv/runtime/cv_runtime.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use bufferUseCaseDescriptor instead')
const BufferUseCase$json = const {
  '1': 'BufferUseCase',
  '2': const [
    const {'1': 'P2P', '2': 0},
    const {'1': 'OpticalFlow', '2': 1},
    const {'1': 'Predict', '2': 2},
    const {'1': 'Drive', '2': 3},
  ],
};

/// Descriptor for `BufferUseCase`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List bufferUseCaseDescriptor = $convert.base64Decode('Cg1CdWZmZXJVc2VDYXNlEgcKA1AyUBAAEg8KC09wdGljYWxGbG93EAESCwoHUHJlZGljdBACEgkKBURyaXZlEAM=');
@$core.Deprecated('Use hitClassDescriptor instead')
const HitClass$json = const {
  '1': 'HitClass',
  '2': const [
    const {'1': 'WEED', '2': 0},
    const {'1': 'CROP', '2': 1},
    const {'1': 'PLANT', '2': 2},
  ],
};

/// Descriptor for `HitClass`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List hitClassDescriptor = $convert.base64Decode('CghIaXRDbGFzcxIICgRXRUVEEAASCAoEQ1JPUBABEgkKBVBMQU5UEAI=');
@$core.Deprecated('Use scoreQueueTypeDescriptor instead')
const ScoreQueueType$json = const {
  '1': 'ScoreQueueType',
  '2': const [
    const {'1': 'PREDICT', '2': 0},
    const {'1': 'CHIP', '2': 1},
  ],
};

/// Descriptor for `ScoreQueueType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List scoreQueueTypeDescriptor = $convert.base64Decode('Cg5TY29yZVF1ZXVlVHlwZRILCgdQUkVESUNUEAASCAoEQ0hJUBAB');
@$core.Deprecated('Use errorTypeDescriptor instead')
const ErrorType$json = const {
  '1': 'ErrorType',
  '2': const [
    const {'1': 'NONE', '2': 0},
    const {'1': 'GRAB', '2': 1},
    const {'1': 'CONNECTION', '2': 2},
    const {'1': 'NO_IMPLEMENTATION', '2': 3},
    const {'1': 'NO_IMAGE_IN_LAST_MINUTE', '2': 4},
  ],
};

/// Descriptor for `ErrorType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List errorTypeDescriptor = $convert.base64Decode('CglFcnJvclR5cGUSCAoETk9ORRAAEggKBEdSQUIQARIOCgpDT05ORUNUSU9OEAISFQoRTk9fSU1QTEVNRU5UQVRJT04QAxIbChdOT19JTUFHRV9JTl9MQVNUX01JTlVURRAE');
@$core.Deprecated('Use targetSafetyZoneDescriptor instead')
const TargetSafetyZone$json = const {
  '1': 'TargetSafetyZone',
  '2': const [
    const {'1': 'up', '3': 1, '4': 1, '5': 2, '10': 'up'},
    const {'1': 'down', '3': 2, '4': 1, '5': 2, '10': 'down'},
    const {'1': 'left', '3': 3, '4': 1, '5': 2, '10': 'left'},
    const {'1': 'right', '3': 4, '4': 1, '5': 2, '10': 'right'},
  ],
};

/// Descriptor for `TargetSafetyZone`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List targetSafetyZoneDescriptor = $convert.base64Decode('ChBUYXJnZXRTYWZldHlab25lEg4KAnVwGAEgASgCUgJ1cBISCgRkb3duGAIgASgCUgRkb3duEhIKBGxlZnQYAyABKAJSBGxlZnQSFAoFcmlnaHQYBCABKAJSBXJpZ2h0');
@$core.Deprecated('Use p2PContextDescriptor instead')
const P2PContext$json = const {
  '1': 'P2PContext',
  '2': const [
    const {'1': 'predict_cam_id', '3': 1, '4': 1, '5': 9, '10': 'predictCamId'},
    const {'1': 'predict_timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'predictTimestampMs'},
    const {'1': 'predict_coord_x', '3': 3, '4': 1, '5': 2, '10': 'predictCoordX'},
    const {'1': 'predict_coord_y', '3': 4, '4': 1, '5': 2, '10': 'predictCoordY'},
  ],
};

/// Descriptor for `P2PContext`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PContextDescriptor = $convert.base64Decode('CgpQMlBDb250ZXh0EiQKDnByZWRpY3RfY2FtX2lkGAEgASgJUgxwcmVkaWN0Q2FtSWQSMAoUcHJlZGljdF90aW1lc3RhbXBfbXMYAiABKANSEnByZWRpY3RUaW1lc3RhbXBNcxImCg9wcmVkaWN0X2Nvb3JkX3gYAyABKAJSDXByZWRpY3RDb29yZFgSJgoPcHJlZGljdF9jb29yZF95GAQgASgCUg1wcmVkaWN0Q29vcmRZ');
@$core.Deprecated('Use setP2PContextRequestDescriptor instead')
const SetP2PContextRequest$json = const {
  '1': 'SetP2PContextRequest',
  '2': const [
    const {'1': 'target_cam_id', '3': 1, '4': 1, '5': 9, '10': 'targetCamId'},
    const {'1': 'primary_context', '3': 2, '4': 1, '5': 11, '6': '.cv.runtime.proto.P2PContext', '10': 'primaryContext'},
    const {'1': 'secondary_context', '3': 3, '4': 1, '5': 11, '6': '.cv.runtime.proto.P2PContext', '9': 0, '10': 'secondaryContext', '17': true},
    const {'1': 'safety_zone', '3': 4, '4': 1, '5': 11, '6': '.cv.runtime.proto.TargetSafetyZone', '10': 'safetyZone'},
    const {'1': 'target_id', '3': 5, '4': 1, '5': 3, '10': 'targetId'},
  ],
  '8': const [
    const {'1': '_secondary_context'},
  ],
};

/// Descriptor for `SetP2PContextRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setP2PContextRequestDescriptor = $convert.base64Decode('ChRTZXRQMlBDb250ZXh0UmVxdWVzdBIiCg10YXJnZXRfY2FtX2lkGAEgASgJUgt0YXJnZXRDYW1JZBJFCg9wcmltYXJ5X2NvbnRleHQYAiABKAsyHC5jdi5ydW50aW1lLnByb3RvLlAyUENvbnRleHRSDnByaW1hcnlDb250ZXh0Ek4KEXNlY29uZGFyeV9jb250ZXh0GAMgASgLMhwuY3YucnVudGltZS5wcm90by5QMlBDb250ZXh0SABSEHNlY29uZGFyeUNvbnRleHSIAQESQwoLc2FmZXR5X3pvbmUYBCABKAsyIi5jdi5ydW50aW1lLnByb3RvLlRhcmdldFNhZmV0eVpvbmVSCnNhZmV0eVpvbmUSGwoJdGFyZ2V0X2lkGAUgASgDUgh0YXJnZXRJZEIUChJfc2Vjb25kYXJ5X2NvbnRleHQ=');
@$core.Deprecated('Use setP2PContextResponseDescriptor instead')
const SetP2PContextResponse$json = const {
  '1': 'SetP2PContextResponse',
};

/// Descriptor for `SetP2PContextResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setP2PContextResponseDescriptor = $convert.base64Decode('ChVTZXRQMlBDb250ZXh0UmVzcG9uc2U=');
@$core.Deprecated('Use getCameraDimensionsRequestDescriptor instead')
const GetCameraDimensionsRequest$json = const {
  '1': 'GetCameraDimensionsRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `GetCameraDimensionsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraDimensionsRequestDescriptor = $convert.base64Decode('ChpHZXRDYW1lcmFEaW1lbnNpb25zUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlk');
@$core.Deprecated('Use getCameraDimensionsResponseDescriptor instead')
const GetCameraDimensionsResponse$json = const {
  '1': 'GetCameraDimensionsResponse',
  '2': const [
    const {'1': 'width', '3': 1, '4': 1, '5': 3, '10': 'width'},
    const {'1': 'height', '3': 2, '4': 1, '5': 3, '10': 'height'},
    const {'1': 'transpose', '3': 3, '4': 1, '5': 8, '10': 'transpose'},
  ],
};

/// Descriptor for `GetCameraDimensionsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraDimensionsResponseDescriptor = $convert.base64Decode('ChtHZXRDYW1lcmFEaW1lbnNpb25zUmVzcG9uc2USFAoFd2lkdGgYASABKANSBXdpZHRoEhYKBmhlaWdodBgCIAEoA1IGaGVpZ2h0EhwKCXRyYW5zcG9zZRgDIAEoCFIJdHJhbnNwb3Nl');
@$core.Deprecated('Use startP2PDataCaptureRequestDescriptor instead')
const StartP2PDataCaptureRequest$json = const {
  '1': 'StartP2PDataCaptureRequest',
  '2': const [
    const {'1': 'target_cam_id', '3': 1, '4': 1, '5': 9, '10': 'targetCamId'},
    const {'1': 'capture_miss_rate', '3': 2, '4': 1, '5': 2, '10': 'captureMissRate'},
    const {'1': 'capture_success_rate', '3': 3, '4': 1, '5': 2, '10': 'captureSuccessRate'},
    const {'1': 'capture_enabled', '3': 4, '4': 1, '5': 8, '10': 'captureEnabled'},
    const {'1': 'capture_path', '3': 5, '4': 1, '5': 9, '10': 'capturePath'},
    const {'1': 'after_timestamp_ms', '3': 6, '4': 1, '5': 3, '10': 'afterTimestampMs'},
  ],
};

/// Descriptor for `StartP2PDataCaptureRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startP2PDataCaptureRequestDescriptor = $convert.base64Decode('ChpTdGFydFAyUERhdGFDYXB0dXJlUmVxdWVzdBIiCg10YXJnZXRfY2FtX2lkGAEgASgJUgt0YXJnZXRDYW1JZBIqChFjYXB0dXJlX21pc3NfcmF0ZRgCIAEoAlIPY2FwdHVyZU1pc3NSYXRlEjAKFGNhcHR1cmVfc3VjY2Vzc19yYXRlGAMgASgCUhJjYXB0dXJlU3VjY2Vzc1JhdGUSJwoPY2FwdHVyZV9lbmFibGVkGAQgASgIUg5jYXB0dXJlRW5hYmxlZBIhCgxjYXB0dXJlX3BhdGgYBSABKAlSC2NhcHR1cmVQYXRoEiwKEmFmdGVyX3RpbWVzdGFtcF9tcxgGIAEoA1IQYWZ0ZXJUaW1lc3RhbXBNcw==');
@$core.Deprecated('Use pointDetectionCategoryDescriptor instead')
const PointDetectionCategory$json = const {
  '1': 'PointDetectionCategory',
  '2': const [
    const {'1': 'threshold', '3': 1, '4': 1, '5': 2, '10': 'threshold'},
    const {'1': 'category', '3': 2, '4': 1, '5': 9, '10': 'category'},
  ],
};

/// Descriptor for `PointDetectionCategory`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pointDetectionCategoryDescriptor = $convert.base64Decode('ChZQb2ludERldGVjdGlvbkNhdGVnb3J5EhwKCXRocmVzaG9sZBgBIAEoAlIJdGhyZXNob2xkEhoKCGNhdGVnb3J5GAIgASgJUghjYXRlZ29yeQ==');
@$core.Deprecated('Use segmentationDetectionCategoryDescriptor instead')
const SegmentationDetectionCategory$json = const {
  '1': 'SegmentationDetectionCategory',
  '2': const [
    const {'1': 'threshold', '3': 1, '4': 1, '5': 2, '10': 'threshold'},
    const {'1': 'category', '3': 2, '4': 1, '5': 9, '10': 'category'},
    const {'1': 'safety_radius_in', '3': 3, '4': 1, '5': 2, '10': 'safetyRadiusIn'},
  ],
};

/// Descriptor for `SegmentationDetectionCategory`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List segmentationDetectionCategoryDescriptor = $convert.base64Decode('Ch1TZWdtZW50YXRpb25EZXRlY3Rpb25DYXRlZ29yeRIcCgl0aHJlc2hvbGQYASABKAJSCXRocmVzaG9sZBIaCghjYXRlZ29yeRgCIAEoCVIIY2F0ZWdvcnkSKAoQc2FmZXR5X3JhZGl1c19pbhgDIAEoAlIOc2FmZXR5UmFkaXVzSW4=');
@$core.Deprecated('Use deepweedDetectionCriteriaSettingDescriptor instead')
const DeepweedDetectionCriteriaSetting$json = const {
  '1': 'DeepweedDetectionCriteriaSetting',
  '2': const [
    const {'1': 'point_categories', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.PointDetectionCategory', '10': 'pointCategories'},
    const {'1': 'weed_point_threshold', '3': 2, '4': 1, '5': 2, '10': 'weedPointThreshold'},
    const {'1': 'crop_point_threshold', '3': 3, '4': 1, '5': 2, '10': 'cropPointThreshold'},
    const {'1': 'segmentation_categories', '3': 4, '4': 3, '5': 11, '6': '.cv.runtime.proto.SegmentationDetectionCategory', '10': 'segmentationCategories'},
  ],
};

/// Descriptor for `DeepweedDetectionCriteriaSetting`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deepweedDetectionCriteriaSettingDescriptor = $convert.base64Decode('CiBEZWVwd2VlZERldGVjdGlvbkNyaXRlcmlhU2V0dGluZxJTChBwb2ludF9jYXRlZ29yaWVzGAEgAygLMiguY3YucnVudGltZS5wcm90by5Qb2ludERldGVjdGlvbkNhdGVnb3J5Ug9wb2ludENhdGVnb3JpZXMSMAoUd2VlZF9wb2ludF90aHJlc2hvbGQYAiABKAJSEndlZWRQb2ludFRocmVzaG9sZBIwChRjcm9wX3BvaW50X3RocmVzaG9sZBgDIAEoAlISY3JvcFBvaW50VGhyZXNob2xkEmgKF3NlZ21lbnRhdGlvbl9jYXRlZ29yaWVzGAQgAygLMi8uY3YucnVudGltZS5wcm90by5TZWdtZW50YXRpb25EZXRlY3Rpb25DYXRlZ29yeVIWc2VnbWVudGF0aW9uQ2F0ZWdvcmllcw==');
@$core.Deprecated('Use setDeepweedDetectionCriteriaRequestDescriptor instead')
const SetDeepweedDetectionCriteriaRequest$json = const {
  '1': 'SetDeepweedDetectionCriteriaRequest',
  '2': const [
    const {'1': 'weed_point_threshold', '3': 1, '4': 1, '5': 2, '10': 'weedPointThreshold'},
    const {'1': 'crop_point_threshold', '3': 2, '4': 1, '5': 2, '10': 'cropPointThreshold'},
    const {'1': 'point_categories', '3': 3, '4': 3, '5': 11, '6': '.cv.runtime.proto.PointDetectionCategory', '10': 'pointCategories'},
    const {'1': 'segmentation_categories', '3': 4, '4': 3, '5': 11, '6': '.cv.runtime.proto.SegmentationDetectionCategory', '10': 'segmentationCategories'},
  ],
};

/// Descriptor for `SetDeepweedDetectionCriteriaRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setDeepweedDetectionCriteriaRequestDescriptor = $convert.base64Decode('CiNTZXREZWVwd2VlZERldGVjdGlvbkNyaXRlcmlhUmVxdWVzdBIwChR3ZWVkX3BvaW50X3RocmVzaG9sZBgBIAEoAlISd2VlZFBvaW50VGhyZXNob2xkEjAKFGNyb3BfcG9pbnRfdGhyZXNob2xkGAIgASgCUhJjcm9wUG9pbnRUaHJlc2hvbGQSUwoQcG9pbnRfY2F0ZWdvcmllcxgDIAMoCzIoLmN2LnJ1bnRpbWUucHJvdG8uUG9pbnREZXRlY3Rpb25DYXRlZ29yeVIPcG9pbnRDYXRlZ29yaWVzEmgKF3NlZ21lbnRhdGlvbl9jYXRlZ29yaWVzGAQgAygLMi8uY3YucnVudGltZS5wcm90by5TZWdtZW50YXRpb25EZXRlY3Rpb25DYXRlZ29yeVIWc2VnbWVudGF0aW9uQ2F0ZWdvcmllcw==');
@$core.Deprecated('Use setDeepweedDetectionCriteriaResponseDescriptor instead')
const SetDeepweedDetectionCriteriaResponse$json = const {
  '1': 'SetDeepweedDetectionCriteriaResponse',
};

/// Descriptor for `SetDeepweedDetectionCriteriaResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setDeepweedDetectionCriteriaResponseDescriptor = $convert.base64Decode('CiRTZXREZWVwd2VlZERldGVjdGlvbkNyaXRlcmlhUmVzcG9uc2U=');
@$core.Deprecated('Use getDeepweedDetectionCriteriaRequestDescriptor instead')
const GetDeepweedDetectionCriteriaRequest$json = const {
  '1': 'GetDeepweedDetectionCriteriaRequest',
};

/// Descriptor for `GetDeepweedDetectionCriteriaRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedDetectionCriteriaRequestDescriptor = $convert.base64Decode('CiNHZXREZWVwd2VlZERldGVjdGlvbkNyaXRlcmlhUmVxdWVzdA==');
@$core.Deprecated('Use getDeepweedDetectionCriteriaResponseDescriptor instead')
const GetDeepweedDetectionCriteriaResponse$json = const {
  '1': 'GetDeepweedDetectionCriteriaResponse',
  '2': const [
    const {'1': 'weed_point_threshold', '3': 1, '4': 1, '5': 2, '10': 'weedPointThreshold'},
    const {'1': 'crop_point_threshold', '3': 2, '4': 1, '5': 2, '10': 'cropPointThreshold'},
    const {'1': 'point_categories', '3': 3, '4': 3, '5': 11, '6': '.cv.runtime.proto.PointDetectionCategory', '10': 'pointCategories'},
    const {'1': 'segmentation_categories', '3': 4, '4': 3, '5': 11, '6': '.cv.runtime.proto.SegmentationDetectionCategory', '10': 'segmentationCategories'},
  ],
};

/// Descriptor for `GetDeepweedDetectionCriteriaResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedDetectionCriteriaResponseDescriptor = $convert.base64Decode('CiRHZXREZWVwd2VlZERldGVjdGlvbkNyaXRlcmlhUmVzcG9uc2USMAoUd2VlZF9wb2ludF90aHJlc2hvbGQYASABKAJSEndlZWRQb2ludFRocmVzaG9sZBIwChRjcm9wX3BvaW50X3RocmVzaG9sZBgCIAEoAlISY3JvcFBvaW50VGhyZXNob2xkElMKEHBvaW50X2NhdGVnb3JpZXMYAyADKAsyKC5jdi5ydW50aW1lLnByb3RvLlBvaW50RGV0ZWN0aW9uQ2F0ZWdvcnlSD3BvaW50Q2F0ZWdvcmllcxJoChdzZWdtZW50YXRpb25fY2F0ZWdvcmllcxgEIAMoCzIvLmN2LnJ1bnRpbWUucHJvdG8uU2VnbWVudGF0aW9uRGV0ZWN0aW9uQ2F0ZWdvcnlSFnNlZ21lbnRhdGlvbkNhdGVnb3JpZXM=');
@$core.Deprecated('Use getDeepweedSupportedCategoriesRequestDescriptor instead')
const GetDeepweedSupportedCategoriesRequest$json = const {
  '1': 'GetDeepweedSupportedCategoriesRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'camId', '17': true},
  ],
  '8': const [
    const {'1': '_cam_id'},
  ],
};

/// Descriptor for `GetDeepweedSupportedCategoriesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedSupportedCategoriesRequestDescriptor = $convert.base64Decode('CiVHZXREZWVwd2VlZFN1cHBvcnRlZENhdGVnb3JpZXNSZXF1ZXN0EhoKBmNhbV9pZBgBIAEoCUgAUgVjYW1JZIgBAUIJCgdfY2FtX2lk');
@$core.Deprecated('Use getDeepweedSupportedCategoriesResponseDescriptor instead')
const GetDeepweedSupportedCategoriesResponse$json = const {
  '1': 'GetDeepweedSupportedCategoriesResponse',
  '2': const [
    const {'1': 'segmentation_categories', '3': 1, '4': 3, '5': 9, '10': 'segmentationCategories'},
    const {'1': 'point_categories', '3': 2, '4': 3, '5': 9, '10': 'pointCategories'},
  ],
};

/// Descriptor for `GetDeepweedSupportedCategoriesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedSupportedCategoriesResponseDescriptor = $convert.base64Decode('CiZHZXREZWVwd2VlZFN1cHBvcnRlZENhdGVnb3JpZXNSZXNwb25zZRI3ChdzZWdtZW50YXRpb25fY2F0ZWdvcmllcxgBIAMoCVIWc2VnbWVudGF0aW9uQ2F0ZWdvcmllcxIpChBwb2ludF9jYXRlZ29yaWVzGAIgAygJUg9wb2ludENhdGVnb3JpZXM=');
@$core.Deprecated('Use getDeepweedIndexToCategoryRequestDescriptor instead')
const GetDeepweedIndexToCategoryRequest$json = const {
  '1': 'GetDeepweedIndexToCategoryRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `GetDeepweedIndexToCategoryRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedIndexToCategoryRequestDescriptor = $convert.base64Decode('CiFHZXREZWVwd2VlZEluZGV4VG9DYXRlZ29yeVJlcXVlc3QSFQoGY2FtX2lkGAEgASgJUgVjYW1JZA==');
@$core.Deprecated('Use getDeepweedIndexToCategoryResponseDescriptor instead')
const GetDeepweedIndexToCategoryResponse$json = const {
  '1': 'GetDeepweedIndexToCategoryResponse',
  '2': const [
    const {'1': 'weed_point_index_to_category', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry', '10': 'weedPointIndexToCategory'},
    const {'1': 'crop_point_index_to_category', '3': 2, '4': 3, '5': 11, '6': '.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry', '10': 'cropPointIndexToCategory'},
    const {'1': 'intersection_index_to_category', '3': 3, '4': 3, '5': 11, '6': '.cv.runtime.proto.GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry', '10': 'intersectionIndexToCategory'},
  ],
  '3': const [GetDeepweedIndexToCategoryResponse_WeedPointIndexToCategoryEntry$json, GetDeepweedIndexToCategoryResponse_CropPointIndexToCategoryEntry$json, GetDeepweedIndexToCategoryResponse_IntersectionIndexToCategoryEntry$json],
};

@$core.Deprecated('Use getDeepweedIndexToCategoryResponseDescriptor instead')
const GetDeepweedIndexToCategoryResponse_WeedPointIndexToCategoryEntry$json = const {
  '1': 'WeedPointIndexToCategoryEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use getDeepweedIndexToCategoryResponseDescriptor instead')
const GetDeepweedIndexToCategoryResponse_CropPointIndexToCategoryEntry$json = const {
  '1': 'CropPointIndexToCategoryEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use getDeepweedIndexToCategoryResponseDescriptor instead')
const GetDeepweedIndexToCategoryResponse_IntersectionIndexToCategoryEntry$json = const {
  '1': 'IntersectionIndexToCategoryEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `GetDeepweedIndexToCategoryResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedIndexToCategoryResponseDescriptor = $convert.base64Decode('CiJHZXREZWVwd2VlZEluZGV4VG9DYXRlZ29yeVJlc3BvbnNlEpIBChx3ZWVkX3BvaW50X2luZGV4X3RvX2NhdGVnb3J5GAEgAygLMlIuY3YucnVudGltZS5wcm90by5HZXREZWVwd2VlZEluZGV4VG9DYXRlZ29yeVJlc3BvbnNlLldlZWRQb2ludEluZGV4VG9DYXRlZ29yeUVudHJ5Uhh3ZWVkUG9pbnRJbmRleFRvQ2F0ZWdvcnkSkgEKHGNyb3BfcG9pbnRfaW5kZXhfdG9fY2F0ZWdvcnkYAiADKAsyUi5jdi5ydW50aW1lLnByb3RvLkdldERlZXB3ZWVkSW5kZXhUb0NhdGVnb3J5UmVzcG9uc2UuQ3JvcFBvaW50SW5kZXhUb0NhdGVnb3J5RW50cnlSGGNyb3BQb2ludEluZGV4VG9DYXRlZ29yeRKaAQoeaW50ZXJzZWN0aW9uX2luZGV4X3RvX2NhdGVnb3J5GAMgAygLMlUuY3YucnVudGltZS5wcm90by5HZXREZWVwd2VlZEluZGV4VG9DYXRlZ29yeVJlc3BvbnNlLkludGVyc2VjdGlvbkluZGV4VG9DYXRlZ29yeUVudHJ5UhtpbnRlcnNlY3Rpb25JbmRleFRvQ2F0ZWdvcnkaSwodV2VlZFBvaW50SW5kZXhUb0NhdGVnb3J5RW50cnkSEAoDa2V5GAEgASgFUgNrZXkSFAoFdmFsdWUYAiABKAlSBXZhbHVlOgI4ARpLCh1Dcm9wUG9pbnRJbmRleFRvQ2F0ZWdvcnlFbnRyeRIQCgNrZXkYASABKAVSA2tleRIUCgV2YWx1ZRgCIAEoCVIFdmFsdWU6AjgBGk4KIEludGVyc2VjdGlvbkluZGV4VG9DYXRlZ29yeUVudHJ5EhAKA2tleRgBIAEoBVIDa2V5EhQKBXZhbHVlGAIgASgJUgV2YWx1ZToCOAE=');
@$core.Deprecated('Use getPredictCamMatrixRequestDescriptor instead')
const GetPredictCamMatrixRequest$json = const {
  '1': 'GetPredictCamMatrixRequest',
  '2': const [
    const {'1': 'predict_cam_id', '3': 1, '4': 1, '5': 9, '10': 'predictCamId'},
  ],
};

/// Descriptor for `GetPredictCamMatrixRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictCamMatrixRequestDescriptor = $convert.base64Decode('ChpHZXRQcmVkaWN0Q2FtTWF0cml4UmVxdWVzdBIkCg5wcmVkaWN0X2NhbV9pZBgBIAEoCVIMcHJlZGljdENhbUlk');
@$core.Deprecated('Use getPredictCamDistortionCoefficientsRequestDescriptor instead')
const GetPredictCamDistortionCoefficientsRequest$json = const {
  '1': 'GetPredictCamDistortionCoefficientsRequest',
  '2': const [
    const {'1': 'predict_cam_id', '3': 1, '4': 1, '5': 9, '10': 'predictCamId'},
  ],
};

/// Descriptor for `GetPredictCamDistortionCoefficientsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictCamDistortionCoefficientsRequestDescriptor = $convert.base64Decode('CipHZXRQcmVkaWN0Q2FtRGlzdG9ydGlvbkNvZWZmaWNpZW50c1JlcXVlc3QSJAoOcHJlZGljdF9jYW1faWQYASABKAlSDHByZWRpY3RDYW1JZA==');
@$core.Deprecated('Use setCameraSettingsRequestDescriptor instead')
const SetCameraSettingsRequest$json = const {
  '1': 'SetCameraSettingsRequest',
  '2': const [
    const {'1': 'cam_ids', '3': 1, '4': 3, '5': 9, '10': 'camIds'},
    const {'1': 'exposure_us', '3': 2, '4': 1, '5': 2, '9': 0, '10': 'exposureUs', '17': true},
    const {'1': 'gamma', '3': 3, '4': 1, '5': 2, '9': 1, '10': 'gamma', '17': true},
    const {'1': 'gain_db', '3': 4, '4': 1, '5': 2, '9': 2, '10': 'gainDb', '17': true},
    const {'1': 'light_source_preset', '3': 5, '4': 1, '5': 14, '6': '.lib.common.camera.LightSourcePreset', '9': 3, '10': 'lightSourcePreset', '17': true},
    const {'1': 'wb_ratio_red', '3': 6, '4': 1, '5': 2, '9': 4, '10': 'wbRatioRed', '17': true},
    const {'1': 'wb_ratio_green', '3': 7, '4': 1, '5': 2, '9': 5, '10': 'wbRatioGreen', '17': true},
    const {'1': 'wb_ratio_blue', '3': 8, '4': 1, '5': 2, '9': 6, '10': 'wbRatioBlue', '17': true},
    const {'1': 'roi_offset_x', '3': 9, '4': 1, '5': 3, '9': 7, '10': 'roiOffsetX', '17': true},
    const {'1': 'roi_offset_y', '3': 10, '4': 1, '5': 3, '9': 8, '10': 'roiOffsetY', '17': true},
    const {'1': 'mirror', '3': 11, '4': 1, '5': 8, '9': 9, '10': 'mirror', '17': true},
    const {'1': 'flip', '3': 12, '4': 1, '5': 8, '9': 10, '10': 'flip', '17': true},
    const {'1': 'strobing', '3': 13, '4': 1, '5': 8, '9': 11, '10': 'strobing', '17': true},
    const {'1': 'ptp', '3': 14, '4': 1, '5': 8, '9': 12, '10': 'ptp', '17': true},
    const {'1': 'auto_whitebalance', '3': 15, '4': 1, '5': 8, '9': 13, '10': 'autoWhitebalance', '17': true},
  ],
  '8': const [
    const {'1': '_exposure_us'},
    const {'1': '_gamma'},
    const {'1': '_gain_db'},
    const {'1': '_light_source_preset'},
    const {'1': '_wb_ratio_red'},
    const {'1': '_wb_ratio_green'},
    const {'1': '_wb_ratio_blue'},
    const {'1': '_roi_offset_x'},
    const {'1': '_roi_offset_y'},
    const {'1': '_mirror'},
    const {'1': '_flip'},
    const {'1': '_strobing'},
    const {'1': '_ptp'},
    const {'1': '_auto_whitebalance'},
  ],
};

/// Descriptor for `SetCameraSettingsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setCameraSettingsRequestDescriptor = $convert.base64Decode('ChhTZXRDYW1lcmFTZXR0aW5nc1JlcXVlc3QSFwoHY2FtX2lkcxgBIAMoCVIGY2FtSWRzEiQKC2V4cG9zdXJlX3VzGAIgASgCSABSCmV4cG9zdXJlVXOIAQESGQoFZ2FtbWEYAyABKAJIAVIFZ2FtbWGIAQESHAoHZ2Fpbl9kYhgEIAEoAkgCUgZnYWluRGKIAQESWQoTbGlnaHRfc291cmNlX3ByZXNldBgFIAEoDjIkLmxpYi5jb21tb24uY2FtZXJhLkxpZ2h0U291cmNlUHJlc2V0SANSEWxpZ2h0U291cmNlUHJlc2V0iAEBEiUKDHdiX3JhdGlvX3JlZBgGIAEoAkgEUgp3YlJhdGlvUmVkiAEBEikKDndiX3JhdGlvX2dyZWVuGAcgASgCSAVSDHdiUmF0aW9HcmVlbogBARInCg13Yl9yYXRpb19ibHVlGAggASgCSAZSC3diUmF0aW9CbHVliAEBEiUKDHJvaV9vZmZzZXRfeBgJIAEoA0gHUgpyb2lPZmZzZXRYiAEBEiUKDHJvaV9vZmZzZXRfeRgKIAEoA0gIUgpyb2lPZmZzZXRZiAEBEhsKBm1pcnJvchgLIAEoCEgJUgZtaXJyb3KIAQESFwoEZmxpcBgMIAEoCEgKUgRmbGlwiAEBEh8KCHN0cm9iaW5nGA0gASgISAtSCHN0cm9iaW5niAEBEhUKA3B0cBgOIAEoCEgMUgNwdHCIAQESMAoRYXV0b193aGl0ZWJhbGFuY2UYDyABKAhIDVIQYXV0b1doaXRlYmFsYW5jZYgBAUIOCgxfZXhwb3N1cmVfdXNCCAoGX2dhbW1hQgoKCF9nYWluX2RiQhYKFF9saWdodF9zb3VyY2VfcHJlc2V0Qg8KDV93Yl9yYXRpb19yZWRCEQoPX3diX3JhdGlvX2dyZWVuQhAKDl93Yl9yYXRpb19ibHVlQg8KDV9yb2lfb2Zmc2V0X3hCDwoNX3JvaV9vZmZzZXRfeUIJCgdfbWlycm9yQgcKBV9mbGlwQgsKCV9zdHJvYmluZ0IGCgRfcHRwQhQKEl9hdXRvX3doaXRlYmFsYW5jZQ==');
@$core.Deprecated('Use setCameraSettingsResponseDescriptor instead')
const SetCameraSettingsResponse$json = const {
  '1': 'SetCameraSettingsResponse',
  '2': const [
    const {'1': 'cam_ids', '3': 1, '4': 3, '5': 9, '10': 'camIds'},
  ],
};

/// Descriptor for `SetCameraSettingsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setCameraSettingsResponseDescriptor = $convert.base64Decode('ChlTZXRDYW1lcmFTZXR0aW5nc1Jlc3BvbnNlEhcKB2NhbV9pZHMYASADKAlSBmNhbUlkcw==');
@$core.Deprecated('Use setAutoWhitebalanceRequestDescriptor instead')
const SetAutoWhitebalanceRequest$json = const {
  '1': 'SetAutoWhitebalanceRequest',
  '2': const [
    const {'1': 'enable', '3': 1, '4': 1, '5': 8, '10': 'enable'},
    const {'1': 'cam_ids', '3': 2, '4': 3, '5': 9, '10': 'camIds'},
  ],
};

/// Descriptor for `SetAutoWhitebalanceRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setAutoWhitebalanceRequestDescriptor = $convert.base64Decode('ChpTZXRBdXRvV2hpdGViYWxhbmNlUmVxdWVzdBIWCgZlbmFibGUYASABKAhSBmVuYWJsZRIXCgdjYW1faWRzGAIgAygJUgZjYW1JZHM=');
@$core.Deprecated('Use setAutoWhitebalanceResponseDescriptor instead')
const SetAutoWhitebalanceResponse$json = const {
  '1': 'SetAutoWhitebalanceResponse',
};

/// Descriptor for `SetAutoWhitebalanceResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setAutoWhitebalanceResponseDescriptor = $convert.base64Decode('ChtTZXRBdXRvV2hpdGViYWxhbmNlUmVzcG9uc2U=');
@$core.Deprecated('Use getCameraSettingsRequestDescriptor instead')
const GetCameraSettingsRequest$json = const {
  '1': 'GetCameraSettingsRequest',
  '2': const [
    const {'1': 'cam_ids', '3': 1, '4': 3, '5': 9, '10': 'camIds'},
  ],
};

/// Descriptor for `GetCameraSettingsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraSettingsRequestDescriptor = $convert.base64Decode('ChhHZXRDYW1lcmFTZXR0aW5nc1JlcXVlc3QSFwoHY2FtX2lkcxgBIAMoCVIGY2FtSWRz');
@$core.Deprecated('Use cameraSettingsResponseDescriptor instead')
const CameraSettingsResponse$json = const {
  '1': 'CameraSettingsResponse',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'exposure_us', '3': 2, '4': 1, '5': 2, '9': 0, '10': 'exposureUs', '17': true},
    const {'1': 'gamma', '3': 3, '4': 1, '5': 2, '9': 1, '10': 'gamma', '17': true},
    const {'1': 'gain_db', '3': 4, '4': 1, '5': 2, '9': 2, '10': 'gainDb', '17': true},
    const {'1': 'light_source_preset', '3': 5, '4': 1, '5': 14, '6': '.lib.common.camera.LightSourcePreset', '9': 3, '10': 'lightSourcePreset', '17': true},
    const {'1': 'wb_ratio_red', '3': 6, '4': 1, '5': 2, '9': 4, '10': 'wbRatioRed', '17': true},
    const {'1': 'wb_ratio_green', '3': 7, '4': 1, '5': 2, '9': 5, '10': 'wbRatioGreen', '17': true},
    const {'1': 'wb_ratio_blue', '3': 8, '4': 1, '5': 2, '9': 6, '10': 'wbRatioBlue', '17': true},
    const {'1': 'roi_width', '3': 9, '4': 1, '5': 3, '9': 7, '10': 'roiWidth', '17': true},
    const {'1': 'roi_height', '3': 10, '4': 1, '5': 3, '9': 8, '10': 'roiHeight', '17': true},
    const {'1': 'roi_offset_x', '3': 11, '4': 1, '5': 3, '9': 9, '10': 'roiOffsetX', '17': true},
    const {'1': 'roi_offset_y', '3': 12, '4': 1, '5': 3, '9': 10, '10': 'roiOffsetY', '17': true},
    const {'1': 'gpu_id', '3': 13, '4': 1, '5': 3, '9': 11, '10': 'gpuId', '17': true},
    const {'1': 'mirror', '3': 14, '4': 1, '5': 8, '10': 'mirror'},
    const {'1': 'flip', '3': 15, '4': 1, '5': 8, '10': 'flip'},
    const {'1': 'strobing', '3': 16, '4': 1, '5': 8, '10': 'strobing'},
    const {'1': 'ptp', '3': 17, '4': 1, '5': 8, '10': 'ptp'},
    const {'1': 'auto_whitebalance', '3': 18, '4': 1, '5': 8, '9': 12, '10': 'autoWhitebalance', '17': true},
  ],
  '8': const [
    const {'1': '_exposure_us'},
    const {'1': '_gamma'},
    const {'1': '_gain_db'},
    const {'1': '_light_source_preset'},
    const {'1': '_wb_ratio_red'},
    const {'1': '_wb_ratio_green'},
    const {'1': '_wb_ratio_blue'},
    const {'1': '_roi_width'},
    const {'1': '_roi_height'},
    const {'1': '_roi_offset_x'},
    const {'1': '_roi_offset_y'},
    const {'1': '_gpu_id'},
    const {'1': '_auto_whitebalance'},
  ],
};

/// Descriptor for `CameraSettingsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cameraSettingsResponseDescriptor = $convert.base64Decode('ChZDYW1lcmFTZXR0aW5nc1Jlc3BvbnNlEhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSJAoLZXhwb3N1cmVfdXMYAiABKAJIAFIKZXhwb3N1cmVVc4gBARIZCgVnYW1tYRgDIAEoAkgBUgVnYW1tYYgBARIcCgdnYWluX2RiGAQgASgCSAJSBmdhaW5EYogBARJZChNsaWdodF9zb3VyY2VfcHJlc2V0GAUgASgOMiQubGliLmNvbW1vbi5jYW1lcmEuTGlnaHRTb3VyY2VQcmVzZXRIA1IRbGlnaHRTb3VyY2VQcmVzZXSIAQESJQoMd2JfcmF0aW9fcmVkGAYgASgCSARSCndiUmF0aW9SZWSIAQESKQoOd2JfcmF0aW9fZ3JlZW4YByABKAJIBVIMd2JSYXRpb0dyZWVuiAEBEicKDXdiX3JhdGlvX2JsdWUYCCABKAJIBlILd2JSYXRpb0JsdWWIAQESIAoJcm9pX3dpZHRoGAkgASgDSAdSCHJvaVdpZHRoiAEBEiIKCnJvaV9oZWlnaHQYCiABKANICFIJcm9pSGVpZ2h0iAEBEiUKDHJvaV9vZmZzZXRfeBgLIAEoA0gJUgpyb2lPZmZzZXRYiAEBEiUKDHJvaV9vZmZzZXRfeRgMIAEoA0gKUgpyb2lPZmZzZXRZiAEBEhoKBmdwdV9pZBgNIAEoA0gLUgVncHVJZIgBARIWCgZtaXJyb3IYDiABKAhSBm1pcnJvchISCgRmbGlwGA8gASgIUgRmbGlwEhoKCHN0cm9iaW5nGBAgASgIUghzdHJvYmluZxIQCgNwdHAYESABKAhSA3B0cBIwChFhdXRvX3doaXRlYmFsYW5jZRgSIAEoCEgMUhBhdXRvV2hpdGViYWxhbmNliAEBQg4KDF9leHBvc3VyZV91c0IICgZfZ2FtbWFCCgoIX2dhaW5fZGJCFgoUX2xpZ2h0X3NvdXJjZV9wcmVzZXRCDwoNX3diX3JhdGlvX3JlZEIRCg9fd2JfcmF0aW9fZ3JlZW5CEAoOX3diX3JhdGlvX2JsdWVCDAoKX3JvaV93aWR0aEINCgtfcm9pX2hlaWdodEIPCg1fcm9pX29mZnNldF94Qg8KDV9yb2lfb2Zmc2V0X3lCCQoHX2dwdV9pZEIUChJfYXV0b193aGl0ZWJhbGFuY2U=');
@$core.Deprecated('Use getCameraSettingsResponseDescriptor instead')
const GetCameraSettingsResponse$json = const {
  '1': 'GetCameraSettingsResponse',
  '2': const [
    const {'1': 'camera_settings_response', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.CameraSettingsResponse', '10': 'cameraSettingsResponse'},
  ],
};

/// Descriptor for `GetCameraSettingsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraSettingsResponseDescriptor = $convert.base64Decode('ChlHZXRDYW1lcmFTZXR0aW5nc1Jlc3BvbnNlEmIKGGNhbWVyYV9zZXR0aW5nc19yZXNwb25zZRgBIAMoCzIoLmN2LnJ1bnRpbWUucHJvdG8uQ2FtZXJhU2V0dGluZ3NSZXNwb25zZVIWY2FtZXJhU2V0dGluZ3NSZXNwb25zZQ==');
@$core.Deprecated('Use startBurstRecordFramesRequestDescriptor instead')
const StartBurstRecordFramesRequest$json = const {
  '1': 'StartBurstRecordFramesRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'duration_ms', '3': 2, '4': 1, '5': 3, '10': 'durationMs'},
    const {'1': 'path', '3': 3, '4': 1, '5': 9, '10': 'path'},
    const {'1': 'dont_capture_predict_image', '3': 4, '4': 1, '5': 8, '10': 'dontCapturePredictImage'},
    const {'1': 'downsample_factor', '3': 5, '4': 1, '5': 5, '10': 'downsampleFactor'},
  ],
};

/// Descriptor for `StartBurstRecordFramesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startBurstRecordFramesRequestDescriptor = $convert.base64Decode('Ch1TdGFydEJ1cnN0UmVjb3JkRnJhbWVzUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEh8KC2R1cmF0aW9uX21zGAIgASgDUgpkdXJhdGlvbk1zEhIKBHBhdGgYAyABKAlSBHBhdGgSOwoaZG9udF9jYXB0dXJlX3ByZWRpY3RfaW1hZ2UYBCABKAhSF2RvbnRDYXB0dXJlUHJlZGljdEltYWdlEisKEWRvd25zYW1wbGVfZmFjdG9yGAUgASgFUhBkb3duc2FtcGxlRmFjdG9y');
@$core.Deprecated('Use startBurstRecordFramesResponseDescriptor instead')
const StartBurstRecordFramesResponse$json = const {
  '1': 'StartBurstRecordFramesResponse',
};

/// Descriptor for `StartBurstRecordFramesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startBurstRecordFramesResponseDescriptor = $convert.base64Decode('Ch5TdGFydEJ1cnN0UmVjb3JkRnJhbWVzUmVzcG9uc2U=');
@$core.Deprecated('Use stopBurstRecordFramesRequestDescriptor instead')
const StopBurstRecordFramesRequest$json = const {
  '1': 'StopBurstRecordFramesRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'last_frame_timestamp_ms', '3': 2, '4': 1, '5': 3, '9': 0, '10': 'lastFrameTimestampMs', '17': true},
  ],
  '8': const [
    const {'1': '_last_frame_timestamp_ms'},
  ],
};

/// Descriptor for `StopBurstRecordFramesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopBurstRecordFramesRequestDescriptor = $convert.base64Decode('ChxTdG9wQnVyc3RSZWNvcmRGcmFtZXNSZXF1ZXN0EhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSOgoXbGFzdF9mcmFtZV90aW1lc3RhbXBfbXMYAiABKANIAFIUbGFzdEZyYW1lVGltZXN0YW1wTXOIAQFCGgoYX2xhc3RfZnJhbWVfdGltZXN0YW1wX21z');
@$core.Deprecated('Use stopBurstRecordFramesResponseDescriptor instead')
const StopBurstRecordFramesResponse$json = const {
  '1': 'StopBurstRecordFramesResponse',
};

/// Descriptor for `StopBurstRecordFramesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopBurstRecordFramesResponseDescriptor = $convert.base64Decode('Ch1TdG9wQnVyc3RSZWNvcmRGcmFtZXNSZXNwb25zZQ==');
@$core.Deprecated('Use p2POutputProtoDescriptor instead')
const P2POutputProto$json = const {
  '1': 'P2POutputProto',
  '2': const [
    const {'1': 'matched', '3': 1, '4': 1, '5': 8, '10': 'matched'},
    const {'1': 'target_coord_x', '3': 2, '4': 1, '5': 2, '10': 'targetCoordX'},
    const {'1': 'target_coord_y', '3': 3, '4': 1, '5': 2, '10': 'targetCoordY'},
    const {'1': 'target_timestamp_ms', '3': 4, '4': 1, '5': 3, '10': 'targetTimestampMs'},
    const {'1': 'predict_timestamp_ms', '3': 5, '4': 1, '5': 3, '10': 'predictTimestampMs'},
    const {'1': 'safe', '3': 6, '4': 1, '5': 8, '10': 'safe'},
    const {'1': 'predict_coord_x', '3': 7, '4': 1, '5': 2, '10': 'predictCoordX'},
    const {'1': 'predict_coord_y', '3': 8, '4': 1, '5': 2, '10': 'predictCoordY'},
    const {'1': 'predict_cam', '3': 9, '4': 1, '5': 9, '10': 'predictCam'},
  ],
};

/// Descriptor for `P2POutputProto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2POutputProtoDescriptor = $convert.base64Decode('Cg5QMlBPdXRwdXRQcm90bxIYCgdtYXRjaGVkGAEgASgIUgdtYXRjaGVkEiQKDnRhcmdldF9jb29yZF94GAIgASgCUgx0YXJnZXRDb29yZFgSJAoOdGFyZ2V0X2Nvb3JkX3kYAyABKAJSDHRhcmdldENvb3JkWRIuChN0YXJnZXRfdGltZXN0YW1wX21zGAQgASgDUhF0YXJnZXRUaW1lc3RhbXBNcxIwChRwcmVkaWN0X3RpbWVzdGFtcF9tcxgFIAEoA1IScHJlZGljdFRpbWVzdGFtcE1zEhIKBHNhZmUYBiABKAhSBHNhZmUSJgoPcHJlZGljdF9jb29yZF94GAcgASgCUg1wcmVkaWN0Q29vcmRYEiYKD3ByZWRpY3RfY29vcmRfeRgIIAEoAlINcHJlZGljdENvb3JkWRIfCgtwcmVkaWN0X2NhbRgJIAEoCVIKcHJlZGljdENhbQ==');
@$core.Deprecated('Use getNextP2POutputRequestDescriptor instead')
const GetNextP2POutputRequest$json = const {
  '1': 'GetNextP2POutputRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'timeout_ms', '3': 3, '4': 1, '5': 3, '10': 'timeoutMs'},
  ],
};

/// Descriptor for `GetNextP2POutputRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextP2POutputRequestDescriptor = $convert.base64Decode('ChdHZXROZXh0UDJQT3V0cHV0UmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXMSHQoKdGltZW91dF9tcxgDIAEoA1IJdGltZW91dE1z');
@$core.Deprecated('Use getConnectorsRequestDescriptor instead')
const GetConnectorsRequest$json = const {
  '1': 'GetConnectorsRequest',
  '2': const [
    const {'1': 'cam_ids', '3': 1, '4': 3, '5': 9, '10': 'camIds'},
    const {'1': 'connector_ids', '3': 2, '4': 3, '5': 9, '10': 'connectorIds'},
  ],
};

/// Descriptor for `GetConnectorsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getConnectorsRequestDescriptor = $convert.base64Decode('ChRHZXRDb25uZWN0b3JzUmVxdWVzdBIXCgdjYW1faWRzGAEgAygJUgZjYW1JZHMSIwoNY29ubmVjdG9yX2lkcxgCIAMoCVIMY29ubmVjdG9ySWRz');
@$core.Deprecated('Use connectorResponseDescriptor instead')
const ConnectorResponse$json = const {
  '1': 'ConnectorResponse',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'connector_id', '3': 2, '4': 1, '5': 9, '10': 'connectorId'},
    const {'1': 'is_enabled', '3': 3, '4': 1, '5': 8, '10': 'isEnabled'},
    const {'1': 'reduction_ratio', '3': 4, '4': 1, '5': 3, '10': 'reductionRatio'},
  ],
};

/// Descriptor for `ConnectorResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List connectorResponseDescriptor = $convert.base64Decode('ChFDb25uZWN0b3JSZXNwb25zZRIVCgZjYW1faWQYASABKAlSBWNhbUlkEiEKDGNvbm5lY3Rvcl9pZBgCIAEoCVILY29ubmVjdG9ySWQSHQoKaXNfZW5hYmxlZBgDIAEoCFIJaXNFbmFibGVkEicKD3JlZHVjdGlvbl9yYXRpbxgEIAEoA1IOcmVkdWN0aW9uUmF0aW8=');
@$core.Deprecated('Use getConnectorsResponseDescriptor instead')
const GetConnectorsResponse$json = const {
  '1': 'GetConnectorsResponse',
  '2': const [
    const {'1': 'connector_response', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.ConnectorResponse', '10': 'connectorResponse'},
  ],
};

/// Descriptor for `GetConnectorsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getConnectorsResponseDescriptor = $convert.base64Decode('ChVHZXRDb25uZWN0b3JzUmVzcG9uc2USUgoSY29ubmVjdG9yX3Jlc3BvbnNlGAEgAygLMiMuY3YucnVudGltZS5wcm90by5Db25uZWN0b3JSZXNwb25zZVIRY29ubmVjdG9yUmVzcG9uc2U=');
@$core.Deprecated('Use setConnectorsRequestDescriptor instead')
const SetConnectorsRequest$json = const {
  '1': 'SetConnectorsRequest',
  '2': const [
    const {'1': 'cam_ids', '3': 1, '4': 3, '5': 9, '10': 'camIds'},
    const {'1': 'connector_ids', '3': 2, '4': 3, '5': 9, '10': 'connectorIds'},
    const {'1': 'is_enabled', '3': 3, '4': 1, '5': 8, '9': 0, '10': 'isEnabled', '17': true},
    const {'1': 'reduction_ratio', '3': 4, '4': 1, '5': 3, '9': 1, '10': 'reductionRatio', '17': true},
  ],
  '8': const [
    const {'1': '_is_enabled'},
    const {'1': '_reduction_ratio'},
  ],
};

/// Descriptor for `SetConnectorsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setConnectorsRequestDescriptor = $convert.base64Decode('ChRTZXRDb25uZWN0b3JzUmVxdWVzdBIXCgdjYW1faWRzGAEgAygJUgZjYW1JZHMSIwoNY29ubmVjdG9yX2lkcxgCIAMoCVIMY29ubmVjdG9ySWRzEiIKCmlzX2VuYWJsZWQYAyABKAhIAFIJaXNFbmFibGVkiAEBEiwKD3JlZHVjdGlvbl9yYXRpbxgEIAEoA0gBUg5yZWR1Y3Rpb25SYXRpb4gBAUINCgtfaXNfZW5hYmxlZEISChBfcmVkdWN0aW9uX3JhdGlv');
@$core.Deprecated('Use setConnectorsResponseDescriptor instead')
const SetConnectorsResponse$json = const {
  '1': 'SetConnectorsResponse',
};

/// Descriptor for `SetConnectorsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setConnectorsResponseDescriptor = $convert.base64Decode('ChVTZXRDb25uZWN0b3JzUmVzcG9uc2U=');
@$core.Deprecated('Use nodeTimingDescriptor instead')
const NodeTiming$json = const {
  '1': 'NodeTiming',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'fps_mean', '3': 2, '4': 1, '5': 2, '10': 'fpsMean'},
    const {'1': 'fps_99pct', '3': 3, '4': 1, '5': 2, '10': 'fps99pct'},
    const {'1': 'latency_ms_mean', '3': 4, '4': 1, '5': 2, '10': 'latencyMsMean'},
    const {'1': 'latency_ms_99pct', '3': 5, '4': 1, '5': 2, '10': 'latencyMs99pct'},
    const {'1': 'state', '3': 6, '4': 1, '5': 9, '10': 'state'},
    const {'1': 'state_timings', '3': 7, '4': 3, '5': 11, '6': '.cv.runtime.proto.NodeTiming.StateTimingsEntry', '10': 'stateTimings'},
  ],
  '3': const [NodeTiming_StateTimingsEntry$json],
};

@$core.Deprecated('Use nodeTimingDescriptor instead')
const NodeTiming_StateTimingsEntry$json = const {
  '1': 'StateTimingsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `NodeTiming`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List nodeTimingDescriptor = $convert.base64Decode('CgpOb2RlVGltaW5nEhIKBG5hbWUYASABKAlSBG5hbWUSGQoIZnBzX21lYW4YAiABKAJSB2Zwc01lYW4SGwoJZnBzXzk5cGN0GAMgASgCUghmcHM5OXBjdBImCg9sYXRlbmN5X21zX21lYW4YBCABKAJSDWxhdGVuY3lNc01lYW4SKAoQbGF0ZW5jeV9tc185OXBjdBgFIAEoAlIObGF0ZW5jeU1zOTlwY3QSFAoFc3RhdGUYBiABKAlSBXN0YXRlElMKDXN0YXRlX3RpbWluZ3MYByADKAsyLi5jdi5ydW50aW1lLnByb3RvLk5vZGVUaW1pbmcuU3RhdGVUaW1pbmdzRW50cnlSDHN0YXRlVGltaW5ncxo/ChFTdGF0ZVRpbWluZ3NFbnRyeRIQCgNrZXkYASABKAlSA2tleRIUCgV2YWx1ZRgCIAEoAlIFdmFsdWU6AjgB');
@$core.Deprecated('Use getTimingRequestDescriptor instead')
const GetTimingRequest$json = const {
  '1': 'GetTimingRequest',
};

/// Descriptor for `GetTimingRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTimingRequestDescriptor = $convert.base64Decode('ChBHZXRUaW1pbmdSZXF1ZXN0');
@$core.Deprecated('Use getTimingResponseDescriptor instead')
const GetTimingResponse$json = const {
  '1': 'GetTimingResponse',
  '2': const [
    const {'1': 'node_timing', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.NodeTiming', '10': 'nodeTiming'},
  ],
};

/// Descriptor for `GetTimingResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTimingResponseDescriptor = $convert.base64Decode('ChFHZXRUaW1pbmdSZXNwb25zZRI9Cgtub2RlX3RpbWluZxgBIAMoCzIcLmN2LnJ1bnRpbWUucHJvdG8uTm9kZVRpbWluZ1IKbm9kZVRpbWluZw==');
@$core.Deprecated('Use predictRequestDescriptor instead')
const PredictRequest$json = const {
  '1': 'PredictRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'file_paths', '3': 2, '4': 3, '5': 9, '10': 'filePaths'},
    const {'1': 'timestamps_ms', '3': 3, '4': 3, '5': 3, '10': 'timestampsMs'},
  ],
};

/// Descriptor for `PredictRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictRequestDescriptor = $convert.base64Decode('Cg5QcmVkaWN0UmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEh0KCmZpbGVfcGF0aHMYAiADKAlSCWZpbGVQYXRocxIjCg10aW1lc3RhbXBzX21zGAMgAygDUgx0aW1lc3RhbXBzTXM=');
@$core.Deprecated('Use predictResponseDescriptor instead')
const PredictResponse$json = const {
  '1': 'PredictResponse',
};

/// Descriptor for `PredictResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictResponseDescriptor = $convert.base64Decode('Cg9QcmVkaWN0UmVzcG9uc2U=');
@$core.Deprecated('Use loadAndQueueRequestDescriptor instead')
const LoadAndQueueRequest$json = const {
  '1': 'LoadAndQueueRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'file_paths', '3': 2, '4': 3, '5': 9, '10': 'filePaths'},
    const {'1': 'timestamps_ms', '3': 3, '4': 3, '5': 3, '10': 'timestampsMs'},
  ],
};

/// Descriptor for `LoadAndQueueRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loadAndQueueRequestDescriptor = $convert.base64Decode('ChNMb2FkQW5kUXVldWVSZXF1ZXN0EhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSHQoKZmlsZV9wYXRocxgCIAMoCVIJZmlsZVBhdGhzEiMKDXRpbWVzdGFtcHNfbXMYAyADKANSDHRpbWVzdGFtcHNNcw==');
@$core.Deprecated('Use loadAndQueueResponseDescriptor instead')
const LoadAndQueueResponse$json = const {
  '1': 'LoadAndQueueResponse',
};

/// Descriptor for `LoadAndQueueResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loadAndQueueResponseDescriptor = $convert.base64Decode('ChRMb2FkQW5kUXVldWVSZXNwb25zZQ==');
@$core.Deprecated('Use setImageRequestDescriptor instead')
const SetImageRequest$json = const {
  '1': 'SetImageRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'file_path', '3': 2, '4': 1, '5': 9, '10': 'filePath'},
  ],
};

/// Descriptor for `SetImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImageRequestDescriptor = $convert.base64Decode('Cg9TZXRJbWFnZVJlcXVlc3QSFQoGY2FtX2lkGAEgASgJUgVjYW1JZBIbCglmaWxlX3BhdGgYAiABKAlSCGZpbGVQYXRo');
@$core.Deprecated('Use setImageResponseDescriptor instead')
const SetImageResponse$json = const {
  '1': 'SetImageResponse',
};

/// Descriptor for `SetImageResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImageResponseDescriptor = $convert.base64Decode('ChBTZXRJbWFnZVJlc3BvbnNl');
@$core.Deprecated('Use unsetImageRequestDescriptor instead')
const UnsetImageRequest$json = const {
  '1': 'UnsetImageRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `UnsetImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List unsetImageRequestDescriptor = $convert.base64Decode('ChFVbnNldEltYWdlUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlk');
@$core.Deprecated('Use unsetImageResponseDescriptor instead')
const UnsetImageResponse$json = const {
  '1': 'UnsetImageResponse',
};

/// Descriptor for `UnsetImageResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List unsetImageResponseDescriptor = $convert.base64Decode('ChJVbnNldEltYWdlUmVzcG9uc2U=');
@$core.Deprecated('Use getModelPathsRequestDescriptor instead')
const GetModelPathsRequest$json = const {
  '1': 'GetModelPathsRequest',
};

/// Descriptor for `GetModelPathsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getModelPathsRequestDescriptor = $convert.base64Decode('ChRHZXRNb2RlbFBhdGhzUmVxdWVzdA==');
@$core.Deprecated('Use getModelPathsResponseDescriptor instead')
const GetModelPathsResponse$json = const {
  '1': 'GetModelPathsResponse',
  '2': const [
    const {'1': 'p2p', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'p2p', '17': true},
    const {'1': 'deepweed', '3': 2, '4': 1, '5': 9, '9': 1, '10': 'deepweed', '17': true},
    const {'1': 'furrows', '3': 3, '4': 1, '5': 9, '9': 2, '10': 'furrows', '17': true},
  ],
  '8': const [
    const {'1': '_p2p'},
    const {'1': '_deepweed'},
    const {'1': '_furrows'},
  ],
};

/// Descriptor for `GetModelPathsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getModelPathsResponseDescriptor = $convert.base64Decode('ChVHZXRNb2RlbFBhdGhzUmVzcG9uc2USFQoDcDJwGAEgASgJSABSA3AycIgBARIfCghkZWVwd2VlZBgCIAEoCUgBUghkZWVwd2VlZIgBARIdCgdmdXJyb3dzGAMgASgJSAJSB2Z1cnJvd3OIAQFCBgoEX3AycEILCglfZGVlcHdlZWRCCgoIX2Z1cnJvd3M=');
@$core.Deprecated('Use getCameraTemperaturesRequestDescriptor instead')
const GetCameraTemperaturesRequest$json = const {
  '1': 'GetCameraTemperaturesRequest',
};

/// Descriptor for `GetCameraTemperaturesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraTemperaturesRequestDescriptor = $convert.base64Decode('ChxHZXRDYW1lcmFUZW1wZXJhdHVyZXNSZXF1ZXN0');
@$core.Deprecated('Use cameraTemperatureDescriptor instead')
const CameraTemperature$json = const {
  '1': 'CameraTemperature',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'temperature', '3': 2, '4': 1, '5': 1, '10': 'temperature'},
  ],
};

/// Descriptor for `CameraTemperature`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cameraTemperatureDescriptor = $convert.base64Decode('ChFDYW1lcmFUZW1wZXJhdHVyZRIVCgZjYW1faWQYASABKAlSBWNhbUlkEiAKC3RlbXBlcmF0dXJlGAIgASgBUgt0ZW1wZXJhdHVyZQ==');
@$core.Deprecated('Use getCameraTemperaturesResponseDescriptor instead')
const GetCameraTemperaturesResponse$json = const {
  '1': 'GetCameraTemperaturesResponse',
  '2': const [
    const {'1': 'temperature', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.CameraTemperature', '10': 'temperature'},
  ],
};

/// Descriptor for `GetCameraTemperaturesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraTemperaturesResponseDescriptor = $convert.base64Decode('Ch1HZXRDYW1lcmFUZW1wZXJhdHVyZXNSZXNwb25zZRJFCgt0ZW1wZXJhdHVyZRgBIAMoCzIjLmN2LnJ1bnRpbWUucHJvdG8uQ2FtZXJhVGVtcGVyYXR1cmVSC3RlbXBlcmF0dXJl');
@$core.Deprecated('Use geoLLADescriptor instead')
const GeoLLA$json = const {
  '1': 'GeoLLA',
  '2': const [
    const {'1': 'lat', '3': 1, '4': 1, '5': 1, '9': 0, '10': 'lat', '17': true},
    const {'1': 'lng', '3': 2, '4': 1, '5': 1, '9': 1, '10': 'lng', '17': true},
    const {'1': 'alt', '3': 3, '4': 1, '5': 1, '9': 2, '10': 'alt', '17': true},
    const {'1': 'timestamp_ms', '3': 4, '4': 1, '5': 3, '9': 3, '10': 'timestampMs', '17': true},
  ],
  '8': const [
    const {'1': '_lat'},
    const {'1': '_lng'},
    const {'1': '_alt'},
    const {'1': '_timestamp_ms'},
  ],
};

/// Descriptor for `GeoLLA`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List geoLLADescriptor = $convert.base64Decode('CgZHZW9MTEESFQoDbGF0GAEgASgBSABSA2xhdIgBARIVCgNsbmcYAiABKAFIAVIDbG5niAEBEhUKA2FsdBgDIAEoAUgCUgNhbHSIAQESJgoMdGltZXN0YW1wX21zGAQgASgDSANSC3RpbWVzdGFtcE1ziAEBQgYKBF9sYXRCBgoEX2xuZ0IGCgRfYWx0Qg8KDV90aW1lc3RhbXBfbXM=');
@$core.Deprecated('Use geoECEFDescriptor instead')
const GeoECEF$json = const {
  '1': 'GeoECEF',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 1, '9': 0, '10': 'x', '17': true},
    const {'1': 'y', '3': 2, '4': 1, '5': 1, '9': 1, '10': 'y', '17': true},
    const {'1': 'z', '3': 3, '4': 1, '5': 1, '9': 2, '10': 'z', '17': true},
    const {'1': 'timestamp_ms', '3': 4, '4': 1, '5': 3, '9': 3, '10': 'timestampMs', '17': true},
  ],
  '8': const [
    const {'1': '_x'},
    const {'1': '_y'},
    const {'1': '_z'},
    const {'1': '_timestamp_ms'},
  ],
};

/// Descriptor for `GeoECEF`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List geoECEFDescriptor = $convert.base64Decode('CgdHZW9FQ0VGEhEKAXgYASABKAFIAFIBeIgBARIRCgF5GAIgASgBSAFSAXmIAQESEQoBehgDIAEoAUgCUgF6iAEBEiYKDHRpbWVzdGFtcF9tcxgEIAEoA0gDUgt0aW1lc3RhbXBNc4gBAUIECgJfeEIECgJfeUIECgJfekIPCg1fdGltZXN0YW1wX21z');
@$core.Deprecated('Use setGPSLocationRequestDescriptor instead')
const SetGPSLocationRequest$json = const {
  '1': 'SetGPSLocationRequest',
  '2': const [
    const {'1': 'lla', '3': 1, '4': 1, '5': 11, '6': '.cv.runtime.proto.GeoLLA', '10': 'lla'},
    const {'1': 'ecef', '3': 2, '4': 1, '5': 11, '6': '.cv.runtime.proto.GeoECEF', '10': 'ecef'},
  ],
};

/// Descriptor for `SetGPSLocationRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setGPSLocationRequestDescriptor = $convert.base64Decode('ChVTZXRHUFNMb2NhdGlvblJlcXVlc3QSKgoDbGxhGAEgASgLMhguY3YucnVudGltZS5wcm90by5HZW9MTEFSA2xsYRItCgRlY2VmGAIgASgLMhkuY3YucnVudGltZS5wcm90by5HZW9FQ0VGUgRlY2Vm');
@$core.Deprecated('Use setGPSLocationResponseDescriptor instead')
const SetGPSLocationResponse$json = const {
  '1': 'SetGPSLocationResponse',
};

/// Descriptor for `SetGPSLocationResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setGPSLocationResponseDescriptor = $convert.base64Decode('ChZTZXRHUFNMb2NhdGlvblJlc3BvbnNl');
@$core.Deprecated('Use setImplementStatusRequestDescriptor instead')
const SetImplementStatusRequest$json = const {
  '1': 'SetImplementStatusRequest',
  '2': const [
    const {'1': 'lifted', '3': 1, '4': 1, '5': 8, '10': 'lifted'},
    const {'1': 'estopped', '3': 2, '4': 1, '5': 8, '10': 'estopped'},
  ],
};

/// Descriptor for `SetImplementStatusRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImplementStatusRequestDescriptor = $convert.base64Decode('ChlTZXRJbXBsZW1lbnRTdGF0dXNSZXF1ZXN0EhYKBmxpZnRlZBgBIAEoCFIGbGlmdGVkEhoKCGVzdG9wcGVkGAIgASgIUghlc3RvcHBlZA==');
@$core.Deprecated('Use setImplementStatusResponseDescriptor instead')
const SetImplementStatusResponse$json = const {
  '1': 'SetImplementStatusResponse',
};

/// Descriptor for `SetImplementStatusResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImplementStatusResponseDescriptor = $convert.base64Decode('ChpTZXRJbXBsZW1lbnRTdGF0dXNSZXNwb25zZQ==');
@$core.Deprecated('Use setImageScoreRequestDescriptor instead')
const SetImageScoreRequest$json = const {
  '1': 'SetImageScoreRequest',
  '2': const [
    const {'1': 'score', '3': 1, '4': 1, '5': 1, '10': 'score'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'deepweed_output', '3': 4, '4': 1, '5': 11, '6': '.cv.runtime.proto.DeepweedOutput', '10': 'deepweedOutput'},
  ],
};

/// Descriptor for `SetImageScoreRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImageScoreRequestDescriptor = $convert.base64Decode('ChRTZXRJbWFnZVNjb3JlUmVxdWVzdBIUCgVzY29yZRgBIAEoAVIFc2NvcmUSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcxIVCgZjYW1faWQYAyABKAlSBWNhbUlkEkkKD2RlZXB3ZWVkX291dHB1dBgEIAEoCzIgLmN2LnJ1bnRpbWUucHJvdG8uRGVlcHdlZWRPdXRwdXRSDmRlZXB3ZWVkT3V0cHV0');
@$core.Deprecated('Use setImageScoreResponseDescriptor instead')
const SetImageScoreResponse$json = const {
  '1': 'SetImageScoreResponse',
};

/// Descriptor for `SetImageScoreResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setImageScoreResponseDescriptor = $convert.base64Decode('ChVTZXRJbWFnZVNjb3JlUmVzcG9uc2U=');
@$core.Deprecated('Use getScoreQueueRequestDescriptor instead')
const GetScoreQueueRequest$json = const {
  '1': 'GetScoreQueueRequest',
  '2': const [
    const {'1': 'score_type', '3': 1, '4': 1, '5': 9, '10': 'scoreType'},
  ],
};

/// Descriptor for `GetScoreQueueRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getScoreQueueRequestDescriptor = $convert.base64Decode('ChRHZXRTY29yZVF1ZXVlUmVxdWVzdBIdCgpzY29yZV90eXBlGAEgASgJUglzY29yZVR5cGU=');
@$core.Deprecated('Use scoreObjectDescriptor instead')
const ScoreObject$json = const {
  '1': 'ScoreObject',
  '2': const [
    const {'1': 'score', '3': 1, '4': 1, '5': 1, '10': 'score'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `ScoreObject`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List scoreObjectDescriptor = $convert.base64Decode('CgtTY29yZU9iamVjdBIUCgVzY29yZRgBIAEoAVIFc2NvcmUSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcxIVCgZjYW1faWQYAyABKAlSBWNhbUlk');
@$core.Deprecated('Use getScoreQueueResponseDescriptor instead')
const GetScoreQueueResponse$json = const {
  '1': 'GetScoreQueueResponse',
  '2': const [
    const {'1': 'score_object', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.ScoreObject', '10': 'scoreObject'},
  ],
};

/// Descriptor for `GetScoreQueueResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getScoreQueueResponseDescriptor = $convert.base64Decode('ChVHZXRTY29yZVF1ZXVlUmVzcG9uc2USQAoMc2NvcmVfb2JqZWN0GAEgAygLMh0uY3YucnVudGltZS5wcm90by5TY29yZU9iamVjdFILc2NvcmVPYmplY3Q=');
@$core.Deprecated('Use getMaxImageScoreRequestDescriptor instead')
const GetMaxImageScoreRequest$json = const {
  '1': 'GetMaxImageScoreRequest',
  '2': const [
    const {'1': 'score_type', '3': 1, '4': 1, '5': 9, '10': 'scoreType'},
  ],
};

/// Descriptor for `GetMaxImageScoreRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getMaxImageScoreRequestDescriptor = $convert.base64Decode('ChdHZXRNYXhJbWFnZVNjb3JlUmVxdWVzdBIdCgpzY29yZV90eXBlGAEgASgJUglzY29yZVR5cGU=');
@$core.Deprecated('Use getMaxImageScoreResponseDescriptor instead')
const GetMaxImageScoreResponse$json = const {
  '1': 'GetMaxImageScoreResponse',
  '2': const [
    const {'1': 'score', '3': 1, '4': 1, '5': 1, '10': 'score'},
    const {'1': 'type', '3': 2, '4': 1, '5': 9, '10': 'type'},
  ],
};

/// Descriptor for `GetMaxImageScoreResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getMaxImageScoreResponseDescriptor = $convert.base64Decode('ChhHZXRNYXhJbWFnZVNjb3JlUmVzcG9uc2USFAoFc2NvcmUYASABKAFSBXNjb3JlEhIKBHR5cGUYAiABKAlSBHR5cGU=');
@$core.Deprecated('Use getMaxScoredImageRequestDescriptor instead')
const GetMaxScoredImageRequest$json = const {
  '1': 'GetMaxScoredImageRequest',
  '2': const [
    const {'1': 'score_type', '3': 1, '4': 1, '5': 9, '10': 'scoreType'},
  ],
};

/// Descriptor for `GetMaxScoredImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getMaxScoredImageRequestDescriptor = $convert.base64Decode('ChhHZXRNYXhTY29yZWRJbWFnZVJlcXVlc3QSHQoKc2NvcmVfdHlwZRgBIAEoCVIJc2NvcmVUeXBl');
@$core.Deprecated('Use getLatestP2PImageRequestDescriptor instead')
const GetLatestP2PImageRequest$json = const {
  '1': 'GetLatestP2PImageRequest',
  '2': const [
    const {
      '1': 'score_type',
      '3': 1,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'scoreType',
    },
    const {'1': 'reason', '3': 2, '4': 1, '5': 14, '6': '.carbon.aimbot.cv.P2PCaptureReason', '10': 'reason'},
  ],
};

/// Descriptor for `GetLatestP2PImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getLatestP2PImageRequestDescriptor = $convert.base64Decode('ChhHZXRMYXRlc3RQMlBJbWFnZVJlcXVlc3QSIQoKc2NvcmVfdHlwZRgBIAEoCUICGAFSCXNjb3JlVHlwZRI6CgZyZWFzb24YAiABKA4yIi5jYXJib24uYWltYm90LmN2LlAyUENhcHR1cmVSZWFzb25SBnJlYXNvbg==');
@$core.Deprecated('Use getLatestImageRequestDescriptor instead')
const GetLatestImageRequest$json = const {
  '1': 'GetLatestImageRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `GetLatestImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getLatestImageRequestDescriptor = $convert.base64Decode('ChVHZXRMYXRlc3RJbWFnZVJlcXVlc3QSFQoGY2FtX2lkGAEgASgJUgVjYW1JZA==');
@$core.Deprecated('Use getImageNearTimestampRequestDescriptor instead')
const GetImageNearTimestampRequest$json = const {
  '1': 'GetImageNearTimestampRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `GetImageNearTimestampRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getImageNearTimestampRequestDescriptor = $convert.base64Decode('ChxHZXRJbWFnZU5lYXJUaW1lc3RhbXBSZXF1ZXN0EhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcw==');
@$core.Deprecated('Use getChipImageRequestDescriptor instead')
const GetChipImageRequest$json = const {
  '1': 'GetChipImageRequest',
  '2': const [
    const {'1': 'score_type', '3': 1, '4': 1, '5': 9, '10': 'scoreType'},
  ],
};

/// Descriptor for `GetChipImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getChipImageRequestDescriptor = $convert.base64Decode('ChNHZXRDaGlwSW1hZ2VSZXF1ZXN0Eh0KCnNjb3JlX3R5cGUYASABKAlSCXNjb3JlVHlwZQ==');
@$core.Deprecated('Use flushQueuesRequestDescriptor instead')
const FlushQueuesRequest$json = const {
  '1': 'FlushQueuesRequest',
  '2': const [
    const {'1': 'score_type', '3': 1, '4': 3, '5': 9, '10': 'scoreType'},
    const {'1': 'score_queue_type', '3': 2, '4': 1, '5': 14, '6': '.cv.runtime.proto.ScoreQueueType', '9': 0, '10': 'scoreQueueType', '17': true},
  ],
  '8': const [
    const {'1': '_score_queue_type'},
  ],
};

/// Descriptor for `FlushQueuesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List flushQueuesRequestDescriptor = $convert.base64Decode('ChJGbHVzaFF1ZXVlc1JlcXVlc3QSHQoKc2NvcmVfdHlwZRgBIAMoCVIJc2NvcmVUeXBlEk8KEHNjb3JlX3F1ZXVlX3R5cGUYAiABKA4yIC5jdi5ydW50aW1lLnByb3RvLlNjb3JlUXVldWVUeXBlSABSDnNjb3JlUXVldWVUeXBliAEBQhMKEV9zY29yZV9xdWV1ZV90eXBl');
@$core.Deprecated('Use flushQueuesResponseDescriptor instead')
const FlushQueuesResponse$json = const {
  '1': 'FlushQueuesResponse',
};

/// Descriptor for `FlushQueuesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List flushQueuesResponseDescriptor = $convert.base64Decode('ChNGbHVzaFF1ZXVlc1Jlc3BvbnNl');
@$core.Deprecated('Use imageAndMetadataResponseDescriptor instead')
const ImageAndMetadataResponse$json = const {
  '1': 'ImageAndMetadataResponse',
  '2': const [
    const {'1': 'bytes', '3': 1, '4': 1, '5': 12, '10': 'bytes'},
    const {'1': 'width', '3': 2, '4': 1, '5': 5, '10': 'width'},
    const {'1': 'height', '3': 3, '4': 1, '5': 5, '10': 'height'},
    const {'1': 'timestamp_ms', '3': 4, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'score', '3': 5, '4': 1, '5': 1, '10': 'score'},
    const {'1': 'cam_id', '3': 6, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'iso_formatted_time', '3': 7, '4': 1, '5': 9, '10': 'isoFormattedTime'},
    const {'1': 'lla_lat', '3': 8, '4': 1, '5': 1, '10': 'llaLat'},
    const {'1': 'lla_lng', '3': 9, '4': 1, '5': 1, '10': 'llaLng'},
    const {'1': 'lla_alt', '3': 10, '4': 1, '5': 1, '10': 'llaAlt'},
    const {'1': 'lla_timestamp_ms', '3': 11, '4': 1, '5': 3, '10': 'llaTimestampMs'},
    const {'1': 'ecef_x', '3': 12, '4': 1, '5': 1, '10': 'ecefX'},
    const {'1': 'ecef_y', '3': 13, '4': 1, '5': 1, '10': 'ecefY'},
    const {'1': 'ecef_z', '3': 14, '4': 1, '5': 1, '10': 'ecefZ'},
    const {'1': 'ecef_timestamp_ms', '3': 15, '4': 1, '5': 3, '10': 'ecefTimestampMs'},
    const {'1': 'ppi', '3': 16, '4': 1, '5': 2, '10': 'ppi'},
    const {'1': 'score_type', '3': 17, '4': 1, '5': 9, '9': 0, '10': 'scoreType', '17': true},
    const {'1': 'image_type', '3': 18, '4': 1, '5': 9, '10': 'imageType'},
    const {'1': 'model_url', '3': 19, '4': 1, '5': 9, '10': 'modelUrl'},
    const {'1': 'crop', '3': 20, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'weed_height_columns', '3': 21, '4': 3, '5': 1, '10': 'weedHeightColumns'},
    const {'1': 'crop_height_columns', '3': 22, '4': 3, '5': 1, '10': 'cropHeightColumns'},
    const {'1': 'bbh_offset_mm', '3': 23, '4': 1, '5': 1, '10': 'bbhOffsetMm'},
    const {'1': 'focus_metric', '3': 24, '4': 1, '5': 1, '10': 'focusMetric'},
    const {'1': 'exposure_us', '3': 25, '4': 1, '5': 1, '10': 'exposureUs'},
    const {'1': 'crop_point_threshold', '3': 26, '4': 1, '5': 1, '10': 'cropPointThreshold'},
    const {'1': 'weed_point_threshold', '3': 27, '4': 1, '5': 1, '10': 'weedPointThreshold'},
    const {'1': 'weeding_enabled', '3': 28, '4': 1, '5': 8, '10': 'weedingEnabled'},
    const {'1': 'thinning_enabled', '3': 29, '4': 1, '5': 8, '10': 'thinningEnabled'},
    const {'1': 'deepweed_id', '3': 30, '4': 1, '5': 9, '10': 'deepweedId'},
    const {'1': 'p2p_id', '3': 31, '4': 1, '5': 9, '10': 'p2pId'},
    const {'1': 'deepweed_detections', '3': 32, '4': 3, '5': 11, '6': '.weed_tracking.Detection', '10': 'deepweedDetections'},
    const {'1': 'segmentation_threshold', '3': 33, '4': 1, '5': 1, '10': 'segmentationThreshold'},
    const {'1': 'simulator_generated', '3': 34, '4': 1, '5': 8, '10': 'simulatorGenerated'},
    const {'1': 'gain_db', '3': 35, '4': 1, '5': 1, '10': 'gainDb'},
  ],
  '8': const [
    const {'1': '_score_type'},
  ],
};

/// Descriptor for `ImageAndMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imageAndMetadataResponseDescriptor = $convert.base64Decode('ChhJbWFnZUFuZE1ldGFkYXRhUmVzcG9uc2USFAoFYnl0ZXMYASABKAxSBWJ5dGVzEhQKBXdpZHRoGAIgASgFUgV3aWR0aBIWCgZoZWlnaHQYAyABKAVSBmhlaWdodBIhCgx0aW1lc3RhbXBfbXMYBCABKANSC3RpbWVzdGFtcE1zEhQKBXNjb3JlGAUgASgBUgVzY29yZRIVCgZjYW1faWQYBiABKAlSBWNhbUlkEiwKEmlzb19mb3JtYXR0ZWRfdGltZRgHIAEoCVIQaXNvRm9ybWF0dGVkVGltZRIXCgdsbGFfbGF0GAggASgBUgZsbGFMYXQSFwoHbGxhX2xuZxgJIAEoAVIGbGxhTG5nEhcKB2xsYV9hbHQYCiABKAFSBmxsYUFsdBIoChBsbGFfdGltZXN0YW1wX21zGAsgASgDUg5sbGFUaW1lc3RhbXBNcxIVCgZlY2VmX3gYDCABKAFSBWVjZWZYEhUKBmVjZWZfeRgNIAEoAVIFZWNlZlkSFQoGZWNlZl96GA4gASgBUgVlY2VmWhIqChFlY2VmX3RpbWVzdGFtcF9tcxgPIAEoA1IPZWNlZlRpbWVzdGFtcE1zEhAKA3BwaRgQIAEoAlIDcHBpEiIKCnNjb3JlX3R5cGUYESABKAlIAFIJc2NvcmVUeXBliAEBEh0KCmltYWdlX3R5cGUYEiABKAlSCWltYWdlVHlwZRIbCgltb2RlbF91cmwYEyABKAlSCG1vZGVsVXJsEhIKBGNyb3AYFCABKAlSBGNyb3ASLgoTd2VlZF9oZWlnaHRfY29sdW1ucxgVIAMoAVIRd2VlZEhlaWdodENvbHVtbnMSLgoTY3JvcF9oZWlnaHRfY29sdW1ucxgWIAMoAVIRY3JvcEhlaWdodENvbHVtbnMSIgoNYmJoX29mZnNldF9tbRgXIAEoAVILYmJoT2Zmc2V0TW0SIQoMZm9jdXNfbWV0cmljGBggASgBUgtmb2N1c01ldHJpYxIfCgtleHBvc3VyZV91cxgZIAEoAVIKZXhwb3N1cmVVcxIwChRjcm9wX3BvaW50X3RocmVzaG9sZBgaIAEoAVISY3JvcFBvaW50VGhyZXNob2xkEjAKFHdlZWRfcG9pbnRfdGhyZXNob2xkGBsgASgBUhJ3ZWVkUG9pbnRUaHJlc2hvbGQSJwoPd2VlZGluZ19lbmFibGVkGBwgASgIUg53ZWVkaW5nRW5hYmxlZBIpChB0aGlubmluZ19lbmFibGVkGB0gASgIUg90aGlubmluZ0VuYWJsZWQSHwoLZGVlcHdlZWRfaWQYHiABKAlSCmRlZXB3ZWVkSWQSFQoGcDJwX2lkGB8gASgJUgVwMnBJZBJJChNkZWVwd2VlZF9kZXRlY3Rpb25zGCAgAygLMhgud2VlZF90cmFja2luZy5EZXRlY3Rpb25SEmRlZXB3ZWVkRGV0ZWN0aW9ucxI1ChZzZWdtZW50YXRpb25fdGhyZXNob2xkGCEgASgBUhVzZWdtZW50YXRpb25UaHJlc2hvbGQSLwoTc2ltdWxhdG9yX2dlbmVyYXRlZBgiIAEoCFISc2ltdWxhdG9yR2VuZXJhdGVkEhcKB2dhaW5fZGIYIyABKAFSBmdhaW5EYkINCgtfc2NvcmVfdHlwZQ==');
@$core.Deprecated('Use chipPredictionDescriptor instead')
const ChipPrediction$json = const {
  '1': 'ChipPrediction',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 1, '10': 'x'},
    const {'1': 'y', '3': 2, '4': 1, '5': 1, '10': 'y'},
    const {'1': 'radius', '3': 3, '4': 1, '5': 1, '10': 'radius'},
    const {'1': 'model_id', '3': 4, '4': 1, '5': 9, '10': 'modelId'},
    const {'1': 'deepweed_category_scores', '3': 5, '4': 3, '5': 11, '6': '.weed_tracking.CategoryPrediction', '10': 'deepweedCategoryScores'},
    const {'1': 'embedding_similarity_scores', '3': 8, '4': 3, '5': 11, '6': '.weed_tracking.CategoryPrediction', '10': 'embeddingSimilarityScores'},
  ],
  '9': const [
    const {'1': 6, '2': 7},
    const {'1': 7, '2': 8},
  ],
};

/// Descriptor for `ChipPrediction`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List chipPredictionDescriptor = $convert.base64Decode('Cg5DaGlwUHJlZGljdGlvbhIMCgF4GAEgASgBUgF4EgwKAXkYAiABKAFSAXkSFgoGcmFkaXVzGAMgASgBUgZyYWRpdXMSGQoIbW9kZWxfaWQYBCABKAlSB21vZGVsSWQSWwoYZGVlcHdlZWRfY2F0ZWdvcnlfc2NvcmVzGAUgAygLMiEud2VlZF90cmFja2luZy5DYXRlZ29yeVByZWRpY3Rpb25SFmRlZXB3ZWVkQ2F0ZWdvcnlTY29yZXMSYQobZW1iZWRkaW5nX3NpbWlsYXJpdHlfc2NvcmVzGAggAygLMiEud2VlZF90cmFja2luZy5DYXRlZ29yeVByZWRpY3Rpb25SGWVtYmVkZGluZ1NpbWlsYXJpdHlTY29yZXNKBAgGEAdKBAgHEAg=');
@$core.Deprecated('Use chipImageAndMetadataResponseDescriptor instead')
const ChipImageAndMetadataResponse$json = const {
  '1': 'ChipImageAndMetadataResponse',
  '2': const [
    const {'1': 'image_and_metadata', '3': 1, '4': 1, '5': 11, '6': '.cv.runtime.proto.ImageAndMetadataResponse', '10': 'imageAndMetadata'},
    const {'1': 'prediction_metadata', '3': 2, '4': 1, '5': 11, '6': '.cv.runtime.proto.ChipPrediction', '10': 'predictionMetadata'},
  ],
};

/// Descriptor for `ChipImageAndMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List chipImageAndMetadataResponseDescriptor = $convert.base64Decode('ChxDaGlwSW1hZ2VBbmRNZXRhZGF0YVJlc3BvbnNlElgKEmltYWdlX2FuZF9tZXRhZGF0YRgBIAEoCzIqLmN2LnJ1bnRpbWUucHJvdG8uSW1hZ2VBbmRNZXRhZGF0YVJlc3BvbnNlUhBpbWFnZUFuZE1ldGFkYXRhElEKE3ByZWRpY3Rpb25fbWV0YWRhdGEYAiABKAsyIC5jdi5ydW50aW1lLnByb3RvLkNoaXBQcmVkaWN0aW9uUhJwcmVkaWN0aW9uTWV0YWRhdGE=');
@$core.Deprecated('Use chipQueueInformationRequestDescriptor instead')
const ChipQueueInformationRequest$json = const {
  '1': 'ChipQueueInformationRequest',
};

/// Descriptor for `ChipQueueInformationRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List chipQueueInformationRequestDescriptor = $convert.base64Decode('ChtDaGlwUXVldWVJbmZvcm1hdGlvblJlcXVlc3Q=');
@$core.Deprecated('Use chipQueueInformationResponseDescriptor instead')
const ChipQueueInformationResponse$json = const {
  '1': 'ChipQueueInformationResponse',
  '2': const [
    const {'1': 'queue_score', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.CategoryPrediction', '10': 'queueScore'},
  ],
};

/// Descriptor for `ChipQueueInformationResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List chipQueueInformationResponseDescriptor = $convert.base64Decode('ChxDaGlwUXVldWVJbmZvcm1hdGlvblJlc3BvbnNlEkIKC3F1ZXVlX3Njb3JlGAEgAygLMiEud2VlZF90cmFja2luZy5DYXRlZ29yeVByZWRpY3Rpb25SCnF1ZXVlU2NvcmU=');
@$core.Deprecated('Use p2PImageAndMetadataResponseDescriptor instead')
const P2PImageAndMetadataResponse$json = const {
  '1': 'P2PImageAndMetadataResponse',
  '2': const [
    const {'1': 'target_bytes', '3': 1, '4': 1, '5': 12, '10': 'targetBytes'},
    const {'1': 'target_width', '3': 2, '4': 1, '5': 5, '10': 'targetWidth'},
    const {'1': 'target_height', '3': 3, '4': 1, '5': 5, '10': 'targetHeight'},
    const {'1': 'perspective_bytes', '3': 4, '4': 1, '5': 12, '10': 'perspectiveBytes'},
    const {'1': 'perspective_width', '3': 5, '4': 1, '5': 5, '10': 'perspectiveWidth'},
    const {'1': 'perspective_height', '3': 6, '4': 1, '5': 5, '10': 'perspectiveHeight'},
    const {'1': 'annotated_target_bytes', '3': 7, '4': 1, '5': 12, '10': 'annotatedTargetBytes'},
    const {'1': 'annotated_target_width', '3': 8, '4': 1, '5': 5, '10': 'annotatedTargetWidth'},
    const {'1': 'annotated_target_height', '3': 9, '4': 1, '5': 5, '10': 'annotatedTargetHeight'},
    const {'1': 'timestamp_ms', '3': 10, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'score', '3': 11, '4': 1, '5': 1, '10': 'score'},
    const {'1': 'cam_id', '3': 12, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'iso_formatted_time', '3': 13, '4': 1, '5': 9, '10': 'isoFormattedTime'},
    const {'1': 'lla_lat', '3': 14, '4': 1, '5': 1, '10': 'llaLat'},
    const {'1': 'lla_lng', '3': 15, '4': 1, '5': 1, '10': 'llaLng'},
    const {'1': 'lla_alt', '3': 16, '4': 1, '5': 1, '10': 'llaAlt'},
    const {'1': 'lla_timestamp_ms', '3': 17, '4': 1, '5': 3, '10': 'llaTimestampMs'},
    const {'1': 'ecef_x', '3': 18, '4': 1, '5': 1, '10': 'ecefX'},
    const {'1': 'ecef_y', '3': 19, '4': 1, '5': 1, '10': 'ecefY'},
    const {'1': 'ecef_z', '3': 20, '4': 1, '5': 1, '10': 'ecefZ'},
    const {'1': 'ecef_timestamp_ms', '3': 21, '4': 1, '5': 3, '10': 'ecefTimestampMs'},
    const {'1': 'ppi', '3': 22, '4': 1, '5': 2, '10': 'ppi'},
    const {'1': 'perspective_ppi', '3': 23, '4': 1, '5': 2, '10': 'perspectivePpi'},
    const {'1': 'image_type', '3': 25, '4': 1, '5': 9, '10': 'imageType'},
    const {'1': 'model_url', '3': 26, '4': 1, '5': 9, '10': 'modelUrl'},
    const {'1': 'crop', '3': 27, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'focus_metric', '3': 28, '4': 1, '5': 1, '10': 'focusMetric'},
    const {'1': 'exposure_us', '3': 29, '4': 1, '5': 1, '10': 'exposureUs'},
    const {'1': 'weeding_enabled', '3': 30, '4': 1, '5': 8, '10': 'weedingEnabled'},
    const {'1': 'thinning_enabled', '3': 31, '4': 1, '5': 8, '10': 'thinningEnabled'},
    const {'1': 'deepweed_id', '3': 32, '4': 1, '5': 9, '10': 'deepweedId'},
    const {'1': 'p2p_id', '3': 33, '4': 1, '5': 9, '10': 'p2pId'},
  ],
};

/// Descriptor for `P2PImageAndMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PImageAndMetadataResponseDescriptor = $convert.base64Decode('ChtQMlBJbWFnZUFuZE1ldGFkYXRhUmVzcG9uc2USIQoMdGFyZ2V0X2J5dGVzGAEgASgMUgt0YXJnZXRCeXRlcxIhCgx0YXJnZXRfd2lkdGgYAiABKAVSC3RhcmdldFdpZHRoEiMKDXRhcmdldF9oZWlnaHQYAyABKAVSDHRhcmdldEhlaWdodBIrChFwZXJzcGVjdGl2ZV9ieXRlcxgEIAEoDFIQcGVyc3BlY3RpdmVCeXRlcxIrChFwZXJzcGVjdGl2ZV93aWR0aBgFIAEoBVIQcGVyc3BlY3RpdmVXaWR0aBItChJwZXJzcGVjdGl2ZV9oZWlnaHQYBiABKAVSEXBlcnNwZWN0aXZlSGVpZ2h0EjQKFmFubm90YXRlZF90YXJnZXRfYnl0ZXMYByABKAxSFGFubm90YXRlZFRhcmdldEJ5dGVzEjQKFmFubm90YXRlZF90YXJnZXRfd2lkdGgYCCABKAVSFGFubm90YXRlZFRhcmdldFdpZHRoEjYKF2Fubm90YXRlZF90YXJnZXRfaGVpZ2h0GAkgASgFUhVhbm5vdGF0ZWRUYXJnZXRIZWlnaHQSIQoMdGltZXN0YW1wX21zGAogASgDUgt0aW1lc3RhbXBNcxIUCgVzY29yZRgLIAEoAVIFc2NvcmUSFQoGY2FtX2lkGAwgASgJUgVjYW1JZBIsChJpc29fZm9ybWF0dGVkX3RpbWUYDSABKAlSEGlzb0Zvcm1hdHRlZFRpbWUSFwoHbGxhX2xhdBgOIAEoAVIGbGxhTGF0EhcKB2xsYV9sbmcYDyABKAFSBmxsYUxuZxIXCgdsbGFfYWx0GBAgASgBUgZsbGFBbHQSKAoQbGxhX3RpbWVzdGFtcF9tcxgRIAEoA1IObGxhVGltZXN0YW1wTXMSFQoGZWNlZl94GBIgASgBUgVlY2VmWBIVCgZlY2VmX3kYEyABKAFSBWVjZWZZEhUKBmVjZWZfehgUIAEoAVIFZWNlZloSKgoRZWNlZl90aW1lc3RhbXBfbXMYFSABKANSD2VjZWZUaW1lc3RhbXBNcxIQCgNwcGkYFiABKAJSA3BwaRInCg9wZXJzcGVjdGl2ZV9wcGkYFyABKAJSDnBlcnNwZWN0aXZlUHBpEh0KCmltYWdlX3R5cGUYGSABKAlSCWltYWdlVHlwZRIbCgltb2RlbF91cmwYGiABKAlSCG1vZGVsVXJsEhIKBGNyb3AYGyABKAlSBGNyb3ASIQoMZm9jdXNfbWV0cmljGBwgASgBUgtmb2N1c01ldHJpYxIfCgtleHBvc3VyZV91cxgdIAEoAVIKZXhwb3N1cmVVcxInCg93ZWVkaW5nX2VuYWJsZWQYHiABKAhSDndlZWRpbmdFbmFibGVkEikKEHRoaW5uaW5nX2VuYWJsZWQYHyABKAhSD3RoaW5uaW5nRW5hYmxlZBIfCgtkZWVwd2VlZF9pZBggIAEoCVIKZGVlcHdlZWRJZBIVCgZwMnBfaWQYISABKAlSBXAycElk');
@$core.Deprecated('Use getCameraInfoRequestDescriptor instead')
const GetCameraInfoRequest$json = const {
  '1': 'GetCameraInfoRequest',
};

/// Descriptor for `GetCameraInfoRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraInfoRequestDescriptor = $convert.base64Decode('ChRHZXRDYW1lcmFJbmZvUmVxdWVzdA==');
@$core.Deprecated('Use cameraInfoDescriptor instead')
const CameraInfo$json = const {
  '1': 'CameraInfo',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'ip_address', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'ipAddress', '17': true},
    const {'1': 'serial_number', '3': 3, '4': 1, '5': 9, '9': 1, '10': 'serialNumber', '17': true},
    const {'1': 'model', '3': 4, '4': 1, '5': 9, '10': 'model'},
    const {'1': 'width', '3': 5, '4': 1, '5': 13, '10': 'width'},
    const {'1': 'height', '3': 6, '4': 1, '5': 13, '10': 'height'},
    const {'1': 'connected', '3': 7, '4': 1, '5': 8, '10': 'connected'},
    const {'1': 'link_speed', '3': 8, '4': 1, '5': 4, '10': 'linkSpeed'},
    const {'1': 'error_type', '3': 9, '4': 1, '5': 14, '6': '.cv.runtime.proto.ErrorType', '10': 'errorType'},
    const {'1': 'v4l2_device_id', '3': 10, '4': 1, '5': 9, '9': 2, '10': 'v4l2DeviceId', '17': true},
    const {'1': 'firmware_version', '3': 11, '4': 1, '5': 9, '10': 'firmwareVersion'},
    const {'1': 'latest_firmware_version', '3': 12, '4': 1, '5': 9, '9': 3, '10': 'latestFirmwareVersion', '17': true},
  ],
  '8': const [
    const {'1': '_ip_address'},
    const {'1': '_serial_number'},
    const {'1': '_v4l2_device_id'},
    const {'1': '_latest_firmware_version'},
  ],
};

/// Descriptor for `CameraInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cameraInfoDescriptor = $convert.base64Decode('CgpDYW1lcmFJbmZvEhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSIgoKaXBfYWRkcmVzcxgCIAEoCUgAUglpcEFkZHJlc3OIAQESKAoNc2VyaWFsX251bWJlchgDIAEoCUgBUgxzZXJpYWxOdW1iZXKIAQESFAoFbW9kZWwYBCABKAlSBW1vZGVsEhQKBXdpZHRoGAUgASgNUgV3aWR0aBIWCgZoZWlnaHQYBiABKA1SBmhlaWdodBIcCgljb25uZWN0ZWQYByABKAhSCWNvbm5lY3RlZBIdCgpsaW5rX3NwZWVkGAggASgEUglsaW5rU3BlZWQSOgoKZXJyb3JfdHlwZRgJIAEoDjIbLmN2LnJ1bnRpbWUucHJvdG8uRXJyb3JUeXBlUgllcnJvclR5cGUSKQoOdjRsMl9kZXZpY2VfaWQYCiABKAlIAlIMdjRsMkRldmljZUlkiAEBEikKEGZpcm13YXJlX3ZlcnNpb24YCyABKAlSD2Zpcm13YXJlVmVyc2lvbhI7ChdsYXRlc3RfZmlybXdhcmVfdmVyc2lvbhgMIAEoCUgDUhVsYXRlc3RGaXJtd2FyZVZlcnNpb26IAQFCDQoLX2lwX2FkZHJlc3NCEAoOX3NlcmlhbF9udW1iZXJCEQoPX3Y0bDJfZGV2aWNlX2lkQhoKGF9sYXRlc3RfZmlybXdhcmVfdmVyc2lvbg==');
@$core.Deprecated('Use getCameraInfoResponseDescriptor instead')
const GetCameraInfoResponse$json = const {
  '1': 'GetCameraInfoResponse',
  '2': const [
    const {'1': 'camera_info', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.CameraInfo', '10': 'cameraInfo'},
  ],
};

/// Descriptor for `GetCameraInfoResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCameraInfoResponseDescriptor = $convert.base64Decode('ChVHZXRDYW1lcmFJbmZvUmVzcG9uc2USPQoLY2FtZXJhX2luZm8YASADKAsyHC5jdi5ydW50aW1lLnByb3RvLkNhbWVyYUluZm9SCmNhbWVyYUluZm8=');
@$core.Deprecated('Use getLightweightBurstRecordRequestDescriptor instead')
const GetLightweightBurstRecordRequest$json = const {
  '1': 'GetLightweightBurstRecordRequest',
};

/// Descriptor for `GetLightweightBurstRecordRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getLightweightBurstRecordRequestDescriptor = $convert.base64Decode('CiBHZXRMaWdodHdlaWdodEJ1cnN0UmVjb3JkUmVxdWVzdA==');
@$core.Deprecated('Use getLightweightBurstRecordResponseDescriptor instead')
const GetLightweightBurstRecordResponse$json = const {
  '1': 'GetLightweightBurstRecordResponse',
  '2': const [
    const {'1': 'zip_file', '3': 1, '4': 1, '5': 12, '10': 'zipFile'},
    const {'1': 'metadata_file', '3': 2, '4': 1, '5': 12, '10': 'metadataFile'},
  ],
};

/// Descriptor for `GetLightweightBurstRecordResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getLightweightBurstRecordResponseDescriptor = $convert.base64Decode('CiFHZXRMaWdodHdlaWdodEJ1cnN0UmVjb3JkUmVzcG9uc2USGQoIemlwX2ZpbGUYASABKAxSB3ppcEZpbGUSIwoNbWV0YWRhdGFfZmlsZRgCIAEoDFIMbWV0YWRhdGFGaWxl');
@$core.Deprecated('Use getBootedRequestDescriptor instead')
const GetBootedRequest$json = const {
  '1': 'GetBootedRequest',
};

/// Descriptor for `GetBootedRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getBootedRequestDescriptor = $convert.base64Decode('ChBHZXRCb290ZWRSZXF1ZXN0');
@$core.Deprecated('Use getBootedResponseDescriptor instead')
const GetBootedResponse$json = const {
  '1': 'GetBootedResponse',
  '2': const [
    const {'1': 'booted', '3': 1, '4': 1, '5': 8, '10': 'booted'},
  ],
};

/// Descriptor for `GetBootedResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getBootedResponseDescriptor = $convert.base64Decode('ChFHZXRCb290ZWRSZXNwb25zZRIWCgZib290ZWQYASABKAhSBmJvb3RlZA==');
@$core.Deprecated('Use getReadyRequestDescriptor instead')
const GetReadyRequest$json = const {
  '1': 'GetReadyRequest',
};

/// Descriptor for `GetReadyRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getReadyRequestDescriptor = $convert.base64Decode('Cg9HZXRSZWFkeVJlcXVlc3Q=');
@$core.Deprecated('Use getReadyResponseDescriptor instead')
const GetReadyResponse$json = const {
  '1': 'GetReadyResponse',
  '2': const [
    const {'1': 'ready', '3': 1, '4': 1, '5': 8, '10': 'ready'},
    const {'1': 'deepweed_ready_state', '3': 2, '4': 3, '5': 11, '6': '.cv.runtime.proto.GetReadyResponse.DeepweedReadyStateEntry', '10': 'deepweedReadyState'},
    const {'1': 'p2p_ready_state', '3': 3, '4': 3, '5': 11, '6': '.cv.runtime.proto.GetReadyResponse.P2pReadyStateEntry', '10': 'p2pReadyState'},
    const {'1': 'booted', '3': 4, '4': 1, '5': 8, '10': 'booted'},
  ],
  '3': const [GetReadyResponse_DeepweedReadyStateEntry$json, GetReadyResponse_P2pReadyStateEntry$json],
};

@$core.Deprecated('Use getReadyResponseDescriptor instead')
const GetReadyResponse_DeepweedReadyStateEntry$json = const {
  '1': 'DeepweedReadyStateEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 8, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use getReadyResponseDescriptor instead')
const GetReadyResponse_P2pReadyStateEntry$json = const {
  '1': 'P2pReadyStateEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 8, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `GetReadyResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getReadyResponseDescriptor = $convert.base64Decode('ChBHZXRSZWFkeVJlc3BvbnNlEhQKBXJlYWR5GAEgASgIUgVyZWFkeRJsChRkZWVwd2VlZF9yZWFkeV9zdGF0ZRgCIAMoCzI6LmN2LnJ1bnRpbWUucHJvdG8uR2V0UmVhZHlSZXNwb25zZS5EZWVwd2VlZFJlYWR5U3RhdGVFbnRyeVISZGVlcHdlZWRSZWFkeVN0YXRlEl0KD3AycF9yZWFkeV9zdGF0ZRgDIAMoCzI1LmN2LnJ1bnRpbWUucHJvdG8uR2V0UmVhZHlSZXNwb25zZS5QMnBSZWFkeVN0YXRlRW50cnlSDXAycFJlYWR5U3RhdGUSFgoGYm9vdGVkGAQgASgIUgZib290ZWQaRQoXRGVlcHdlZWRSZWFkeVN0YXRlRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAiABKAhSBXZhbHVlOgI4ARpAChJQMnBSZWFkeVN0YXRlRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAiABKAhSBXZhbHVlOgI4AQ==');
@$core.Deprecated('Use getErrorStateResponseDescriptor instead')
const GetErrorStateResponse$json = const {
  '1': 'GetErrorStateResponse',
  '2': const [
    const {'1': 'plant_profile_error', '3': 1, '4': 1, '5': 8, '10': 'plantProfileError'},
    const {'1': 'model_unsupported_embeddings', '3': 2, '4': 1, '5': 8, '10': 'modelUnsupportedEmbeddings'},
  ],
};

/// Descriptor for `GetErrorStateResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getErrorStateResponseDescriptor = $convert.base64Decode('ChVHZXRFcnJvclN0YXRlUmVzcG9uc2USLgoTcGxhbnRfcHJvZmlsZV9lcnJvchgBIAEoCFIRcGxhbnRQcm9maWxlRXJyb3ISQAocbW9kZWxfdW5zdXBwb3J0ZWRfZW1iZWRkaW5ncxgCIAEoCFIabW9kZWxVbnN1cHBvcnRlZEVtYmVkZGluZ3M=');
@$core.Deprecated('Use deepweedDetectionDescriptor instead')
const DeepweedDetection$json = const {
  '1': 'DeepweedDetection',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 2, '10': 'x'},
    const {'1': 'y', '3': 2, '4': 1, '5': 2, '10': 'y'},
    const {'1': 'size', '3': 3, '4': 1, '5': 2, '10': 'size'},
    const {'1': 'score', '3': 4, '4': 1, '5': 2, '10': 'score'},
    const {'1': 'hit_class', '3': 6, '4': 1, '5': 14, '6': '.cv.runtime.proto.HitClass', '10': 'hitClass'},
    const {'1': 'mask_intersections', '3': 7, '4': 3, '5': 13, '10': 'maskIntersections'},
    const {'1': 'weed_score', '3': 8, '4': 1, '5': 2, '10': 'weedScore'},
    const {'1': 'crop_score', '3': 9, '4': 1, '5': 2, '10': 'cropScore'},
    const {'1': 'weed_detection_class_scores', '3': 10, '4': 3, '5': 2, '10': 'weedDetectionClassScores'},
    const {'1': 'embedding', '3': 11, '4': 3, '5': 2, '10': 'embedding'},
    const {'1': 'plant_score', '3': 12, '4': 1, '5': 2, '10': 'plantScore'},
    const {'1': 'embedding_category_distances', '3': 13, '4': 3, '5': 2, '10': 'embeddingCategoryDistances'},
    const {'1': 'embedding_similarity_scores', '3': 15, '4': 3, '5': 11, '6': '.weed_tracking.CategoryPrediction', '10': 'embeddingSimilarityScores'},
    const {'1': 'detection_id', '3': 14, '4': 1, '5': 13, '10': 'detectionId'},
  ],
};

/// Descriptor for `DeepweedDetection`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deepweedDetectionDescriptor = $convert.base64Decode('ChFEZWVwd2VlZERldGVjdGlvbhIMCgF4GAEgASgCUgF4EgwKAXkYAiABKAJSAXkSEgoEc2l6ZRgDIAEoAlIEc2l6ZRIUCgVzY29yZRgEIAEoAlIFc2NvcmUSNwoJaGl0X2NsYXNzGAYgASgOMhouY3YucnVudGltZS5wcm90by5IaXRDbGFzc1IIaGl0Q2xhc3MSLQoSbWFza19pbnRlcnNlY3Rpb25zGAcgAygNUhFtYXNrSW50ZXJzZWN0aW9ucxIdCgp3ZWVkX3Njb3JlGAggASgCUgl3ZWVkU2NvcmUSHQoKY3JvcF9zY29yZRgJIAEoAlIJY3JvcFNjb3JlEj0KG3dlZWRfZGV0ZWN0aW9uX2NsYXNzX3Njb3JlcxgKIAMoAlIYd2VlZERldGVjdGlvbkNsYXNzU2NvcmVzEhwKCWVtYmVkZGluZxgLIAMoAlIJZW1iZWRkaW5nEh8KC3BsYW50X3Njb3JlGAwgASgCUgpwbGFudFNjb3JlEkAKHGVtYmVkZGluZ19jYXRlZ29yeV9kaXN0YW5jZXMYDSADKAJSGmVtYmVkZGluZ0NhdGVnb3J5RGlzdGFuY2VzEmEKG2VtYmVkZGluZ19zaW1pbGFyaXR5X3Njb3JlcxgPIAMoCzIhLndlZWRfdHJhY2tpbmcuQ2F0ZWdvcnlQcmVkaWN0aW9uUhllbWJlZGRpbmdTaW1pbGFyaXR5U2NvcmVzEiEKDGRldGVjdGlvbl9pZBgOIAEoDVILZGV0ZWN0aW9uSWQ=');
@$core.Deprecated('Use deepweedOutputDescriptor instead')
const DeepweedOutput$json = const {
  '1': 'DeepweedOutput',
  '2': const [
    const {'1': 'detections', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.DeepweedDetection', '10': 'detections'},
    const {'1': 'mask_width', '3': 2, '4': 1, '5': 13, '10': 'maskWidth'},
    const {'1': 'mask_height', '3': 3, '4': 1, '5': 13, '10': 'maskHeight'},
    const {'1': 'mask_channels', '3': 4, '4': 1, '5': 13, '10': 'maskChannels'},
    const {'1': 'mask', '3': 5, '4': 1, '5': 12, '10': 'mask'},
    const {'1': 'mask_channel_classes', '3': 6, '4': 3, '5': 9, '10': 'maskChannelClasses'},
    const {'1': 'predict_in_distance_buffer', '3': 7, '4': 1, '5': 8, '10': 'predictInDistanceBuffer'},
    const {'1': 'timestamp_ms', '3': 8, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'weed_detection_classes', '3': 9, '4': 3, '5': 9, '10': 'weedDetectionClasses'},
    const {'1': 'embedding_categories', '3': 10, '4': 3, '5': 9, '10': 'embeddingCategories'},
    const {'1': 'available_for_snapshotting', '3': 11, '4': 1, '5': 8, '10': 'availableForSnapshotting'},
  ],
};

/// Descriptor for `DeepweedOutput`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deepweedOutputDescriptor = $convert.base64Decode('Cg5EZWVwd2VlZE91dHB1dBJDCgpkZXRlY3Rpb25zGAEgAygLMiMuY3YucnVudGltZS5wcm90by5EZWVwd2VlZERldGVjdGlvblIKZGV0ZWN0aW9ucxIdCgptYXNrX3dpZHRoGAIgASgNUgltYXNrV2lkdGgSHwoLbWFza19oZWlnaHQYAyABKA1SCm1hc2tIZWlnaHQSIwoNbWFza19jaGFubmVscxgEIAEoDVIMbWFza0NoYW5uZWxzEhIKBG1hc2sYBSABKAxSBG1hc2sSMAoUbWFza19jaGFubmVsX2NsYXNzZXMYBiADKAlSEm1hc2tDaGFubmVsQ2xhc3NlcxI7ChpwcmVkaWN0X2luX2Rpc3RhbmNlX2J1ZmZlchgHIAEoCFIXcHJlZGljdEluRGlzdGFuY2VCdWZmZXISIQoMdGltZXN0YW1wX21zGAggASgDUgt0aW1lc3RhbXBNcxI0ChZ3ZWVkX2RldGVjdGlvbl9jbGFzc2VzGAkgAygJUhR3ZWVkRGV0ZWN0aW9uQ2xhc3NlcxIxChRlbWJlZGRpbmdfY2F0ZWdvcmllcxgKIAMoCVITZW1iZWRkaW5nQ2F0ZWdvcmllcxI8ChphdmFpbGFibGVfZm9yX3NuYXBzaG90dGluZxgLIAEoCFIYYXZhaWxhYmxlRm9yU25hcHNob3R0aW5n');
@$core.Deprecated('Use getDeepweedOutputByTimestampRequestDescriptor instead')
const GetDeepweedOutputByTimestampRequest$json = const {
  '1': 'GetDeepweedOutputByTimestampRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `GetDeepweedOutputByTimestampRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedOutputByTimestampRequestDescriptor = $convert.base64Decode('CiNHZXREZWVwd2VlZE91dHB1dEJ5VGltZXN0YW1wUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXM=');
@$core.Deprecated('Use getRecommendedStrobeSettingsRequestDescriptor instead')
const GetRecommendedStrobeSettingsRequest$json = const {
  '1': 'GetRecommendedStrobeSettingsRequest',
};

/// Descriptor for `GetRecommendedStrobeSettingsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRecommendedStrobeSettingsRequestDescriptor = $convert.base64Decode('CiNHZXRSZWNvbW1lbmRlZFN0cm9iZVNldHRpbmdzUmVxdWVzdA==');
@$core.Deprecated('Use getRecommendedStrobeSettingsResponseDescriptor instead')
const GetRecommendedStrobeSettingsResponse$json = const {
  '1': 'GetRecommendedStrobeSettingsResponse',
  '2': const [
    const {'1': 'target_camera_fps', '3': 1, '4': 1, '5': 2, '10': 'targetCameraFps'},
    const {'1': 'targets_per_predict_ratio', '3': 2, '4': 1, '5': 5, '10': 'targetsPerPredictRatio'},
  ],
};

/// Descriptor for `GetRecommendedStrobeSettingsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRecommendedStrobeSettingsResponseDescriptor = $convert.base64Decode('CiRHZXRSZWNvbW1lbmRlZFN0cm9iZVNldHRpbmdzUmVzcG9uc2USKgoRdGFyZ2V0X2NhbWVyYV9mcHMYASABKAJSD3RhcmdldENhbWVyYUZwcxI5Chl0YXJnZXRzX3Blcl9wcmVkaWN0X3JhdGlvGAIgASgFUhZ0YXJnZXRzUGVyUHJlZGljdFJhdGlv');
@$core.Deprecated('Use startP2PBufferringRequestDescriptor instead')
const StartP2PBufferringRequest$json = const {
  '1': 'StartP2PBufferringRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `StartP2PBufferringRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startP2PBufferringRequestDescriptor = $convert.base64Decode('ChlTdGFydFAyUEJ1ZmZlcnJpbmdSZXF1ZXN0EhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQ6AhgB');
@$core.Deprecated('Use startP2PBufferringResponseDescriptor instead')
const StartP2PBufferringResponse$json = const {
  '1': 'StartP2PBufferringResponse',
  '7': const {'3': true},
};

/// Descriptor for `StartP2PBufferringResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startP2PBufferringResponseDescriptor = $convert.base64Decode('ChpTdGFydFAyUEJ1ZmZlcnJpbmdSZXNwb25zZToCGAE=');
@$core.Deprecated('Use stopP2PBufferringRequestDescriptor instead')
const StopP2PBufferringRequest$json = const {
  '1': 'StopP2PBufferringRequest',
  '2': const [
    const {'1': 'save_burst', '3': 1, '4': 1, '5': 8, '10': 'saveBurst'},
    const {'1': 'cam_id', '3': 2, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'path', '3': 3, '4': 1, '5': 9, '10': 'path'},
    const {'1': 'dont_capture_predict_image', '3': 4, '4': 1, '5': 8, '10': 'dontCapturePredictImage'},
    const {'1': 'start_timestamp_ms', '3': 5, '4': 1, '5': 3, '10': 'startTimestampMs'},
    const {'1': 'end_timestamp_ms', '3': 6, '4': 1, '5': 3, '10': 'endTimestampMs'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `StopP2PBufferringRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopP2PBufferringRequestDescriptor = $convert.base64Decode('ChhTdG9wUDJQQnVmZmVycmluZ1JlcXVlc3QSHQoKc2F2ZV9idXJzdBgBIAEoCFIJc2F2ZUJ1cnN0EhUKBmNhbV9pZBgCIAEoCVIFY2FtSWQSEgoEcGF0aBgDIAEoCVIEcGF0aBI7Chpkb250X2NhcHR1cmVfcHJlZGljdF9pbWFnZRgEIAEoCFIXZG9udENhcHR1cmVQcmVkaWN0SW1hZ2USLAoSc3RhcnRfdGltZXN0YW1wX21zGAUgASgDUhBzdGFydFRpbWVzdGFtcE1zEigKEGVuZF90aW1lc3RhbXBfbXMYBiABKANSDmVuZFRpbWVzdGFtcE1zOgIYAQ==');
@$core.Deprecated('Use stopP2PBufferringResponseDescriptor instead')
const StopP2PBufferringResponse$json = const {
  '1': 'StopP2PBufferringResponse',
  '7': const {'3': true},
};

/// Descriptor for `StopP2PBufferringResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopP2PBufferringResponseDescriptor = $convert.base64Decode('ChlTdG9wUDJQQnVmZmVycmluZ1Jlc3BvbnNlOgIYAQ==');
@$core.Deprecated('Use p2PCaptureRequestDescriptor instead')
const P2PCaptureRequest$json = const {
  '1': 'P2PCaptureRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'timestamp_ms', '3': 3, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'write_to_disk', '3': 4, '4': 1, '5': 8, '10': 'writeToDisk'},
    const {'1': 'reason', '3': 5, '4': 1, '5': 14, '6': '.carbon.aimbot.cv.P2PCaptureReason', '10': 'reason'},
  ],
};

/// Descriptor for `P2PCaptureRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PCaptureRequestDescriptor = $convert.base64Decode('ChFQMlBDYXB0dXJlUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEhIKBG5hbWUYAiABKAlSBG5hbWUSIQoMdGltZXN0YW1wX21zGAMgASgDUgt0aW1lc3RhbXBNcxIiCg13cml0ZV90b19kaXNrGAQgASgIUgt3cml0ZVRvRGlzaxI6CgZyZWFzb24YBSABKA4yIi5jYXJib24uYWltYm90LmN2LlAyUENhcHR1cmVSZWFzb25SBnJlYXNvbg==');
@$core.Deprecated('Use p2PCaptureResponseDescriptor instead')
const P2PCaptureResponse$json = const {
  '1': 'P2PCaptureResponse',
};

/// Descriptor for `P2PCaptureResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PCaptureResponseDescriptor = $convert.base64Decode('ChJQMlBDYXB0dXJlUmVzcG9uc2U=');
@$core.Deprecated('Use p2PBufferingBurstPredictMetadataDescriptor instead')
const P2PBufferingBurstPredictMetadata$json = const {
  '1': 'P2PBufferingBurstPredictMetadata',
  '2': const [
    const {'1': 'plant_size_px', '3': 1, '4': 1, '5': 2, '10': 'plantSizePx'},
  ],
};

/// Descriptor for `P2PBufferingBurstPredictMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PBufferingBurstPredictMetadataDescriptor = $convert.base64Decode('CiBQMlBCdWZmZXJpbmdCdXJzdFByZWRpY3RNZXRhZGF0YRIiCg1wbGFudF9zaXplX3B4GAEgASgCUgtwbGFudFNpemVQeA==');
@$core.Deprecated('Use p2PBufferringBurstCaptureRequestDescriptor instead')
const P2PBufferringBurstCaptureRequest$json = const {
  '1': 'P2PBufferringBurstCaptureRequest',
  '2': const [
    const {'1': 'cam_id', '3': 2, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'path', '3': 3, '4': 1, '5': 9, '10': 'path'},
    const {'1': 'dont_capture_predict_image', '3': 4, '4': 1, '5': 8, '10': 'dontCapturePredictImage'},
    const {'1': 'start_timestamp_ms', '3': 5, '4': 1, '5': 3, '10': 'startTimestampMs'},
    const {'1': 'end_timestamp_ms', '3': 6, '4': 1, '5': 3, '10': 'endTimestampMs'},
    const {'1': 'predict_path', '3': 7, '4': 1, '5': 9, '9': 0, '10': 'predictPath', '17': true},
    const {'1': 'predict_path_exists', '3': 8, '4': 1, '5': 8, '10': 'predictPathExists'},
    const {'1': 'save_predict_metadata', '3': 9, '4': 1, '5': 8, '10': 'savePredictMetadata'},
    const {'1': 'predict_metadata', '3': 10, '4': 1, '5': 11, '6': '.cv.runtime.proto.P2PBufferingBurstPredictMetadata', '10': 'predictMetadata'},
  ],
  '8': const [
    const {'1': '_predict_path'},
  ],
};

/// Descriptor for `P2PBufferringBurstCaptureRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PBufferringBurstCaptureRequestDescriptor = $convert.base64Decode('CiBQMlBCdWZmZXJyaW5nQnVyc3RDYXB0dXJlUmVxdWVzdBIVCgZjYW1faWQYAiABKAlSBWNhbUlkEhIKBHBhdGgYAyABKAlSBHBhdGgSOwoaZG9udF9jYXB0dXJlX3ByZWRpY3RfaW1hZ2UYBCABKAhSF2RvbnRDYXB0dXJlUHJlZGljdEltYWdlEiwKEnN0YXJ0X3RpbWVzdGFtcF9tcxgFIAEoA1IQc3RhcnRUaW1lc3RhbXBNcxIoChBlbmRfdGltZXN0YW1wX21zGAYgASgDUg5lbmRUaW1lc3RhbXBNcxImCgxwcmVkaWN0X3BhdGgYByABKAlIAFILcHJlZGljdFBhdGiIAQESLgoTcHJlZGljdF9wYXRoX2V4aXN0cxgIIAEoCFIRcHJlZGljdFBhdGhFeGlzdHMSMgoVc2F2ZV9wcmVkaWN0X21ldGFkYXRhGAkgASgIUhNzYXZlUHJlZGljdE1ldGFkYXRhEl0KEHByZWRpY3RfbWV0YWRhdGEYCiABKAsyMi5jdi5ydW50aW1lLnByb3RvLlAyUEJ1ZmZlcmluZ0J1cnN0UHJlZGljdE1ldGFkYXRhUg9wcmVkaWN0TWV0YWRhdGFCDwoNX3ByZWRpY3RfcGF0aA==');
@$core.Deprecated('Use p2PBufferringBurstCaptureResponseDescriptor instead')
const P2PBufferringBurstCaptureResponse$json = const {
  '1': 'P2PBufferringBurstCaptureResponse',
};

/// Descriptor for `P2PBufferringBurstCaptureResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PBufferringBurstCaptureResponseDescriptor = $convert.base64Decode('CiFQMlBCdWZmZXJyaW5nQnVyc3RDYXB0dXJlUmVzcG9uc2U=');
@$core.Deprecated('Use getNextDeepweedOutputRequestDescriptor instead')
const GetNextDeepweedOutputRequest$json = const {
  '1': 'GetNextDeepweedOutputRequest',
  '2': const [
    const {'1': 'timestamp_ms', '3': 1, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'timeout_ms', '3': 2, '4': 1, '5': 3, '10': 'timeoutMs'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 9, '10': 'camId'},
  ],
};

/// Descriptor for `GetNextDeepweedOutputRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextDeepweedOutputRequestDescriptor = $convert.base64Decode('ChxHZXROZXh0RGVlcHdlZWRPdXRwdXRSZXF1ZXN0EiEKDHRpbWVzdGFtcF9tcxgBIAEoA1ILdGltZXN0YW1wTXMSHQoKdGltZW91dF9tcxgCIAEoA1IJdGltZW91dE1zEhUKBmNhbV9pZBgDIAEoCVIFY2FtSWQ=');
@$core.Deprecated('Use setTargetingStateRequestDescriptor instead')
const SetTargetingStateRequest$json = const {
  '1': 'SetTargetingStateRequest',
  '2': const [
    const {'1': 'weeding_enabled', '3': 1, '4': 1, '5': 8, '10': 'weedingEnabled'},
    const {'1': 'thinning_enabled', '3': 2, '4': 1, '5': 8, '10': 'thinningEnabled'},
  ],
};

/// Descriptor for `SetTargetingStateRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setTargetingStateRequestDescriptor = $convert.base64Decode('ChhTZXRUYXJnZXRpbmdTdGF0ZVJlcXVlc3QSJwoPd2VlZGluZ19lbmFibGVkGAEgASgIUg53ZWVkaW5nRW5hYmxlZBIpChB0aGlubmluZ19lbmFibGVkGAIgASgIUg90aGlubmluZ0VuYWJsZWQ=');
@$core.Deprecated('Use setTargetingStateResponseDescriptor instead')
const SetTargetingStateResponse$json = const {
  '1': 'SetTargetingStateResponse',
};

/// Descriptor for `SetTargetingStateResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setTargetingStateResponseDescriptor = $convert.base64Decode('ChlTZXRUYXJnZXRpbmdTdGF0ZVJlc3BvbnNl');
@$core.Deprecated('Use getNextFocusMetricRequestDescriptor instead')
const GetNextFocusMetricRequest$json = const {
  '1': 'GetNextFocusMetricRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `GetNextFocusMetricRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextFocusMetricRequestDescriptor = $convert.base64Decode('ChlHZXROZXh0Rm9jdXNNZXRyaWNSZXF1ZXN0EhUKBmNhbV9pZBgBIAEoCVIFY2FtSWQSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcw==');
@$core.Deprecated('Use getNextFocusMetricResponseDescriptor instead')
const GetNextFocusMetricResponse$json = const {
  '1': 'GetNextFocusMetricResponse',
  '2': const [
    const {'1': 'focus_metric', '3': 1, '4': 1, '5': 2, '10': 'focusMetric'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `GetNextFocusMetricResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextFocusMetricResponseDescriptor = $convert.base64Decode('ChpHZXROZXh0Rm9jdXNNZXRyaWNSZXNwb25zZRIhCgxmb2N1c19tZXRyaWMYASABKAJSC2ZvY3VzTWV0cmljEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXM=');
@$core.Deprecated('Use removeDataDirRequestDescriptor instead')
const RemoveDataDirRequest$json = const {
  '1': 'RemoveDataDirRequest',
  '2': const [
    const {'1': 'path', '3': 1, '4': 1, '5': 9, '10': 'path'},
  ],
};

/// Descriptor for `RemoveDataDirRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List removeDataDirRequestDescriptor = $convert.base64Decode('ChRSZW1vdmVEYXRhRGlyUmVxdWVzdBISCgRwYXRoGAEgASgJUgRwYXRo');
@$core.Deprecated('Use removeDataDirResponseDescriptor instead')
const RemoveDataDirResponse$json = const {
  '1': 'RemoveDataDirResponse',
};

/// Descriptor for `RemoveDataDirResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List removeDataDirResponseDescriptor = $convert.base64Decode('ChVSZW1vdmVEYXRhRGlyUmVzcG9uc2U=');
@$core.Deprecated('Use lastNImageRequestDescriptor instead')
const LastNImageRequest$json = const {
  '1': 'LastNImageRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'num_images', '3': 2, '4': 1, '5': 4, '10': 'numImages'},
  ],
};

/// Descriptor for `LastNImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List lastNImageRequestDescriptor = $convert.base64Decode('ChFMYXN0TkltYWdlUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEh0KCm51bV9pbWFnZXMYAiABKARSCW51bUltYWdlcw==');
@$core.Deprecated('Use computeCapabilitiesResponseDescriptor instead')
const ComputeCapabilitiesResponse$json = const {
  '1': 'ComputeCapabilitiesResponse',
  '2': const [
    const {'1': 'capabilities', '3': 1, '4': 3, '5': 9, '10': 'capabilities'},
  ],
};

/// Descriptor for `ComputeCapabilitiesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List computeCapabilitiesResponseDescriptor = $convert.base64Decode('ChtDb21wdXRlQ2FwYWJpbGl0aWVzUmVzcG9uc2USIgoMY2FwYWJpbGl0aWVzGAEgAygJUgxjYXBhYmlsaXRpZXM=');
@$core.Deprecated('Use supportedTensorRTVersionsResponseDescriptor instead')
const SupportedTensorRTVersionsResponse$json = const {
  '1': 'SupportedTensorRTVersionsResponse',
  '2': const [
    const {'1': 'versions', '3': 1, '4': 3, '5': 9, '10': 'versions'},
  ],
};

/// Descriptor for `SupportedTensorRTVersionsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List supportedTensorRTVersionsResponseDescriptor = $convert.base64Decode('CiFTdXBwb3J0ZWRUZW5zb3JSVFZlcnNpb25zUmVzcG9uc2USGgoIdmVyc2lvbnMYASADKAlSCHZlcnNpb25z');
@$core.Deprecated('Use emptyDescriptor instead')
const Empty$json = const {
  '1': 'Empty',
};

/// Descriptor for `Empty`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List emptyDescriptor = $convert.base64Decode('CgVFbXB0eQ==');
@$core.Deprecated('Use scoreQueueAndCountDescriptor instead')
const ScoreQueueAndCount$json = const {
  '1': 'ScoreQueueAndCount',
  '2': const [
    const {'1': 'score_queue', '3': 1, '4': 1, '5': 9, '10': 'scoreQueue'},
    const {'1': 'num_items', '3': 2, '4': 1, '5': 4, '10': 'numItems'},
  ],
};

/// Descriptor for `ScoreQueueAndCount`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List scoreQueueAndCountDescriptor = $convert.base64Decode('ChJTY29yZVF1ZXVlQW5kQ291bnQSHwoLc2NvcmVfcXVldWUYASABKAlSCnNjb3JlUXVldWUSGwoJbnVtX2l0ZW1zGAIgASgEUghudW1JdGVtcw==');
@$core.Deprecated('Use listScoreQueuesResponseDescriptor instead')
const ListScoreQueuesResponse$json = const {
  '1': 'ListScoreQueuesResponse',
  '2': const [
    const {'1': 'score_queue', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.ScoreQueueAndCount', '10': 'scoreQueue'},
  ],
};

/// Descriptor for `ListScoreQueuesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listScoreQueuesResponseDescriptor = $convert.base64Decode('ChdMaXN0U2NvcmVRdWV1ZXNSZXNwb25zZRJFCgtzY29yZV9xdWV1ZRgBIAMoCzIkLmN2LnJ1bnRpbWUucHJvdG8uU2NvcmVRdWV1ZUFuZENvdW50UgpzY29yZVF1ZXVl');
@$core.Deprecated('Use getCategoryCollectionResponseDescriptor instead')
const GetCategoryCollectionResponse$json = const {
  '1': 'GetCategoryCollectionResponse',
  '2': const [
    const {'1': 'category_collection_id', '3': 1, '4': 1, '5': 9, '10': 'categoryCollectionId'},
    const {'1': 'last_updated_timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'lastUpdatedTimestampMs'},
  ],
};

/// Descriptor for `GetCategoryCollectionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCategoryCollectionResponseDescriptor = $convert.base64Decode('Ch1HZXRDYXRlZ29yeUNvbGxlY3Rpb25SZXNwb25zZRI0ChZjYXRlZ29yeV9jb2xsZWN0aW9uX2lkGAEgASgJUhRjYXRlZ29yeUNvbGxlY3Rpb25JZBI5ChlsYXN0X3VwZGF0ZWRfdGltZXN0YW1wX21zGAIgASgDUhZsYXN0VXBkYXRlZFRpbWVzdGFtcE1z');
@$core.Deprecated('Use snapshotPredictImagesRequestDescriptor instead')
const SnapshotPredictImagesRequest$json = const {
  '1': 'SnapshotPredictImagesRequest',
};

/// Descriptor for `SnapshotPredictImagesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapshotPredictImagesRequestDescriptor = $convert.base64Decode('ChxTbmFwc2hvdFByZWRpY3RJbWFnZXNSZXF1ZXN0');
@$core.Deprecated('Use pcamSnapshotDescriptor instead')
const PcamSnapshot$json = const {
  '1': 'PcamSnapshot',
  '2': const [
    const {'1': 'pcam_id', '3': 1, '4': 1, '5': 9, '10': 'pcamId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `PcamSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pcamSnapshotDescriptor = $convert.base64Decode('CgxQY2FtU25hcHNob3QSFwoHcGNhbV9pZBgBIAEoCVIGcGNhbUlkEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXM=');
@$core.Deprecated('Use snapshotPredictImagesResponseDescriptor instead')
const SnapshotPredictImagesResponse$json = const {
  '1': 'SnapshotPredictImagesResponse',
  '2': const [
    const {'1': 'snapshots', '3': 1, '4': 3, '5': 11, '6': '.cv.runtime.proto.PcamSnapshot', '10': 'snapshots'},
  ],
};

/// Descriptor for `SnapshotPredictImagesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapshotPredictImagesResponseDescriptor = $convert.base64Decode('Ch1TbmFwc2hvdFByZWRpY3RJbWFnZXNSZXNwb25zZRI8CglzbmFwc2hvdHMYASADKAsyHi5jdi5ydW50aW1lLnByb3RvLlBjYW1TbmFwc2hvdFIJc25hcHNob3Rz');
@$core.Deprecated('Use getChipForPredictImageRequestDescriptor instead')
const GetChipForPredictImageRequest$json = const {
  '1': 'GetChipForPredictImageRequest',
  '2': const [
    const {'1': 'pcam_id', '3': 1, '4': 1, '5': 9, '10': 'pcamId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'center_x_px', '3': 3, '4': 1, '5': 5, '10': 'centerXPx'},
    const {'1': 'center_y_px', '3': 4, '4': 1, '5': 5, '10': 'centerYPx'},
  ],
};

/// Descriptor for `GetChipForPredictImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getChipForPredictImageRequestDescriptor = $convert.base64Decode('Ch1HZXRDaGlwRm9yUHJlZGljdEltYWdlUmVxdWVzdBIXCgdwY2FtX2lkGAEgASgJUgZwY2FtSWQSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcxIeCgtjZW50ZXJfeF9weBgDIAEoBVIJY2VudGVyWFB4Eh4KC2NlbnRlcl95X3B4GAQgASgFUgljZW50ZXJZUHg=');
@$core.Deprecated('Use getChipForPredictImageResponseDescriptor instead')
const GetChipForPredictImageResponse$json = const {
  '1': 'GetChipForPredictImageResponse',
  '2': const [
    const {'1': 'image_and_metadata', '3': 1, '4': 1, '5': 11, '6': '.cv.runtime.proto.ImageAndMetadataResponse', '10': 'imageAndMetadata'},
  ],
};

/// Descriptor for `GetChipForPredictImageResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getChipForPredictImageResponseDescriptor = $convert.base64Decode('Ch5HZXRDaGlwRm9yUHJlZGljdEltYWdlUmVzcG9uc2USWAoSaW1hZ2VfYW5kX21ldGFkYXRhGAEgASgLMiouY3YucnVudGltZS5wcm90by5JbWFnZUFuZE1ldGFkYXRhUmVzcG9uc2VSEGltYWdlQW5kTWV0YWRhdGE=');
