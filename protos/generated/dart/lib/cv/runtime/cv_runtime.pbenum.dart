///
//  Generated code. Do not modify.
//  source: cv/runtime/cv_runtime.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

// ignore_for_file: UNDEFINED_SHOWN_NAME
import 'dart:core' as $core;
import 'package:protobuf/protobuf.dart' as $pb;

class BufferUseCase extends $pb.ProtobufEnum {
  static const BufferUseCase P2P = BufferUseCase._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'P2P');
  static const BufferUseCase OpticalFlow = BufferUseCase._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'OpticalFlow');
  static const BufferUseCase Predict = BufferUseCase._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'Predict');
  static const BufferUseCase Drive = BufferUseCase._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'Drive');

  static const $core.List<BufferUseCase> values = <BufferUseCase> [
    P2P,
    OpticalFlow,
    Predict,
    Drive,
  ];

  static final $core.Map<$core.int, BufferUseCase> _byValue = $pb.ProtobufEnum.initByValue(values);
  static BufferUseCase? valueOf($core.int value) => _byValue[value];

  const BufferUseCase._($core.int v, $core.String n) : super(v, n);
}

class HitClass extends $pb.ProtobufEnum {
  static const HitClass WEED = HitClass._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'WEED');
  static const HitClass CROP = HitClass._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'CROP');
  static const HitClass PLANT = HitClass._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'PLANT');

  static const $core.List<HitClass> values = <HitClass> [
    WEED,
    CROP,
    PLANT,
  ];

  static final $core.Map<$core.int, HitClass> _byValue = $pb.ProtobufEnum.initByValue(values);
  static HitClass? valueOf($core.int value) => _byValue[value];

  const HitClass._($core.int v, $core.String n) : super(v, n);
}

class ScoreQueueType extends $pb.ProtobufEnum {
  static const ScoreQueueType PREDICT = ScoreQueueType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'PREDICT');
  static const ScoreQueueType CHIP = ScoreQueueType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'CHIP');

  static const $core.List<ScoreQueueType> values = <ScoreQueueType> [
    PREDICT,
    CHIP,
  ];

  static final $core.Map<$core.int, ScoreQueueType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static ScoreQueueType? valueOf($core.int value) => _byValue[value];

  const ScoreQueueType._($core.int v, $core.String n) : super(v, n);
}

class ErrorType extends $pb.ProtobufEnum {
  static const ErrorType NONE = ErrorType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'NONE');
  static const ErrorType GRAB = ErrorType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'GRAB');
  static const ErrorType CONNECTION = ErrorType._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'CONNECTION');
  static const ErrorType NO_IMPLEMENTATION = ErrorType._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'NO_IMPLEMENTATION');
  static const ErrorType NO_IMAGE_IN_LAST_MINUTE = ErrorType._(4, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'NO_IMAGE_IN_LAST_MINUTE');

  static const $core.List<ErrorType> values = <ErrorType> [
    NONE,
    GRAB,
    CONNECTION,
    NO_IMPLEMENTATION,
    NO_IMAGE_IN_LAST_MINUTE,
  ];

  static final $core.Map<$core.int, ErrorType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static ErrorType? valueOf($core.int value) => _byValue[value];

  const ErrorType._($core.int v, $core.String n) : super(v, n);
}

