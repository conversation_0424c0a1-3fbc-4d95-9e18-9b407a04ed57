///
//  Generated code. Do not modify.
//  source: cv/runtime/cv_runtime.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'cv_runtime.pb.dart' as $6;
export 'cv_runtime.pb.dart';

class CVRuntimeServiceClient extends $grpc.Client {
  static final _$setP2PContext =
      $grpc.ClientMethod<$6.SetP2PContextRequest, $6.SetP2PContextResponse>(
          '/cv.runtime.proto.CVRuntimeService/SetP2PContext',
          ($6.SetP2PContextRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SetP2PContextResponse.fromBuffer(value));
  static final _$getCameraDimensions = $grpc.ClientMethod<
          $6.GetCameraDimensionsRequest, $6.GetCameraDimensionsResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetCameraDimensions',
      ($6.GetCameraDimensionsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetCameraDimensionsResponse.fromBuffer(value));
  static final _$getCameraInfo =
      $grpc.ClientMethod<$6.GetCameraInfoRequest, $6.GetCameraInfoResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetCameraInfo',
          ($6.GetCameraInfoRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetCameraInfoResponse.fromBuffer(value));
  static final _$getDeepweedIndexToCategory = $grpc.ClientMethod<
          $6.GetDeepweedIndexToCategoryRequest,
          $6.GetDeepweedIndexToCategoryResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory',
      ($6.GetDeepweedIndexToCategoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetDeepweedIndexToCategoryResponse.fromBuffer(value));
  static final _$setDeepweedDetectionCriteria = $grpc.ClientMethod<
          $6.SetDeepweedDetectionCriteriaRequest,
          $6.SetDeepweedDetectionCriteriaResponse>(
      '/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria',
      ($6.SetDeepweedDetectionCriteriaRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SetDeepweedDetectionCriteriaResponse.fromBuffer(value));
  static final _$getDeepweedDetectionCriteria = $grpc.ClientMethod<
          $6.GetDeepweedDetectionCriteriaRequest,
          $6.GetDeepweedDetectionCriteriaResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria',
      ($6.GetDeepweedDetectionCriteriaRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetDeepweedDetectionCriteriaResponse.fromBuffer(value));
  static final _$getDeepweedSupportedCategories = $grpc.ClientMethod<
          $6.GetDeepweedSupportedCategoriesRequest,
          $6.GetDeepweedSupportedCategoriesResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories',
      ($6.GetDeepweedSupportedCategoriesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetDeepweedSupportedCategoriesResponse.fromBuffer(value));
  static final _$getCameraTemperatures = $grpc.ClientMethod<
          $6.GetCameraTemperaturesRequest, $6.GetCameraTemperaturesResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures',
      ($6.GetCameraTemperaturesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetCameraTemperaturesResponse.fromBuffer(value));
  static final _$setCameraSettings = $grpc.ClientMethod<
          $6.SetCameraSettingsRequest, $6.SetCameraSettingsResponse>(
      '/cv.runtime.proto.CVRuntimeService/SetCameraSettings',
      ($6.SetCameraSettingsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SetCameraSettingsResponse.fromBuffer(value));
  static final _$getCameraSettings = $grpc.ClientMethod<
          $6.GetCameraSettingsRequest, $6.GetCameraSettingsResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetCameraSettings',
      ($6.GetCameraSettingsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetCameraSettingsResponse.fromBuffer(value));
  static final _$startBurstRecordFrames = $grpc.ClientMethod<
          $6.StartBurstRecordFramesRequest, $6.StartBurstRecordFramesResponse>(
      '/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames',
      ($6.StartBurstRecordFramesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.StartBurstRecordFramesResponse.fromBuffer(value));
  static final _$stopBurstRecordFrames = $grpc.ClientMethod<
          $6.StopBurstRecordFramesRequest, $6.StopBurstRecordFramesResponse>(
      '/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames',
      ($6.StopBurstRecordFramesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.StopBurstRecordFramesResponse.fromBuffer(value));
  static final _$getConnectors =
      $grpc.ClientMethod<$6.GetConnectorsRequest, $6.GetConnectorsResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetConnectors',
          ($6.GetConnectorsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetConnectorsResponse.fromBuffer(value));
  static final _$setConnectors =
      $grpc.ClientMethod<$6.SetConnectorsRequest, $6.SetConnectorsResponse>(
          '/cv.runtime.proto.CVRuntimeService/SetConnectors',
          ($6.SetConnectorsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SetConnectorsResponse.fromBuffer(value));
  static final _$getTiming =
      $grpc.ClientMethod<$6.GetTimingRequest, $6.GetTimingResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetTiming',
          ($6.GetTimingRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetTimingResponse.fromBuffer(value));
  static final _$predict =
      $grpc.ClientMethod<$6.PredictRequest, $6.PredictResponse>(
          '/cv.runtime.proto.CVRuntimeService/Predict',
          ($6.PredictRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.PredictResponse.fromBuffer(value));
  static final _$loadAndQueue =
      $grpc.ClientMethod<$6.LoadAndQueueRequest, $6.LoadAndQueueResponse>(
          '/cv.runtime.proto.CVRuntimeService/LoadAndQueue',
          ($6.LoadAndQueueRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.LoadAndQueueResponse.fromBuffer(value));
  static final _$setImage =
      $grpc.ClientMethod<$6.SetImageRequest, $6.SetImageResponse>(
          '/cv.runtime.proto.CVRuntimeService/SetImage',
          ($6.SetImageRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SetImageResponse.fromBuffer(value));
  static final _$unsetImage =
      $grpc.ClientMethod<$6.UnsetImageRequest, $6.UnsetImageResponse>(
          '/cv.runtime.proto.CVRuntimeService/UnsetImage',
          ($6.UnsetImageRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.UnsetImageResponse.fromBuffer(value));
  static final _$getModelPaths =
      $grpc.ClientMethod<$6.GetModelPathsRequest, $6.GetModelPathsResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetModelPaths',
          ($6.GetModelPathsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetModelPathsResponse.fromBuffer(value));
  static final _$setGPSLocation =
      $grpc.ClientMethod<$6.SetGPSLocationRequest, $6.SetGPSLocationResponse>(
          '/cv.runtime.proto.CVRuntimeService/SetGPSLocation',
          ($6.SetGPSLocationRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SetGPSLocationResponse.fromBuffer(value));
  static final _$setImplementStatus = $grpc.ClientMethod<
          $6.SetImplementStatusRequest, $6.SetImplementStatusResponse>(
      '/cv.runtime.proto.CVRuntimeService/SetImplementStatus',
      ($6.SetImplementStatusRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SetImplementStatusResponse.fromBuffer(value));
  static final _$setImageScore =
      $grpc.ClientMethod<$6.SetImageScoreRequest, $6.SetImageScoreResponse>(
          '/cv.runtime.proto.CVRuntimeService/SetImageScore',
          ($6.SetImageScoreRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SetImageScoreResponse.fromBuffer(value));
  static final _$getScoreQueue =
      $grpc.ClientMethod<$6.GetScoreQueueRequest, $6.GetScoreQueueResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetScoreQueue',
          ($6.GetScoreQueueRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetScoreQueueResponse.fromBuffer(value));
  static final _$listScoreQueues =
      $grpc.ClientMethod<$6.Empty, $6.ListScoreQueuesResponse>(
          '/cv.runtime.proto.CVRuntimeService/ListScoreQueues',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.ListScoreQueuesResponse.fromBuffer(value));
  static final _$getMaxImageScore = $grpc.ClientMethod<
          $6.GetMaxImageScoreRequest, $6.GetMaxImageScoreResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetMaxImageScore',
      ($6.GetMaxImageScoreRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetMaxImageScoreResponse.fromBuffer(value));
  static final _$getMaxScoredImage = $grpc.ClientMethod<
          $6.GetMaxScoredImageRequest, $6.ImageAndMetadataResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage',
      ($6.GetMaxScoredImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.ImageAndMetadataResponse.fromBuffer(value));
  static final _$getLatestP2PImage = $grpc.ClientMethod<
          $6.GetLatestP2PImageRequest, $6.P2PImageAndMetadataResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage',
      ($6.GetLatestP2PImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.P2PImageAndMetadataResponse.fromBuffer(value));
  static final _$getChipImage = $grpc.ClientMethod<$6.GetChipImageRequest,
          $6.ChipImageAndMetadataResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetChipImage',
      ($6.GetChipImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.ChipImageAndMetadataResponse.fromBuffer(value));
  static final _$getChipQueueInformation = $grpc.ClientMethod<
          $6.ChipQueueInformationRequest, $6.ChipQueueInformationResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation',
      ($6.ChipQueueInformationRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.ChipQueueInformationResponse.fromBuffer(value));
  static final _$flushQueues =
      $grpc.ClientMethod<$6.FlushQueuesRequest, $6.FlushQueuesResponse>(
          '/cv.runtime.proto.CVRuntimeService/FlushQueues',
          ($6.FlushQueuesRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.FlushQueuesResponse.fromBuffer(value));
  static final _$getLatestImage =
      $grpc.ClientMethod<$6.GetLatestImageRequest, $6.ImageAndMetadataResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetLatestImage',
          ($6.GetLatestImageRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.ImageAndMetadataResponse.fromBuffer(value));
  static final _$getImageNearTimestamp = $grpc.ClientMethod<
          $6.GetImageNearTimestampRequest, $6.ImageAndMetadataResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp',
      ($6.GetImageNearTimestampRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.ImageAndMetadataResponse.fromBuffer(value));
  static final _$getLightweightBurstRecord = $grpc.ClientMethod<
          $6.GetLightweightBurstRecordRequest,
          $6.GetLightweightBurstRecordResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord',
      ($6.GetLightweightBurstRecordRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetLightweightBurstRecordResponse.fromBuffer(value));
  static final _$getBooted =
      $grpc.ClientMethod<$6.GetBootedRequest, $6.GetBootedResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetBooted',
          ($6.GetBootedRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetBootedResponse.fromBuffer(value));
  static final _$getReady =
      $grpc.ClientMethod<$6.GetReadyRequest, $6.GetReadyResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetReady',
          ($6.GetReadyRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetReadyResponse.fromBuffer(value));
  static final _$getDeepweedOutputByTimestamp = $grpc.ClientMethod<
          $6.GetDeepweedOutputByTimestampRequest, $6.DeepweedOutput>(
      '/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp',
      ($6.GetDeepweedOutputByTimestampRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $6.DeepweedOutput.fromBuffer(value));
  static final _$getRecommendedStrobeSettings = $grpc.ClientMethod<
          $6.GetRecommendedStrobeSettingsRequest,
          $6.GetRecommendedStrobeSettingsResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings',
      ($6.GetRecommendedStrobeSettingsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetRecommendedStrobeSettingsResponse.fromBuffer(value));
  static final _$p2PCapture =
      $grpc.ClientMethod<$6.P2PCaptureRequest, $6.P2PCaptureResponse>(
          '/cv.runtime.proto.CVRuntimeService/P2PCapture',
          ($6.P2PCaptureRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.P2PCaptureResponse.fromBuffer(value));
  static final _$setAutoWhitebalance = $grpc.ClientMethod<
          $6.SetAutoWhitebalanceRequest, $6.SetAutoWhitebalanceResponse>(
      '/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance',
      ($6.SetAutoWhitebalanceRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SetAutoWhitebalanceResponse.fromBuffer(value));
  static final _$getNextDeepweedOutput =
      $grpc.ClientMethod<$6.GetNextDeepweedOutputRequest, $6.DeepweedOutput>(
          '/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput',
          ($6.GetNextDeepweedOutputRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $6.DeepweedOutput.fromBuffer(value));
  static final _$getNextP2POutput =
      $grpc.ClientMethod<$6.GetNextP2POutputRequest, $6.P2POutputProto>(
          '/cv.runtime.proto.CVRuntimeService/GetNextP2POutput',
          ($6.GetNextP2POutputRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $6.P2POutputProto.fromBuffer(value));
  static final _$setTargetingState = $grpc.ClientMethod<
          $6.SetTargetingStateRequest, $6.SetTargetingStateResponse>(
      '/cv.runtime.proto.CVRuntimeService/SetTargetingState',
      ($6.SetTargetingStateRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SetTargetingStateResponse.fromBuffer(value));
  static final _$p2PBufferringBurstCapture = $grpc.ClientMethod<
          $6.P2PBufferringBurstCaptureRequest,
          $6.P2PBufferringBurstCaptureResponse>(
      '/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture',
      ($6.P2PBufferringBurstCaptureRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.P2PBufferringBurstCaptureResponse.fromBuffer(value));
  static final _$getNextFocusMetric = $grpc.ClientMethod<
          $6.GetNextFocusMetricRequest, $6.GetNextFocusMetricResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric',
      ($6.GetNextFocusMetricRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetNextFocusMetricResponse.fromBuffer(value));
  static final _$removeDataDir =
      $grpc.ClientMethod<$6.RemoveDataDirRequest, $6.RemoveDataDirResponse>(
          '/cv.runtime.proto.CVRuntimeService/RemoveDataDir',
          ($6.RemoveDataDirRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.RemoveDataDirResponse.fromBuffer(value));
  static final _$getLastNImages =
      $grpc.ClientMethod<$6.LastNImageRequest, $6.ImageAndMetadataResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetLastNImages',
          ($6.LastNImageRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.ImageAndMetadataResponse.fromBuffer(value));
  static final _$getComputeCapabilities =
      $grpc.ClientMethod<$6.Empty, $6.ComputeCapabilitiesResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.ComputeCapabilitiesResponse.fromBuffer(value));
  static final _$getSupportedTensorRTVersions =
      $grpc.ClientMethod<$6.Empty, $6.SupportedTensorRTVersionsResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.SupportedTensorRTVersionsResponse.fromBuffer(value));
  static final _$reloadCategoryCollection =
      $grpc.ClientMethod<$6.Empty, $6.Empty>(
          '/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $6.Empty.fromBuffer(value));
  static final _$getCategoryCollection =
      $grpc.ClientMethod<$6.Empty, $6.GetCategoryCollectionResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetCategoryCollection',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetCategoryCollectionResponse.fromBuffer(value));
  static final _$getErrorState =
      $grpc.ClientMethod<$6.Empty, $6.GetErrorStateResponse>(
          '/cv.runtime.proto.CVRuntimeService/GetErrorState',
          ($6.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $6.GetErrorStateResponse.fromBuffer(value));
  static final _$snapshotPredictImages = $grpc.ClientMethod<
          $6.SnapshotPredictImagesRequest, $6.SnapshotPredictImagesResponse>(
      '/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages',
      ($6.SnapshotPredictImagesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.SnapshotPredictImagesResponse.fromBuffer(value));
  static final _$getChipForPredictImage = $grpc.ClientMethod<
          $6.GetChipForPredictImageRequest, $6.GetChipForPredictImageResponse>(
      '/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage',
      ($6.GetChipForPredictImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $6.GetChipForPredictImageResponse.fromBuffer(value));

  CVRuntimeServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$6.SetP2PContextResponse> setP2PContext(
      $6.SetP2PContextRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setP2PContext, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetCameraDimensionsResponse> getCameraDimensions(
      $6.GetCameraDimensionsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCameraDimensions, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetCameraInfoResponse> getCameraInfo(
      $6.GetCameraInfoRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCameraInfo, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetDeepweedIndexToCategoryResponse>
      getDeepweedIndexToCategory($6.GetDeepweedIndexToCategoryRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedIndexToCategory, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.SetDeepweedDetectionCriteriaResponse>
      setDeepweedDetectionCriteria(
          $6.SetDeepweedDetectionCriteriaRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setDeepweedDetectionCriteria, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetDeepweedDetectionCriteriaResponse>
      getDeepweedDetectionCriteria(
          $6.GetDeepweedDetectionCriteriaRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedDetectionCriteria, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetDeepweedSupportedCategoriesResponse>
      getDeepweedSupportedCategories(
          $6.GetDeepweedSupportedCategoriesRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedSupportedCategories, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetCameraTemperaturesResponse> getCameraTemperatures(
      $6.GetCameraTemperaturesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCameraTemperatures, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetCameraSettingsResponse> setCameraSettings(
      $6.SetCameraSettingsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setCameraSettings, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetCameraSettingsResponse> getCameraSettings(
      $6.GetCameraSettingsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCameraSettings, request, options: options);
  }

  $grpc.ResponseFuture<$6.StartBurstRecordFramesResponse>
      startBurstRecordFrames($6.StartBurstRecordFramesRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startBurstRecordFrames, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.StopBurstRecordFramesResponse> stopBurstRecordFrames(
      $6.StopBurstRecordFramesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$stopBurstRecordFrames, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetConnectorsResponse> getConnectors(
      $6.GetConnectorsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getConnectors, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetConnectorsResponse> setConnectors(
      $6.SetConnectorsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setConnectors, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetTimingResponse> getTiming(
      $6.GetTimingRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTiming, request, options: options);
  }

  $grpc.ResponseFuture<$6.PredictResponse> predict($6.PredictRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$predict, request, options: options);
  }

  $grpc.ResponseFuture<$6.LoadAndQueueResponse> loadAndQueue(
      $6.LoadAndQueueRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$loadAndQueue, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetImageResponse> setImage($6.SetImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.UnsetImageResponse> unsetImage(
      $6.UnsetImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$unsetImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetModelPathsResponse> getModelPaths(
      $6.GetModelPathsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getModelPaths, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetGPSLocationResponse> setGPSLocation(
      $6.SetGPSLocationRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setGPSLocation, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetImplementStatusResponse> setImplementStatus(
      $6.SetImplementStatusRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setImplementStatus, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetImageScoreResponse> setImageScore(
      $6.SetImageScoreRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setImageScore, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetScoreQueueResponse> getScoreQueue(
      $6.GetScoreQueueRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getScoreQueue, request, options: options);
  }

  $grpc.ResponseFuture<$6.ListScoreQueuesResponse> listScoreQueues(
      $6.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listScoreQueues, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetMaxImageScoreResponse> getMaxImageScore(
      $6.GetMaxImageScoreRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getMaxImageScore, request, options: options);
  }

  $grpc.ResponseFuture<$6.ImageAndMetadataResponse> getMaxScoredImage(
      $6.GetMaxScoredImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getMaxScoredImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.P2PImageAndMetadataResponse> getLatestP2PImage(
      $6.GetLatestP2PImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getLatestP2PImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.ChipImageAndMetadataResponse> getChipImage(
      $6.GetChipImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getChipImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.ChipQueueInformationResponse> getChipQueueInformation(
      $6.ChipQueueInformationRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getChipQueueInformation, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.FlushQueuesResponse> flushQueues(
      $6.FlushQueuesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$flushQueues, request, options: options);
  }

  $grpc.ResponseFuture<$6.ImageAndMetadataResponse> getLatestImage(
      $6.GetLatestImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getLatestImage, request, options: options);
  }

  $grpc.ResponseFuture<$6.ImageAndMetadataResponse> getImageNearTimestamp(
      $6.GetImageNearTimestampRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getImageNearTimestamp, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetLightweightBurstRecordResponse>
      getLightweightBurstRecord($6.GetLightweightBurstRecordRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getLightweightBurstRecord, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetBootedResponse> getBooted(
      $6.GetBootedRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getBooted, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetReadyResponse> getReady($6.GetReadyRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getReady, request, options: options);
  }

  $grpc.ResponseFuture<$6.DeepweedOutput> getDeepweedOutputByTimestamp(
      $6.GetDeepweedOutputByTimestampRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedOutputByTimestamp, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetRecommendedStrobeSettingsResponse>
      getRecommendedStrobeSettings(
          $6.GetRecommendedStrobeSettingsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getRecommendedStrobeSettings, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.P2PCaptureResponse> p2PCapture(
      $6.P2PCaptureRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$p2PCapture, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetAutoWhitebalanceResponse> setAutoWhitebalance(
      $6.SetAutoWhitebalanceRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setAutoWhitebalance, request, options: options);
  }

  $grpc.ResponseFuture<$6.DeepweedOutput> getNextDeepweedOutput(
      $6.GetNextDeepweedOutputRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextDeepweedOutput, request, options: options);
  }

  $grpc.ResponseFuture<$6.P2POutputProto> getNextP2POutput(
      $6.GetNextP2POutputRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextP2POutput, request, options: options);
  }

  $grpc.ResponseFuture<$6.SetTargetingStateResponse> setTargetingState(
      $6.SetTargetingStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setTargetingState, request, options: options);
  }

  $grpc.ResponseFuture<$6.P2PBufferringBurstCaptureResponse>
      p2PBufferringBurstCapture($6.P2PBufferringBurstCaptureRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$p2PBufferringBurstCapture, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetNextFocusMetricResponse> getNextFocusMetric(
      $6.GetNextFocusMetricRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextFocusMetric, request, options: options);
  }

  $grpc.ResponseFuture<$6.RemoveDataDirResponse> removeDataDir(
      $6.RemoveDataDirRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$removeDataDir, request, options: options);
  }

  $grpc.ResponseStream<$6.ImageAndMetadataResponse> getLastNImages(
      $6.LastNImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(
        _$getLastNImages, $async.Stream.fromIterable([request]),
        options: options);
  }

  $grpc.ResponseFuture<$6.ComputeCapabilitiesResponse> getComputeCapabilities(
      $6.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getComputeCapabilities, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.SupportedTensorRTVersionsResponse>
      getSupportedTensorRTVersions($6.Empty request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSupportedTensorRTVersions, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.Empty> reloadCategoryCollection($6.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$reloadCategoryCollection, request,
        options: options);
  }

  $grpc.ResponseFuture<$6.GetCategoryCollectionResponse> getCategoryCollection(
      $6.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCategoryCollection, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetErrorStateResponse> getErrorState($6.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getErrorState, request, options: options);
  }

  $grpc.ResponseFuture<$6.SnapshotPredictImagesResponse> snapshotPredictImages(
      $6.SnapshotPredictImagesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$snapshotPredictImages, request, options: options);
  }

  $grpc.ResponseFuture<$6.GetChipForPredictImageResponse>
      getChipForPredictImage($6.GetChipForPredictImageRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getChipForPredictImage, request,
        options: options);
  }
}

abstract class CVRuntimeServiceBase extends $grpc.Service {
  $core.String get $name => 'cv.runtime.proto.CVRuntimeService';

  CVRuntimeServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$6.SetP2PContextRequest, $6.SetP2PContextResponse>(
            'SetP2PContext',
            setP2PContext_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.SetP2PContextRequest.fromBuffer(value),
            ($6.SetP2PContextResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetCameraDimensionsRequest,
            $6.GetCameraDimensionsResponse>(
        'GetCameraDimensions',
        getCameraDimensions_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetCameraDimensionsRequest.fromBuffer(value),
        ($6.GetCameraDimensionsResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetCameraInfoRequest, $6.GetCameraInfoResponse>(
            'GetCameraInfo',
            getCameraInfo_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetCameraInfoRequest.fromBuffer(value),
            ($6.GetCameraInfoResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetDeepweedIndexToCategoryRequest,
            $6.GetDeepweedIndexToCategoryResponse>(
        'GetDeepweedIndexToCategory',
        getDeepweedIndexToCategory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetDeepweedIndexToCategoryRequest.fromBuffer(value),
        ($6.GetDeepweedIndexToCategoryResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetDeepweedDetectionCriteriaRequest,
            $6.SetDeepweedDetectionCriteriaResponse>(
        'SetDeepweedDetectionCriteria',
        setDeepweedDetectionCriteria_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetDeepweedDetectionCriteriaRequest.fromBuffer(value),
        ($6.SetDeepweedDetectionCriteriaResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetDeepweedDetectionCriteriaRequest,
            $6.GetDeepweedDetectionCriteriaResponse>(
        'GetDeepweedDetectionCriteria',
        getDeepweedDetectionCriteria_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetDeepweedDetectionCriteriaRequest.fromBuffer(value),
        ($6.GetDeepweedDetectionCriteriaResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetDeepweedSupportedCategoriesRequest,
            $6.GetDeepweedSupportedCategoriesResponse>(
        'GetDeepweedSupportedCategories',
        getDeepweedSupportedCategories_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetDeepweedSupportedCategoriesRequest.fromBuffer(value),
        ($6.GetDeepweedSupportedCategoriesResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetCameraTemperaturesRequest,
            $6.GetCameraTemperaturesResponse>(
        'GetCameraTemperatures',
        getCameraTemperatures_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetCameraTemperaturesRequest.fromBuffer(value),
        ($6.GetCameraTemperaturesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetCameraSettingsRequest,
            $6.SetCameraSettingsResponse>(
        'SetCameraSettings',
        setCameraSettings_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetCameraSettingsRequest.fromBuffer(value),
        ($6.SetCameraSettingsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetCameraSettingsRequest,
            $6.GetCameraSettingsResponse>(
        'GetCameraSettings',
        getCameraSettings_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetCameraSettingsRequest.fromBuffer(value),
        ($6.GetCameraSettingsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.StartBurstRecordFramesRequest,
            $6.StartBurstRecordFramesResponse>(
        'StartBurstRecordFrames',
        startBurstRecordFrames_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.StartBurstRecordFramesRequest.fromBuffer(value),
        ($6.StartBurstRecordFramesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.StopBurstRecordFramesRequest,
            $6.StopBurstRecordFramesResponse>(
        'StopBurstRecordFrames',
        stopBurstRecordFrames_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.StopBurstRecordFramesRequest.fromBuffer(value),
        ($6.StopBurstRecordFramesResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetConnectorsRequest, $6.GetConnectorsResponse>(
            'GetConnectors',
            getConnectors_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetConnectorsRequest.fromBuffer(value),
            ($6.GetConnectorsResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.SetConnectorsRequest, $6.SetConnectorsResponse>(
            'SetConnectors',
            setConnectors_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.SetConnectorsRequest.fromBuffer(value),
            ($6.SetConnectorsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetTimingRequest, $6.GetTimingResponse>(
        'GetTiming',
        getTiming_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.GetTimingRequest.fromBuffer(value),
        ($6.GetTimingResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.PredictRequest, $6.PredictResponse>(
        'Predict',
        predict_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.PredictRequest.fromBuffer(value),
        ($6.PredictResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.LoadAndQueueRequest, $6.LoadAndQueueResponse>(
            'LoadAndQueue',
            loadAndQueue_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.LoadAndQueueRequest.fromBuffer(value),
            ($6.LoadAndQueueResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetImageRequest, $6.SetImageResponse>(
        'SetImage',
        setImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.SetImageRequest.fromBuffer(value),
        ($6.SetImageResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.UnsetImageRequest, $6.UnsetImageResponse>(
        'UnsetImage',
        unsetImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.UnsetImageRequest.fromBuffer(value),
        ($6.UnsetImageResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetModelPathsRequest, $6.GetModelPathsResponse>(
            'GetModelPaths',
            getModelPaths_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetModelPathsRequest.fromBuffer(value),
            ($6.GetModelPathsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetGPSLocationRequest,
            $6.SetGPSLocationResponse>(
        'SetGPSLocation',
        setGPSLocation_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetGPSLocationRequest.fromBuffer(value),
        ($6.SetGPSLocationResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetImplementStatusRequest,
            $6.SetImplementStatusResponse>(
        'SetImplementStatus',
        setImplementStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetImplementStatusRequest.fromBuffer(value),
        ($6.SetImplementStatusResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.SetImageScoreRequest, $6.SetImageScoreResponse>(
            'SetImageScore',
            setImageScore_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.SetImageScoreRequest.fromBuffer(value),
            ($6.SetImageScoreResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetScoreQueueRequest, $6.GetScoreQueueResponse>(
            'GetScoreQueue',
            getScoreQueue_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetScoreQueueRequest.fromBuffer(value),
            ($6.GetScoreQueueResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.Empty, $6.ListScoreQueuesResponse>(
        'ListScoreQueues',
        listScoreQueues_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
        ($6.ListScoreQueuesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetMaxImageScoreRequest,
            $6.GetMaxImageScoreResponse>(
        'GetMaxImageScore',
        getMaxImageScore_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetMaxImageScoreRequest.fromBuffer(value),
        ($6.GetMaxImageScoreResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetMaxScoredImageRequest,
            $6.ImageAndMetadataResponse>(
        'GetMaxScoredImage',
        getMaxScoredImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetMaxScoredImageRequest.fromBuffer(value),
        ($6.ImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetLatestP2PImageRequest,
            $6.P2PImageAndMetadataResponse>(
        'GetLatestP2PImage',
        getLatestP2PImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetLatestP2PImageRequest.fromBuffer(value),
        ($6.P2PImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetChipImageRequest,
            $6.ChipImageAndMetadataResponse>(
        'GetChipImage',
        getChipImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetChipImageRequest.fromBuffer(value),
        ($6.ChipImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.ChipQueueInformationRequest,
            $6.ChipQueueInformationResponse>(
        'GetChipQueueInformation',
        getChipQueueInformation_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.ChipQueueInformationRequest.fromBuffer(value),
        ($6.ChipQueueInformationResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.FlushQueuesRequest, $6.FlushQueuesResponse>(
            'FlushQueues',
            flushQueues_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.FlushQueuesRequest.fromBuffer(value),
            ($6.FlushQueuesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetLatestImageRequest,
            $6.ImageAndMetadataResponse>(
        'GetLatestImage',
        getLatestImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetLatestImageRequest.fromBuffer(value),
        ($6.ImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetImageNearTimestampRequest,
            $6.ImageAndMetadataResponse>(
        'GetImageNearTimestamp',
        getImageNearTimestamp_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetImageNearTimestampRequest.fromBuffer(value),
        ($6.ImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetLightweightBurstRecordRequest,
            $6.GetLightweightBurstRecordResponse>(
        'GetLightweightBurstRecord',
        getLightweightBurstRecord_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetLightweightBurstRecordRequest.fromBuffer(value),
        ($6.GetLightweightBurstRecordResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetBootedRequest, $6.GetBootedResponse>(
        'GetBooted',
        getBooted_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.GetBootedRequest.fromBuffer(value),
        ($6.GetBootedResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetReadyRequest, $6.GetReadyResponse>(
        'GetReady',
        getReady_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.GetReadyRequest.fromBuffer(value),
        ($6.GetReadyResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetDeepweedOutputByTimestampRequest,
            $6.DeepweedOutput>(
        'GetDeepweedOutputByTimestamp',
        getDeepweedOutputByTimestamp_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetDeepweedOutputByTimestampRequest.fromBuffer(value),
        ($6.DeepweedOutput value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetRecommendedStrobeSettingsRequest,
            $6.GetRecommendedStrobeSettingsResponse>(
        'GetRecommendedStrobeSettings',
        getRecommendedStrobeSettings_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetRecommendedStrobeSettingsRequest.fromBuffer(value),
        ($6.GetRecommendedStrobeSettingsResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.P2PCaptureRequest, $6.P2PCaptureResponse>(
        'P2PCapture',
        p2PCapture_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.P2PCaptureRequest.fromBuffer(value),
        ($6.P2PCaptureResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetAutoWhitebalanceRequest,
            $6.SetAutoWhitebalanceResponse>(
        'SetAutoWhitebalance',
        setAutoWhitebalance_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetAutoWhitebalanceRequest.fromBuffer(value),
        ($6.SetAutoWhitebalanceResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetNextDeepweedOutputRequest, $6.DeepweedOutput>(
            'GetNextDeepweedOutput',
            getNextDeepweedOutput_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetNextDeepweedOutputRequest.fromBuffer(value),
            ($6.DeepweedOutput value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.GetNextP2POutputRequest, $6.P2POutputProto>(
            'GetNextP2POutput',
            getNextP2POutput_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.GetNextP2POutputRequest.fromBuffer(value),
            ($6.P2POutputProto value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SetTargetingStateRequest,
            $6.SetTargetingStateResponse>(
        'SetTargetingState',
        setTargetingState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SetTargetingStateRequest.fromBuffer(value),
        ($6.SetTargetingStateResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.P2PBufferringBurstCaptureRequest,
            $6.P2PBufferringBurstCaptureResponse>(
        'P2PBufferringBurstCapture',
        p2PBufferringBurstCapture_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.P2PBufferringBurstCaptureRequest.fromBuffer(value),
        ($6.P2PBufferringBurstCaptureResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetNextFocusMetricRequest,
            $6.GetNextFocusMetricResponse>(
        'GetNextFocusMetric',
        getNextFocusMetric_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetNextFocusMetricRequest.fromBuffer(value),
        ($6.GetNextFocusMetricResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.RemoveDataDirRequest, $6.RemoveDataDirResponse>(
            'RemoveDataDir',
            removeDataDir_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $6.RemoveDataDirRequest.fromBuffer(value),
            ($6.RemoveDataDirResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.LastNImageRequest, $6.ImageAndMetadataResponse>(
            'GetLastNImages',
            getLastNImages_Pre,
            false,
            true,
            ($core.List<$core.int> value) =>
                $6.LastNImageRequest.fromBuffer(value),
            ($6.ImageAndMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.Empty, $6.ComputeCapabilitiesResponse>(
        'GetComputeCapabilities',
        getComputeCapabilities_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
        ($6.ComputeCapabilitiesResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$6.Empty, $6.SupportedTensorRTVersionsResponse>(
            'GetSupportedTensorRTVersions',
            getSupportedTensorRTVersions_Pre,
            false,
            false,
            ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
            ($6.SupportedTensorRTVersionsResponse value) =>
                value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.Empty, $6.Empty>(
        'ReloadCategoryCollection',
        reloadCategoryCollection_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
        ($6.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.Empty, $6.GetCategoryCollectionResponse>(
        'GetCategoryCollection',
        getCategoryCollection_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
        ($6.GetCategoryCollectionResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.Empty, $6.GetErrorStateResponse>(
        'GetErrorState',
        getErrorState_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $6.Empty.fromBuffer(value),
        ($6.GetErrorStateResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.SnapshotPredictImagesRequest,
            $6.SnapshotPredictImagesResponse>(
        'SnapshotPredictImages',
        snapshotPredictImages_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.SnapshotPredictImagesRequest.fromBuffer(value),
        ($6.SnapshotPredictImagesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$6.GetChipForPredictImageRequest,
            $6.GetChipForPredictImageResponse>(
        'GetChipForPredictImage',
        getChipForPredictImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $6.GetChipForPredictImageRequest.fromBuffer(value),
        ($6.GetChipForPredictImageResponse value) => value.writeToBuffer()));
  }

  $async.Future<$6.SetP2PContextResponse> setP2PContext_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetP2PContextRequest> request) async {
    return setP2PContext(call, await request);
  }

  $async.Future<$6.GetCameraDimensionsResponse> getCameraDimensions_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetCameraDimensionsRequest> request) async {
    return getCameraDimensions(call, await request);
  }

  $async.Future<$6.GetCameraInfoResponse> getCameraInfo_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetCameraInfoRequest> request) async {
    return getCameraInfo(call, await request);
  }

  $async.Future<$6.GetDeepweedIndexToCategoryResponse>
      getDeepweedIndexToCategory_Pre($grpc.ServiceCall call,
          $async.Future<$6.GetDeepweedIndexToCategoryRequest> request) async {
    return getDeepweedIndexToCategory(call, await request);
  }

  $async.Future<$6.SetDeepweedDetectionCriteriaResponse>
      setDeepweedDetectionCriteria_Pre($grpc.ServiceCall call,
          $async.Future<$6.SetDeepweedDetectionCriteriaRequest> request) async {
    return setDeepweedDetectionCriteria(call, await request);
  }

  $async.Future<$6.GetDeepweedDetectionCriteriaResponse>
      getDeepweedDetectionCriteria_Pre($grpc.ServiceCall call,
          $async.Future<$6.GetDeepweedDetectionCriteriaRequest> request) async {
    return getDeepweedDetectionCriteria(call, await request);
  }

  $async.Future<$6.GetDeepweedSupportedCategoriesResponse>
      getDeepweedSupportedCategories_Pre(
          $grpc.ServiceCall call,
          $async.Future<$6.GetDeepweedSupportedCategoriesRequest>
              request) async {
    return getDeepweedSupportedCategories(call, await request);
  }

  $async.Future<$6.GetCameraTemperaturesResponse> getCameraTemperatures_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetCameraTemperaturesRequest> request) async {
    return getCameraTemperatures(call, await request);
  }

  $async.Future<$6.SetCameraSettingsResponse> setCameraSettings_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetCameraSettingsRequest> request) async {
    return setCameraSettings(call, await request);
  }

  $async.Future<$6.GetCameraSettingsResponse> getCameraSettings_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetCameraSettingsRequest> request) async {
    return getCameraSettings(call, await request);
  }

  $async.Future<$6.StartBurstRecordFramesResponse> startBurstRecordFrames_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.StartBurstRecordFramesRequest> request) async {
    return startBurstRecordFrames(call, await request);
  }

  $async.Future<$6.StopBurstRecordFramesResponse> stopBurstRecordFrames_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.StopBurstRecordFramesRequest> request) async {
    return stopBurstRecordFrames(call, await request);
  }

  $async.Future<$6.GetConnectorsResponse> getConnectors_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetConnectorsRequest> request) async {
    return getConnectors(call, await request);
  }

  $async.Future<$6.SetConnectorsResponse> setConnectors_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetConnectorsRequest> request) async {
    return setConnectors(call, await request);
  }

  $async.Future<$6.GetTimingResponse> getTiming_Pre($grpc.ServiceCall call,
      $async.Future<$6.GetTimingRequest> request) async {
    return getTiming(call, await request);
  }

  $async.Future<$6.PredictResponse> predict_Pre(
      $grpc.ServiceCall call, $async.Future<$6.PredictRequest> request) async {
    return predict(call, await request);
  }

  $async.Future<$6.LoadAndQueueResponse> loadAndQueue_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.LoadAndQueueRequest> request) async {
    return loadAndQueue(call, await request);
  }

  $async.Future<$6.SetImageResponse> setImage_Pre(
      $grpc.ServiceCall call, $async.Future<$6.SetImageRequest> request) async {
    return setImage(call, await request);
  }

  $async.Future<$6.UnsetImageResponse> unsetImage_Pre($grpc.ServiceCall call,
      $async.Future<$6.UnsetImageRequest> request) async {
    return unsetImage(call, await request);
  }

  $async.Future<$6.GetModelPathsResponse> getModelPaths_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetModelPathsRequest> request) async {
    return getModelPaths(call, await request);
  }

  $async.Future<$6.SetGPSLocationResponse> setGPSLocation_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetGPSLocationRequest> request) async {
    return setGPSLocation(call, await request);
  }

  $async.Future<$6.SetImplementStatusResponse> setImplementStatus_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetImplementStatusRequest> request) async {
    return setImplementStatus(call, await request);
  }

  $async.Future<$6.SetImageScoreResponse> setImageScore_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetImageScoreRequest> request) async {
    return setImageScore(call, await request);
  }

  $async.Future<$6.GetScoreQueueResponse> getScoreQueue_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetScoreQueueRequest> request) async {
    return getScoreQueue(call, await request);
  }

  $async.Future<$6.ListScoreQueuesResponse> listScoreQueues_Pre(
      $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return listScoreQueues(call, await request);
  }

  $async.Future<$6.GetMaxImageScoreResponse> getMaxImageScore_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetMaxImageScoreRequest> request) async {
    return getMaxImageScore(call, await request);
  }

  $async.Future<$6.ImageAndMetadataResponse> getMaxScoredImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetMaxScoredImageRequest> request) async {
    return getMaxScoredImage(call, await request);
  }

  $async.Future<$6.P2PImageAndMetadataResponse> getLatestP2PImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetLatestP2PImageRequest> request) async {
    return getLatestP2PImage(call, await request);
  }

  $async.Future<$6.ChipImageAndMetadataResponse> getChipImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetChipImageRequest> request) async {
    return getChipImage(call, await request);
  }

  $async.Future<$6.ChipQueueInformationResponse> getChipQueueInformation_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.ChipQueueInformationRequest> request) async {
    return getChipQueueInformation(call, await request);
  }

  $async.Future<$6.FlushQueuesResponse> flushQueues_Pre($grpc.ServiceCall call,
      $async.Future<$6.FlushQueuesRequest> request) async {
    return flushQueues(call, await request);
  }

  $async.Future<$6.ImageAndMetadataResponse> getLatestImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetLatestImageRequest> request) async {
    return getLatestImage(call, await request);
  }

  $async.Future<$6.ImageAndMetadataResponse> getImageNearTimestamp_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetImageNearTimestampRequest> request) async {
    return getImageNearTimestamp(call, await request);
  }

  $async.Future<$6.GetLightweightBurstRecordResponse>
      getLightweightBurstRecord_Pre($grpc.ServiceCall call,
          $async.Future<$6.GetLightweightBurstRecordRequest> request) async {
    return getLightweightBurstRecord(call, await request);
  }

  $async.Future<$6.GetBootedResponse> getBooted_Pre($grpc.ServiceCall call,
      $async.Future<$6.GetBootedRequest> request) async {
    return getBooted(call, await request);
  }

  $async.Future<$6.GetReadyResponse> getReady_Pre(
      $grpc.ServiceCall call, $async.Future<$6.GetReadyRequest> request) async {
    return getReady(call, await request);
  }

  $async.Future<$6.DeepweedOutput> getDeepweedOutputByTimestamp_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetDeepweedOutputByTimestampRequest> request) async {
    return getDeepweedOutputByTimestamp(call, await request);
  }

  $async.Future<$6.GetRecommendedStrobeSettingsResponse>
      getRecommendedStrobeSettings_Pre($grpc.ServiceCall call,
          $async.Future<$6.GetRecommendedStrobeSettingsRequest> request) async {
    return getRecommendedStrobeSettings(call, await request);
  }

  $async.Future<$6.P2PCaptureResponse> p2PCapture_Pre($grpc.ServiceCall call,
      $async.Future<$6.P2PCaptureRequest> request) async {
    return p2PCapture(call, await request);
  }

  $async.Future<$6.SetAutoWhitebalanceResponse> setAutoWhitebalance_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetAutoWhitebalanceRequest> request) async {
    return setAutoWhitebalance(call, await request);
  }

  $async.Future<$6.DeepweedOutput> getNextDeepweedOutput_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetNextDeepweedOutputRequest> request) async {
    return getNextDeepweedOutput(call, await request);
  }

  $async.Future<$6.P2POutputProto> getNextP2POutput_Pre($grpc.ServiceCall call,
      $async.Future<$6.GetNextP2POutputRequest> request) async {
    return getNextP2POutput(call, await request);
  }

  $async.Future<$6.SetTargetingStateResponse> setTargetingState_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SetTargetingStateRequest> request) async {
    return setTargetingState(call, await request);
  }

  $async.Future<$6.P2PBufferringBurstCaptureResponse>
      p2PBufferringBurstCapture_Pre($grpc.ServiceCall call,
          $async.Future<$6.P2PBufferringBurstCaptureRequest> request) async {
    return p2PBufferringBurstCapture(call, await request);
  }

  $async.Future<$6.GetNextFocusMetricResponse> getNextFocusMetric_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetNextFocusMetricRequest> request) async {
    return getNextFocusMetric(call, await request);
  }

  $async.Future<$6.RemoveDataDirResponse> removeDataDir_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.RemoveDataDirRequest> request) async {
    return removeDataDir(call, await request);
  }

  $async.Stream<$6.ImageAndMetadataResponse> getLastNImages_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.LastNImageRequest> request) async* {
    yield* getLastNImages(call, await request);
  }

  $async.Future<$6.ComputeCapabilitiesResponse> getComputeCapabilities_Pre(
      $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return getComputeCapabilities(call, await request);
  }

  $async.Future<$6.SupportedTensorRTVersionsResponse>
      getSupportedTensorRTVersions_Pre(
          $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return getSupportedTensorRTVersions(call, await request);
  }

  $async.Future<$6.Empty> reloadCategoryCollection_Pre(
      $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return reloadCategoryCollection(call, await request);
  }

  $async.Future<$6.GetCategoryCollectionResponse> getCategoryCollection_Pre(
      $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return getCategoryCollection(call, await request);
  }

  $async.Future<$6.GetErrorStateResponse> getErrorState_Pre(
      $grpc.ServiceCall call, $async.Future<$6.Empty> request) async {
    return getErrorState(call, await request);
  }

  $async.Future<$6.SnapshotPredictImagesResponse> snapshotPredictImages_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.SnapshotPredictImagesRequest> request) async {
    return snapshotPredictImages(call, await request);
  }

  $async.Future<$6.GetChipForPredictImageResponse> getChipForPredictImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$6.GetChipForPredictImageRequest> request) async {
    return getChipForPredictImage(call, await request);
  }

  $async.Future<$6.SetP2PContextResponse> setP2PContext(
      $grpc.ServiceCall call, $6.SetP2PContextRequest request);
  $async.Future<$6.GetCameraDimensionsResponse> getCameraDimensions(
      $grpc.ServiceCall call, $6.GetCameraDimensionsRequest request);
  $async.Future<$6.GetCameraInfoResponse> getCameraInfo(
      $grpc.ServiceCall call, $6.GetCameraInfoRequest request);
  $async.Future<$6.GetDeepweedIndexToCategoryResponse>
      getDeepweedIndexToCategory(
          $grpc.ServiceCall call, $6.GetDeepweedIndexToCategoryRequest request);
  $async.Future<$6.SetDeepweedDetectionCriteriaResponse>
      setDeepweedDetectionCriteria($grpc.ServiceCall call,
          $6.SetDeepweedDetectionCriteriaRequest request);
  $async.Future<$6.GetDeepweedDetectionCriteriaResponse>
      getDeepweedDetectionCriteria($grpc.ServiceCall call,
          $6.GetDeepweedDetectionCriteriaRequest request);
  $async.Future<$6.GetDeepweedSupportedCategoriesResponse>
      getDeepweedSupportedCategories($grpc.ServiceCall call,
          $6.GetDeepweedSupportedCategoriesRequest request);
  $async.Future<$6.GetCameraTemperaturesResponse> getCameraTemperatures(
      $grpc.ServiceCall call, $6.GetCameraTemperaturesRequest request);
  $async.Future<$6.SetCameraSettingsResponse> setCameraSettings(
      $grpc.ServiceCall call, $6.SetCameraSettingsRequest request);
  $async.Future<$6.GetCameraSettingsResponse> getCameraSettings(
      $grpc.ServiceCall call, $6.GetCameraSettingsRequest request);
  $async.Future<$6.StartBurstRecordFramesResponse> startBurstRecordFrames(
      $grpc.ServiceCall call, $6.StartBurstRecordFramesRequest request);
  $async.Future<$6.StopBurstRecordFramesResponse> stopBurstRecordFrames(
      $grpc.ServiceCall call, $6.StopBurstRecordFramesRequest request);
  $async.Future<$6.GetConnectorsResponse> getConnectors(
      $grpc.ServiceCall call, $6.GetConnectorsRequest request);
  $async.Future<$6.SetConnectorsResponse> setConnectors(
      $grpc.ServiceCall call, $6.SetConnectorsRequest request);
  $async.Future<$6.GetTimingResponse> getTiming(
      $grpc.ServiceCall call, $6.GetTimingRequest request);
  $async.Future<$6.PredictResponse> predict(
      $grpc.ServiceCall call, $6.PredictRequest request);
  $async.Future<$6.LoadAndQueueResponse> loadAndQueue(
      $grpc.ServiceCall call, $6.LoadAndQueueRequest request);
  $async.Future<$6.SetImageResponse> setImage(
      $grpc.ServiceCall call, $6.SetImageRequest request);
  $async.Future<$6.UnsetImageResponse> unsetImage(
      $grpc.ServiceCall call, $6.UnsetImageRequest request);
  $async.Future<$6.GetModelPathsResponse> getModelPaths(
      $grpc.ServiceCall call, $6.GetModelPathsRequest request);
  $async.Future<$6.SetGPSLocationResponse> setGPSLocation(
      $grpc.ServiceCall call, $6.SetGPSLocationRequest request);
  $async.Future<$6.SetImplementStatusResponse> setImplementStatus(
      $grpc.ServiceCall call, $6.SetImplementStatusRequest request);
  $async.Future<$6.SetImageScoreResponse> setImageScore(
      $grpc.ServiceCall call, $6.SetImageScoreRequest request);
  $async.Future<$6.GetScoreQueueResponse> getScoreQueue(
      $grpc.ServiceCall call, $6.GetScoreQueueRequest request);
  $async.Future<$6.ListScoreQueuesResponse> listScoreQueues(
      $grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.GetMaxImageScoreResponse> getMaxImageScore(
      $grpc.ServiceCall call, $6.GetMaxImageScoreRequest request);
  $async.Future<$6.ImageAndMetadataResponse> getMaxScoredImage(
      $grpc.ServiceCall call, $6.GetMaxScoredImageRequest request);
  $async.Future<$6.P2PImageAndMetadataResponse> getLatestP2PImage(
      $grpc.ServiceCall call, $6.GetLatestP2PImageRequest request);
  $async.Future<$6.ChipImageAndMetadataResponse> getChipImage(
      $grpc.ServiceCall call, $6.GetChipImageRequest request);
  $async.Future<$6.ChipQueueInformationResponse> getChipQueueInformation(
      $grpc.ServiceCall call, $6.ChipQueueInformationRequest request);
  $async.Future<$6.FlushQueuesResponse> flushQueues(
      $grpc.ServiceCall call, $6.FlushQueuesRequest request);
  $async.Future<$6.ImageAndMetadataResponse> getLatestImage(
      $grpc.ServiceCall call, $6.GetLatestImageRequest request);
  $async.Future<$6.ImageAndMetadataResponse> getImageNearTimestamp(
      $grpc.ServiceCall call, $6.GetImageNearTimestampRequest request);
  $async.Future<$6.GetLightweightBurstRecordResponse> getLightweightBurstRecord(
      $grpc.ServiceCall call, $6.GetLightweightBurstRecordRequest request);
  $async.Future<$6.GetBootedResponse> getBooted(
      $grpc.ServiceCall call, $6.GetBootedRequest request);
  $async.Future<$6.GetReadyResponse> getReady(
      $grpc.ServiceCall call, $6.GetReadyRequest request);
  $async.Future<$6.DeepweedOutput> getDeepweedOutputByTimestamp(
      $grpc.ServiceCall call, $6.GetDeepweedOutputByTimestampRequest request);
  $async.Future<$6.GetRecommendedStrobeSettingsResponse>
      getRecommendedStrobeSettings($grpc.ServiceCall call,
          $6.GetRecommendedStrobeSettingsRequest request);
  $async.Future<$6.P2PCaptureResponse> p2PCapture(
      $grpc.ServiceCall call, $6.P2PCaptureRequest request);
  $async.Future<$6.SetAutoWhitebalanceResponse> setAutoWhitebalance(
      $grpc.ServiceCall call, $6.SetAutoWhitebalanceRequest request);
  $async.Future<$6.DeepweedOutput> getNextDeepweedOutput(
      $grpc.ServiceCall call, $6.GetNextDeepweedOutputRequest request);
  $async.Future<$6.P2POutputProto> getNextP2POutput(
      $grpc.ServiceCall call, $6.GetNextP2POutputRequest request);
  $async.Future<$6.SetTargetingStateResponse> setTargetingState(
      $grpc.ServiceCall call, $6.SetTargetingStateRequest request);
  $async.Future<$6.P2PBufferringBurstCaptureResponse> p2PBufferringBurstCapture(
      $grpc.ServiceCall call, $6.P2PBufferringBurstCaptureRequest request);
  $async.Future<$6.GetNextFocusMetricResponse> getNextFocusMetric(
      $grpc.ServiceCall call, $6.GetNextFocusMetricRequest request);
  $async.Future<$6.RemoveDataDirResponse> removeDataDir(
      $grpc.ServiceCall call, $6.RemoveDataDirRequest request);
  $async.Stream<$6.ImageAndMetadataResponse> getLastNImages(
      $grpc.ServiceCall call, $6.LastNImageRequest request);
  $async.Future<$6.ComputeCapabilitiesResponse> getComputeCapabilities(
      $grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.SupportedTensorRTVersionsResponse>
      getSupportedTensorRTVersions($grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.Empty> reloadCategoryCollection(
      $grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.GetCategoryCollectionResponse> getCategoryCollection(
      $grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.GetErrorStateResponse> getErrorState(
      $grpc.ServiceCall call, $6.Empty request);
  $async.Future<$6.SnapshotPredictImagesResponse> snapshotPredictImages(
      $grpc.ServiceCall call, $6.SnapshotPredictImagesRequest request);
  $async.Future<$6.GetChipForPredictImageResponse> getChipForPredictImage(
      $grpc.ServiceCall call, $6.GetChipForPredictImageRequest request);
}
