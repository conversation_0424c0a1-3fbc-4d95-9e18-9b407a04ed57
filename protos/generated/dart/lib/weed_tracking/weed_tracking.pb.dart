///
//  Generated code. Do not modify.
//  source: weed_tracking/weed_tracking.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'weed_tracking.pbenum.dart';

export 'weed_tracking.pbenum.dart';

class Empty extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Empty', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  Empty._() : super();
  factory Empty() => create();
  factory Empty.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Empty.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Empty clone() => Empty()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Empty copyWith(void Function(Empty) updates) => super.copyWith((message) => updates(message as Empty)) as Empty; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Empty create() => Empty._();
  Empty createEmptyInstance() => create();
  static $pb.PbList<Empty> createRepeated() => $pb.PbList<Empty>();
  @$core.pragma('dart2js:noInline')
  static Empty getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Empty>(create);
  static Empty? _defaultInstance;
}

class CategoryPrediction extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryPrediction', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  CategoryPrediction._() : super();
  factory CategoryPrediction({
    $core.String? category,
    $core.double? score,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    if (score != null) {
      _result.score = score;
    }
    return _result;
  }
  factory CategoryPrediction.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryPrediction.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryPrediction clone() => CategoryPrediction()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryPrediction copyWith(void Function(CategoryPrediction) updates) => super.copyWith((message) => updates(message as CategoryPrediction)) as CategoryPrediction; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryPrediction create() => CategoryPrediction._();
  CategoryPrediction createEmptyInstance() => create();
  static $pb.PbList<CategoryPrediction> createRepeated() => $pb.PbList<CategoryPrediction>();
  @$core.pragma('dart2js:noInline')
  static CategoryPrediction getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryPrediction>(create);
  static CategoryPrediction? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get category => $_getSZ(0);
  @$pb.TagNumber(1)
  set category($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get score => $_getN(1);
  @$pb.TagNumber(2)
  set score($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasScore() => $_has(1);
  @$pb.TagNumber(2)
  void clearScore() => clearField(2);
}

class Trajectory extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Trajectory', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trackerId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OU3)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeed')
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xMm', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yMm', $pb.PbFieldType.OD)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zMm', $pb.PbFieldType.OD)
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intersectedWithNonshootable')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nonshootableTypeString')
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deduplicatedAcrossTracker')
    ..hasRequiredFields = false
  ;

  Trajectory._() : super();
  factory Trajectory({
    $core.int? id,
    $core.int? trackerId,
    $core.int? status,
    $core.bool? isWeed,
    $core.double? xMm,
    $core.double? yMm,
    $core.double? zMm,
    $core.bool? intersectedWithNonshootable,
    $core.String? nonshootableTypeString,
    $core.bool? deduplicatedAcrossTracker,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (trackerId != null) {
      _result.trackerId = trackerId;
    }
    if (status != null) {
      _result.status = status;
    }
    if (isWeed != null) {
      _result.isWeed = isWeed;
    }
    if (xMm != null) {
      _result.xMm = xMm;
    }
    if (yMm != null) {
      _result.yMm = yMm;
    }
    if (zMm != null) {
      _result.zMm = zMm;
    }
    if (intersectedWithNonshootable != null) {
      _result.intersectedWithNonshootable = intersectedWithNonshootable;
    }
    if (nonshootableTypeString != null) {
      _result.nonshootableTypeString = nonshootableTypeString;
    }
    if (deduplicatedAcrossTracker != null) {
      _result.deduplicatedAcrossTracker = deduplicatedAcrossTracker;
    }
    return _result;
  }
  factory Trajectory.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Trajectory.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Trajectory clone() => Trajectory()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Trajectory copyWith(void Function(Trajectory) updates) => super.copyWith((message) => updates(message as Trajectory)) as Trajectory; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Trajectory create() => Trajectory._();
  Trajectory createEmptyInstance() => create();
  static $pb.PbList<Trajectory> createRepeated() => $pb.PbList<Trajectory>();
  @$core.pragma('dart2js:noInline')
  static Trajectory getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Trajectory>(create);
  static Trajectory? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get id => $_getIZ(0);
  @$pb.TagNumber(1)
  set id($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get trackerId => $_getIZ(1);
  @$pb.TagNumber(2)
  set trackerId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTrackerId() => $_has(1);
  @$pb.TagNumber(2)
  void clearTrackerId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get status => $_getIZ(2);
  @$pb.TagNumber(3)
  set status($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStatus() => $_has(2);
  @$pb.TagNumber(3)
  void clearStatus() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get isWeed => $_getBF(3);
  @$pb.TagNumber(4)
  set isWeed($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIsWeed() => $_has(3);
  @$pb.TagNumber(4)
  void clearIsWeed() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get xMm => $_getN(4);
  @$pb.TagNumber(5)
  set xMm($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasXMm() => $_has(4);
  @$pb.TagNumber(5)
  void clearXMm() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get yMm => $_getN(5);
  @$pb.TagNumber(6)
  set yMm($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasYMm() => $_has(5);
  @$pb.TagNumber(6)
  void clearYMm() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get zMm => $_getN(6);
  @$pb.TagNumber(7)
  set zMm($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasZMm() => $_has(6);
  @$pb.TagNumber(7)
  void clearZMm() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get intersectedWithNonshootable => $_getBF(7);
  @$pb.TagNumber(8)
  set intersectedWithNonshootable($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasIntersectedWithNonshootable() => $_has(7);
  @$pb.TagNumber(8)
  void clearIntersectedWithNonshootable() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get nonshootableTypeString => $_getSZ(8);
  @$pb.TagNumber(9)
  set nonshootableTypeString($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasNonshootableTypeString() => $_has(8);
  @$pb.TagNumber(9)
  void clearNonshootableTypeString() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get deduplicatedAcrossTracker => $_getBF(9);
  @$pb.TagNumber(10)
  set deduplicatedAcrossTracker($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasDeduplicatedAcrossTracker() => $_has(9);
  @$pb.TagNumber(10)
  void clearDeduplicatedAcrossTracker() => clearField(10);
}

class Target extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Target', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scannerId', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextTrajectoryId', $pb.PbFieldType.OU3)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startingPosY', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endingPosY', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  Target._() : super();
  factory Target({
    $core.int? scannerId,
    $core.int? trajectoryId,
    $core.int? nextTrajectoryId,
    $core.double? startingPosY,
    $core.double? endingPosY,
  }) {
    final _result = create();
    if (scannerId != null) {
      _result.scannerId = scannerId;
    }
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    if (nextTrajectoryId != null) {
      _result.nextTrajectoryId = nextTrajectoryId;
    }
    if (startingPosY != null) {
      _result.startingPosY = startingPosY;
    }
    if (endingPosY != null) {
      _result.endingPosY = endingPosY;
    }
    return _result;
  }
  factory Target.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Target.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Target clone() => Target()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Target copyWith(void Function(Target) updates) => super.copyWith((message) => updates(message as Target)) as Target; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Target create() => Target._();
  Target createEmptyInstance() => create();
  static $pb.PbList<Target> createRepeated() => $pb.PbList<Target>();
  @$core.pragma('dart2js:noInline')
  static Target getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Target>(create);
  static Target? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get scannerId => $_getIZ(0);
  @$pb.TagNumber(1)
  set scannerId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScannerId() => $_has(0);
  @$pb.TagNumber(1)
  void clearScannerId() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get trajectoryId => $_getIZ(1);
  @$pb.TagNumber(2)
  set trajectoryId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTrajectoryId() => $_has(1);
  @$pb.TagNumber(2)
  void clearTrajectoryId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get nextTrajectoryId => $_getIZ(2);
  @$pb.TagNumber(3)
  set nextTrajectoryId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasNextTrajectoryId() => $_has(2);
  @$pb.TagNumber(3)
  void clearNextTrajectoryId() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get startingPosY => $_getN(3);
  @$pb.TagNumber(4)
  set startingPosY($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasStartingPosY() => $_has(3);
  @$pb.TagNumber(4)
  void clearStartingPosY() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get endingPosY => $_getN(4);
  @$pb.TagNumber(5)
  set endingPosY($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasEndingPosY() => $_has(4);
  @$pb.TagNumber(5)
  void clearEndingPosY() => clearField(5);
}

class Bounds extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Bounds', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trackerId')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxPosMmY', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  Bounds._() : super();
  factory Bounds({
    $core.String? trackerId,
    $core.double? maxPosMmY,
  }) {
    final _result = create();
    if (trackerId != null) {
      _result.trackerId = trackerId;
    }
    if (maxPosMmY != null) {
      _result.maxPosMmY = maxPosMmY;
    }
    return _result;
  }
  factory Bounds.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Bounds.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Bounds clone() => Bounds()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Bounds copyWith(void Function(Bounds) updates) => super.copyWith((message) => updates(message as Bounds)) as Bounds; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Bounds create() => Bounds._();
  Bounds createEmptyInstance() => create();
  static $pb.PbList<Bounds> createRepeated() => $pb.PbList<Bounds>();
  @$core.pragma('dart2js:noInline')
  static Bounds getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Bounds>(create);
  static Bounds? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get trackerId => $_getSZ(0);
  @$pb.TagNumber(1)
  set trackerId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrackerId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrackerId() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get maxPosMmY => $_getN(1);
  @$pb.TagNumber(2)
  set maxPosMmY($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMaxPosMmY() => $_has(1);
  @$pb.TagNumber(2)
  void clearMaxPosMmY() => clearField(2);
}

class TrackingStatusReply extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrackingStatusReply', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<Target>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targets', $pb.PbFieldType.PM, subBuilder: Target.create)
    ..pc<Trajectory>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectories', $pb.PbFieldType.PM, subBuilder: Trajectory.create)
    ..pc<Bounds>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bounds', $pb.PbFieldType.PM, subBuilder: Bounds.create)
    ..hasRequiredFields = false
  ;

  TrackingStatusReply._() : super();
  factory TrackingStatusReply({
    $core.Iterable<Target>? targets,
    $core.Iterable<Trajectory>? trajectories,
    $core.Iterable<Bounds>? bounds,
  }) {
    final _result = create();
    if (targets != null) {
      _result.targets.addAll(targets);
    }
    if (trajectories != null) {
      _result.trajectories.addAll(trajectories);
    }
    if (bounds != null) {
      _result.bounds.addAll(bounds);
    }
    return _result;
  }
  factory TrackingStatusReply.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrackingStatusReply.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrackingStatusReply clone() => TrackingStatusReply()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrackingStatusReply copyWith(void Function(TrackingStatusReply) updates) => super.copyWith((message) => updates(message as TrackingStatusReply)) as TrackingStatusReply; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrackingStatusReply create() => TrackingStatusReply._();
  TrackingStatusReply createEmptyInstance() => create();
  static $pb.PbList<TrackingStatusReply> createRepeated() => $pb.PbList<TrackingStatusReply>();
  @$core.pragma('dart2js:noInline')
  static TrackingStatusReply getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrackingStatusReply>(create);
  static TrackingStatusReply? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Target> get targets => $_getList(0);

  @$pb.TagNumber(2)
  $core.List<Trajectory> get trajectories => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<Bounds> get bounds => $_getList(2);
}

class GetDetectionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDetectionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxX', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  GetDetectionsRequest._() : super();
  factory GetDetectionsRequest({
    $core.String? camId,
    $fixnum.Int64? timestampMs,
    $core.int? maxX,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (maxX != null) {
      _result.maxX = maxX;
    }
    return _result;
  }
  factory GetDetectionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDetectionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDetectionsRequest clone() => GetDetectionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDetectionsRequest copyWith(void Function(GetDetectionsRequest) updates) => super.copyWith((message) => updates(message as GetDetectionsRequest)) as GetDetectionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDetectionsRequest create() => GetDetectionsRequest._();
  GetDetectionsRequest createEmptyInstance() => create();
  static $pb.PbList<GetDetectionsRequest> createRepeated() => $pb.PbList<GetDetectionsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDetectionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDetectionsRequest>(create);
  static GetDetectionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get maxX => $_getIZ(2);
  @$pb.TagNumber(3)
  set maxX($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMaxX() => $_has(2);
  @$pb.TagNumber(3)
  void clearMaxX() => clearField(3);
}

class Detection extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Detection', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'size', $pb.PbFieldType.OF)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'clz')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeed')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'outOfBand')
    ..a<$core.int>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU3)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OF)
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedScore', $pb.PbFieldType.OF)
    ..a<$core.double>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropScore', $pb.PbFieldType.OF)
    ..p<$core.double>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'embedding', $pb.PbFieldType.KF)
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantScore', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  Detection._() : super();
  factory Detection({
    $core.double? x,
    $core.double? y,
    $core.double? size,
    $core.String? clz,
    $core.bool? isWeed,
    $core.bool? outOfBand,
    $core.int? id,
    $core.double? score,
    $core.double? weedScore,
    $core.double? cropScore,
    $core.Iterable<$core.double>? embedding,
    $core.double? plantScore,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    if (size != null) {
      _result.size = size;
    }
    if (clz != null) {
      _result.clz = clz;
    }
    if (isWeed != null) {
      _result.isWeed = isWeed;
    }
    if (outOfBand != null) {
      _result.outOfBand = outOfBand;
    }
    if (id != null) {
      _result.id = id;
    }
    if (score != null) {
      _result.score = score;
    }
    if (weedScore != null) {
      _result.weedScore = weedScore;
    }
    if (cropScore != null) {
      _result.cropScore = cropScore;
    }
    if (embedding != null) {
      _result.embedding.addAll(embedding);
    }
    if (plantScore != null) {
      _result.plantScore = plantScore;
    }
    return _result;
  }
  factory Detection.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Detection.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Detection clone() => Detection()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Detection copyWith(void Function(Detection) updates) => super.copyWith((message) => updates(message as Detection)) as Detection; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Detection create() => Detection._();
  Detection createEmptyInstance() => create();
  static $pb.PbList<Detection> createRepeated() => $pb.PbList<Detection>();
  @$core.pragma('dart2js:noInline')
  static Detection getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Detection>(create);
  static Detection? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get x => $_getN(0);
  @$pb.TagNumber(1)
  set x($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get y => $_getN(1);
  @$pb.TagNumber(2)
  set y($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasY() => $_has(1);
  @$pb.TagNumber(2)
  void clearY() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get size => $_getN(2);
  @$pb.TagNumber(3)
  set size($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSize() => $_has(2);
  @$pb.TagNumber(3)
  void clearSize() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get clz => $_getSZ(3);
  @$pb.TagNumber(4)
  set clz($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasClz() => $_has(3);
  @$pb.TagNumber(4)
  void clearClz() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get isWeed => $_getBF(4);
  @$pb.TagNumber(5)
  set isWeed($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIsWeed() => $_has(4);
  @$pb.TagNumber(5)
  void clearIsWeed() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get outOfBand => $_getBF(5);
  @$pb.TagNumber(6)
  set outOfBand($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasOutOfBand() => $_has(5);
  @$pb.TagNumber(6)
  void clearOutOfBand() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get id => $_getIZ(6);
  @$pb.TagNumber(7)
  set id($core.int v) { $_setUnsignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasId() => $_has(6);
  @$pb.TagNumber(7)
  void clearId() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get score => $_getN(7);
  @$pb.TagNumber(8)
  set score($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasScore() => $_has(7);
  @$pb.TagNumber(8)
  void clearScore() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get weedScore => $_getN(8);
  @$pb.TagNumber(9)
  set weedScore($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWeedScore() => $_has(8);
  @$pb.TagNumber(9)
  void clearWeedScore() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get cropScore => $_getN(9);
  @$pb.TagNumber(10)
  set cropScore($core.double v) { $_setFloat(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCropScore() => $_has(9);
  @$pb.TagNumber(10)
  void clearCropScore() => clearField(10);

  @$pb.TagNumber(11)
  $core.List<$core.double> get embedding => $_getList(10);

  @$pb.TagNumber(12)
  $core.double get plantScore => $_getN(11);
  @$pb.TagNumber(12)
  set plantScore($core.double v) { $_setFloat(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPlantScore() => $_has(11);
  @$pb.TagNumber(12)
  void clearPlantScore() => clearField(12);
}

class Detections extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Detections', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<Detection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detections', $pb.PbFieldType.PM, subBuilder: Detection.create)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  Detections._() : super();
  factory Detections({
    $core.Iterable<Detection>? detections,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (detections != null) {
      _result.detections.addAll(detections);
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory Detections.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Detections.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Detections clone() => Detections()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Detections copyWith(void Function(Detections) updates) => super.copyWith((message) => updates(message as Detections)) as Detections; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Detections create() => Detections._();
  Detections createEmptyInstance() => create();
  static $pb.PbList<Detections> createRepeated() => $pb.PbList<Detections>();
  @$core.pragma('dart2js:noInline')
  static Detections getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Detections>(create);
  static Detections? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Detection> get detections => $_getList(0);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);
}

class Band extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Band', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startXPx', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endXPx', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  Band._() : super();
  factory Band({
    $core.double? startXPx,
    $core.double? endXPx,
  }) {
    final _result = create();
    if (startXPx != null) {
      _result.startXPx = startXPx;
    }
    if (endXPx != null) {
      _result.endXPx = endXPx;
    }
    return _result;
  }
  factory Band.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Band.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Band clone() => Band()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Band copyWith(void Function(Band) updates) => super.copyWith((message) => updates(message as Band)) as Band; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Band create() => Band._();
  Band createEmptyInstance() => create();
  static $pb.PbList<Band> createRepeated() => $pb.PbList<Band>();
  @$core.pragma('dart2js:noInline')
  static Band getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Band>(create);
  static Band? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get startXPx => $_getN(0);
  @$pb.TagNumber(1)
  set startXPx($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasStartXPx() => $_has(0);
  @$pb.TagNumber(1)
  void clearStartXPx() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get endXPx => $_getN(1);
  @$pb.TagNumber(2)
  set endXPx($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasEndXPx() => $_has(1);
  @$pb.TagNumber(2)
  void clearEndXPx() => clearField(2);
}

class Bands extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Bands', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<Band>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'band', $pb.PbFieldType.PM, subBuilder: Band.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingEnabled')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowHasBandsDefined')
    ..hasRequiredFields = false
  ;

  Bands._() : super();
  factory Bands({
    $core.Iterable<Band>? band,
    $core.bool? bandingEnabled,
    $core.bool? rowHasBandsDefined,
  }) {
    final _result = create();
    if (band != null) {
      _result.band.addAll(band);
    }
    if (bandingEnabled != null) {
      _result.bandingEnabled = bandingEnabled;
    }
    if (rowHasBandsDefined != null) {
      _result.rowHasBandsDefined = rowHasBandsDefined;
    }
    return _result;
  }
  factory Bands.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Bands.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Bands clone() => Bands()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Bands copyWith(void Function(Bands) updates) => super.copyWith((message) => updates(message as Bands)) as Bands; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Bands create() => Bands._();
  Bands createEmptyInstance() => create();
  static $pb.PbList<Bands> createRepeated() => $pb.PbList<Bands>();
  @$core.pragma('dart2js:noInline')
  static Bands getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Bands>(create);
  static Bands? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Band> get band => $_getList(0);

  @$pb.TagNumber(2)
  $core.bool get bandingEnabled => $_getBF(1);
  @$pb.TagNumber(2)
  set bandingEnabled($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBandingEnabled() => $_has(1);
  @$pb.TagNumber(2)
  void clearBandingEnabled() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get rowHasBandsDefined => $_getBF(2);
  @$pb.TagNumber(3)
  set rowHasBandsDefined($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRowHasBandsDefined() => $_has(2);
  @$pb.TagNumber(3)
  void clearRowHasBandsDefined() => clearField(3);
}

class GetDetectionsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDetectionsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOM<Detections>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detections', subBuilder: Detections.create)
    ..aOM<Bands>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bands', subBuilder: Bands.create)
    ..hasRequiredFields = false
  ;

  GetDetectionsResponse._() : super();
  factory GetDetectionsResponse({
    Detections? detections,
    Bands? bands,
  }) {
    final _result = create();
    if (detections != null) {
      _result.detections = detections;
    }
    if (bands != null) {
      _result.bands = bands;
    }
    return _result;
  }
  factory GetDetectionsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDetectionsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDetectionsResponse clone() => GetDetectionsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDetectionsResponse copyWith(void Function(GetDetectionsResponse) updates) => super.copyWith((message) => updates(message as GetDetectionsResponse)) as GetDetectionsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDetectionsResponse create() => GetDetectionsResponse._();
  GetDetectionsResponse createEmptyInstance() => create();
  static $pb.PbList<GetDetectionsResponse> createRepeated() => $pb.PbList<GetDetectionsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDetectionsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDetectionsResponse>(create);
  static GetDetectionsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Detections get detections => $_getN(0);
  @$pb.TagNumber(1)
  set detections(Detections v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDetections() => $_has(0);
  @$pb.TagNumber(1)
  void clearDetections() => clearField(1);
  @$pb.TagNumber(1)
  Detections ensureDetections() => $_ensure(0);

  @$pb.TagNumber(2)
  Bands get bands => $_getN(1);
  @$pb.TagNumber(2)
  set bands(Bands v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasBands() => $_has(1);
  @$pb.TagNumber(2)
  void clearBands() => clearField(2);
  @$pb.TagNumber(2)
  Bands ensureBands() => $_ensure(1);
}

class GetTrajectoryMetadataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTrajectoryMetadataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetTrajectoryMetadataRequest._() : super();
  factory GetTrajectoryMetadataRequest() => create();
  factory GetTrajectoryMetadataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrajectoryMetadataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrajectoryMetadataRequest clone() => GetTrajectoryMetadataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrajectoryMetadataRequest copyWith(void Function(GetTrajectoryMetadataRequest) updates) => super.copyWith((message) => updates(message as GetTrajectoryMetadataRequest)) as GetTrajectoryMetadataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryMetadataRequest create() => GetTrajectoryMetadataRequest._();
  GetTrajectoryMetadataRequest createEmptyInstance() => create();
  static $pb.PbList<GetTrajectoryMetadataRequest> createRepeated() => $pb.PbList<GetTrajectoryMetadataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryMetadataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrajectoryMetadataRequest>(create);
  static GetTrajectoryMetadataRequest? _defaultInstance;
}

class TrackedItemMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrackedItemMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detectionId', $pb.PbFieldType.OU3)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  TrackedItemMetadata._() : super();
  factory TrackedItemMetadata({
    $core.int? detectionId,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (detectionId != null) {
      _result.detectionId = detectionId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory TrackedItemMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrackedItemMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrackedItemMetadata clone() => TrackedItemMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrackedItemMetadata copyWith(void Function(TrackedItemMetadata) updates) => super.copyWith((message) => updates(message as TrackedItemMetadata)) as TrackedItemMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrackedItemMetadata create() => TrackedItemMetadata._();
  TrackedItemMetadata createEmptyInstance() => create();
  static $pb.PbList<TrackedItemMetadata> createRepeated() => $pb.PbList<TrackedItemMetadata>();
  @$core.pragma('dart2js:noInline')
  static TrackedItemMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrackedItemMetadata>(create);
  static TrackedItemMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get detectionId => $_getIZ(0);
  @$pb.TagNumber(1)
  set detectionId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDetectionId() => $_has(0);
  @$pb.TagNumber(1)
  void clearDetectionId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);
}

class TrajectoryMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectoryMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..pc<TrackedItemMetadata>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trackedItemMetadata', $pb.PbFieldType.PM, subBuilder: TrackedItemMetadata.create)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandStatus')
    ..hasRequiredFields = false
  ;

  TrajectoryMetadata._() : super();
  factory TrajectoryMetadata({
    $core.int? trajectoryId,
    $core.String? camId,
    $core.Iterable<TrackedItemMetadata>? trackedItemMetadata,
    $core.String? bandStatus,
  }) {
    final _result = create();
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (trackedItemMetadata != null) {
      _result.trackedItemMetadata.addAll(trackedItemMetadata);
    }
    if (bandStatus != null) {
      _result.bandStatus = bandStatus;
    }
    return _result;
  }
  factory TrajectoryMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectoryMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectoryMetadata clone() => TrajectoryMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectoryMetadata copyWith(void Function(TrajectoryMetadata) updates) => super.copyWith((message) => updates(message as TrajectoryMetadata)) as TrajectoryMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectoryMetadata create() => TrajectoryMetadata._();
  TrajectoryMetadata createEmptyInstance() => create();
  static $pb.PbList<TrajectoryMetadata> createRepeated() => $pb.PbList<TrajectoryMetadata>();
  @$core.pragma('dart2js:noInline')
  static TrajectoryMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectoryMetadata>(create);
  static TrajectoryMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trajectoryId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trajectoryId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrajectoryId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrajectoryId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get camId => $_getSZ(1);
  @$pb.TagNumber(2)
  set camId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCamId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCamId() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<TrackedItemMetadata> get trackedItemMetadata => $_getList(2);

  @$pb.TagNumber(4)
  $core.String get bandStatus => $_getSZ(3);
  @$pb.TagNumber(4)
  set bandStatus($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBandStatus() => $_has(3);
  @$pb.TagNumber(4)
  void clearBandStatus() => clearField(4);
}

class GetTrajectoryMetadataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTrajectoryMetadataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<TrajectoryMetadata>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', $pb.PbFieldType.PM, subBuilder: TrajectoryMetadata.create)
    ..hasRequiredFields = false
  ;

  GetTrajectoryMetadataResponse._() : super();
  factory GetTrajectoryMetadataResponse({
    $core.Iterable<TrajectoryMetadata>? metadata,
  }) {
    final _result = create();
    if (metadata != null) {
      _result.metadata.addAll(metadata);
    }
    return _result;
  }
  factory GetTrajectoryMetadataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrajectoryMetadataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrajectoryMetadataResponse clone() => GetTrajectoryMetadataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrajectoryMetadataResponse copyWith(void Function(GetTrajectoryMetadataResponse) updates) => super.copyWith((message) => updates(message as GetTrajectoryMetadataResponse)) as GetTrajectoryMetadataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryMetadataResponse create() => GetTrajectoryMetadataResponse._();
  GetTrajectoryMetadataResponse createEmptyInstance() => create();
  static $pb.PbList<GetTrajectoryMetadataResponse> createRepeated() => $pb.PbList<GetTrajectoryMetadataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryMetadataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrajectoryMetadataResponse>(create);
  static GetTrajectoryMetadataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<TrajectoryMetadata> get metadata => $_getList(0);
}

class GetCurrentTrajectoriesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCurrentTrajectoriesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetCurrentTrajectoriesRequest._() : super();
  factory GetCurrentTrajectoriesRequest() => create();
  factory GetCurrentTrajectoriesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCurrentTrajectoriesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCurrentTrajectoriesRequest clone() => GetCurrentTrajectoriesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCurrentTrajectoriesRequest copyWith(void Function(GetCurrentTrajectoriesRequest) updates) => super.copyWith((message) => updates(message as GetCurrentTrajectoriesRequest)) as GetCurrentTrajectoriesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCurrentTrajectoriesRequest create() => GetCurrentTrajectoriesRequest._();
  GetCurrentTrajectoriesRequest createEmptyInstance() => create();
  static $pb.PbList<GetCurrentTrajectoriesRequest> createRepeated() => $pb.PbList<GetCurrentTrajectoriesRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCurrentTrajectoriesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCurrentTrajectoriesRequest>(create);
  static GetCurrentTrajectoriesRequest? _defaultInstance;
}

class PingRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PingRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  PingRequest._() : super();
  factory PingRequest({
    $core.int? x,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    return _result;
  }
  factory PingRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PingRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PingRequest clone() => PingRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PingRequest copyWith(void Function(PingRequest) updates) => super.copyWith((message) => updates(message as PingRequest)) as PingRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PingRequest create() => PingRequest._();
  PingRequest createEmptyInstance() => create();
  static $pb.PbList<PingRequest> createRepeated() => $pb.PbList<PingRequest>();
  @$core.pragma('dart2js:noInline')
  static PingRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PingRequest>(create);
  static PingRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get x => $_getIZ(0);
  @$pb.TagNumber(1)
  set x($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);
}

class PongReply extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PongReply', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  PongReply._() : super();
  factory PongReply({
    $core.int? x,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    return _result;
  }
  factory PongReply.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PongReply.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PongReply clone() => PongReply()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PongReply copyWith(void Function(PongReply) updates) => super.copyWith((message) => updates(message as PongReply)) as PongReply; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PongReply create() => PongReply._();
  PongReply createEmptyInstance() => create();
  static $pb.PbList<PongReply> createRepeated() => $pb.PbList<PongReply>();
  @$core.pragma('dart2js:noInline')
  static PongReply getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PongReply>(create);
  static PongReply? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get x => $_getIZ(0);
  @$pb.TagNumber(1)
  set x($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);
}

class TrajectoryScore extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectoryScore', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreTilt', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scorePan', $pb.PbFieldType.OD)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreBusy', $pb.PbFieldType.OD)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreInRange', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreImportance', $pb.PbFieldType.OD)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreSize', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  TrajectoryScore._() : super();
  factory TrajectoryScore({
    $fixnum.Int64? score,
    $core.double? scoreTilt,
    $core.double? scorePan,
    $core.double? scoreBusy,
    $core.double? scoreInRange,
    $core.double? scoreImportance,
    $core.double? scoreSize,
  }) {
    final _result = create();
    if (score != null) {
      _result.score = score;
    }
    if (scoreTilt != null) {
      _result.scoreTilt = scoreTilt;
    }
    if (scorePan != null) {
      _result.scorePan = scorePan;
    }
    if (scoreBusy != null) {
      _result.scoreBusy = scoreBusy;
    }
    if (scoreInRange != null) {
      _result.scoreInRange = scoreInRange;
    }
    if (scoreImportance != null) {
      _result.scoreImportance = scoreImportance;
    }
    if (scoreSize != null) {
      _result.scoreSize = scoreSize;
    }
    return _result;
  }
  factory TrajectoryScore.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectoryScore.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectoryScore clone() => TrajectoryScore()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectoryScore copyWith(void Function(TrajectoryScore) updates) => super.copyWith((message) => updates(message as TrajectoryScore)) as TrajectoryScore; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectoryScore create() => TrajectoryScore._();
  TrajectoryScore createEmptyInstance() => create();
  static $pb.PbList<TrajectoryScore> createRepeated() => $pb.PbList<TrajectoryScore>();
  @$core.pragma('dart2js:noInline')
  static TrajectoryScore getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectoryScore>(create);
  static TrajectoryScore? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get score => $_getI64(0);
  @$pb.TagNumber(1)
  set score($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScore() => $_has(0);
  @$pb.TagNumber(1)
  void clearScore() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get scoreTilt => $_getN(1);
  @$pb.TagNumber(2)
  set scoreTilt($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasScoreTilt() => $_has(1);
  @$pb.TagNumber(2)
  void clearScoreTilt() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get scorePan => $_getN(2);
  @$pb.TagNumber(3)
  set scorePan($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasScorePan() => $_has(2);
  @$pb.TagNumber(3)
  void clearScorePan() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get scoreBusy => $_getN(3);
  @$pb.TagNumber(4)
  set scoreBusy($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasScoreBusy() => $_has(3);
  @$pb.TagNumber(4)
  void clearScoreBusy() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get scoreInRange => $_getN(4);
  @$pb.TagNumber(5)
  set scoreInRange($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasScoreInRange() => $_has(4);
  @$pb.TagNumber(5)
  void clearScoreInRange() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get scoreImportance => $_getN(5);
  @$pb.TagNumber(6)
  set scoreImportance($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasScoreImportance() => $_has(5);
  @$pb.TagNumber(6)
  void clearScoreImportance() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get scoreSize => $_getN(6);
  @$pb.TagNumber(7)
  set scoreSize($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasScoreSize() => $_has(6);
  @$pb.TagNumber(7)
  void clearScoreSize() => clearField(7);
}

class PerScannerScore extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PerScannerScore', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scannerId', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  PerScannerScore._() : super();
  factory PerScannerScore({
    $core.int? scannerId,
    $core.int? score,
  }) {
    final _result = create();
    if (scannerId != null) {
      _result.scannerId = scannerId;
    }
    if (score != null) {
      _result.score = score;
    }
    return _result;
  }
  factory PerScannerScore.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PerScannerScore.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PerScannerScore clone() => PerScannerScore()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PerScannerScore copyWith(void Function(PerScannerScore) updates) => super.copyWith((message) => updates(message as PerScannerScore)) as PerScannerScore; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PerScannerScore create() => PerScannerScore._();
  PerScannerScore createEmptyInstance() => create();
  static $pb.PbList<PerScannerScore> createRepeated() => $pb.PbList<PerScannerScore>();
  @$core.pragma('dart2js:noInline')
  static PerScannerScore getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PerScannerScore>(create);
  static PerScannerScore? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get scannerId => $_getIZ(0);
  @$pb.TagNumber(1)
  set scannerId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScannerId() => $_has(0);
  @$pb.TagNumber(1)
  void clearScannerId() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get score => $_getIZ(1);
  @$pb.TagNumber(2)
  set score($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasScore() => $_has(1);
  @$pb.TagNumber(2)
  void clearScore() => clearField(2);
}

class ScoreState extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ScoreState', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..e<TargetableState>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetState', $pb.PbFieldType.OE, defaultOrMaker: TargetableState.TARGET_NOT_IN_SCHEDULER, valueOf: TargetableState.valueOf, enumValues: TargetableState.values)
    ..pc<PerScannerScore>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scores', $pb.PbFieldType.PM, subBuilder: PerScannerScore.create)
    ..hasRequiredFields = false
  ;

  ScoreState._() : super();
  factory ScoreState({
    TargetableState? targetState,
    $core.Iterable<PerScannerScore>? scores,
  }) {
    final _result = create();
    if (targetState != null) {
      _result.targetState = targetState;
    }
    if (scores != null) {
      _result.scores.addAll(scores);
    }
    return _result;
  }
  factory ScoreState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ScoreState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ScoreState clone() => ScoreState()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ScoreState copyWith(void Function(ScoreState) updates) => super.copyWith((message) => updates(message as ScoreState)) as ScoreState; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ScoreState create() => ScoreState._();
  ScoreState createEmptyInstance() => create();
  static $pb.PbList<ScoreState> createRepeated() => $pb.PbList<ScoreState>();
  @$core.pragma('dart2js:noInline')
  static ScoreState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ScoreState>(create);
  static ScoreState? _defaultInstance;

  @$pb.TagNumber(1)
  TargetableState get targetState => $_getN(0);
  @$pb.TagNumber(1)
  set targetState(TargetableState v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetState() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetState() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<PerScannerScore> get scores => $_getList(1);
}

class Pos2D extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Pos2D', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  Pos2D._() : super();
  factory Pos2D({
    $core.double? x,
    $core.double? y,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    return _result;
  }
  factory Pos2D.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Pos2D.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Pos2D clone() => Pos2D()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Pos2D copyWith(void Function(Pos2D) updates) => super.copyWith((message) => updates(message as Pos2D)) as Pos2D; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Pos2D create() => Pos2D._();
  Pos2D createEmptyInstance() => create();
  static $pb.PbList<Pos2D> createRepeated() => $pb.PbList<Pos2D>();
  @$core.pragma('dart2js:noInline')
  static Pos2D getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Pos2D>(create);
  static Pos2D? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get x => $_getN(0);
  @$pb.TagNumber(1)
  set x($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get y => $_getN(1);
  @$pb.TagNumber(2)
  set y($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasY() => $_has(1);
  @$pb.TagNumber(2)
  void clearY() => clearField(2);
}

class KillBox extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'KillBox', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scannerId', $pb.PbFieldType.OU3)
    ..aOM<Pos2D>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'topLeft', subBuilder: Pos2D.create)
    ..aOM<Pos2D>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bottomRight', subBuilder: Pos2D.create)
    ..hasRequiredFields = false
  ;

  KillBox._() : super();
  factory KillBox({
    $core.int? scannerId,
    Pos2D? topLeft,
    Pos2D? bottomRight,
  }) {
    final _result = create();
    if (scannerId != null) {
      _result.scannerId = scannerId;
    }
    if (topLeft != null) {
      _result.topLeft = topLeft;
    }
    if (bottomRight != null) {
      _result.bottomRight = bottomRight;
    }
    return _result;
  }
  factory KillBox.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory KillBox.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  KillBox clone() => KillBox()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  KillBox copyWith(void Function(KillBox) updates) => super.copyWith((message) => updates(message as KillBox)) as KillBox; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static KillBox create() => KillBox._();
  KillBox createEmptyInstance() => create();
  static $pb.PbList<KillBox> createRepeated() => $pb.PbList<KillBox>();
  @$core.pragma('dart2js:noInline')
  static KillBox getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<KillBox>(create);
  static KillBox? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get scannerId => $_getIZ(0);
  @$pb.TagNumber(1)
  set scannerId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScannerId() => $_has(0);
  @$pb.TagNumber(1)
  void clearScannerId() => clearField(1);

  @$pb.TagNumber(2)
  Pos2D get topLeft => $_getN(1);
  @$pb.TagNumber(2)
  set topLeft(Pos2D v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTopLeft() => $_has(1);
  @$pb.TagNumber(2)
  void clearTopLeft() => clearField(2);
  @$pb.TagNumber(2)
  Pos2D ensureTopLeft() => $_ensure(1);

  @$pb.TagNumber(3)
  Pos2D get bottomRight => $_getN(2);
  @$pb.TagNumber(3)
  set bottomRight(Pos2D v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasBottomRight() => $_has(2);
  @$pb.TagNumber(3)
  void clearBottomRight() => clearField(3);
  @$pb.TagNumber(3)
  Pos2D ensureBottomRight() => $_ensure(2);
}

class Thresholds extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Thresholds', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeding', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinning', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'banding', $pb.PbFieldType.OF)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'passedWeeding')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'passedThinning')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'passedBanding')
    ..hasRequiredFields = false
  ;

  Thresholds._() : super();
  factory Thresholds({
  @$core.Deprecated('This field is deprecated.')
    $core.double? weeding,
  @$core.Deprecated('This field is deprecated.')
    $core.double? thinning,
  @$core.Deprecated('This field is deprecated.')
    $core.double? banding,
    $core.bool? passedWeeding,
    $core.bool? passedThinning,
    $core.bool? passedBanding,
  }) {
    final _result = create();
    if (weeding != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.weeding = weeding;
    }
    if (thinning != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.thinning = thinning;
    }
    if (banding != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.banding = banding;
    }
    if (passedWeeding != null) {
      _result.passedWeeding = passedWeeding;
    }
    if (passedThinning != null) {
      _result.passedThinning = passedThinning;
    }
    if (passedBanding != null) {
      _result.passedBanding = passedBanding;
    }
    return _result;
  }
  factory Thresholds.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Thresholds.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Thresholds clone() => Thresholds()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Thresholds copyWith(void Function(Thresholds) updates) => super.copyWith((message) => updates(message as Thresholds)) as Thresholds; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Thresholds create() => Thresholds._();
  Thresholds createEmptyInstance() => create();
  static $pb.PbList<Thresholds> createRepeated() => $pb.PbList<Thresholds>();
  @$core.pragma('dart2js:noInline')
  static Thresholds getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Thresholds>(create);
  static Thresholds? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.double get weeding => $_getN(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set weeding($core.double v) { $_setFloat(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasWeeding() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearWeeding() => clearField(1);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.double get thinning => $_getN(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  set thinning($core.double v) { $_setFloat(1, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool hasThinning() => $_has(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  void clearThinning() => clearField(2);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.double get banding => $_getN(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  set banding($core.double v) { $_setFloat(2, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.bool hasBanding() => $_has(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  void clearBanding() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get passedWeeding => $_getBF(3);
  @$pb.TagNumber(4)
  set passedWeeding($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPassedWeeding() => $_has(3);
  @$pb.TagNumber(4)
  void clearPassedWeeding() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get passedThinning => $_getBF(4);
  @$pb.TagNumber(5)
  set passedThinning($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPassedThinning() => $_has(4);
  @$pb.TagNumber(5)
  void clearPassedThinning() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get passedBanding => $_getBF(5);
  @$pb.TagNumber(6)
  set passedBanding($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPassedBanding() => $_has(5);
  @$pb.TagNumber(6)
  void clearPassedBanding() => clearField(6);
}

class Decisions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Decisions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingWeed')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingCrop')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningWeed')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningCrop')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'keepableCrop')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingCrop')
    ..hasRequiredFields = false
  ;

  Decisions._() : super();
  factory Decisions({
    $core.bool? weedingWeed,
    $core.bool? weedingCrop,
    $core.bool? thinningWeed,
    $core.bool? thinningCrop,
    $core.bool? keepableCrop,
    $core.bool? bandingCrop,
  }) {
    final _result = create();
    if (weedingWeed != null) {
      _result.weedingWeed = weedingWeed;
    }
    if (weedingCrop != null) {
      _result.weedingCrop = weedingCrop;
    }
    if (thinningWeed != null) {
      _result.thinningWeed = thinningWeed;
    }
    if (thinningCrop != null) {
      _result.thinningCrop = thinningCrop;
    }
    if (keepableCrop != null) {
      _result.keepableCrop = keepableCrop;
    }
    if (bandingCrop != null) {
      _result.bandingCrop = bandingCrop;
    }
    return _result;
  }
  factory Decisions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Decisions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Decisions clone() => Decisions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Decisions copyWith(void Function(Decisions) updates) => super.copyWith((message) => updates(message as Decisions)) as Decisions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Decisions create() => Decisions._();
  Decisions createEmptyInstance() => create();
  static $pb.PbList<Decisions> createRepeated() => $pb.PbList<Decisions>();
  @$core.pragma('dart2js:noInline')
  static Decisions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Decisions>(create);
  static Decisions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get weedingWeed => $_getBF(0);
  @$pb.TagNumber(1)
  set weedingWeed($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedingWeed() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedingWeed() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get weedingCrop => $_getBF(1);
  @$pb.TagNumber(2)
  set weedingCrop($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWeedingCrop() => $_has(1);
  @$pb.TagNumber(2)
  void clearWeedingCrop() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get thinningWeed => $_getBF(2);
  @$pb.TagNumber(3)
  set thinningWeed($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasThinningWeed() => $_has(2);
  @$pb.TagNumber(3)
  void clearThinningWeed() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get thinningCrop => $_getBF(3);
  @$pb.TagNumber(4)
  set thinningCrop($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasThinningCrop() => $_has(3);
  @$pb.TagNumber(4)
  void clearThinningCrop() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get keepableCrop => $_getBF(4);
  @$pb.TagNumber(5)
  set keepableCrop($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasKeepableCrop() => $_has(4);
  @$pb.TagNumber(5)
  void clearKeepableCrop() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get bandingCrop => $_getBF(5);
  @$pb.TagNumber(6)
  set bandingCrop($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasBandingCrop() => $_has(5);
  @$pb.TagNumber(6)
  void clearBandingCrop() => clearField(6);
}

enum DetailedMetadata_ScoreDetails {
  score, 
  invalidScore, 
  notSet
}

class DetailedMetadata extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, DetailedMetadata_ScoreDetails> _DetailedMetadata_ScoreDetailsByTag = {
    21 : DetailedMetadata_ScoreDetails.score,
    22 : DetailedMetadata_ScoreDetails.invalidScore,
    0 : DetailedMetadata_ScoreDetails.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DetailedMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..oo(0, [21, 22])
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidence', $pb.PbFieldType.OD)
    ..m<$core.String, $core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detectionClasses', entryClassName: 'DetailedMetadata.DetectionClassesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OD, packageName: const $pb.PackageName('weed_tracking'))
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropScore', $pb.PbFieldType.OD)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedScore', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantScore', $pb.PbFieldType.OD)
    ..a<$core.int>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numDetectionsUsedForDecision', $pb.PbFieldType.OU3)
    ..aOM<Decisions>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'decisions', subBuilder: Decisions.create)
    ..a<$fixnum.Int64>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shootTimeRequestedMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shootTimeActualMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.int>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'speculativeShootTimeActualMs', $pb.PbFieldType.OU3)
    ..a<$core.int>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trackerId', $pb.PbFieldType.OU3)
    ..p<$core.int>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignedLasers', $pb.PbFieldType.KU3)
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'globalPos', $pb.PbFieldType.OD)
    ..a<$core.double>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'doo', $pb.PbFieldType.OF)
    ..a<$core.int>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'distancePerspectivesCount', $pb.PbFieldType.OU3)
    ..a<$core.int>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizeCategoryIndex', $pb.PbFieldType.O3)
    ..aOB(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'speculativeAllowed')
    ..aOB(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'protectedByTraj')
    ..aOM<SnapshotMetadata>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshotMetadata', subBuilder: SnapshotMetadata.create)
    ..aOM<TrajectoryScore>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', subBuilder: TrajectoryScore.create)
    ..e<InvalidScoreReason>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'invalidScore', $pb.PbFieldType.OE, defaultOrMaker: InvalidScoreReason.NONE, valueOf: InvalidScoreReason.valueOf, enumValues: InvalidScoreReason.values)
    ..hasRequiredFields = false
  ;

  DetailedMetadata._() : super();
  factory DetailedMetadata({
    $core.String? category,
    $core.double? confidence,
    $core.Map<$core.String, $core.double>? detectionClasses,
    $core.double? cropScore,
    $core.double? weedScore,
    $core.double? plantScore,
    $core.int? numDetectionsUsedForDecision,
    Decisions? decisions,
    $fixnum.Int64? shootTimeRequestedMs,
    $fixnum.Int64? shootTimeActualMs,
    $core.int? speculativeShootTimeActualMs,
    $core.int? trackerId,
    $core.Iterable<$core.int>? assignedLasers,
    $core.double? globalPos,
    $core.double? doo,
    $core.int? distancePerspectivesCount,
    $core.int? sizeCategoryIndex,
    $core.bool? speculativeAllowed,
    $core.bool? protectedByTraj,
    SnapshotMetadata? snapshotMetadata,
    TrajectoryScore? score,
    InvalidScoreReason? invalidScore,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    if (confidence != null) {
      _result.confidence = confidence;
    }
    if (detectionClasses != null) {
      _result.detectionClasses.addAll(detectionClasses);
    }
    if (cropScore != null) {
      _result.cropScore = cropScore;
    }
    if (weedScore != null) {
      _result.weedScore = weedScore;
    }
    if (plantScore != null) {
      _result.plantScore = plantScore;
    }
    if (numDetectionsUsedForDecision != null) {
      _result.numDetectionsUsedForDecision = numDetectionsUsedForDecision;
    }
    if (decisions != null) {
      _result.decisions = decisions;
    }
    if (shootTimeRequestedMs != null) {
      _result.shootTimeRequestedMs = shootTimeRequestedMs;
    }
    if (shootTimeActualMs != null) {
      _result.shootTimeActualMs = shootTimeActualMs;
    }
    if (speculativeShootTimeActualMs != null) {
      _result.speculativeShootTimeActualMs = speculativeShootTimeActualMs;
    }
    if (trackerId != null) {
      _result.trackerId = trackerId;
    }
    if (assignedLasers != null) {
      _result.assignedLasers.addAll(assignedLasers);
    }
    if (globalPos != null) {
      _result.globalPos = globalPos;
    }
    if (doo != null) {
      _result.doo = doo;
    }
    if (distancePerspectivesCount != null) {
      _result.distancePerspectivesCount = distancePerspectivesCount;
    }
    if (sizeCategoryIndex != null) {
      _result.sizeCategoryIndex = sizeCategoryIndex;
    }
    if (speculativeAllowed != null) {
      _result.speculativeAllowed = speculativeAllowed;
    }
    if (protectedByTraj != null) {
      _result.protectedByTraj = protectedByTraj;
    }
    if (snapshotMetadata != null) {
      _result.snapshotMetadata = snapshotMetadata;
    }
    if (score != null) {
      _result.score = score;
    }
    if (invalidScore != null) {
      _result.invalidScore = invalidScore;
    }
    return _result;
  }
  factory DetailedMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DetailedMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DetailedMetadata clone() => DetailedMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DetailedMetadata copyWith(void Function(DetailedMetadata) updates) => super.copyWith((message) => updates(message as DetailedMetadata)) as DetailedMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DetailedMetadata create() => DetailedMetadata._();
  DetailedMetadata createEmptyInstance() => create();
  static $pb.PbList<DetailedMetadata> createRepeated() => $pb.PbList<DetailedMetadata>();
  @$core.pragma('dart2js:noInline')
  static DetailedMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DetailedMetadata>(create);
  static DetailedMetadata? _defaultInstance;

  DetailedMetadata_ScoreDetails whichScoreDetails() => _DetailedMetadata_ScoreDetailsByTag[$_whichOneof(0)]!;
  void clearScoreDetails() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get category => $_getSZ(0);
  @$pb.TagNumber(1)
  set category($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get confidence => $_getN(1);
  @$pb.TagNumber(2)
  set confidence($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasConfidence() => $_has(1);
  @$pb.TagNumber(2)
  void clearConfidence() => clearField(2);

  @$pb.TagNumber(3)
  $core.Map<$core.String, $core.double> get detectionClasses => $_getMap(2);

  @$pb.TagNumber(4)
  $core.double get cropScore => $_getN(3);
  @$pb.TagNumber(4)
  set cropScore($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCropScore() => $_has(3);
  @$pb.TagNumber(4)
  void clearCropScore() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get weedScore => $_getN(4);
  @$pb.TagNumber(5)
  set weedScore($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasWeedScore() => $_has(4);
  @$pb.TagNumber(5)
  void clearWeedScore() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get plantScore => $_getN(5);
  @$pb.TagNumber(6)
  set plantScore($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPlantScore() => $_has(5);
  @$pb.TagNumber(6)
  void clearPlantScore() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get numDetectionsUsedForDecision => $_getIZ(6);
  @$pb.TagNumber(7)
  set numDetectionsUsedForDecision($core.int v) { $_setUnsignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasNumDetectionsUsedForDecision() => $_has(6);
  @$pb.TagNumber(7)
  void clearNumDetectionsUsedForDecision() => clearField(7);

  @$pb.TagNumber(8)
  Decisions get decisions => $_getN(7);
  @$pb.TagNumber(8)
  set decisions(Decisions v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasDecisions() => $_has(7);
  @$pb.TagNumber(8)
  void clearDecisions() => clearField(8);
  @$pb.TagNumber(8)
  Decisions ensureDecisions() => $_ensure(7);

  @$pb.TagNumber(9)
  $fixnum.Int64 get shootTimeRequestedMs => $_getI64(8);
  @$pb.TagNumber(9)
  set shootTimeRequestedMs($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasShootTimeRequestedMs() => $_has(8);
  @$pb.TagNumber(9)
  void clearShootTimeRequestedMs() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get shootTimeActualMs => $_getI64(9);
  @$pb.TagNumber(10)
  set shootTimeActualMs($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasShootTimeActualMs() => $_has(9);
  @$pb.TagNumber(10)
  void clearShootTimeActualMs() => clearField(10);

  @$pb.TagNumber(11)
  $core.int get speculativeShootTimeActualMs => $_getIZ(10);
  @$pb.TagNumber(11)
  set speculativeShootTimeActualMs($core.int v) { $_setUnsignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSpeculativeShootTimeActualMs() => $_has(10);
  @$pb.TagNumber(11)
  void clearSpeculativeShootTimeActualMs() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get trackerId => $_getIZ(11);
  @$pb.TagNumber(12)
  set trackerId($core.int v) { $_setUnsignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasTrackerId() => $_has(11);
  @$pb.TagNumber(12)
  void clearTrackerId() => clearField(12);

  @$pb.TagNumber(13)
  $core.List<$core.int> get assignedLasers => $_getList(12);

  @$pb.TagNumber(14)
  $core.double get globalPos => $_getN(13);
  @$pb.TagNumber(14)
  set globalPos($core.double v) { $_setDouble(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasGlobalPos() => $_has(13);
  @$pb.TagNumber(14)
  void clearGlobalPos() => clearField(14);

  @$pb.TagNumber(15)
  $core.double get doo => $_getN(14);
  @$pb.TagNumber(15)
  set doo($core.double v) { $_setFloat(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasDoo() => $_has(14);
  @$pb.TagNumber(15)
  void clearDoo() => clearField(15);

  @$pb.TagNumber(16)
  $core.int get distancePerspectivesCount => $_getIZ(15);
  @$pb.TagNumber(16)
  set distancePerspectivesCount($core.int v) { $_setUnsignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasDistancePerspectivesCount() => $_has(15);
  @$pb.TagNumber(16)
  void clearDistancePerspectivesCount() => clearField(16);

  @$pb.TagNumber(17)
  $core.int get sizeCategoryIndex => $_getIZ(16);
  @$pb.TagNumber(17)
  set sizeCategoryIndex($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasSizeCategoryIndex() => $_has(16);
  @$pb.TagNumber(17)
  void clearSizeCategoryIndex() => clearField(17);

  @$pb.TagNumber(18)
  $core.bool get speculativeAllowed => $_getBF(17);
  @$pb.TagNumber(18)
  set speculativeAllowed($core.bool v) { $_setBool(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasSpeculativeAllowed() => $_has(17);
  @$pb.TagNumber(18)
  void clearSpeculativeAllowed() => clearField(18);

  @$pb.TagNumber(19)
  $core.bool get protectedByTraj => $_getBF(18);
  @$pb.TagNumber(19)
  set protectedByTraj($core.bool v) { $_setBool(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasProtectedByTraj() => $_has(18);
  @$pb.TagNumber(19)
  void clearProtectedByTraj() => clearField(19);

  @$pb.TagNumber(20)
  SnapshotMetadata get snapshotMetadata => $_getN(19);
  @$pb.TagNumber(20)
  set snapshotMetadata(SnapshotMetadata v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasSnapshotMetadata() => $_has(19);
  @$pb.TagNumber(20)
  void clearSnapshotMetadata() => clearField(20);
  @$pb.TagNumber(20)
  SnapshotMetadata ensureSnapshotMetadata() => $_ensure(19);

  @$pb.TagNumber(21)
  TrajectoryScore get score => $_getN(20);
  @$pb.TagNumber(21)
  set score(TrajectoryScore v) { setField(21, v); }
  @$pb.TagNumber(21)
  $core.bool hasScore() => $_has(20);
  @$pb.TagNumber(21)
  void clearScore() => clearField(21);
  @$pb.TagNumber(21)
  TrajectoryScore ensureScore() => $_ensure(20);

  @$pb.TagNumber(22)
  InvalidScoreReason get invalidScore => $_getN(21);
  @$pb.TagNumber(22)
  set invalidScore(InvalidScoreReason v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasInvalidScore() => $_has(21);
  @$pb.TagNumber(22)
  void clearInvalidScore() => clearField(22);
}

enum TrajectorySnapshot_ScoreDetails {
  score, 
  invalidScore, 
  notSet
}

class TrajectorySnapshot extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, TrajectorySnapshot_ScoreDetails> _TrajectorySnapshot_ScoreDetailsByTag = {
    7 : TrajectorySnapshot_ScoreDetails.score,
    8 : TrajectorySnapshot_ScoreDetails.invalidScore,
    0 : TrajectorySnapshot_ScoreDetails.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectorySnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..oo(0, [7, 8])
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU3)
    ..e<KillStatus>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'killStatus', $pb.PbFieldType.OE, defaultOrMaker: KillStatus.STATUS_NOT_SHOT, valueOf: KillStatus.valueOf, enumValues: KillStatus.values)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeed')
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xMm', $pb.PbFieldType.OD)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yMm', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zMm', $pb.PbFieldType.OD)
    ..aOM<TrajectoryScore>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', subBuilder: TrajectoryScore.create)
    ..e<InvalidScoreReason>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'invalidScore', $pb.PbFieldType.OE, defaultOrMaker: InvalidScoreReason.NONE, valueOf: InvalidScoreReason.valueOf, enumValues: InvalidScoreReason.values)
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'radiusMm', $pb.PbFieldType.OD)
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$fixnum.Int64>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shootTimeRequestedMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shootTimeActualMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOB(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'markedForThinning')
    ..a<$core.int>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trackerId', $pb.PbFieldType.OU3)
    ..e<DuplicateStatus>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'duplicateStatus', $pb.PbFieldType.OE, defaultOrMaker: DuplicateStatus.UNIQUE, valueOf: DuplicateStatus.valueOf, enumValues: DuplicateStatus.values)
    ..a<$core.int>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'duplicateTrajectoryId', $pb.PbFieldType.OU3)
    ..p<$core.int>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignedLasers', $pb.PbFieldType.KU3)
    ..aOB(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'outOfBand')
    ..aOB(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intersectedWithNonshootable')
    ..m<$core.String, $core.double>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detectionClasses', entryClassName: 'TrajectorySnapshot.DetectionClassesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OD, packageName: const $pb.PackageName('weed_tracking'))
    ..a<$core.double>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidence', $pb.PbFieldType.OD)
    ..e<ThinningState>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningState', $pb.PbFieldType.OE, defaultOrMaker: ThinningState.THINNING_UNSET, valueOf: ThinningState.valueOf, enumValues: ThinningState.values)
    ..a<$core.double>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'globalPos', $pb.PbFieldType.OD)
    ..aOM<ScoreState>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreState', subBuilder: ScoreState.create)
    ..a<$core.double>(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'doo', $pb.PbFieldType.OF)
    ..a<$core.int>(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'distancePerspectivesCount', $pb.PbFieldType.OU3)
    ..a<$core.int>(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizeCategoryIndex', $pb.PbFieldType.O3)
    ..aOB(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'speculativeAllowed')
    ..aOB(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'protectedByTraj')
    ..aOM<Thresholds>(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thresholds', subBuilder: Thresholds.create)
    ..aOM<Decisions>(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'decisions', subBuilder: Decisions.create)
    ..a<$core.double>(32, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropScore', $pb.PbFieldType.OD)
    ..a<$core.double>(33, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedScore', $pb.PbFieldType.OD)
    ..a<$core.double>(34, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantScore', $pb.PbFieldType.OD)
    ..a<$core.int>(35, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numDetectionsUsedForDecision', $pb.PbFieldType.OU3)
    ..e<Classification>(36, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'classification', $pb.PbFieldType.OE, defaultOrMaker: Classification.CLASS_UNDECIDED, valueOf: Classification.valueOf, enumValues: Classification.values)
    ..a<$core.int>(38, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'speculativeShootTimeActualMs', $pb.PbFieldType.OU3)
    ..aOM<SnapshotMetadata>(39, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshotMetadata', subBuilder: SnapshotMetadata.create)
    ..aOM<DetailedMetadata>(40, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detailedMetadata', subBuilder: DetailedMetadata.create)
    ..hasRequiredFields = false
  ;

  TrajectorySnapshot._() : super();
  factory TrajectorySnapshot({
    $core.int? id,
    KillStatus? killStatus,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? isWeed,
    $core.double? xMm,
    $core.double? yMm,
    $core.double? zMm,
    TrajectoryScore? score,
    InvalidScoreReason? invalidScore,
    $core.double? radiusMm,
  @$core.Deprecated('This field is deprecated.')
    $core.String? category,
  @$core.Deprecated('This field is deprecated.')
    $fixnum.Int64? shootTimeRequestedMs,
  @$core.Deprecated('This field is deprecated.')
    $fixnum.Int64? shootTimeActualMs,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? markedForThinning,
  @$core.Deprecated('This field is deprecated.')
    $core.int? trackerId,
    DuplicateStatus? duplicateStatus,
    $core.int? duplicateTrajectoryId,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.int>? assignedLasers,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? outOfBand,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? intersectedWithNonshootable,
  @$core.Deprecated('This field is deprecated.')
    $core.Map<$core.String, $core.double>? detectionClasses,
  @$core.Deprecated('This field is deprecated.')
    $core.double? confidence,
    ThinningState? thinningState,
  @$core.Deprecated('This field is deprecated.')
    $core.double? globalPos,
    ScoreState? scoreState,
  @$core.Deprecated('This field is deprecated.')
    $core.double? doo,
  @$core.Deprecated('This field is deprecated.')
    $core.int? distancePerspectivesCount,
  @$core.Deprecated('This field is deprecated.')
    $core.int? sizeCategoryIndex,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? speculativeAllowed,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? protectedByTraj,
    Thresholds? thresholds,
  @$core.Deprecated('This field is deprecated.')
    Decisions? decisions,
  @$core.Deprecated('This field is deprecated.')
    $core.double? cropScore,
  @$core.Deprecated('This field is deprecated.')
    $core.double? weedScore,
  @$core.Deprecated('This field is deprecated.')
    $core.double? plantScore,
  @$core.Deprecated('This field is deprecated.')
    $core.int? numDetectionsUsedForDecision,
    Classification? classification,
  @$core.Deprecated('This field is deprecated.')
    $core.int? speculativeShootTimeActualMs,
  @$core.Deprecated('This field is deprecated.')
    SnapshotMetadata? snapshotMetadata,
    DetailedMetadata? detailedMetadata,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (killStatus != null) {
      _result.killStatus = killStatus;
    }
    if (isWeed != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.isWeed = isWeed;
    }
    if (xMm != null) {
      _result.xMm = xMm;
    }
    if (yMm != null) {
      _result.yMm = yMm;
    }
    if (zMm != null) {
      _result.zMm = zMm;
    }
    if (score != null) {
      _result.score = score;
    }
    if (invalidScore != null) {
      _result.invalidScore = invalidScore;
    }
    if (radiusMm != null) {
      _result.radiusMm = radiusMm;
    }
    if (category != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.category = category;
    }
    if (shootTimeRequestedMs != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.shootTimeRequestedMs = shootTimeRequestedMs;
    }
    if (shootTimeActualMs != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.shootTimeActualMs = shootTimeActualMs;
    }
    if (markedForThinning != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.markedForThinning = markedForThinning;
    }
    if (trackerId != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.trackerId = trackerId;
    }
    if (duplicateStatus != null) {
      _result.duplicateStatus = duplicateStatus;
    }
    if (duplicateTrajectoryId != null) {
      _result.duplicateTrajectoryId = duplicateTrajectoryId;
    }
    if (assignedLasers != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.assignedLasers.addAll(assignedLasers);
    }
    if (outOfBand != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.outOfBand = outOfBand;
    }
    if (intersectedWithNonshootable != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.intersectedWithNonshootable = intersectedWithNonshootable;
    }
    if (detectionClasses != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.detectionClasses.addAll(detectionClasses);
    }
    if (confidence != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.confidence = confidence;
    }
    if (thinningState != null) {
      _result.thinningState = thinningState;
    }
    if (globalPos != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.globalPos = globalPos;
    }
    if (scoreState != null) {
      _result.scoreState = scoreState;
    }
    if (doo != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.doo = doo;
    }
    if (distancePerspectivesCount != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.distancePerspectivesCount = distancePerspectivesCount;
    }
    if (sizeCategoryIndex != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.sizeCategoryIndex = sizeCategoryIndex;
    }
    if (speculativeAllowed != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.speculativeAllowed = speculativeAllowed;
    }
    if (protectedByTraj != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.protectedByTraj = protectedByTraj;
    }
    if (thresholds != null) {
      _result.thresholds = thresholds;
    }
    if (decisions != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.decisions = decisions;
    }
    if (cropScore != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.cropScore = cropScore;
    }
    if (weedScore != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.weedScore = weedScore;
    }
    if (plantScore != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.plantScore = plantScore;
    }
    if (numDetectionsUsedForDecision != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.numDetectionsUsedForDecision = numDetectionsUsedForDecision;
    }
    if (classification != null) {
      _result.classification = classification;
    }
    if (speculativeShootTimeActualMs != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.speculativeShootTimeActualMs = speculativeShootTimeActualMs;
    }
    if (snapshotMetadata != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.snapshotMetadata = snapshotMetadata;
    }
    if (detailedMetadata != null) {
      _result.detailedMetadata = detailedMetadata;
    }
    return _result;
  }
  factory TrajectorySnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectorySnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectorySnapshot clone() => TrajectorySnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectorySnapshot copyWith(void Function(TrajectorySnapshot) updates) => super.copyWith((message) => updates(message as TrajectorySnapshot)) as TrajectorySnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectorySnapshot create() => TrajectorySnapshot._();
  TrajectorySnapshot createEmptyInstance() => create();
  static $pb.PbList<TrajectorySnapshot> createRepeated() => $pb.PbList<TrajectorySnapshot>();
  @$core.pragma('dart2js:noInline')
  static TrajectorySnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectorySnapshot>(create);
  static TrajectorySnapshot? _defaultInstance;

  TrajectorySnapshot_ScoreDetails whichScoreDetails() => _TrajectorySnapshot_ScoreDetailsByTag[$_whichOneof(0)]!;
  void clearScoreDetails() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.int get id => $_getIZ(0);
  @$pb.TagNumber(1)
  set id($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  KillStatus get killStatus => $_getN(1);
  @$pb.TagNumber(2)
  set killStatus(KillStatus v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasKillStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearKillStatus() => clearField(2);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.bool get isWeed => $_getBF(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  set isWeed($core.bool v) { $_setBool(2, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.bool hasIsWeed() => $_has(2);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  void clearIsWeed() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get xMm => $_getN(3);
  @$pb.TagNumber(4)
  set xMm($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasXMm() => $_has(3);
  @$pb.TagNumber(4)
  void clearXMm() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get yMm => $_getN(4);
  @$pb.TagNumber(5)
  set yMm($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasYMm() => $_has(4);
  @$pb.TagNumber(5)
  void clearYMm() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get zMm => $_getN(5);
  @$pb.TagNumber(6)
  set zMm($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasZMm() => $_has(5);
  @$pb.TagNumber(6)
  void clearZMm() => clearField(6);

  @$pb.TagNumber(7)
  TrajectoryScore get score => $_getN(6);
  @$pb.TagNumber(7)
  set score(TrajectoryScore v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasScore() => $_has(6);
  @$pb.TagNumber(7)
  void clearScore() => clearField(7);
  @$pb.TagNumber(7)
  TrajectoryScore ensureScore() => $_ensure(6);

  @$pb.TagNumber(8)
  InvalidScoreReason get invalidScore => $_getN(7);
  @$pb.TagNumber(8)
  set invalidScore(InvalidScoreReason v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasInvalidScore() => $_has(7);
  @$pb.TagNumber(8)
  void clearInvalidScore() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get radiusMm => $_getN(8);
  @$pb.TagNumber(9)
  set radiusMm($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasRadiusMm() => $_has(8);
  @$pb.TagNumber(9)
  void clearRadiusMm() => clearField(9);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(10)
  $core.String get category => $_getSZ(9);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(10)
  set category($core.String v) { $_setString(9, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(10)
  $core.bool hasCategory() => $_has(9);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(10)
  void clearCategory() => clearField(10);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $fixnum.Int64 get shootTimeRequestedMs => $_getI64(10);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  set shootTimeRequestedMs($fixnum.Int64 v) { $_setInt64(10, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.bool hasShootTimeRequestedMs() => $_has(10);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  void clearShootTimeRequestedMs() => clearField(11);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(12)
  $fixnum.Int64 get shootTimeActualMs => $_getI64(11);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(12)
  set shootTimeActualMs($fixnum.Int64 v) { $_setInt64(11, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(12)
  $core.bool hasShootTimeActualMs() => $_has(11);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(12)
  void clearShootTimeActualMs() => clearField(12);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(13)
  $core.bool get markedForThinning => $_getBF(12);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(13)
  set markedForThinning($core.bool v) { $_setBool(12, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(13)
  $core.bool hasMarkedForThinning() => $_has(12);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(13)
  void clearMarkedForThinning() => clearField(13);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(14)
  $core.int get trackerId => $_getIZ(13);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(14)
  set trackerId($core.int v) { $_setUnsignedInt32(13, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(14)
  $core.bool hasTrackerId() => $_has(13);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(14)
  void clearTrackerId() => clearField(14);

  @$pb.TagNumber(15)
  DuplicateStatus get duplicateStatus => $_getN(14);
  @$pb.TagNumber(15)
  set duplicateStatus(DuplicateStatus v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasDuplicateStatus() => $_has(14);
  @$pb.TagNumber(15)
  void clearDuplicateStatus() => clearField(15);

  @$pb.TagNumber(16)
  $core.int get duplicateTrajectoryId => $_getIZ(15);
  @$pb.TagNumber(16)
  set duplicateTrajectoryId($core.int v) { $_setUnsignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasDuplicateTrajectoryId() => $_has(15);
  @$pb.TagNumber(16)
  void clearDuplicateTrajectoryId() => clearField(16);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(17)
  $core.List<$core.int> get assignedLasers => $_getList(16);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(18)
  $core.bool get outOfBand => $_getBF(17);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(18)
  set outOfBand($core.bool v) { $_setBool(17, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(18)
  $core.bool hasOutOfBand() => $_has(17);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(18)
  void clearOutOfBand() => clearField(18);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(19)
  $core.bool get intersectedWithNonshootable => $_getBF(18);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(19)
  set intersectedWithNonshootable($core.bool v) { $_setBool(18, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(19)
  $core.bool hasIntersectedWithNonshootable() => $_has(18);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(19)
  void clearIntersectedWithNonshootable() => clearField(19);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(20)
  $core.Map<$core.String, $core.double> get detectionClasses => $_getMap(19);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(21)
  $core.double get confidence => $_getN(20);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(21)
  set confidence($core.double v) { $_setDouble(20, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(21)
  $core.bool hasConfidence() => $_has(20);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(21)
  void clearConfidence() => clearField(21);

  @$pb.TagNumber(22)
  ThinningState get thinningState => $_getN(21);
  @$pb.TagNumber(22)
  set thinningState(ThinningState v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasThinningState() => $_has(21);
  @$pb.TagNumber(22)
  void clearThinningState() => clearField(22);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(23)
  $core.double get globalPos => $_getN(22);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(23)
  set globalPos($core.double v) { $_setDouble(22, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(23)
  $core.bool hasGlobalPos() => $_has(22);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(23)
  void clearGlobalPos() => clearField(23);

  @$pb.TagNumber(24)
  ScoreState get scoreState => $_getN(23);
  @$pb.TagNumber(24)
  set scoreState(ScoreState v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasScoreState() => $_has(23);
  @$pb.TagNumber(24)
  void clearScoreState() => clearField(24);
  @$pb.TagNumber(24)
  ScoreState ensureScoreState() => $_ensure(23);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(25)
  $core.double get doo => $_getN(24);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(25)
  set doo($core.double v) { $_setFloat(24, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(25)
  $core.bool hasDoo() => $_has(24);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(25)
  void clearDoo() => clearField(25);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(26)
  $core.int get distancePerspectivesCount => $_getIZ(25);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(26)
  set distancePerspectivesCount($core.int v) { $_setUnsignedInt32(25, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(26)
  $core.bool hasDistancePerspectivesCount() => $_has(25);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(26)
  void clearDistancePerspectivesCount() => clearField(26);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(27)
  $core.int get sizeCategoryIndex => $_getIZ(26);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(27)
  set sizeCategoryIndex($core.int v) { $_setSignedInt32(26, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(27)
  $core.bool hasSizeCategoryIndex() => $_has(26);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(27)
  void clearSizeCategoryIndex() => clearField(27);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(28)
  $core.bool get speculativeAllowed => $_getBF(27);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(28)
  set speculativeAllowed($core.bool v) { $_setBool(27, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(28)
  $core.bool hasSpeculativeAllowed() => $_has(27);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(28)
  void clearSpeculativeAllowed() => clearField(28);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(29)
  $core.bool get protectedByTraj => $_getBF(28);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(29)
  set protectedByTraj($core.bool v) { $_setBool(28, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(29)
  $core.bool hasProtectedByTraj() => $_has(28);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(29)
  void clearProtectedByTraj() => clearField(29);

  @$pb.TagNumber(30)
  Thresholds get thresholds => $_getN(29);
  @$pb.TagNumber(30)
  set thresholds(Thresholds v) { setField(30, v); }
  @$pb.TagNumber(30)
  $core.bool hasThresholds() => $_has(29);
  @$pb.TagNumber(30)
  void clearThresholds() => clearField(30);
  @$pb.TagNumber(30)
  Thresholds ensureThresholds() => $_ensure(29);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(31)
  Decisions get decisions => $_getN(30);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(31)
  set decisions(Decisions v) { setField(31, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(31)
  $core.bool hasDecisions() => $_has(30);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(31)
  void clearDecisions() => clearField(31);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(31)
  Decisions ensureDecisions() => $_ensure(30);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(32)
  $core.double get cropScore => $_getN(31);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(32)
  set cropScore($core.double v) { $_setDouble(31, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(32)
  $core.bool hasCropScore() => $_has(31);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(32)
  void clearCropScore() => clearField(32);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(33)
  $core.double get weedScore => $_getN(32);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(33)
  set weedScore($core.double v) { $_setDouble(32, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(33)
  $core.bool hasWeedScore() => $_has(32);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(33)
  void clearWeedScore() => clearField(33);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(34)
  $core.double get plantScore => $_getN(33);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(34)
  set plantScore($core.double v) { $_setDouble(33, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(34)
  $core.bool hasPlantScore() => $_has(33);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(34)
  void clearPlantScore() => clearField(34);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(35)
  $core.int get numDetectionsUsedForDecision => $_getIZ(34);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(35)
  set numDetectionsUsedForDecision($core.int v) { $_setUnsignedInt32(34, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(35)
  $core.bool hasNumDetectionsUsedForDecision() => $_has(34);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(35)
  void clearNumDetectionsUsedForDecision() => clearField(35);

  @$pb.TagNumber(36)
  Classification get classification => $_getN(35);
  @$pb.TagNumber(36)
  set classification(Classification v) { setField(36, v); }
  @$pb.TagNumber(36)
  $core.bool hasClassification() => $_has(35);
  @$pb.TagNumber(36)
  void clearClassification() => clearField(36);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(38)
  $core.int get speculativeShootTimeActualMs => $_getIZ(36);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(38)
  set speculativeShootTimeActualMs($core.int v) { $_setUnsignedInt32(36, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(38)
  $core.bool hasSpeculativeShootTimeActualMs() => $_has(36);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(38)
  void clearSpeculativeShootTimeActualMs() => clearField(38);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(39)
  SnapshotMetadata get snapshotMetadata => $_getN(37);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(39)
  set snapshotMetadata(SnapshotMetadata v) { setField(39, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(39)
  $core.bool hasSnapshotMetadata() => $_has(37);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(39)
  void clearSnapshotMetadata() => clearField(39);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(39)
  SnapshotMetadata ensureSnapshotMetadata() => $_ensure(37);

  @$pb.TagNumber(40)
  DetailedMetadata get detailedMetadata => $_getN(38);
  @$pb.TagNumber(40)
  set detailedMetadata(DetailedMetadata v) { setField(40, v); }
  @$pb.TagNumber(40)
  $core.bool hasDetailedMetadata() => $_has(38);
  @$pb.TagNumber(40)
  void clearDetailedMetadata() => clearField(40);
  @$pb.TagNumber(40)
  DetailedMetadata ensureDetailedMetadata() => $_ensure(38);
}

class SnapshotMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SnapshotMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pcamId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerXPx', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerYPx', $pb.PbFieldType.OF)
    ..pc<CategoryPrediction>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'embeddingSimilarityScores', $pb.PbFieldType.PM, subBuilder: CategoryPrediction.create)
    ..hasRequiredFields = false
  ;

  SnapshotMetadata._() : super();
  factory SnapshotMetadata({
    $core.String? pcamId,
    $fixnum.Int64? timestampMs,
    $core.double? centerXPx,
    $core.double? centerYPx,
    $core.Iterable<CategoryPrediction>? embeddingSimilarityScores,
  }) {
    final _result = create();
    if (pcamId != null) {
      _result.pcamId = pcamId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (centerXPx != null) {
      _result.centerXPx = centerXPx;
    }
    if (centerYPx != null) {
      _result.centerYPx = centerYPx;
    }
    if (embeddingSimilarityScores != null) {
      _result.embeddingSimilarityScores.addAll(embeddingSimilarityScores);
    }
    return _result;
  }
  factory SnapshotMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SnapshotMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SnapshotMetadata clone() => SnapshotMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SnapshotMetadata copyWith(void Function(SnapshotMetadata) updates) => super.copyWith((message) => updates(message as SnapshotMetadata)) as SnapshotMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SnapshotMetadata create() => SnapshotMetadata._();
  SnapshotMetadata createEmptyInstance() => create();
  static $pb.PbList<SnapshotMetadata> createRepeated() => $pb.PbList<SnapshotMetadata>();
  @$core.pragma('dart2js:noInline')
  static SnapshotMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SnapshotMetadata>(create);
  static SnapshotMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pcamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set pcamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPcamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPcamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get centerXPx => $_getN(2);
  @$pb.TagNumber(3)
  set centerXPx($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCenterXPx() => $_has(2);
  @$pb.TagNumber(3)
  void clearCenterXPx() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get centerYPx => $_getN(3);
  @$pb.TagNumber(4)
  set centerYPx($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCenterYPx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCenterYPx() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<CategoryPrediction> get embeddingSimilarityScores => $_getList(4);
}

class BandDefinition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BandDefinition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'offsetMm', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'widthMm', $pb.PbFieldType.OF)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  BandDefinition._() : super();
  factory BandDefinition({
    $core.double? offsetMm,
    $core.double? widthMm,
    $core.int? id,
  }) {
    final _result = create();
    if (offsetMm != null) {
      _result.offsetMm = offsetMm;
    }
    if (widthMm != null) {
      _result.widthMm = widthMm;
    }
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory BandDefinition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BandDefinition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BandDefinition clone() => BandDefinition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BandDefinition copyWith(void Function(BandDefinition) updates) => super.copyWith((message) => updates(message as BandDefinition)) as BandDefinition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BandDefinition create() => BandDefinition._();
  BandDefinition createEmptyInstance() => create();
  static $pb.PbList<BandDefinition> createRepeated() => $pb.PbList<BandDefinition>();
  @$core.pragma('dart2js:noInline')
  static BandDefinition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BandDefinition>(create);
  static BandDefinition? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get offsetMm => $_getN(0);
  @$pb.TagNumber(1)
  set offsetMm($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasOffsetMm() => $_has(0);
  @$pb.TagNumber(1)
  void clearOffsetMm() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get widthMm => $_getN(1);
  @$pb.TagNumber(2)
  set widthMm($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWidthMm() => $_has(1);
  @$pb.TagNumber(2)
  void clearWidthMm() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get id => $_getIZ(2);
  @$pb.TagNumber(3)
  set id($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasId() => $_has(2);
  @$pb.TagNumber(3)
  void clearId() => clearField(3);
}

class CLDAlgorithmSnapshot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CLDAlgorithmSnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..p<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'graphPointsX', $pb.PbFieldType.KF)
    ..p<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'graphPointsY', $pb.PbFieldType.KF)
    ..p<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minimasX', $pb.PbFieldType.KF)
    ..p<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minimasY', $pb.PbFieldType.KF)
    ..p<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lines', $pb.PbFieldType.KF)
    ..hasRequiredFields = false
  ;

  CLDAlgorithmSnapshot._() : super();
  factory CLDAlgorithmSnapshot({
    $fixnum.Int64? timestampMs,
    $core.Iterable<$core.double>? graphPointsX,
    $core.Iterable<$core.double>? graphPointsY,
    $core.Iterable<$core.double>? minimasX,
    $core.Iterable<$core.double>? minimasY,
    $core.Iterable<$core.double>? lines,
  }) {
    final _result = create();
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (graphPointsX != null) {
      _result.graphPointsX.addAll(graphPointsX);
    }
    if (graphPointsY != null) {
      _result.graphPointsY.addAll(graphPointsY);
    }
    if (minimasX != null) {
      _result.minimasX.addAll(minimasX);
    }
    if (minimasY != null) {
      _result.minimasY.addAll(minimasY);
    }
    if (lines != null) {
      _result.lines.addAll(lines);
    }
    return _result;
  }
  factory CLDAlgorithmSnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CLDAlgorithmSnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CLDAlgorithmSnapshot clone() => CLDAlgorithmSnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CLDAlgorithmSnapshot copyWith(void Function(CLDAlgorithmSnapshot) updates) => super.copyWith((message) => updates(message as CLDAlgorithmSnapshot)) as CLDAlgorithmSnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CLDAlgorithmSnapshot create() => CLDAlgorithmSnapshot._();
  CLDAlgorithmSnapshot createEmptyInstance() => create();
  static $pb.PbList<CLDAlgorithmSnapshot> createRepeated() => $pb.PbList<CLDAlgorithmSnapshot>();
  @$core.pragma('dart2js:noInline')
  static CLDAlgorithmSnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CLDAlgorithmSnapshot>(create);
  static CLDAlgorithmSnapshot? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get timestampMs => $_getI64(0);
  @$pb.TagNumber(1)
  set timestampMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestampMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestampMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.double> get graphPointsX => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$core.double> get graphPointsY => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<$core.double> get minimasX => $_getList(3);

  @$pb.TagNumber(5)
  $core.List<$core.double> get minimasY => $_getList(4);

  @$pb.TagNumber(6)
  $core.List<$core.double> get lines => $_getList(5);
}

class DiagnosticsSnapshot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DiagnosticsSnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..pc<TrajectorySnapshot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectories', $pb.PbFieldType.PM, subBuilder: TrajectorySnapshot.create)
    ..pc<BandDefinition>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bands', $pb.PbFieldType.PM, subBuilder: BandDefinition.create)
    ..pc<KillBox>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'killBoxes', $pb.PbFieldType.PM, subBuilder: KillBox.create)
    ..aOM<CLDAlgorithmSnapshot>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingAlgorithmSnapshot', subBuilder: CLDAlgorithmSnapshot.create)
    ..hasRequiredFields = false
  ;

  DiagnosticsSnapshot._() : super();
  factory DiagnosticsSnapshot({
    $fixnum.Int64? timestampMs,
    $core.Iterable<TrajectorySnapshot>? trajectories,
    $core.Iterable<BandDefinition>? bands,
    $core.Iterable<KillBox>? killBoxes,
    CLDAlgorithmSnapshot? bandingAlgorithmSnapshot,
  }) {
    final _result = create();
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (trajectories != null) {
      _result.trajectories.addAll(trajectories);
    }
    if (bands != null) {
      _result.bands.addAll(bands);
    }
    if (killBoxes != null) {
      _result.killBoxes.addAll(killBoxes);
    }
    if (bandingAlgorithmSnapshot != null) {
      _result.bandingAlgorithmSnapshot = bandingAlgorithmSnapshot;
    }
    return _result;
  }
  factory DiagnosticsSnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DiagnosticsSnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DiagnosticsSnapshot clone() => DiagnosticsSnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DiagnosticsSnapshot copyWith(void Function(DiagnosticsSnapshot) updates) => super.copyWith((message) => updates(message as DiagnosticsSnapshot)) as DiagnosticsSnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DiagnosticsSnapshot create() => DiagnosticsSnapshot._();
  DiagnosticsSnapshot createEmptyInstance() => create();
  static $pb.PbList<DiagnosticsSnapshot> createRepeated() => $pb.PbList<DiagnosticsSnapshot>();
  @$core.pragma('dart2js:noInline')
  static DiagnosticsSnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DiagnosticsSnapshot>(create);
  static DiagnosticsSnapshot? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get timestampMs => $_getI64(0);
  @$pb.TagNumber(1)
  set timestampMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestampMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestampMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<TrajectorySnapshot> get trajectories => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<BandDefinition> get bands => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<KillBox> get killBoxes => $_getList(3);

  @$pb.TagNumber(5)
  CLDAlgorithmSnapshot get bandingAlgorithmSnapshot => $_getN(4);
  @$pb.TagNumber(5)
  set bandingAlgorithmSnapshot(CLDAlgorithmSnapshot v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasBandingAlgorithmSnapshot() => $_has(4);
  @$pb.TagNumber(5)
  void clearBandingAlgorithmSnapshot() => clearField(5);
  @$pb.TagNumber(5)
  CLDAlgorithmSnapshot ensureBandingAlgorithmSnapshot() => $_ensure(4);
}

class RecordDiagnosticsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RecordDiagnosticsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ttlSec', $pb.PbFieldType.OU3)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropImagesPerSec', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedImagesPerSec', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  RecordDiagnosticsRequest._() : super();
  factory RecordDiagnosticsRequest({
    $core.int? ttlSec,
    $core.double? cropImagesPerSec,
    $core.double? weedImagesPerSec,
  }) {
    final _result = create();
    if (ttlSec != null) {
      _result.ttlSec = ttlSec;
    }
    if (cropImagesPerSec != null) {
      _result.cropImagesPerSec = cropImagesPerSec;
    }
    if (weedImagesPerSec != null) {
      _result.weedImagesPerSec = weedImagesPerSec;
    }
    return _result;
  }
  factory RecordDiagnosticsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RecordDiagnosticsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RecordDiagnosticsRequest clone() => RecordDiagnosticsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RecordDiagnosticsRequest copyWith(void Function(RecordDiagnosticsRequest) updates) => super.copyWith((message) => updates(message as RecordDiagnosticsRequest)) as RecordDiagnosticsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RecordDiagnosticsRequest create() => RecordDiagnosticsRequest._();
  RecordDiagnosticsRequest createEmptyInstance() => create();
  static $pb.PbList<RecordDiagnosticsRequest> createRepeated() => $pb.PbList<RecordDiagnosticsRequest>();
  @$core.pragma('dart2js:noInline')
  static RecordDiagnosticsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RecordDiagnosticsRequest>(create);
  static RecordDiagnosticsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get ttlSec => $_getIZ(0);
  @$pb.TagNumber(1)
  set ttlSec($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTtlSec() => $_has(0);
  @$pb.TagNumber(1)
  void clearTtlSec() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get cropImagesPerSec => $_getN(1);
  @$pb.TagNumber(2)
  set cropImagesPerSec($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropImagesPerSec() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropImagesPerSec() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get weedImagesPerSec => $_getN(2);
  @$pb.TagNumber(3)
  set weedImagesPerSec($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasWeedImagesPerSec() => $_has(2);
  @$pb.TagNumber(3)
  void clearWeedImagesPerSec() => clearField(3);
}

class GetRecordingStatusResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRecordingStatusResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..e<RecordingStatus>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: RecordingStatus.NOT_RECORDING, valueOf: RecordingStatus.valueOf, enumValues: RecordingStatus.values)
    ..hasRequiredFields = false
  ;

  GetRecordingStatusResponse._() : super();
  factory GetRecordingStatusResponse({
    RecordingStatus? status,
  }) {
    final _result = create();
    if (status != null) {
      _result.status = status;
    }
    return _result;
  }
  factory GetRecordingStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRecordingStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRecordingStatusResponse clone() => GetRecordingStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRecordingStatusResponse copyWith(void Function(GetRecordingStatusResponse) updates) => super.copyWith((message) => updates(message as GetRecordingStatusResponse)) as GetRecordingStatusResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRecordingStatusResponse create() => GetRecordingStatusResponse._();
  GetRecordingStatusResponse createEmptyInstance() => create();
  static $pb.PbList<GetRecordingStatusResponse> createRepeated() => $pb.PbList<GetRecordingStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static GetRecordingStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRecordingStatusResponse>(create);
  static GetRecordingStatusResponse? _defaultInstance;

  @$pb.TagNumber(1)
  RecordingStatus get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(RecordingStatus v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);
}

class StartSavingCropLineDetectionReplayRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartSavingCropLineDetectionReplayRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'filename')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ttlMs', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  StartSavingCropLineDetectionReplayRequest._() : super();
  factory StartSavingCropLineDetectionReplayRequest({
    $core.String? filename,
    $core.int? ttlMs,
  }) {
    final _result = create();
    if (filename != null) {
      _result.filename = filename;
    }
    if (ttlMs != null) {
      _result.ttlMs = ttlMs;
    }
    return _result;
  }
  factory StartSavingCropLineDetectionReplayRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartSavingCropLineDetectionReplayRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartSavingCropLineDetectionReplayRequest clone() => StartSavingCropLineDetectionReplayRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartSavingCropLineDetectionReplayRequest copyWith(void Function(StartSavingCropLineDetectionReplayRequest) updates) => super.copyWith((message) => updates(message as StartSavingCropLineDetectionReplayRequest)) as StartSavingCropLineDetectionReplayRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartSavingCropLineDetectionReplayRequest create() => StartSavingCropLineDetectionReplayRequest._();
  StartSavingCropLineDetectionReplayRequest createEmptyInstance() => create();
  static $pb.PbList<StartSavingCropLineDetectionReplayRequest> createRepeated() => $pb.PbList<StartSavingCropLineDetectionReplayRequest>();
  @$core.pragma('dart2js:noInline')
  static StartSavingCropLineDetectionReplayRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartSavingCropLineDetectionReplayRequest>(create);
  static StartSavingCropLineDetectionReplayRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get filename => $_getSZ(0);
  @$pb.TagNumber(1)
  set filename($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFilename() => $_has(0);
  @$pb.TagNumber(1)
  void clearFilename() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get ttlMs => $_getIZ(1);
  @$pb.TagNumber(2)
  set ttlMs($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTtlMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTtlMs() => clearField(2);
}

class RecordAimbotInputRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RecordAimbotInputRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ttlMs', $pb.PbFieldType.OU3)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rotaryTicks')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweed')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laneHeights')
    ..hasRequiredFields = false
  ;

  RecordAimbotInputRequest._() : super();
  factory RecordAimbotInputRequest({
    $core.String? name,
    $core.int? ttlMs,
    $core.bool? rotaryTicks,
    $core.bool? deepweed,
    $core.bool? laneHeights,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (ttlMs != null) {
      _result.ttlMs = ttlMs;
    }
    if (rotaryTicks != null) {
      _result.rotaryTicks = rotaryTicks;
    }
    if (deepweed != null) {
      _result.deepweed = deepweed;
    }
    if (laneHeights != null) {
      _result.laneHeights = laneHeights;
    }
    return _result;
  }
  factory RecordAimbotInputRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RecordAimbotInputRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RecordAimbotInputRequest clone() => RecordAimbotInputRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RecordAimbotInputRequest copyWith(void Function(RecordAimbotInputRequest) updates) => super.copyWith((message) => updates(message as RecordAimbotInputRequest)) as RecordAimbotInputRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RecordAimbotInputRequest create() => RecordAimbotInputRequest._();
  RecordAimbotInputRequest createEmptyInstance() => create();
  static $pb.PbList<RecordAimbotInputRequest> createRepeated() => $pb.PbList<RecordAimbotInputRequest>();
  @$core.pragma('dart2js:noInline')
  static RecordAimbotInputRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RecordAimbotInputRequest>(create);
  static RecordAimbotInputRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get ttlMs => $_getIZ(1);
  @$pb.TagNumber(2)
  set ttlMs($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTtlMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTtlMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get rotaryTicks => $_getBF(2);
  @$pb.TagNumber(3)
  set rotaryTicks($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRotaryTicks() => $_has(2);
  @$pb.TagNumber(3)
  void clearRotaryTicks() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get deepweed => $_getBF(3);
  @$pb.TagNumber(4)
  set deepweed($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeepweed() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeepweed() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get laneHeights => $_getBF(4);
  @$pb.TagNumber(5)
  set laneHeights($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLaneHeights() => $_has(4);
  @$pb.TagNumber(5)
  void clearLaneHeights() => clearField(5);
}

class ConclusionCount extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ConclusionCount', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..e<ConclusionType>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: ConclusionType.NOT_WEEDING, valueOf: ConclusionType.valueOf, enumValues: ConclusionType.values)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'count', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  ConclusionCount._() : super();
  factory ConclusionCount({
    ConclusionType? type,
    $core.int? count,
  }) {
    final _result = create();
    if (type != null) {
      _result.type = type;
    }
    if (count != null) {
      _result.count = count;
    }
    return _result;
  }
  factory ConclusionCount.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ConclusionCount.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ConclusionCount clone() => ConclusionCount()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ConclusionCount copyWith(void Function(ConclusionCount) updates) => super.copyWith((message) => updates(message as ConclusionCount)) as ConclusionCount; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ConclusionCount create() => ConclusionCount._();
  ConclusionCount createEmptyInstance() => create();
  static $pb.PbList<ConclusionCount> createRepeated() => $pb.PbList<ConclusionCount>();
  @$core.pragma('dart2js:noInline')
  static ConclusionCount getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ConclusionCount>(create);
  static ConclusionCount? _defaultInstance;

  @$pb.TagNumber(1)
  ConclusionType get type => $_getN(0);
  @$pb.TagNumber(1)
  set type(ConclusionType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasType() => $_has(0);
  @$pb.TagNumber(1)
  void clearType() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get count => $_getIZ(1);
  @$pb.TagNumber(2)
  set count($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCount() => $_has(1);
  @$pb.TagNumber(2)
  void clearCount() => clearField(2);
}

class ConclusionCounter extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ConclusionCounter', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<ConclusionCount>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'counts', $pb.PbFieldType.PM, subBuilder: ConclusionCount.create)
    ..hasRequiredFields = false
  ;

  ConclusionCounter._() : super();
  factory ConclusionCounter({
    $core.Iterable<ConclusionCount>? counts,
  }) {
    final _result = create();
    if (counts != null) {
      _result.counts.addAll(counts);
    }
    return _result;
  }
  factory ConclusionCounter.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ConclusionCounter.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ConclusionCounter clone() => ConclusionCounter()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ConclusionCounter copyWith(void Function(ConclusionCounter) updates) => super.copyWith((message) => updates(message as ConclusionCounter)) as ConclusionCounter; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ConclusionCounter create() => ConclusionCounter._();
  ConclusionCounter createEmptyInstance() => create();
  static $pb.PbList<ConclusionCounter> createRepeated() => $pb.PbList<ConclusionCounter>();
  @$core.pragma('dart2js:noInline')
  static ConclusionCounter getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ConclusionCounter>(create);
  static ConclusionCounter? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ConclusionCount> get counts => $_getList(0);
}

class BandDefinitions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BandDefinitions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..pc<BandDefinition>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bands', $pb.PbFieldType.PM, subBuilder: BandDefinition.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingEnabled')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowHasBandsDefined')
    ..hasRequiredFields = false
  ;

  BandDefinitions._() : super();
  factory BandDefinitions({
    $core.Iterable<BandDefinition>? bands,
    $core.bool? bandingEnabled,
    $core.bool? rowHasBandsDefined,
  }) {
    final _result = create();
    if (bands != null) {
      _result.bands.addAll(bands);
    }
    if (bandingEnabled != null) {
      _result.bandingEnabled = bandingEnabled;
    }
    if (rowHasBandsDefined != null) {
      _result.rowHasBandsDefined = rowHasBandsDefined;
    }
    return _result;
  }
  factory BandDefinitions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BandDefinitions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BandDefinitions clone() => BandDefinitions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BandDefinitions copyWith(void Function(BandDefinitions) updates) => super.copyWith((message) => updates(message as BandDefinitions)) as BandDefinitions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BandDefinitions create() => BandDefinitions._();
  BandDefinitions createEmptyInstance() => create();
  static $pb.PbList<BandDefinitions> createRepeated() => $pb.PbList<BandDefinitions>();
  @$core.pragma('dart2js:noInline')
  static BandDefinitions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BandDefinitions>(create);
  static BandDefinitions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<BandDefinition> get bands => $_getList(0);

  @$pb.TagNumber(2)
  $core.bool get bandingEnabled => $_getBF(1);
  @$pb.TagNumber(2)
  set bandingEnabled($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBandingEnabled() => $_has(1);
  @$pb.TagNumber(2)
  void clearBandingEnabled() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get rowHasBandsDefined => $_getBF(2);
  @$pb.TagNumber(3)
  set rowHasBandsDefined($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRowHasBandsDefined() => $_has(2);
  @$pb.TagNumber(3)
  void clearRowHasBandsDefined() => clearField(3);
}

class PlantCaptchaStatusResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaStatusResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..e<PlantCaptchaStatus>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: PlantCaptchaStatus.NOT_STARTED, valueOf: PlantCaptchaStatus.valueOf, enumValues: PlantCaptchaStatus.values)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalImages', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesTaken', $pb.PbFieldType.O3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadataTaken', $pb.PbFieldType.O3)
    ..m<$core.String, $core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exemplarCounts', entryClassName: 'PlantCaptchaStatusResponse.ExemplarCountsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.O3, packageName: const $pb.PackageName('weed_tracking'))
    ..hasRequiredFields = false
  ;

  PlantCaptchaStatusResponse._() : super();
  factory PlantCaptchaStatusResponse({
    PlantCaptchaStatus? status,
    $core.int? totalImages,
    $core.int? imagesTaken,
    $core.int? metadataTaken,
    $core.Map<$core.String, $core.int>? exemplarCounts,
  }) {
    final _result = create();
    if (status != null) {
      _result.status = status;
    }
    if (totalImages != null) {
      _result.totalImages = totalImages;
    }
    if (imagesTaken != null) {
      _result.imagesTaken = imagesTaken;
    }
    if (metadataTaken != null) {
      _result.metadataTaken = metadataTaken;
    }
    if (exemplarCounts != null) {
      _result.exemplarCounts.addAll(exemplarCounts);
    }
    return _result;
  }
  factory PlantCaptchaStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaStatusResponse clone() => PlantCaptchaStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaStatusResponse copyWith(void Function(PlantCaptchaStatusResponse) updates) => super.copyWith((message) => updates(message as PlantCaptchaStatusResponse)) as PlantCaptchaStatusResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaStatusResponse create() => PlantCaptchaStatusResponse._();
  PlantCaptchaStatusResponse createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaStatusResponse> createRepeated() => $pb.PbList<PlantCaptchaStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaStatusResponse>(create);
  static PlantCaptchaStatusResponse? _defaultInstance;

  @$pb.TagNumber(1)
  PlantCaptchaStatus get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(PlantCaptchaStatus v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get totalImages => $_getIZ(1);
  @$pb.TagNumber(2)
  set totalImages($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTotalImages() => $_has(1);
  @$pb.TagNumber(2)
  void clearTotalImages() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get imagesTaken => $_getIZ(2);
  @$pb.TagNumber(3)
  set imagesTaken($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasImagesTaken() => $_has(2);
  @$pb.TagNumber(3)
  void clearImagesTaken() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get metadataTaken => $_getIZ(3);
  @$pb.TagNumber(4)
  set metadataTaken($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasMetadataTaken() => $_has(3);
  @$pb.TagNumber(4)
  void clearMetadataTaken() => clearField(4);

  @$pb.TagNumber(5)
  $core.Map<$core.String, $core.int> get exemplarCounts => $_getMap(4);
}

class Embedding extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Embedding', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..p<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'elements', $pb.PbFieldType.KF)
    ..hasRequiredFields = false
  ;

  Embedding._() : super();
  factory Embedding({
    $core.Iterable<$core.double>? elements,
  }) {
    final _result = create();
    if (elements != null) {
      _result.elements.addAll(elements);
    }
    return _result;
  }
  factory Embedding.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Embedding.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Embedding clone() => Embedding()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Embedding copyWith(void Function(Embedding) updates) => super.copyWith((message) => updates(message as Embedding)) as Embedding; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Embedding create() => Embedding._();
  Embedding createEmptyInstance() => create();
  static $pb.PbList<Embedding> createRepeated() => $pb.PbList<Embedding>();
  @$core.pragma('dart2js:noInline')
  static Embedding getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Embedding>(create);
  static Embedding? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.double> get elements => $_getList(0);
}

class WeedClasses extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedClasses', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..m<$core.String, $core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'classes', entryClassName: 'WeedClasses.ClassesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('weed_tracking'))
    ..hasRequiredFields = false
  ;

  WeedClasses._() : super();
  factory WeedClasses({
    $core.Map<$core.String, $core.double>? classes,
  }) {
    final _result = create();
    if (classes != null) {
      _result.classes.addAll(classes);
    }
    return _result;
  }
  factory WeedClasses.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedClasses.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedClasses clone() => WeedClasses()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedClasses copyWith(void Function(WeedClasses) updates) => super.copyWith((message) => updates(message as WeedClasses)) as WeedClasses; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedClasses create() => WeedClasses._();
  WeedClasses createEmptyInstance() => create();
  static $pb.PbList<WeedClasses> createRepeated() => $pb.PbList<WeedClasses>();
  @$core.pragma('dart2js:noInline')
  static WeedClasses getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedClasses>(create);
  static WeedClasses? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, $core.double> get classes => $_getMap(0);
}

class PlantCaptchaItemMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaItemMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidence', $pb.PbFieldType.OF)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xPx', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yPx', $pb.PbFieldType.O3)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xMm', $pb.PbFieldType.OD)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yMm', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zMm', $pb.PbFieldType.OD)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizeMm', $pb.PbFieldType.OF)
    ..m<$core.String, $core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categories', entryClassName: 'PlantCaptchaItemMetadata.CategoriesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('weed_tracking'))
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'doo', $pb.PbFieldType.OF)
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeed')
    ..aOB(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intersectedWithNonshootable')
    ..p<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidenceHistory', $pb.PbFieldType.KF)
    ..aOB(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isInBand')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..a<$core.double>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizePx', $pb.PbFieldType.OF)
    ..a<$core.int>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shootTimeMs', $pb.PbFieldType.OU3)
    ..p<$core.double>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedConfidenceHistory', $pb.PbFieldType.KF)
    ..p<$core.double>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropConfidenceHistory', $pb.PbFieldType.KF)
    ..a<$core.int>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizeCategoryIndex', $pb.PbFieldType.O3)
    ..m<$core.String, $core.double>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedCategories', entryClassName: 'PlantCaptchaItemMetadata.WeedCategoriesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('weed_tracking'))
    ..pc<Embedding>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'embeddingHistory', $pb.PbFieldType.PM, subBuilder: Embedding.create)
    ..e<PlantCaptchaUserPrediction>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'initialLabel', $pb.PbFieldType.OE, defaultOrMaker: PlantCaptchaUserPrediction.WEED, valueOf: PlantCaptchaUserPrediction.valueOf, enumValues: PlantCaptchaUserPrediction.values)
    ..p<$core.double>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantConfidenceHistory', $pb.PbFieldType.KF)
    ..pc<WeedClasses>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedClassesHistory', $pb.PbFieldType.PM, subBuilder: WeedClasses.create)
    ..p<$core.double>(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sizeMmHistory', $pb.PbFieldType.KF)
    ..aOM<Decisions>(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'decisions', subBuilder: Decisions.create)
    ..a<$core.int>(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numDetectionsUsedForDecision', $pb.PbFieldType.OU3)
    ..m<$core.String, $core.double>(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'embeddingDistances', entryClassName: 'PlantCaptchaItemMetadata.EmbeddingDistancesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('weed_tracking'))
    ..hasRequiredFields = false
  ;

  PlantCaptchaItemMetadata._() : super();
  factory PlantCaptchaItemMetadata({
    $core.double? confidence,
    $core.int? xPx,
    $core.int? yPx,
    $core.double? xMm,
    $core.double? yMm,
    $core.double? zMm,
    $core.double? sizeMm,
    $core.Map<$core.String, $core.double>? categories,
    $core.double? doo,
    $core.bool? isWeed,
    $core.bool? intersectedWithNonshootable,
    $core.Iterable<$core.double>? confidenceHistory,
    $core.bool? isInBand,
    $core.String? id,
    $core.double? sizePx,
    $core.int? shootTimeMs,
    $core.Iterable<$core.double>? weedConfidenceHistory,
    $core.Iterable<$core.double>? cropConfidenceHistory,
    $core.int? sizeCategoryIndex,
    $core.Map<$core.String, $core.double>? weedCategories,
    $core.Iterable<Embedding>? embeddingHistory,
    PlantCaptchaUserPrediction? initialLabel,
    $core.Iterable<$core.double>? plantConfidenceHistory,
    $core.Iterable<WeedClasses>? weedClassesHistory,
    $core.Iterable<$core.double>? sizeMmHistory,
    Decisions? decisions,
    $core.int? numDetectionsUsedForDecision,
    $core.Map<$core.String, $core.double>? embeddingDistances,
  }) {
    final _result = create();
    if (confidence != null) {
      _result.confidence = confidence;
    }
    if (xPx != null) {
      _result.xPx = xPx;
    }
    if (yPx != null) {
      _result.yPx = yPx;
    }
    if (xMm != null) {
      _result.xMm = xMm;
    }
    if (yMm != null) {
      _result.yMm = yMm;
    }
    if (zMm != null) {
      _result.zMm = zMm;
    }
    if (sizeMm != null) {
      _result.sizeMm = sizeMm;
    }
    if (categories != null) {
      _result.categories.addAll(categories);
    }
    if (doo != null) {
      _result.doo = doo;
    }
    if (isWeed != null) {
      _result.isWeed = isWeed;
    }
    if (intersectedWithNonshootable != null) {
      _result.intersectedWithNonshootable = intersectedWithNonshootable;
    }
    if (confidenceHistory != null) {
      _result.confidenceHistory.addAll(confidenceHistory);
    }
    if (isInBand != null) {
      _result.isInBand = isInBand;
    }
    if (id != null) {
      _result.id = id;
    }
    if (sizePx != null) {
      _result.sizePx = sizePx;
    }
    if (shootTimeMs != null) {
      _result.shootTimeMs = shootTimeMs;
    }
    if (weedConfidenceHistory != null) {
      _result.weedConfidenceHistory.addAll(weedConfidenceHistory);
    }
    if (cropConfidenceHistory != null) {
      _result.cropConfidenceHistory.addAll(cropConfidenceHistory);
    }
    if (sizeCategoryIndex != null) {
      _result.sizeCategoryIndex = sizeCategoryIndex;
    }
    if (weedCategories != null) {
      _result.weedCategories.addAll(weedCategories);
    }
    if (embeddingHistory != null) {
      _result.embeddingHistory.addAll(embeddingHistory);
    }
    if (initialLabel != null) {
      _result.initialLabel = initialLabel;
    }
    if (plantConfidenceHistory != null) {
      _result.plantConfidenceHistory.addAll(plantConfidenceHistory);
    }
    if (weedClassesHistory != null) {
      _result.weedClassesHistory.addAll(weedClassesHistory);
    }
    if (sizeMmHistory != null) {
      _result.sizeMmHistory.addAll(sizeMmHistory);
    }
    if (decisions != null) {
      _result.decisions = decisions;
    }
    if (numDetectionsUsedForDecision != null) {
      _result.numDetectionsUsedForDecision = numDetectionsUsedForDecision;
    }
    if (embeddingDistances != null) {
      _result.embeddingDistances.addAll(embeddingDistances);
    }
    return _result;
  }
  factory PlantCaptchaItemMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaItemMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaItemMetadata clone() => PlantCaptchaItemMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaItemMetadata copyWith(void Function(PlantCaptchaItemMetadata) updates) => super.copyWith((message) => updates(message as PlantCaptchaItemMetadata)) as PlantCaptchaItemMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItemMetadata create() => PlantCaptchaItemMetadata._();
  PlantCaptchaItemMetadata createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaItemMetadata> createRepeated() => $pb.PbList<PlantCaptchaItemMetadata>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItemMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaItemMetadata>(create);
  static PlantCaptchaItemMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get confidence => $_getN(0);
  @$pb.TagNumber(1)
  set confidence($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfidence() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfidence() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get xPx => $_getIZ(1);
  @$pb.TagNumber(2)
  set xPx($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasXPx() => $_has(1);
  @$pb.TagNumber(2)
  void clearXPx() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get yPx => $_getIZ(2);
  @$pb.TagNumber(3)
  set yPx($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYPx() => $_has(2);
  @$pb.TagNumber(3)
  void clearYPx() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get xMm => $_getN(3);
  @$pb.TagNumber(4)
  set xMm($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasXMm() => $_has(3);
  @$pb.TagNumber(4)
  void clearXMm() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get yMm => $_getN(4);
  @$pb.TagNumber(5)
  set yMm($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasYMm() => $_has(4);
  @$pb.TagNumber(5)
  void clearYMm() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get zMm => $_getN(5);
  @$pb.TagNumber(6)
  set zMm($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasZMm() => $_has(5);
  @$pb.TagNumber(6)
  void clearZMm() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get sizeMm => $_getN(6);
  @$pb.TagNumber(7)
  set sizeMm($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSizeMm() => $_has(6);
  @$pb.TagNumber(7)
  void clearSizeMm() => clearField(7);

  @$pb.TagNumber(8)
  $core.Map<$core.String, $core.double> get categories => $_getMap(7);

  @$pb.TagNumber(9)
  $core.double get doo => $_getN(8);
  @$pb.TagNumber(9)
  set doo($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDoo() => $_has(8);
  @$pb.TagNumber(9)
  void clearDoo() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get isWeed => $_getBF(9);
  @$pb.TagNumber(10)
  set isWeed($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasIsWeed() => $_has(9);
  @$pb.TagNumber(10)
  void clearIsWeed() => clearField(10);

  @$pb.TagNumber(11)
  $core.bool get intersectedWithNonshootable => $_getBF(10);
  @$pb.TagNumber(11)
  set intersectedWithNonshootable($core.bool v) { $_setBool(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasIntersectedWithNonshootable() => $_has(10);
  @$pb.TagNumber(11)
  void clearIntersectedWithNonshootable() => clearField(11);

  @$pb.TagNumber(12)
  $core.List<$core.double> get confidenceHistory => $_getList(11);

  @$pb.TagNumber(13)
  $core.bool get isInBand => $_getBF(12);
  @$pb.TagNumber(13)
  set isInBand($core.bool v) { $_setBool(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasIsInBand() => $_has(12);
  @$pb.TagNumber(13)
  void clearIsInBand() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get id => $_getSZ(13);
  @$pb.TagNumber(14)
  set id($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasId() => $_has(13);
  @$pb.TagNumber(14)
  void clearId() => clearField(14);

  @$pb.TagNumber(15)
  $core.double get sizePx => $_getN(14);
  @$pb.TagNumber(15)
  set sizePx($core.double v) { $_setFloat(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasSizePx() => $_has(14);
  @$pb.TagNumber(15)
  void clearSizePx() => clearField(15);

  @$pb.TagNumber(16)
  $core.int get shootTimeMs => $_getIZ(15);
  @$pb.TagNumber(16)
  set shootTimeMs($core.int v) { $_setUnsignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasShootTimeMs() => $_has(15);
  @$pb.TagNumber(16)
  void clearShootTimeMs() => clearField(16);

  @$pb.TagNumber(17)
  $core.List<$core.double> get weedConfidenceHistory => $_getList(16);

  @$pb.TagNumber(18)
  $core.List<$core.double> get cropConfidenceHistory => $_getList(17);

  @$pb.TagNumber(19)
  $core.int get sizeCategoryIndex => $_getIZ(18);
  @$pb.TagNumber(19)
  set sizeCategoryIndex($core.int v) { $_setSignedInt32(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasSizeCategoryIndex() => $_has(18);
  @$pb.TagNumber(19)
  void clearSizeCategoryIndex() => clearField(19);

  @$pb.TagNumber(20)
  $core.Map<$core.String, $core.double> get weedCategories => $_getMap(19);

  @$pb.TagNumber(21)
  $core.List<Embedding> get embeddingHistory => $_getList(20);

  @$pb.TagNumber(22)
  PlantCaptchaUserPrediction get initialLabel => $_getN(21);
  @$pb.TagNumber(22)
  set initialLabel(PlantCaptchaUserPrediction v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasInitialLabel() => $_has(21);
  @$pb.TagNumber(22)
  void clearInitialLabel() => clearField(22);

  @$pb.TagNumber(23)
  $core.List<$core.double> get plantConfidenceHistory => $_getList(22);

  @$pb.TagNumber(24)
  $core.List<WeedClasses> get weedClassesHistory => $_getList(23);

  @$pb.TagNumber(25)
  $core.List<$core.double> get sizeMmHistory => $_getList(24);

  @$pb.TagNumber(26)
  Decisions get decisions => $_getN(25);
  @$pb.TagNumber(26)
  set decisions(Decisions v) { setField(26, v); }
  @$pb.TagNumber(26)
  $core.bool hasDecisions() => $_has(25);
  @$pb.TagNumber(26)
  void clearDecisions() => clearField(26);
  @$pb.TagNumber(26)
  Decisions ensureDecisions() => $_ensure(25);

  @$pb.TagNumber(27)
  $core.int get numDetectionsUsedForDecision => $_getIZ(26);
  @$pb.TagNumber(27)
  set numDetectionsUsedForDecision($core.int v) { $_setUnsignedInt32(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasNumDetectionsUsedForDecision() => $_has(26);
  @$pb.TagNumber(27)
  void clearNumDetectionsUsedForDecision() => clearField(27);

  @$pb.TagNumber(28)
  $core.Map<$core.String, $core.double> get embeddingDistances => $_getMap(27);
}

class GetTargetingEnabledRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTargetingEnabledRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetTargetingEnabledRequest._() : super();
  factory GetTargetingEnabledRequest() => create();
  factory GetTargetingEnabledRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTargetingEnabledRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTargetingEnabledRequest clone() => GetTargetingEnabledRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTargetingEnabledRequest copyWith(void Function(GetTargetingEnabledRequest) updates) => super.copyWith((message) => updates(message as GetTargetingEnabledRequest)) as GetTargetingEnabledRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTargetingEnabledRequest create() => GetTargetingEnabledRequest._();
  GetTargetingEnabledRequest createEmptyInstance() => create();
  static $pb.PbList<GetTargetingEnabledRequest> createRepeated() => $pb.PbList<GetTargetingEnabledRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTargetingEnabledRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTargetingEnabledRequest>(create);
  static GetTargetingEnabledRequest? _defaultInstance;
}

class GetTargetingEnabledResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTargetingEnabledResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..hasRequiredFields = false
  ;

  GetTargetingEnabledResponse._() : super();
  factory GetTargetingEnabledResponse({
    $core.bool? enabled,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    return _result;
  }
  factory GetTargetingEnabledResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTargetingEnabledResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTargetingEnabledResponse clone() => GetTargetingEnabledResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTargetingEnabledResponse copyWith(void Function(GetTargetingEnabledResponse) updates) => super.copyWith((message) => updates(message as GetTargetingEnabledResponse)) as GetTargetingEnabledResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTargetingEnabledResponse create() => GetTargetingEnabledResponse._();
  GetTargetingEnabledResponse createEmptyInstance() => create();
  static $pb.PbList<GetTargetingEnabledResponse> createRepeated() => $pb.PbList<GetTargetingEnabledResponse>();
  @$core.pragma('dart2js:noInline')
  static GetTargetingEnabledResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTargetingEnabledResponse>(create);
  static GetTargetingEnabledResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);
}

class GetBootedRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBootedRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetBootedRequest._() : super();
  factory GetBootedRequest() => create();
  factory GetBootedRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBootedRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBootedRequest clone() => GetBootedRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBootedRequest copyWith(void Function(GetBootedRequest) updates) => super.copyWith((message) => updates(message as GetBootedRequest)) as GetBootedRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBootedRequest create() => GetBootedRequest._();
  GetBootedRequest createEmptyInstance() => create();
  static $pb.PbList<GetBootedRequest> createRepeated() => $pb.PbList<GetBootedRequest>();
  @$core.pragma('dart2js:noInline')
  static GetBootedRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBootedRequest>(create);
  static GetBootedRequest? _defaultInstance;
}

class GetBootedResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBootedResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'weed_tracking'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'booted')
    ..hasRequiredFields = false
  ;

  GetBootedResponse._() : super();
  factory GetBootedResponse({
    $core.bool? booted,
  }) {
    final _result = create();
    if (booted != null) {
      _result.booted = booted;
    }
    return _result;
  }
  factory GetBootedResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBootedResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBootedResponse clone() => GetBootedResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBootedResponse copyWith(void Function(GetBootedResponse) updates) => super.copyWith((message) => updates(message as GetBootedResponse)) as GetBootedResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBootedResponse create() => GetBootedResponse._();
  GetBootedResponse createEmptyInstance() => create();
  static $pb.PbList<GetBootedResponse> createRepeated() => $pb.PbList<GetBootedResponse>();
  @$core.pragma('dart2js:noInline')
  static GetBootedResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBootedResponse>(create);
  static GetBootedResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get booted => $_getBF(0);
  @$pb.TagNumber(1)
  set booted($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBooted() => $_has(0);
  @$pb.TagNumber(1)
  void clearBooted() => clearField(1);
}

