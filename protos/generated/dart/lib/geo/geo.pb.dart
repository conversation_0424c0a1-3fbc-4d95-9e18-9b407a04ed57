///
//  Generated code. Do not modify.
//  source: geo/geo.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/timestamp.pb.dart' as $71;

import 'geo.pbenum.dart';

export 'geo.pbenum.dart';

class CaptureInfo extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CaptureInfo', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..e<FixType>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fixType', $pb.PbFieldType.OE, defaultOrMaker: FixType.FIX_TYPE_UNSPECIFIED, valueOf: FixType.valueOf, enumValues: FixType.values)
    ..aOM<$71.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureTime', subBuilder: $71.Timestamp.create)
    ..hasRequiredFields = false
  ;

  CaptureInfo._() : super();
  factory CaptureInfo({
    FixType? fixType,
    $71.Timestamp? captureTime,
  }) {
    final _result = create();
    if (fixType != null) {
      _result.fixType = fixType;
    }
    if (captureTime != null) {
      _result.captureTime = captureTime;
    }
    return _result;
  }
  factory CaptureInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CaptureInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CaptureInfo clone() => CaptureInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CaptureInfo copyWith(void Function(CaptureInfo) updates) => super.copyWith((message) => updates(message as CaptureInfo)) as CaptureInfo; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CaptureInfo create() => CaptureInfo._();
  CaptureInfo createEmptyInstance() => create();
  static $pb.PbList<CaptureInfo> createRepeated() => $pb.PbList<CaptureInfo>();
  @$core.pragma('dart2js:noInline')
  static CaptureInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CaptureInfo>(create);
  static CaptureInfo? _defaultInstance;

  @$pb.TagNumber(1)
  FixType get fixType => $_getN(0);
  @$pb.TagNumber(1)
  set fixType(FixType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasFixType() => $_has(0);
  @$pb.TagNumber(1)
  void clearFixType() => clearField(1);

  @$pb.TagNumber(2)
  $71.Timestamp get captureTime => $_getN(1);
  @$pb.TagNumber(2)
  set captureTime($71.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasCaptureTime() => $_has(1);
  @$pb.TagNumber(2)
  void clearCaptureTime() => clearField(2);
  @$pb.TagNumber(2)
  $71.Timestamp ensureCaptureTime() => $_ensure(1);
}

class Id extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Id', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  Id._() : super();
  factory Id({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory Id.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Id.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Id clone() => Id()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Id copyWith(void Function(Id) updates) => super.copyWith((message) => updates(message as Id)) as Id; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Id create() => Id._();
  Id createEmptyInstance() => create();
  static $pb.PbList<Id> createRepeated() => $pb.PbList<Id>();
  @$core.pragma('dart2js:noInline')
  static Id getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Id>(create);
  static Id? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class Point extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Point', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lng', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alt', $pb.PbFieldType.OD)
    ..aOM<CaptureInfo>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureInfo', subBuilder: CaptureInfo.create)
    ..aOM<Id>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', subBuilder: Id.create)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  Point._() : super();
  factory Point({
    $core.double? lng,
    $core.double? lat,
    $core.double? alt,
    CaptureInfo? captureInfo,
    Id? id,
    $core.String? name,
  }) {
    final _result = create();
    if (lng != null) {
      _result.lng = lng;
    }
    if (lat != null) {
      _result.lat = lat;
    }
    if (alt != null) {
      _result.alt = alt;
    }
    if (captureInfo != null) {
      _result.captureInfo = captureInfo;
    }
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory Point.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Point.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Point clone() => Point()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Point copyWith(void Function(Point) updates) => super.copyWith((message) => updates(message as Point)) as Point; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Point create() => Point._();
  Point createEmptyInstance() => create();
  static $pb.PbList<Point> createRepeated() => $pb.PbList<Point>();
  @$core.pragma('dart2js:noInline')
  static Point getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Point>(create);
  static Point? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get lng => $_getN(0);
  @$pb.TagNumber(1)
  set lng($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLng() => $_has(0);
  @$pb.TagNumber(1)
  void clearLng() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get lat => $_getN(1);
  @$pb.TagNumber(2)
  set lat($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLat() => $_has(1);
  @$pb.TagNumber(2)
  void clearLat() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get alt => $_getN(2);
  @$pb.TagNumber(3)
  set alt($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlt() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlt() => clearField(3);

  @$pb.TagNumber(4)
  CaptureInfo get captureInfo => $_getN(3);
  @$pb.TagNumber(4)
  set captureInfo(CaptureInfo v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasCaptureInfo() => $_has(3);
  @$pb.TagNumber(4)
  void clearCaptureInfo() => clearField(4);
  @$pb.TagNumber(4)
  CaptureInfo ensureCaptureInfo() => $_ensure(3);

  @$pb.TagNumber(5)
  Id get id => $_getN(4);
  @$pb.TagNumber(5)
  set id(Id v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasId() => $_has(4);
  @$pb.TagNumber(5)
  void clearId() => clearField(5);
  @$pb.TagNumber(5)
  Id ensureId() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.String get name => $_getSZ(5);
  @$pb.TagNumber(6)
  set name($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasName() => $_has(5);
  @$pb.TagNumber(6)
  void clearName() => clearField(6);
}

class LineString extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LineString', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..pc<Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'points', $pb.PbFieldType.PM, subBuilder: Point.create)
    ..hasRequiredFields = false
  ;

  LineString._() : super();
  factory LineString({
    $core.Iterable<Point>? points,
  }) {
    final _result = create();
    if (points != null) {
      _result.points.addAll(points);
    }
    return _result;
  }
  factory LineString.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LineString.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LineString clone() => LineString()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LineString copyWith(void Function(LineString) updates) => super.copyWith((message) => updates(message as LineString)) as LineString; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LineString create() => LineString._();
  LineString createEmptyInstance() => create();
  static $pb.PbList<LineString> createRepeated() => $pb.PbList<LineString>();
  @$core.pragma('dart2js:noInline')
  static LineString getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LineString>(create);
  static LineString? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Point> get points => $_getList(0);
}

class AbLine extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AbLine', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..aOM<Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'a', subBuilder: Point.create)
    ..aOM<Point>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'b', subBuilder: Point.create)
    ..hasRequiredFields = false
  ;

  AbLine._() : super();
  factory AbLine({
    Point? a,
    Point? b,
  }) {
    final _result = create();
    if (a != null) {
      _result.a = a;
    }
    if (b != null) {
      _result.b = b;
    }
    return _result;
  }
  factory AbLine.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AbLine.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AbLine clone() => AbLine()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AbLine copyWith(void Function(AbLine) updates) => super.copyWith((message) => updates(message as AbLine)) as AbLine; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AbLine create() => AbLine._();
  AbLine createEmptyInstance() => create();
  static $pb.PbList<AbLine> createRepeated() => $pb.PbList<AbLine>();
  @$core.pragma('dart2js:noInline')
  static AbLine getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AbLine>(create);
  static AbLine? _defaultInstance;

  @$pb.TagNumber(1)
  Point get a => $_getN(0);
  @$pb.TagNumber(1)
  set a(Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasA() => $_has(0);
  @$pb.TagNumber(1)
  void clearA() => clearField(1);
  @$pb.TagNumber(1)
  Point ensureA() => $_ensure(0);

  @$pb.TagNumber(2)
  Point get b => $_getN(1);
  @$pb.TagNumber(2)
  set b(Point v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasB() => $_has(1);
  @$pb.TagNumber(2)
  void clearB() => clearField(2);
  @$pb.TagNumber(2)
  Point ensureB() => $_ensure(1);
}

class Polygon extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Polygon', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..aOM<PolygonRing>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'boundary', subBuilder: PolygonRing.create)
    ..pc<PolygonRing>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'holes', $pb.PbFieldType.PM, subBuilder: PolygonRing.create)
    ..hasRequiredFields = false
  ;

  Polygon._() : super();
  factory Polygon({
    PolygonRing? boundary,
    $core.Iterable<PolygonRing>? holes,
  }) {
    final _result = create();
    if (boundary != null) {
      _result.boundary = boundary;
    }
    if (holes != null) {
      _result.holes.addAll(holes);
    }
    return _result;
  }
  factory Polygon.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Polygon.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Polygon clone() => Polygon()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Polygon copyWith(void Function(Polygon) updates) => super.copyWith((message) => updates(message as Polygon)) as Polygon; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Polygon create() => Polygon._();
  Polygon createEmptyInstance() => create();
  static $pb.PbList<Polygon> createRepeated() => $pb.PbList<Polygon>();
  @$core.pragma('dart2js:noInline')
  static Polygon getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Polygon>(create);
  static Polygon? _defaultInstance;

  @$pb.TagNumber(1)
  PolygonRing get boundary => $_getN(0);
  @$pb.TagNumber(1)
  set boundary(PolygonRing v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasBoundary() => $_has(0);
  @$pb.TagNumber(1)
  void clearBoundary() => clearField(1);
  @$pb.TagNumber(1)
  PolygonRing ensureBoundary() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<PolygonRing> get holes => $_getList(1);
}

class PolygonRing extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PolygonRing', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..pc<Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'points', $pb.PbFieldType.PM, subBuilder: Point.create)
    ..hasRequiredFields = false
  ;

  PolygonRing._() : super();
  factory PolygonRing({
    $core.Iterable<Point>? points,
  }) {
    final _result = create();
    if (points != null) {
      _result.points.addAll(points);
    }
    return _result;
  }
  factory PolygonRing.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PolygonRing.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PolygonRing clone() => PolygonRing()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PolygonRing copyWith(void Function(PolygonRing) updates) => super.copyWith((message) => updates(message as PolygonRing)) as PolygonRing; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PolygonRing create() => PolygonRing._();
  PolygonRing createEmptyInstance() => create();
  static $pb.PbList<PolygonRing> createRepeated() => $pb.PbList<PolygonRing>();
  @$core.pragma('dart2js:noInline')
  static PolygonRing getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PolygonRing>(create);
  static PolygonRing? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Point> get points => $_getList(0);
}

class MultiPolygon extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MultiPolygon', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.geo'), createEmptyInstance: create)
    ..pc<Polygon>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'polygons', $pb.PbFieldType.PM, subBuilder: Polygon.create)
    ..hasRequiredFields = false
  ;

  MultiPolygon._() : super();
  factory MultiPolygon({
    $core.Iterable<Polygon>? polygons,
  }) {
    final _result = create();
    if (polygons != null) {
      _result.polygons.addAll(polygons);
    }
    return _result;
  }
  factory MultiPolygon.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MultiPolygon.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MultiPolygon clone() => MultiPolygon()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MultiPolygon copyWith(void Function(MultiPolygon) updates) => super.copyWith((message) => updates(message as MultiPolygon)) as MultiPolygon; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MultiPolygon create() => MultiPolygon._();
  MultiPolygon createEmptyInstance() => create();
  static $pb.PbList<MultiPolygon> createRepeated() => $pb.PbList<MultiPolygon>();
  @$core.pragma('dart2js:noInline')
  static MultiPolygon getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MultiPolygon>(create);
  static MultiPolygon? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Polygon> get polygons => $_getList(0);
}

