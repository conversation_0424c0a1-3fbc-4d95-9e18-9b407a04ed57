///
//  Generated code. Do not modify.
//  source: rtc/implement.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use hitchTypeDescriptor instead')
const HitchType$json = const {
  '1': 'HitchType',
  '2': const [
    const {'1': 'HITCH_TYPE_UNSPECIFIED', '2': 0},
    const {'1': 'FIXED', '2': 1},
    const {'1': 'THREE_POINT', '2': 2},
  ],
};

/// Descriptor for `HitchType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List hitchTypeDescriptor = $convert.base64Decode('CglIaXRjaFR5cGUSGgoWSElUQ0hfVFlQRV9VTlNQRUNJRklFRBAAEgkKBUZJWEVEEAESDwoLVEhSRUVfUE9JTlQQAg==');
@$core.Deprecated('Use implementDefinitionDescriptor instead')
const ImplementDefinition$json = const {
  '1': 'ImplementDefinition',
  '2': const [
    const {'1': 'hitch_type', '3': 1, '4': 1, '5': 14, '6': '.carbon.rtc.HitchType', '10': 'hitchType'},
    const {'1': 'width_m', '3': 2, '4': 1, '5': 2, '10': 'widthM'},
    const {'1': 'hitch_to_start_m', '3': 3, '4': 1, '5': 2, '10': 'hitchToStartM'},
    const {'1': 'hitch_to_end_m', '3': 4, '4': 1, '5': 2, '10': 'hitchToEndM'},
    const {'1': 'tool_speed_kmh', '3': 5, '4': 1, '5': 2, '10': 'toolSpeedKmh'},
    const {'1': 'transition_speed_kmh', '3': 6, '4': 1, '5': 2, '10': 'transitionSpeedKmh'},
    const {'1': 'run_up_distance_m', '3': 7, '4': 1, '5': 2, '10': 'runUpDistanceM'},
    const {'1': 'straighten_distance_m', '3': 8, '4': 1, '5': 2, '10': 'straightenDistanceM'},
  ],
};

/// Descriptor for `ImplementDefinition`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List implementDefinitionDescriptor = $convert.base64Decode('ChNJbXBsZW1lbnREZWZpbml0aW9uEjQKCmhpdGNoX3R5cGUYASABKA4yFS5jYXJib24ucnRjLkhpdGNoVHlwZVIJaGl0Y2hUeXBlEhcKB3dpZHRoX20YAiABKAJSBndpZHRoTRInChBoaXRjaF90b19zdGFydF9tGAMgASgCUg1oaXRjaFRvU3RhcnRNEiMKDmhpdGNoX3RvX2VuZF9tGAQgASgCUgtoaXRjaFRvRW5kTRIkCg50b29sX3NwZWVkX2ttaBgFIAEoAlIMdG9vbFNwZWVkS21oEjAKFHRyYW5zaXRpb25fc3BlZWRfa21oGAYgASgCUhJ0cmFuc2l0aW9uU3BlZWRLbWgSKQoRcnVuX3VwX2Rpc3RhbmNlX20YByABKAJSDnJ1blVwRGlzdGFuY2VNEjIKFXN0cmFpZ2h0ZW5fZGlzdGFuY2VfbRgIIAEoAlITc3RyYWlnaHRlbkRpc3RhbmNlTQ==');
