///
//  Generated code. Do not modify.
//  source: rtc/tractor.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class RelativePosition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RelativePosition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xM', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yM', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  RelativePosition._() : super();
  factory RelativePosition({
    $core.double? xM,
    $core.double? yM,
  }) {
    final _result = create();
    if (xM != null) {
      _result.xM = xM;
    }
    if (yM != null) {
      _result.yM = yM;
    }
    return _result;
  }
  factory RelativePosition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RelativePosition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RelativePosition clone() => RelativePosition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RelativePosition copyWith(void Function(RelativePosition) updates) => super.copyWith((message) => updates(message as RelativePosition)) as RelativePosition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RelativePosition create() => RelativePosition._();
  RelativePosition createEmptyInstance() => create();
  static $pb.PbList<RelativePosition> createRepeated() => $pb.PbList<RelativePosition>();
  @$core.pragma('dart2js:noInline')
  static RelativePosition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RelativePosition>(create);
  static RelativePosition? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get xM => $_getN(0);
  @$pb.TagNumber(1)
  set xM($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasXM() => $_has(0);
  @$pb.TagNumber(1)
  void clearXM() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get yM => $_getN(1);
  @$pb.TagNumber(2)
  set yM($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYM() => $_has(1);
  @$pb.TagNumber(2)
  void clearYM() => clearField(2);
}

class TractorDefinition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TractorDefinition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<RelativePosition>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rearAxleCenterPt', subBuilder: RelativePosition.create)
    ..aOM<RelativePosition>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fixedHitchPt', subBuilder: RelativePosition.create)
    ..aOM<RelativePosition>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'threePtHitchPt', subBuilder: RelativePosition.create)
    ..aOM<RelativePosition>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nosePt', subBuilder: RelativePosition.create)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxWidthM', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  TractorDefinition._() : super();
  factory TractorDefinition({
    RelativePosition? rearAxleCenterPt,
    RelativePosition? fixedHitchPt,
    RelativePosition? threePtHitchPt,
    RelativePosition? nosePt,
    $core.double? maxWidthM,
  }) {
    final _result = create();
    if (rearAxleCenterPt != null) {
      _result.rearAxleCenterPt = rearAxleCenterPt;
    }
    if (fixedHitchPt != null) {
      _result.fixedHitchPt = fixedHitchPt;
    }
    if (threePtHitchPt != null) {
      _result.threePtHitchPt = threePtHitchPt;
    }
    if (nosePt != null) {
      _result.nosePt = nosePt;
    }
    if (maxWidthM != null) {
      _result.maxWidthM = maxWidthM;
    }
    return _result;
  }
  factory TractorDefinition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TractorDefinition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TractorDefinition clone() => TractorDefinition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TractorDefinition copyWith(void Function(TractorDefinition) updates) => super.copyWith((message) => updates(message as TractorDefinition)) as TractorDefinition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TractorDefinition create() => TractorDefinition._();
  TractorDefinition createEmptyInstance() => create();
  static $pb.PbList<TractorDefinition> createRepeated() => $pb.PbList<TractorDefinition>();
  @$core.pragma('dart2js:noInline')
  static TractorDefinition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TractorDefinition>(create);
  static TractorDefinition? _defaultInstance;

  @$pb.TagNumber(1)
  RelativePosition get rearAxleCenterPt => $_getN(0);
  @$pb.TagNumber(1)
  set rearAxleCenterPt(RelativePosition v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasRearAxleCenterPt() => $_has(0);
  @$pb.TagNumber(1)
  void clearRearAxleCenterPt() => clearField(1);
  @$pb.TagNumber(1)
  RelativePosition ensureRearAxleCenterPt() => $_ensure(0);

  @$pb.TagNumber(2)
  RelativePosition get fixedHitchPt => $_getN(1);
  @$pb.TagNumber(2)
  set fixedHitchPt(RelativePosition v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasFixedHitchPt() => $_has(1);
  @$pb.TagNumber(2)
  void clearFixedHitchPt() => clearField(2);
  @$pb.TagNumber(2)
  RelativePosition ensureFixedHitchPt() => $_ensure(1);

  @$pb.TagNumber(3)
  RelativePosition get threePtHitchPt => $_getN(2);
  @$pb.TagNumber(3)
  set threePtHitchPt(RelativePosition v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasThreePtHitchPt() => $_has(2);
  @$pb.TagNumber(3)
  void clearThreePtHitchPt() => clearField(3);
  @$pb.TagNumber(3)
  RelativePosition ensureThreePtHitchPt() => $_ensure(2);

  @$pb.TagNumber(4)
  RelativePosition get nosePt => $_getN(3);
  @$pb.TagNumber(4)
  set nosePt(RelativePosition v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasNosePt() => $_has(3);
  @$pb.TagNumber(4)
  void clearNosePt() => clearField(4);
  @$pb.TagNumber(4)
  RelativePosition ensureNosePt() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.double get maxWidthM => $_getN(4);
  @$pb.TagNumber(5)
  set maxWidthM($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasMaxWidthM() => $_has(4);
  @$pb.TagNumber(5)
  void clearMaxWidthM() => clearField(5);
}

