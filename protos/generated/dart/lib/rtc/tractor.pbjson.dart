///
//  Generated code. Do not modify.
//  source: rtc/tractor.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use relativePositionDescriptor instead')
const RelativePosition$json = const {
  '1': 'RelativePosition',
  '2': const [
    const {'1': 'x_m', '3': 1, '4': 1, '5': 2, '10': 'xM'},
    const {'1': 'y_m', '3': 2, '4': 1, '5': 2, '10': 'yM'},
  ],
};

/// Descriptor for `RelativePosition`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List relativePositionDescriptor = $convert.base64Decode('ChBSZWxhdGl2ZVBvc2l0aW9uEg8KA3hfbRgBIAEoAlICeE0SDwoDeV9tGAIgASgCUgJ5TQ==');
@$core.Deprecated('Use tractorDefinitionDescriptor instead')
const TractorDefinition$json = const {
  '1': 'TractorDefinition',
  '2': const [
    const {'1': 'rear_axle_center_pt', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.RelativePosition', '10': 'rearAxleCenterPt'},
    const {'1': 'fixed_hitch_pt', '3': 2, '4': 1, '5': 11, '6': '.carbon.rtc.RelativePosition', '10': 'fixedHitchPt'},
    const {'1': 'three_pt_hitch_pt', '3': 3, '4': 1, '5': 11, '6': '.carbon.rtc.RelativePosition', '10': 'threePtHitchPt'},
    const {'1': 'nose_pt', '3': 4, '4': 1, '5': 11, '6': '.carbon.rtc.RelativePosition', '10': 'nosePt'},
    const {'1': 'max_width_m', '3': 5, '4': 1, '5': 2, '10': 'maxWidthM'},
  ],
};

/// Descriptor for `TractorDefinition`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List tractorDefinitionDescriptor = $convert.base64Decode('ChFUcmFjdG9yRGVmaW5pdGlvbhJLChNyZWFyX2F4bGVfY2VudGVyX3B0GAEgASgLMhwuY2FyYm9uLnJ0Yy5SZWxhdGl2ZVBvc2l0aW9uUhByZWFyQXhsZUNlbnRlclB0EkIKDmZpeGVkX2hpdGNoX3B0GAIgASgLMhwuY2FyYm9uLnJ0Yy5SZWxhdGl2ZVBvc2l0aW9uUgxmaXhlZEhpdGNoUHQSRwoRdGhyZWVfcHRfaGl0Y2hfcHQYAyABKAsyHC5jYXJib24ucnRjLlJlbGF0aXZlUG9zaXRpb25SDnRocmVlUHRIaXRjaFB0EjUKB25vc2VfcHQYBCABKAsyHC5jYXJib24ucnRjLlJlbGF0aXZlUG9zaXRpb25SBm5vc2VQdBIeCgttYXhfd2lkdGhfbRgFIAEoAlIJbWF4V2lkdGhN');
