///
//  Generated code. Do not modify.
//  source: rtc/location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'location_history.pb.dart' as $57;
import '../google/protobuf/empty.pb.dart' as $43;
export 'location_history.pb.dart';

class LocationHistoryClient extends $grpc.Client {
  static final _$logLocationHistory =
      $grpc.ClientMethod<$57.LogLocationHistoryRequest, $43.Empty>(
          '/carbon.rtc.LocationHistory/LogLocationHistory',
          ($57.LogLocationHistoryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $43.Empty.fromBuffer(value));
  static final _$listRobots =
      $grpc.ClientMethod<$57.ListRobotsRequest, $57.ListRobotsResponse>(
          '/carbon.rtc.LocationHistory/ListRobots',
          ($57.ListRobotsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $57.ListRobotsResponse.fromBuffer(value));
  static final _$listLocationHistory = $grpc.ClientMethod<
          $57.ListLocationHistoryRequest, $57.ListLocationHistoryResponse>(
      '/carbon.rtc.LocationHistory/ListLocationHistory',
      ($57.ListLocationHistoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $57.ListLocationHistoryResponse.fromBuffer(value));
  static final _$streamLocation =
      $grpc.ClientMethod<$57.LocationHistoryRecord, $43.Empty>(
          '/carbon.rtc.LocationHistory/StreamLocation',
          ($57.LocationHistoryRecord value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $43.Empty.fromBuffer(value));

  LocationHistoryClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$43.Empty> logLocationHistory(
      $57.LogLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$logLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$57.ListRobotsResponse> listRobots(
      $57.ListRobotsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listRobots, request, options: options);
  }

  $grpc.ResponseFuture<$57.ListLocationHistoryResponse> listLocationHistory(
      $57.ListLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$43.Empty> streamLocation(
      $async.Stream<$57.LocationHistoryRecord> request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$streamLocation, request, options: options)
        .single;
  }
}

abstract class LocationHistoryServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.LocationHistory';

  LocationHistoryServiceBase() {
    $addMethod($grpc.ServiceMethod<$57.LogLocationHistoryRequest, $43.Empty>(
        'LogLocationHistory',
        logLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.LogLocationHistoryRequest.fromBuffer(value),
        ($43.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$57.ListRobotsRequest, $57.ListRobotsResponse>(
            'ListRobots',
            listRobots_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $57.ListRobotsRequest.fromBuffer(value),
            ($57.ListRobotsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$57.ListLocationHistoryRequest,
            $57.ListLocationHistoryResponse>(
        'ListLocationHistory',
        listLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.ListLocationHistoryRequest.fromBuffer(value),
        ($57.ListLocationHistoryResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$57.LocationHistoryRecord, $43.Empty>(
        'StreamLocation',
        streamLocation,
        true,
        false,
        ($core.List<$core.int> value) =>
            $57.LocationHistoryRecord.fromBuffer(value),
        ($43.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$43.Empty> logLocationHistory_Pre($grpc.ServiceCall call,
      $async.Future<$57.LogLocationHistoryRequest> request) async {
    return logLocationHistory(call, await request);
  }

  $async.Future<$57.ListRobotsResponse> listRobots_Pre($grpc.ServiceCall call,
      $async.Future<$57.ListRobotsRequest> request) async {
    return listRobots(call, await request);
  }

  $async.Future<$57.ListLocationHistoryResponse> listLocationHistory_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.ListLocationHistoryRequest> request) async {
    return listLocationHistory(call, await request);
  }

  $async.Future<$43.Empty> logLocationHistory(
      $grpc.ServiceCall call, $57.LogLocationHistoryRequest request);
  $async.Future<$57.ListRobotsResponse> listRobots(
      $grpc.ServiceCall call, $57.ListRobotsRequest request);
  $async.Future<$57.ListLocationHistoryResponse> listLocationHistory(
      $grpc.ServiceCall call, $57.ListLocationHistoryRequest request);
  $async.Future<$43.Empty> streamLocation(
      $grpc.ServiceCall call, $async.Stream<$57.LocationHistoryRecord> request);
}
