///
//  Generated code. Do not modify.
//  source: rtc/implement.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

// ignore_for_file: UNDEFINED_SHOWN_NAME
import 'dart:core' as $core;
import 'package:protobuf/protobuf.dart' as $pb;

class HitchType extends $pb.ProtobufEnum {
  static const HitchType HITCH_TYPE_UNSPECIFIED = HitchType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'HITCH_TYPE_UNSPECIFIED');
  static const HitchType FIXED = HitchType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'FIXED');
  static const HitchType THREE_POINT = HitchType._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'THREE_POINT');

  static const $core.List<HitchType> values = <HitchType> [
    HITCH_TYPE_UNSPECIFIED,
    FIXED,
    THREE_POINT,
  ];

  static final $core.Map<$core.int, HitchType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static HitchType? valueOf($core.int value) => _byValue[value];

  const HitchType._($core.int v, $core.String n) : super(v, n);
}

