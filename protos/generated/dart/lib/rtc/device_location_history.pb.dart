///
//  Generated code. Do not modify.
//  source: rtc/device_location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../geo/geo.pb.dart' as $76;
import '../google/protobuf/timestamp.pb.dart' as $71;

import 'device_location_history.pbenum.dart';

export 'device_location_history.pbenum.dart';

class DeviceLocationHistoryRecord extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeviceLocationHistoryRecord', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$76.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'point', subBuilder: $76.Point.create)
    ..aOM<$71.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $71.Timestamp.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceId')
    ..e<DeviceType>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceType', $pb.PbFieldType.OE, defaultOrMaker: DeviceType.DEVICE_TYPE_UNKNOWN, valueOf: DeviceType.valueOf, enumValues: DeviceType.values)
    ..hasRequiredFields = false
  ;

  DeviceLocationHistoryRecord._() : super();
  factory DeviceLocationHistoryRecord({
    $76.Point? point,
    $71.Timestamp? timestamp,
    $core.String? deviceId,
    DeviceType? deviceType,
  }) {
    final _result = create();
    if (point != null) {
      _result.point = point;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    if (deviceId != null) {
      _result.deviceId = deviceId;
    }
    if (deviceType != null) {
      _result.deviceType = deviceType;
    }
    return _result;
  }
  factory DeviceLocationHistoryRecord.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeviceLocationHistoryRecord.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeviceLocationHistoryRecord clone() => DeviceLocationHistoryRecord()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeviceLocationHistoryRecord copyWith(void Function(DeviceLocationHistoryRecord) updates) => super.copyWith((message) => updates(message as DeviceLocationHistoryRecord)) as DeviceLocationHistoryRecord; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeviceLocationHistoryRecord create() => DeviceLocationHistoryRecord._();
  DeviceLocationHistoryRecord createEmptyInstance() => create();
  static $pb.PbList<DeviceLocationHistoryRecord> createRepeated() => $pb.PbList<DeviceLocationHistoryRecord>();
  @$core.pragma('dart2js:noInline')
  static DeviceLocationHistoryRecord getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeviceLocationHistoryRecord>(create);
  static DeviceLocationHistoryRecord? _defaultInstance;

  @$pb.TagNumber(1)
  $76.Point get point => $_getN(0);
  @$pb.TagNumber(1)
  set point($76.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPoint() => $_has(0);
  @$pb.TagNumber(1)
  void clearPoint() => clearField(1);
  @$pb.TagNumber(1)
  $76.Point ensurePoint() => $_ensure(0);

  @$pb.TagNumber(2)
  $71.Timestamp get timestamp => $_getN(1);
  @$pb.TagNumber(2)
  set timestamp($71.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => clearField(2);
  @$pb.TagNumber(2)
  $71.Timestamp ensureTimestamp() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(3)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeviceId() => clearField(3);

  @$pb.TagNumber(4)
  DeviceType get deviceType => $_getN(3);
  @$pb.TagNumber(4)
  set deviceType(DeviceType v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceType() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeviceType() => clearField(4);
}

class DeviceLocationHistoryRecordList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeviceLocationHistoryRecordList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<DeviceLocationHistoryRecord>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'records', $pb.PbFieldType.PM, subBuilder: DeviceLocationHistoryRecord.create)
    ..hasRequiredFields = false
  ;

  DeviceLocationHistoryRecordList._() : super();
  factory DeviceLocationHistoryRecordList({
    $core.Iterable<DeviceLocationHistoryRecord>? records,
  }) {
    final _result = create();
    if (records != null) {
      _result.records.addAll(records);
    }
    return _result;
  }
  factory DeviceLocationHistoryRecordList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeviceLocationHistoryRecordList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeviceLocationHistoryRecordList clone() => DeviceLocationHistoryRecordList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeviceLocationHistoryRecordList copyWith(void Function(DeviceLocationHistoryRecordList) updates) => super.copyWith((message) => updates(message as DeviceLocationHistoryRecordList)) as DeviceLocationHistoryRecordList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeviceLocationHistoryRecordList create() => DeviceLocationHistoryRecordList._();
  DeviceLocationHistoryRecordList createEmptyInstance() => create();
  static $pb.PbList<DeviceLocationHistoryRecordList> createRepeated() => $pb.PbList<DeviceLocationHistoryRecordList>();
  @$core.pragma('dart2js:noInline')
  static DeviceLocationHistoryRecordList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeviceLocationHistoryRecordList>(create);
  static DeviceLocationHistoryRecordList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<DeviceLocationHistoryRecord> get records => $_getList(0);
}

class LogDeviceLocationHistoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LogDeviceLocationHistoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<DeviceLocationHistoryRecordList>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'history', subBuilder: DeviceLocationHistoryRecordList.create)
    ..hasRequiredFields = false
  ;

  LogDeviceLocationHistoryRequest._() : super();
  factory LogDeviceLocationHistoryRequest({
    DeviceLocationHistoryRecordList? history,
  }) {
    final _result = create();
    if (history != null) {
      _result.history = history;
    }
    return _result;
  }
  factory LogDeviceLocationHistoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LogDeviceLocationHistoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LogDeviceLocationHistoryRequest clone() => LogDeviceLocationHistoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LogDeviceLocationHistoryRequest copyWith(void Function(LogDeviceLocationHistoryRequest) updates) => super.copyWith((message) => updates(message as LogDeviceLocationHistoryRequest)) as LogDeviceLocationHistoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LogDeviceLocationHistoryRequest create() => LogDeviceLocationHistoryRequest._();
  LogDeviceLocationHistoryRequest createEmptyInstance() => create();
  static $pb.PbList<LogDeviceLocationHistoryRequest> createRepeated() => $pb.PbList<LogDeviceLocationHistoryRequest>();
  @$core.pragma('dart2js:noInline')
  static LogDeviceLocationHistoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LogDeviceLocationHistoryRequest>(create);
  static LogDeviceLocationHistoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  DeviceLocationHistoryRecordList get history => $_getN(0);
  @$pb.TagNumber(1)
  set history(DeviceLocationHistoryRecordList v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHistory() => $_has(0);
  @$pb.TagNumber(1)
  void clearHistory() => clearField(1);
  @$pb.TagNumber(1)
  DeviceLocationHistoryRecordList ensureHistory() => $_ensure(0);
}

class ListDevicesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListDevicesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageSize', $pb.PbFieldType.O3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pPS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceIds')
    ..hasRequiredFields = false
  ;

  ListDevicesRequest._() : super();
  factory ListDevicesRequest({
    $core.int? pageSize,
    $core.String? pageToken,
    $core.Iterable<$core.String>? deviceIds,
  }) {
    final _result = create();
    if (pageSize != null) {
      _result.pageSize = pageSize;
    }
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (deviceIds != null) {
      _result.deviceIds.addAll(deviceIds);
    }
    return _result;
  }
  factory ListDevicesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListDevicesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListDevicesRequest clone() => ListDevicesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListDevicesRequest copyWith(void Function(ListDevicesRequest) updates) => super.copyWith((message) => updates(message as ListDevicesRequest)) as ListDevicesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListDevicesRequest create() => ListDevicesRequest._();
  ListDevicesRequest createEmptyInstance() => create();
  static $pb.PbList<ListDevicesRequest> createRepeated() => $pb.PbList<ListDevicesRequest>();
  @$core.pragma('dart2js:noInline')
  static ListDevicesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListDevicesRequest>(create);
  static ListDevicesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get pageSize => $_getIZ(0);
  @$pb.TagNumber(1)
  set pageSize($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageSize() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageSize() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get pageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set pageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearPageToken() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.String> get deviceIds => $_getList(2);
}

class ListDevicesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListDevicesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..pc<DeviceSummary>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'devices', $pb.PbFieldType.PM, subBuilder: DeviceSummary.create)
    ..hasRequiredFields = false
  ;

  ListDevicesResponse._() : super();
  factory ListDevicesResponse({
    $core.String? nextPageToken,
    $core.Iterable<DeviceSummary>? devices,
  }) {
    final _result = create();
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    if (devices != null) {
      _result.devices.addAll(devices);
    }
    return _result;
  }
  factory ListDevicesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListDevicesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListDevicesResponse clone() => ListDevicesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListDevicesResponse copyWith(void Function(ListDevicesResponse) updates) => super.copyWith((message) => updates(message as ListDevicesResponse)) as ListDevicesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListDevicesResponse create() => ListDevicesResponse._();
  ListDevicesResponse createEmptyInstance() => create();
  static $pb.PbList<ListDevicesResponse> createRepeated() => $pb.PbList<ListDevicesResponse>();
  @$core.pragma('dart2js:noInline')
  static ListDevicesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListDevicesResponse>(create);
  static ListDevicesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get nextPageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set nextPageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNextPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearNextPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<DeviceSummary> get devices => $_getList(1);
}

class DeviceSummary extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeviceSummary', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOM<DeviceLocationHistoryRecord>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastSeen', subBuilder: DeviceLocationHistoryRecord.create)
    ..hasRequiredFields = false
  ;

  DeviceSummary._() : super();
  factory DeviceSummary({
    $core.String? id,
    DeviceLocationHistoryRecord? lastSeen,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (lastSeen != null) {
      _result.lastSeen = lastSeen;
    }
    return _result;
  }
  factory DeviceSummary.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeviceSummary.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeviceSummary clone() => DeviceSummary()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeviceSummary copyWith(void Function(DeviceSummary) updates) => super.copyWith((message) => updates(message as DeviceSummary)) as DeviceSummary; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeviceSummary create() => DeviceSummary._();
  DeviceSummary createEmptyInstance() => create();
  static $pb.PbList<DeviceSummary> createRepeated() => $pb.PbList<DeviceSummary>();
  @$core.pragma('dart2js:noInline')
  static DeviceSummary getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeviceSummary>(create);
  static DeviceSummary? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  DeviceLocationHistoryRecord get lastSeen => $_getN(1);
  @$pb.TagNumber(2)
  set lastSeen(DeviceLocationHistoryRecord v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLastSeen() => $_has(1);
  @$pb.TagNumber(2)
  void clearLastSeen() => clearField(2);
  @$pb.TagNumber(2)
  DeviceLocationHistoryRecord ensureLastSeen() => $_ensure(1);
}

class ListDeviceLocationHistoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListDeviceLocationHistoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageSize', $pb.PbFieldType.O3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceId')
    ..aOM<$71.Timestamp>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'start', subBuilder: $71.Timestamp.create)
    ..aOM<$71.Timestamp>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'end', subBuilder: $71.Timestamp.create)
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'desc')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'includeClosest')
    ..hasRequiredFields = false
  ;

  ListDeviceLocationHistoryRequest._() : super();
  factory ListDeviceLocationHistoryRequest({
    $core.int? pageSize,
    $core.String? pageToken,
    $core.String? deviceId,
    $71.Timestamp? start,
    $71.Timestamp? end,
    $core.bool? desc,
    $core.bool? includeClosest,
  }) {
    final _result = create();
    if (pageSize != null) {
      _result.pageSize = pageSize;
    }
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (deviceId != null) {
      _result.deviceId = deviceId;
    }
    if (start != null) {
      _result.start = start;
    }
    if (end != null) {
      _result.end = end;
    }
    if (desc != null) {
      _result.desc = desc;
    }
    if (includeClosest != null) {
      _result.includeClosest = includeClosest;
    }
    return _result;
  }
  factory ListDeviceLocationHistoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListDeviceLocationHistoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListDeviceLocationHistoryRequest clone() => ListDeviceLocationHistoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListDeviceLocationHistoryRequest copyWith(void Function(ListDeviceLocationHistoryRequest) updates) => super.copyWith((message) => updates(message as ListDeviceLocationHistoryRequest)) as ListDeviceLocationHistoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListDeviceLocationHistoryRequest create() => ListDeviceLocationHistoryRequest._();
  ListDeviceLocationHistoryRequest createEmptyInstance() => create();
  static $pb.PbList<ListDeviceLocationHistoryRequest> createRepeated() => $pb.PbList<ListDeviceLocationHistoryRequest>();
  @$core.pragma('dart2js:noInline')
  static ListDeviceLocationHistoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListDeviceLocationHistoryRequest>(create);
  static ListDeviceLocationHistoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get pageSize => $_getIZ(0);
  @$pb.TagNumber(1)
  set pageSize($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageSize() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageSize() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get pageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set pageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearPageToken() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(3)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeviceId() => clearField(3);

  @$pb.TagNumber(4)
  $71.Timestamp get start => $_getN(3);
  @$pb.TagNumber(4)
  set start($71.Timestamp v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasStart() => $_has(3);
  @$pb.TagNumber(4)
  void clearStart() => clearField(4);
  @$pb.TagNumber(4)
  $71.Timestamp ensureStart() => $_ensure(3);

  @$pb.TagNumber(5)
  $71.Timestamp get end => $_getN(4);
  @$pb.TagNumber(5)
  set end($71.Timestamp v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasEnd() => $_has(4);
  @$pb.TagNumber(5)
  void clearEnd() => clearField(5);
  @$pb.TagNumber(5)
  $71.Timestamp ensureEnd() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.bool get desc => $_getBF(5);
  @$pb.TagNumber(6)
  set desc($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDesc() => $_has(5);
  @$pb.TagNumber(6)
  void clearDesc() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get includeClosest => $_getBF(6);
  @$pb.TagNumber(7)
  set includeClosest($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasIncludeClosest() => $_has(6);
  @$pb.TagNumber(7)
  void clearIncludeClosest() => clearField(7);
}

class ListDeviceLocationHistoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListDeviceLocationHistoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..aOM<DeviceLocationHistoryRecordList>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'history', subBuilder: DeviceLocationHistoryRecordList.create)
    ..hasRequiredFields = false
  ;

  ListDeviceLocationHistoryResponse._() : super();
  factory ListDeviceLocationHistoryResponse({
    $core.String? nextPageToken,
    DeviceLocationHistoryRecordList? history,
  }) {
    final _result = create();
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    if (history != null) {
      _result.history = history;
    }
    return _result;
  }
  factory ListDeviceLocationHistoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListDeviceLocationHistoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListDeviceLocationHistoryResponse clone() => ListDeviceLocationHistoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListDeviceLocationHistoryResponse copyWith(void Function(ListDeviceLocationHistoryResponse) updates) => super.copyWith((message) => updates(message as ListDeviceLocationHistoryResponse)) as ListDeviceLocationHistoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListDeviceLocationHistoryResponse create() => ListDeviceLocationHistoryResponse._();
  ListDeviceLocationHistoryResponse createEmptyInstance() => create();
  static $pb.PbList<ListDeviceLocationHistoryResponse> createRepeated() => $pb.PbList<ListDeviceLocationHistoryResponse>();
  @$core.pragma('dart2js:noInline')
  static ListDeviceLocationHistoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListDeviceLocationHistoryResponse>(create);
  static ListDeviceLocationHistoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get nextPageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set nextPageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNextPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearNextPageToken() => clearField(1);

  @$pb.TagNumber(2)
  DeviceLocationHistoryRecordList get history => $_getN(1);
  @$pb.TagNumber(2)
  set history(DeviceLocationHistoryRecordList v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasHistory() => $_has(1);
  @$pb.TagNumber(2)
  void clearHistory() => clearField(2);
  @$pb.TagNumber(2)
  DeviceLocationHistoryRecordList ensureHistory() => $_ensure(1);
}

