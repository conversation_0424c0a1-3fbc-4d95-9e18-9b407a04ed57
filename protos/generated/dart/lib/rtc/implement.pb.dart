///
//  Generated code. Do not modify.
//  source: rtc/implement.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'implement.pbenum.dart';

export 'implement.pbenum.dart';

class ImplementDefinition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ImplementDefinition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..e<HitchType>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hitchType', $pb.PbFieldType.OE, defaultOrMaker: HitchType.HITCH_TYPE_UNSPECIFIED, valueOf: HitchType.valueOf, enumValues: HitchType.values)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'widthM', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hitchToStartM', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hitchToEndM', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'toolSpeedKmh', $pb.PbFieldType.OF)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'transitionSpeedKmh', $pb.PbFieldType.OF)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'runUpDistanceM', $pb.PbFieldType.OF)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'straightenDistanceM', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  ImplementDefinition._() : super();
  factory ImplementDefinition({
    HitchType? hitchType,
    $core.double? widthM,
    $core.double? hitchToStartM,
    $core.double? hitchToEndM,
    $core.double? toolSpeedKmh,
    $core.double? transitionSpeedKmh,
    $core.double? runUpDistanceM,
    $core.double? straightenDistanceM,
  }) {
    final _result = create();
    if (hitchType != null) {
      _result.hitchType = hitchType;
    }
    if (widthM != null) {
      _result.widthM = widthM;
    }
    if (hitchToStartM != null) {
      _result.hitchToStartM = hitchToStartM;
    }
    if (hitchToEndM != null) {
      _result.hitchToEndM = hitchToEndM;
    }
    if (toolSpeedKmh != null) {
      _result.toolSpeedKmh = toolSpeedKmh;
    }
    if (transitionSpeedKmh != null) {
      _result.transitionSpeedKmh = transitionSpeedKmh;
    }
    if (runUpDistanceM != null) {
      _result.runUpDistanceM = runUpDistanceM;
    }
    if (straightenDistanceM != null) {
      _result.straightenDistanceM = straightenDistanceM;
    }
    return _result;
  }
  factory ImplementDefinition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImplementDefinition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImplementDefinition clone() => ImplementDefinition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImplementDefinition copyWith(void Function(ImplementDefinition) updates) => super.copyWith((message) => updates(message as ImplementDefinition)) as ImplementDefinition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ImplementDefinition create() => ImplementDefinition._();
  ImplementDefinition createEmptyInstance() => create();
  static $pb.PbList<ImplementDefinition> createRepeated() => $pb.PbList<ImplementDefinition>();
  @$core.pragma('dart2js:noInline')
  static ImplementDefinition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImplementDefinition>(create);
  static ImplementDefinition? _defaultInstance;

  @$pb.TagNumber(1)
  HitchType get hitchType => $_getN(0);
  @$pb.TagNumber(1)
  set hitchType(HitchType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHitchType() => $_has(0);
  @$pb.TagNumber(1)
  void clearHitchType() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get widthM => $_getN(1);
  @$pb.TagNumber(2)
  set widthM($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWidthM() => $_has(1);
  @$pb.TagNumber(2)
  void clearWidthM() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get hitchToStartM => $_getN(2);
  @$pb.TagNumber(3)
  set hitchToStartM($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasHitchToStartM() => $_has(2);
  @$pb.TagNumber(3)
  void clearHitchToStartM() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get hitchToEndM => $_getN(3);
  @$pb.TagNumber(4)
  set hitchToEndM($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasHitchToEndM() => $_has(3);
  @$pb.TagNumber(4)
  void clearHitchToEndM() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get toolSpeedKmh => $_getN(4);
  @$pb.TagNumber(5)
  set toolSpeedKmh($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasToolSpeedKmh() => $_has(4);
  @$pb.TagNumber(5)
  void clearToolSpeedKmh() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get transitionSpeedKmh => $_getN(5);
  @$pb.TagNumber(6)
  set transitionSpeedKmh($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTransitionSpeedKmh() => $_has(5);
  @$pb.TagNumber(6)
  void clearTransitionSpeedKmh() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get runUpDistanceM => $_getN(6);
  @$pb.TagNumber(7)
  set runUpDistanceM($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRunUpDistanceM() => $_has(6);
  @$pb.TagNumber(7)
  void clearRunUpDistanceM() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get straightenDistanceM => $_getN(7);
  @$pb.TagNumber(8)
  set straightenDistanceM($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasStraightenDistanceM() => $_has(7);
  @$pb.TagNumber(8)
  void clearStraightenDistanceM() => clearField(8);
}

