///
//  Generated code. Do not modify.
//  source: rtc/hh.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'hh.pb.dart' as $55;
import '../google/protobuf/empty.pb.dart' as $43;
export 'hh.pb.dart';

class RobotStateClient extends $grpc.Client {
  static final _$setRobotRequiredState =
      $grpc.ClientMethod<$55.SetRobotRequiredStateRequest, $43.Empty>(
          '/carbon.rtc.RobotState/SetRobotRequiredState',
          ($55.SetRobotRequiredStateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $43.Empty.fromBuffer(value));
  static final _$getNextRequiredState = $grpc.ClientMethod<
          $55.GetRobotRequiredStateRequest, $55.GetRobotRequiredStateResponse>(
      '/carbon.rtc.RobotState/GetNextRequiredState',
      ($55.GetRobotRequiredStateRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $55.GetRobotRequiredStateResponse.fromBuffer(value));
  static final _$robotRequirementStream =
      $grpc.ClientMethod<$55.RobotStatusInfo, $55.RobotRequiredState>(
          '/carbon.rtc.RobotState/RobotRequirementStream',
          ($55.RobotStatusInfo value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $55.RobotRequiredState.fromBuffer(value));

  RobotStateClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$43.Empty> setRobotRequiredState(
      $55.SetRobotRequiredStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setRobotRequiredState, request, options: options);
  }

  $grpc.ResponseFuture<$55.GetRobotRequiredStateResponse> getNextRequiredState(
      $55.GetRobotRequiredStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextRequiredState, request, options: options);
  }

  $grpc.ResponseStream<$55.RobotRequiredState> robotRequirementStream(
      $async.Stream<$55.RobotStatusInfo> request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$robotRequirementStream, request,
        options: options);
  }
}

abstract class RobotStateServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.RobotState';

  RobotStateServiceBase() {
    $addMethod($grpc.ServiceMethod<$55.SetRobotRequiredStateRequest, $43.Empty>(
        'SetRobotRequiredState',
        setRobotRequiredState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $55.SetRobotRequiredStateRequest.fromBuffer(value),
        ($43.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$55.GetRobotRequiredStateRequest,
            $55.GetRobotRequiredStateResponse>(
        'GetNextRequiredState',
        getNextRequiredState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $55.GetRobotRequiredStateRequest.fromBuffer(value),
        ($55.GetRobotRequiredStateResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$55.RobotStatusInfo, $55.RobotRequiredState>(
        'RobotRequirementStream',
        robotRequirementStream,
        true,
        true,
        ($core.List<$core.int> value) => $55.RobotStatusInfo.fromBuffer(value),
        ($55.RobotRequiredState value) => value.writeToBuffer()));
  }

  $async.Future<$43.Empty> setRobotRequiredState_Pre($grpc.ServiceCall call,
      $async.Future<$55.SetRobotRequiredStateRequest> request) async {
    return setRobotRequiredState(call, await request);
  }

  $async.Future<$55.GetRobotRequiredStateResponse> getNextRequiredState_Pre(
      $grpc.ServiceCall call,
      $async.Future<$55.GetRobotRequiredStateRequest> request) async {
    return getNextRequiredState(call, await request);
  }

  $async.Future<$43.Empty> setRobotRequiredState(
      $grpc.ServiceCall call, $55.SetRobotRequiredStateRequest request);
  $async.Future<$55.GetRobotRequiredStateResponse> getNextRequiredState(
      $grpc.ServiceCall call, $55.GetRobotRequiredStateRequest request);
  $async.Stream<$55.RobotRequiredState> robotRequirementStream(
      $grpc.ServiceCall call, $async.Stream<$55.RobotStatusInfo> request);
}
