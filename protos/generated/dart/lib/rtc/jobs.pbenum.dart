///
//  Generated code. Do not modify.
//  source: rtc/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

// ignore_for_file: UNDEFINED_SHOWN_NAME
import 'dart:core' as $core;
import 'package:protobuf/protobuf.dart' as $pb;

class State extends $pb.ProtobufEnum {
  static const State STATE_UNSPECIFIED = State._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'STATE_UNSPECIFIED');
  static const State PENDING = State._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'PENDING');
  static const State READY = State._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'READY');
  static const State IN_PROGRESS = State._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'IN_PROGRESS');
  static const State COMPLETED = State._(4, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'COMPLETED');
  static const State CANCELLED = State._(5, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'CANCELLED');
  static const State PAUSED = State._(6, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'PAUSED');
  static const State FAILED = State._(7, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'FAILED');
  static const State ACKNOWLEDGED = State._(8, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'ACKNOWLEDGED');
  static const State NEW = State._(9, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'NEW');

  static const $core.List<State> values = <State> [
    STATE_UNSPECIFIED,
    PENDING,
    READY,
    IN_PROGRESS,
    COMPLETED,
    CANCELLED,
    PAUSED,
    FAILED,
    ACKNOWLEDGED,
    NEW,
  ];

  static final $core.Map<$core.int, State> _byValue = $pb.ProtobufEnum.initByValue(values);
  static State? valueOf($core.int value) => _byValue[value];

  const State._($core.int v, $core.String n) : super(v, n);
}

class JobType extends $pb.ProtobufEnum {
  static const JobType JOB_TYPE_UNSPECIFIED = JobType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'JOB_TYPE_UNSPECIFIED');
  static const JobType LASER_WEED = JobType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LASER_WEED');
  static const JobType GROUND_PREP = JobType._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'GROUND_PREP');

  static const $core.List<JobType> values = <JobType> [
    JOB_TYPE_UNSPECIFIED,
    LASER_WEED,
    GROUND_PREP,
  ];

  static final $core.Map<$core.int, JobType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static JobType? valueOf($core.int value) => _byValue[value];

  const JobType._($core.int v, $core.String n) : super(v, n);
}

class Objective_ObjectiveType extends $pb.ProtobufEnum {
  static const Objective_ObjectiveType OBJECTIVE_TYPE_UNSPECIFIED = Objective_ObjectiveType._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'OBJECTIVE_TYPE_UNSPECIFIED');
  static const Objective_ObjectiveType LASER_WEED_ROW = Objective_ObjectiveType._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LASER_WEED_ROW');
  static const Objective_ObjectiveType GROUND_PREP_SECTION = Objective_ObjectiveType._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'GROUND_PREP_SECTION');

  static const $core.List<Objective_ObjectiveType> values = <Objective_ObjectiveType> [
    OBJECTIVE_TYPE_UNSPECIFIED,
    LASER_WEED_ROW,
    GROUND_PREP_SECTION,
  ];

  static final $core.Map<$core.int, Objective_ObjectiveType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static Objective_ObjectiveType? valueOf($core.int value) => _byValue[value];

  const Objective_ObjectiveType._($core.int v, $core.String n) : super(v, n);
}

class TractorState_Gear extends $pb.ProtobufEnum {
  static const TractorState_Gear GEAR_UNSPECIFIED = TractorState_Gear._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'GEAR_UNSPECIFIED');
  static const TractorState_Gear PARK = TractorState_Gear._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'PARK');
  static const TractorState_Gear REVERSE = TractorState_Gear._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'REVERSE');
  static const TractorState_Gear NEUTRAL = TractorState_Gear._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'NEUTRAL');
  static const TractorState_Gear FORWARD = TractorState_Gear._(4, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'FORWARD');
  static const TractorState_Gear POWERZERO = TractorState_Gear._(5, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'POWERZERO');

  static const $core.List<TractorState_Gear> values = <TractorState_Gear> [
    GEAR_UNSPECIFIED,
    PARK,
    REVERSE,
    NEUTRAL,
    FORWARD,
    POWERZERO,
  ];

  static final $core.Map<$core.int, TractorState_Gear> _byValue = $pb.ProtobufEnum.initByValue(values);
  static TractorState_Gear? valueOf($core.int value) => _byValue[value];

  const TractorState_Gear._($core.int v, $core.String n) : super(v, n);
}

class HitchState_HitchCommand extends $pb.ProtobufEnum {
  static const HitchState_HitchCommand HITCH_COMMAND_UNSPECIFIED = HitchState_HitchCommand._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'HITCH_COMMAND_UNSPECIFIED');
  static const HitchState_HitchCommand RAISED = HitchState_HitchCommand._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'RAISED');
  static const HitchState_HitchCommand LOWERED = HitchState_HitchCommand._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'LOWERED');

  static const $core.List<HitchState_HitchCommand> values = <HitchState_HitchCommand> [
    HITCH_COMMAND_UNSPECIFIED,
    RAISED,
    LOWERED,
  ];

  static final $core.Map<$core.int, HitchState_HitchCommand> _byValue = $pb.ProtobufEnum.initByValue(values);
  static HitchState_HitchCommand? valueOf($core.int value) => _byValue[value];

  const HitchState_HitchCommand._($core.int v, $core.String n) : super(v, n);
}

class Intervention_InterventionCause extends $pb.ProtobufEnum {
  static const Intervention_InterventionCause INTERVENTION_CAUSE_UNSPECIFIED = Intervention_InterventionCause._(0, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'INTERVENTION_CAUSE_UNSPECIFIED');
  static const Intervention_InterventionCause SENSOR_TRIGGERED = Intervention_InterventionCause._(1, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'SENSOR_TRIGGERED');
  static const Intervention_InterventionCause SAFETY_DRIVER_ACTION = Intervention_InterventionCause._(2, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'SAFETY_DRIVER_ACTION');
  static const Intervention_InterventionCause TRACTOR_REQUEST = Intervention_InterventionCause._(3, const $core.bool.fromEnvironment('protobuf.omit_enum_names') ? '' : 'TRACTOR_REQUEST');

  static const $core.List<Intervention_InterventionCause> values = <Intervention_InterventionCause> [
    INTERVENTION_CAUSE_UNSPECIFIED,
    SENSOR_TRIGGERED,
    SAFETY_DRIVER_ACTION,
    TRACTOR_REQUEST,
  ];

  static final $core.Map<$core.int, Intervention_InterventionCause> _byValue = $pb.ProtobufEnum.initByValue(values);
  static Intervention_InterventionCause? valueOf($core.int value) => _byValue[value];

  const Intervention_InterventionCause._($core.int v, $core.String n) : super(v, n);
}

