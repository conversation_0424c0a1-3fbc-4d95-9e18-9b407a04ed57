///
//  Generated code. Do not modify.
//  source: rtc/web_socket.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'location_history.pb.dart' as $57;
import 'device_location_history.pb.dart' as $54;

import 'web_socket.pbenum.dart';

export 'web_socket.pbenum.dart';

class SubscriptionRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SubscriptionRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..e<SubscriptionRequest_Type>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: SubscriptionRequest_Type.TYPE_UNSPECIFIED, valueOf: SubscriptionRequest_Type.valueOf, enumValues: SubscriptionRequest_Type.values)
    ..aOM<DeviceSelector>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'device', subBuilder: DeviceSelector.create)
    ..hasRequiredFields = false
  ;

  SubscriptionRequest._() : super();
  factory SubscriptionRequest({
    SubscriptionRequest_Type? type,
    DeviceSelector? device,
  }) {
    final _result = create();
    if (type != null) {
      _result.type = type;
    }
    if (device != null) {
      _result.device = device;
    }
    return _result;
  }
  factory SubscriptionRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SubscriptionRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SubscriptionRequest clone() => SubscriptionRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SubscriptionRequest copyWith(void Function(SubscriptionRequest) updates) => super.copyWith((message) => updates(message as SubscriptionRequest)) as SubscriptionRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SubscriptionRequest create() => SubscriptionRequest._();
  SubscriptionRequest createEmptyInstance() => create();
  static $pb.PbList<SubscriptionRequest> createRepeated() => $pb.PbList<SubscriptionRequest>();
  @$core.pragma('dart2js:noInline')
  static SubscriptionRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SubscriptionRequest>(create);
  static SubscriptionRequest? _defaultInstance;

  @$pb.TagNumber(1)
  SubscriptionRequest_Type get type => $_getN(0);
  @$pb.TagNumber(1)
  set type(SubscriptionRequest_Type v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasType() => $_has(0);
  @$pb.TagNumber(1)
  void clearType() => clearField(1);

  @$pb.TagNumber(2)
  DeviceSelector get device => $_getN(1);
  @$pb.TagNumber(2)
  set device(DeviceSelector v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasDevice() => $_has(1);
  @$pb.TagNumber(2)
  void clearDevice() => clearField(2);
  @$pb.TagNumber(2)
  DeviceSelector ensureDevice() => $_ensure(1);
}

enum DeviceSelector_Identifier {
  serial, 
  deviceId, 
  notSet
}

class DeviceSelector extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, DeviceSelector_Identifier> _DeviceSelector_IdentifierByTag = {
    2 : DeviceSelector_Identifier.serial,
    3 : DeviceSelector_Identifier.deviceId,
    0 : DeviceSelector_Identifier.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeviceSelector', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [2, 3])
    ..e<SubscriptionDeviceType>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: SubscriptionDeviceType.SUBSCRIPTION_DEVICE_TYPE_UNSPECIFIED, valueOf: SubscriptionDeviceType.valueOf, enumValues: SubscriptionDeviceType.values)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceId')
    ..hasRequiredFields = false
  ;

  DeviceSelector._() : super();
  factory DeviceSelector({
    SubscriptionDeviceType? type,
    $core.String? serial,
    $core.String? deviceId,
  }) {
    final _result = create();
    if (type != null) {
      _result.type = type;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    if (deviceId != null) {
      _result.deviceId = deviceId;
    }
    return _result;
  }
  factory DeviceSelector.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeviceSelector.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeviceSelector clone() => DeviceSelector()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeviceSelector copyWith(void Function(DeviceSelector) updates) => super.copyWith((message) => updates(message as DeviceSelector)) as DeviceSelector; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeviceSelector create() => DeviceSelector._();
  DeviceSelector createEmptyInstance() => create();
  static $pb.PbList<DeviceSelector> createRepeated() => $pb.PbList<DeviceSelector>();
  @$core.pragma('dart2js:noInline')
  static DeviceSelector getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeviceSelector>(create);
  static DeviceSelector? _defaultInstance;

  DeviceSelector_Identifier whichIdentifier() => _DeviceSelector_IdentifierByTag[$_whichOneof(0)]!;
  void clearIdentifier() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SubscriptionDeviceType get type => $_getN(0);
  @$pb.TagNumber(1)
  set type(SubscriptionDeviceType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasType() => $_has(0);
  @$pb.TagNumber(1)
  void clearType() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get serial => $_getSZ(1);
  @$pb.TagNumber(2)
  set serial($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSerial() => $_has(1);
  @$pb.TagNumber(2)
  void clearSerial() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(3)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeviceId() => clearField(3);
}

enum SubscriptionMessage_Update {
  location, 
  notSet
}

class SubscriptionMessage extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, SubscriptionMessage_Update> _SubscriptionMessage_UpdateByTag = {
    2 : SubscriptionMessage_Update.location,
    0 : SubscriptionMessage_Update.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SubscriptionMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [2])
    ..e<SubscriptionMessage_Type>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: SubscriptionMessage_Type.TYPE_UNSPECIFIED, valueOf: SubscriptionMessage_Type.valueOf, enumValues: SubscriptionMessage_Type.values)
    ..aOM<LocationUpdate>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: LocationUpdate.create)
    ..hasRequiredFields = false
  ;

  SubscriptionMessage._() : super();
  factory SubscriptionMessage({
    SubscriptionMessage_Type? type,
    LocationUpdate? location,
  }) {
    final _result = create();
    if (type != null) {
      _result.type = type;
    }
    if (location != null) {
      _result.location = location;
    }
    return _result;
  }
  factory SubscriptionMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SubscriptionMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SubscriptionMessage clone() => SubscriptionMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SubscriptionMessage copyWith(void Function(SubscriptionMessage) updates) => super.copyWith((message) => updates(message as SubscriptionMessage)) as SubscriptionMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SubscriptionMessage create() => SubscriptionMessage._();
  SubscriptionMessage createEmptyInstance() => create();
  static $pb.PbList<SubscriptionMessage> createRepeated() => $pb.PbList<SubscriptionMessage>();
  @$core.pragma('dart2js:noInline')
  static SubscriptionMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SubscriptionMessage>(create);
  static SubscriptionMessage? _defaultInstance;

  SubscriptionMessage_Update whichUpdate() => _SubscriptionMessage_UpdateByTag[$_whichOneof(0)]!;
  void clearUpdate() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SubscriptionMessage_Type get type => $_getN(0);
  @$pb.TagNumber(1)
  set type(SubscriptionMessage_Type v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasType() => $_has(0);
  @$pb.TagNumber(1)
  void clearType() => clearField(1);

  @$pb.TagNumber(2)
  LocationUpdate get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(LocationUpdate v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  LocationUpdate ensureLocation() => $_ensure(1);
}

enum LocationUpdate_Record {
  robotRec, 
  deviceRec, 
  notSet
}

class LocationUpdate extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, LocationUpdate_Record> _LocationUpdate_RecordByTag = {
    3 : LocationUpdate_Record.robotRec,
    4 : LocationUpdate_Record.deviceRec,
    0 : LocationUpdate_Record.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LocationUpdate', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [3, 4])
    ..aOM<DeviceSelector>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'device', subBuilder: DeviceSelector.create)
    ..aOM<$57.LocationHistoryRecord>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotRec', subBuilder: $57.LocationHistoryRecord.create)
    ..aOM<$54.DeviceLocationHistoryRecord>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deviceRec', subBuilder: $54.DeviceLocationHistoryRecord.create)
    ..hasRequiredFields = false
  ;

  LocationUpdate._() : super();
  factory LocationUpdate({
    DeviceSelector? device,
    $57.LocationHistoryRecord? robotRec,
    $54.DeviceLocationHistoryRecord? deviceRec,
  }) {
    final _result = create();
    if (device != null) {
      _result.device = device;
    }
    if (robotRec != null) {
      _result.robotRec = robotRec;
    }
    if (deviceRec != null) {
      _result.deviceRec = deviceRec;
    }
    return _result;
  }
  factory LocationUpdate.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LocationUpdate.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LocationUpdate clone() => LocationUpdate()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LocationUpdate copyWith(void Function(LocationUpdate) updates) => super.copyWith((message) => updates(message as LocationUpdate)) as LocationUpdate; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LocationUpdate create() => LocationUpdate._();
  LocationUpdate createEmptyInstance() => create();
  static $pb.PbList<LocationUpdate> createRepeated() => $pb.PbList<LocationUpdate>();
  @$core.pragma('dart2js:noInline')
  static LocationUpdate getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LocationUpdate>(create);
  static LocationUpdate? _defaultInstance;

  LocationUpdate_Record whichRecord() => _LocationUpdate_RecordByTag[$_whichOneof(0)]!;
  void clearRecord() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  DeviceSelector get device => $_getN(0);
  @$pb.TagNumber(1)
  set device(DeviceSelector v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevice() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevice() => clearField(1);
  @$pb.TagNumber(1)
  DeviceSelector ensureDevice() => $_ensure(0);

  @$pb.TagNumber(3)
  $57.LocationHistoryRecord get robotRec => $_getN(1);
  @$pb.TagNumber(3)
  set robotRec($57.LocationHistoryRecord v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotRec() => $_has(1);
  @$pb.TagNumber(3)
  void clearRobotRec() => clearField(3);
  @$pb.TagNumber(3)
  $57.LocationHistoryRecord ensureRobotRec() => $_ensure(1);

  @$pb.TagNumber(4)
  $54.DeviceLocationHistoryRecord get deviceRec => $_getN(2);
  @$pb.TagNumber(4)
  set deviceRec($54.DeviceLocationHistoryRecord v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceRec() => $_has(2);
  @$pb.TagNumber(4)
  void clearDeviceRec() => clearField(4);
  @$pb.TagNumber(4)
  $54.DeviceLocationHistoryRecord ensureDeviceRec() => $_ensure(2);
}

class WebSocketAuthenticateRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WebSocketAuthenticateRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'token')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'audience')
    ..hasRequiredFields = false
  ;

  WebSocketAuthenticateRequest._() : super();
  factory WebSocketAuthenticateRequest({
    $core.String? token,
    $core.String? audience,
  }) {
    final _result = create();
    if (token != null) {
      _result.token = token;
    }
    if (audience != null) {
      _result.audience = audience;
    }
    return _result;
  }
  factory WebSocketAuthenticateRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WebSocketAuthenticateRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WebSocketAuthenticateRequest clone() => WebSocketAuthenticateRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WebSocketAuthenticateRequest copyWith(void Function(WebSocketAuthenticateRequest) updates) => super.copyWith((message) => updates(message as WebSocketAuthenticateRequest)) as WebSocketAuthenticateRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WebSocketAuthenticateRequest create() => WebSocketAuthenticateRequest._();
  WebSocketAuthenticateRequest createEmptyInstance() => create();
  static $pb.PbList<WebSocketAuthenticateRequest> createRepeated() => $pb.PbList<WebSocketAuthenticateRequest>();
  @$core.pragma('dart2js:noInline')
  static WebSocketAuthenticateRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WebSocketAuthenticateRequest>(create);
  static WebSocketAuthenticateRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get token => $_getSZ(0);
  @$pb.TagNumber(1)
  set token($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get audience => $_getSZ(1);
  @$pb.TagNumber(2)
  set audience($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAudience() => $_has(1);
  @$pb.TagNumber(2)
  void clearAudience() => clearField(2);
}

class WebSocketResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WebSocketResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..e<WebSocketStatus>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: WebSocketStatus.WEB_SOCKET_STATUS_UNSPECIFIED, valueOf: WebSocketStatus.valueOf, enumValues: WebSocketStatus.values)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'message')
    ..hasRequiredFields = false
  ;

  WebSocketResponse._() : super();
  factory WebSocketResponse({
    WebSocketStatus? status,
    $core.String? message,
  }) {
    final _result = create();
    if (status != null) {
      _result.status = status;
    }
    if (message != null) {
      _result.message = message;
    }
    return _result;
  }
  factory WebSocketResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WebSocketResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WebSocketResponse clone() => WebSocketResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WebSocketResponse copyWith(void Function(WebSocketResponse) updates) => super.copyWith((message) => updates(message as WebSocketResponse)) as WebSocketResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WebSocketResponse create() => WebSocketResponse._();
  WebSocketResponse createEmptyInstance() => create();
  static $pb.PbList<WebSocketResponse> createRepeated() => $pb.PbList<WebSocketResponse>();
  @$core.pragma('dart2js:noInline')
  static WebSocketResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WebSocketResponse>(create);
  static WebSocketResponse? _defaultInstance;

  @$pb.TagNumber(1)
  WebSocketStatus get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(WebSocketStatus v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get message => $_getSZ(1);
  @$pb.TagNumber(2)
  set message($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMessage() => $_has(1);
  @$pb.TagNumber(2)
  void clearMessage() => clearField(2);
}

