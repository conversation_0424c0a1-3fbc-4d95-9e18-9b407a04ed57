///
//  Generated code. Do not modify.
//  source: rtc/device_location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'device_location_history.pb.dart' as $54;
import '../google/protobuf/empty.pb.dart' as $43;
export 'device_location_history.pb.dart';

class DeviceLocationClient extends $grpc.Client {
  static final _$logLocationHistory =
      $grpc.ClientMethod<$54.LogDeviceLocationHistoryRequest, $43.Empty>(
          '/carbon.rtc.DeviceLocation/LogLocationHistory',
          ($54.LogDeviceLocationHistoryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $43.Empty.fromBuffer(value));
  static final _$listDevices =
      $grpc.ClientMethod<$54.ListDevicesRequest, $54.ListDevicesResponse>(
          '/carbon.rtc.DeviceLocation/ListDevices',
          ($54.ListDevicesRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $54.ListDevicesResponse.fromBuffer(value));
  static final _$listLocationHistory = $grpc.ClientMethod<
          $54.ListDeviceLocationHistoryRequest,
          $54.ListDeviceLocationHistoryResponse>(
      '/carbon.rtc.DeviceLocation/ListLocationHistory',
      ($54.ListDeviceLocationHistoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $54.ListDeviceLocationHistoryResponse.fromBuffer(value));

  DeviceLocationClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$43.Empty> logLocationHistory(
      $54.LogDeviceLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$logLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$54.ListDevicesResponse> listDevices(
      $54.ListDevicesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listDevices, request, options: options);
  }

  $grpc.ResponseFuture<$54.ListDeviceLocationHistoryResponse>
      listLocationHistory($54.ListDeviceLocationHistoryRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listLocationHistory, request, options: options);
  }
}

abstract class DeviceLocationServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.DeviceLocation';

  DeviceLocationServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$54.LogDeviceLocationHistoryRequest, $43.Empty>(
            'LogLocationHistory',
            logLocationHistory_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $54.LogDeviceLocationHistoryRequest.fromBuffer(value),
            ($43.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$54.ListDevicesRequest, $54.ListDevicesResponse>(
            'ListDevices',
            listDevices_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $54.ListDevicesRequest.fromBuffer(value),
            ($54.ListDevicesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$54.ListDeviceLocationHistoryRequest,
            $54.ListDeviceLocationHistoryResponse>(
        'ListLocationHistory',
        listLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $54.ListDeviceLocationHistoryRequest.fromBuffer(value),
        ($54.ListDeviceLocationHistoryResponse value) =>
            value.writeToBuffer()));
  }

  $async.Future<$43.Empty> logLocationHistory_Pre($grpc.ServiceCall call,
      $async.Future<$54.LogDeviceLocationHistoryRequest> request) async {
    return logLocationHistory(call, await request);
  }

  $async.Future<$54.ListDevicesResponse> listDevices_Pre($grpc.ServiceCall call,
      $async.Future<$54.ListDevicesRequest> request) async {
    return listDevices(call, await request);
  }

  $async.Future<$54.ListDeviceLocationHistoryResponse> listLocationHistory_Pre(
      $grpc.ServiceCall call,
      $async.Future<$54.ListDeviceLocationHistoryRequest> request) async {
    return listLocationHistory(call, await request);
  }

  $async.Future<$43.Empty> logLocationHistory(
      $grpc.ServiceCall call, $54.LogDeviceLocationHistoryRequest request);
  $async.Future<$54.ListDevicesResponse> listDevices(
      $grpc.ServiceCall call, $54.ListDevicesRequest request);
  $async.Future<$54.ListDeviceLocationHistoryResponse> listLocationHistory(
      $grpc.ServiceCall call, $54.ListDeviceLocationHistoryRequest request);
}
