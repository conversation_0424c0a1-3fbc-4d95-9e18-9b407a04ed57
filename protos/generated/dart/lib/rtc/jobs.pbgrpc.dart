///
//  Generated code. Do not modify.
//  source: rtc/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'jobs.pb.dart' as $56;
export 'jobs.pb.dart';

class JobServiceClient extends $grpc.Client {
  static final _$createIntervention = $grpc.ClientMethod<
          $56.CreateInterventionRequest, $56.CreateInterventionResponse>(
      '/carbon.rtc.JobService/CreateIntervention',
      ($56.CreateInterventionRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $56.CreateInterventionResponse.fromBuffer(value));
  static final _$getActiveTask =
      $grpc.ClientMethod<$56.GetActiveTaskRequest, $56.GetActiveTaskResponse>(
          '/carbon.rtc.JobService/GetActiveTask',
          ($56.GetActiveTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $56.GetActiveTaskResponse.fromBuffer(value));
  static final _$getTask =
      $grpc.ClientMethod<$56.GetTaskRequest, $56.GetTaskResponse>(
          '/carbon.rtc.JobService/GetTask',
          ($56.GetTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $56.GetTaskResponse.fromBuffer(value));
  static final _$getNextActiveObjective = $grpc.ClientMethod<
          $56.GetNextActiveObjectiveRequest,
          $56.GetNextActiveObjectiveResponse>(
      '/carbon.rtc.JobService/GetNextActiveObjective',
      ($56.GetNextActiveObjectiveRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $56.GetNextActiveObjectiveResponse.fromBuffer(value));
  static final _$updateTask =
      $grpc.ClientMethod<$56.UpdateTaskRequest, $56.UpdateTaskResponse>(
          '/carbon.rtc.JobService/UpdateTask',
          ($56.UpdateTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $56.UpdateTaskResponse.fromBuffer(value));

  JobServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$56.CreateInterventionResponse> createIntervention(
      $56.CreateInterventionRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$createIntervention, request, options: options);
  }

  $grpc.ResponseFuture<$56.GetActiveTaskResponse> getActiveTask(
      $56.GetActiveTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getActiveTask, request, options: options);
  }

  $grpc.ResponseFuture<$56.GetTaskResponse> getTask($56.GetTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTask, request, options: options);
  }

  $grpc.ResponseFuture<$56.GetNextActiveObjectiveResponse>
      getNextActiveObjective($56.GetNextActiveObjectiveRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextActiveObjective, request,
        options: options);
  }

  $grpc.ResponseFuture<$56.UpdateTaskResponse> updateTask(
      $56.UpdateTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$updateTask, request, options: options);
  }
}

abstract class JobServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.JobService';

  JobServiceBase() {
    $addMethod($grpc.ServiceMethod<$56.CreateInterventionRequest,
            $56.CreateInterventionResponse>(
        'CreateIntervention',
        createIntervention_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $56.CreateInterventionRequest.fromBuffer(value),
        ($56.CreateInterventionResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$56.GetActiveTaskRequest,
            $56.GetActiveTaskResponse>(
        'GetActiveTask',
        getActiveTask_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $56.GetActiveTaskRequest.fromBuffer(value),
        ($56.GetActiveTaskResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$56.GetTaskRequest, $56.GetTaskResponse>(
        'GetTask',
        getTask_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $56.GetTaskRequest.fromBuffer(value),
        ($56.GetTaskResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$56.GetNextActiveObjectiveRequest,
            $56.GetNextActiveObjectiveResponse>(
        'GetNextActiveObjective',
        getNextActiveObjective_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $56.GetNextActiveObjectiveRequest.fromBuffer(value),
        ($56.GetNextActiveObjectiveResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$56.UpdateTaskRequest, $56.UpdateTaskResponse>(
            'UpdateTask',
            updateTask_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $56.UpdateTaskRequest.fromBuffer(value),
            ($56.UpdateTaskResponse value) => value.writeToBuffer()));
  }

  $async.Future<$56.CreateInterventionResponse> createIntervention_Pre(
      $grpc.ServiceCall call,
      $async.Future<$56.CreateInterventionRequest> request) async {
    return createIntervention(call, await request);
  }

  $async.Future<$56.GetActiveTaskResponse> getActiveTask_Pre(
      $grpc.ServiceCall call,
      $async.Future<$56.GetActiveTaskRequest> request) async {
    return getActiveTask(call, await request);
  }

  $async.Future<$56.GetTaskResponse> getTask_Pre(
      $grpc.ServiceCall call, $async.Future<$56.GetTaskRequest> request) async {
    return getTask(call, await request);
  }

  $async.Future<$56.GetNextActiveObjectiveResponse> getNextActiveObjective_Pre(
      $grpc.ServiceCall call,
      $async.Future<$56.GetNextActiveObjectiveRequest> request) async {
    return getNextActiveObjective(call, await request);
  }

  $async.Future<$56.UpdateTaskResponse> updateTask_Pre($grpc.ServiceCall call,
      $async.Future<$56.UpdateTaskRequest> request) async {
    return updateTask(call, await request);
  }

  $async.Future<$56.CreateInterventionResponse> createIntervention(
      $grpc.ServiceCall call, $56.CreateInterventionRequest request);
  $async.Future<$56.GetActiveTaskResponse> getActiveTask(
      $grpc.ServiceCall call, $56.GetActiveTaskRequest request);
  $async.Future<$56.GetTaskResponse> getTask(
      $grpc.ServiceCall call, $56.GetTaskRequest request);
  $async.Future<$56.GetNextActiveObjectiveResponse> getNextActiveObjective(
      $grpc.ServiceCall call, $56.GetNextActiveObjectiveRequest request);
  $async.Future<$56.UpdateTaskResponse> updateTask(
      $grpc.ServiceCall call, $56.UpdateTaskRequest request);
}
