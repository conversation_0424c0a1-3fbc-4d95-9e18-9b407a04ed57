///
//  Generated code. Do not modify.
//  source: rtc/path_plan.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'path_plan.pbenum.dart';

export 'path_plan.pbenum.dart';

class PathPlanConfiguration extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PathPlanConfiguration', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'doHeadlandFirst')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowHeadingDeg', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'headlandWidthM', $pb.PbFieldType.OF)
    ..e<Direction>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'turnDirection', $pb.PbFieldType.OE, defaultOrMaker: Direction.DIRECTION_UNSPECIFIED, valueOf: Direction.valueOf, enumValues: Direction.values)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numHeadlandPasses', $pb.PbFieldType.O3)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'combinedTurnRadiusM', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  PathPlanConfiguration._() : super();
  factory PathPlanConfiguration({
    $core.bool? doHeadlandFirst,
    $core.double? rowHeadingDeg,
    $core.double? headlandWidthM,
    Direction? turnDirection,
    $core.int? numHeadlandPasses,
    $core.double? combinedTurnRadiusM,
  }) {
    final _result = create();
    if (doHeadlandFirst != null) {
      _result.doHeadlandFirst = doHeadlandFirst;
    }
    if (rowHeadingDeg != null) {
      _result.rowHeadingDeg = rowHeadingDeg;
    }
    if (headlandWidthM != null) {
      _result.headlandWidthM = headlandWidthM;
    }
    if (turnDirection != null) {
      _result.turnDirection = turnDirection;
    }
    if (numHeadlandPasses != null) {
      _result.numHeadlandPasses = numHeadlandPasses;
    }
    if (combinedTurnRadiusM != null) {
      _result.combinedTurnRadiusM = combinedTurnRadiusM;
    }
    return _result;
  }
  factory PathPlanConfiguration.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PathPlanConfiguration.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PathPlanConfiguration clone() => PathPlanConfiguration()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PathPlanConfiguration copyWith(void Function(PathPlanConfiguration) updates) => super.copyWith((message) => updates(message as PathPlanConfiguration)) as PathPlanConfiguration; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PathPlanConfiguration create() => PathPlanConfiguration._();
  PathPlanConfiguration createEmptyInstance() => create();
  static $pb.PbList<PathPlanConfiguration> createRepeated() => $pb.PbList<PathPlanConfiguration>();
  @$core.pragma('dart2js:noInline')
  static PathPlanConfiguration getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PathPlanConfiguration>(create);
  static PathPlanConfiguration? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get doHeadlandFirst => $_getBF(0);
  @$pb.TagNumber(1)
  set doHeadlandFirst($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDoHeadlandFirst() => $_has(0);
  @$pb.TagNumber(1)
  void clearDoHeadlandFirst() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get rowHeadingDeg => $_getN(1);
  @$pb.TagNumber(2)
  set rowHeadingDeg($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowHeadingDeg() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowHeadingDeg() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get headlandWidthM => $_getN(2);
  @$pb.TagNumber(3)
  set headlandWidthM($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasHeadlandWidthM() => $_has(2);
  @$pb.TagNumber(3)
  void clearHeadlandWidthM() => clearField(3);

  @$pb.TagNumber(4)
  Direction get turnDirection => $_getN(3);
  @$pb.TagNumber(4)
  set turnDirection(Direction v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasTurnDirection() => $_has(3);
  @$pb.TagNumber(4)
  void clearTurnDirection() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get numHeadlandPasses => $_getIZ(4);
  @$pb.TagNumber(5)
  set numHeadlandPasses($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasNumHeadlandPasses() => $_has(4);
  @$pb.TagNumber(5)
  void clearNumHeadlandPasses() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get combinedTurnRadiusM => $_getN(5);
  @$pb.TagNumber(6)
  set combinedTurnRadiusM($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCombinedTurnRadiusM() => $_has(5);
  @$pb.TagNumber(6)
  void clearCombinedTurnRadiusM() => clearField(6);
}

