///
//  Generated code. Do not modify.
//  source: rtc/path_plan.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use directionDescriptor instead')
const Direction$json = const {
  '1': 'Direction',
  '2': const [
    const {'1': 'DIRECTION_UNSPECIFIED', '2': 0},
    const {'1': 'CW', '2': 1},
    const {'1': 'CCW', '2': 2},
  ],
};

/// Descriptor for `Direction`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List directionDescriptor = $convert.base64Decode('CglEaXJlY3Rpb24SGQoVRElSRUNUSU9OX1VOU1BFQ0lGSUVEEAASBgoCQ1cQARIHCgNDQ1cQAg==');
@$core.Deprecated('Use pathPlanConfigurationDescriptor instead')
const PathPlanConfiguration$json = const {
  '1': 'PathPlanConfiguration',
  '2': const [
    const {'1': 'do_headland_first', '3': 1, '4': 1, '5': 8, '10': 'doHeadlandFirst'},
    const {'1': 'row_heading_deg', '3': 2, '4': 1, '5': 2, '10': 'rowHeadingDeg'},
    const {'1': 'headland_width_m', '3': 3, '4': 1, '5': 2, '10': 'headlandWidthM'},
    const {'1': 'turn_direction', '3': 4, '4': 1, '5': 14, '6': '.carbon.rtc.Direction', '10': 'turnDirection'},
    const {'1': 'num_headland_passes', '3': 5, '4': 1, '5': 5, '10': 'numHeadlandPasses'},
    const {'1': 'combined_turn_radius_m', '3': 6, '4': 1, '5': 2, '10': 'combinedTurnRadiusM'},
  ],
};

/// Descriptor for `PathPlanConfiguration`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pathPlanConfigurationDescriptor = $convert.base64Decode('ChVQYXRoUGxhbkNvbmZpZ3VyYXRpb24SKgoRZG9faGVhZGxhbmRfZmlyc3QYASABKAhSD2RvSGVhZGxhbmRGaXJzdBImCg9yb3dfaGVhZGluZ19kZWcYAiABKAJSDXJvd0hlYWRpbmdEZWcSKAoQaGVhZGxhbmRfd2lkdGhfbRgDIAEoAlIOaGVhZGxhbmRXaWR0aE0SPAoOdHVybl9kaXJlY3Rpb24YBCABKA4yFS5jYXJib24ucnRjLkRpcmVjdGlvblINdHVybkRpcmVjdGlvbhIuChNudW1faGVhZGxhbmRfcGFzc2VzGAUgASgFUhFudW1IZWFkbGFuZFBhc3NlcxIzChZjb21iaW5lZF90dXJuX3JhZGl1c19tGAYgASgCUhNjb21iaW5lZFR1cm5SYWRpdXNN');
