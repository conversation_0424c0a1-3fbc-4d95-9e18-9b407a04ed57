///
//  Generated code. Do not modify.
//  source: lib/common/image/cam.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../buffer/buffer.pb.dart' as $74;

class Size extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Size', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'lib.common.image'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.O3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  Size._() : super();
  factory Size({
    $core.int? width,
    $core.int? height,
  }) {
    final _result = create();
    if (width != null) {
      _result.width = width;
    }
    if (height != null) {
      _result.height = height;
    }
    return _result;
  }
  factory Size.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Size.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Size clone() => Size()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Size copyWith(void Function(Size) updates) => super.copyWith((message) => updates(message as Size)) as Size; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Size create() => Size._();
  Size createEmptyInstance() => create();
  static $pb.PbList<Size> createRepeated() => $pb.PbList<Size>();
  @$core.pragma('dart2js:noInline')
  static Size getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Size>(create);
  static Size? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get width => $_getIZ(0);
  @$pb.TagNumber(1)
  set width($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWidth() => $_has(0);
  @$pb.TagNumber(1)
  void clearWidth() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get height => $_getIZ(1);
  @$pb.TagNumber(2)
  set height($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHeight() => $_has(1);
  @$pb.TagNumber(2)
  void clearHeight() => clearField(2);
}

class CamImageProto extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CamImageProto', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'lib.common.image'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'devicePath')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cameraId')
    ..a<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOM<Size>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'size', subBuilder: Size.create)
    ..aOM<$74.BufferProto>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bytes', subBuilder: $74.BufferProto.create)
    ..aOM<$74.BufferProto>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'depthBytes', subBuilder: $74.BufferProto.create)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ppi', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  CamImageProto._() : super();
  factory CamImageProto({
    $core.String? devicePath,
    $core.String? cameraId,
    $fixnum.Int64? timestampMs,
    Size? size,
    $74.BufferProto? bytes,
    $74.BufferProto? depthBytes,
    $core.double? ppi,
  }) {
    final _result = create();
    if (devicePath != null) {
      _result.devicePath = devicePath;
    }
    if (cameraId != null) {
      _result.cameraId = cameraId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (size != null) {
      _result.size = size;
    }
    if (bytes != null) {
      _result.bytes = bytes;
    }
    if (depthBytes != null) {
      _result.depthBytes = depthBytes;
    }
    if (ppi != null) {
      _result.ppi = ppi;
    }
    return _result;
  }
  factory CamImageProto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CamImageProto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CamImageProto clone() => CamImageProto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CamImageProto copyWith(void Function(CamImageProto) updates) => super.copyWith((message) => updates(message as CamImageProto)) as CamImageProto; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CamImageProto create() => CamImageProto._();
  CamImageProto createEmptyInstance() => create();
  static $pb.PbList<CamImageProto> createRepeated() => $pb.PbList<CamImageProto>();
  @$core.pragma('dart2js:noInline')
  static CamImageProto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CamImageProto>(create);
  static CamImageProto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get devicePath => $_getSZ(0);
  @$pb.TagNumber(1)
  set devicePath($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevicePath() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevicePath() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cameraId => $_getSZ(1);
  @$pb.TagNumber(2)
  set cameraId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCameraId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCameraId() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timestampMs => $_getI64(2);
  @$pb.TagNumber(3)
  set timestampMs($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimestampMs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimestampMs() => clearField(3);

  @$pb.TagNumber(4)
  Size get size => $_getN(3);
  @$pb.TagNumber(4)
  set size(Size v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasSize() => $_has(3);
  @$pb.TagNumber(4)
  void clearSize() => clearField(4);
  @$pb.TagNumber(4)
  Size ensureSize() => $_ensure(3);

  @$pb.TagNumber(5)
  $74.BufferProto get bytes => $_getN(4);
  @$pb.TagNumber(5)
  set bytes($74.BufferProto v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasBytes() => $_has(4);
  @$pb.TagNumber(5)
  void clearBytes() => clearField(5);
  @$pb.TagNumber(5)
  $74.BufferProto ensureBytes() => $_ensure(4);

  @$pb.TagNumber(6)
  $74.BufferProto get depthBytes => $_getN(5);
  @$pb.TagNumber(6)
  set depthBytes($74.BufferProto v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasDepthBytes() => $_has(5);
  @$pb.TagNumber(6)
  void clearDepthBytes() => clearField(6);
  @$pb.TagNumber(6)
  $74.BufferProto ensureDepthBytes() => $_ensure(5);

  @$pb.TagNumber(7)
  $core.double get ppi => $_getN(6);
  @$pb.TagNumber(7)
  set ppi($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasPpi() => $_has(6);
  @$pb.TagNumber(7)
  void clearPpi() => clearField(7);
}

