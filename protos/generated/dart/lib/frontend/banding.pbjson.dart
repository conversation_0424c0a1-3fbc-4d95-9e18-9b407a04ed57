///
//  Generated code. Do not modify.
//  source: frontend/banding.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use visualizationTypeToIncludeDescriptor instead')
const VisualizationTypeToInclude$json = const {
  '1': 'VisualizationTypeToInclude',
  '2': const [
    const {'1': 'DUPLICATE_WEED', '2': 0},
    const {'1': 'DUPLICATE_CROP', '2': 1},
    const {'1': 'KILLED_WEED', '2': 2},
    const {'1': 'KILLED_CROP', '2': 3},
    const {'1': 'KILLING_WEED', '2': 4},
    const {'1': 'IGNORED_WEED', '2': 5},
    const {'1': 'KILLING_CROP', '2': 6},
    const {'1': 'ERROR_WEED', '2': 7},
    const {'1': 'ERROR_CROP', '2': 8},
    const {'1': 'IGNORED_CROP', '2': 9},
    const {'1': 'WEED', '2': 10},
    const {'1': 'CROP', '2': 11},
    const {'1': 'CROP_RADIUS', '2': 12},
    const {'1': 'CROP_KEPT', '2': 13},
    const {'1': 'THINNING_BOX', '2': 14},
  ],
};

/// Descriptor for `VisualizationTypeToInclude`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List visualizationTypeToIncludeDescriptor = $convert.base64Decode('ChpWaXN1YWxpemF0aW9uVHlwZVRvSW5jbHVkZRISCg5EVVBMSUNBVEVfV0VFRBAAEhIKDkRVUExJQ0FURV9DUk9QEAESDwoLS0lMTEVEX1dFRUQQAhIPCgtLSUxMRURfQ1JPUBADEhAKDEtJTExJTkdfV0VFRBAEEhAKDElHTk9SRURfV0VFRBAFEhAKDEtJTExJTkdfQ1JPUBAGEg4KCkVSUk9SX1dFRUQQBxIOCgpFUlJPUl9DUk9QEAgSEAoMSUdOT1JFRF9DUk9QEAkSCAoEV0VFRBAKEggKBENST1AQCxIPCgtDUk9QX1JBRElVUxAMEg0KCUNST1BfS0VQVBANEhAKDFRISU5OSU5HX0JPWBAO');
@$core.Deprecated('Use thresholdStateDescriptor instead')
const ThresholdState$json = const {
  '1': 'ThresholdState',
  '2': const [
    const {'1': 'ANY', '2': 0},
    const {'1': 'PASS', '2': 1},
    const {'1': 'FAIL', '2': 2},
  ],
};

/// Descriptor for `ThresholdState`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List thresholdStateDescriptor = $convert.base64Decode('Cg5UaHJlc2hvbGRTdGF0ZRIHCgNBTlkQABIICgRQQVNTEAESCAoERkFJTBAC');
@$core.Deprecated('Use bandingRowDescriptor instead')
const BandingRow$json = const {
  '1': 'BandingRow',
  '2': const [
    const {'1': 'row_id', '3': 1, '4': 1, '5': 5, '10': 'rowId'},
    const {'1': 'bands', '3': 2, '4': 3, '5': 11, '6': '.weed_tracking.BandDefinition', '10': 'bands'},
  ],
};

/// Descriptor for `BandingRow`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandingRowDescriptor = $convert.base64Decode('CgpCYW5kaW5nUm93EhUKBnJvd19pZBgBIAEoBVIFcm93SWQSMwoFYmFuZHMYAiADKAsyHS53ZWVkX3RyYWNraW5nLkJhbmREZWZpbml0aW9uUgViYW5kcw==');
@$core.Deprecated('Use bandingDefDescriptor instead')
const BandingDef$json = const {
  '1': 'BandingDef',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'rows', '3': 2, '4': 3, '5': 11, '6': '.carbon.frontend.banding.BandingRow', '10': 'rows'},
    const {'1': 'uuid', '3': 3, '4': 1, '5': 9, '10': 'uuid'},
  ],
};

/// Descriptor for `BandingDef`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandingDefDescriptor = $convert.base64Decode('CgpCYW5kaW5nRGVmEhIKBG5hbWUYASABKAlSBG5hbWUSNwoEcm93cxgCIAMoCzIjLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLkJhbmRpbmdSb3dSBHJvd3MSEgoEdXVpZBgDIAEoCVIEdXVpZA==');
@$core.Deprecated('Use saveBandingDefRequestDescriptor instead')
const SaveBandingDefRequest$json = const {
  '1': 'SaveBandingDefRequest',
  '2': const [
    const {'1': 'bandingDef', '3': 1, '4': 1, '5': 11, '6': '.carbon.frontend.banding.BandingDef', '10': 'bandingDef'},
    const {'1': 'setActive', '3': 2, '4': 1, '5': 8, '10': 'setActive'},
  ],
};

/// Descriptor for `SaveBandingDefRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List saveBandingDefRequestDescriptor = $convert.base64Decode('ChVTYXZlQmFuZGluZ0RlZlJlcXVlc3QSQwoKYmFuZGluZ0RlZhgBIAEoCzIjLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLkJhbmRpbmdEZWZSCmJhbmRpbmdEZWYSHAoJc2V0QWN0aXZlGAIgASgIUglzZXRBY3RpdmU=');
@$core.Deprecated('Use loadBandingDefsResponseDescriptor instead')
const LoadBandingDefsResponse$json = const {
  '1': 'LoadBandingDefsResponse',
  '2': const [
    const {'1': 'bandingDefs', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.banding.BandingDef', '10': 'bandingDefs'},
    const {
      '1': 'activeDef',
      '3': 2,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'activeDef',
    },
    const {'1': 'activeDefUUID', '3': 3, '4': 1, '5': 9, '10': 'activeDefUUID'},
  ],
};

/// Descriptor for `LoadBandingDefsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loadBandingDefsResponseDescriptor = $convert.base64Decode('ChdMb2FkQmFuZGluZ0RlZnNSZXNwb25zZRJFCgtiYW5kaW5nRGVmcxgBIAMoCzIjLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLkJhbmRpbmdEZWZSC2JhbmRpbmdEZWZzEiAKCWFjdGl2ZURlZhgCIAEoCUICGAFSCWFjdGl2ZURlZhIkCg1hY3RpdmVEZWZVVUlEGAMgASgJUg1hY3RpdmVEZWZVVUlE');
@$core.Deprecated('Use setActiveBandingDefRequestDescriptor instead')
const SetActiveBandingDefRequest$json = const {
  '1': 'SetActiveBandingDefRequest',
  '2': const [
    const {
      '1': 'name',
      '3': 1,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'name',
    },
    const {'1': 'uuid', '3': 2, '4': 1, '5': 9, '10': 'uuid'},
  ],
};

/// Descriptor for `SetActiveBandingDefRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setActiveBandingDefRequestDescriptor = $convert.base64Decode('ChpTZXRBY3RpdmVCYW5kaW5nRGVmUmVxdWVzdBIWCgRuYW1lGAEgASgJQgIYAVIEbmFtZRISCgR1dWlkGAIgASgJUgR1dWlk');
@$core.Deprecated('Use getActiveBandingDefResponseDescriptor instead')
const GetActiveBandingDefResponse$json = const {
  '1': 'GetActiveBandingDefResponse',
  '2': const [
    const {
      '1': 'name',
      '3': 1,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'name',
    },
    const {'1': 'uuid', '3': 2, '4': 1, '5': 9, '10': 'uuid'},
  ],
};

/// Descriptor for `GetActiveBandingDefResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getActiveBandingDefResponseDescriptor = $convert.base64Decode('ChtHZXRBY3RpdmVCYW5kaW5nRGVmUmVzcG9uc2USFgoEbmFtZRgBIAEoCUICGAFSBG5hbWUSEgoEdXVpZBgCIAEoCVIEdXVpZA==');
@$core.Deprecated('Use deleteBandingDefRequestDescriptor instead')
const DeleteBandingDefRequest$json = const {
  '1': 'DeleteBandingDefRequest',
  '2': const [
    const {
      '1': 'name',
      '3': 1,
      '4': 1,
      '5': 9,
      '8': const {'3': true},
      '10': 'name',
    },
    const {'1': 'uuid', '3': 2, '4': 1, '5': 9, '10': 'uuid'},
  ],
};

/// Descriptor for `DeleteBandingDefRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deleteBandingDefRequestDescriptor = $convert.base64Decode('ChdEZWxldGVCYW5kaW5nRGVmUmVxdWVzdBIWCgRuYW1lGAEgASgJQgIYAVIEbmFtZRISCgR1dWlkGAIgASgJUgR1dWlk');
@$core.Deprecated('Use visualizationDataDescriptor instead')
const VisualizationData$json = const {
  '1': 'VisualizationData',
  '2': const [
    const {'1': 'x_mm', '3': 1, '4': 1, '5': 5, '10': 'xMm'},
    const {'1': 'y_mm', '3': 2, '4': 1, '5': 5, '10': 'yMm'},
    const {'1': 'z_mm', '3': 3, '4': 1, '5': 5, '10': 'zMm'},
    const {'1': 'is_weed', '3': 4, '4': 1, '5': 8, '10': 'isWeed'},
  ],
};

/// Descriptor for `VisualizationData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List visualizationDataDescriptor = $convert.base64Decode('ChFWaXN1YWxpemF0aW9uRGF0YRIRCgR4X21tGAEgASgFUgN4TW0SEQoEeV9tbRgCIAEoBVIDeU1tEhEKBHpfbW0YAyABKAVSA3pNbRIXCgdpc193ZWVkGAQgASgIUgZpc1dlZWQ=');
@$core.Deprecated('Use getNextVisualizationDataRequestDescriptor instead')
const GetNextVisualizationDataRequest$json = const {
  '1': 'GetNextVisualizationDataRequest',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 5, '10': 'rowId'},
    const {'1': 'types_to_include', '3': 3, '4': 3, '5': 14, '6': '.carbon.frontend.banding.VisualizationTypeToInclude', '10': 'typesToInclude'},
    const {'1': 'threshold_filters', '3': 4, '4': 1, '5': 11, '6': '.carbon.frontend.banding.ThresholdFilters', '10': 'thresholdFilters'},
    const {'1': 'include_detailed_metadata', '3': 5, '4': 1, '5': 8, '9': 0, '10': 'includeDetailedMetadata', '17': true},
  ],
  '8': const [
    const {'1': '_include_detailed_metadata'},
  ],
};

/// Descriptor for `GetNextVisualizationDataRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextVisualizationDataRequestDescriptor = $convert.base64Decode('Ch9HZXROZXh0VmlzdWFsaXphdGlvbkRhdGFSZXF1ZXN0EiYKAnRzGAEgASgLMhYuY2FyYm9uLnV0aWwuVGltZXN0YW1wUgJ0cxIVCgZyb3dfaWQYAiABKAVSBXJvd0lkEl0KEHR5cGVzX3RvX2luY2x1ZGUYAyADKA4yMy5jYXJib24uZnJvbnRlbmQuYmFuZGluZy5WaXN1YWxpemF0aW9uVHlwZVRvSW5jbHVkZVIOdHlwZXNUb0luY2x1ZGUSVgoRdGhyZXNob2xkX2ZpbHRlcnMYBCABKAsyKS5jYXJib24uZnJvbnRlbmQuYmFuZGluZy5UaHJlc2hvbGRGaWx0ZXJzUhB0aHJlc2hvbGRGaWx0ZXJzEj8KGWluY2x1ZGVfZGV0YWlsZWRfbWV0YWRhdGEYBSABKAhIAFIXaW5jbHVkZURldGFpbGVkTWV0YWRhdGGIAQFCHAoaX2luY2x1ZGVfZGV0YWlsZWRfbWV0YWRhdGE=');
@$core.Deprecated('Use getNextVisualizationDataResponseDescriptor instead')
const GetNextVisualizationDataResponse$json = const {
  '1': 'GetNextVisualizationDataResponse',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'data', '3': 2, '4': 3, '5': 11, '6': '.carbon.frontend.banding.VisualizationData', '10': 'data'},
    const {'1': 'bands', '3': 3, '4': 3, '5': 11, '6': '.weed_tracking.BandDefinition', '10': 'bands'},
  ],
};

/// Descriptor for `GetNextVisualizationDataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextVisualizationDataResponseDescriptor = $convert.base64Decode('CiBHZXROZXh0VmlzdWFsaXphdGlvbkRhdGFSZXNwb25zZRImCgJ0cxgBIAEoCzIWLmNhcmJvbi51dGlsLlRpbWVzdGFtcFICdHMSPgoEZGF0YRgCIAMoCzIqLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlZpc3VhbGl6YXRpb25EYXRhUgRkYXRhEjMKBWJhbmRzGAMgAygLMh0ud2VlZF90cmFja2luZy5CYW5kRGVmaW5pdGlvblIFYmFuZHM=');
@$core.Deprecated('Use getNextVisualizationData2ResponseDescriptor instead')
const GetNextVisualizationData2Response$json = const {
  '1': 'GetNextVisualizationData2Response',
  '2': const [
    const {'1': 'data', '3': 1, '4': 1, '5': 11, '6': '.weed_tracking.DiagnosticsSnapshot', '10': 'data'},
    const {'1': 'ts', '3': 2, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
  ],
};

/// Descriptor for `GetNextVisualizationData2Response`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextVisualizationData2ResponseDescriptor = $convert.base64Decode('CiFHZXROZXh0VmlzdWFsaXphdGlvbkRhdGEyUmVzcG9uc2USNgoEZGF0YRgBIAEoCzIiLndlZWRfdHJhY2tpbmcuRGlhZ25vc3RpY3NTbmFwc2hvdFIEZGF0YRImCgJ0cxgCIAEoCzIWLmNhcmJvbi51dGlsLlRpbWVzdGFtcFICdHM=');
@$core.Deprecated('Use getDimensionsRequestDescriptor instead')
const GetDimensionsRequest$json = const {
  '1': 'GetDimensionsRequest',
  '2': const [
    const {'1': 'row_id', '3': 1, '4': 1, '5': 5, '10': 'rowId'},
  ],
};

/// Descriptor for `GetDimensionsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDimensionsRequestDescriptor = $convert.base64Decode('ChRHZXREaW1lbnNpb25zUmVxdWVzdBIVCgZyb3dfaWQYASABKAVSBXJvd0lk');
@$core.Deprecated('Use setBandingEnabledRequestDescriptor instead')
const SetBandingEnabledRequest$json = const {
  '1': 'SetBandingEnabledRequest',
  '2': const [
    const {'1': 'enabled', '3': 1, '4': 1, '5': 8, '10': 'enabled'},
  ],
};

/// Descriptor for `SetBandingEnabledRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setBandingEnabledRequestDescriptor = $convert.base64Decode('ChhTZXRCYW5kaW5nRW5hYmxlZFJlcXVlc3QSGAoHZW5hYmxlZBgBIAEoCFIHZW5hYmxlZA==');
@$core.Deprecated('Use isBandingEnabledResponseDescriptor instead')
const IsBandingEnabledResponse$json = const {
  '1': 'IsBandingEnabledResponse',
  '2': const [
    const {'1': 'enabled', '3': 1, '4': 1, '5': 8, '10': 'enabled'},
  ],
};

/// Descriptor for `IsBandingEnabledResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List isBandingEnabledResponseDescriptor = $convert.base64Decode('ChhJc0JhbmRpbmdFbmFibGVkUmVzcG9uc2USGAoHZW5hYmxlZBgBIAEoCFIHZW5hYmxlZA==');
@$core.Deprecated('Use getVisualizationMetadataResponseDescriptor instead')
const GetVisualizationMetadataResponse$json = const {
  '1': 'GetVisualizationMetadataResponse',
  '2': const [
    const {'1': 'crop_safety_radius_mm_per_row', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry', '10': 'cropSafetyRadiusMmPerRow'},
  ],
  '3': const [GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry$json],
};

@$core.Deprecated('Use getVisualizationMetadataResponseDescriptor instead')
const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry$json = const {
  '1': 'CropSafetyRadiusMmPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `GetVisualizationMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getVisualizationMetadataResponseDescriptor = $convert.base64Decode('CiBHZXRWaXN1YWxpemF0aW9uTWV0YWRhdGFSZXNwb25zZRKYAQodY3JvcF9zYWZldHlfcmFkaXVzX21tX3Blcl9yb3cYASADKAsyVy5jYXJib24uZnJvbnRlbmQuYmFuZGluZy5HZXRWaXN1YWxpemF0aW9uTWV0YWRhdGFSZXNwb25zZS5Dcm9wU2FmZXR5UmFkaXVzTW1QZXJSb3dFbnRyeVIYY3JvcFNhZmV0eVJhZGl1c01tUGVyUm93GksKHUNyb3BTYWZldHlSYWRpdXNNbVBlclJvd0VudHJ5EhAKA2tleRgBIAEoBVIDa2V5EhQKBXZhbHVlGAIgASgCUgV2YWx1ZToCOAE=');
@$core.Deprecated('Use thresholdFilterDescriptor instead')
const ThresholdFilter$json = const {
  '1': 'ThresholdFilter',
  '2': const [
    const {'1': 'weeding', '3': 1, '4': 1, '5': 14, '6': '.carbon.frontend.banding.ThresholdState', '10': 'weeding'},
    const {'1': 'thinning', '3': 2, '4': 1, '5': 14, '6': '.carbon.frontend.banding.ThresholdState', '10': 'thinning'},
    const {'1': 'banding', '3': 3, '4': 1, '5': 14, '6': '.carbon.frontend.banding.ThresholdState', '10': 'banding'},
  ],
};

/// Descriptor for `ThresholdFilter`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List thresholdFilterDescriptor = $convert.base64Decode('Cg9UaHJlc2hvbGRGaWx0ZXISQQoHd2VlZGluZxgBIAEoDjInLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlRocmVzaG9sZFN0YXRlUgd3ZWVkaW5nEkMKCHRoaW5uaW5nGAIgASgOMicuY2FyYm9uLmZyb250ZW5kLmJhbmRpbmcuVGhyZXNob2xkU3RhdGVSCHRoaW5uaW5nEkEKB2JhbmRpbmcYAyABKA4yJy5jYXJib24uZnJvbnRlbmQuYmFuZGluZy5UaHJlc2hvbGRTdGF0ZVIHYmFuZGluZw==');
@$core.Deprecated('Use thresholdFiltersDescriptor instead')
const ThresholdFilters$json = const {
  '1': 'ThresholdFilters',
  '2': const [
    const {'1': 'crop', '3': 1, '4': 1, '5': 11, '6': '.carbon.frontend.banding.ThresholdFilter', '10': 'crop'},
    const {'1': 'weed', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.banding.ThresholdFilter', '10': 'weed'},
  ],
};

/// Descriptor for `ThresholdFilters`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List thresholdFiltersDescriptor = $convert.base64Decode('ChBUaHJlc2hvbGRGaWx0ZXJzEjwKBGNyb3AYASABKAsyKC5jYXJib24uZnJvbnRlbmQuYmFuZGluZy5UaHJlc2hvbGRGaWx0ZXJSBGNyb3ASPAoEd2VlZBgCIAEoCzIoLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlRocmVzaG9sZEZpbHRlclIEd2VlZA==');
@$core.Deprecated('Use getNextVisualizationDataForAllRowsRequestDescriptor instead')
const GetNextVisualizationDataForAllRowsRequest$json = const {
  '1': 'GetNextVisualizationDataForAllRowsRequest',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'types_to_include', '3': 2, '4': 3, '5': 14, '6': '.carbon.frontend.banding.VisualizationTypeToInclude', '10': 'typesToInclude'},
    const {'1': 'threshold_filters', '3': 3, '4': 1, '5': 11, '6': '.carbon.frontend.banding.ThresholdFilters', '10': 'thresholdFilters'},
    const {'1': 'include_detailed_metadata', '3': 4, '4': 1, '5': 8, '9': 0, '10': 'includeDetailedMetadata', '17': true},
  ],
  '8': const [
    const {'1': '_include_detailed_metadata'},
  ],
};

/// Descriptor for `GetNextVisualizationDataForAllRowsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextVisualizationDataForAllRowsRequestDescriptor = $convert.base64Decode('CilHZXROZXh0VmlzdWFsaXphdGlvbkRhdGFGb3JBbGxSb3dzUmVxdWVzdBImCgJ0cxgBIAEoCzIWLmNhcmJvbi51dGlsLlRpbWVzdGFtcFICdHMSXQoQdHlwZXNfdG9faW5jbHVkZRgCIAMoDjIzLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlZpc3VhbGl6YXRpb25UeXBlVG9JbmNsdWRlUg50eXBlc1RvSW5jbHVkZRJWChF0aHJlc2hvbGRfZmlsdGVycxgDIAEoCzIpLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlRocmVzaG9sZEZpbHRlcnNSEHRocmVzaG9sZEZpbHRlcnMSPwoZaW5jbHVkZV9kZXRhaWxlZF9tZXRhZGF0YRgEIAEoCEgAUhdpbmNsdWRlRGV0YWlsZWRNZXRhZGF0YYgBAUIcChpfaW5jbHVkZV9kZXRhaWxlZF9tZXRhZGF0YQ==');
@$core.Deprecated('Use getNextVisualizationDataForAllRowsResponseDescriptor instead')
const GetNextVisualizationDataForAllRowsResponse$json = const {
  '1': 'GetNextVisualizationDataForAllRowsResponse',
  '2': const [
    const {'1': 'data_per_row', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry', '10': 'dataPerRow'},
    const {'1': 'ts', '3': 2, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'types_to_include', '3': 3, '4': 3, '5': 14, '6': '.carbon.frontend.banding.VisualizationTypeToInclude', '10': 'typesToInclude'},
  ],
  '3': const [GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry$json],
};

@$core.Deprecated('Use getNextVisualizationDataForAllRowsResponseDescriptor instead')
const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry$json = const {
  '1': 'DataPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.weed_tracking.DiagnosticsSnapshot', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `GetNextVisualizationDataForAllRowsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextVisualizationDataForAllRowsResponseDescriptor = $convert.base64Decode('CipHZXROZXh0VmlzdWFsaXphdGlvbkRhdGFGb3JBbGxSb3dzUmVzcG9uc2USdQoMZGF0YV9wZXJfcm93GAEgAygLMlMuY2FyYm9uLmZyb250ZW5kLmJhbmRpbmcuR2V0TmV4dFZpc3VhbGl6YXRpb25EYXRhRm9yQWxsUm93c1Jlc3BvbnNlLkRhdGFQZXJSb3dFbnRyeVIKZGF0YVBlclJvdxImCgJ0cxgCIAEoCzIWLmNhcmJvbi51dGlsLlRpbWVzdGFtcFICdHMSXQoQdHlwZXNfdG9faW5jbHVkZRgDIAMoDjIzLmNhcmJvbi5mcm9udGVuZC5iYW5kaW5nLlZpc3VhbGl6YXRpb25UeXBlVG9JbmNsdWRlUg50eXBlc1RvSW5jbHVkZRphCg9EYXRhUGVyUm93RW50cnkSEAoDa2V5GAEgASgFUgNrZXkSOAoFdmFsdWUYAiABKAsyIi53ZWVkX3RyYWNraW5nLkRpYWdub3N0aWNzU25hcHNob3RSBXZhbHVlOgI4AQ==');
@$core.Deprecated('Use getNextBandingStateResponseDescriptor instead')
const GetNextBandingStateResponse$json = const {
  '1': 'GetNextBandingStateResponse',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'bandingDefs', '3': 2, '4': 3, '5': 11, '6': '.carbon.frontend.banding.BandingDef', '10': 'bandingDefs'},
    const {'1': 'activeDefUUID', '3': 3, '4': 1, '5': 9, '10': 'activeDefUUID'},
    const {'1': 'is_banding_enabled', '3': 4, '4': 1, '5': 8, '10': 'isBandingEnabled'},
    const {'1': 'is_dynamic_banding_enabled', '3': 5, '4': 1, '5': 8, '10': 'isDynamicBandingEnabled'},
  ],
};

/// Descriptor for `GetNextBandingStateResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextBandingStateResponseDescriptor = $convert.base64Decode('ChtHZXROZXh0QmFuZGluZ1N0YXRlUmVzcG9uc2USJgoCdHMYASABKAsyFi5jYXJib24udXRpbC5UaW1lc3RhbXBSAnRzEkUKC2JhbmRpbmdEZWZzGAIgAygLMiMuY2FyYm9uLmZyb250ZW5kLmJhbmRpbmcuQmFuZGluZ0RlZlILYmFuZGluZ0RlZnMSJAoNYWN0aXZlRGVmVVVJRBgDIAEoCVINYWN0aXZlRGVmVVVJRBIsChJpc19iYW5kaW5nX2VuYWJsZWQYBCABKAhSEGlzQmFuZGluZ0VuYWJsZWQSOwoaaXNfZHluYW1pY19iYW5kaW5nX2VuYWJsZWQYBSABKAhSF2lzRHluYW1pY0JhbmRpbmdFbmFibGVk');
