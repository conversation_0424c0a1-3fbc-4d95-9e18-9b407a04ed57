///
//  Generated code. Do not modify.
//  source: frontend/almanac.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../almanac/almanac.pb.dart' as $64;
import '../util/util.pb.dart' as $1;

class GetConfigDataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConfigDataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numSizeCategories', $pb.PbFieldType.OU3)
    ..m<$core.String, $core.String>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropCategoryNames', entryClassName: 'GetConfigDataResponse.CropCategoryNamesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..m<$core.String, $core.String>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedCategoryNames', entryClassName: 'GetConfigDataResponse.WeedCategoryNamesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..hasRequiredFields = false
  ;

  GetConfigDataResponse._() : super();
  factory GetConfigDataResponse({
    $core.int? numSizeCategories,
    $core.Map<$core.String, $core.String>? cropCategoryNames,
    $core.Map<$core.String, $core.String>? weedCategoryNames,
  }) {
    final _result = create();
    if (numSizeCategories != null) {
      _result.numSizeCategories = numSizeCategories;
    }
    if (cropCategoryNames != null) {
      _result.cropCategoryNames.addAll(cropCategoryNames);
    }
    if (weedCategoryNames != null) {
      _result.weedCategoryNames.addAll(weedCategoryNames);
    }
    return _result;
  }
  factory GetConfigDataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConfigDataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConfigDataResponse clone() => GetConfigDataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConfigDataResponse copyWith(void Function(GetConfigDataResponse) updates) => super.copyWith((message) => updates(message as GetConfigDataResponse)) as GetConfigDataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConfigDataResponse create() => GetConfigDataResponse._();
  GetConfigDataResponse createEmptyInstance() => create();
  static $pb.PbList<GetConfigDataResponse> createRepeated() => $pb.PbList<GetConfigDataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetConfigDataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConfigDataResponse>(create);
  static GetConfigDataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get numSizeCategories => $_getIZ(0);
  @$pb.TagNumber(1)
  set numSizeCategories($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNumSizeCategories() => $_has(0);
  @$pb.TagNumber(1)
  void clearNumSizeCategories() => clearField(1);

  @$pb.TagNumber(2)
  $core.Map<$core.String, $core.String> get cropCategoryNames => $_getMap(1);

  @$pb.TagNumber(3)
  $core.Map<$core.String, $core.String> get weedCategoryNames => $_getMap(2);
}

class LoadAlmanacConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadAlmanacConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  LoadAlmanacConfigRequest._() : super();
  factory LoadAlmanacConfigRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory LoadAlmanacConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadAlmanacConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadAlmanacConfigRequest clone() => LoadAlmanacConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadAlmanacConfigRequest copyWith(void Function(LoadAlmanacConfigRequest) updates) => super.copyWith((message) => updates(message as LoadAlmanacConfigRequest)) as LoadAlmanacConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadAlmanacConfigRequest create() => LoadAlmanacConfigRequest._();
  LoadAlmanacConfigRequest createEmptyInstance() => create();
  static $pb.PbList<LoadAlmanacConfigRequest> createRepeated() => $pb.PbList<LoadAlmanacConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static LoadAlmanacConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadAlmanacConfigRequest>(create);
  static LoadAlmanacConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class LoadAlmanacConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadAlmanacConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.AlmanacConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.AlmanacConfig.create)
    ..hasRequiredFields = false
  ;

  LoadAlmanacConfigResponse._() : super();
  factory LoadAlmanacConfigResponse({
    $64.AlmanacConfig? config,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory LoadAlmanacConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadAlmanacConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadAlmanacConfigResponse clone() => LoadAlmanacConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadAlmanacConfigResponse copyWith(void Function(LoadAlmanacConfigResponse) updates) => super.copyWith((message) => updates(message as LoadAlmanacConfigResponse)) as LoadAlmanacConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadAlmanacConfigResponse create() => LoadAlmanacConfigResponse._();
  LoadAlmanacConfigResponse createEmptyInstance() => create();
  static $pb.PbList<LoadAlmanacConfigResponse> createRepeated() => $pb.PbList<LoadAlmanacConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static LoadAlmanacConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadAlmanacConfigResponse>(create);
  static LoadAlmanacConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $64.AlmanacConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.AlmanacConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.AlmanacConfig ensureConfig() => $_ensure(0);
}

class SaveAlmanacConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveAlmanacConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.AlmanacConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.AlmanacConfig.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'setActive')
    ..hasRequiredFields = false
  ;

  SaveAlmanacConfigRequest._() : super();
  factory SaveAlmanacConfigRequest({
    $64.AlmanacConfig? config,
    $core.bool? setActive,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    if (setActive != null) {
      _result.setActive = setActive;
    }
    return _result;
  }
  factory SaveAlmanacConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveAlmanacConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveAlmanacConfigRequest clone() => SaveAlmanacConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveAlmanacConfigRequest copyWith(void Function(SaveAlmanacConfigRequest) updates) => super.copyWith((message) => updates(message as SaveAlmanacConfigRequest)) as SaveAlmanacConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveAlmanacConfigRequest create() => SaveAlmanacConfigRequest._();
  SaveAlmanacConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SaveAlmanacConfigRequest> createRepeated() => $pb.PbList<SaveAlmanacConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SaveAlmanacConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveAlmanacConfigRequest>(create);
  static SaveAlmanacConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $64.AlmanacConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.AlmanacConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.AlmanacConfig ensureConfig() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get setActive => $_getBF(1);
  @$pb.TagNumber(2)
  set setActive($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSetActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearSetActive() => clearField(2);
}

class SaveAlmanacConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveAlmanacConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  SaveAlmanacConfigResponse._() : super();
  factory SaveAlmanacConfigResponse({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SaveAlmanacConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveAlmanacConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveAlmanacConfigResponse clone() => SaveAlmanacConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveAlmanacConfigResponse copyWith(void Function(SaveAlmanacConfigResponse) updates) => super.copyWith((message) => updates(message as SaveAlmanacConfigResponse)) as SaveAlmanacConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveAlmanacConfigResponse create() => SaveAlmanacConfigResponse._();
  SaveAlmanacConfigResponse createEmptyInstance() => create();
  static $pb.PbList<SaveAlmanacConfigResponse> createRepeated() => $pb.PbList<SaveAlmanacConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static SaveAlmanacConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveAlmanacConfigResponse>(create);
  static SaveAlmanacConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class SetActiveAlmanacConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveAlmanacConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  SetActiveAlmanacConfigRequest._() : super();
  factory SetActiveAlmanacConfigRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SetActiveAlmanacConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveAlmanacConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveAlmanacConfigRequest clone() => SetActiveAlmanacConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveAlmanacConfigRequest copyWith(void Function(SetActiveAlmanacConfigRequest) updates) => super.copyWith((message) => updates(message as SetActiveAlmanacConfigRequest)) as SetActiveAlmanacConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveAlmanacConfigRequest create() => SetActiveAlmanacConfigRequest._();
  SetActiveAlmanacConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveAlmanacConfigRequest> createRepeated() => $pb.PbList<SetActiveAlmanacConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveAlmanacConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveAlmanacConfigRequest>(create);
  static SetActiveAlmanacConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class DeleteAlmanacConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteAlmanacConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'newActiveId')
    ..hasRequiredFields = false
  ;

  DeleteAlmanacConfigRequest._() : super();
  factory DeleteAlmanacConfigRequest({
    $core.String? id,
    $core.String? newActiveId,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (newActiveId != null) {
      _result.newActiveId = newActiveId;
    }
    return _result;
  }
  factory DeleteAlmanacConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteAlmanacConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteAlmanacConfigRequest clone() => DeleteAlmanacConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteAlmanacConfigRequest copyWith(void Function(DeleteAlmanacConfigRequest) updates) => super.copyWith((message) => updates(message as DeleteAlmanacConfigRequest)) as DeleteAlmanacConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteAlmanacConfigRequest create() => DeleteAlmanacConfigRequest._();
  DeleteAlmanacConfigRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteAlmanacConfigRequest> createRepeated() => $pb.PbList<DeleteAlmanacConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteAlmanacConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteAlmanacConfigRequest>(create);
  static DeleteAlmanacConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get newActiveId => $_getSZ(1);
  @$pb.TagNumber(2)
  set newActiveId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasNewActiveId() => $_has(1);
  @$pb.TagNumber(2)
  void clearNewActiveId() => clearField(2);
}

class GetNextAlmanacConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAlmanacConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'active')
    ..m<$core.String, $core.String>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'available', entryClassName: 'GetNextAlmanacConfigResponse.AvailableEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..hasRequiredFields = false
  ;

  GetNextAlmanacConfigResponse._() : super();
  factory GetNextAlmanacConfigResponse({
    $1.Timestamp? ts,
    $core.String? active,
    $core.Map<$core.String, $core.String>? available,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (active != null) {
      _result.active = active;
    }
    if (available != null) {
      _result.available.addAll(available);
    }
    return _result;
  }
  factory GetNextAlmanacConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAlmanacConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAlmanacConfigResponse clone() => GetNextAlmanacConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAlmanacConfigResponse copyWith(void Function(GetNextAlmanacConfigResponse) updates) => super.copyWith((message) => updates(message as GetNextAlmanacConfigResponse)) as GetNextAlmanacConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAlmanacConfigResponse create() => GetNextAlmanacConfigResponse._();
  GetNextAlmanacConfigResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextAlmanacConfigResponse> createRepeated() => $pb.PbList<GetNextAlmanacConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextAlmanacConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAlmanacConfigResponse>(create);
  static GetNextAlmanacConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get active => $_getSZ(1);
  @$pb.TagNumber(2)
  set active($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearActive() => clearField(2);

  @$pb.TagNumber(3)
  $core.Map<$core.String, $core.String> get available => $_getMap(2);
}

class LoadDiscriminatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadDiscriminatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  LoadDiscriminatorConfigRequest._() : super();
  factory LoadDiscriminatorConfigRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory LoadDiscriminatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadDiscriminatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadDiscriminatorConfigRequest clone() => LoadDiscriminatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadDiscriminatorConfigRequest copyWith(void Function(LoadDiscriminatorConfigRequest) updates) => super.copyWith((message) => updates(message as LoadDiscriminatorConfigRequest)) as LoadDiscriminatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadDiscriminatorConfigRequest create() => LoadDiscriminatorConfigRequest._();
  LoadDiscriminatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<LoadDiscriminatorConfigRequest> createRepeated() => $pb.PbList<LoadDiscriminatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static LoadDiscriminatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadDiscriminatorConfigRequest>(create);
  static LoadDiscriminatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class LoadDiscriminatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadDiscriminatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.DiscriminatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.DiscriminatorConfig.create)
    ..hasRequiredFields = false
  ;

  LoadDiscriminatorConfigResponse._() : super();
  factory LoadDiscriminatorConfigResponse({
    $64.DiscriminatorConfig? config,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory LoadDiscriminatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadDiscriminatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadDiscriminatorConfigResponse clone() => LoadDiscriminatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadDiscriminatorConfigResponse copyWith(void Function(LoadDiscriminatorConfigResponse) updates) => super.copyWith((message) => updates(message as LoadDiscriminatorConfigResponse)) as LoadDiscriminatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadDiscriminatorConfigResponse create() => LoadDiscriminatorConfigResponse._();
  LoadDiscriminatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<LoadDiscriminatorConfigResponse> createRepeated() => $pb.PbList<LoadDiscriminatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static LoadDiscriminatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadDiscriminatorConfigResponse>(create);
  static LoadDiscriminatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $64.DiscriminatorConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.DiscriminatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.DiscriminatorConfig ensureConfig() => $_ensure(0);
}

class SaveDiscriminatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveDiscriminatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.DiscriminatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.DiscriminatorConfig.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'associateWithActiveCrop')
    ..hasRequiredFields = false
  ;

  SaveDiscriminatorConfigRequest._() : super();
  factory SaveDiscriminatorConfigRequest({
    $64.DiscriminatorConfig? config,
    $core.bool? associateWithActiveCrop,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    if (associateWithActiveCrop != null) {
      _result.associateWithActiveCrop = associateWithActiveCrop;
    }
    return _result;
  }
  factory SaveDiscriminatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveDiscriminatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveDiscriminatorConfigRequest clone() => SaveDiscriminatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveDiscriminatorConfigRequest copyWith(void Function(SaveDiscriminatorConfigRequest) updates) => super.copyWith((message) => updates(message as SaveDiscriminatorConfigRequest)) as SaveDiscriminatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveDiscriminatorConfigRequest create() => SaveDiscriminatorConfigRequest._();
  SaveDiscriminatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SaveDiscriminatorConfigRequest> createRepeated() => $pb.PbList<SaveDiscriminatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SaveDiscriminatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveDiscriminatorConfigRequest>(create);
  static SaveDiscriminatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $64.DiscriminatorConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.DiscriminatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.DiscriminatorConfig ensureConfig() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get associateWithActiveCrop => $_getBF(1);
  @$pb.TagNumber(2)
  set associateWithActiveCrop($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAssociateWithActiveCrop() => $_has(1);
  @$pb.TagNumber(2)
  void clearAssociateWithActiveCrop() => clearField(2);
}

class SaveDiscriminatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveDiscriminatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  SaveDiscriminatorConfigResponse._() : super();
  factory SaveDiscriminatorConfigResponse({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SaveDiscriminatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveDiscriminatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveDiscriminatorConfigResponse clone() => SaveDiscriminatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveDiscriminatorConfigResponse copyWith(void Function(SaveDiscriminatorConfigResponse) updates) => super.copyWith((message) => updates(message as SaveDiscriminatorConfigResponse)) as SaveDiscriminatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveDiscriminatorConfigResponse create() => SaveDiscriminatorConfigResponse._();
  SaveDiscriminatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<SaveDiscriminatorConfigResponse> createRepeated() => $pb.PbList<SaveDiscriminatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static SaveDiscriminatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveDiscriminatorConfigResponse>(create);
  static SaveDiscriminatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class SetActiveDiscriminatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveDiscriminatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..hasRequiredFields = false
  ;

  SetActiveDiscriminatorConfigRequest._() : super();
  factory SetActiveDiscriminatorConfigRequest({
    $core.String? id,
    $core.String? cropId,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    return _result;
  }
  factory SetActiveDiscriminatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveDiscriminatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveDiscriminatorConfigRequest clone() => SetActiveDiscriminatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveDiscriminatorConfigRequest copyWith(void Function(SetActiveDiscriminatorConfigRequest) updates) => super.copyWith((message) => updates(message as SetActiveDiscriminatorConfigRequest)) as SetActiveDiscriminatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveDiscriminatorConfigRequest create() => SetActiveDiscriminatorConfigRequest._();
  SetActiveDiscriminatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveDiscriminatorConfigRequest> createRepeated() => $pb.PbList<SetActiveDiscriminatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveDiscriminatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveDiscriminatorConfigRequest>(create);
  static SetActiveDiscriminatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cropId => $_getSZ(1);
  @$pb.TagNumber(2)
  set cropId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropId() => clearField(2);
}

class DeleteDiscriminatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteDiscriminatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  DeleteDiscriminatorConfigRequest._() : super();
  factory DeleteDiscriminatorConfigRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory DeleteDiscriminatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteDiscriminatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteDiscriminatorConfigRequest clone() => DeleteDiscriminatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteDiscriminatorConfigRequest copyWith(void Function(DeleteDiscriminatorConfigRequest) updates) => super.copyWith((message) => updates(message as DeleteDiscriminatorConfigRequest)) as DeleteDiscriminatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteDiscriminatorConfigRequest create() => DeleteDiscriminatorConfigRequest._();
  DeleteDiscriminatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteDiscriminatorConfigRequest> createRepeated() => $pb.PbList<DeleteDiscriminatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteDiscriminatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteDiscriminatorConfigRequest>(create);
  static DeleteDiscriminatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class GetNextDiscriminatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextDiscriminatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'active')
    ..m<$core.String, $core.String>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'available', entryClassName: 'GetNextDiscriminatorConfigResponse.AvailableEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..hasRequiredFields = false
  ;

  GetNextDiscriminatorConfigResponse._() : super();
  factory GetNextDiscriminatorConfigResponse({
    $1.Timestamp? ts,
    $core.String? active,
    $core.Map<$core.String, $core.String>? available,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (active != null) {
      _result.active = active;
    }
    if (available != null) {
      _result.available.addAll(available);
    }
    return _result;
  }
  factory GetNextDiscriminatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextDiscriminatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextDiscriminatorConfigResponse clone() => GetNextDiscriminatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextDiscriminatorConfigResponse copyWith(void Function(GetNextDiscriminatorConfigResponse) updates) => super.copyWith((message) => updates(message as GetNextDiscriminatorConfigResponse)) as GetNextDiscriminatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextDiscriminatorConfigResponse create() => GetNextDiscriminatorConfigResponse._();
  GetNextDiscriminatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextDiscriminatorConfigResponse> createRepeated() => $pb.PbList<GetNextDiscriminatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextDiscriminatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextDiscriminatorConfigResponse>(create);
  static GetNextDiscriminatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get active => $_getSZ(1);
  @$pb.TagNumber(2)
  set active($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearActive() => clearField(2);

  @$pb.TagNumber(3)
  $core.Map<$core.String, $core.String> get available => $_getMap(2);
}

class GetNextModelinatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextModelinatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOM<$64.ModelinatorConfig>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.ModelinatorConfig.create)
    ..hasRequiredFields = false
  ;

  GetNextModelinatorConfigResponse._() : super();
  factory GetNextModelinatorConfigResponse({
    $1.Timestamp? ts,
    $64.ModelinatorConfig? config,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory GetNextModelinatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextModelinatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextModelinatorConfigResponse clone() => GetNextModelinatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextModelinatorConfigResponse copyWith(void Function(GetNextModelinatorConfigResponse) updates) => super.copyWith((message) => updates(message as GetNextModelinatorConfigResponse)) as GetNextModelinatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextModelinatorConfigResponse create() => GetNextModelinatorConfigResponse._();
  GetNextModelinatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextModelinatorConfigResponse> createRepeated() => $pb.PbList<GetNextModelinatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextModelinatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextModelinatorConfigResponse>(create);
  static GetNextModelinatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $64.ModelinatorConfig get config => $_getN(1);
  @$pb.TagNumber(2)
  set config($64.ModelinatorConfig v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasConfig() => $_has(1);
  @$pb.TagNumber(2)
  void clearConfig() => clearField(2);
  @$pb.TagNumber(2)
  $64.ModelinatorConfig ensureConfig() => $_ensure(1);
}

class SaveModelinatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveModelinatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.ModelinatorConfig.create)
    ..hasRequiredFields = false
  ;

  SaveModelinatorConfigRequest._() : super();
  factory SaveModelinatorConfigRequest({
    $64.ModelinatorConfig? config,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory SaveModelinatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveModelinatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveModelinatorConfigRequest clone() => SaveModelinatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveModelinatorConfigRequest copyWith(void Function(SaveModelinatorConfigRequest) updates) => super.copyWith((message) => updates(message as SaveModelinatorConfigRequest)) as SaveModelinatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveModelinatorConfigRequest create() => SaveModelinatorConfigRequest._();
  SaveModelinatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SaveModelinatorConfigRequest> createRepeated() => $pb.PbList<SaveModelinatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SaveModelinatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveModelinatorConfigRequest>(create);
  static SaveModelinatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $64.ModelinatorConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.ModelinatorConfig ensureConfig() => $_ensure(0);
}

class FetchModelinatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FetchModelinatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..hasRequiredFields = false
  ;

  FetchModelinatorConfigRequest._() : super();
  factory FetchModelinatorConfigRequest({
    $core.String? modelId,
    $core.String? cropId,
  }) {
    final _result = create();
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    return _result;
  }
  factory FetchModelinatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FetchModelinatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FetchModelinatorConfigRequest clone() => FetchModelinatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FetchModelinatorConfigRequest copyWith(void Function(FetchModelinatorConfigRequest) updates) => super.copyWith((message) => updates(message as FetchModelinatorConfigRequest)) as FetchModelinatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FetchModelinatorConfigRequest create() => FetchModelinatorConfigRequest._();
  FetchModelinatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<FetchModelinatorConfigRequest> createRepeated() => $pb.PbList<FetchModelinatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static FetchModelinatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FetchModelinatorConfigRequest>(create);
  static FetchModelinatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get modelId => $_getSZ(0);
  @$pb.TagNumber(1)
  set modelId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cropId => $_getSZ(1);
  @$pb.TagNumber(2)
  set cropId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropId() => clearField(2);
}

class FetchModelinatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FetchModelinatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$64.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'config', subBuilder: $64.ModelinatorConfig.create)
    ..hasRequiredFields = false
  ;

  FetchModelinatorConfigResponse._() : super();
  factory FetchModelinatorConfigResponse({
    $64.ModelinatorConfig? config,
  }) {
    final _result = create();
    if (config != null) {
      _result.config = config;
    }
    return _result;
  }
  factory FetchModelinatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FetchModelinatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FetchModelinatorConfigResponse clone() => FetchModelinatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FetchModelinatorConfigResponse copyWith(void Function(FetchModelinatorConfigResponse) updates) => super.copyWith((message) => updates(message as FetchModelinatorConfigResponse)) as FetchModelinatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FetchModelinatorConfigResponse create() => FetchModelinatorConfigResponse._();
  FetchModelinatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<FetchModelinatorConfigResponse> createRepeated() => $pb.PbList<FetchModelinatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static FetchModelinatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FetchModelinatorConfigResponse>(create);
  static FetchModelinatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $64.ModelinatorConfig get config => $_getN(0);
  @$pb.TagNumber(1)
  set config($64.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearConfig() => clearField(1);
  @$pb.TagNumber(1)
  $64.ModelinatorConfig ensureConfig() => $_ensure(0);
}

class ResetModelinatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ResetModelinatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  ResetModelinatorConfigRequest._() : super();
  factory ResetModelinatorConfigRequest() => create();
  factory ResetModelinatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ResetModelinatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ResetModelinatorConfigRequest clone() => ResetModelinatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ResetModelinatorConfigRequest copyWith(void Function(ResetModelinatorConfigRequest) updates) => super.copyWith((message) => updates(message as ResetModelinatorConfigRequest)) as ResetModelinatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ResetModelinatorConfigRequest create() => ResetModelinatorConfigRequest._();
  ResetModelinatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<ResetModelinatorConfigRequest> createRepeated() => $pb.PbList<ResetModelinatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static ResetModelinatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ResetModelinatorConfigRequest>(create);
  static ResetModelinatorConfigRequest? _defaultInstance;
}

class GetNextConfigDataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextConfigDataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lang')
    ..hasRequiredFields = false
  ;

  GetNextConfigDataRequest._() : super();
  factory GetNextConfigDataRequest({
    $1.Timestamp? ts,
    $core.String? lang,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (lang != null) {
      _result.lang = lang;
    }
    return _result;
  }
  factory GetNextConfigDataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextConfigDataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextConfigDataRequest clone() => GetNextConfigDataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextConfigDataRequest copyWith(void Function(GetNextConfigDataRequest) updates) => super.copyWith((message) => updates(message as GetNextConfigDataRequest)) as GetNextConfigDataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextConfigDataRequest create() => GetNextConfigDataRequest._();
  GetNextConfigDataRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextConfigDataRequest> createRepeated() => $pb.PbList<GetNextConfigDataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextConfigDataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextConfigDataRequest>(create);
  static GetNextConfigDataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get lang => $_getSZ(1);
  @$pb.TagNumber(2)
  set lang($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLang() => $_has(1);
  @$pb.TagNumber(2)
  void clearLang() => clearField(2);
}

class GetNextConfigDataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextConfigDataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.almanac'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numSizeCategories', $pb.PbFieldType.OU3)
    ..m<$core.String, $core.String>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropCategoryNames', entryClassName: 'GetNextConfigDataResponse.CropCategoryNamesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..m<$core.String, $core.String>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedCategoryNames', entryClassName: 'GetNextConfigDataResponse.WeedCategoryNamesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.almanac'))
    ..hasRequiredFields = false
  ;

  GetNextConfigDataResponse._() : super();
  factory GetNextConfigDataResponse({
    $1.Timestamp? ts,
    $core.int? numSizeCategories,
    $core.Map<$core.String, $core.String>? cropCategoryNames,
    $core.Map<$core.String, $core.String>? weedCategoryNames,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (numSizeCategories != null) {
      _result.numSizeCategories = numSizeCategories;
    }
    if (cropCategoryNames != null) {
      _result.cropCategoryNames.addAll(cropCategoryNames);
    }
    if (weedCategoryNames != null) {
      _result.weedCategoryNames.addAll(weedCategoryNames);
    }
    return _result;
  }
  factory GetNextConfigDataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextConfigDataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextConfigDataResponse clone() => GetNextConfigDataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextConfigDataResponse copyWith(void Function(GetNextConfigDataResponse) updates) => super.copyWith((message) => updates(message as GetNextConfigDataResponse)) as GetNextConfigDataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextConfigDataResponse create() => GetNextConfigDataResponse._();
  GetNextConfigDataResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextConfigDataResponse> createRepeated() => $pb.PbList<GetNextConfigDataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextConfigDataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextConfigDataResponse>(create);
  static GetNextConfigDataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get numSizeCategories => $_getIZ(1);
  @$pb.TagNumber(2)
  set numSizeCategories($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasNumSizeCategories() => $_has(1);
  @$pb.TagNumber(2)
  void clearNumSizeCategories() => clearField(2);

  @$pb.TagNumber(3)
  $core.Map<$core.String, $core.String> get cropCategoryNames => $_getMap(2);

  @$pb.TagNumber(4)
  $core.Map<$core.String, $core.String> get weedCategoryNames => $_getMap(3);
}

