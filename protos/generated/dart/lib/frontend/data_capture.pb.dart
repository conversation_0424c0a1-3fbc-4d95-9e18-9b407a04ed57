///
//  Generated code. Do not modify.
//  source: frontend/data_capture.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;

import 'data_capture.pbenum.dart';

export 'data_capture.pbenum.dart';

class DataCaptureRate extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DataCaptureRate', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rate', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  DataCaptureRate._() : super();
  factory DataCaptureRate({
    $core.double? rate,
  }) {
    final _result = create();
    if (rate != null) {
      _result.rate = rate;
    }
    return _result;
  }
  factory DataCaptureRate.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DataCaptureRate.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DataCaptureRate clone() => DataCaptureRate()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DataCaptureRate copyWith(void Function(DataCaptureRate) updates) => super.copyWith((message) => updates(message as DataCaptureRate)) as DataCaptureRate; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DataCaptureRate create() => DataCaptureRate._();
  DataCaptureRate createEmptyInstance() => create();
  static $pb.PbList<DataCaptureRate> createRepeated() => $pb.PbList<DataCaptureRate>();
  @$core.pragma('dart2js:noInline')
  static DataCaptureRate getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DataCaptureRate>(create);
  static DataCaptureRate? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get rate => $_getN(0);
  @$pb.TagNumber(1)
  set rate($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRate() => $_has(0);
  @$pb.TagNumber(1)
  void clearRate() => clearField(1);
}

class DataCaptureState extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DataCaptureState', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesTaken', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetImagesTaken', $pb.PbFieldType.OU3)
    ..a<$fixnum.Int64>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'estimatedCaptureRemainingTimeMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesUploaded', $pb.PbFieldType.OU3)
    ..a<$core.int>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetImagesUploaded', $pb.PbFieldType.OU3)
    ..a<$fixnum.Int64>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'estimatedUploadRemainingTimeMs', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOM<DataCaptureRate>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rate', subBuilder: DataCaptureRate.create)
    ..aOB(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wirelessUploadAvailable')
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'usbStorageConnected')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureStatus')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uploadStatus')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionName')
    ..e<ProcedureStep>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'step', $pb.PbFieldType.OE, defaultOrMaker: ProcedureStep.NEW, valueOf: ProcedureStep.valueOf, enumValues: ProcedureStep.values)
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'errorMessage')
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..hasRequiredFields = false
  ;

  DataCaptureState._() : super();
  factory DataCaptureState({
    $1.Timestamp? ts,
    $core.int? imagesTaken,
    $core.int? targetImagesTaken,
    $fixnum.Int64? estimatedCaptureRemainingTimeMs,
    $core.int? imagesUploaded,
    $core.int? targetImagesUploaded,
    $fixnum.Int64? estimatedUploadRemainingTimeMs,
    DataCaptureRate? rate,
    $core.bool? wirelessUploadAvailable,
    $core.bool? usbStorageConnected,
    $core.String? captureStatus,
    $core.String? uploadStatus,
    $core.String? sessionName,
    ProcedureStep? step,
    $core.String? crop,
    $core.String? errorMessage,
    $core.String? cropId,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (imagesTaken != null) {
      _result.imagesTaken = imagesTaken;
    }
    if (targetImagesTaken != null) {
      _result.targetImagesTaken = targetImagesTaken;
    }
    if (estimatedCaptureRemainingTimeMs != null) {
      _result.estimatedCaptureRemainingTimeMs = estimatedCaptureRemainingTimeMs;
    }
    if (imagesUploaded != null) {
      _result.imagesUploaded = imagesUploaded;
    }
    if (targetImagesUploaded != null) {
      _result.targetImagesUploaded = targetImagesUploaded;
    }
    if (estimatedUploadRemainingTimeMs != null) {
      _result.estimatedUploadRemainingTimeMs = estimatedUploadRemainingTimeMs;
    }
    if (rate != null) {
      _result.rate = rate;
    }
    if (wirelessUploadAvailable != null) {
      _result.wirelessUploadAvailable = wirelessUploadAvailable;
    }
    if (usbStorageConnected != null) {
      _result.usbStorageConnected = usbStorageConnected;
    }
    if (captureStatus != null) {
      _result.captureStatus = captureStatus;
    }
    if (uploadStatus != null) {
      _result.uploadStatus = uploadStatus;
    }
    if (sessionName != null) {
      _result.sessionName = sessionName;
    }
    if (step != null) {
      _result.step = step;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (errorMessage != null) {
      _result.errorMessage = errorMessage;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    return _result;
  }
  factory DataCaptureState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DataCaptureState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DataCaptureState clone() => DataCaptureState()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DataCaptureState copyWith(void Function(DataCaptureState) updates) => super.copyWith((message) => updates(message as DataCaptureState)) as DataCaptureState; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DataCaptureState create() => DataCaptureState._();
  DataCaptureState createEmptyInstance() => create();
  static $pb.PbList<DataCaptureState> createRepeated() => $pb.PbList<DataCaptureState>();
  @$core.pragma('dart2js:noInline')
  static DataCaptureState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DataCaptureState>(create);
  static DataCaptureState? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get imagesTaken => $_getIZ(1);
  @$pb.TagNumber(2)
  set imagesTaken($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasImagesTaken() => $_has(1);
  @$pb.TagNumber(2)
  void clearImagesTaken() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get targetImagesTaken => $_getIZ(2);
  @$pb.TagNumber(3)
  set targetImagesTaken($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTargetImagesTaken() => $_has(2);
  @$pb.TagNumber(3)
  void clearTargetImagesTaken() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get estimatedCaptureRemainingTimeMs => $_getI64(3);
  @$pb.TagNumber(4)
  set estimatedCaptureRemainingTimeMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasEstimatedCaptureRemainingTimeMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearEstimatedCaptureRemainingTimeMs() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get imagesUploaded => $_getIZ(4);
  @$pb.TagNumber(5)
  set imagesUploaded($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasImagesUploaded() => $_has(4);
  @$pb.TagNumber(5)
  void clearImagesUploaded() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get targetImagesUploaded => $_getIZ(5);
  @$pb.TagNumber(6)
  set targetImagesUploaded($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTargetImagesUploaded() => $_has(5);
  @$pb.TagNumber(6)
  void clearTargetImagesUploaded() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get estimatedUploadRemainingTimeMs => $_getI64(6);
  @$pb.TagNumber(7)
  set estimatedUploadRemainingTimeMs($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasEstimatedUploadRemainingTimeMs() => $_has(6);
  @$pb.TagNumber(7)
  void clearEstimatedUploadRemainingTimeMs() => clearField(7);

  @$pb.TagNumber(8)
  DataCaptureRate get rate => $_getN(7);
  @$pb.TagNumber(8)
  set rate(DataCaptureRate v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasRate() => $_has(7);
  @$pb.TagNumber(8)
  void clearRate() => clearField(8);
  @$pb.TagNumber(8)
  DataCaptureRate ensureRate() => $_ensure(7);

  @$pb.TagNumber(9)
  $core.bool get wirelessUploadAvailable => $_getBF(8);
  @$pb.TagNumber(9)
  set wirelessUploadAvailable($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWirelessUploadAvailable() => $_has(8);
  @$pb.TagNumber(9)
  void clearWirelessUploadAvailable() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get usbStorageConnected => $_getBF(9);
  @$pb.TagNumber(10)
  set usbStorageConnected($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasUsbStorageConnected() => $_has(9);
  @$pb.TagNumber(10)
  void clearUsbStorageConnected() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get captureStatus => $_getSZ(10);
  @$pb.TagNumber(11)
  set captureStatus($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasCaptureStatus() => $_has(10);
  @$pb.TagNumber(11)
  void clearCaptureStatus() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get uploadStatus => $_getSZ(11);
  @$pb.TagNumber(12)
  set uploadStatus($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasUploadStatus() => $_has(11);
  @$pb.TagNumber(12)
  void clearUploadStatus() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get sessionName => $_getSZ(12);
  @$pb.TagNumber(13)
  set sessionName($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasSessionName() => $_has(12);
  @$pb.TagNumber(13)
  void clearSessionName() => clearField(13);

  @$pb.TagNumber(14)
  ProcedureStep get step => $_getN(13);
  @$pb.TagNumber(14)
  set step(ProcedureStep v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasStep() => $_has(13);
  @$pb.TagNumber(14)
  void clearStep() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get crop => $_getSZ(14);
  @$pb.TagNumber(15)
  set crop($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasCrop() => $_has(14);
  @$pb.TagNumber(15)
  void clearCrop() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get errorMessage => $_getSZ(15);
  @$pb.TagNumber(16)
  set errorMessage($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasErrorMessage() => $_has(15);
  @$pb.TagNumber(16)
  void clearErrorMessage() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get cropId => $_getSZ(16);
  @$pb.TagNumber(17)
  set cropId($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasCropId() => $_has(16);
  @$pb.TagNumber(17)
  void clearCropId() => clearField(17);
}

class DataCaptureSession extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DataCaptureSession', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  DataCaptureSession._() : super();
  factory DataCaptureSession({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory DataCaptureSession.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DataCaptureSession.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DataCaptureSession clone() => DataCaptureSession()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DataCaptureSession copyWith(void Function(DataCaptureSession) updates) => super.copyWith((message) => updates(message as DataCaptureSession)) as DataCaptureSession; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DataCaptureSession create() => DataCaptureSession._();
  DataCaptureSession createEmptyInstance() => create();
  static $pb.PbList<DataCaptureSession> createRepeated() => $pb.PbList<DataCaptureSession>();
  @$core.pragma('dart2js:noInline')
  static DataCaptureSession getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DataCaptureSession>(create);
  static DataCaptureSession? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class StartDataCaptureRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartDataCaptureRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rate', $pb.PbFieldType.OD)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapCapture')
    ..hasRequiredFields = false
  ;

  StartDataCaptureRequest._() : super();
  factory StartDataCaptureRequest({
    $core.String? name,
    $core.double? rate,
    $core.String? crop,
    $core.String? cropId,
    $core.bool? snapCapture,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (rate != null) {
      _result.rate = rate;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (snapCapture != null) {
      _result.snapCapture = snapCapture;
    }
    return _result;
  }
  factory StartDataCaptureRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartDataCaptureRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartDataCaptureRequest clone() => StartDataCaptureRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartDataCaptureRequest copyWith(void Function(StartDataCaptureRequest) updates) => super.copyWith((message) => updates(message as StartDataCaptureRequest)) as StartDataCaptureRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartDataCaptureRequest create() => StartDataCaptureRequest._();
  StartDataCaptureRequest createEmptyInstance() => create();
  static $pb.PbList<StartDataCaptureRequest> createRepeated() => $pb.PbList<StartDataCaptureRequest>();
  @$core.pragma('dart2js:noInline')
  static StartDataCaptureRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartDataCaptureRequest>(create);
  static StartDataCaptureRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get rate => $_getN(1);
  @$pb.TagNumber(2)
  set rate($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRate() => $_has(1);
  @$pb.TagNumber(2)
  void clearRate() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get crop => $_getSZ(2);
  @$pb.TagNumber(3)
  set crop($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCrop() => $_has(2);
  @$pb.TagNumber(3)
  void clearCrop() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get cropId => $_getSZ(3);
  @$pb.TagNumber(4)
  set cropId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCropId() => $_has(3);
  @$pb.TagNumber(4)
  void clearCropId() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get snapCapture => $_getBF(4);
  @$pb.TagNumber(5)
  set snapCapture($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSnapCapture() => $_has(4);
  @$pb.TagNumber(5)
  void clearSnapCapture() => clearField(5);
}

class SnapImagesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SnapImagesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionName')
    ..hasRequiredFields = false
  ;

  SnapImagesRequest._() : super();
  factory SnapImagesRequest({
    $core.String? crop,
    $core.String? cropId,
    $core.String? camId,
    $fixnum.Int64? timestampMs,
    $core.String? sessionName,
  }) {
    final _result = create();
    if (crop != null) {
      _result.crop = crop;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (sessionName != null) {
      _result.sessionName = sessionName;
    }
    return _result;
  }
  factory SnapImagesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SnapImagesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SnapImagesRequest clone() => SnapImagesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SnapImagesRequest copyWith(void Function(SnapImagesRequest) updates) => super.copyWith((message) => updates(message as SnapImagesRequest)) as SnapImagesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SnapImagesRequest create() => SnapImagesRequest._();
  SnapImagesRequest createEmptyInstance() => create();
  static $pb.PbList<SnapImagesRequest> createRepeated() => $pb.PbList<SnapImagesRequest>();
  @$core.pragma('dart2js:noInline')
  static SnapImagesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SnapImagesRequest>(create);
  static SnapImagesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get crop => $_getSZ(0);
  @$pb.TagNumber(1)
  set crop($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCrop() => $_has(0);
  @$pb.TagNumber(1)
  void clearCrop() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cropId => $_getSZ(1);
  @$pb.TagNumber(2)
  set cropId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get camId => $_getSZ(2);
  @$pb.TagNumber(3)
  set camId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCamId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCamId() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get timestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set timestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimestampMs() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get sessionName => $_getSZ(4);
  @$pb.TagNumber(5)
  set sessionName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSessionName() => $_has(4);
  @$pb.TagNumber(5)
  void clearSessionName() => clearField(5);
}

class Session extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Session', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesRemaining', $pb.PbFieldType.OU3)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isUploading')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hasCompleted')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isCapturing')
    ..hasRequiredFields = false
  ;

  Session._() : super();
  factory Session({
    $core.String? name,
    $core.int? imagesRemaining,
    $core.bool? isUploading,
    $core.bool? hasCompleted,
    $core.bool? isCapturing,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (imagesRemaining != null) {
      _result.imagesRemaining = imagesRemaining;
    }
    if (isUploading != null) {
      _result.isUploading = isUploading;
    }
    if (hasCompleted != null) {
      _result.hasCompleted = hasCompleted;
    }
    if (isCapturing != null) {
      _result.isCapturing = isCapturing;
    }
    return _result;
  }
  factory Session.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Session.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Session clone() => Session()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Session copyWith(void Function(Session) updates) => super.copyWith((message) => updates(message as Session)) as Session; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Session create() => Session._();
  Session createEmptyInstance() => create();
  static $pb.PbList<Session> createRepeated() => $pb.PbList<Session>();
  @$core.pragma('dart2js:noInline')
  static Session getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Session>(create);
  static Session? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get imagesRemaining => $_getIZ(1);
  @$pb.TagNumber(2)
  set imagesRemaining($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasImagesRemaining() => $_has(1);
  @$pb.TagNumber(2)
  void clearImagesRemaining() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get isUploading => $_getBF(2);
  @$pb.TagNumber(3)
  set isUploading($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIsUploading() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsUploading() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get hasCompleted => $_getBF(3);
  @$pb.TagNumber(4)
  set hasCompleted($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasHasCompleted() => $_has(3);
  @$pb.TagNumber(4)
  void clearHasCompleted() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get isCapturing => $_getBF(4);
  @$pb.TagNumber(5)
  set isCapturing($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIsCapturing() => $_has(4);
  @$pb.TagNumber(5)
  void clearIsCapturing() => clearField(5);
}

class AvailableSessionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AvailableSessionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..pc<Session>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessions', $pb.PbFieldType.PM, subBuilder: Session.create)
    ..hasRequiredFields = false
  ;

  AvailableSessionResponse._() : super();
  factory AvailableSessionResponse({
    $core.Iterable<Session>? sessions,
  }) {
    final _result = create();
    if (sessions != null) {
      _result.sessions.addAll(sessions);
    }
    return _result;
  }
  factory AvailableSessionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AvailableSessionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AvailableSessionResponse clone() => AvailableSessionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AvailableSessionResponse copyWith(void Function(AvailableSessionResponse) updates) => super.copyWith((message) => updates(message as AvailableSessionResponse)) as AvailableSessionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AvailableSessionResponse create() => AvailableSessionResponse._();
  AvailableSessionResponse createEmptyInstance() => create();
  static $pb.PbList<AvailableSessionResponse> createRepeated() => $pb.PbList<AvailableSessionResponse>();
  @$core.pragma('dart2js:noInline')
  static AvailableSessionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AvailableSessionResponse>(create);
  static AvailableSessionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Session> get sessions => $_getList(0);
}

class SessionName extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SessionName', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  SessionName._() : super();
  factory SessionName({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory SessionName.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionName.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SessionName clone() => SessionName()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SessionName copyWith(void Function(SessionName) updates) => super.copyWith((message) => updates(message as SessionName)) as SessionName; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SessionName create() => SessionName._();
  SessionName createEmptyInstance() => create();
  static $pb.PbList<SessionName> createRepeated() => $pb.PbList<SessionName>();
  @$core.pragma('dart2js:noInline')
  static SessionName getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionName>(create);
  static SessionName? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class RegularCaptureStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RegularCaptureStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.data_capture'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uploaded', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'budget', $pb.PbFieldType.OU3)
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastUploadTimestamp')
    ..hasRequiredFields = false
  ;

  RegularCaptureStatus._() : super();
  factory RegularCaptureStatus({
    $core.int? uploaded,
    $core.int? budget,
    $fixnum.Int64? lastUploadTimestamp,
  }) {
    final _result = create();
    if (uploaded != null) {
      _result.uploaded = uploaded;
    }
    if (budget != null) {
      _result.budget = budget;
    }
    if (lastUploadTimestamp != null) {
      _result.lastUploadTimestamp = lastUploadTimestamp;
    }
    return _result;
  }
  factory RegularCaptureStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RegularCaptureStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RegularCaptureStatus clone() => RegularCaptureStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RegularCaptureStatus copyWith(void Function(RegularCaptureStatus) updates) => super.copyWith((message) => updates(message as RegularCaptureStatus)) as RegularCaptureStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RegularCaptureStatus create() => RegularCaptureStatus._();
  RegularCaptureStatus createEmptyInstance() => create();
  static $pb.PbList<RegularCaptureStatus> createRepeated() => $pb.PbList<RegularCaptureStatus>();
  @$core.pragma('dart2js:noInline')
  static RegularCaptureStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RegularCaptureStatus>(create);
  static RegularCaptureStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get uploaded => $_getIZ(0);
  @$pb.TagNumber(1)
  set uploaded($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUploaded() => $_has(0);
  @$pb.TagNumber(1)
  void clearUploaded() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get budget => $_getIZ(1);
  @$pb.TagNumber(2)
  set budget($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBudget() => $_has(1);
  @$pb.TagNumber(2)
  void clearBudget() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get lastUploadTimestamp => $_getI64(2);
  @$pb.TagNumber(3)
  set lastUploadTimestamp($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLastUploadTimestamp() => $_has(2);
  @$pb.TagNumber(3)
  void clearLastUploadTimestamp() => clearField(3);
}

