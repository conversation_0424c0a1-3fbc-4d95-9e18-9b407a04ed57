///
//  Generated code. Do not modify.
//  source: frontend/status_bar.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'translation.pb.dart' as $63;
import '../util/util.pb.dart' as $1;

import 'status_bar.pbenum.dart';

export 'status_bar.pbenum.dart';

class GlobalStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GlobalStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hint')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'iconName')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'iconColor')
    ..hasRequiredFields = false
  ;

  GlobalStatus._() : super();
  factory GlobalStatus({
    $core.String? hint,
    $core.String? iconName,
    $core.String? iconColor,
  }) {
    final _result = create();
    if (hint != null) {
      _result.hint = hint;
    }
    if (iconName != null) {
      _result.iconName = iconName;
    }
    if (iconColor != null) {
      _result.iconColor = iconColor;
    }
    return _result;
  }
  factory GlobalStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GlobalStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GlobalStatus clone() => GlobalStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GlobalStatus copyWith(void Function(GlobalStatus) updates) => super.copyWith((message) => updates(message as GlobalStatus)) as GlobalStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GlobalStatus create() => GlobalStatus._();
  GlobalStatus createEmptyInstance() => create();
  static $pb.PbList<GlobalStatus> createRepeated() => $pb.PbList<GlobalStatus>();
  @$core.pragma('dart2js:noInline')
  static GlobalStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GlobalStatus>(create);
  static GlobalStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get hint => $_getSZ(0);
  @$pb.TagNumber(1)
  set hint($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHint() => $_has(0);
  @$pb.TagNumber(1)
  void clearHint() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get iconName => $_getSZ(1);
  @$pb.TagNumber(2)
  set iconName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIconName() => $_has(1);
  @$pb.TagNumber(2)
  void clearIconName() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get iconColor => $_getSZ(2);
  @$pb.TagNumber(3)
  set iconColor($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIconColor() => $_has(2);
  @$pb.TagNumber(3)
  void clearIconColor() => clearField(3);
}

class ServiceStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ServiceStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..e<StatusLevel>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusLevel', $pb.PbFieldType.OE, defaultOrMaker: StatusLevel.INVALID, valueOf: StatusLevel.valueOf, enumValues: StatusLevel.values)
    ..hasRequiredFields = false
  ;

  ServiceStatus._() : super();
  factory ServiceStatus({
    $core.String? name,
    StatusLevel? statusLevel,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (statusLevel != null) {
      _result.statusLevel = statusLevel;
    }
    return _result;
  }
  factory ServiceStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ServiceStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ServiceStatus clone() => ServiceStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ServiceStatus copyWith(void Function(ServiceStatus) updates) => super.copyWith((message) => updates(message as ServiceStatus)) as ServiceStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ServiceStatus create() => ServiceStatus._();
  ServiceStatus createEmptyInstance() => create();
  static $pb.PbList<ServiceStatus> createRepeated() => $pb.PbList<ServiceStatus>();
  @$core.pragma('dart2js:noInline')
  static ServiceStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ServiceStatus>(create);
  static ServiceStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  StatusLevel get statusLevel => $_getN(1);
  @$pb.TagNumber(2)
  set statusLevel(StatusLevel v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasStatusLevel() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatusLevel() => clearField(2);
}

class ServerStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ServerStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..e<StatusLevel>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusLevel', $pb.PbFieldType.OE, defaultOrMaker: StatusLevel.INVALID, valueOf: StatusLevel.valueOf, enumValues: StatusLevel.values)
    ..pc<ServiceStatus>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serviceStatus', $pb.PbFieldType.PM, subBuilder: ServiceStatus.create)
    ..hasRequiredFields = false
  ;

  ServerStatus._() : super();
  factory ServerStatus({
    StatusLevel? statusLevel,
    $core.Iterable<ServiceStatus>? serviceStatus,
  }) {
    final _result = create();
    if (statusLevel != null) {
      _result.statusLevel = statusLevel;
    }
    if (serviceStatus != null) {
      _result.serviceStatus.addAll(serviceStatus);
    }
    return _result;
  }
  factory ServerStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ServerStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ServerStatus clone() => ServerStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ServerStatus copyWith(void Function(ServerStatus) updates) => super.copyWith((message) => updates(message as ServerStatus)) as ServerStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ServerStatus create() => ServerStatus._();
  ServerStatus createEmptyInstance() => create();
  static $pb.PbList<ServerStatus> createRepeated() => $pb.PbList<ServerStatus>();
  @$core.pragma('dart2js:noInline')
  static ServerStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ServerStatus>(create);
  static ServerStatus? _defaultInstance;

  @$pb.TagNumber(1)
  StatusLevel get statusLevel => $_getN(0);
  @$pb.TagNumber(1)
  set statusLevel(StatusLevel v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatusLevel() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatusLevel() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<ServiceStatus> get serviceStatus => $_getList(1);
}

enum TranslatedStatusMessageDetails_Details {
  detailsStringKey, 
  timer, 
  notSet
}

class TranslatedStatusMessageDetails extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, TranslatedStatusMessageDetails_Details> _TranslatedStatusMessageDetails_DetailsByTag = {
    1 : TranslatedStatusMessageDetails_Details.detailsStringKey,
    2 : TranslatedStatusMessageDetails_Details.timer,
    0 : TranslatedStatusMessageDetails_Details.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TranslatedStatusMessageDetails', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detailsStringKey')
    ..aOM<$63.DurationValue>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timer', subBuilder: $63.DurationValue.create)
    ..hasRequiredFields = false
  ;

  TranslatedStatusMessageDetails._() : super();
  factory TranslatedStatusMessageDetails({
    $core.String? detailsStringKey,
    $63.DurationValue? timer,
  }) {
    final _result = create();
    if (detailsStringKey != null) {
      _result.detailsStringKey = detailsStringKey;
    }
    if (timer != null) {
      _result.timer = timer;
    }
    return _result;
  }
  factory TranslatedStatusMessageDetails.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TranslatedStatusMessageDetails.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TranslatedStatusMessageDetails clone() => TranslatedStatusMessageDetails()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TranslatedStatusMessageDetails copyWith(void Function(TranslatedStatusMessageDetails) updates) => super.copyWith((message) => updates(message as TranslatedStatusMessageDetails)) as TranslatedStatusMessageDetails; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TranslatedStatusMessageDetails create() => TranslatedStatusMessageDetails._();
  TranslatedStatusMessageDetails createEmptyInstance() => create();
  static $pb.PbList<TranslatedStatusMessageDetails> createRepeated() => $pb.PbList<TranslatedStatusMessageDetails>();
  @$core.pragma('dart2js:noInline')
  static TranslatedStatusMessageDetails getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TranslatedStatusMessageDetails>(create);
  static TranslatedStatusMessageDetails? _defaultInstance;

  TranslatedStatusMessageDetails_Details whichDetails() => _TranslatedStatusMessageDetails_DetailsByTag[$_whichOneof(0)]!;
  void clearDetails() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get detailsStringKey => $_getSZ(0);
  @$pb.TagNumber(1)
  set detailsStringKey($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDetailsStringKey() => $_has(0);
  @$pb.TagNumber(1)
  void clearDetailsStringKey() => clearField(1);

  @$pb.TagNumber(2)
  $63.DurationValue get timer => $_getN(1);
  @$pb.TagNumber(2)
  set timer($63.DurationValue v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimer() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimer() => clearField(2);
  @$pb.TagNumber(2)
  $63.DurationValue ensureTimer() => $_ensure(1);
}

class TranslatedStatusMessage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TranslatedStatusMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'prefix')
    ..aOM<TranslatedStatusMessageDetails>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'details', subBuilder: TranslatedStatusMessageDetails.create)
    ..hasRequiredFields = false
  ;

  TranslatedStatusMessage._() : super();
  factory TranslatedStatusMessage({
    $core.String? prefix,
    TranslatedStatusMessageDetails? details,
  }) {
    final _result = create();
    if (prefix != null) {
      _result.prefix = prefix;
    }
    if (details != null) {
      _result.details = details;
    }
    return _result;
  }
  factory TranslatedStatusMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TranslatedStatusMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TranslatedStatusMessage clone() => TranslatedStatusMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TranslatedStatusMessage copyWith(void Function(TranslatedStatusMessage) updates) => super.copyWith((message) => updates(message as TranslatedStatusMessage)) as TranslatedStatusMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TranslatedStatusMessage create() => TranslatedStatusMessage._();
  TranslatedStatusMessage createEmptyInstance() => create();
  static $pb.PbList<TranslatedStatusMessage> createRepeated() => $pb.PbList<TranslatedStatusMessage>();
  @$core.pragma('dart2js:noInline')
  static TranslatedStatusMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TranslatedStatusMessage>(create);
  static TranslatedStatusMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get prefix => $_getSZ(0);
  @$pb.TagNumber(1)
  set prefix($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPrefix() => $_has(0);
  @$pb.TagNumber(1)
  void clearPrefix() => clearField(1);

  @$pb.TagNumber(2)
  TranslatedStatusMessageDetails get details => $_getN(1);
  @$pb.TagNumber(2)
  set details(TranslatedStatusMessageDetails v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasDetails() => $_has(1);
  @$pb.TagNumber(2)
  void clearDetails() => clearField(2);
  @$pb.TagNumber(2)
  TranslatedStatusMessageDetails ensureDetails() => $_ensure(1);
}

class StatusBarMessage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StatusBarMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'estopped')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lasersEnabled')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..e<Status>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusLevel', $pb.PbFieldType.OE, defaultOrMaker: Status.STATUS_ERROR, valueOf: Status.valueOf, enumValues: Status.values)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusMessage')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..m<$core.int, ServerStatus>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowStatus', entryClassName: 'StatusBarMessage.RowStatusEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OM, valueCreator: ServerStatus.create, packageName: const $pb.PackageName('carbon.frontend.status_bar'))
    ..aOM<ServerStatus>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'commandStatus', subBuilder: ServerStatus.create)
    ..pc<GlobalStatus>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'globalStatuses', $pb.PbFieldType.PM, subBuilder: GlobalStatus.create)
    ..aOM<TranslatedStatusMessage>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'translatedStatusMessage', subBuilder: TranslatedStatusMessage.create)
    ..hasRequiredFields = false
  ;

  StatusBarMessage._() : super();
  factory StatusBarMessage({
    $1.Timestamp? ts,
  @$core.Deprecated('This field is deprecated.')
    $core.bool? estopped,
    $core.bool? lasersEnabled,
    $core.bool? weedingEnabled,
    Status? statusLevel,
    $core.String? statusMessage,
    $core.String? serial,
    $core.Map<$core.int, ServerStatus>? rowStatus,
    ServerStatus? commandStatus,
    $core.Iterable<GlobalStatus>? globalStatuses,
    TranslatedStatusMessage? translatedStatusMessage,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (estopped != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.estopped = estopped;
    }
    if (lasersEnabled != null) {
      _result.lasersEnabled = lasersEnabled;
    }
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (statusLevel != null) {
      _result.statusLevel = statusLevel;
    }
    if (statusMessage != null) {
      _result.statusMessage = statusMessage;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    if (rowStatus != null) {
      _result.rowStatus.addAll(rowStatus);
    }
    if (commandStatus != null) {
      _result.commandStatus = commandStatus;
    }
    if (globalStatuses != null) {
      _result.globalStatuses.addAll(globalStatuses);
    }
    if (translatedStatusMessage != null) {
      _result.translatedStatusMessage = translatedStatusMessage;
    }
    return _result;
  }
  factory StatusBarMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StatusBarMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StatusBarMessage clone() => StatusBarMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StatusBarMessage copyWith(void Function(StatusBarMessage) updates) => super.copyWith((message) => updates(message as StatusBarMessage)) as StatusBarMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StatusBarMessage create() => StatusBarMessage._();
  StatusBarMessage createEmptyInstance() => create();
  static $pb.PbList<StatusBarMessage> createRepeated() => $pb.PbList<StatusBarMessage>();
  @$core.pragma('dart2js:noInline')
  static StatusBarMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StatusBarMessage>(create);
  static StatusBarMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool get estopped => $_getBF(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  set estopped($core.bool v) { $_setBool(1, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool hasEstopped() => $_has(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  void clearEstopped() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get lasersEnabled => $_getBF(2);
  @$pb.TagNumber(3)
  set lasersEnabled($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLasersEnabled() => $_has(2);
  @$pb.TagNumber(3)
  void clearLasersEnabled() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get weedingEnabled => $_getBF(3);
  @$pb.TagNumber(4)
  set weedingEnabled($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasWeedingEnabled() => $_has(3);
  @$pb.TagNumber(4)
  void clearWeedingEnabled() => clearField(4);

  @$pb.TagNumber(5)
  Status get statusLevel => $_getN(4);
  @$pb.TagNumber(5)
  set statusLevel(Status v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasStatusLevel() => $_has(4);
  @$pb.TagNumber(5)
  void clearStatusLevel() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get statusMessage => $_getSZ(5);
  @$pb.TagNumber(6)
  set statusMessage($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasStatusMessage() => $_has(5);
  @$pb.TagNumber(6)
  void clearStatusMessage() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get serial => $_getSZ(6);
  @$pb.TagNumber(7)
  set serial($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSerial() => $_has(6);
  @$pb.TagNumber(7)
  void clearSerial() => clearField(7);

  @$pb.TagNumber(8)
  $core.Map<$core.int, ServerStatus> get rowStatus => $_getMap(7);

  @$pb.TagNumber(9)
  ServerStatus get commandStatus => $_getN(8);
  @$pb.TagNumber(9)
  set commandStatus(ServerStatus v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasCommandStatus() => $_has(8);
  @$pb.TagNumber(9)
  void clearCommandStatus() => clearField(9);
  @$pb.TagNumber(9)
  ServerStatus ensureCommandStatus() => $_ensure(8);

  @$pb.TagNumber(10)
  $core.List<GlobalStatus> get globalStatuses => $_getList(9);

  @$pb.TagNumber(11)
  TranslatedStatusMessage get translatedStatusMessage => $_getN(10);
  @$pb.TagNumber(11)
  set translatedStatusMessage(TranslatedStatusMessage v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasTranslatedStatusMessage() => $_has(10);
  @$pb.TagNumber(11)
  void clearTranslatedStatusMessage() => clearField(11);
  @$pb.TagNumber(11)
  TranslatedStatusMessage ensureTranslatedStatusMessage() => $_ensure(10);
}

class ReportIssueRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ReportIssueRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneNumber')
    ..hasRequiredFields = false
  ;

  ReportIssueRequest._() : super();
  factory ReportIssueRequest({
    $core.String? description,
    $core.String? phoneNumber,
  }) {
    final _result = create();
    if (description != null) {
      _result.description = description;
    }
    if (phoneNumber != null) {
      _result.phoneNumber = phoneNumber;
    }
    return _result;
  }
  factory ReportIssueRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ReportIssueRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ReportIssueRequest clone() => ReportIssueRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ReportIssueRequest copyWith(void Function(ReportIssueRequest) updates) => super.copyWith((message) => updates(message as ReportIssueRequest)) as ReportIssueRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ReportIssueRequest create() => ReportIssueRequest._();
  ReportIssueRequest createEmptyInstance() => create();
  static $pb.PbList<ReportIssueRequest> createRepeated() => $pb.PbList<ReportIssueRequest>();
  @$core.pragma('dart2js:noInline')
  static ReportIssueRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ReportIssueRequest>(create);
  static ReportIssueRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get description => $_getSZ(0);
  @$pb.TagNumber(1)
  set description($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDescription() => $_has(0);
  @$pb.TagNumber(1)
  void clearDescription() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get phoneNumber => $_getSZ(1);
  @$pb.TagNumber(2)
  set phoneNumber($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPhoneNumber() => $_has(1);
  @$pb.TagNumber(2)
  void clearPhoneNumber() => clearField(2);
}

class SupportPhoneMessage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SupportPhoneMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.status_bar'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'supportPhone')
    ..hasRequiredFields = false
  ;

  SupportPhoneMessage._() : super();
  factory SupportPhoneMessage({
    $core.String? supportPhone,
  }) {
    final _result = create();
    if (supportPhone != null) {
      _result.supportPhone = supportPhone;
    }
    return _result;
  }
  factory SupportPhoneMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SupportPhoneMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SupportPhoneMessage clone() => SupportPhoneMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SupportPhoneMessage copyWith(void Function(SupportPhoneMessage) updates) => super.copyWith((message) => updates(message as SupportPhoneMessage)) as SupportPhoneMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SupportPhoneMessage create() => SupportPhoneMessage._();
  SupportPhoneMessage createEmptyInstance() => create();
  static $pb.PbList<SupportPhoneMessage> createRepeated() => $pb.PbList<SupportPhoneMessage>();
  @$core.pragma('dart2js:noInline')
  static SupportPhoneMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SupportPhoneMessage>(create);
  static SupportPhoneMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get supportPhone => $_getSZ(0);
  @$pb.TagNumber(1)
  set supportPhone($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSupportPhone() => $_has(0);
  @$pb.TagNumber(1)
  void clearSupportPhone() => clearField(1);
}

