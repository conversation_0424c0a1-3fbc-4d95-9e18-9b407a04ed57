///
//  Generated code. Do not modify.
//  source: frontend/data_capture.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use uploadMethodDescriptor instead')
const UploadMethod$json = const {
  '1': 'UploadMethod',
  '2': const [
    const {'1': 'WIRELESS', '2': 0},
    const {'1': 'USB', '2': 1},
  ],
};

/// Descriptor for `UploadMethod`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List uploadMethodDescriptor = $convert.base64Decode('CgxVcGxvYWRNZXRob2QSDAoIV0lSRUxFU1MQABIHCgNVU0IQAQ==');
@$core.Deprecated('Use procedureStepDescriptor instead')
const ProcedureStep$json = const {
  '1': 'ProcedureStep',
  '2': const [
    const {'1': 'NEW', '2': 0},
    const {'1': 'CAPTURING', '2': 1},
    const {'1': 'CAPTURE_PAUSED', '2': 2},
    const {'1': 'CAPTURE_COMPLETE', '2': 3},
    const {'1': 'UPLOADING_WIRELESS', '2': 4},
    const {'1': 'UPLOADING_WIRELESS_PAUSED', '2': 5},
    const {'1': 'UPLOADING_USB', '2': 6},
    const {'1': 'UPLOADING_USB_PAUSED', '2': 7},
    const {'1': 'UPLOADING_COMPLETE', '2': 8},
  ],
};

/// Descriptor for `ProcedureStep`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List procedureStepDescriptor = $convert.base64Decode('Cg1Qcm9jZWR1cmVTdGVwEgcKA05FVxAAEg0KCUNBUFRVUklORxABEhIKDkNBUFRVUkVfUEFVU0VEEAISFAoQQ0FQVFVSRV9DT01QTEVURRADEhYKElVQTE9BRElOR19XSVJFTEVTUxAEEh0KGVVQTE9BRElOR19XSVJFTEVTU19QQVVTRUQQBRIRCg1VUExPQURJTkdfVVNCEAYSGAoUVVBMT0FESU5HX1VTQl9QQVVTRUQQBxIWChJVUExPQURJTkdfQ09NUExFVEUQCA==');
@$core.Deprecated('Use dataCaptureRateDescriptor instead')
const DataCaptureRate$json = const {
  '1': 'DataCaptureRate',
  '2': const [
    const {'1': 'rate', '3': 1, '4': 1, '5': 1, '10': 'rate'},
  ],
};

/// Descriptor for `DataCaptureRate`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dataCaptureRateDescriptor = $convert.base64Decode('Cg9EYXRhQ2FwdHVyZVJhdGUSEgoEcmF0ZRgBIAEoAVIEcmF0ZQ==');
@$core.Deprecated('Use dataCaptureStateDescriptor instead')
const DataCaptureState$json = const {
  '1': 'DataCaptureState',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'images_taken', '3': 2, '4': 1, '5': 13, '10': 'imagesTaken'},
    const {'1': 'target_images_taken', '3': 3, '4': 1, '5': 13, '10': 'targetImagesTaken'},
    const {'1': 'estimated_capture_remaining_time_ms', '3': 4, '4': 1, '5': 4, '10': 'estimatedCaptureRemainingTimeMs'},
    const {'1': 'images_uploaded', '3': 5, '4': 1, '5': 13, '10': 'imagesUploaded'},
    const {'1': 'target_images_uploaded', '3': 6, '4': 1, '5': 13, '10': 'targetImagesUploaded'},
    const {'1': 'estimated_upload_remaining_time_ms', '3': 7, '4': 1, '5': 4, '10': 'estimatedUploadRemainingTimeMs'},
    const {'1': 'rate', '3': 8, '4': 1, '5': 11, '6': '.carbon.frontend.data_capture.DataCaptureRate', '10': 'rate'},
    const {'1': 'wireless_upload_available', '3': 9, '4': 1, '5': 8, '10': 'wirelessUploadAvailable'},
    const {'1': 'usb_storage_connected', '3': 10, '4': 1, '5': 8, '10': 'usbStorageConnected'},
    const {'1': 'capture_status', '3': 11, '4': 1, '5': 9, '10': 'captureStatus'},
    const {'1': 'upload_status', '3': 12, '4': 1, '5': 9, '10': 'uploadStatus'},
    const {'1': 'session_name', '3': 13, '4': 1, '5': 9, '10': 'sessionName'},
    const {'1': 'step', '3': 14, '4': 1, '5': 14, '6': '.carbon.frontend.data_capture.ProcedureStep', '10': 'step'},
    const {'1': 'crop', '3': 15, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'error_message', '3': 16, '4': 1, '5': 9, '10': 'errorMessage'},
    const {'1': 'crop_id', '3': 17, '4': 1, '5': 9, '10': 'cropId'},
  ],
};

/// Descriptor for `DataCaptureState`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dataCaptureStateDescriptor = $convert.base64Decode('ChBEYXRhQ2FwdHVyZVN0YXRlEiYKAnRzGAEgASgLMhYuY2FyYm9uLnV0aWwuVGltZXN0YW1wUgJ0cxIhCgxpbWFnZXNfdGFrZW4YAiABKA1SC2ltYWdlc1Rha2VuEi4KE3RhcmdldF9pbWFnZXNfdGFrZW4YAyABKA1SEXRhcmdldEltYWdlc1Rha2VuEkwKI2VzdGltYXRlZF9jYXB0dXJlX3JlbWFpbmluZ190aW1lX21zGAQgASgEUh9lc3RpbWF0ZWRDYXB0dXJlUmVtYWluaW5nVGltZU1zEicKD2ltYWdlc191cGxvYWRlZBgFIAEoDVIOaW1hZ2VzVXBsb2FkZWQSNAoWdGFyZ2V0X2ltYWdlc191cGxvYWRlZBgGIAEoDVIUdGFyZ2V0SW1hZ2VzVXBsb2FkZWQSSgoiZXN0aW1hdGVkX3VwbG9hZF9yZW1haW5pbmdfdGltZV9tcxgHIAEoBFIeZXN0aW1hdGVkVXBsb2FkUmVtYWluaW5nVGltZU1zEkEKBHJhdGUYCCABKAsyLS5jYXJib24uZnJvbnRlbmQuZGF0YV9jYXB0dXJlLkRhdGFDYXB0dXJlUmF0ZVIEcmF0ZRI6Chl3aXJlbGVzc191cGxvYWRfYXZhaWxhYmxlGAkgASgIUhd3aXJlbGVzc1VwbG9hZEF2YWlsYWJsZRIyChV1c2Jfc3RvcmFnZV9jb25uZWN0ZWQYCiABKAhSE3VzYlN0b3JhZ2VDb25uZWN0ZWQSJQoOY2FwdHVyZV9zdGF0dXMYCyABKAlSDWNhcHR1cmVTdGF0dXMSIwoNdXBsb2FkX3N0YXR1cxgMIAEoCVIMdXBsb2FkU3RhdHVzEiEKDHNlc3Npb25fbmFtZRgNIAEoCVILc2Vzc2lvbk5hbWUSPwoEc3RlcBgOIAEoDjIrLmNhcmJvbi5mcm9udGVuZC5kYXRhX2NhcHR1cmUuUHJvY2VkdXJlU3RlcFIEc3RlcBISCgRjcm9wGA8gASgJUgRjcm9wEiMKDWVycm9yX21lc3NhZ2UYECABKAlSDGVycm9yTWVzc2FnZRIXCgdjcm9wX2lkGBEgASgJUgZjcm9wSWQ=');
@$core.Deprecated('Use dataCaptureSessionDescriptor instead')
const DataCaptureSession$json = const {
  '1': 'DataCaptureSession',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
  ],
};

/// Descriptor for `DataCaptureSession`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dataCaptureSessionDescriptor = $convert.base64Decode('ChJEYXRhQ2FwdHVyZVNlc3Npb24SEgoEbmFtZRgBIAEoCVIEbmFtZQ==');
@$core.Deprecated('Use startDataCaptureRequestDescriptor instead')
const StartDataCaptureRequest$json = const {
  '1': 'StartDataCaptureRequest',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'rate', '3': 2, '4': 1, '5': 1, '10': 'rate'},
    const {'1': 'crop', '3': 3, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'crop_id', '3': 4, '4': 1, '5': 9, '10': 'cropId'},
    const {'1': 'snap_capture', '3': 5, '4': 1, '5': 8, '10': 'snapCapture'},
  ],
};

/// Descriptor for `StartDataCaptureRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startDataCaptureRequestDescriptor = $convert.base64Decode('ChdTdGFydERhdGFDYXB0dXJlUmVxdWVzdBISCgRuYW1lGAEgASgJUgRuYW1lEhIKBHJhdGUYAiABKAFSBHJhdGUSEgoEY3JvcBgDIAEoCVIEY3JvcBIXCgdjcm9wX2lkGAQgASgJUgZjcm9wSWQSIQoMc25hcF9jYXB0dXJlGAUgASgIUgtzbmFwQ2FwdHVyZQ==');
@$core.Deprecated('Use snapImagesRequestDescriptor instead')
const SnapImagesRequest$json = const {
  '1': 'SnapImagesRequest',
  '2': const [
    const {'1': 'crop', '3': 1, '4': 1, '5': 9, '10': 'crop'},
    const {'1': 'crop_id', '3': 2, '4': 1, '5': 9, '10': 'cropId'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'camId', '17': true},
    const {'1': 'timestamp_ms', '3': 4, '4': 1, '5': 3, '9': 1, '10': 'timestampMs', '17': true},
    const {'1': 'session_name', '3': 5, '4': 1, '5': 9, '10': 'sessionName'},
  ],
  '8': const [
    const {'1': '_cam_id'},
    const {'1': '_timestamp_ms'},
  ],
};

/// Descriptor for `SnapImagesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapImagesRequestDescriptor = $convert.base64Decode('ChFTbmFwSW1hZ2VzUmVxdWVzdBISCgRjcm9wGAEgASgJUgRjcm9wEhcKB2Nyb3BfaWQYAiABKAlSBmNyb3BJZBIaCgZjYW1faWQYAyABKAlIAFIFY2FtSWSIAQESJgoMdGltZXN0YW1wX21zGAQgASgDSAFSC3RpbWVzdGFtcE1ziAEBEiEKDHNlc3Npb25fbmFtZRgFIAEoCVILc2Vzc2lvbk5hbWVCCQoHX2NhbV9pZEIPCg1fdGltZXN0YW1wX21z');
@$core.Deprecated('Use sessionDescriptor instead')
const Session$json = const {
  '1': 'Session',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'images_remaining', '3': 2, '4': 1, '5': 13, '10': 'imagesRemaining'},
    const {'1': 'is_uploading', '3': 3, '4': 1, '5': 8, '10': 'isUploading'},
    const {'1': 'has_completed', '3': 4, '4': 1, '5': 8, '10': 'hasCompleted'},
    const {'1': 'is_capturing', '3': 5, '4': 1, '5': 8, '10': 'isCapturing'},
  ],
};

/// Descriptor for `Session`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sessionDescriptor = $convert.base64Decode('CgdTZXNzaW9uEhIKBG5hbWUYASABKAlSBG5hbWUSKQoQaW1hZ2VzX3JlbWFpbmluZxgCIAEoDVIPaW1hZ2VzUmVtYWluaW5nEiEKDGlzX3VwbG9hZGluZxgDIAEoCFILaXNVcGxvYWRpbmcSIwoNaGFzX2NvbXBsZXRlZBgEIAEoCFIMaGFzQ29tcGxldGVkEiEKDGlzX2NhcHR1cmluZxgFIAEoCFILaXNDYXB0dXJpbmc=');
@$core.Deprecated('Use availableSessionResponseDescriptor instead')
const AvailableSessionResponse$json = const {
  '1': 'AvailableSessionResponse',
  '2': const [
    const {'1': 'sessions', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.data_capture.Session', '10': 'sessions'},
  ],
};

/// Descriptor for `AvailableSessionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List availableSessionResponseDescriptor = $convert.base64Decode('ChhBdmFpbGFibGVTZXNzaW9uUmVzcG9uc2USQQoIc2Vzc2lvbnMYASADKAsyJS5jYXJib24uZnJvbnRlbmQuZGF0YV9jYXB0dXJlLlNlc3Npb25SCHNlc3Npb25z');
@$core.Deprecated('Use sessionNameDescriptor instead')
const SessionName$json = const {
  '1': 'SessionName',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
  ],
};

/// Descriptor for `SessionName`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sessionNameDescriptor = $convert.base64Decode('CgtTZXNzaW9uTmFtZRISCgRuYW1lGAEgASgJUgRuYW1l');
@$core.Deprecated('Use regularCaptureStatusDescriptor instead')
const RegularCaptureStatus$json = const {
  '1': 'RegularCaptureStatus',
  '2': const [
    const {'1': 'uploaded', '3': 1, '4': 1, '5': 13, '10': 'uploaded'},
    const {'1': 'budget', '3': 2, '4': 1, '5': 13, '10': 'budget'},
    const {'1': 'last_upload_timestamp', '3': 3, '4': 1, '5': 3, '10': 'lastUploadTimestamp'},
  ],
};

/// Descriptor for `RegularCaptureStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List regularCaptureStatusDescriptor = $convert.base64Decode('ChRSZWd1bGFyQ2FwdHVyZVN0YXR1cxIaCgh1cGxvYWRlZBgBIAEoDVIIdXBsb2FkZWQSFgoGYnVkZ2V0GAIgASgNUgZidWRnZXQSMgoVbGFzdF91cGxvYWRfdGltZXN0YW1wGAMgASgDUhNsYXN0VXBsb2FkVGltZXN0YW1w');
