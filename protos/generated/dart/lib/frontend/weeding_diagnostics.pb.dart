///
//  Generated code. Do not modify.
//  source: frontend/weeding_diagnostics.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../weed_tracking/weed_tracking.pb.dart' as $4;
import '../thinning/thinning.pb.dart' as $66;
import '../core/controls/exterminator/controllers/aimbot/process/aimbot.pb.dart' as $5;
import '../util/util.pb.dart' as $1;
import 'image_stream.pb.dart' as $21;
import '../cv/runtime/cv_runtime.pb.dart' as $6;
import '../recorder/recorder.pb.dart' as $67;

import 'weeding_diagnostics.pbenum.dart';

export 'weeding_diagnostics.pbenum.dart';

class RecordWeedingDiagnosticsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RecordWeedingDiagnosticsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ttlSec', $pb.PbFieldType.OU3)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropImagesPerSec', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedImagesPerSec', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  RecordWeedingDiagnosticsRequest._() : super();
  factory RecordWeedingDiagnosticsRequest({
    $core.String? name,
    $core.int? ttlSec,
    $core.double? cropImagesPerSec,
    $core.double? weedImagesPerSec,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (ttlSec != null) {
      _result.ttlSec = ttlSec;
    }
    if (cropImagesPerSec != null) {
      _result.cropImagesPerSec = cropImagesPerSec;
    }
    if (weedImagesPerSec != null) {
      _result.weedImagesPerSec = weedImagesPerSec;
    }
    return _result;
  }
  factory RecordWeedingDiagnosticsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RecordWeedingDiagnosticsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RecordWeedingDiagnosticsRequest clone() => RecordWeedingDiagnosticsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RecordWeedingDiagnosticsRequest copyWith(void Function(RecordWeedingDiagnosticsRequest) updates) => super.copyWith((message) => updates(message as RecordWeedingDiagnosticsRequest)) as RecordWeedingDiagnosticsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RecordWeedingDiagnosticsRequest create() => RecordWeedingDiagnosticsRequest._();
  RecordWeedingDiagnosticsRequest createEmptyInstance() => create();
  static $pb.PbList<RecordWeedingDiagnosticsRequest> createRepeated() => $pb.PbList<RecordWeedingDiagnosticsRequest>();
  @$core.pragma('dart2js:noInline')
  static RecordWeedingDiagnosticsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RecordWeedingDiagnosticsRequest>(create);
  static RecordWeedingDiagnosticsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get ttlSec => $_getIZ(1);
  @$pb.TagNumber(2)
  set ttlSec($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTtlSec() => $_has(1);
  @$pb.TagNumber(2)
  void clearTtlSec() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get cropImagesPerSec => $_getN(2);
  @$pb.TagNumber(3)
  set cropImagesPerSec($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCropImagesPerSec() => $_has(2);
  @$pb.TagNumber(3)
  void clearCropImagesPerSec() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get weedImagesPerSec => $_getN(3);
  @$pb.TagNumber(4)
  set weedImagesPerSec($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasWeedImagesPerSec() => $_has(3);
  @$pb.TagNumber(4)
  void clearWeedImagesPerSec() => clearField(4);
}

class GetCurrentTrajectoriesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCurrentTrajectoriesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'includeDetailedMetadata')
    ..hasRequiredFields = false
  ;

  GetCurrentTrajectoriesRequest._() : super();
  factory GetCurrentTrajectoriesRequest({
    $core.int? rowId,
    $core.bool? includeDetailedMetadata,
  }) {
    final _result = create();
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (includeDetailedMetadata != null) {
      _result.includeDetailedMetadata = includeDetailedMetadata;
    }
    return _result;
  }
  factory GetCurrentTrajectoriesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCurrentTrajectoriesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCurrentTrajectoriesRequest clone() => GetCurrentTrajectoriesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCurrentTrajectoriesRequest copyWith(void Function(GetCurrentTrajectoriesRequest) updates) => super.copyWith((message) => updates(message as GetCurrentTrajectoriesRequest)) as GetCurrentTrajectoriesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCurrentTrajectoriesRequest create() => GetCurrentTrajectoriesRequest._();
  GetCurrentTrajectoriesRequest createEmptyInstance() => create();
  static $pb.PbList<GetCurrentTrajectoriesRequest> createRepeated() => $pb.PbList<GetCurrentTrajectoriesRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCurrentTrajectoriesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCurrentTrajectoriesRequest>(create);
  static GetCurrentTrajectoriesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowId => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowId() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get includeDetailedMetadata => $_getBF(1);
  @$pb.TagNumber(2)
  set includeDetailedMetadata($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIncludeDetailedMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearIncludeDetailedMetadata() => clearField(2);
}

class GetRecordingsListRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRecordingsListRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetRecordingsListRequest._() : super();
  factory GetRecordingsListRequest() => create();
  factory GetRecordingsListRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRecordingsListRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRecordingsListRequest clone() => GetRecordingsListRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRecordingsListRequest copyWith(void Function(GetRecordingsListRequest) updates) => super.copyWith((message) => updates(message as GetRecordingsListRequest)) as GetRecordingsListRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRecordingsListRequest create() => GetRecordingsListRequest._();
  GetRecordingsListRequest createEmptyInstance() => create();
  static $pb.PbList<GetRecordingsListRequest> createRepeated() => $pb.PbList<GetRecordingsListRequest>();
  @$core.pragma('dart2js:noInline')
  static GetRecordingsListRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRecordingsListRequest>(create);
  static GetRecordingsListRequest? _defaultInstance;
}

class GetRecordingsListResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRecordingsListResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  GetRecordingsListResponse._() : super();
  factory GetRecordingsListResponse({
    $core.Iterable<$core.String>? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name.addAll(name);
    }
    return _result;
  }
  factory GetRecordingsListResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRecordingsListResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRecordingsListResponse clone() => GetRecordingsListResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRecordingsListResponse copyWith(void Function(GetRecordingsListResponse) updates) => super.copyWith((message) => updates(message as GetRecordingsListResponse)) as GetRecordingsListResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRecordingsListResponse create() => GetRecordingsListResponse._();
  GetRecordingsListResponse createEmptyInstance() => create();
  static $pb.PbList<GetRecordingsListResponse> createRepeated() => $pb.PbList<GetRecordingsListResponse>();
  @$core.pragma('dart2js:noInline')
  static GetRecordingsListResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRecordingsListResponse>(create);
  static GetRecordingsListResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get name => $_getList(0);
}

class GetSnapshotRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetSnapshotRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshotNumber', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetSnapshotRequest._() : super();
  factory GetSnapshotRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.int? snapshotNumber,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (snapshotNumber != null) {
      _result.snapshotNumber = snapshotNumber;
    }
    return _result;
  }
  factory GetSnapshotRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetSnapshotRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetSnapshotRequest clone() => GetSnapshotRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetSnapshotRequest copyWith(void Function(GetSnapshotRequest) updates) => super.copyWith((message) => updates(message as GetSnapshotRequest)) as GetSnapshotRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetSnapshotRequest create() => GetSnapshotRequest._();
  GetSnapshotRequest createEmptyInstance() => create();
  static $pb.PbList<GetSnapshotRequest> createRepeated() => $pb.PbList<GetSnapshotRequest>();
  @$core.pragma('dart2js:noInline')
  static GetSnapshotRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetSnapshotRequest>(create);
  static GetSnapshotRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get snapshotNumber => $_getIZ(2);
  @$pb.TagNumber(3)
  set snapshotNumber($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSnapshotNumber() => $_has(2);
  @$pb.TagNumber(3)
  void clearSnapshotNumber() => clearField(3);
}

class GetSnapshotResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetSnapshotResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<$4.DiagnosticsSnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshot', subBuilder: $4.DiagnosticsSnapshot.create)
    ..hasRequiredFields = false
  ;

  GetSnapshotResponse._() : super();
  factory GetSnapshotResponse({
    $4.DiagnosticsSnapshot? snapshot,
  }) {
    final _result = create();
    if (snapshot != null) {
      _result.snapshot = snapshot;
    }
    return _result;
  }
  factory GetSnapshotResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetSnapshotResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetSnapshotResponse clone() => GetSnapshotResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetSnapshotResponse copyWith(void Function(GetSnapshotResponse) updates) => super.copyWith((message) => updates(message as GetSnapshotResponse)) as GetSnapshotResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetSnapshotResponse create() => GetSnapshotResponse._();
  GetSnapshotResponse createEmptyInstance() => create();
  static $pb.PbList<GetSnapshotResponse> createRepeated() => $pb.PbList<GetSnapshotResponse>();
  @$core.pragma('dart2js:noInline')
  static GetSnapshotResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetSnapshotResponse>(create);
  static GetSnapshotResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $4.DiagnosticsSnapshot get snapshot => $_getN(0);
  @$pb.TagNumber(1)
  set snapshot($4.DiagnosticsSnapshot v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSnapshot() => $_has(0);
  @$pb.TagNumber(1)
  void clearSnapshot() => clearField(1);
  @$pb.TagNumber(1)
  $4.DiagnosticsSnapshot ensureSnapshot() => $_ensure(0);
}

class OpenRecordingRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'OpenRecordingRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..hasRequiredFields = false
  ;

  OpenRecordingRequest._() : super();
  factory OpenRecordingRequest({
    $core.String? recordingName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    return _result;
  }
  factory OpenRecordingRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OpenRecordingRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OpenRecordingRequest clone() => OpenRecordingRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OpenRecordingRequest copyWith(void Function(OpenRecordingRequest) updates) => super.copyWith((message) => updates(message as OpenRecordingRequest)) as OpenRecordingRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static OpenRecordingRequest create() => OpenRecordingRequest._();
  OpenRecordingRequest createEmptyInstance() => create();
  static $pb.PbList<OpenRecordingRequest> createRepeated() => $pb.PbList<OpenRecordingRequest>();
  @$core.pragma('dart2js:noInline')
  static OpenRecordingRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OpenRecordingRequest>(create);
  static OpenRecordingRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);
}

class TrajectoriesWithImages extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectoriesWithImages', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..p<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ids', $pb.PbFieldType.KU3)
    ..hasRequiredFields = false
  ;

  TrajectoriesWithImages._() : super();
  factory TrajectoriesWithImages({
    $core.Iterable<$core.int>? ids,
  }) {
    final _result = create();
    if (ids != null) {
      _result.ids.addAll(ids);
    }
    return _result;
  }
  factory TrajectoriesWithImages.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectoriesWithImages.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectoriesWithImages clone() => TrajectoriesWithImages()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectoriesWithImages copyWith(void Function(TrajectoriesWithImages) updates) => super.copyWith((message) => updates(message as TrajectoriesWithImages)) as TrajectoriesWithImages; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectoriesWithImages create() => TrajectoriesWithImages._();
  TrajectoriesWithImages createEmptyInstance() => create();
  static $pb.PbList<TrajectoriesWithImages> createRepeated() => $pb.PbList<TrajectoriesWithImages>();
  @$core.pragma('dart2js:noInline')
  static TrajectoriesWithImages getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectoriesWithImages>(create);
  static TrajectoriesWithImages? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get ids => $_getList(0);
}

class PredictImages extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictImages', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'names')
    ..hasRequiredFields = false
  ;

  PredictImages._() : super();
  factory PredictImages({
    $core.Iterable<$core.String>? names,
  }) {
    final _result = create();
    if (names != null) {
      _result.names.addAll(names);
    }
    return _result;
  }
  factory PredictImages.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictImages.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictImages clone() => PredictImages()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictImages copyWith(void Function(PredictImages) updates) => super.copyWith((message) => updates(message as PredictImages)) as PredictImages; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictImages create() => PredictImages._();
  PredictImages createEmptyInstance() => create();
  static $pb.PbList<PredictImages> createRepeated() => $pb.PbList<PredictImages>();
  @$core.pragma('dart2js:noInline')
  static PredictImages getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictImages>(create);
  static PredictImages? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get names => $_getList(0);
}

class PredictImagesPerCam extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictImagesPerCam', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..m<$core.int, PredictImages>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'images', entryClassName: 'PredictImagesPerCam.ImagesEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OM, valueCreator: PredictImages.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..hasRequiredFields = false
  ;

  PredictImagesPerCam._() : super();
  factory PredictImagesPerCam({
    $core.Map<$core.int, PredictImages>? images,
  }) {
    final _result = create();
    if (images != null) {
      _result.images.addAll(images);
    }
    return _result;
  }
  factory PredictImagesPerCam.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictImagesPerCam.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictImagesPerCam clone() => PredictImagesPerCam()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictImagesPerCam copyWith(void Function(PredictImagesPerCam) updates) => super.copyWith((message) => updates(message as PredictImagesPerCam)) as PredictImagesPerCam; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictImagesPerCam create() => PredictImagesPerCam._();
  PredictImagesPerCam createEmptyInstance() => create();
  static $pb.PbList<PredictImagesPerCam> createRepeated() => $pb.PbList<PredictImagesPerCam>();
  @$core.pragma('dart2js:noInline')
  static PredictImagesPerCam getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictImagesPerCam>(create);
  static PredictImagesPerCam? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.int, PredictImages> get images => $_getMap(0);
}

class OpenRecordingResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'OpenRecordingResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..m<$core.int, $core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numSnapshotsPerRow', entryClassName: 'OpenRecordingResponse.NumSnapshotsPerRowEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OU3, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..aOM<StaticRecordingData>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingData', subBuilder: StaticRecordingData.create)
    ..m<$core.int, TrajectoriesWithImages>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryImagesPerRow', entryClassName: 'OpenRecordingResponse.TrajectoryImagesPerRowEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OM, valueCreator: TrajectoriesWithImages.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..m<$core.int, PredictImagesPerCam>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictImagesPerRow', entryClassName: 'OpenRecordingResponse.PredictImagesPerRowEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OM, valueCreator: PredictImagesPerCam.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..hasRequiredFields = false
  ;

  OpenRecordingResponse._() : super();
  factory OpenRecordingResponse({
    $core.Map<$core.int, $core.int>? numSnapshotsPerRow,
    StaticRecordingData? recordingData,
    $core.Map<$core.int, TrajectoriesWithImages>? trajectoryImagesPerRow,
    $core.Map<$core.int, PredictImagesPerCam>? predictImagesPerRow,
  }) {
    final _result = create();
    if (numSnapshotsPerRow != null) {
      _result.numSnapshotsPerRow.addAll(numSnapshotsPerRow);
    }
    if (recordingData != null) {
      _result.recordingData = recordingData;
    }
    if (trajectoryImagesPerRow != null) {
      _result.trajectoryImagesPerRow.addAll(trajectoryImagesPerRow);
    }
    if (predictImagesPerRow != null) {
      _result.predictImagesPerRow.addAll(predictImagesPerRow);
    }
    return _result;
  }
  factory OpenRecordingResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OpenRecordingResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OpenRecordingResponse clone() => OpenRecordingResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OpenRecordingResponse copyWith(void Function(OpenRecordingResponse) updates) => super.copyWith((message) => updates(message as OpenRecordingResponse)) as OpenRecordingResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static OpenRecordingResponse create() => OpenRecordingResponse._();
  OpenRecordingResponse createEmptyInstance() => create();
  static $pb.PbList<OpenRecordingResponse> createRepeated() => $pb.PbList<OpenRecordingResponse>();
  @$core.pragma('dart2js:noInline')
  static OpenRecordingResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OpenRecordingResponse>(create);
  static OpenRecordingResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.int, $core.int> get numSnapshotsPerRow => $_getMap(0);

  @$pb.TagNumber(2)
  StaticRecordingData get recordingData => $_getN(1);
  @$pb.TagNumber(2)
  set recordingData(StaticRecordingData v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasRecordingData() => $_has(1);
  @$pb.TagNumber(2)
  void clearRecordingData() => clearField(2);
  @$pb.TagNumber(2)
  StaticRecordingData ensureRecordingData() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.Map<$core.int, TrajectoriesWithImages> get trajectoryImagesPerRow => $_getMap(2);

  @$pb.TagNumber(4)
  $core.Map<$core.int, PredictImagesPerCam> get predictImagesPerRow => $_getMap(3);
}

class DeleteRecordingRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteRecordingRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..hasRequiredFields = false
  ;

  DeleteRecordingRequest._() : super();
  factory DeleteRecordingRequest({
    $core.String? recordingName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    return _result;
  }
  factory DeleteRecordingRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteRecordingRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteRecordingRequest clone() => DeleteRecordingRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteRecordingRequest copyWith(void Function(DeleteRecordingRequest) updates) => super.copyWith((message) => updates(message as DeleteRecordingRequest)) as DeleteRecordingRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteRecordingRequest create() => DeleteRecordingRequest._();
  DeleteRecordingRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteRecordingRequest> createRepeated() => $pb.PbList<DeleteRecordingRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteRecordingRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteRecordingRequest>(create);
  static DeleteRecordingRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);
}

class ConfigNodeSnapshot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ConfigNodeSnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..m<$core.String, $core.String>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'values', entryClassName: 'ConfigNodeSnapshot.ValuesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..m<$core.String, ConfigNodeSnapshot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'childNodes', entryClassName: 'ConfigNodeSnapshot.ChildNodesEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: ConfigNodeSnapshot.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..hasRequiredFields = false
  ;

  ConfigNodeSnapshot._() : super();
  factory ConfigNodeSnapshot({
    $core.Map<$core.String, $core.String>? values,
    $core.Map<$core.String, ConfigNodeSnapshot>? childNodes,
  }) {
    final _result = create();
    if (values != null) {
      _result.values.addAll(values);
    }
    if (childNodes != null) {
      _result.childNodes.addAll(childNodes);
    }
    return _result;
  }
  factory ConfigNodeSnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ConfigNodeSnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ConfigNodeSnapshot clone() => ConfigNodeSnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ConfigNodeSnapshot copyWith(void Function(ConfigNodeSnapshot) updates) => super.copyWith((message) => updates(message as ConfigNodeSnapshot)) as ConfigNodeSnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ConfigNodeSnapshot create() => ConfigNodeSnapshot._();
  ConfigNodeSnapshot createEmptyInstance() => create();
  static $pb.PbList<ConfigNodeSnapshot> createRepeated() => $pb.PbList<ConfigNodeSnapshot>();
  @$core.pragma('dart2js:noInline')
  static ConfigNodeSnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ConfigNodeSnapshot>(create);
  static ConfigNodeSnapshot? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, $core.String> get values => $_getMap(0);

  @$pb.TagNumber(2)
  $core.Map<$core.String, ConfigNodeSnapshot> get childNodes => $_getMap(1);
}

class CameraDimensions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CameraDimensions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  CameraDimensions._() : super();
  factory CameraDimensions({
    $core.int? width,
    $core.int? height,
  }) {
    final _result = create();
    if (width != null) {
      _result.width = width;
    }
    if (height != null) {
      _result.height = height;
    }
    return _result;
  }
  factory CameraDimensions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CameraDimensions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CameraDimensions clone() => CameraDimensions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CameraDimensions copyWith(void Function(CameraDimensions) updates) => super.copyWith((message) => updates(message as CameraDimensions)) as CameraDimensions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CameraDimensions create() => CameraDimensions._();
  CameraDimensions createEmptyInstance() => create();
  static $pb.PbList<CameraDimensions> createRepeated() => $pb.PbList<CameraDimensions>();
  @$core.pragma('dart2js:noInline')
  static CameraDimensions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CameraDimensions>(create);
  static CameraDimensions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get width => $_getIZ(0);
  @$pb.TagNumber(1)
  set width($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWidth() => $_has(0);
  @$pb.TagNumber(1)
  void clearWidth() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get height => $_getIZ(1);
  @$pb.TagNumber(2)
  set height($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHeight() => $_has(1);
  @$pb.TagNumber(2)
  void clearHeight() => clearField(2);
}

class RowCameras extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowCameras', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..m<$core.String, CameraDimensions>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cams', entryClassName: 'RowCameras.CamsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: CameraDimensions.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..hasRequiredFields = false
  ;

  RowCameras._() : super();
  factory RowCameras({
    $core.Map<$core.String, CameraDimensions>? cams,
  }) {
    final _result = create();
    if (cams != null) {
      _result.cams.addAll(cams);
    }
    return _result;
  }
  factory RowCameras.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowCameras.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowCameras clone() => RowCameras()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowCameras copyWith(void Function(RowCameras) updates) => super.copyWith((message) => updates(message as RowCameras)) as RowCameras; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowCameras create() => RowCameras._();
  RowCameras createEmptyInstance() => create();
  static $pb.PbList<RowCameras> createRepeated() => $pb.PbList<RowCameras>();
  @$core.pragma('dart2js:noInline')
  static RowCameras getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowCameras>(create);
  static RowCameras? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, CameraDimensions> get cams => $_getMap(0);
}

class StaticRecordingData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StaticRecordingData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..m<$core.String, $core.bool>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lasersEnabled', entryClassName: 'StaticRecordingData.LasersEnabledEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OB, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..aOM<ConfigNodeSnapshot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rootConfig', subBuilder: ConfigNodeSnapshot.create)
    ..p<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowsRecorded', $pb.PbFieldType.K3)
    ..m<$core.int, $5.GetDimensionsResponse>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowDimensions', entryClassName: 'StaticRecordingData.RowDimensionsEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OM, valueCreator: $5.GetDimensionsResponse.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..m<$core.int, $core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropSafetyRadiusMmPerRow', entryClassName: 'StaticRecordingData.CropSafetyRadiusMmPerRowEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..aOM<$66.ConfigDefinition>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningConfig', subBuilder: $66.ConfigDefinition.create)
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingTimestamp')
    ..m<$core.int, RowCameras>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowCameras', entryClassName: 'StaticRecordingData.RowCamerasEntry', keyFieldType: $pb.PbFieldType.OU3, valueFieldType: $pb.PbFieldType.OM, valueCreator: RowCameras.create, packageName: const $pb.PackageName('carbon.frontend.weeding_diagnostics'))
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wheelDiameterBackLeftIn', $pb.PbFieldType.OF)
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wheelDiameterBackRightIn', $pb.PbFieldType.OF)
    ..a<$core.double>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wheelDiameterFrontLeftIn', $pb.PbFieldType.OF)
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wheelDiameterFrontRightIn', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  StaticRecordingData._() : super();
  factory StaticRecordingData({
    $core.Map<$core.String, $core.bool>? lasersEnabled,
    ConfigNodeSnapshot? rootConfig,
    $core.Iterable<$core.int>? rowsRecorded,
    $core.Map<$core.int, $5.GetDimensionsResponse>? rowDimensions,
    $core.Map<$core.int, $core.double>? cropSafetyRadiusMmPerRow,
    $66.ConfigDefinition? thinningConfig,
    $fixnum.Int64? recordingTimestamp,
    $core.Map<$core.int, RowCameras>? rowCameras,
    $core.double? weedPointThreshold,
    $core.double? cropPointThreshold,
    $core.double? wheelDiameterBackLeftIn,
    $core.double? wheelDiameterBackRightIn,
    $core.double? wheelDiameterFrontLeftIn,
    $core.double? wheelDiameterFrontRightIn,
  }) {
    final _result = create();
    if (lasersEnabled != null) {
      _result.lasersEnabled.addAll(lasersEnabled);
    }
    if (rootConfig != null) {
      _result.rootConfig = rootConfig;
    }
    if (rowsRecorded != null) {
      _result.rowsRecorded.addAll(rowsRecorded);
    }
    if (rowDimensions != null) {
      _result.rowDimensions.addAll(rowDimensions);
    }
    if (cropSafetyRadiusMmPerRow != null) {
      _result.cropSafetyRadiusMmPerRow.addAll(cropSafetyRadiusMmPerRow);
    }
    if (thinningConfig != null) {
      _result.thinningConfig = thinningConfig;
    }
    if (recordingTimestamp != null) {
      _result.recordingTimestamp = recordingTimestamp;
    }
    if (rowCameras != null) {
      _result.rowCameras.addAll(rowCameras);
    }
    if (weedPointThreshold != null) {
      _result.weedPointThreshold = weedPointThreshold;
    }
    if (cropPointThreshold != null) {
      _result.cropPointThreshold = cropPointThreshold;
    }
    if (wheelDiameterBackLeftIn != null) {
      _result.wheelDiameterBackLeftIn = wheelDiameterBackLeftIn;
    }
    if (wheelDiameterBackRightIn != null) {
      _result.wheelDiameterBackRightIn = wheelDiameterBackRightIn;
    }
    if (wheelDiameterFrontLeftIn != null) {
      _result.wheelDiameterFrontLeftIn = wheelDiameterFrontLeftIn;
    }
    if (wheelDiameterFrontRightIn != null) {
      _result.wheelDiameterFrontRightIn = wheelDiameterFrontRightIn;
    }
    return _result;
  }
  factory StaticRecordingData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StaticRecordingData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StaticRecordingData clone() => StaticRecordingData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StaticRecordingData copyWith(void Function(StaticRecordingData) updates) => super.copyWith((message) => updates(message as StaticRecordingData)) as StaticRecordingData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StaticRecordingData create() => StaticRecordingData._();
  StaticRecordingData createEmptyInstance() => create();
  static $pb.PbList<StaticRecordingData> createRepeated() => $pb.PbList<StaticRecordingData>();
  @$core.pragma('dart2js:noInline')
  static StaticRecordingData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StaticRecordingData>(create);
  static StaticRecordingData? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, $core.bool> get lasersEnabled => $_getMap(0);

  @$pb.TagNumber(2)
  ConfigNodeSnapshot get rootConfig => $_getN(1);
  @$pb.TagNumber(2)
  set rootConfig(ConfigNodeSnapshot v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasRootConfig() => $_has(1);
  @$pb.TagNumber(2)
  void clearRootConfig() => clearField(2);
  @$pb.TagNumber(2)
  ConfigNodeSnapshot ensureRootConfig() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.List<$core.int> get rowsRecorded => $_getList(2);

  @$pb.TagNumber(4)
  $core.Map<$core.int, $5.GetDimensionsResponse> get rowDimensions => $_getMap(3);

  @$pb.TagNumber(5)
  $core.Map<$core.int, $core.double> get cropSafetyRadiusMmPerRow => $_getMap(4);

  @$pb.TagNumber(6)
  $66.ConfigDefinition get thinningConfig => $_getN(5);
  @$pb.TagNumber(6)
  set thinningConfig($66.ConfigDefinition v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasThinningConfig() => $_has(5);
  @$pb.TagNumber(6)
  void clearThinningConfig() => clearField(6);
  @$pb.TagNumber(6)
  $66.ConfigDefinition ensureThinningConfig() => $_ensure(5);

  @$pb.TagNumber(7)
  $fixnum.Int64 get recordingTimestamp => $_getI64(6);
  @$pb.TagNumber(7)
  set recordingTimestamp($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRecordingTimestamp() => $_has(6);
  @$pb.TagNumber(7)
  void clearRecordingTimestamp() => clearField(7);

  @$pb.TagNumber(8)
  $core.Map<$core.int, RowCameras> get rowCameras => $_getMap(7);

  @$pb.TagNumber(9)
  $core.double get weedPointThreshold => $_getN(8);
  @$pb.TagNumber(9)
  set weedPointThreshold($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWeedPointThreshold() => $_has(8);
  @$pb.TagNumber(9)
  void clearWeedPointThreshold() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get cropPointThreshold => $_getN(9);
  @$pb.TagNumber(10)
  set cropPointThreshold($core.double v) { $_setFloat(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCropPointThreshold() => $_has(9);
  @$pb.TagNumber(10)
  void clearCropPointThreshold() => clearField(10);

  @$pb.TagNumber(11)
  $core.double get wheelDiameterBackLeftIn => $_getN(10);
  @$pb.TagNumber(11)
  set wheelDiameterBackLeftIn($core.double v) { $_setFloat(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasWheelDiameterBackLeftIn() => $_has(10);
  @$pb.TagNumber(11)
  void clearWheelDiameterBackLeftIn() => clearField(11);

  @$pb.TagNumber(12)
  $core.double get wheelDiameterBackRightIn => $_getN(11);
  @$pb.TagNumber(12)
  set wheelDiameterBackRightIn($core.double v) { $_setFloat(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasWheelDiameterBackRightIn() => $_has(11);
  @$pb.TagNumber(12)
  void clearWheelDiameterBackRightIn() => clearField(12);

  @$pb.TagNumber(13)
  $core.double get wheelDiameterFrontLeftIn => $_getN(12);
  @$pb.TagNumber(13)
  set wheelDiameterFrontLeftIn($core.double v) { $_setFloat(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasWheelDiameterFrontLeftIn() => $_has(12);
  @$pb.TagNumber(13)
  void clearWheelDiameterFrontLeftIn() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get wheelDiameterFrontRightIn => $_getN(13);
  @$pb.TagNumber(14)
  set wheelDiameterFrontRightIn($core.double v) { $_setFloat(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasWheelDiameterFrontRightIn() => $_has(13);
  @$pb.TagNumber(14)
  void clearWheelDiameterFrontRightIn() => clearField(14);
}

class GetTrajectoryDataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTrajectoryDataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetTrajectoryDataRequest._() : super();
  factory GetTrajectoryDataRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.int? trajectoryId,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    return _result;
  }
  factory GetTrajectoryDataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrajectoryDataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrajectoryDataRequest clone() => GetTrajectoryDataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrajectoryDataRequest copyWith(void Function(GetTrajectoryDataRequest) updates) => super.copyWith((message) => updates(message as GetTrajectoryDataRequest)) as GetTrajectoryDataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryDataRequest create() => GetTrajectoryDataRequest._();
  GetTrajectoryDataRequest createEmptyInstance() => create();
  static $pb.PbList<GetTrajectoryDataRequest> createRepeated() => $pb.PbList<GetTrajectoryDataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryDataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrajectoryDataRequest>(create);
  static GetTrajectoryDataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get trajectoryId => $_getIZ(2);
  @$pb.TagNumber(3)
  set trajectoryId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTrajectoryId() => $_has(2);
  @$pb.TagNumber(3)
  void clearTrajectoryId() => clearField(3);
}

class LastSnapshot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LastSnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<$4.TrajectorySnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectory', subBuilder: $4.TrajectorySnapshot.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'diagnosticsSnapshotNumber', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  LastSnapshot._() : super();
  factory LastSnapshot({
    $4.TrajectorySnapshot? trajectory,
    $core.int? diagnosticsSnapshotNumber,
  }) {
    final _result = create();
    if (trajectory != null) {
      _result.trajectory = trajectory;
    }
    if (diagnosticsSnapshotNumber != null) {
      _result.diagnosticsSnapshotNumber = diagnosticsSnapshotNumber;
    }
    return _result;
  }
  factory LastSnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LastSnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LastSnapshot clone() => LastSnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LastSnapshot copyWith(void Function(LastSnapshot) updates) => super.copyWith((message) => updates(message as LastSnapshot)) as LastSnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LastSnapshot create() => LastSnapshot._();
  LastSnapshot createEmptyInstance() => create();
  static $pb.PbList<LastSnapshot> createRepeated() => $pb.PbList<LastSnapshot>();
  @$core.pragma('dart2js:noInline')
  static LastSnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LastSnapshot>(create);
  static LastSnapshot? _defaultInstance;

  @$pb.TagNumber(1)
  $4.TrajectorySnapshot get trajectory => $_getN(0);
  @$pb.TagNumber(1)
  set trajectory($4.TrajectorySnapshot v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrajectory() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrajectory() => clearField(1);
  @$pb.TagNumber(1)
  $4.TrajectorySnapshot ensureTrajectory() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get diagnosticsSnapshotNumber => $_getIZ(1);
  @$pb.TagNumber(2)
  set diagnosticsSnapshotNumber($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDiagnosticsSnapshotNumber() => $_has(1);
  @$pb.TagNumber(2)
  void clearDiagnosticsSnapshotNumber() => clearField(2);
}

class ExtraTrajectoryField extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ExtraTrajectoryField', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'label')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'value')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'color')
    ..hasRequiredFields = false
  ;

  ExtraTrajectoryField._() : super();
  factory ExtraTrajectoryField({
    $core.String? label,
    $core.String? value,
    $core.String? color,
  }) {
    final _result = create();
    if (label != null) {
      _result.label = label;
    }
    if (value != null) {
      _result.value = value;
    }
    if (color != null) {
      _result.color = color;
    }
    return _result;
  }
  factory ExtraTrajectoryField.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExtraTrajectoryField.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExtraTrajectoryField clone() => ExtraTrajectoryField()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExtraTrajectoryField copyWith(void Function(ExtraTrajectoryField) updates) => super.copyWith((message) => updates(message as ExtraTrajectoryField)) as ExtraTrajectoryField; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ExtraTrajectoryField create() => ExtraTrajectoryField._();
  ExtraTrajectoryField createEmptyInstance() => create();
  static $pb.PbList<ExtraTrajectoryField> createRepeated() => $pb.PbList<ExtraTrajectoryField>();
  @$core.pragma('dart2js:noInline')
  static ExtraTrajectoryField getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExtraTrajectoryField>(create);
  static ExtraTrajectoryField? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get label => $_getSZ(0);
  @$pb.TagNumber(1)
  set label($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLabel() => $_has(0);
  @$pb.TagNumber(1)
  void clearLabel() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get value => $_getSZ(1);
  @$pb.TagNumber(2)
  set value($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasValue() => $_has(1);
  @$pb.TagNumber(2)
  void clearValue() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get color => $_getSZ(2);
  @$pb.TagNumber(3)
  set color($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasColor() => $_has(2);
  @$pb.TagNumber(3)
  void clearColor() => clearField(3);
}

class TargetImage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TargetImage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2pPredictX', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2pPredictY', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  TargetImage._() : super();
  factory TargetImage({
    $core.String? name,
    $fixnum.Int64? timestamp,
    $core.int? p2pPredictX,
    $core.int? p2pPredictY,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    if (p2pPredictX != null) {
      _result.p2pPredictX = p2pPredictX;
    }
    if (p2pPredictY != null) {
      _result.p2pPredictY = p2pPredictY;
    }
    return _result;
  }
  factory TargetImage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TargetImage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TargetImage clone() => TargetImage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TargetImage copyWith(void Function(TargetImage) updates) => super.copyWith((message) => updates(message as TargetImage)) as TargetImage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TargetImage create() => TargetImage._();
  TargetImage createEmptyInstance() => create();
  static $pb.PbList<TargetImage> createRepeated() => $pb.PbList<TargetImage>();
  @$core.pragma('dart2js:noInline')
  static TargetImage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TargetImage>(create);
  static TargetImage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestamp => $_getI64(1);
  @$pb.TagNumber(2)
  set timestamp($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get p2pPredictX => $_getIZ(2);
  @$pb.TagNumber(3)
  set p2pPredictX($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasP2pPredictX() => $_has(2);
  @$pb.TagNumber(3)
  void clearP2pPredictX() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get p2pPredictY => $_getIZ(3);
  @$pb.TagNumber(4)
  set p2pPredictY($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasP2pPredictY() => $_has(3);
  @$pb.TagNumber(4)
  void clearP2pPredictY() => clearField(4);
}

class P2PPredictImage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PPredictImage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerXPx', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerYPx', $pb.PbFieldType.O3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'radiusPx', $pb.PbFieldType.O3)
    ..aOM<$1.Timestamp>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pcamId')
    ..hasRequiredFields = false
  ;

  P2PPredictImage._() : super();
  factory P2PPredictImage({
    $core.String? name,
    $core.int? centerXPx,
    $core.int? centerYPx,
    $core.int? radiusPx,
    $1.Timestamp? ts,
    $core.String? pcamId,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (centerXPx != null) {
      _result.centerXPx = centerXPx;
    }
    if (centerYPx != null) {
      _result.centerYPx = centerYPx;
    }
    if (radiusPx != null) {
      _result.radiusPx = radiusPx;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (pcamId != null) {
      _result.pcamId = pcamId;
    }
    return _result;
  }
  factory P2PPredictImage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PPredictImage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PPredictImage clone() => P2PPredictImage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PPredictImage copyWith(void Function(P2PPredictImage) updates) => super.copyWith((message) => updates(message as P2PPredictImage)) as P2PPredictImage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PPredictImage create() => P2PPredictImage._();
  P2PPredictImage createEmptyInstance() => create();
  static $pb.PbList<P2PPredictImage> createRepeated() => $pb.PbList<P2PPredictImage>();
  @$core.pragma('dart2js:noInline')
  static P2PPredictImage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PPredictImage>(create);
  static P2PPredictImage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get centerXPx => $_getIZ(1);
  @$pb.TagNumber(2)
  set centerXPx($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCenterXPx() => $_has(1);
  @$pb.TagNumber(2)
  void clearCenterXPx() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get centerYPx => $_getIZ(2);
  @$pb.TagNumber(3)
  set centerYPx($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCenterYPx() => $_has(2);
  @$pb.TagNumber(3)
  void clearCenterYPx() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get radiusPx => $_getIZ(3);
  @$pb.TagNumber(4)
  set radiusPx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRadiusPx() => $_has(3);
  @$pb.TagNumber(4)
  void clearRadiusPx() => clearField(4);

  @$pb.TagNumber(5)
  $1.Timestamp get ts => $_getN(4);
  @$pb.TagNumber(5)
  set ts($1.Timestamp v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasTs() => $_has(4);
  @$pb.TagNumber(5)
  void clearTs() => clearField(5);
  @$pb.TagNumber(5)
  $1.Timestamp ensureTs() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.String get pcamId => $_getSZ(5);
  @$pb.TagNumber(6)
  set pcamId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPcamId() => $_has(5);
  @$pb.TagNumber(6)
  void clearPcamId() => clearField(6);
}

class TrajectoryPredictImageMetadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectoryPredictImageMetadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerXPx', $pb.PbFieldType.O3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerYPx', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'radiusPx', $pb.PbFieldType.O3)
    ..aOM<$1.Timestamp>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pcamId')
    ..hasRequiredFields = false
  ;

  TrajectoryPredictImageMetadata._() : super();
  factory TrajectoryPredictImageMetadata({
    $core.int? centerXPx,
    $core.int? centerYPx,
    $core.int? radiusPx,
    $1.Timestamp? ts,
    $core.String? pcamId,
  }) {
    final _result = create();
    if (centerXPx != null) {
      _result.centerXPx = centerXPx;
    }
    if (centerYPx != null) {
      _result.centerYPx = centerYPx;
    }
    if (radiusPx != null) {
      _result.radiusPx = radiusPx;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (pcamId != null) {
      _result.pcamId = pcamId;
    }
    return _result;
  }
  factory TrajectoryPredictImageMetadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectoryPredictImageMetadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectoryPredictImageMetadata clone() => TrajectoryPredictImageMetadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectoryPredictImageMetadata copyWith(void Function(TrajectoryPredictImageMetadata) updates) => super.copyWith((message) => updates(message as TrajectoryPredictImageMetadata)) as TrajectoryPredictImageMetadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectoryPredictImageMetadata create() => TrajectoryPredictImageMetadata._();
  TrajectoryPredictImageMetadata createEmptyInstance() => create();
  static $pb.PbList<TrajectoryPredictImageMetadata> createRepeated() => $pb.PbList<TrajectoryPredictImageMetadata>();
  @$core.pragma('dart2js:noInline')
  static TrajectoryPredictImageMetadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectoryPredictImageMetadata>(create);
  static TrajectoryPredictImageMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get centerXPx => $_getIZ(0);
  @$pb.TagNumber(1)
  set centerXPx($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCenterXPx() => $_has(0);
  @$pb.TagNumber(1)
  void clearCenterXPx() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get centerYPx => $_getIZ(1);
  @$pb.TagNumber(2)
  set centerYPx($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCenterYPx() => $_has(1);
  @$pb.TagNumber(2)
  void clearCenterYPx() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get radiusPx => $_getIZ(2);
  @$pb.TagNumber(3)
  set radiusPx($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRadiusPx() => $_has(2);
  @$pb.TagNumber(3)
  void clearRadiusPx() => clearField(3);

  @$pb.TagNumber(4)
  $1.Timestamp get ts => $_getN(3);
  @$pb.TagNumber(4)
  set ts($1.Timestamp v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasTs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTs() => clearField(4);
  @$pb.TagNumber(4)
  $1.Timestamp ensureTs() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.String get pcamId => $_getSZ(4);
  @$pb.TagNumber(5)
  set pcamId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPcamId() => $_has(4);
  @$pb.TagNumber(5)
  void clearPcamId() => clearField(5);
}

class TrajectoryData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TrajectoryData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<TrajectoryPredictImageMetadata>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictImage', subBuilder: TrajectoryPredictImageMetadata.create)
    ..pc<TargetImage>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetImages', $pb.PbFieldType.PM, subBuilder: TargetImage.create)
    ..aOM<LastSnapshot>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastSnapshot', subBuilder: LastSnapshot.create)
    ..pc<ExtraTrajectoryField>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'extraFields', $pb.PbFieldType.PM, subBuilder: ExtraTrajectoryField.create)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crosshairX', $pb.PbFieldType.OU3)
    ..a<$core.int>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crosshairY', $pb.PbFieldType.OU3)
    ..pc<P2PPredictImage>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2pPredictImages', $pb.PbFieldType.PM, subBuilder: P2PPredictImage.create)
    ..hasRequiredFields = false
  ;

  TrajectoryData._() : super();
  factory TrajectoryData({
    TrajectoryPredictImageMetadata? predictImage,
    $core.Iterable<TargetImage>? targetImages,
    LastSnapshot? lastSnapshot,
    $core.Iterable<ExtraTrajectoryField>? extraFields,
    $core.int? crosshairX,
    $core.int? crosshairY,
    $core.Iterable<P2PPredictImage>? p2pPredictImages,
  }) {
    final _result = create();
    if (predictImage != null) {
      _result.predictImage = predictImage;
    }
    if (targetImages != null) {
      _result.targetImages.addAll(targetImages);
    }
    if (lastSnapshot != null) {
      _result.lastSnapshot = lastSnapshot;
    }
    if (extraFields != null) {
      _result.extraFields.addAll(extraFields);
    }
    if (crosshairX != null) {
      _result.crosshairX = crosshairX;
    }
    if (crosshairY != null) {
      _result.crosshairY = crosshairY;
    }
    if (p2pPredictImages != null) {
      _result.p2pPredictImages.addAll(p2pPredictImages);
    }
    return _result;
  }
  factory TrajectoryData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrajectoryData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrajectoryData clone() => TrajectoryData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrajectoryData copyWith(void Function(TrajectoryData) updates) => super.copyWith((message) => updates(message as TrajectoryData)) as TrajectoryData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TrajectoryData create() => TrajectoryData._();
  TrajectoryData createEmptyInstance() => create();
  static $pb.PbList<TrajectoryData> createRepeated() => $pb.PbList<TrajectoryData>();
  @$core.pragma('dart2js:noInline')
  static TrajectoryData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrajectoryData>(create);
  static TrajectoryData? _defaultInstance;

  @$pb.TagNumber(1)
  TrajectoryPredictImageMetadata get predictImage => $_getN(0);
  @$pb.TagNumber(1)
  set predictImage(TrajectoryPredictImageMetadata v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPredictImage() => $_has(0);
  @$pb.TagNumber(1)
  void clearPredictImage() => clearField(1);
  @$pb.TagNumber(1)
  TrajectoryPredictImageMetadata ensurePredictImage() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<TargetImage> get targetImages => $_getList(1);

  @$pb.TagNumber(3)
  LastSnapshot get lastSnapshot => $_getN(2);
  @$pb.TagNumber(3)
  set lastSnapshot(LastSnapshot v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasLastSnapshot() => $_has(2);
  @$pb.TagNumber(3)
  void clearLastSnapshot() => clearField(3);
  @$pb.TagNumber(3)
  LastSnapshot ensureLastSnapshot() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.List<ExtraTrajectoryField> get extraFields => $_getList(3);

  @$pb.TagNumber(5)
  $core.int get crosshairX => $_getIZ(4);
  @$pb.TagNumber(5)
  set crosshairX($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCrosshairX() => $_has(4);
  @$pb.TagNumber(5)
  void clearCrosshairX() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get crosshairY => $_getIZ(5);
  @$pb.TagNumber(6)
  set crosshairY($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCrosshairY() => $_has(5);
  @$pb.TagNumber(6)
  void clearCrosshairY() => clearField(6);

  @$pb.TagNumber(7)
  $core.List<P2PPredictImage> get p2pPredictImages => $_getList(6);
}

class GetTrajectoryPredictImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTrajectoryPredictImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetTrajectoryPredictImageRequest._() : super();
  factory GetTrajectoryPredictImageRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.int? trajectoryId,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    return _result;
  }
  factory GetTrajectoryPredictImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrajectoryPredictImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrajectoryPredictImageRequest clone() => GetTrajectoryPredictImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrajectoryPredictImageRequest copyWith(void Function(GetTrajectoryPredictImageRequest) updates) => super.copyWith((message) => updates(message as GetTrajectoryPredictImageRequest)) as GetTrajectoryPredictImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryPredictImageRequest create() => GetTrajectoryPredictImageRequest._();
  GetTrajectoryPredictImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetTrajectoryPredictImageRequest> createRepeated() => $pb.PbList<GetTrajectoryPredictImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryPredictImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrajectoryPredictImageRequest>(create);
  static GetTrajectoryPredictImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get trajectoryId => $_getIZ(2);
  @$pb.TagNumber(3)
  set trajectoryId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTrajectoryId() => $_has(2);
  @$pb.TagNumber(3)
  void clearTrajectoryId() => clearField(3);
}

class GetTrajectoryTargetImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTrajectoryTargetImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageName')
    ..hasRequiredFields = false
  ;

  GetTrajectoryTargetImageRequest._() : super();
  factory GetTrajectoryTargetImageRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.int? trajectoryId,
    $core.String? imageName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    if (imageName != null) {
      _result.imageName = imageName;
    }
    return _result;
  }
  factory GetTrajectoryTargetImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrajectoryTargetImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrajectoryTargetImageRequest clone() => GetTrajectoryTargetImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrajectoryTargetImageRequest copyWith(void Function(GetTrajectoryTargetImageRequest) updates) => super.copyWith((message) => updates(message as GetTrajectoryTargetImageRequest)) as GetTrajectoryTargetImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryTargetImageRequest create() => GetTrajectoryTargetImageRequest._();
  GetTrajectoryTargetImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetTrajectoryTargetImageRequest> createRepeated() => $pb.PbList<GetTrajectoryTargetImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTrajectoryTargetImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrajectoryTargetImageRequest>(create);
  static GetTrajectoryTargetImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get trajectoryId => $_getIZ(2);
  @$pb.TagNumber(3)
  set trajectoryId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTrajectoryId() => $_has(2);
  @$pb.TagNumber(3)
  void clearTrajectoryId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get imageName => $_getSZ(3);
  @$pb.TagNumber(4)
  set imageName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasImageName() => $_has(3);
  @$pb.TagNumber(4)
  void clearImageName() => clearField(4);
}

class ImageChunk extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ImageChunk', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageChunk', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  ImageChunk._() : super();
  factory ImageChunk({
    $core.List<$core.int>? imageChunk,
  }) {
    final _result = create();
    if (imageChunk != null) {
      _result.imageChunk = imageChunk;
    }
    return _result;
  }
  factory ImageChunk.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImageChunk.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImageChunk clone() => ImageChunk()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImageChunk copyWith(void Function(ImageChunk) updates) => super.copyWith((message) => updates(message as ImageChunk)) as ImageChunk; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ImageChunk create() => ImageChunk._();
  ImageChunk createEmptyInstance() => create();
  static $pb.PbList<ImageChunk> createRepeated() => $pb.PbList<ImageChunk>();
  @$core.pragma('dart2js:noInline')
  static ImageChunk getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImageChunk>(create);
  static ImageChunk? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get imageChunk => $_getN(0);
  @$pb.TagNumber(1)
  set imageChunk($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasImageChunk() => $_has(0);
  @$pb.TagNumber(1)
  void clearImageChunk() => clearField(1);
}

class GetPredictImageMetadataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictImageMetadataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageName')
    ..hasRequiredFields = false
  ;

  GetPredictImageMetadataRequest._() : super();
  factory GetPredictImageMetadataRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.String? imageName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (imageName != null) {
      _result.imageName = imageName;
    }
    return _result;
  }
  factory GetPredictImageMetadataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictImageMetadataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictImageMetadataRequest clone() => GetPredictImageMetadataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictImageMetadataRequest copyWith(void Function(GetPredictImageMetadataRequest) updates) => super.copyWith((message) => updates(message as GetPredictImageMetadataRequest)) as GetPredictImageMetadataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictImageMetadataRequest create() => GetPredictImageMetadataRequest._();
  GetPredictImageMetadataRequest createEmptyInstance() => create();
  static $pb.PbList<GetPredictImageMetadataRequest> createRepeated() => $pb.PbList<GetPredictImageMetadataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPredictImageMetadataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictImageMetadataRequest>(create);
  static GetPredictImageMetadataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get imageName => $_getSZ(2);
  @$pb.TagNumber(3)
  set imageName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasImageName() => $_has(2);
  @$pb.TagNumber(3)
  void clearImageName() => clearField(3);
}

class GetPredictImageMetadataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictImageMetadataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOM<$21.Annotations>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'annotations', subBuilder: $21.Annotations.create)
    ..aOM<$6.DeepweedOutput>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedOutput', subBuilder: $6.DeepweedOutput.create)
    ..aOM<$6.DeepweedOutput>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedOutputBelowThreshold', subBuilder: $6.DeepweedOutput.create)
    ..hasRequiredFields = false
  ;

  GetPredictImageMetadataResponse._() : super();
  factory GetPredictImageMetadataResponse({
    $1.Timestamp? ts,
    $21.Annotations? annotations,
    $6.DeepweedOutput? deepweedOutput,
    $6.DeepweedOutput? deepweedOutputBelowThreshold,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (annotations != null) {
      _result.annotations = annotations;
    }
    if (deepweedOutput != null) {
      _result.deepweedOutput = deepweedOutput;
    }
    if (deepweedOutputBelowThreshold != null) {
      _result.deepweedOutputBelowThreshold = deepweedOutputBelowThreshold;
    }
    return _result;
  }
  factory GetPredictImageMetadataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictImageMetadataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictImageMetadataResponse clone() => GetPredictImageMetadataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictImageMetadataResponse copyWith(void Function(GetPredictImageMetadataResponse) updates) => super.copyWith((message) => updates(message as GetPredictImageMetadataResponse)) as GetPredictImageMetadataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictImageMetadataResponse create() => GetPredictImageMetadataResponse._();
  GetPredictImageMetadataResponse createEmptyInstance() => create();
  static $pb.PbList<GetPredictImageMetadataResponse> createRepeated() => $pb.PbList<GetPredictImageMetadataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetPredictImageMetadataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictImageMetadataResponse>(create);
  static GetPredictImageMetadataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $21.Annotations get annotations => $_getN(1);
  @$pb.TagNumber(2)
  set annotations($21.Annotations v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAnnotations() => $_has(1);
  @$pb.TagNumber(2)
  void clearAnnotations() => clearField(2);
  @$pb.TagNumber(2)
  $21.Annotations ensureAnnotations() => $_ensure(1);

  @$pb.TagNumber(3)
  $6.DeepweedOutput get deepweedOutput => $_getN(2);
  @$pb.TagNumber(3)
  set deepweedOutput($6.DeepweedOutput v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeepweedOutput() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeepweedOutput() => clearField(3);
  @$pb.TagNumber(3)
  $6.DeepweedOutput ensureDeepweedOutput() => $_ensure(2);

  @$pb.TagNumber(4)
  $6.DeepweedOutput get deepweedOutputBelowThreshold => $_getN(3);
  @$pb.TagNumber(4)
  set deepweedOutputBelowThreshold($6.DeepweedOutput v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeepweedOutputBelowThreshold() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeepweedOutputBelowThreshold() => clearField(4);
  @$pb.TagNumber(4)
  $6.DeepweedOutput ensureDeepweedOutputBelowThreshold() => $_ensure(3);
}

class GetPredictImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageName')
    ..hasRequiredFields = false
  ;

  GetPredictImageRequest._() : super();
  factory GetPredictImageRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.String? imageName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (imageName != null) {
      _result.imageName = imageName;
    }
    return _result;
  }
  factory GetPredictImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictImageRequest clone() => GetPredictImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictImageRequest copyWith(void Function(GetPredictImageRequest) updates) => super.copyWith((message) => updates(message as GetPredictImageRequest)) as GetPredictImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictImageRequest create() => GetPredictImageRequest._();
  GetPredictImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetPredictImageRequest> createRepeated() => $pb.PbList<GetPredictImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPredictImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictImageRequest>(create);
  static GetPredictImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get imageName => $_getSZ(2);
  @$pb.TagNumber(3)
  set imageName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasImageName() => $_has(2);
  @$pb.TagNumber(3)
  void clearImageName() => clearField(3);
}

class StartUploadRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartUploadRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..hasRequiredFields = false
  ;

  StartUploadRequest._() : super();
  factory StartUploadRequest({
    $core.String? recordingName,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    return _result;
  }
  factory StartUploadRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartUploadRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartUploadRequest clone() => StartUploadRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartUploadRequest copyWith(void Function(StartUploadRequest) updates) => super.copyWith((message) => updates(message as StartUploadRequest)) as StartUploadRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartUploadRequest create() => StartUploadRequest._();
  StartUploadRequest createEmptyInstance() => create();
  static $pb.PbList<StartUploadRequest> createRepeated() => $pb.PbList<StartUploadRequest>();
  @$core.pragma('dart2js:noInline')
  static StartUploadRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartUploadRequest>(create);
  static StartUploadRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);
}

class GetNextUploadStateRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextUploadStateRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextUploadStateRequest._() : super();
  factory GetNextUploadStateRequest({
    $core.String? recordingName,
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextUploadStateRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextUploadStateRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextUploadStateRequest clone() => GetNextUploadStateRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextUploadStateRequest copyWith(void Function(GetNextUploadStateRequest) updates) => super.copyWith((message) => updates(message as GetNextUploadStateRequest)) as GetNextUploadStateRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextUploadStateRequest create() => GetNextUploadStateRequest._();
  GetNextUploadStateRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextUploadStateRequest> createRepeated() => $pb.PbList<GetNextUploadStateRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextUploadStateRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextUploadStateRequest>(create);
  static GetNextUploadStateRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);
}

class GetNextUploadStateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextUploadStateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..e<UploadState>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uploadState', $pb.PbFieldType.OE, defaultOrMaker: UploadState.NONE, valueOf: UploadState.valueOf, enumValues: UploadState.values)
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'percentUploaded', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetNextUploadStateResponse._() : super();
  factory GetNextUploadStateResponse({
    UploadState? uploadState,
    $1.Timestamp? ts,
    $core.int? percentUploaded,
  }) {
    final _result = create();
    if (uploadState != null) {
      _result.uploadState = uploadState;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (percentUploaded != null) {
      _result.percentUploaded = percentUploaded;
    }
    return _result;
  }
  factory GetNextUploadStateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextUploadStateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextUploadStateResponse clone() => GetNextUploadStateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextUploadStateResponse copyWith(void Function(GetNextUploadStateResponse) updates) => super.copyWith((message) => updates(message as GetNextUploadStateResponse)) as GetNextUploadStateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextUploadStateResponse create() => GetNextUploadStateResponse._();
  GetNextUploadStateResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextUploadStateResponse> createRepeated() => $pb.PbList<GetNextUploadStateResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextUploadStateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextUploadStateResponse>(create);
  static GetNextUploadStateResponse? _defaultInstance;

  @$pb.TagNumber(1)
  UploadState get uploadState => $_getN(0);
  @$pb.TagNumber(1)
  set uploadState(UploadState v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasUploadState() => $_has(0);
  @$pb.TagNumber(1)
  void clearUploadState() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get percentUploaded => $_getIZ(2);
  @$pb.TagNumber(3)
  set percentUploaded($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPercentUploaded() => $_has(2);
  @$pb.TagNumber(3)
  void clearPercentUploaded() => clearField(3);
}

class GetDeepweedPredictionsCountRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedPredictionsCountRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'row', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetDeepweedPredictionsCountRequest._() : super();
  factory GetDeepweedPredictionsCountRequest({
    $core.String? recordingName,
    $core.int? row,
    $core.int? camId,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (row != null) {
      _result.row = row;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory GetDeepweedPredictionsCountRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedPredictionsCountRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsCountRequest clone() => GetDeepweedPredictionsCountRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsCountRequest copyWith(void Function(GetDeepweedPredictionsCountRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedPredictionsCountRequest)) as GetDeepweedPredictionsCountRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsCountRequest create() => GetDeepweedPredictionsCountRequest._();
  GetDeepweedPredictionsCountRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedPredictionsCountRequest> createRepeated() => $pb.PbList<GetDeepweedPredictionsCountRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsCountRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedPredictionsCountRequest>(create);
  static GetDeepweedPredictionsCountRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get row => $_getIZ(1);
  @$pb.TagNumber(2)
  set row($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRow() => $_has(1);
  @$pb.TagNumber(2)
  void clearRow() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get camId => $_getIZ(2);
  @$pb.TagNumber(3)
  set camId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCamId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCamId() => clearField(3);
}

class GetDeepweedPredictionsCountResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedPredictionsCountResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'count', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetDeepweedPredictionsCountResponse._() : super();
  factory GetDeepweedPredictionsCountResponse({
    $core.int? count,
  }) {
    final _result = create();
    if (count != null) {
      _result.count = count;
    }
    return _result;
  }
  factory GetDeepweedPredictionsCountResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedPredictionsCountResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsCountResponse clone() => GetDeepweedPredictionsCountResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsCountResponse copyWith(void Function(GetDeepweedPredictionsCountResponse) updates) => super.copyWith((message) => updates(message as GetDeepweedPredictionsCountResponse)) as GetDeepweedPredictionsCountResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsCountResponse create() => GetDeepweedPredictionsCountResponse._();
  GetDeepweedPredictionsCountResponse createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedPredictionsCountResponse> createRepeated() => $pb.PbList<GetDeepweedPredictionsCountResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsCountResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedPredictionsCountResponse>(create);
  static GetDeepweedPredictionsCountResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get count => $_getIZ(0);
  @$pb.TagNumber(1)
  set count($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCount() => $_has(0);
  @$pb.TagNumber(1)
  void clearCount() => clearField(1);
}

class GetDeepweedPredictionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedPredictionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'row', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'idx', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetDeepweedPredictionsRequest._() : super();
  factory GetDeepweedPredictionsRequest({
    $core.String? recordingName,
    $core.int? row,
    $core.int? camId,
    $core.int? idx,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (row != null) {
      _result.row = row;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (idx != null) {
      _result.idx = idx;
    }
    return _result;
  }
  factory GetDeepweedPredictionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedPredictionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsRequest clone() => GetDeepweedPredictionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsRequest copyWith(void Function(GetDeepweedPredictionsRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedPredictionsRequest)) as GetDeepweedPredictionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsRequest create() => GetDeepweedPredictionsRequest._();
  GetDeepweedPredictionsRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedPredictionsRequest> createRepeated() => $pb.PbList<GetDeepweedPredictionsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedPredictionsRequest>(create);
  static GetDeepweedPredictionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get row => $_getIZ(1);
  @$pb.TagNumber(2)
  set row($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRow() => $_has(1);
  @$pb.TagNumber(2)
  void clearRow() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get camId => $_getIZ(2);
  @$pb.TagNumber(3)
  set camId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCamId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCamId() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get idx => $_getIZ(3);
  @$pb.TagNumber(4)
  set idx($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIdx() => $_has(3);
  @$pb.TagNumber(4)
  void clearIdx() => clearField(4);
}

class GetDeepweedPredictionsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedPredictionsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<$67.DeepweedPredictionRecord>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictions', subBuilder: $67.DeepweedPredictionRecord.create)
    ..hasRequiredFields = false
  ;

  GetDeepweedPredictionsResponse._() : super();
  factory GetDeepweedPredictionsResponse({
    $67.DeepweedPredictionRecord? predictions,
  }) {
    final _result = create();
    if (predictions != null) {
      _result.predictions = predictions;
    }
    return _result;
  }
  factory GetDeepweedPredictionsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedPredictionsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsResponse clone() => GetDeepweedPredictionsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedPredictionsResponse copyWith(void Function(GetDeepweedPredictionsResponse) updates) => super.copyWith((message) => updates(message as GetDeepweedPredictionsResponse)) as GetDeepweedPredictionsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsResponse create() => GetDeepweedPredictionsResponse._();
  GetDeepweedPredictionsResponse createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedPredictionsResponse> createRepeated() => $pb.PbList<GetDeepweedPredictionsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedPredictionsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedPredictionsResponse>(create);
  static GetDeepweedPredictionsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $67.DeepweedPredictionRecord get predictions => $_getN(0);
  @$pb.TagNumber(1)
  set predictions($67.DeepweedPredictionRecord v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPredictions() => $_has(0);
  @$pb.TagNumber(1)
  void clearPredictions() => clearField(1);
  @$pb.TagNumber(1)
  $67.DeepweedPredictionRecord ensurePredictions() => $_ensure(0);
}

class FindTrajectoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FindTrajectoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectoryId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  FindTrajectoryRequest._() : super();
  factory FindTrajectoryRequest({
    $core.String? recordingName,
    $core.int? rowId,
    $core.int? trajectoryId,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (trajectoryId != null) {
      _result.trajectoryId = trajectoryId;
    }
    return _result;
  }
  factory FindTrajectoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FindTrajectoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FindTrajectoryRequest clone() => FindTrajectoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FindTrajectoryRequest copyWith(void Function(FindTrajectoryRequest) updates) => super.copyWith((message) => updates(message as FindTrajectoryRequest)) as FindTrajectoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FindTrajectoryRequest create() => FindTrajectoryRequest._();
  FindTrajectoryRequest createEmptyInstance() => create();
  static $pb.PbList<FindTrajectoryRequest> createRepeated() => $pb.PbList<FindTrajectoryRequest>();
  @$core.pragma('dart2js:noInline')
  static FindTrajectoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FindTrajectoryRequest>(create);
  static FindTrajectoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get trajectoryId => $_getIZ(2);
  @$pb.TagNumber(3)
  set trajectoryId($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTrajectoryId() => $_has(2);
  @$pb.TagNumber(3)
  void clearTrajectoryId() => clearField(3);
}

class FindTrajectoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FindTrajectoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshotId', $pb.PbFieldType.OU3)
    ..aOM<$4.TrajectorySnapshot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectory', subBuilder: $4.TrajectorySnapshot.create)
    ..hasRequiredFields = false
  ;

  FindTrajectoryResponse._() : super();
  factory FindTrajectoryResponse({
    $core.int? snapshotId,
    $4.TrajectorySnapshot? trajectory,
  }) {
    final _result = create();
    if (snapshotId != null) {
      _result.snapshotId = snapshotId;
    }
    if (trajectory != null) {
      _result.trajectory = trajectory;
    }
    return _result;
  }
  factory FindTrajectoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FindTrajectoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FindTrajectoryResponse clone() => FindTrajectoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FindTrajectoryResponse copyWith(void Function(FindTrajectoryResponse) updates) => super.copyWith((message) => updates(message as FindTrajectoryResponse)) as FindTrajectoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FindTrajectoryResponse create() => FindTrajectoryResponse._();
  FindTrajectoryResponse createEmptyInstance() => create();
  static $pb.PbList<FindTrajectoryResponse> createRepeated() => $pb.PbList<FindTrajectoryResponse>();
  @$core.pragma('dart2js:noInline')
  static FindTrajectoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FindTrajectoryResponse>(create);
  static FindTrajectoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get snapshotId => $_getIZ(0);
  @$pb.TagNumber(1)
  set snapshotId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSnapshotId() => $_has(0);
  @$pb.TagNumber(1)
  void clearSnapshotId() => clearField(1);

  @$pb.TagNumber(2)
  $4.TrajectorySnapshot get trajectory => $_getN(1);
  @$pb.TagNumber(2)
  set trajectory($4.TrajectorySnapshot v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTrajectory() => $_has(1);
  @$pb.TagNumber(2)
  void clearTrajectory() => clearField(2);
  @$pb.TagNumber(2)
  $4.TrajectorySnapshot ensureTrajectory() => $_ensure(1);
}

class GetRotaryTicksRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRotaryTicksRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'recordingName')
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetRotaryTicksRequest._() : super();
  factory GetRotaryTicksRequest({
    $core.String? recordingName,
    $core.int? rowId,
  }) {
    final _result = create();
    if (recordingName != null) {
      _result.recordingName = recordingName;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    return _result;
  }
  factory GetRotaryTicksRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRotaryTicksRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRotaryTicksRequest clone() => GetRotaryTicksRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRotaryTicksRequest copyWith(void Function(GetRotaryTicksRequest) updates) => super.copyWith((message) => updates(message as GetRotaryTicksRequest)) as GetRotaryTicksRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRotaryTicksRequest create() => GetRotaryTicksRequest._();
  GetRotaryTicksRequest createEmptyInstance() => create();
  static $pb.PbList<GetRotaryTicksRequest> createRepeated() => $pb.PbList<GetRotaryTicksRequest>();
  @$core.pragma('dart2js:noInline')
  static GetRotaryTicksRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRotaryTicksRequest>(create);
  static GetRotaryTicksRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get recordingName => $_getSZ(0);
  @$pb.TagNumber(1)
  set recordingName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRecordingName() => $_has(0);
  @$pb.TagNumber(1)
  void clearRecordingName() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);
}

class GetRotaryTicksResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRotaryTicksResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..pc<$67.RotaryTicksRecord>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'records', $pb.PbFieldType.PM, subBuilder: $67.RotaryTicksRecord.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wheelEncoderResolution', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetRotaryTicksResponse._() : super();
  factory GetRotaryTicksResponse({
    $core.Iterable<$67.RotaryTicksRecord>? records,
    $core.int? wheelEncoderResolution,
  }) {
    final _result = create();
    if (records != null) {
      _result.records.addAll(records);
    }
    if (wheelEncoderResolution != null) {
      _result.wheelEncoderResolution = wheelEncoderResolution;
    }
    return _result;
  }
  factory GetRotaryTicksResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRotaryTicksResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRotaryTicksResponse clone() => GetRotaryTicksResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRotaryTicksResponse copyWith(void Function(GetRotaryTicksResponse) updates) => super.copyWith((message) => updates(message as GetRotaryTicksResponse)) as GetRotaryTicksResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRotaryTicksResponse create() => GetRotaryTicksResponse._();
  GetRotaryTicksResponse createEmptyInstance() => create();
  static $pb.PbList<GetRotaryTicksResponse> createRepeated() => $pb.PbList<GetRotaryTicksResponse>();
  @$core.pragma('dart2js:noInline')
  static GetRotaryTicksResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRotaryTicksResponse>(create);
  static GetRotaryTicksResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$67.RotaryTicksRecord> get records => $_getList(0);

  @$pb.TagNumber(2)
  $core.int get wheelEncoderResolution => $_getIZ(1);
  @$pb.TagNumber(2)
  set wheelEncoderResolution($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWheelEncoderResolution() => $_has(1);
  @$pb.TagNumber(2)
  void clearWheelEncoderResolution() => clearField(2);
}

class SnapshotPredictImagesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SnapshotPredictImagesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  SnapshotPredictImagesRequest._() : super();
  factory SnapshotPredictImagesRequest({
    $core.int? rowId,
  }) {
    final _result = create();
    if (rowId != null) {
      _result.rowId = rowId;
    }
    return _result;
  }
  factory SnapshotPredictImagesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SnapshotPredictImagesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SnapshotPredictImagesRequest clone() => SnapshotPredictImagesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SnapshotPredictImagesRequest copyWith(void Function(SnapshotPredictImagesRequest) updates) => super.copyWith((message) => updates(message as SnapshotPredictImagesRequest)) as SnapshotPredictImagesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SnapshotPredictImagesRequest create() => SnapshotPredictImagesRequest._();
  SnapshotPredictImagesRequest createEmptyInstance() => create();
  static $pb.PbList<SnapshotPredictImagesRequest> createRepeated() => $pb.PbList<SnapshotPredictImagesRequest>();
  @$core.pragma('dart2js:noInline')
  static SnapshotPredictImagesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SnapshotPredictImagesRequest>(create);
  static SnapshotPredictImagesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowId => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowId() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowId() => clearField(1);
}

class PcamSnapshot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PcamSnapshot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pcamId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  PcamSnapshot._() : super();
  factory PcamSnapshot({
    $core.String? pcamId,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (pcamId != null) {
      _result.pcamId = pcamId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory PcamSnapshot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PcamSnapshot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PcamSnapshot clone() => PcamSnapshot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PcamSnapshot copyWith(void Function(PcamSnapshot) updates) => super.copyWith((message) => updates(message as PcamSnapshot)) as PcamSnapshot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PcamSnapshot create() => PcamSnapshot._();
  PcamSnapshot createEmptyInstance() => create();
  static $pb.PbList<PcamSnapshot> createRepeated() => $pb.PbList<PcamSnapshot>();
  @$core.pragma('dart2js:noInline')
  static PcamSnapshot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PcamSnapshot>(create);
  static PcamSnapshot? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pcamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set pcamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPcamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPcamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);
}

class SnapshotPredictImagesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SnapshotPredictImagesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..pc<PcamSnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshots', $pb.PbFieldType.PM, subBuilder: PcamSnapshot.create)
    ..hasRequiredFields = false
  ;

  SnapshotPredictImagesResponse._() : super();
  factory SnapshotPredictImagesResponse({
    $core.Iterable<PcamSnapshot>? snapshots,
  }) {
    final _result = create();
    if (snapshots != null) {
      _result.snapshots.addAll(snapshots);
    }
    return _result;
  }
  factory SnapshotPredictImagesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SnapshotPredictImagesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SnapshotPredictImagesResponse clone() => SnapshotPredictImagesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SnapshotPredictImagesResponse copyWith(void Function(SnapshotPredictImagesResponse) updates) => super.copyWith((message) => updates(message as SnapshotPredictImagesResponse)) as SnapshotPredictImagesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SnapshotPredictImagesResponse create() => SnapshotPredictImagesResponse._();
  SnapshotPredictImagesResponse createEmptyInstance() => create();
  static $pb.PbList<SnapshotPredictImagesResponse> createRepeated() => $pb.PbList<SnapshotPredictImagesResponse>();
  @$core.pragma('dart2js:noInline')
  static SnapshotPredictImagesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SnapshotPredictImagesResponse>(create);
  static SnapshotPredictImagesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<PcamSnapshot> get snapshots => $_getList(0);
}

class GetChipForPredictImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetChipForPredictImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pcamId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerXPx', $pb.PbFieldType.O3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerYPx', $pb.PbFieldType.O3)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  GetChipForPredictImageRequest._() : super();
  factory GetChipForPredictImageRequest({
    $core.String? pcamId,
    $fixnum.Int64? timestampMs,
    $core.int? centerXPx,
    $core.int? centerYPx,
    $core.int? rowId,
  }) {
    final _result = create();
    if (pcamId != null) {
      _result.pcamId = pcamId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (centerXPx != null) {
      _result.centerXPx = centerXPx;
    }
    if (centerYPx != null) {
      _result.centerYPx = centerYPx;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    return _result;
  }
  factory GetChipForPredictImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetChipForPredictImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetChipForPredictImageRequest clone() => GetChipForPredictImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetChipForPredictImageRequest copyWith(void Function(GetChipForPredictImageRequest) updates) => super.copyWith((message) => updates(message as GetChipForPredictImageRequest)) as GetChipForPredictImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetChipForPredictImageRequest create() => GetChipForPredictImageRequest._();
  GetChipForPredictImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetChipForPredictImageRequest> createRepeated() => $pb.PbList<GetChipForPredictImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetChipForPredictImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetChipForPredictImageRequest>(create);
  static GetChipForPredictImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pcamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set pcamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPcamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPcamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get centerXPx => $_getIZ(2);
  @$pb.TagNumber(3)
  set centerXPx($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCenterXPx() => $_has(2);
  @$pb.TagNumber(3)
  void clearCenterXPx() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get centerYPx => $_getIZ(3);
  @$pb.TagNumber(4)
  set centerYPx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCenterYPx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCenterYPx() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get rowId => $_getIZ(4);
  @$pb.TagNumber(5)
  set rowId($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRowId() => $_has(4);
  @$pb.TagNumber(5)
  void clearRowId() => clearField(5);
}

class GetChipForPredictImageResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetChipForPredictImageResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'chipImage', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  GetChipForPredictImageResponse._() : super();
  factory GetChipForPredictImageResponse({
    $core.List<$core.int>? chipImage,
  }) {
    final _result = create();
    if (chipImage != null) {
      _result.chipImage = chipImage;
    }
    return _result;
  }
  factory GetChipForPredictImageResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetChipForPredictImageResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetChipForPredictImageResponse clone() => GetChipForPredictImageResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetChipForPredictImageResponse copyWith(void Function(GetChipForPredictImageResponse) updates) => super.copyWith((message) => updates(message as GetChipForPredictImageResponse)) as GetChipForPredictImageResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetChipForPredictImageResponse create() => GetChipForPredictImageResponse._();
  GetChipForPredictImageResponse createEmptyInstance() => create();
  static $pb.PbList<GetChipForPredictImageResponse> createRepeated() => $pb.PbList<GetChipForPredictImageResponse>();
  @$core.pragma('dart2js:noInline')
  static GetChipForPredictImageResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetChipForPredictImageResponse>(create);
  static GetChipForPredictImageResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get chipImage => $_getN(0);
  @$pb.TagNumber(1)
  set chipImage($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasChipImage() => $_has(0);
  @$pb.TagNumber(1)
  void clearChipImage() => clearField(1);
}

class UploadChipRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UploadChipRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.weeding_diagnostics'), createEmptyInstance: create)
    ..aOM<$4.TrajectorySnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trajectorySnapshot', subBuilder: $4.TrajectorySnapshot.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  UploadChipRequest._() : super();
  factory UploadChipRequest({
    $4.TrajectorySnapshot? trajectorySnapshot,
    $core.int? rowId,
  }) {
    final _result = create();
    if (trajectorySnapshot != null) {
      _result.trajectorySnapshot = trajectorySnapshot;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    return _result;
  }
  factory UploadChipRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UploadChipRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UploadChipRequest clone() => UploadChipRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UploadChipRequest copyWith(void Function(UploadChipRequest) updates) => super.copyWith((message) => updates(message as UploadChipRequest)) as UploadChipRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UploadChipRequest create() => UploadChipRequest._();
  UploadChipRequest createEmptyInstance() => create();
  static $pb.PbList<UploadChipRequest> createRepeated() => $pb.PbList<UploadChipRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadChipRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadChipRequest>(create);
  static UploadChipRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $4.TrajectorySnapshot get trajectorySnapshot => $_getN(0);
  @$pb.TagNumber(1)
  set trajectorySnapshot($4.TrajectorySnapshot v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrajectorySnapshot() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrajectorySnapshot() => clearField(1);
  @$pb.TagNumber(1)
  $4.TrajectorySnapshot ensureTrajectorySnapshot() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);
}

