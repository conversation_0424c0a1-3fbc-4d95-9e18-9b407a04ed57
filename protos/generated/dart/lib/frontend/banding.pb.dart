///
//  Generated code. Do not modify.
//  source: frontend/banding.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../weed_tracking/weed_tracking.pb.dart' as $4;
import '../util/util.pb.dart' as $1;

import 'banding.pbenum.dart';

export 'banding.pbenum.dart';

class BandingRow extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BandingRow', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.O3)
    ..pc<$4.BandDefinition>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bands', $pb.PbFieldType.PM, subBuilder: $4.BandDefinition.create)
    ..hasRequiredFields = false
  ;

  BandingRow._() : super();
  factory BandingRow({
    $core.int? rowId,
    $core.Iterable<$4.BandDefinition>? bands,
  }) {
    final _result = create();
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (bands != null) {
      _result.bands.addAll(bands);
    }
    return _result;
  }
  factory BandingRow.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BandingRow.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BandingRow clone() => BandingRow()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BandingRow copyWith(void Function(BandingRow) updates) => super.copyWith((message) => updates(message as BandingRow)) as BandingRow; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BandingRow create() => BandingRow._();
  BandingRow createEmptyInstance() => create();
  static $pb.PbList<BandingRow> createRepeated() => $pb.PbList<BandingRow>();
  @$core.pragma('dart2js:noInline')
  static BandingRow getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BandingRow>(create);
  static BandingRow? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowId => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowId() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$4.BandDefinition> get bands => $_getList(1);
}

class BandingDef extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BandingDef', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pc<BandingRow>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: BandingRow.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  BandingDef._() : super();
  factory BandingDef({
    $core.String? name,
    $core.Iterable<BandingRow>? rows,
    $core.String? uuid,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (rows != null) {
      _result.rows.addAll(rows);
    }
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory BandingDef.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BandingDef.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BandingDef clone() => BandingDef()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BandingDef copyWith(void Function(BandingDef) updates) => super.copyWith((message) => updates(message as BandingDef)) as BandingDef; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BandingDef create() => BandingDef._();
  BandingDef createEmptyInstance() => create();
  static $pb.PbList<BandingDef> createRepeated() => $pb.PbList<BandingDef>();
  @$core.pragma('dart2js:noInline')
  static BandingDef getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BandingDef>(create);
  static BandingDef? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<BandingRow> get rows => $_getList(1);

  @$pb.TagNumber(3)
  $core.String get uuid => $_getSZ(2);
  @$pb.TagNumber(3)
  set uuid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUuid() => $_has(2);
  @$pb.TagNumber(3)
  void clearUuid() => clearField(3);
}

class SaveBandingDefRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveBandingDefRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<BandingDef>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingDef', protoName: 'bandingDef', subBuilder: BandingDef.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'setActive', protoName: 'setActive')
    ..hasRequiredFields = false
  ;

  SaveBandingDefRequest._() : super();
  factory SaveBandingDefRequest({
    BandingDef? bandingDef,
    $core.bool? setActive,
  }) {
    final _result = create();
    if (bandingDef != null) {
      _result.bandingDef = bandingDef;
    }
    if (setActive != null) {
      _result.setActive = setActive;
    }
    return _result;
  }
  factory SaveBandingDefRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveBandingDefRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveBandingDefRequest clone() => SaveBandingDefRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveBandingDefRequest copyWith(void Function(SaveBandingDefRequest) updates) => super.copyWith((message) => updates(message as SaveBandingDefRequest)) as SaveBandingDefRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveBandingDefRequest create() => SaveBandingDefRequest._();
  SaveBandingDefRequest createEmptyInstance() => create();
  static $pb.PbList<SaveBandingDefRequest> createRepeated() => $pb.PbList<SaveBandingDefRequest>();
  @$core.pragma('dart2js:noInline')
  static SaveBandingDefRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveBandingDefRequest>(create);
  static SaveBandingDefRequest? _defaultInstance;

  @$pb.TagNumber(1)
  BandingDef get bandingDef => $_getN(0);
  @$pb.TagNumber(1)
  set bandingDef(BandingDef v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasBandingDef() => $_has(0);
  @$pb.TagNumber(1)
  void clearBandingDef() => clearField(1);
  @$pb.TagNumber(1)
  BandingDef ensureBandingDef() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get setActive => $_getBF(1);
  @$pb.TagNumber(2)
  set setActive($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSetActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearSetActive() => clearField(2);
}

class LoadBandingDefsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadBandingDefsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..pc<BandingDef>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingDefs', $pb.PbFieldType.PM, protoName: 'bandingDefs', subBuilder: BandingDef.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeDef', protoName: 'activeDef')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeDefUUID', protoName: 'activeDefUUID')
    ..hasRequiredFields = false
  ;

  LoadBandingDefsResponse._() : super();
  factory LoadBandingDefsResponse({
    $core.Iterable<BandingDef>? bandingDefs,
  @$core.Deprecated('This field is deprecated.')
    $core.String? activeDef,
    $core.String? activeDefUUID,
  }) {
    final _result = create();
    if (bandingDefs != null) {
      _result.bandingDefs.addAll(bandingDefs);
    }
    if (activeDef != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.activeDef = activeDef;
    }
    if (activeDefUUID != null) {
      _result.activeDefUUID = activeDefUUID;
    }
    return _result;
  }
  factory LoadBandingDefsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadBandingDefsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadBandingDefsResponse clone() => LoadBandingDefsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadBandingDefsResponse copyWith(void Function(LoadBandingDefsResponse) updates) => super.copyWith((message) => updates(message as LoadBandingDefsResponse)) as LoadBandingDefsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadBandingDefsResponse create() => LoadBandingDefsResponse._();
  LoadBandingDefsResponse createEmptyInstance() => create();
  static $pb.PbList<LoadBandingDefsResponse> createRepeated() => $pb.PbList<LoadBandingDefsResponse>();
  @$core.pragma('dart2js:noInline')
  static LoadBandingDefsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadBandingDefsResponse>(create);
  static LoadBandingDefsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<BandingDef> get bandingDefs => $_getList(0);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.String get activeDef => $_getSZ(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  set activeDef($core.String v) { $_setString(1, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool hasActiveDef() => $_has(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  void clearActiveDef() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get activeDefUUID => $_getSZ(2);
  @$pb.TagNumber(3)
  set activeDefUUID($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActiveDefUUID() => $_has(2);
  @$pb.TagNumber(3)
  void clearActiveDefUUID() => clearField(3);
}

class SetActiveBandingDefRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveBandingDefRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  SetActiveBandingDefRequest._() : super();
  factory SetActiveBandingDefRequest({
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? uuid,
  }) {
    final _result = create();
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory SetActiveBandingDefRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveBandingDefRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveBandingDefRequest clone() => SetActiveBandingDefRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveBandingDefRequest copyWith(void Function(SetActiveBandingDefRequest) updates) => super.copyWith((message) => updates(message as SetActiveBandingDefRequest)) as SetActiveBandingDefRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveBandingDefRequest create() => SetActiveBandingDefRequest._();
  SetActiveBandingDefRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveBandingDefRequest> createRepeated() => $pb.PbList<SetActiveBandingDefRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveBandingDefRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveBandingDefRequest>(create);
  static SetActiveBandingDefRequest? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get uuid => $_getSZ(1);
  @$pb.TagNumber(2)
  set uuid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUuid() => $_has(1);
  @$pb.TagNumber(2)
  void clearUuid() => clearField(2);
}

class GetActiveBandingDefResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetActiveBandingDefResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  GetActiveBandingDefResponse._() : super();
  factory GetActiveBandingDefResponse({
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? uuid,
  }) {
    final _result = create();
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory GetActiveBandingDefResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetActiveBandingDefResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetActiveBandingDefResponse clone() => GetActiveBandingDefResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetActiveBandingDefResponse copyWith(void Function(GetActiveBandingDefResponse) updates) => super.copyWith((message) => updates(message as GetActiveBandingDefResponse)) as GetActiveBandingDefResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetActiveBandingDefResponse create() => GetActiveBandingDefResponse._();
  GetActiveBandingDefResponse createEmptyInstance() => create();
  static $pb.PbList<GetActiveBandingDefResponse> createRepeated() => $pb.PbList<GetActiveBandingDefResponse>();
  @$core.pragma('dart2js:noInline')
  static GetActiveBandingDefResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetActiveBandingDefResponse>(create);
  static GetActiveBandingDefResponse? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get uuid => $_getSZ(1);
  @$pb.TagNumber(2)
  set uuid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUuid() => $_has(1);
  @$pb.TagNumber(2)
  void clearUuid() => clearField(2);
}

class DeleteBandingDefRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteBandingDefRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..hasRequiredFields = false
  ;

  DeleteBandingDefRequest._() : super();
  factory DeleteBandingDefRequest({
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? uuid,
  }) {
    final _result = create();
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (uuid != null) {
      _result.uuid = uuid;
    }
    return _result;
  }
  factory DeleteBandingDefRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteBandingDefRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteBandingDefRequest clone() => DeleteBandingDefRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteBandingDefRequest copyWith(void Function(DeleteBandingDefRequest) updates) => super.copyWith((message) => updates(message as DeleteBandingDefRequest)) as DeleteBandingDefRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteBandingDefRequest create() => DeleteBandingDefRequest._();
  DeleteBandingDefRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteBandingDefRequest> createRepeated() => $pb.PbList<DeleteBandingDefRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteBandingDefRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteBandingDefRequest>(create);
  static DeleteBandingDefRequest? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get uuid => $_getSZ(1);
  @$pb.TagNumber(2)
  set uuid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUuid() => $_has(1);
  @$pb.TagNumber(2)
  void clearUuid() => clearField(2);
}

class VisualizationData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'VisualizationData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'xMm', $pb.PbFieldType.O3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'yMm', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zMm', $pb.PbFieldType.O3)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeed')
    ..hasRequiredFields = false
  ;

  VisualizationData._() : super();
  factory VisualizationData({
    $core.int? xMm,
    $core.int? yMm,
    $core.int? zMm,
    $core.bool? isWeed,
  }) {
    final _result = create();
    if (xMm != null) {
      _result.xMm = xMm;
    }
    if (yMm != null) {
      _result.yMm = yMm;
    }
    if (zMm != null) {
      _result.zMm = zMm;
    }
    if (isWeed != null) {
      _result.isWeed = isWeed;
    }
    return _result;
  }
  factory VisualizationData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory VisualizationData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  VisualizationData clone() => VisualizationData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  VisualizationData copyWith(void Function(VisualizationData) updates) => super.copyWith((message) => updates(message as VisualizationData)) as VisualizationData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static VisualizationData create() => VisualizationData._();
  VisualizationData createEmptyInstance() => create();
  static $pb.PbList<VisualizationData> createRepeated() => $pb.PbList<VisualizationData>();
  @$core.pragma('dart2js:noInline')
  static VisualizationData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<VisualizationData>(create);
  static VisualizationData? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get xMm => $_getIZ(0);
  @$pb.TagNumber(1)
  set xMm($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasXMm() => $_has(0);
  @$pb.TagNumber(1)
  void clearXMm() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get yMm => $_getIZ(1);
  @$pb.TagNumber(2)
  set yMm($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYMm() => $_has(1);
  @$pb.TagNumber(2)
  void clearYMm() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get zMm => $_getIZ(2);
  @$pb.TagNumber(3)
  set zMm($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasZMm() => $_has(2);
  @$pb.TagNumber(3)
  void clearZMm() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get isWeed => $_getBF(3);
  @$pb.TagNumber(4)
  set isWeed($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIsWeed() => $_has(3);
  @$pb.TagNumber(4)
  void clearIsWeed() => clearField(4);
}

class GetNextVisualizationDataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextVisualizationDataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.O3)
    ..pc<VisualizationTypeToInclude>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'typesToInclude', $pb.PbFieldType.KE, valueOf: VisualizationTypeToInclude.valueOf, enumValues: VisualizationTypeToInclude.values, defaultEnumValue: VisualizationTypeToInclude.DUPLICATE_WEED)
    ..aOM<ThresholdFilters>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thresholdFilters', subBuilder: ThresholdFilters.create)
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'includeDetailedMetadata')
    ..hasRequiredFields = false
  ;

  GetNextVisualizationDataRequest._() : super();
  factory GetNextVisualizationDataRequest({
    $1.Timestamp? ts,
    $core.int? rowId,
    $core.Iterable<VisualizationTypeToInclude>? typesToInclude,
    ThresholdFilters? thresholdFilters,
    $core.bool? includeDetailedMetadata,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (typesToInclude != null) {
      _result.typesToInclude.addAll(typesToInclude);
    }
    if (thresholdFilters != null) {
      _result.thresholdFilters = thresholdFilters;
    }
    if (includeDetailedMetadata != null) {
      _result.includeDetailedMetadata = includeDetailedMetadata;
    }
    return _result;
  }
  factory GetNextVisualizationDataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextVisualizationDataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataRequest clone() => GetNextVisualizationDataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataRequest copyWith(void Function(GetNextVisualizationDataRequest) updates) => super.copyWith((message) => updates(message as GetNextVisualizationDataRequest)) as GetNextVisualizationDataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataRequest create() => GetNextVisualizationDataRequest._();
  GetNextVisualizationDataRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextVisualizationDataRequest> createRepeated() => $pb.PbList<GetNextVisualizationDataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextVisualizationDataRequest>(create);
  static GetNextVisualizationDataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get rowId => $_getIZ(1);
  @$pb.TagNumber(2)
  set rowId($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRowId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRowId() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<VisualizationTypeToInclude> get typesToInclude => $_getList(2);

  @$pb.TagNumber(4)
  ThresholdFilters get thresholdFilters => $_getN(3);
  @$pb.TagNumber(4)
  set thresholdFilters(ThresholdFilters v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasThresholdFilters() => $_has(3);
  @$pb.TagNumber(4)
  void clearThresholdFilters() => clearField(4);
  @$pb.TagNumber(4)
  ThresholdFilters ensureThresholdFilters() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.bool get includeDetailedMetadata => $_getBF(4);
  @$pb.TagNumber(5)
  set includeDetailedMetadata($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIncludeDetailedMetadata() => $_has(4);
  @$pb.TagNumber(5)
  void clearIncludeDetailedMetadata() => clearField(5);
}

class GetNextVisualizationDataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextVisualizationDataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<VisualizationData>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'data', $pb.PbFieldType.PM, subBuilder: VisualizationData.create)
    ..pc<$4.BandDefinition>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bands', $pb.PbFieldType.PM, subBuilder: $4.BandDefinition.create)
    ..hasRequiredFields = false
  ;

  GetNextVisualizationDataResponse._() : super();
  factory GetNextVisualizationDataResponse({
    $1.Timestamp? ts,
    $core.Iterable<VisualizationData>? data,
    $core.Iterable<$4.BandDefinition>? bands,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (data != null) {
      _result.data.addAll(data);
    }
    if (bands != null) {
      _result.bands.addAll(bands);
    }
    return _result;
  }
  factory GetNextVisualizationDataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextVisualizationDataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataResponse clone() => GetNextVisualizationDataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataResponse copyWith(void Function(GetNextVisualizationDataResponse) updates) => super.copyWith((message) => updates(message as GetNextVisualizationDataResponse)) as GetNextVisualizationDataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataResponse create() => GetNextVisualizationDataResponse._();
  GetNextVisualizationDataResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextVisualizationDataResponse> createRepeated() => $pb.PbList<GetNextVisualizationDataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextVisualizationDataResponse>(create);
  static GetNextVisualizationDataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<VisualizationData> get data => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$4.BandDefinition> get bands => $_getList(2);
}

class GetNextVisualizationData2Response extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextVisualizationData2Response', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<$4.DiagnosticsSnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'data', subBuilder: $4.DiagnosticsSnapshot.create)
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextVisualizationData2Response._() : super();
  factory GetNextVisualizationData2Response({
    $4.DiagnosticsSnapshot? data,
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (data != null) {
      _result.data = data;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextVisualizationData2Response.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextVisualizationData2Response.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextVisualizationData2Response clone() => GetNextVisualizationData2Response()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextVisualizationData2Response copyWith(void Function(GetNextVisualizationData2Response) updates) => super.copyWith((message) => updates(message as GetNextVisualizationData2Response)) as GetNextVisualizationData2Response; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationData2Response create() => GetNextVisualizationData2Response._();
  GetNextVisualizationData2Response createEmptyInstance() => create();
  static $pb.PbList<GetNextVisualizationData2Response> createRepeated() => $pb.PbList<GetNextVisualizationData2Response>();
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationData2Response getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextVisualizationData2Response>(create);
  static GetNextVisualizationData2Response? _defaultInstance;

  @$pb.TagNumber(1)
  $4.DiagnosticsSnapshot get data => $_getN(0);
  @$pb.TagNumber(1)
  set data($4.DiagnosticsSnapshot v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasData() => $_has(0);
  @$pb.TagNumber(1)
  void clearData() => clearField(1);
  @$pb.TagNumber(1)
  $4.DiagnosticsSnapshot ensureData() => $_ensure(0);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);
}

class GetDimensionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDimensionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  GetDimensionsRequest._() : super();
  factory GetDimensionsRequest({
    $core.int? rowId,
  }) {
    final _result = create();
    if (rowId != null) {
      _result.rowId = rowId;
    }
    return _result;
  }
  factory GetDimensionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDimensionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDimensionsRequest clone() => GetDimensionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDimensionsRequest copyWith(void Function(GetDimensionsRequest) updates) => super.copyWith((message) => updates(message as GetDimensionsRequest)) as GetDimensionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDimensionsRequest create() => GetDimensionsRequest._();
  GetDimensionsRequest createEmptyInstance() => create();
  static $pb.PbList<GetDimensionsRequest> createRepeated() => $pb.PbList<GetDimensionsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDimensionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDimensionsRequest>(create);
  static GetDimensionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowId => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowId() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowId() => clearField(1);
}

class SetBandingEnabledRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetBandingEnabledRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..hasRequiredFields = false
  ;

  SetBandingEnabledRequest._() : super();
  factory SetBandingEnabledRequest({
    $core.bool? enabled,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    return _result;
  }
  factory SetBandingEnabledRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetBandingEnabledRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetBandingEnabledRequest clone() => SetBandingEnabledRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetBandingEnabledRequest copyWith(void Function(SetBandingEnabledRequest) updates) => super.copyWith((message) => updates(message as SetBandingEnabledRequest)) as SetBandingEnabledRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetBandingEnabledRequest create() => SetBandingEnabledRequest._();
  SetBandingEnabledRequest createEmptyInstance() => create();
  static $pb.PbList<SetBandingEnabledRequest> createRepeated() => $pb.PbList<SetBandingEnabledRequest>();
  @$core.pragma('dart2js:noInline')
  static SetBandingEnabledRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetBandingEnabledRequest>(create);
  static SetBandingEnabledRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);
}

class IsBandingEnabledResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IsBandingEnabledResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..hasRequiredFields = false
  ;

  IsBandingEnabledResponse._() : super();
  factory IsBandingEnabledResponse({
    $core.bool? enabled,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    return _result;
  }
  factory IsBandingEnabledResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IsBandingEnabledResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IsBandingEnabledResponse clone() => IsBandingEnabledResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IsBandingEnabledResponse copyWith(void Function(IsBandingEnabledResponse) updates) => super.copyWith((message) => updates(message as IsBandingEnabledResponse)) as IsBandingEnabledResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IsBandingEnabledResponse create() => IsBandingEnabledResponse._();
  IsBandingEnabledResponse createEmptyInstance() => create();
  static $pb.PbList<IsBandingEnabledResponse> createRepeated() => $pb.PbList<IsBandingEnabledResponse>();
  @$core.pragma('dart2js:noInline')
  static IsBandingEnabledResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IsBandingEnabledResponse>(create);
  static IsBandingEnabledResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);
}

class GetVisualizationMetadataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetVisualizationMetadataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..m<$core.int, $core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropSafetyRadiusMmPerRow', entryClassName: 'GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('carbon.frontend.banding'))
    ..hasRequiredFields = false
  ;

  GetVisualizationMetadataResponse._() : super();
  factory GetVisualizationMetadataResponse({
    $core.Map<$core.int, $core.double>? cropSafetyRadiusMmPerRow,
  }) {
    final _result = create();
    if (cropSafetyRadiusMmPerRow != null) {
      _result.cropSafetyRadiusMmPerRow.addAll(cropSafetyRadiusMmPerRow);
    }
    return _result;
  }
  factory GetVisualizationMetadataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetVisualizationMetadataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetVisualizationMetadataResponse clone() => GetVisualizationMetadataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetVisualizationMetadataResponse copyWith(void Function(GetVisualizationMetadataResponse) updates) => super.copyWith((message) => updates(message as GetVisualizationMetadataResponse)) as GetVisualizationMetadataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetVisualizationMetadataResponse create() => GetVisualizationMetadataResponse._();
  GetVisualizationMetadataResponse createEmptyInstance() => create();
  static $pb.PbList<GetVisualizationMetadataResponse> createRepeated() => $pb.PbList<GetVisualizationMetadataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetVisualizationMetadataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetVisualizationMetadataResponse>(create);
  static GetVisualizationMetadataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.int, $core.double> get cropSafetyRadiusMmPerRow => $_getMap(0);
}

class ThresholdFilter extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ThresholdFilter', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..e<ThresholdState>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeding', $pb.PbFieldType.OE, defaultOrMaker: ThresholdState.ANY, valueOf: ThresholdState.valueOf, enumValues: ThresholdState.values)
    ..e<ThresholdState>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinning', $pb.PbFieldType.OE, defaultOrMaker: ThresholdState.ANY, valueOf: ThresholdState.valueOf, enumValues: ThresholdState.values)
    ..e<ThresholdState>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'banding', $pb.PbFieldType.OE, defaultOrMaker: ThresholdState.ANY, valueOf: ThresholdState.valueOf, enumValues: ThresholdState.values)
    ..hasRequiredFields = false
  ;

  ThresholdFilter._() : super();
  factory ThresholdFilter({
    ThresholdState? weeding,
    ThresholdState? thinning,
    ThresholdState? banding,
  }) {
    final _result = create();
    if (weeding != null) {
      _result.weeding = weeding;
    }
    if (thinning != null) {
      _result.thinning = thinning;
    }
    if (banding != null) {
      _result.banding = banding;
    }
    return _result;
  }
  factory ThresholdFilter.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ThresholdFilter.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ThresholdFilter clone() => ThresholdFilter()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ThresholdFilter copyWith(void Function(ThresholdFilter) updates) => super.copyWith((message) => updates(message as ThresholdFilter)) as ThresholdFilter; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ThresholdFilter create() => ThresholdFilter._();
  ThresholdFilter createEmptyInstance() => create();
  static $pb.PbList<ThresholdFilter> createRepeated() => $pb.PbList<ThresholdFilter>();
  @$core.pragma('dart2js:noInline')
  static ThresholdFilter getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ThresholdFilter>(create);
  static ThresholdFilter? _defaultInstance;

  @$pb.TagNumber(1)
  ThresholdState get weeding => $_getN(0);
  @$pb.TagNumber(1)
  set weeding(ThresholdState v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeeding() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeeding() => clearField(1);

  @$pb.TagNumber(2)
  ThresholdState get thinning => $_getN(1);
  @$pb.TagNumber(2)
  set thinning(ThresholdState v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasThinning() => $_has(1);
  @$pb.TagNumber(2)
  void clearThinning() => clearField(2);

  @$pb.TagNumber(3)
  ThresholdState get banding => $_getN(2);
  @$pb.TagNumber(3)
  set banding(ThresholdState v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasBanding() => $_has(2);
  @$pb.TagNumber(3)
  void clearBanding() => clearField(3);
}

class ThresholdFilters extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ThresholdFilters', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<ThresholdFilter>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop', subBuilder: ThresholdFilter.create)
    ..aOM<ThresholdFilter>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weed', subBuilder: ThresholdFilter.create)
    ..hasRequiredFields = false
  ;

  ThresholdFilters._() : super();
  factory ThresholdFilters({
    ThresholdFilter? crop,
    ThresholdFilter? weed,
  }) {
    final _result = create();
    if (crop != null) {
      _result.crop = crop;
    }
    if (weed != null) {
      _result.weed = weed;
    }
    return _result;
  }
  factory ThresholdFilters.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ThresholdFilters.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ThresholdFilters clone() => ThresholdFilters()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ThresholdFilters copyWith(void Function(ThresholdFilters) updates) => super.copyWith((message) => updates(message as ThresholdFilters)) as ThresholdFilters; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ThresholdFilters create() => ThresholdFilters._();
  ThresholdFilters createEmptyInstance() => create();
  static $pb.PbList<ThresholdFilters> createRepeated() => $pb.PbList<ThresholdFilters>();
  @$core.pragma('dart2js:noInline')
  static ThresholdFilters getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ThresholdFilters>(create);
  static ThresholdFilters? _defaultInstance;

  @$pb.TagNumber(1)
  ThresholdFilter get crop => $_getN(0);
  @$pb.TagNumber(1)
  set crop(ThresholdFilter v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCrop() => $_has(0);
  @$pb.TagNumber(1)
  void clearCrop() => clearField(1);
  @$pb.TagNumber(1)
  ThresholdFilter ensureCrop() => $_ensure(0);

  @$pb.TagNumber(2)
  ThresholdFilter get weed => $_getN(1);
  @$pb.TagNumber(2)
  set weed(ThresholdFilter v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasWeed() => $_has(1);
  @$pb.TagNumber(2)
  void clearWeed() => clearField(2);
  @$pb.TagNumber(2)
  ThresholdFilter ensureWeed() => $_ensure(1);
}

class GetNextVisualizationDataForAllRowsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextVisualizationDataForAllRowsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<VisualizationTypeToInclude>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'typesToInclude', $pb.PbFieldType.KE, valueOf: VisualizationTypeToInclude.valueOf, enumValues: VisualizationTypeToInclude.values, defaultEnumValue: VisualizationTypeToInclude.DUPLICATE_WEED)
    ..aOM<ThresholdFilters>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thresholdFilters', subBuilder: ThresholdFilters.create)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'includeDetailedMetadata')
    ..hasRequiredFields = false
  ;

  GetNextVisualizationDataForAllRowsRequest._() : super();
  factory GetNextVisualizationDataForAllRowsRequest({
    $1.Timestamp? ts,
    $core.Iterable<VisualizationTypeToInclude>? typesToInclude,
    ThresholdFilters? thresholdFilters,
    $core.bool? includeDetailedMetadata,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (typesToInclude != null) {
      _result.typesToInclude.addAll(typesToInclude);
    }
    if (thresholdFilters != null) {
      _result.thresholdFilters = thresholdFilters;
    }
    if (includeDetailedMetadata != null) {
      _result.includeDetailedMetadata = includeDetailedMetadata;
    }
    return _result;
  }
  factory GetNextVisualizationDataForAllRowsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextVisualizationDataForAllRowsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataForAllRowsRequest clone() => GetNextVisualizationDataForAllRowsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataForAllRowsRequest copyWith(void Function(GetNextVisualizationDataForAllRowsRequest) updates) => super.copyWith((message) => updates(message as GetNextVisualizationDataForAllRowsRequest)) as GetNextVisualizationDataForAllRowsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataForAllRowsRequest create() => GetNextVisualizationDataForAllRowsRequest._();
  GetNextVisualizationDataForAllRowsRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextVisualizationDataForAllRowsRequest> createRepeated() => $pb.PbList<GetNextVisualizationDataForAllRowsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataForAllRowsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextVisualizationDataForAllRowsRequest>(create);
  static GetNextVisualizationDataForAllRowsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<VisualizationTypeToInclude> get typesToInclude => $_getList(1);

  @$pb.TagNumber(3)
  ThresholdFilters get thresholdFilters => $_getN(2);
  @$pb.TagNumber(3)
  set thresholdFilters(ThresholdFilters v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasThresholdFilters() => $_has(2);
  @$pb.TagNumber(3)
  void clearThresholdFilters() => clearField(3);
  @$pb.TagNumber(3)
  ThresholdFilters ensureThresholdFilters() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.bool get includeDetailedMetadata => $_getBF(3);
  @$pb.TagNumber(4)
  set includeDetailedMetadata($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIncludeDetailedMetadata() => $_has(3);
  @$pb.TagNumber(4)
  void clearIncludeDetailedMetadata() => clearField(4);
}

class GetNextVisualizationDataForAllRowsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextVisualizationDataForAllRowsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..m<$core.int, $4.DiagnosticsSnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dataPerRow', entryClassName: 'GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OM, valueCreator: $4.DiagnosticsSnapshot.create, packageName: const $pb.PackageName('carbon.frontend.banding'))
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<VisualizationTypeToInclude>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'typesToInclude', $pb.PbFieldType.KE, valueOf: VisualizationTypeToInclude.valueOf, enumValues: VisualizationTypeToInclude.values, defaultEnumValue: VisualizationTypeToInclude.DUPLICATE_WEED)
    ..hasRequiredFields = false
  ;

  GetNextVisualizationDataForAllRowsResponse._() : super();
  factory GetNextVisualizationDataForAllRowsResponse({
    $core.Map<$core.int, $4.DiagnosticsSnapshot>? dataPerRow,
    $1.Timestamp? ts,
    $core.Iterable<VisualizationTypeToInclude>? typesToInclude,
  }) {
    final _result = create();
    if (dataPerRow != null) {
      _result.dataPerRow.addAll(dataPerRow);
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (typesToInclude != null) {
      _result.typesToInclude.addAll(typesToInclude);
    }
    return _result;
  }
  factory GetNextVisualizationDataForAllRowsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextVisualizationDataForAllRowsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataForAllRowsResponse clone() => GetNextVisualizationDataForAllRowsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextVisualizationDataForAllRowsResponse copyWith(void Function(GetNextVisualizationDataForAllRowsResponse) updates) => super.copyWith((message) => updates(message as GetNextVisualizationDataForAllRowsResponse)) as GetNextVisualizationDataForAllRowsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataForAllRowsResponse create() => GetNextVisualizationDataForAllRowsResponse._();
  GetNextVisualizationDataForAllRowsResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextVisualizationDataForAllRowsResponse> createRepeated() => $pb.PbList<GetNextVisualizationDataForAllRowsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextVisualizationDataForAllRowsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextVisualizationDataForAllRowsResponse>(create);
  static GetNextVisualizationDataForAllRowsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.int, $4.DiagnosticsSnapshot> get dataPerRow => $_getMap(0);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.List<VisualizationTypeToInclude> get typesToInclude => $_getList(2);
}

class GetNextBandingStateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextBandingStateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.banding'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<BandingDef>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingDefs', $pb.PbFieldType.PM, protoName: 'bandingDefs', subBuilder: BandingDef.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeDefUUID', protoName: 'activeDefUUID')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isBandingEnabled')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isDynamicBandingEnabled')
    ..hasRequiredFields = false
  ;

  GetNextBandingStateResponse._() : super();
  factory GetNextBandingStateResponse({
    $1.Timestamp? ts,
    $core.Iterable<BandingDef>? bandingDefs,
    $core.String? activeDefUUID,
    $core.bool? isBandingEnabled,
    $core.bool? isDynamicBandingEnabled,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (bandingDefs != null) {
      _result.bandingDefs.addAll(bandingDefs);
    }
    if (activeDefUUID != null) {
      _result.activeDefUUID = activeDefUUID;
    }
    if (isBandingEnabled != null) {
      _result.isBandingEnabled = isBandingEnabled;
    }
    if (isDynamicBandingEnabled != null) {
      _result.isDynamicBandingEnabled = isDynamicBandingEnabled;
    }
    return _result;
  }
  factory GetNextBandingStateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextBandingStateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextBandingStateResponse clone() => GetNextBandingStateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextBandingStateResponse copyWith(void Function(GetNextBandingStateResponse) updates) => super.copyWith((message) => updates(message as GetNextBandingStateResponse)) as GetNextBandingStateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextBandingStateResponse create() => GetNextBandingStateResponse._();
  GetNextBandingStateResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextBandingStateResponse> createRepeated() => $pb.PbList<GetNextBandingStateResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextBandingStateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextBandingStateResponse>(create);
  static GetNextBandingStateResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<BandingDef> get bandingDefs => $_getList(1);

  @$pb.TagNumber(3)
  $core.String get activeDefUUID => $_getSZ(2);
  @$pb.TagNumber(3)
  set activeDefUUID($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActiveDefUUID() => $_has(2);
  @$pb.TagNumber(3)
  void clearActiveDefUUID() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get isBandingEnabled => $_getBF(3);
  @$pb.TagNumber(4)
  set isBandingEnabled($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIsBandingEnabled() => $_has(3);
  @$pb.TagNumber(4)
  void clearIsBandingEnabled() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get isDynamicBandingEnabled => $_getBF(4);
  @$pb.TagNumber(5)
  set isDynamicBandingEnabled($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIsDynamicBandingEnabled() => $_has(4);
  @$pb.TagNumber(5)
  void clearIsDynamicBandingEnabled() => clearField(5);
}

