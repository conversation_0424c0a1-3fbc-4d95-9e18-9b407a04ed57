///
//  Generated code. Do not modify.
//  source: frontend/thinning.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../thinning/thinning.pb.dart' as $66;

import 'thinning.pbenum.dart';

export 'thinning.pbenum.dart';

class GetNextConfigurationsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextConfigurationsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<$66.ConfigDefinition>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'definitions', $pb.PbFieldType.PM, subBuilder: $66.ConfigDefinition.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeId')
    ..hasRequiredFields = false
  ;

  GetNextConfigurationsResponse._() : super();
  factory GetNextConfigurationsResponse({
    $1.Timestamp? ts,
    $core.Iterable<$66.ConfigDefinition>? definitions,
    $core.String? activeId,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (definitions != null) {
      _result.definitions.addAll(definitions);
    }
    if (activeId != null) {
      _result.activeId = activeId;
    }
    return _result;
  }
  factory GetNextConfigurationsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextConfigurationsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextConfigurationsResponse clone() => GetNextConfigurationsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextConfigurationsResponse copyWith(void Function(GetNextConfigurationsResponse) updates) => super.copyWith((message) => updates(message as GetNextConfigurationsResponse)) as GetNextConfigurationsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextConfigurationsResponse create() => GetNextConfigurationsResponse._();
  GetNextConfigurationsResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextConfigurationsResponse> createRepeated() => $pb.PbList<GetNextConfigurationsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextConfigurationsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextConfigurationsResponse>(create);
  static GetNextConfigurationsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$66.ConfigDefinition> get definitions => $_getList(1);

  @$pb.TagNumber(3)
  $core.String get activeId => $_getSZ(2);
  @$pb.TagNumber(3)
  set activeId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActiveId() => $_has(2);
  @$pb.TagNumber(3)
  void clearActiveId() => clearField(3);
}

class GetNextActiveConfResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveConfResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  GetNextActiveConfResponse._() : super();
  factory GetNextActiveConfResponse({
    $1.Timestamp? ts,
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? id,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory GetNextActiveConfResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveConfResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveConfResponse clone() => GetNextActiveConfResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveConfResponse copyWith(void Function(GetNextActiveConfResponse) updates) => super.copyWith((message) => updates(message as GetNextActiveConfResponse)) as GetNextActiveConfResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveConfResponse create() => GetNextActiveConfResponse._();
  GetNextActiveConfResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveConfResponse> createRepeated() => $pb.PbList<GetNextActiveConfResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveConfResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveConfResponse>(create);
  static GetNextActiveConfResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get id => $_getSZ(2);
  @$pb.TagNumber(3)
  set id($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasId() => $_has(2);
  @$pb.TagNumber(3)
  void clearId() => clearField(3);
}

class DefineConfigurationRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DefineConfigurationRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOM<$66.ConfigDefinition>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'definition', subBuilder: $66.ConfigDefinition.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'setActive')
    ..e<ThinningConfVer>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ver', $pb.PbFieldType.OE, defaultOrMaker: ThinningConfVer.THIN_CONF_V1, valueOf: ThinningConfVer.valueOf, enumValues: ThinningConfVer.values)
    ..hasRequiredFields = false
  ;

  DefineConfigurationRequest._() : super();
  factory DefineConfigurationRequest({
    $66.ConfigDefinition? definition,
    $core.bool? setActive,
    ThinningConfVer? ver,
  }) {
    final _result = create();
    if (definition != null) {
      _result.definition = definition;
    }
    if (setActive != null) {
      _result.setActive = setActive;
    }
    if (ver != null) {
      _result.ver = ver;
    }
    return _result;
  }
  factory DefineConfigurationRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DefineConfigurationRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DefineConfigurationRequest clone() => DefineConfigurationRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DefineConfigurationRequest copyWith(void Function(DefineConfigurationRequest) updates) => super.copyWith((message) => updates(message as DefineConfigurationRequest)) as DefineConfigurationRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DefineConfigurationRequest create() => DefineConfigurationRequest._();
  DefineConfigurationRequest createEmptyInstance() => create();
  static $pb.PbList<DefineConfigurationRequest> createRepeated() => $pb.PbList<DefineConfigurationRequest>();
  @$core.pragma('dart2js:noInline')
  static DefineConfigurationRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DefineConfigurationRequest>(create);
  static DefineConfigurationRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $66.ConfigDefinition get definition => $_getN(0);
  @$pb.TagNumber(1)
  set definition($66.ConfigDefinition v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDefinition() => $_has(0);
  @$pb.TagNumber(1)
  void clearDefinition() => clearField(1);
  @$pb.TagNumber(1)
  $66.ConfigDefinition ensureDefinition() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get setActive => $_getBF(1);
  @$pb.TagNumber(2)
  set setActive($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSetActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearSetActive() => clearField(2);

  @$pb.TagNumber(3)
  ThinningConfVer get ver => $_getN(2);
  @$pb.TagNumber(3)
  set ver(ThinningConfVer v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasVer() => $_has(2);
  @$pb.TagNumber(3)
  void clearVer() => clearField(3);
}

class DefineConfigurationResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DefineConfigurationResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  DefineConfigurationResponse._() : super();
  factory DefineConfigurationResponse({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory DefineConfigurationResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DefineConfigurationResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DefineConfigurationResponse clone() => DefineConfigurationResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DefineConfigurationResponse copyWith(void Function(DefineConfigurationResponse) updates) => super.copyWith((message) => updates(message as DefineConfigurationResponse)) as DefineConfigurationResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DefineConfigurationResponse create() => DefineConfigurationResponse._();
  DefineConfigurationResponse createEmptyInstance() => create();
  static $pb.PbList<DefineConfigurationResponse> createRepeated() => $pb.PbList<DefineConfigurationResponse>();
  @$core.pragma('dart2js:noInline')
  static DefineConfigurationResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DefineConfigurationResponse>(create);
  static DefineConfigurationResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class SetActiveConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..e<ThinningConfVer>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ver', $pb.PbFieldType.OE, defaultOrMaker: ThinningConfVer.THIN_CONF_V1, valueOf: ThinningConfVer.valueOf, enumValues: ThinningConfVer.values)
    ..hasRequiredFields = false
  ;

  SetActiveConfigRequest._() : super();
  factory SetActiveConfigRequest({
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? id,
    ThinningConfVer? ver,
  }) {
    final _result = create();
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (id != null) {
      _result.id = id;
    }
    if (ver != null) {
      _result.ver = ver;
    }
    return _result;
  }
  factory SetActiveConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveConfigRequest clone() => SetActiveConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveConfigRequest copyWith(void Function(SetActiveConfigRequest) updates) => super.copyWith((message) => updates(message as SetActiveConfigRequest)) as SetActiveConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveConfigRequest create() => SetActiveConfigRequest._();
  SetActiveConfigRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveConfigRequest> createRepeated() => $pb.PbList<SetActiveConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveConfigRequest>(create);
  static SetActiveConfigRequest? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get id => $_getSZ(1);
  @$pb.TagNumber(2)
  set id($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasId() => $_has(1);
  @$pb.TagNumber(2)
  void clearId() => clearField(2);

  @$pb.TagNumber(3)
  ThinningConfVer get ver => $_getN(2);
  @$pb.TagNumber(3)
  set ver(ThinningConfVer v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasVer() => $_has(2);
  @$pb.TagNumber(3)
  void clearVer() => clearField(3);
}

class SetActiveConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetActiveConfigResponse._() : super();
  factory SetActiveConfigResponse() => create();
  factory SetActiveConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveConfigResponse clone() => SetActiveConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveConfigResponse copyWith(void Function(SetActiveConfigResponse) updates) => super.copyWith((message) => updates(message as SetActiveConfigResponse)) as SetActiveConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveConfigResponse create() => SetActiveConfigResponse._();
  SetActiveConfigResponse createEmptyInstance() => create();
  static $pb.PbList<SetActiveConfigResponse> createRepeated() => $pb.PbList<SetActiveConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static SetActiveConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveConfigResponse>(create);
  static SetActiveConfigResponse? _defaultInstance;
}

class DeleteConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..e<ThinningConfVer>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ver', $pb.PbFieldType.OE, defaultOrMaker: ThinningConfVer.THIN_CONF_V1, valueOf: ThinningConfVer.valueOf, enumValues: ThinningConfVer.values)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'newActiveId')
    ..hasRequiredFields = false
  ;

  DeleteConfigRequest._() : super();
  factory DeleteConfigRequest({
  @$core.Deprecated('This field is deprecated.')
    $core.String? name,
    $core.String? id,
    ThinningConfVer? ver,
    $core.String? newActiveId,
  }) {
    final _result = create();
    if (name != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.name = name;
    }
    if (id != null) {
      _result.id = id;
    }
    if (ver != null) {
      _result.ver = ver;
    }
    if (newActiveId != null) {
      _result.newActiveId = newActiveId;
    }
    return _result;
  }
  factory DeleteConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteConfigRequest clone() => DeleteConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteConfigRequest copyWith(void Function(DeleteConfigRequest) updates) => super.copyWith((message) => updates(message as DeleteConfigRequest)) as DeleteConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteConfigRequest create() => DeleteConfigRequest._();
  DeleteConfigRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteConfigRequest> createRepeated() => $pb.PbList<DeleteConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteConfigRequest>(create);
  static DeleteConfigRequest? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get id => $_getSZ(1);
  @$pb.TagNumber(2)
  set id($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasId() => $_has(1);
  @$pb.TagNumber(2)
  void clearId() => clearField(2);

  @$pb.TagNumber(3)
  ThinningConfVer get ver => $_getN(2);
  @$pb.TagNumber(3)
  set ver(ThinningConfVer v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasVer() => $_has(2);
  @$pb.TagNumber(3)
  void clearVer() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get newActiveId => $_getSZ(3);
  @$pb.TagNumber(4)
  set newActiveId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasNewActiveId() => $_has(3);
  @$pb.TagNumber(4)
  void clearNewActiveId() => clearField(4);
}

class DeleteConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.thinning'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  DeleteConfigResponse._() : super();
  factory DeleteConfigResponse() => create();
  factory DeleteConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteConfigResponse clone() => DeleteConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteConfigResponse copyWith(void Function(DeleteConfigResponse) updates) => super.copyWith((message) => updates(message as DeleteConfigResponse)) as DeleteConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteConfigResponse create() => DeleteConfigResponse._();
  DeleteConfigResponse createEmptyInstance() => create();
  static $pb.PbList<DeleteConfigResponse> createRepeated() => $pb.PbList<DeleteConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static DeleteConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteConfigResponse>(create);
  static DeleteConfigResponse? _defaultInstance;
}

