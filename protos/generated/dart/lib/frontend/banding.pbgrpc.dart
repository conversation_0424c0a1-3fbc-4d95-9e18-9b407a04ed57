///
//  Generated code. Do not modify.
//  source: frontend/banding.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import '../util/util.pb.dart' as $1;
import 'banding.pb.dart' as $10;
import '../core/controls/exterminator/controllers/aimbot/process/aimbot.pb.dart'
    as $5;
export 'banding.pb.dart';

class BandingServiceClient extends $grpc.Client {
  static final _$loadBandingDefs =
      $grpc.ClientMethod<$1.Empty, $10.LoadBandingDefsResponse>(
          '/carbon.frontend.banding.BandingService/LoadBandingDefs',
          ($1.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.LoadBandingDefsResponse.fromBuffer(value));
  static final _$saveBandingDef =
      $grpc.ClientMethod<$10.SaveBandingDefRequest, $1.Empty>(
          '/carbon.frontend.banding.BandingService/SaveBandingDef',
          ($10.SaveBandingDefRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$deleteBandingDef =
      $grpc.ClientMethod<$10.DeleteBandingDefRequest, $1.Empty>(
          '/carbon.frontend.banding.BandingService/DeleteBandingDef',
          ($10.DeleteBandingDefRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$setActiveBandingDef =
      $grpc.ClientMethod<$10.SetActiveBandingDefRequest, $1.Empty>(
          '/carbon.frontend.banding.BandingService/SetActiveBandingDef',
          ($10.SetActiveBandingDefRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getActiveBandingDef =
      $grpc.ClientMethod<$1.Empty, $10.GetActiveBandingDefResponse>(
          '/carbon.frontend.banding.BandingService/GetActiveBandingDef',
          ($1.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.GetActiveBandingDefResponse.fromBuffer(value));
  static final _$getNextVisualizationData = $grpc.ClientMethod<
          $10.GetNextVisualizationDataRequest,
          $10.GetNextVisualizationDataResponse>(
      '/carbon.frontend.banding.BandingService/GetNextVisualizationData',
      ($10.GetNextVisualizationDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $10.GetNextVisualizationDataResponse.fromBuffer(value));
  static final _$getNextVisualizationData2 = $grpc.ClientMethod<
          $10.GetNextVisualizationDataRequest,
          $10.GetNextVisualizationData2Response>(
      '/carbon.frontend.banding.BandingService/GetNextVisualizationData2',
      ($10.GetNextVisualizationDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $10.GetNextVisualizationData2Response.fromBuffer(value));
  static final _$getNextVisualizationDataForAllRows = $grpc.ClientMethod<
          $10.GetNextVisualizationDataForAllRowsRequest,
          $10.GetNextVisualizationDataForAllRowsResponse>(
      '/carbon.frontend.banding.BandingService/GetNextVisualizationDataForAllRows',
      ($10.GetNextVisualizationDataForAllRowsRequest value) =>
          value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $10.GetNextVisualizationDataForAllRowsResponse.fromBuffer(value));
  static final _$getDimensions =
      $grpc.ClientMethod<$10.GetDimensionsRequest, $5.GetDimensionsResponse>(
          '/carbon.frontend.banding.BandingService/GetDimensions',
          ($10.GetDimensionsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $5.GetDimensionsResponse.fromBuffer(value));
  static final _$setBandingEnabled =
      $grpc.ClientMethod<$10.SetBandingEnabledRequest, $1.Empty>(
          '/carbon.frontend.banding.BandingService/SetBandingEnabled',
          ($10.SetBandingEnabledRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$isBandingEnabled =
      $grpc.ClientMethod<$1.Empty, $10.IsBandingEnabledResponse>(
          '/carbon.frontend.banding.BandingService/IsBandingEnabled',
          ($1.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.IsBandingEnabledResponse.fromBuffer(value));
  static final _$setDynamicBandingEnabled =
      $grpc.ClientMethod<$10.SetBandingEnabledRequest, $1.Empty>(
          '/carbon.frontend.banding.BandingService/SetDynamicBandingEnabled',
          ($10.SetBandingEnabledRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$isDynamicBandingEnabled =
      $grpc.ClientMethod<$1.Empty, $10.IsBandingEnabledResponse>(
          '/carbon.frontend.banding.BandingService/IsDynamicBandingEnabled',
          ($1.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.IsBandingEnabledResponse.fromBuffer(value));
  static final _$getVisualizationMetadata =
      $grpc.ClientMethod<$1.Empty, $10.GetVisualizationMetadataResponse>(
          '/carbon.frontend.banding.BandingService/GetVisualizationMetadata',
          ($1.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.GetVisualizationMetadataResponse.fromBuffer(value));
  static final _$getNextBandingState =
      $grpc.ClientMethod<$1.Timestamp, $10.GetNextBandingStateResponse>(
          '/carbon.frontend.banding.BandingService/GetNextBandingState',
          ($1.Timestamp value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $10.GetNextBandingStateResponse.fromBuffer(value));

  BandingServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$10.LoadBandingDefsResponse> loadBandingDefs(
      $1.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$loadBandingDefs, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> saveBandingDef(
      $10.SaveBandingDefRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$saveBandingDef, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteBandingDef(
      $10.DeleteBandingDefRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteBandingDef, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> setActiveBandingDef(
      $10.SetActiveBandingDefRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setActiveBandingDef, request, options: options);
  }

  $grpc.ResponseFuture<$10.GetActiveBandingDefResponse> getActiveBandingDef(
      $1.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getActiveBandingDef, request, options: options);
  }

  $grpc.ResponseFuture<$10.GetNextVisualizationDataResponse>
      getNextVisualizationData($10.GetNextVisualizationDataRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextVisualizationData, request,
        options: options);
  }

  $grpc.ResponseFuture<$10.GetNextVisualizationData2Response>
      getNextVisualizationData2($10.GetNextVisualizationDataRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextVisualizationData2, request,
        options: options);
  }

  $grpc.ResponseFuture<$10.GetNextVisualizationDataForAllRowsResponse>
      getNextVisualizationDataForAllRows(
          $10.GetNextVisualizationDataForAllRowsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextVisualizationDataForAllRows, request,
        options: options);
  }

  $grpc.ResponseFuture<$5.GetDimensionsResponse> getDimensions(
      $10.GetDimensionsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDimensions, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> setBandingEnabled(
      $10.SetBandingEnabledRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setBandingEnabled, request, options: options);
  }

  $grpc.ResponseFuture<$10.IsBandingEnabledResponse> isBandingEnabled(
      $1.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$isBandingEnabled, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> setDynamicBandingEnabled(
      $10.SetBandingEnabledRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setDynamicBandingEnabled, request,
        options: options);
  }

  $grpc.ResponseFuture<$10.IsBandingEnabledResponse> isDynamicBandingEnabled(
      $1.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$isDynamicBandingEnabled, request,
        options: options);
  }

  $grpc.ResponseFuture<$10.GetVisualizationMetadataResponse>
      getVisualizationMetadata($1.Empty request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getVisualizationMetadata, request,
        options: options);
  }

  $grpc.ResponseFuture<$10.GetNextBandingStateResponse> getNextBandingState(
      $1.Timestamp request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextBandingState, request, options: options);
  }
}

abstract class BandingServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.frontend.banding.BandingService';

  BandingServiceBase() {
    $addMethod($grpc.ServiceMethod<$1.Empty, $10.LoadBandingDefsResponse>(
        'LoadBandingDefs',
        loadBandingDefs_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.Empty.fromBuffer(value),
        ($10.LoadBandingDefsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.SaveBandingDefRequest, $1.Empty>(
        'SaveBandingDef',
        saveBandingDef_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.SaveBandingDefRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.DeleteBandingDefRequest, $1.Empty>(
        'DeleteBandingDef',
        deleteBandingDef_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.DeleteBandingDefRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.SetActiveBandingDefRequest, $1.Empty>(
        'SetActiveBandingDef',
        setActiveBandingDef_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.SetActiveBandingDefRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.Empty, $10.GetActiveBandingDefResponse>(
        'GetActiveBandingDef',
        getActiveBandingDef_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.Empty.fromBuffer(value),
        ($10.GetActiveBandingDefResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.GetNextVisualizationDataRequest,
            $10.GetNextVisualizationDataResponse>(
        'GetNextVisualizationData',
        getNextVisualizationData_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.GetNextVisualizationDataRequest.fromBuffer(value),
        ($10.GetNextVisualizationDataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.GetNextVisualizationDataRequest,
            $10.GetNextVisualizationData2Response>(
        'GetNextVisualizationData2',
        getNextVisualizationData2_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.GetNextVisualizationDataRequest.fromBuffer(value),
        ($10.GetNextVisualizationData2Response value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<
            $10.GetNextVisualizationDataForAllRowsRequest,
            $10.GetNextVisualizationDataForAllRowsResponse>(
        'GetNextVisualizationDataForAllRows',
        getNextVisualizationDataForAllRows_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.GetNextVisualizationDataForAllRowsRequest.fromBuffer(value),
        ($10.GetNextVisualizationDataForAllRowsResponse value) =>
            value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$10.GetDimensionsRequest, $5.GetDimensionsResponse>(
            'GetDimensions',
            getDimensions_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $10.GetDimensionsRequest.fromBuffer(value),
            ($5.GetDimensionsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.SetBandingEnabledRequest, $1.Empty>(
        'SetBandingEnabled',
        setBandingEnabled_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.SetBandingEnabledRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.Empty, $10.IsBandingEnabledResponse>(
        'IsBandingEnabled',
        isBandingEnabled_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.Empty.fromBuffer(value),
        ($10.IsBandingEnabledResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$10.SetBandingEnabledRequest, $1.Empty>(
        'SetDynamicBandingEnabled',
        setDynamicBandingEnabled_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $10.SetBandingEnabledRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.Empty, $10.IsBandingEnabledResponse>(
        'IsDynamicBandingEnabled',
        isDynamicBandingEnabled_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.Empty.fromBuffer(value),
        ($10.IsBandingEnabledResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$1.Empty, $10.GetVisualizationMetadataResponse>(
            'GetVisualizationMetadata',
            getVisualizationMetadata_Pre,
            false,
            false,
            ($core.List<$core.int> value) => $1.Empty.fromBuffer(value),
            ($10.GetVisualizationMetadataResponse value) =>
                value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$1.Timestamp, $10.GetNextBandingStateResponse>(
            'GetNextBandingState',
            getNextBandingState_Pre,
            false,
            false,
            ($core.List<$core.int> value) => $1.Timestamp.fromBuffer(value),
            ($10.GetNextBandingStateResponse value) => value.writeToBuffer()));
  }

  $async.Future<$10.LoadBandingDefsResponse> loadBandingDefs_Pre(
      $grpc.ServiceCall call, $async.Future<$1.Empty> request) async {
    return loadBandingDefs(call, await request);
  }

  $async.Future<$1.Empty> saveBandingDef_Pre($grpc.ServiceCall call,
      $async.Future<$10.SaveBandingDefRequest> request) async {
    return saveBandingDef(call, await request);
  }

  $async.Future<$1.Empty> deleteBandingDef_Pre($grpc.ServiceCall call,
      $async.Future<$10.DeleteBandingDefRequest> request) async {
    return deleteBandingDef(call, await request);
  }

  $async.Future<$1.Empty> setActiveBandingDef_Pre($grpc.ServiceCall call,
      $async.Future<$10.SetActiveBandingDefRequest> request) async {
    return setActiveBandingDef(call, await request);
  }

  $async.Future<$10.GetActiveBandingDefResponse> getActiveBandingDef_Pre(
      $grpc.ServiceCall call, $async.Future<$1.Empty> request) async {
    return getActiveBandingDef(call, await request);
  }

  $async.Future<$10.GetNextVisualizationDataResponse>
      getNextVisualizationData_Pre($grpc.ServiceCall call,
          $async.Future<$10.GetNextVisualizationDataRequest> request) async {
    return getNextVisualizationData(call, await request);
  }

  $async.Future<$10.GetNextVisualizationData2Response>
      getNextVisualizationData2_Pre($grpc.ServiceCall call,
          $async.Future<$10.GetNextVisualizationDataRequest> request) async {
    return getNextVisualizationData2(call, await request);
  }

  $async.Future<$10.GetNextVisualizationDataForAllRowsResponse>
      getNextVisualizationDataForAllRows_Pre(
          $grpc.ServiceCall call,
          $async.Future<$10.GetNextVisualizationDataForAllRowsRequest>
              request) async {
    return getNextVisualizationDataForAllRows(call, await request);
  }

  $async.Future<$5.GetDimensionsResponse> getDimensions_Pre(
      $grpc.ServiceCall call,
      $async.Future<$10.GetDimensionsRequest> request) async {
    return getDimensions(call, await request);
  }

  $async.Future<$1.Empty> setBandingEnabled_Pre($grpc.ServiceCall call,
      $async.Future<$10.SetBandingEnabledRequest> request) async {
    return setBandingEnabled(call, await request);
  }

  $async.Future<$10.IsBandingEnabledResponse> isBandingEnabled_Pre(
      $grpc.ServiceCall call, $async.Future<$1.Empty> request) async {
    return isBandingEnabled(call, await request);
  }

  $async.Future<$1.Empty> setDynamicBandingEnabled_Pre($grpc.ServiceCall call,
      $async.Future<$10.SetBandingEnabledRequest> request) async {
    return setDynamicBandingEnabled(call, await request);
  }

  $async.Future<$10.IsBandingEnabledResponse> isDynamicBandingEnabled_Pre(
      $grpc.ServiceCall call, $async.Future<$1.Empty> request) async {
    return isDynamicBandingEnabled(call, await request);
  }

  $async.Future<$10.GetVisualizationMetadataResponse>
      getVisualizationMetadata_Pre(
          $grpc.ServiceCall call, $async.Future<$1.Empty> request) async {
    return getVisualizationMetadata(call, await request);
  }

  $async.Future<$10.GetNextBandingStateResponse> getNextBandingState_Pre(
      $grpc.ServiceCall call, $async.Future<$1.Timestamp> request) async {
    return getNextBandingState(call, await request);
  }

  $async.Future<$10.LoadBandingDefsResponse> loadBandingDefs(
      $grpc.ServiceCall call, $1.Empty request);
  $async.Future<$1.Empty> saveBandingDef(
      $grpc.ServiceCall call, $10.SaveBandingDefRequest request);
  $async.Future<$1.Empty> deleteBandingDef(
      $grpc.ServiceCall call, $10.DeleteBandingDefRequest request);
  $async.Future<$1.Empty> setActiveBandingDef(
      $grpc.ServiceCall call, $10.SetActiveBandingDefRequest request);
  $async.Future<$10.GetActiveBandingDefResponse> getActiveBandingDef(
      $grpc.ServiceCall call, $1.Empty request);
  $async.Future<$10.GetNextVisualizationDataResponse> getNextVisualizationData(
      $grpc.ServiceCall call, $10.GetNextVisualizationDataRequest request);
  $async.Future<$10.GetNextVisualizationData2Response>
      getNextVisualizationData2(
          $grpc.ServiceCall call, $10.GetNextVisualizationDataRequest request);
  $async.Future<$10.GetNextVisualizationDataForAllRowsResponse>
      getNextVisualizationDataForAllRows($grpc.ServiceCall call,
          $10.GetNextVisualizationDataForAllRowsRequest request);
  $async.Future<$5.GetDimensionsResponse> getDimensions(
      $grpc.ServiceCall call, $10.GetDimensionsRequest request);
  $async.Future<$1.Empty> setBandingEnabled(
      $grpc.ServiceCall call, $10.SetBandingEnabledRequest request);
  $async.Future<$10.IsBandingEnabledResponse> isBandingEnabled(
      $grpc.ServiceCall call, $1.Empty request);
  $async.Future<$1.Empty> setDynamicBandingEnabled(
      $grpc.ServiceCall call, $10.SetBandingEnabledRequest request);
  $async.Future<$10.IsBandingEnabledResponse> isDynamicBandingEnabled(
      $grpc.ServiceCall call, $1.Empty request);
  $async.Future<$10.GetVisualizationMetadataResponse> getVisualizationMetadata(
      $grpc.ServiceCall call, $1.Empty request);
  $async.Future<$10.GetNextBandingStateResponse> getNextBandingState(
      $grpc.ServiceCall call, $1.Timestamp request);
}
