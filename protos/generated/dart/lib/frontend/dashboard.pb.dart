///
//  Generated code. Do not modify.
//  source: frontend/dashboard.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'translation.pb.dart' as $63;
import '../util/util.pb.dart' as $1;

import 'dashboard.pbenum.dart';

export 'dashboard.pbenum.dart';

class ExtraStatus extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ExtraStatus', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'title')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'iconName')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'iconColor')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusText')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusColor')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'groupId')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sectionId')
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progress', $pb.PbFieldType.OD)
    ..a<$core.int>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.OU3)
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bottomText')
    ..hasRequiredFields = false
  ;

  ExtraStatus._() : super();
  factory ExtraStatus({
    $core.String? title,
    $core.String? iconName,
    $core.String? iconColor,
    $core.String? statusText,
    $core.String? statusColor,
    $core.String? groupId,
    $core.String? sectionId,
    $core.double? progress,
    $core.int? width,
    $core.String? bottomText,
  }) {
    final _result = create();
    if (title != null) {
      _result.title = title;
    }
    if (iconName != null) {
      _result.iconName = iconName;
    }
    if (iconColor != null) {
      _result.iconColor = iconColor;
    }
    if (statusText != null) {
      _result.statusText = statusText;
    }
    if (statusColor != null) {
      _result.statusColor = statusColor;
    }
    if (groupId != null) {
      _result.groupId = groupId;
    }
    if (sectionId != null) {
      _result.sectionId = sectionId;
    }
    if (progress != null) {
      _result.progress = progress;
    }
    if (width != null) {
      _result.width = width;
    }
    if (bottomText != null) {
      _result.bottomText = bottomText;
    }
    return _result;
  }
  factory ExtraStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExtraStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExtraStatus clone() => ExtraStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExtraStatus copyWith(void Function(ExtraStatus) updates) => super.copyWith((message) => updates(message as ExtraStatus)) as ExtraStatus; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ExtraStatus create() => ExtraStatus._();
  ExtraStatus createEmptyInstance() => create();
  static $pb.PbList<ExtraStatus> createRepeated() => $pb.PbList<ExtraStatus>();
  @$core.pragma('dart2js:noInline')
  static ExtraStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExtraStatus>(create);
  static ExtraStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get title => $_getSZ(0);
  @$pb.TagNumber(1)
  set title($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTitle() => $_has(0);
  @$pb.TagNumber(1)
  void clearTitle() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get iconName => $_getSZ(1);
  @$pb.TagNumber(2)
  set iconName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIconName() => $_has(1);
  @$pb.TagNumber(2)
  void clearIconName() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get iconColor => $_getSZ(2);
  @$pb.TagNumber(3)
  set iconColor($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIconColor() => $_has(2);
  @$pb.TagNumber(3)
  void clearIconColor() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get statusText => $_getSZ(3);
  @$pb.TagNumber(4)
  set statusText($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasStatusText() => $_has(3);
  @$pb.TagNumber(4)
  void clearStatusText() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get statusColor => $_getSZ(4);
  @$pb.TagNumber(5)
  set statusColor($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasStatusColor() => $_has(4);
  @$pb.TagNumber(5)
  void clearStatusColor() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get groupId => $_getSZ(5);
  @$pb.TagNumber(6)
  set groupId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasGroupId() => $_has(5);
  @$pb.TagNumber(6)
  void clearGroupId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get sectionId => $_getSZ(6);
  @$pb.TagNumber(7)
  set sectionId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSectionId() => $_has(6);
  @$pb.TagNumber(7)
  void clearSectionId() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get progress => $_getN(7);
  @$pb.TagNumber(8)
  set progress($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasProgress() => $_has(7);
  @$pb.TagNumber(8)
  void clearProgress() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get width => $_getIZ(8);
  @$pb.TagNumber(9)
  set width($core.int v) { $_setUnsignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWidth() => $_has(8);
  @$pb.TagNumber(9)
  void clearWidth() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get bottomText => $_getSZ(9);
  @$pb.TagNumber(10)
  set bottomText($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasBottomText() => $_has(9);
  @$pb.TagNumber(10)
  void clearBottomText() => clearField(10);
}

class WeedTargeting extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedTargeting', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..hasRequiredFields = false
  ;

  WeedTargeting._() : super();
  factory WeedTargeting({
    $core.bool? enabled,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    return _result;
  }
  factory WeedTargeting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedTargeting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedTargeting clone() => WeedTargeting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedTargeting copyWith(void Function(WeedTargeting) updates) => super.copyWith((message) => updates(message as WeedTargeting)) as WeedTargeting; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedTargeting create() => WeedTargeting._();
  WeedTargeting createEmptyInstance() => create();
  static $pb.PbList<WeedTargeting> createRepeated() => $pb.PbList<WeedTargeting>();
  @$core.pragma('dart2js:noInline')
  static WeedTargeting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedTargeting>(create);
  static WeedTargeting? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);
}

class ThinningTargeting extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ThinningTargeting', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'algorithm', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  ThinningTargeting._() : super();
  factory ThinningTargeting({
    $core.bool? enabled,
  @$core.Deprecated('This field is deprecated.')
    $fixnum.Int64? algorithm,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    if (algorithm != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.algorithm = algorithm;
    }
    return _result;
  }
  factory ThinningTargeting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ThinningTargeting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ThinningTargeting clone() => ThinningTargeting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ThinningTargeting copyWith(void Function(ThinningTargeting) updates) => super.copyWith((message) => updates(message as ThinningTargeting)) as ThinningTargeting; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ThinningTargeting create() => ThinningTargeting._();
  ThinningTargeting createEmptyInstance() => create();
  static $pb.PbList<ThinningTargeting> createRepeated() => $pb.PbList<ThinningTargeting>();
  @$core.pragma('dart2js:noInline')
  static ThinningTargeting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ThinningTargeting>(create);
  static ThinningTargeting? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $fixnum.Int64 get algorithm => $_getI64(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  set algorithm($fixnum.Int64 v) { $_setInt64(1, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  $core.bool hasAlgorithm() => $_has(1);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(2)
  void clearAlgorithm() => clearField(2);
}

class TargetingState extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TargetingState', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOM<WeedTargeting>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedState', subBuilder: WeedTargeting.create)
    ..aOM<ThinningTargeting>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningState', subBuilder: ThinningTargeting.create)
    ..p<$core.bool>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled', $pb.PbFieldType.KB)
    ..m<$core.int, $core.bool>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabledRows', entryClassName: 'TargetingState.EnabledRowsEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OB, packageName: const $pb.PackageName('carbon.frontend.dashboard'))
    ..hasRequiredFields = false
  ;

  TargetingState._() : super();
  factory TargetingState({
    WeedTargeting? weedState,
    ThinningTargeting? thinningState,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.bool>? enabled,
    $core.Map<$core.int, $core.bool>? enabledRows,
  }) {
    final _result = create();
    if (weedState != null) {
      _result.weedState = weedState;
    }
    if (thinningState != null) {
      _result.thinningState = thinningState;
    }
    if (enabled != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.enabled.addAll(enabled);
    }
    if (enabledRows != null) {
      _result.enabledRows.addAll(enabledRows);
    }
    return _result;
  }
  factory TargetingState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TargetingState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TargetingState clone() => TargetingState()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TargetingState copyWith(void Function(TargetingState) updates) => super.copyWith((message) => updates(message as TargetingState)) as TargetingState; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TargetingState create() => TargetingState._();
  TargetingState createEmptyInstance() => create();
  static $pb.PbList<TargetingState> createRepeated() => $pb.PbList<TargetingState>();
  @$core.pragma('dart2js:noInline')
  static TargetingState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TargetingState>(create);
  static TargetingState? _defaultInstance;

  @$pb.TagNumber(1)
  WeedTargeting get weedState => $_getN(0);
  @$pb.TagNumber(1)
  set weedState(WeedTargeting v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedState() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedState() => clearField(1);
  @$pb.TagNumber(1)
  WeedTargeting ensureWeedState() => $_ensure(0);

  @$pb.TagNumber(2)
  ThinningTargeting get thinningState => $_getN(1);
  @$pb.TagNumber(2)
  set thinningState(ThinningTargeting v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasThinningState() => $_has(1);
  @$pb.TagNumber(2)
  void clearThinningState() => clearField(2);
  @$pb.TagNumber(2)
  ThinningTargeting ensureThinningState() => $_ensure(1);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.List<$core.bool> get enabled => $_getList(2);

  @$pb.TagNumber(4)
  $core.Map<$core.int, $core.bool> get enabledRows => $_getMap(3);
}

class ExtraConclusion extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ExtraConclusion', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'title')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'flipThresholds')
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goodThresholdPercent', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mediumThresholdPercent', $pb.PbFieldType.OU3)
    ..aOM<$63.PercentValue>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'percent', subBuilder: $63.PercentValue.create)
    ..hasRequiredFields = false
  ;

  ExtraConclusion._() : super();
  factory ExtraConclusion({
    $core.String? title,
    $core.bool? flipThresholds,
    $core.int? goodThresholdPercent,
    $core.int? mediumThresholdPercent,
    $63.PercentValue? percent,
  }) {
    final _result = create();
    if (title != null) {
      _result.title = title;
    }
    if (flipThresholds != null) {
      _result.flipThresholds = flipThresholds;
    }
    if (goodThresholdPercent != null) {
      _result.goodThresholdPercent = goodThresholdPercent;
    }
    if (mediumThresholdPercent != null) {
      _result.mediumThresholdPercent = mediumThresholdPercent;
    }
    if (percent != null) {
      _result.percent = percent;
    }
    return _result;
  }
  factory ExtraConclusion.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExtraConclusion.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExtraConclusion clone() => ExtraConclusion()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExtraConclusion copyWith(void Function(ExtraConclusion) updates) => super.copyWith((message) => updates(message as ExtraConclusion)) as ExtraConclusion; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ExtraConclusion create() => ExtraConclusion._();
  ExtraConclusion createEmptyInstance() => create();
  static $pb.PbList<ExtraConclusion> createRepeated() => $pb.PbList<ExtraConclusion>();
  @$core.pragma('dart2js:noInline')
  static ExtraConclusion getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExtraConclusion>(create);
  static ExtraConclusion? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get title => $_getSZ(0);
  @$pb.TagNumber(1)
  set title($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTitle() => $_has(0);
  @$pb.TagNumber(1)
  void clearTitle() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get flipThresholds => $_getBF(1);
  @$pb.TagNumber(2)
  set flipThresholds($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFlipThresholds() => $_has(1);
  @$pb.TagNumber(2)
  void clearFlipThresholds() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get goodThresholdPercent => $_getIZ(2);
  @$pb.TagNumber(3)
  set goodThresholdPercent($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGoodThresholdPercent() => $_has(2);
  @$pb.TagNumber(3)
  void clearGoodThresholdPercent() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get mediumThresholdPercent => $_getIZ(3);
  @$pb.TagNumber(4)
  set mediumThresholdPercent($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasMediumThresholdPercent() => $_has(3);
  @$pb.TagNumber(4)
  void clearMediumThresholdPercent() => clearField(4);

  @$pb.TagNumber(5)
  $63.PercentValue get percent => $_getN(4);
  @$pb.TagNumber(5)
  set percent($63.PercentValue v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasPercent() => $_has(4);
  @$pb.TagNumber(5)
  void clearPercent() => clearField(5);
  @$pb.TagNumber(5)
  $63.PercentValue ensurePercent() => $_ensure(4);
}

class RowStateMessage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowStateMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetStateMismatch')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ready')
    ..e<SafetyOverrideState>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'safetyOverrideState', $pb.PbFieldType.OE, defaultOrMaker: SafetyOverrideState.SafetyOverrideNone, valueOf: SafetyOverrideState.valueOf, enumValues: SafetyOverrideState.values)
    ..hasRequiredFields = false
  ;

  RowStateMessage._() : super();
  factory RowStateMessage({
    $core.bool? enabled,
    $core.bool? targetStateMismatch,
    $core.bool? ready,
    SafetyOverrideState? safetyOverrideState,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    if (targetStateMismatch != null) {
      _result.targetStateMismatch = targetStateMismatch;
    }
    if (ready != null) {
      _result.ready = ready;
    }
    if (safetyOverrideState != null) {
      _result.safetyOverrideState = safetyOverrideState;
    }
    return _result;
  }
  factory RowStateMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowStateMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowStateMessage clone() => RowStateMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowStateMessage copyWith(void Function(RowStateMessage) updates) => super.copyWith((message) => updates(message as RowStateMessage)) as RowStateMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowStateMessage create() => RowStateMessage._();
  RowStateMessage createEmptyInstance() => create();
  static $pb.PbList<RowStateMessage> createRepeated() => $pb.PbList<RowStateMessage>();
  @$core.pragma('dart2js:noInline')
  static RowStateMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowStateMessage>(create);
  static RowStateMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get targetStateMismatch => $_getBF(1);
  @$pb.TagNumber(2)
  set targetStateMismatch($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetStateMismatch() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetStateMismatch() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get ready => $_getBF(2);
  @$pb.TagNumber(3)
  set ready($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasReady() => $_has(2);
  @$pb.TagNumber(3)
  void clearReady() => clearField(3);

  @$pb.TagNumber(4)
  SafetyOverrideState get safetyOverrideState => $_getN(3);
  @$pb.TagNumber(4)
  set safetyOverrideState(SafetyOverrideState v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasSafetyOverrideState() => $_has(3);
  @$pb.TagNumber(4)
  void clearSafetyOverrideState() => clearField(4);
}

class DashboardStateMessage extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DashboardStateMessage', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lasersEnabled')
    ..p<$core.bool>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowEnabled', $pb.PbFieldType.KB)
    ..pc<ExtraStatus>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'extras', $pb.PbFieldType.PM, subBuilder: ExtraStatus.create)
    ..aOM<CropModel>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'selectedModel', subBuilder: CropModel.create)
    ..aOM<TargetingState>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetingState', subBuilder: TargetingState.create)
    ..p<$core.bool>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetStateMismatch', $pb.PbFieldType.KB)
    ..p<$core.bool>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowReady', $pb.PbFieldType.KB)
    ..p<$core.bool>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowExists', $pb.PbFieldType.KB)
    ..a<$core.double>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowWidthIn', $pb.PbFieldType.OD)
    ..pc<SafetyOverrideState>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'safetyOverrideState', $pb.PbFieldType.KE, valueOf: SafetyOverrideState.valueOf, enumValues: SafetyOverrideState.values, defaultEnumValue: SafetyOverrideState.SafetyOverrideNone)
    ..e<InternetState>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'internetState', $pb.PbFieldType.OE, defaultOrMaker: InternetState.DISCONNECTED, valueOf: InternetState.valueOf, enumValues: InternetState.values)
    ..e<ImplementState>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'implementState', $pb.PbFieldType.OE, defaultOrMaker: ImplementState.RAISED, valueOf: ImplementState.valueOf, enumValues: ImplementState.values)
    ..aOB(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'efficiencyEnabled')
    ..aOM<$63.PercentValue>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'efficiencyPercent', subBuilder: $63.PercentValue.create)
    ..aOB(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'errorRateEnabled')
    ..aOM<$63.PercentValue>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'errorRate', subBuilder: $63.PercentValue.create)
    ..pc<ExtraConclusion>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'extraConclusions', $pb.PbFieldType.PM, subBuilder: ExtraConclusion.create)
    ..aOM<$63.AreaValue>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'areaWeededToday', subBuilder: $63.AreaValue.create)
    ..aOM<$63.AreaValue>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'areaWeededTotal', subBuilder: $63.AreaValue.create)
    ..aOM<$63.IntegerValue>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsKilledToday', subBuilder: $63.IntegerValue.create)
    ..aOM<$63.IntegerValue>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsKilledTotal', subBuilder: $63.IntegerValue.create)
    ..aOM<$63.DurationValue>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeWeededToday', subBuilder: $63.DurationValue.create)
    ..aOM<$63.DurationValue>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeWeededTotal', subBuilder: $63.DurationValue.create)
    ..aOB(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..aOB(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'debugMode')
    ..aOM<$63.IntegerValue>(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropsKilledToday', subBuilder: $63.IntegerValue.create)
    ..aOM<$63.IntegerValue>(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropsKilledTotal', subBuilder: $63.IntegerValue.create)
    ..aOB(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cruiseEnabled')
    ..m<$core.int, RowStateMessage>(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowStates', entryClassName: 'DashboardStateMessage.RowStatesEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OM, valueCreator: RowStateMessage.create, packageName: const $pb.PackageName('carbon.frontend.dashboard'))
    ..aOB(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cruiseAllowEnable')
    ..hasRequiredFields = false
  ;

  DashboardStateMessage._() : super();
  factory DashboardStateMessage({
    $1.Timestamp? ts,
    $core.bool? lasersEnabled,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.bool>? rowEnabled,
    $core.Iterable<ExtraStatus>? extras,
    CropModel? selectedModel,
    TargetingState? targetingState,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.bool>? targetStateMismatch,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.bool>? rowReady,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<$core.bool>? rowExists,
    $core.double? rowWidthIn,
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<SafetyOverrideState>? safetyOverrideState,
    InternetState? internetState,
    ImplementState? implementState,
    $core.bool? efficiencyEnabled,
    $63.PercentValue? efficiencyPercent,
    $core.bool? errorRateEnabled,
    $63.PercentValue? errorRate,
    $core.Iterable<ExtraConclusion>? extraConclusions,
    $63.AreaValue? areaWeededToday,
    $63.AreaValue? areaWeededTotal,
    $63.IntegerValue? weedsKilledToday,
    $63.IntegerValue? weedsKilledTotal,
    $63.DurationValue? timeWeededToday,
    $63.DurationValue? timeWeededTotal,
    $core.bool? weedingEnabled,
    $core.bool? debugMode,
    $63.IntegerValue? cropsKilledToday,
    $63.IntegerValue? cropsKilledTotal,
    $core.bool? cruiseEnabled,
    $core.Map<$core.int, RowStateMessage>? rowStates,
    $core.bool? cruiseAllowEnable,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (lasersEnabled != null) {
      _result.lasersEnabled = lasersEnabled;
    }
    if (rowEnabled != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.rowEnabled.addAll(rowEnabled);
    }
    if (extras != null) {
      _result.extras.addAll(extras);
    }
    if (selectedModel != null) {
      _result.selectedModel = selectedModel;
    }
    if (targetingState != null) {
      _result.targetingState = targetingState;
    }
    if (targetStateMismatch != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.targetStateMismatch.addAll(targetStateMismatch);
    }
    if (rowReady != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.rowReady.addAll(rowReady);
    }
    if (rowExists != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.rowExists.addAll(rowExists);
    }
    if (rowWidthIn != null) {
      _result.rowWidthIn = rowWidthIn;
    }
    if (safetyOverrideState != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.safetyOverrideState.addAll(safetyOverrideState);
    }
    if (internetState != null) {
      _result.internetState = internetState;
    }
    if (implementState != null) {
      _result.implementState = implementState;
    }
    if (efficiencyEnabled != null) {
      _result.efficiencyEnabled = efficiencyEnabled;
    }
    if (efficiencyPercent != null) {
      _result.efficiencyPercent = efficiencyPercent;
    }
    if (errorRateEnabled != null) {
      _result.errorRateEnabled = errorRateEnabled;
    }
    if (errorRate != null) {
      _result.errorRate = errorRate;
    }
    if (extraConclusions != null) {
      _result.extraConclusions.addAll(extraConclusions);
    }
    if (areaWeededToday != null) {
      _result.areaWeededToday = areaWeededToday;
    }
    if (areaWeededTotal != null) {
      _result.areaWeededTotal = areaWeededTotal;
    }
    if (weedsKilledToday != null) {
      _result.weedsKilledToday = weedsKilledToday;
    }
    if (weedsKilledTotal != null) {
      _result.weedsKilledTotal = weedsKilledTotal;
    }
    if (timeWeededToday != null) {
      _result.timeWeededToday = timeWeededToday;
    }
    if (timeWeededTotal != null) {
      _result.timeWeededTotal = timeWeededTotal;
    }
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (debugMode != null) {
      _result.debugMode = debugMode;
    }
    if (cropsKilledToday != null) {
      _result.cropsKilledToday = cropsKilledToday;
    }
    if (cropsKilledTotal != null) {
      _result.cropsKilledTotal = cropsKilledTotal;
    }
    if (cruiseEnabled != null) {
      _result.cruiseEnabled = cruiseEnabled;
    }
    if (rowStates != null) {
      _result.rowStates.addAll(rowStates);
    }
    if (cruiseAllowEnable != null) {
      _result.cruiseAllowEnable = cruiseAllowEnable;
    }
    return _result;
  }
  factory DashboardStateMessage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DashboardStateMessage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DashboardStateMessage clone() => DashboardStateMessage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DashboardStateMessage copyWith(void Function(DashboardStateMessage) updates) => super.copyWith((message) => updates(message as DashboardStateMessage)) as DashboardStateMessage; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DashboardStateMessage create() => DashboardStateMessage._();
  DashboardStateMessage createEmptyInstance() => create();
  static $pb.PbList<DashboardStateMessage> createRepeated() => $pb.PbList<DashboardStateMessage>();
  @$core.pragma('dart2js:noInline')
  static DashboardStateMessage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DashboardStateMessage>(create);
  static DashboardStateMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get lasersEnabled => $_getBF(1);
  @$pb.TagNumber(2)
  set lasersEnabled($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLasersEnabled() => $_has(1);
  @$pb.TagNumber(2)
  void clearLasersEnabled() => clearField(2);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(3)
  $core.List<$core.bool> get rowEnabled => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<ExtraStatus> get extras => $_getList(3);

  @$pb.TagNumber(5)
  CropModel get selectedModel => $_getN(4);
  @$pb.TagNumber(5)
  set selectedModel(CropModel v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasSelectedModel() => $_has(4);
  @$pb.TagNumber(5)
  void clearSelectedModel() => clearField(5);
  @$pb.TagNumber(5)
  CropModel ensureSelectedModel() => $_ensure(4);

  @$pb.TagNumber(6)
  TargetingState get targetingState => $_getN(5);
  @$pb.TagNumber(6)
  set targetingState(TargetingState v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasTargetingState() => $_has(5);
  @$pb.TagNumber(6)
  void clearTargetingState() => clearField(6);
  @$pb.TagNumber(6)
  TargetingState ensureTargetingState() => $_ensure(5);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(7)
  $core.List<$core.bool> get targetStateMismatch => $_getList(6);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(8)
  $core.List<$core.bool> get rowReady => $_getList(7);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(9)
  $core.List<$core.bool> get rowExists => $_getList(8);

  @$pb.TagNumber(10)
  $core.double get rowWidthIn => $_getN(9);
  @$pb.TagNumber(10)
  set rowWidthIn($core.double v) { $_setDouble(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasRowWidthIn() => $_has(9);
  @$pb.TagNumber(10)
  void clearRowWidthIn() => clearField(10);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.List<SafetyOverrideState> get safetyOverrideState => $_getList(10);

  @$pb.TagNumber(12)
  InternetState get internetState => $_getN(11);
  @$pb.TagNumber(12)
  set internetState(InternetState v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasInternetState() => $_has(11);
  @$pb.TagNumber(12)
  void clearInternetState() => clearField(12);

  @$pb.TagNumber(13)
  ImplementState get implementState => $_getN(12);
  @$pb.TagNumber(13)
  set implementState(ImplementState v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasImplementState() => $_has(12);
  @$pb.TagNumber(13)
  void clearImplementState() => clearField(13);

  @$pb.TagNumber(14)
  $core.bool get efficiencyEnabled => $_getBF(13);
  @$pb.TagNumber(14)
  set efficiencyEnabled($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasEfficiencyEnabled() => $_has(13);
  @$pb.TagNumber(14)
  void clearEfficiencyEnabled() => clearField(14);

  @$pb.TagNumber(15)
  $63.PercentValue get efficiencyPercent => $_getN(14);
  @$pb.TagNumber(15)
  set efficiencyPercent($63.PercentValue v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasEfficiencyPercent() => $_has(14);
  @$pb.TagNumber(15)
  void clearEfficiencyPercent() => clearField(15);
  @$pb.TagNumber(15)
  $63.PercentValue ensureEfficiencyPercent() => $_ensure(14);

  @$pb.TagNumber(16)
  $core.bool get errorRateEnabled => $_getBF(15);
  @$pb.TagNumber(16)
  set errorRateEnabled($core.bool v) { $_setBool(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasErrorRateEnabled() => $_has(15);
  @$pb.TagNumber(16)
  void clearErrorRateEnabled() => clearField(16);

  @$pb.TagNumber(17)
  $63.PercentValue get errorRate => $_getN(16);
  @$pb.TagNumber(17)
  set errorRate($63.PercentValue v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasErrorRate() => $_has(16);
  @$pb.TagNumber(17)
  void clearErrorRate() => clearField(17);
  @$pb.TagNumber(17)
  $63.PercentValue ensureErrorRate() => $_ensure(16);

  @$pb.TagNumber(18)
  $core.List<ExtraConclusion> get extraConclusions => $_getList(17);

  @$pb.TagNumber(19)
  $63.AreaValue get areaWeededToday => $_getN(18);
  @$pb.TagNumber(19)
  set areaWeededToday($63.AreaValue v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasAreaWeededToday() => $_has(18);
  @$pb.TagNumber(19)
  void clearAreaWeededToday() => clearField(19);
  @$pb.TagNumber(19)
  $63.AreaValue ensureAreaWeededToday() => $_ensure(18);

  @$pb.TagNumber(20)
  $63.AreaValue get areaWeededTotal => $_getN(19);
  @$pb.TagNumber(20)
  set areaWeededTotal($63.AreaValue v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasAreaWeededTotal() => $_has(19);
  @$pb.TagNumber(20)
  void clearAreaWeededTotal() => clearField(20);
  @$pb.TagNumber(20)
  $63.AreaValue ensureAreaWeededTotal() => $_ensure(19);

  @$pb.TagNumber(21)
  $63.IntegerValue get weedsKilledToday => $_getN(20);
  @$pb.TagNumber(21)
  set weedsKilledToday($63.IntegerValue v) { setField(21, v); }
  @$pb.TagNumber(21)
  $core.bool hasWeedsKilledToday() => $_has(20);
  @$pb.TagNumber(21)
  void clearWeedsKilledToday() => clearField(21);
  @$pb.TagNumber(21)
  $63.IntegerValue ensureWeedsKilledToday() => $_ensure(20);

  @$pb.TagNumber(22)
  $63.IntegerValue get weedsKilledTotal => $_getN(21);
  @$pb.TagNumber(22)
  set weedsKilledTotal($63.IntegerValue v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasWeedsKilledTotal() => $_has(21);
  @$pb.TagNumber(22)
  void clearWeedsKilledTotal() => clearField(22);
  @$pb.TagNumber(22)
  $63.IntegerValue ensureWeedsKilledTotal() => $_ensure(21);

  @$pb.TagNumber(23)
  $63.DurationValue get timeWeededToday => $_getN(22);
  @$pb.TagNumber(23)
  set timeWeededToday($63.DurationValue v) { setField(23, v); }
  @$pb.TagNumber(23)
  $core.bool hasTimeWeededToday() => $_has(22);
  @$pb.TagNumber(23)
  void clearTimeWeededToday() => clearField(23);
  @$pb.TagNumber(23)
  $63.DurationValue ensureTimeWeededToday() => $_ensure(22);

  @$pb.TagNumber(24)
  $63.DurationValue get timeWeededTotal => $_getN(23);
  @$pb.TagNumber(24)
  set timeWeededTotal($63.DurationValue v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasTimeWeededTotal() => $_has(23);
  @$pb.TagNumber(24)
  void clearTimeWeededTotal() => clearField(24);
  @$pb.TagNumber(24)
  $63.DurationValue ensureTimeWeededTotal() => $_ensure(23);

  @$pb.TagNumber(25)
  $core.bool get weedingEnabled => $_getBF(24);
  @$pb.TagNumber(25)
  set weedingEnabled($core.bool v) { $_setBool(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasWeedingEnabled() => $_has(24);
  @$pb.TagNumber(25)
  void clearWeedingEnabled() => clearField(25);

  @$pb.TagNumber(26)
  $core.bool get debugMode => $_getBF(25);
  @$pb.TagNumber(26)
  set debugMode($core.bool v) { $_setBool(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasDebugMode() => $_has(25);
  @$pb.TagNumber(26)
  void clearDebugMode() => clearField(26);

  @$pb.TagNumber(27)
  $63.IntegerValue get cropsKilledToday => $_getN(26);
  @$pb.TagNumber(27)
  set cropsKilledToday($63.IntegerValue v) { setField(27, v); }
  @$pb.TagNumber(27)
  $core.bool hasCropsKilledToday() => $_has(26);
  @$pb.TagNumber(27)
  void clearCropsKilledToday() => clearField(27);
  @$pb.TagNumber(27)
  $63.IntegerValue ensureCropsKilledToday() => $_ensure(26);

  @$pb.TagNumber(28)
  $63.IntegerValue get cropsKilledTotal => $_getN(27);
  @$pb.TagNumber(28)
  set cropsKilledTotal($63.IntegerValue v) { setField(28, v); }
  @$pb.TagNumber(28)
  $core.bool hasCropsKilledTotal() => $_has(27);
  @$pb.TagNumber(28)
  void clearCropsKilledTotal() => clearField(28);
  @$pb.TagNumber(28)
  $63.IntegerValue ensureCropsKilledTotal() => $_ensure(27);

  @$pb.TagNumber(29)
  $core.bool get cruiseEnabled => $_getBF(28);
  @$pb.TagNumber(29)
  set cruiseEnabled($core.bool v) { $_setBool(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasCruiseEnabled() => $_has(28);
  @$pb.TagNumber(29)
  void clearCruiseEnabled() => clearField(29);

  @$pb.TagNumber(30)
  $core.Map<$core.int, RowStateMessage> get rowStates => $_getMap(29);

  @$pb.TagNumber(31)
  $core.bool get cruiseAllowEnable => $_getBF(30);
  @$pb.TagNumber(31)
  set cruiseAllowEnable($core.bool v) { $_setBool(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasCruiseAllowEnable() => $_has(30);
  @$pb.TagNumber(31)
  void clearCruiseAllowEnable() => clearField(31);
}

class CropModel extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CropModel', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hasModel')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'preferred')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..hasRequiredFields = false
  ;

  CropModel._() : super();
  factory CropModel({
  @$core.Deprecated('This field is deprecated.')
    $core.String? crop,
    $core.bool? hasModel,
    $core.bool? preferred,
    $core.String? cropId,
  }) {
    final _result = create();
    if (crop != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.crop = crop;
    }
    if (hasModel != null) {
      _result.hasModel = hasModel;
    }
    if (preferred != null) {
      _result.preferred = preferred;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    return _result;
  }
  factory CropModel.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CropModel.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CropModel clone() => CropModel()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CropModel copyWith(void Function(CropModel) updates) => super.copyWith((message) => updates(message as CropModel)) as CropModel; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CropModel create() => CropModel._();
  CropModel createEmptyInstance() => create();
  static $pb.PbList<CropModel> createRepeated() => $pb.PbList<CropModel>();
  @$core.pragma('dart2js:noInline')
  static CropModel getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CropModel>(create);
  static CropModel? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get crop => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set crop($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasCrop() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearCrop() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get hasModel => $_getBF(1);
  @$pb.TagNumber(2)
  set hasModel($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHasModel() => $_has(1);
  @$pb.TagNumber(2)
  void clearHasModel() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get preferred => $_getBF(2);
  @$pb.TagNumber(3)
  set preferred($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPreferred() => $_has(2);
  @$pb.TagNumber(3)
  void clearPreferred() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get cropId => $_getSZ(3);
  @$pb.TagNumber(4)
  set cropId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCropId() => $_has(3);
  @$pb.TagNumber(4)
  void clearCropId() => clearField(4);
}

class CropModelOptions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CropModelOptions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..pc<CropModel>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'models', $pb.PbFieldType.PM, subBuilder: CropModel.create)
    ..hasRequiredFields = false
  ;

  CropModelOptions._() : super();
  factory CropModelOptions({
    $core.Iterable<CropModel>? models,
  }) {
    final _result = create();
    if (models != null) {
      _result.models.addAll(models);
    }
    return _result;
  }
  factory CropModelOptions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CropModelOptions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CropModelOptions clone() => CropModelOptions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CropModelOptions copyWith(void Function(CropModelOptions) updates) => super.copyWith((message) => updates(message as CropModelOptions)) as CropModelOptions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CropModelOptions create() => CropModelOptions._();
  CropModelOptions createEmptyInstance() => create();
  static $pb.PbList<CropModelOptions> createRepeated() => $pb.PbList<CropModelOptions>();
  @$core.pragma('dart2js:noInline')
  static CropModelOptions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CropModelOptions>(create);
  static CropModelOptions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<CropModel> get models => $_getList(0);
}

class RowId extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowId', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowNumber', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  RowId._() : super();
  factory RowId({
    $core.int? rowNumber,
  }) {
    final _result = create();
    if (rowNumber != null) {
      _result.rowNumber = rowNumber;
    }
    return _result;
  }
  factory RowId.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowId.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowId clone() => RowId()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowId copyWith(void Function(RowId) updates) => super.copyWith((message) => updates(message as RowId)) as RowId; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowId create() => RowId._();
  RowId createEmptyInstance() => create();
  static $pb.PbList<RowId> createRepeated() => $pb.PbList<RowId>();
  @$core.pragma('dart2js:noInline')
  static RowId getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowId>(create);
  static RowId? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowNumber => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowNumber($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowNumber() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowNumber() => clearField(1);
}

class WeedingVelocity extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedingVelocity', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'currentVelocityMph', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVelocityMph', $pb.PbFieldType.OD)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'toleranceMph', $pb.PbFieldType.OD)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'primaryTargetVelocityTopMph', $pb.PbFieldType.OD)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'primaryTargetVelocityBottomMph', $pb.PbFieldType.OD)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'secondaryTargetVelocityTopMph', $pb.PbFieldType.OD)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'secondaryTargetVelocityBottomMph', $pb.PbFieldType.OD)
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cruiseControlVelocityMph', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  WeedingVelocity._() : super();
  factory WeedingVelocity({
    $1.Timestamp? ts,
    $core.double? currentVelocityMph,
    $core.double? targetVelocityMph,
    $core.double? toleranceMph,
    $core.double? primaryTargetVelocityTopMph,
    $core.double? primaryTargetVelocityBottomMph,
    $core.double? secondaryTargetVelocityTopMph,
    $core.double? secondaryTargetVelocityBottomMph,
    $core.double? cruiseControlVelocityMph,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (currentVelocityMph != null) {
      _result.currentVelocityMph = currentVelocityMph;
    }
    if (targetVelocityMph != null) {
      _result.targetVelocityMph = targetVelocityMph;
    }
    if (toleranceMph != null) {
      _result.toleranceMph = toleranceMph;
    }
    if (primaryTargetVelocityTopMph != null) {
      _result.primaryTargetVelocityTopMph = primaryTargetVelocityTopMph;
    }
    if (primaryTargetVelocityBottomMph != null) {
      _result.primaryTargetVelocityBottomMph = primaryTargetVelocityBottomMph;
    }
    if (secondaryTargetVelocityTopMph != null) {
      _result.secondaryTargetVelocityTopMph = secondaryTargetVelocityTopMph;
    }
    if (secondaryTargetVelocityBottomMph != null) {
      _result.secondaryTargetVelocityBottomMph = secondaryTargetVelocityBottomMph;
    }
    if (cruiseControlVelocityMph != null) {
      _result.cruiseControlVelocityMph = cruiseControlVelocityMph;
    }
    return _result;
  }
  factory WeedingVelocity.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedingVelocity.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedingVelocity clone() => WeedingVelocity()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedingVelocity copyWith(void Function(WeedingVelocity) updates) => super.copyWith((message) => updates(message as WeedingVelocity)) as WeedingVelocity; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedingVelocity create() => WeedingVelocity._();
  WeedingVelocity createEmptyInstance() => create();
  static $pb.PbList<WeedingVelocity> createRepeated() => $pb.PbList<WeedingVelocity>();
  @$core.pragma('dart2js:noInline')
  static WeedingVelocity getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedingVelocity>(create);
  static WeedingVelocity? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get currentVelocityMph => $_getN(1);
  @$pb.TagNumber(2)
  set currentVelocityMph($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCurrentVelocityMph() => $_has(1);
  @$pb.TagNumber(2)
  void clearCurrentVelocityMph() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get targetVelocityMph => $_getN(2);
  @$pb.TagNumber(3)
  set targetVelocityMph($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTargetVelocityMph() => $_has(2);
  @$pb.TagNumber(3)
  void clearTargetVelocityMph() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get toleranceMph => $_getN(3);
  @$pb.TagNumber(4)
  set toleranceMph($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasToleranceMph() => $_has(3);
  @$pb.TagNumber(4)
  void clearToleranceMph() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get primaryTargetVelocityTopMph => $_getN(4);
  @$pb.TagNumber(5)
  set primaryTargetVelocityTopMph($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPrimaryTargetVelocityTopMph() => $_has(4);
  @$pb.TagNumber(5)
  void clearPrimaryTargetVelocityTopMph() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get primaryTargetVelocityBottomMph => $_getN(5);
  @$pb.TagNumber(6)
  set primaryTargetVelocityBottomMph($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPrimaryTargetVelocityBottomMph() => $_has(5);
  @$pb.TagNumber(6)
  void clearPrimaryTargetVelocityBottomMph() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get secondaryTargetVelocityTopMph => $_getN(6);
  @$pb.TagNumber(7)
  set secondaryTargetVelocityTopMph($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSecondaryTargetVelocityTopMph() => $_has(6);
  @$pb.TagNumber(7)
  void clearSecondaryTargetVelocityTopMph() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get secondaryTargetVelocityBottomMph => $_getN(7);
  @$pb.TagNumber(8)
  set secondaryTargetVelocityBottomMph($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasSecondaryTargetVelocityBottomMph() => $_has(7);
  @$pb.TagNumber(8)
  void clearSecondaryTargetVelocityBottomMph() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get cruiseControlVelocityMph => $_getN(8);
  @$pb.TagNumber(9)
  set cruiseControlVelocityMph($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCruiseControlVelocityMph() => $_has(8);
  @$pb.TagNumber(9)
  void clearCruiseControlVelocityMph() => clearField(9);
}

class RowSpacing extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RowSpacing', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  RowSpacing._() : super();
  factory RowSpacing({
    $core.double? width,
  }) {
    final _result = create();
    if (width != null) {
      _result.width = width;
    }
    return _result;
  }
  factory RowSpacing.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RowSpacing.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RowSpacing clone() => RowSpacing()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RowSpacing copyWith(void Function(RowSpacing) updates) => super.copyWith((message) => updates(message as RowSpacing)) as RowSpacing; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RowSpacing create() => RowSpacing._();
  RowSpacing createEmptyInstance() => create();
  static $pb.PbList<RowSpacing> createRepeated() => $pb.PbList<RowSpacing>();
  @$core.pragma('dart2js:noInline')
  static RowSpacing getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RowSpacing>(create);
  static RowSpacing? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get width => $_getN(0);
  @$pb.TagNumber(1)
  set width($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWidth() => $_has(0);
  @$pb.TagNumber(1)
  void clearWidth() => clearField(1);
}

class CruiseEnable extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CruiseEnable', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.dashboard'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enabled')
    ..hasRequiredFields = false
  ;

  CruiseEnable._() : super();
  factory CruiseEnable({
    $core.bool? enabled,
  }) {
    final _result = create();
    if (enabled != null) {
      _result.enabled = enabled;
    }
    return _result;
  }
  factory CruiseEnable.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CruiseEnable.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CruiseEnable clone() => CruiseEnable()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CruiseEnable copyWith(void Function(CruiseEnable) updates) => super.copyWith((message) => updates(message as CruiseEnable)) as CruiseEnable; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CruiseEnable create() => CruiseEnable._();
  CruiseEnable createEmptyInstance() => create();
  static $pb.PbList<CruiseEnable> createRepeated() => $pb.PbList<CruiseEnable>();
  @$core.pragma('dart2js:noInline')
  static CruiseEnable getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CruiseEnable>(create);
  static CruiseEnable? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enabled => $_getBF(0);
  @$pb.TagNumber(1)
  set enabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnabled() => clearField(1);
}

