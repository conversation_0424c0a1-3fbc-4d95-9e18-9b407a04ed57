syntax = "proto3";

package carbon.frontend.banding;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/frontend";

import "util/util.proto";
import "core/controls/exterminator/controllers/aimbot/process/aimbot.proto";
import "weed_tracking/weed_tracking.proto";

message BandingRow {
  int32 row_id = 1;
  repeated weed_tracking.BandDefinition bands = 2;
}

message BandingDef {
  string name = 1;
  repeated BandingRow rows = 2;
  string uuid = 3;
}

message SaveBandingDefRequest {
  BandingDef bandingDef = 1;
  bool setActive = 2;
}

message LoadBandingDefsResponse {
  repeated BandingDef bandingDefs = 1;
  string activeDef = 2 [ deprecated = true ];
  string activeDefUUID = 3;
}

message SetActiveBandingDefRequest {
  string name = 1 [ deprecated = true ];
  string uuid = 2;
}

message GetActiveBandingDefResponse {
  string name = 1 [ deprecated = true ];
  string uuid = 2;
}

message DeleteBandingDefRequest {
  string name = 1 [ deprecated = true ];
  string uuid = 2;
}

message VisualizationData {
  int32 x_mm = 1;
  int32 y_mm = 2;
  int32 z_mm = 3;
  bool is_weed = 4;
}

message GetNextVisualizationDataRequest {
  carbon.util.Timestamp ts = 1;
  int32 row_id = 2;
  repeated VisualizationTypeToInclude types_to_include = 3;
  ThresholdFilters threshold_filters = 4;
  optional bool include_detailed_metadata = 5;
}

message GetNextVisualizationDataResponse {
  carbon.util.Timestamp ts = 1;
  repeated VisualizationData data = 2;
  repeated weed_tracking.BandDefinition bands = 3;
}

message GetNextVisualizationData2Response {
  weed_tracking.DiagnosticsSnapshot data = 1;
  carbon.util.Timestamp ts = 2;
}

message GetDimensionsRequest { int32 row_id = 1; }

message SetBandingEnabledRequest { bool enabled = 1; }

message IsBandingEnabledResponse { bool enabled = 1; }

message GetVisualizationMetadataResponse {
  map<int32, float> crop_safety_radius_mm_per_row = 1;
}

enum VisualizationTypeToInclude {
  DUPLICATE_WEED = 0;
  DUPLICATE_CROP = 1;
  KILLED_WEED = 2;
  KILLED_CROP = 3;
  KILLING_WEED = 4;
  IGNORED_WEED = 5;
  KILLING_CROP = 6;
  ERROR_WEED = 7;
  ERROR_CROP = 8;
  IGNORED_CROP = 9;
  WEED = 10;
  CROP = 11;
  CROP_RADIUS = 12;
  CROP_KEPT = 13;
  THINNING_BOX = 14;
}
enum ThresholdState {
  ANY = 0;
  PASS = 1;
  FAIL = 2;
}

message ThresholdFilter {
  ThresholdState weeding = 1;
  ThresholdState thinning = 2;
  ThresholdState banding = 3;
}

message ThresholdFilters {
  ThresholdFilter crop = 1;
  ThresholdFilter weed = 2;
}

message GetNextVisualizationDataForAllRowsRequest {
  carbon.util.Timestamp ts = 1;
  repeated VisualizationTypeToInclude types_to_include = 2;
  ThresholdFilters threshold_filters = 3;
  optional bool include_detailed_metadata = 4;
}

message GetNextVisualizationDataForAllRowsResponse {
  map<int32, weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
  carbon.util.Timestamp ts = 2;
  repeated VisualizationTypeToInclude types_to_include = 3;
}

message GetNextBandingStateResponse {
  carbon.util.Timestamp ts = 1;
  repeated BandingDef bandingDefs = 2;
  string activeDefUUID = 3;
  bool is_banding_enabled = 4;
  bool is_dynamic_banding_enabled = 5;
}

service BandingService {
  rpc LoadBandingDefs(carbon.util.Empty) returns (LoadBandingDefsResponse);
  rpc SaveBandingDef(SaveBandingDefRequest) returns (carbon.util.Empty);
  rpc DeleteBandingDef(DeleteBandingDefRequest) returns (carbon.util.Empty);
  rpc SetActiveBandingDef(SetActiveBandingDefRequest)
      returns (carbon.util.Empty);
  rpc GetActiveBandingDef(carbon.util.Empty)
      returns (GetActiveBandingDefResponse);
  rpc GetNextVisualizationData(GetNextVisualizationDataRequest)
      returns (GetNextVisualizationDataResponse);
  rpc GetNextVisualizationData2(GetNextVisualizationDataRequest)
      returns (GetNextVisualizationData2Response);
  rpc GetNextVisualizationDataForAllRows(
      GetNextVisualizationDataForAllRowsRequest)
      returns (GetNextVisualizationDataForAllRowsResponse);
  rpc GetDimensions(GetDimensionsRequest)
      returns (aimbot.GetDimensionsResponse);
  rpc SetBandingEnabled(SetBandingEnabledRequest) returns (carbon.util.Empty);
  rpc IsBandingEnabled(carbon.util.Empty) returns (IsBandingEnabledResponse);
  rpc SetDynamicBandingEnabled(SetBandingEnabledRequest)
      returns (carbon.util.Empty);
  rpc IsDynamicBandingEnabled(carbon.util.Empty)
      returns (IsBandingEnabledResponse);
  rpc GetVisualizationMetadata(carbon.util.Empty)
      returns (GetVisualizationMetadataResponse);
  rpc GetNextBandingState(carbon.util.Timestamp)
      returns (GetNextBandingStateResponse);
}
