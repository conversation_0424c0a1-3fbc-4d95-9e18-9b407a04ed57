syntax = "proto3";

package carbon.robot_syncer.audit;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer";

import "google/protobuf/timestamp.proto";

import "config/api/config_service.proto";

message ConfigAuditLog {
  // Time that this operation was recorded in RoSy, i.e. synced up to the
  // cloud. If this operation happened on the robot, it may have occurred at an
  // earlier date.
  google.protobuf.Timestamp create_time = 1;

  // Auth0 user ID of the user under whose authority this change was made, or
  // simply the robot hostname (e.g., "reaper114") if this was made directly on
  // a robot with no end-user credentials available.
  string user_id = 2;

  // The method that was called in this operation, e.g. "UpdateConfigNode" or
  // "SetTree". These do not necessarily correspond 1:1 with gRPC method calls
  // or REST endpoints.
  string method = 3;

  // Serial of the robot whose config was updated.
  string serial = 4;

  // Full config key that was updated, under the root of the updated robot:
  // e.g., "common/software_manager/target_version".
  string key = 5;

  // Value of this config node before the change, if applicable for this
  // operation. This is set when the operation changes a scalar, like
  // `SetValue`. It's not set when the node is a list or a tree.
  carbon.config.proto.ConfigValue old_value = 6;

  // Value of this config node after the change, if applicable for this
  // operation. This is set when the operation changes a scalar, like
  // `SetValue`. It's not set when the node is a list or a tree.
  carbon.config.proto.ConfigValue new_value = 7;
}

message GetConfigAuditLogsResponse { repeated ConfigAuditLog audit_logs = 1; }
