syntax = "proto3";

package carbon.rtc;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/rtc";

message PathPlanConfiguration {
  bool do_headland_first = 1;
  float row_heading_deg = 2;
  float headland_width_m = 3;
  Direction turn_direction = 4;
  int32 num_headland_passes = 5;
  float combined_turn_radius_m = 6;
}

enum Direction {
  DIRECTION_UNSPECIFIED = 0;
  CW = 1;
  CCW = 2;
}
