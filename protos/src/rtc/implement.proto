syntax = "proto3";

package carbon.rtc;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/rtc";

enum HitchType {
  HITCH_TYPE_UNSPECIFIED = 0;
  FIXED = 1;
  THREE_POINT = 2;
}

message ImplementDefinition {
  HitchType hitch_type = 1;
  float width_m = 2;
  float hitch_to_start_m = 3;
  float hitch_to_end_m = 4;
  float tool_speed_kmh = 5;
  float transition_speed_kmh = 6;
  float run_up_distance_m = 7;
  float straighten_distance_m = 8;
}
