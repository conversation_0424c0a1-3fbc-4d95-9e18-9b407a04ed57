syntax = "proto3";

// Geospatial types: these correspond to standard data types used across GIS
// systems. For example, see the GeoJSON spec for details:
// https://www.rfc-editor.org/rfc/rfc7946#section-3.1
package carbon.geo;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/geo";

import "google/protobuf/timestamp.proto";

// Type of "fix", indicating the method by which a point was captured. This
// informs the precision of the measurement.
//
// These categories map to fix types for GGA messages under the NMEA 0183 spec,
// though the numeric enum values differ.
enum FixType {
  FIX_TYPE_UNSPECIFIED = 0;
  NO_FIX = 1;
  GNSS = 2;
  DIFFERENTIAL_GNSS = 3;
  RTK_FIXED = 4;
  RTK_FLOAT = 5;
  DEAD_RECKONING = 6;
}

// Information about how a point was recorded.
message CaptureInfo {
  FixType fix_type = 1;
  google.protobuf.Timestamp capture_time = 2;
}

message Id {
  // Globally unique ID for this entity.
  //
  // This should be drawn uniformly from the space of 12-character strings made
  // of ASCII letters and digits (alphabet size 62), which has ~71.4 bits of
  // entropy. For example: "rI4j42TlGw8f".
  string id = 1;
}

// A point on the earth represented in the WGS 84 geodetic coordinate system.
message Point {
  // Longitude, as degrees east of the prime meridian.
  double lng = 1;
  // Latitude, as degrees north of the equator.
  double lat = 2;
  // Altitude, as height in meters above the reference ellipsoid.
  double alt = 3;

  CaptureInfo capture_info = 4;

  // Globally unique identifier for this point.
  //
  // Geometries containing points can include only the ID to incorporate
  // points by reference; e.g., the following `LineString` could
  // represent a closed path:
  //
  //    {
  //      points { id { id: "Xb8M8INv9bo" } }
  //      points { id { id: "B8pNtE3frX4" } }
  //      points { id { id: "a78CkdKl7gk" } }
  //      points { id { id: "Xb8M8INv9bo" } }
  //    }
  Id id = 5;
  // Optional human-readable name for this point.
  string name = 6;
}

message LineString { repeated Point points = 1; }

// An A-B segment, typically indicating a ray, a line, or a heading.
message AbLine {
  // The start of the line.
  Point a = 1;
  // The end of the line.
  Point b = 2;
}

// A region on the earth, which has an exterior boundary and possible some
// interior holes. The exterior boundary must be wound counterclockwise and
// each hole must be wound clockwise.
message Polygon {
  PolygonRing boundary = 1;
  repeated PolygonRing holes = 2;
}

// A linear ring on the boundary of the polygon, forming an exterior boundary
// or interior hole. The ring must contain at least four points, and the first
// and last points must be identical.
message PolygonRing { repeated Point points = 1; }

// A union of disjoint `Polygon`s.
message MultiPolygon { repeated Polygon polygons = 1; }
