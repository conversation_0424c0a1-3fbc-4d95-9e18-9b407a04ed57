syntax = "proto3";

package carbon.portal.assets;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

// Shared location types

enum ReaperModuleAbLocation {
  LOCATION_UNSPECIFIED = 0;
  LOCATION_A = 1;
  LOCATION_B = 2;
}

message ReaperModuleAbAssetLocation {
  string weeding_module_serial = 1;
  ReaperModuleAbLocation ab_location = 2;
}

message ReaperModuleSingleAssetLocation { string weeding_module_serial = 1; }

message SlayerRowAssetLocation {
  string robot_serial = 1;
  int32 row = 2;
  int32 id = 3;
}

// Scanners

message Scanner {
  string serial = 1;
  ScannerLocation location = 2;
}

message ScannerLocation {
  oneof generation {
    SlayerRowAssetLocation slayer = 1;
    ReaperModuleAbAssetLocation reaper = 2;
  }
}

// lasers

message Laser {
  string serial = 1;
  LaserLocation location = 2;
}

message LaserLocation {
  oneof generation {
    SlayerRowAssetLocation slayer = 1;
    ReaperModuleAbAssetLocation reaper = 2;
  }
}

// Target Cams

message TargetCam {
  string serial = 1;
  TargetCamLocation location = 2;
}

message TargetCamLocation { string scanner_serial = 1; }

// Predict Cams

message PredictCam {
  string serial = 1;
  PredictCamLocation location = 2;
}

message PredictCamLocation {
  oneof generation {
    SlayerRowAssetLocation slayer = 1;
    ReaperModuleSingleAssetLocation reaper = 2;
  }
}

// Computers

message CommandComputer {
  string serial = 1;
  CommandComputerLocation location = 2;
}

message CommandComputerLocation { string robot_serial = 1; }

message RowComputer {
  string serial = 1;
  RowComputerLocation location = 2;
}

message RowComputerLocation {
  oneof generation {
    SlayerRowComputerLocation slayer = 1;
    ReaperModuleSingleAssetLocation reaper = 2;
  }
}

message SlayerRowComputerLocation {
  string robot_serial = 1;
  int32 row = 2;
  SlayerRowComputerType type = 3;
}

enum SlayerRowComputerType {
  COMPUTER_UNKNOWN = 0;
  COMPUTER_SINGLE = 1;
  COMPUTER_PRIMARY = 2;
  COMPUTER_SECONDARY = 3;
}

// Weeding modules

message WeedingModule {
  string serial = 1;
  WeedingModuleLocation location = 2;
}

message WeedingModuleLocation {
  string robot_serial = 1;
  int32 module_number = 2;
}

// Starlink

message Starlink {
  string serial = 1;
  StarlinkLocation location = 2;
}

message StarlinkLocation { string robot_serial = 1; }

// Modem

message Modem {
  string serial = 1;
  ModemLocation location = 2;
}

message ModemLocation { string robot_serial = 1; }

// Asset manifest

message AssetManifest {
  google.protobuf.Timestamp create_time = 1;
  string robot_serial = 2;

  CommandComputer command_computer = 4;
  repeated WeedingModule weeding_modules = 3;
  repeated RowComputer row_computers = 5;
  repeated Scanner scanners = 6;
  repeated Laser lasers = 7;
  repeated TargetCam target_cams = 8;
  repeated PredictCam predict_cams = 9;
  Modem modem = 10;
  Starlink starlink = 11;
}

service AssetManifestService {
  rpc UploadAssetManifest(AssetManifest) returns (google.protobuf.Empty);
}
