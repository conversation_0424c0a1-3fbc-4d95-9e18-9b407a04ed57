syntax = "proto3";

package carbon.portal.customers;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

import "portal/db.proto";

message FeatureFlags {
  bool reports = 1;
  bool almanac = 2;
  bool jobs = 3;
  bool unvalidated_metrics = 4;
  bool spatial = 5;
  bool velocity_estimator = 6;
  bool explore = 7 [ deprecated = true ];
  bool category_collection = 8;
  bool metrics_redesign = 9;
}

message CustomerResponse {
  carbon.portal.db.DB db = 1;
  string name = 2;
  string sfdc_account_id = 3 [ deprecated = true ];
  repeated string emails = 4;
  FeatureFlags featureFlags = 5;
  int64 weekly_report_hour = 6;
  int64 weekly_report_day = 7;
  bool weekly_report_enabled = 8;
  int64 weekly_report_lookback_days = 9;
  string weekly_report_timezone = 10;
}

message Customer {
  string uuid = 1;
  string name = 2;
}

message ListCustomersRequest {
  // Cursor from `ListCustomersResponse.next_page_token` on a previous response.
  // If blank, starts from the beginning.
  string page_token = 1;
}

message ListCustomersResponse {
  repeated Customer customers = 1;
  // Cursor for `ListCustomersRequest.page_token`. If blank, there are no
  // further pages.
  string next_page_token = 2;
}
