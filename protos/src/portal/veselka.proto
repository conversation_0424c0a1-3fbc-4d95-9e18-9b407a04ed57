syntax = "proto3";

package carbon.portal.veselka;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

import "portal/db.proto";
import "portal/category_profile.proto";

message Image {
  string id = 1;
  string location = 2;
  string robot_id = 3;
  string use_case = 4;
  string role = 5;
  int64 created = 6;
  string url = 7;
  int64 height = 8;
  int64 width = 9;
  int64 ppi = 10;
  int64 valid = 11;
  int64 captured_at = 12;
  string detection_json = 13;
  string reason_json = 14;
  string priority = 15;
  string cam_id = 16;
  string row_id = 17;
  string geo_json = 18;
  string crop = 19;
  string image_type = 20;
  string city = 21;
  bool corrected_hw = 22;
  string session_name = 23;
  string crop_id = 24;
  int64 focus_metric = 25;
  string geohash = 26;
  string quarantine_reason = 27;
}

message CropConfidence {
  carbon.portal.db.DB db = 1;
  int64 robot_id = 2;
  string crop_id = 3;
  float latitude = 4;
  float longitude = 5;
  int64 precision = 6;
  int64 num_total_images = 7;
  int64 num_regional_images = 8;
  string confidence = 9;
}

message Crop {
  bool archived = 1;
  string carbon_name = 2;
  string common_name = 3;
  int64 created = 4;
  string description = 5;
  string id = 6;
  string legacy_crop_name = 7;
  string notes = 8;
  int64 updated = 9;
}

message RobotCrop {
  Crop crop = 1;
  CropConfidence confidence = 2;
}

message Model {
  string id = 1;
  int64 created = 2;
  int64 updated = 3;
  string url = 4;
  string customer = 5;
  string crop = 6;
  int64 version = 7;
  string training_docker_tag = 8;
  string gitSha = 9;
  string checksum = 10;
  string location = 11;
  int64 trained_at = 12;
  string type = 13;
  string description = 14;
  string metadata_json = 15;
  string production_container_version = 16;
  string test_results_json = 17;
  string wandb_json = 18;
  string snapshot_json = 19;
  bool is_stub = 20;
  bool is_good_to_deploy = 21;
  string robot_name = 22;
  string environment = 23;
  bool deploy = 24;
  bool is_pretraining = 25;
  string sub_type = 26;
  string dataset_id = 27;
  string container_version = 28;
  string container_id = 29;
  string pipeline_id = 30;
  string parent_model_id = 31;
  repeated string viable_crop_ids = 32;
}

message CreateCategoryCollectionSessionRequest {
  string model_id = 1;
  optional uint64 customer_id = 2;
  carbon.portal.category_profile.UnsavedExpandedCategoryCollection
      category_collection_profile = 3;
}
