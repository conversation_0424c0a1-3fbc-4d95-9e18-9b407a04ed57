syntax = "proto3";

package carbon.portal.category_profile;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

import "category_profile/category_profile.proto";

message Metadata { optional int64 updated_at = 6; }

message SavedCategoryCollection {
  carbon.category_profile.CategoryCollection profile = 1;
  Metadata metadata = 2;
}

message SavedCategory {
  carbon.category_profile.Category profile = 1;
  Metadata metadata = 2;
}

message SavedExpandedCategoryCollection {
  SavedCategoryCollection profile = 1;
  repeated SavedCategory categories = 2;
}

message UnsavedExpandedCategoryCollection {
  carbon.category_profile.CategoryCollection profile = 1;
  repeated carbon.category_profile.Category categories = 2;
}
