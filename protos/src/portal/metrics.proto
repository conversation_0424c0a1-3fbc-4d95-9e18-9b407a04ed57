syntax = "proto3";

package carbon.portal.metrics;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

import "portal/db.proto";

message DailyMetricResponse {
  carbon.portal.db.DB db = 1;
  string serial = 2;
  uint64 robot_id = 3;
  string date = 25;
  string job_id = 44;
  string job_name = 45;

  float acres_weeded = 4;
  float avg_speed_mph = 5;
  float avg_weed_size_mm = 6;
  string banding_config_name = 7;
  bool banding_enabled = 8;
  float coverage_speed_acres_hr = 9;
  float distance_weeded_meters = 10;
  int64 killed_weeds = 11;
  int64 missed_weeds = 12;
  int64 skipped_weeds = 13;
  float time_efficiency = 14;
  int64 total_weeds = 15;
  float uptime_seconds = 16;
  float weed_density_sq_ft = 17;
  float weeding_uptime_seconds = 18;
  float weeding_efficiency = 19;
  int64 weeds_type_count_broadleaf = 20;
  int64 weeds_type_count_grass = 21;
  int64 weeds_type_count_offshoot = 22;
  int64 weeds_type_count_purslane = 23;
  int64 not_weeding_weeds = 24;
  int64 kept_crops = 26;
  int64 missed_crops = 27;
  int64 not_thinning = 28;
  int64 not_weeding = 29;
  int64 skipped_crops = 30;
  int64 thinned_crops = 31;
  int64 total_crops = 32;
  float banding_percentage = 33;
  float thinning_efficiency = 34;
  string crop_id = 35;
  string crop = 36;
  float crop_density_sq_ft = 37;
  float avg_crop_size_mm = 38;
  int64 avg_targetable_req_laser_time = 39;
  int64 avg_untargetable_req_laser_time = 40;
  int64 valid_crops = 41;
  float operator_effectiveness = 42;
  int64 target_weeding_time_seconds = 43;
  float embeddings_active_uptime_seconds = 46;
}

message DailyMetricsByDateResponse {
  map<string, DailyMetricResponse> metrics = 1;
}

message DailyMetricsByDateByRobotResponse {
  map<string, DailyMetricsByDateResponse> metrics = 1;
}
