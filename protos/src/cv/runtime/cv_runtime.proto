syntax = "proto3";

package cv.runtime.proto;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/cv";

import "lib/common/camera/camera.proto";
import "cv/cv.proto";
import "weed_tracking/weed_tracking.proto";

message TargetSafetyZone {
  float up = 1;
  float down = 2;
  float left = 3;
  float right = 4;
}

message P2PContext {
  string predict_cam_id = 1;
  int64 predict_timestamp_ms = 2;
  float predict_coord_x = 3;
  float predict_coord_y = 4;
}

message SetP2PContextRequest {
  string target_cam_id = 1;
  P2PContext primary_context = 2;
  optional P2PContext secondary_context = 3;
  TargetSafetyZone safety_zone = 4;
  int64 target_id = 5;
}

message SetP2PContextResponse {}

enum BufferUseCase {
  P2P = 0;
  OpticalFlow = 1; // Deprecated
  Predict = 2;
  Drive = 3;
}

message GetCameraDimensionsRequest { string cam_id = 1; }

message GetCameraDimensionsResponse {
  int64 width = 1;
  int64 height = 2;
  bool transpose = 3;
}

message StartP2PDataCaptureRequest {
  string target_cam_id = 1;
  float capture_miss_rate = 2;
  float capture_success_rate = 3;
  bool capture_enabled = 4;
  string capture_path = 5;
  int64 after_timestamp_ms = 6;
}

message PointDetectionCategory {
  float threshold = 1;
  string category = 2;
}

message SegmentationDetectionCategory {
  float threshold = 1;
  string category = 2;
  float safety_radius_in = 3;
}

message DeepweedDetectionCriteriaSetting {
  // v1
  repeated PointDetectionCategory point_categories = 1;

  // v2
  float weed_point_threshold = 2;
  float crop_point_threshold = 3;

  repeated SegmentationDetectionCategory segmentation_categories = 4;
}

message SetDeepweedDetectionCriteriaRequest {
  float weed_point_threshold = 1;
  float crop_point_threshold = 2;
  repeated PointDetectionCategory point_categories = 3;
  repeated SegmentationDetectionCategory segmentation_categories = 4;
}

message SetDeepweedDetectionCriteriaResponse {}

message GetDeepweedDetectionCriteriaRequest {}

message GetDeepweedDetectionCriteriaResponse {
  float weed_point_threshold = 1;
  float crop_point_threshold = 2;
  repeated PointDetectionCategory point_categories = 3;
  repeated SegmentationDetectionCategory segmentation_categories = 4;
}

message GetDeepweedSupportedCategoriesRequest { optional string cam_id = 1; }

message GetDeepweedSupportedCategoriesResponse {
  repeated string segmentation_categories = 1;
  repeated string point_categories = 2;
}

message GetDeepweedIndexToCategoryRequest { string cam_id = 1; }
message GetDeepweedIndexToCategoryResponse {
  map<int32, string> weed_point_index_to_category = 1;
  map<int32, string> crop_point_index_to_category = 2;
  map<int32, string> intersection_index_to_category = 3;
}

message GetPredictCamMatrixRequest { string predict_cam_id = 1; }

message GetPredictCamDistortionCoefficientsRequest {
  string predict_cam_id = 1;
}

message SetCameraSettingsRequest {
  repeated string cam_ids = 1;

  optional float exposure_us = 2;
  optional float gamma = 3;
  optional float gain_db = 4;
  optional lib.common.camera.LightSourcePreset light_source_preset = 5;
  optional float wb_ratio_red = 6;
  optional float wb_ratio_green = 7;
  optional float wb_ratio_blue = 8;

  optional int64 roi_offset_x = 9;
  optional int64 roi_offset_y = 10;
  optional bool mirror = 11;
  optional bool flip = 12;
  optional bool strobing = 13;
  optional bool ptp = 14;

  optional bool auto_whitebalance = 15;
}

message SetCameraSettingsResponse { repeated string cam_ids = 1; }

message SetAutoWhitebalanceRequest {
  bool enable = 1;
  repeated string cam_ids = 2;
}

message SetAutoWhitebalanceResponse {}

message GetCameraSettingsRequest { repeated string cam_ids = 1; }

message CameraSettingsResponse {
  string cam_id = 1;

  optional float exposure_us = 2;
  optional float gamma = 3;
  optional float gain_db = 4;
  optional lib.common.camera.LightSourcePreset light_source_preset = 5;
  optional float wb_ratio_red = 6;
  optional float wb_ratio_green = 7;
  optional float wb_ratio_blue = 8;

  optional int64 roi_width = 9;
  optional int64 roi_height = 10;
  optional int64 roi_offset_x = 11;
  optional int64 roi_offset_y = 12;
  optional int64 gpu_id = 13;
  bool mirror = 14;
  bool flip = 15;
  bool strobing = 16;
  bool ptp = 17;
  optional bool auto_whitebalance = 18;
}

message GetCameraSettingsResponse {
  repeated CameraSettingsResponse camera_settings_response = 1;
}
message StartBurstRecordFramesRequest {
  string cam_id = 1;
  int64 duration_ms = 2;
  string path = 3;
  bool dont_capture_predict_image = 4;
  int32 downsample_factor = 5;
}
message StartBurstRecordFramesResponse {}

message StopBurstRecordFramesRequest {
  string cam_id = 1;
  optional int64 last_frame_timestamp_ms = 2;
}
message StopBurstRecordFramesResponse {}

message P2POutputProto {
  bool matched = 1;
  float target_coord_x = 2;
  float target_coord_y = 3;
  int64 target_timestamp_ms = 4;
  int64 predict_timestamp_ms = 5;
  bool safe = 6;
  float predict_coord_x = 7;
  float predict_coord_y = 8;
  string predict_cam = 9;
}

message GetNextP2POutputRequest {
  string cam_id = 1;
  int64 timestamp_ms = 2;
  int64 timeout_ms = 3;
}

message GetConnectorsRequest {
  repeated string cam_ids = 1;
  repeated string connector_ids = 2;
}

message ConnectorResponse {
  string cam_id = 1;
  string connector_id = 2;
  bool is_enabled = 3;
  int64 reduction_ratio = 4;
}

message GetConnectorsResponse {
  repeated ConnectorResponse connector_response = 1;
}

message SetConnectorsRequest {
  repeated string cam_ids = 1;
  repeated string connector_ids = 2;
  optional bool is_enabled = 3;
  optional int64 reduction_ratio = 4;
}

message SetConnectorsResponse {}

message NodeTiming {
  string name = 1;

  float fps_mean = 2;
  float fps_99pct = 3;

  float latency_ms_mean = 4;
  float latency_ms_99pct = 5;

  string state = 6;
  map<string, float> state_timings = 7;
}

message GetTimingRequest {}

message GetTimingResponse { repeated NodeTiming node_timing = 1; }

message PredictRequest {
  string cam_id = 1;
  repeated string file_paths = 2;
  repeated int64 timestamps_ms = 3;
}

message PredictResponse {}

message LoadAndQueueRequest {
  string cam_id = 1;
  repeated string file_paths = 2;
  repeated int64 timestamps_ms = 3;
}

message LoadAndQueueResponse {}

message SetImageRequest {
  string cam_id = 1;
  string file_path = 2;
}

message SetImageResponse {}

message UnsetImageRequest { string cam_id = 1; }

message UnsetImageResponse {}

message GetModelPathsRequest {}

message GetModelPathsResponse {
  optional string p2p = 1;
  optional string deepweed = 2;
  optional string furrows = 3;
}

message GetCameraTemperaturesRequest {}

message CameraTemperature {
  string cam_id = 1;
  double temperature = 2;
}

message GetCameraTemperaturesResponse {
  repeated CameraTemperature temperature = 1;
}

message GeoLLA {
  optional double lat = 1;
  optional double lng = 2;
  optional double alt = 3;
  optional int64 timestamp_ms = 4;
}

message GeoECEF {
  optional double x = 1;
  optional double y = 2;
  optional double z = 3;
  optional int64 timestamp_ms = 4;
}

message SetGPSLocationRequest {
  GeoLLA lla = 1;
  GeoECEF ecef = 2;
}

message SetGPSLocationResponse {}

message SetImplementStatusRequest {
  bool lifted = 1;
  bool estopped = 2;
}

message SetImplementStatusResponse {}

enum HitClass {
  WEED = 0;
  CROP = 1;
  PLANT = 2;
}

message SetImageScoreRequest {
  double score = 1;
  int64 timestamp_ms = 2;
  string cam_id = 3;
  DeepweedOutput deepweed_output = 4;
}

message SetImageScoreResponse {}

message GetScoreQueueRequest { string score_type = 1; }

message ScoreObject {
  double score = 1;
  int64 timestamp_ms = 2;
  string cam_id = 3;
}

message GetScoreQueueResponse { repeated ScoreObject score_object = 1; }

message GetMaxImageScoreRequest { string score_type = 1; }

message GetMaxImageScoreResponse {
  double score = 1;
  string type = 2;
}

message GetMaxScoredImageRequest { string score_type = 1; }

message GetLatestP2PImageRequest {
  string score_type = 1 [ deprecated = true ];
  carbon.aimbot.cv.P2PCaptureReason reason = 2;
}

message GetLatestImageRequest { string cam_id = 1; }

message GetImageNearTimestampRequest {
  string cam_id = 1;
  int64 timestamp_ms = 2;
}

message GetChipImageRequest { string score_type = 1; }

enum ScoreQueueType {
  PREDICT = 0;
  CHIP = 1;
}

message FlushQueuesRequest {
  repeated string score_type = 1;
  optional ScoreQueueType score_queue_type = 2;
}

message FlushQueuesResponse {}

message ImageAndMetadataResponse {
  bytes bytes = 1;
  int32 width = 2;
  int32 height = 3;
  int64 timestamp_ms = 4;
  double score = 5;
  string cam_id = 6;
  string iso_formatted_time = 7;
  double lla_lat = 8;
  double lla_lng = 9;
  double lla_alt = 10;
  int64 lla_timestamp_ms = 11;
  double ecef_x = 12;
  double ecef_y = 13;
  double ecef_z = 14;
  int64 ecef_timestamp_ms = 15;
  float ppi = 16;
  optional string score_type = 17;
  string image_type = 18;
  string model_url = 19;
  string crop = 20;
  repeated double weed_height_columns = 21;
  repeated double crop_height_columns = 22;
  double bbh_offset_mm = 23;
  double focus_metric = 24;
  double exposure_us = 25;
  double crop_point_threshold = 26;
  double weed_point_threshold = 27;
  bool weeding_enabled = 28;
  bool thinning_enabled = 29;
  string deepweed_id = 30;
  string p2p_id = 31;
  repeated weed_tracking.Detection deepweed_detections = 32;
  double segmentation_threshold = 33;
  bool simulator_generated = 34;
  double gain_db = 35;
}

message ChipPrediction {
  double x = 1;
  double y = 2;
  double radius = 3;
  string model_id = 4;
  repeated weed_tracking.CategoryPrediction deepweed_category_scores = 5;
  repeated weed_tracking.CategoryPrediction embedding_similarity_scores = 8;
  reserved 6; // repeated weed_tracking.CategoryPrediction embedding_distance
  reserved 7; // string band_status
}

message ChipImageAndMetadataResponse {
  ImageAndMetadataResponse image_and_metadata = 1;
  ChipPrediction prediction_metadata = 2;
}

message ChipQueueInformationRequest {}

message ChipQueueInformationResponse {
  repeated weed_tracking.CategoryPrediction queue_score = 1;
}

message P2PImageAndMetadataResponse {
  bytes target_bytes = 1;
  int32 target_width = 2;
  int32 target_height = 3;
  bytes perspective_bytes = 4;
  int32 perspective_width = 5;
  int32 perspective_height = 6;
  bytes annotated_target_bytes = 7;
  int32 annotated_target_width = 8;
  int32 annotated_target_height = 9;
  int64 timestamp_ms = 10;
  double score = 11;
  string cam_id = 12;
  string iso_formatted_time = 13;
  double lla_lat = 14;
  double lla_lng = 15;
  double lla_alt = 16;
  int64 lla_timestamp_ms = 17;
  double ecef_x = 18;
  double ecef_y = 19;
  double ecef_z = 20;
  int64 ecef_timestamp_ms = 21;
  float ppi = 22;
  float perspective_ppi = 23;
  string image_type = 25;
  string model_url = 26;
  string crop = 27;
  double focus_metric = 28;
  double exposure_us = 29;
  bool weeding_enabled = 30;
  bool thinning_enabled = 31;
  string deepweed_id = 32;
  string p2p_id = 33;
}

message GetCameraInfoRequest {}

enum ErrorType {
  NONE = 0;
  GRAB = 1;
  CONNECTION = 2;
  NO_IMPLEMENTATION = 3;
  NO_IMAGE_IN_LAST_MINUTE = 4;
}

message CameraInfo {
  string cam_id = 1;
  optional string ip_address = 2;
  optional string serial_number = 3;
  string model = 4;
  uint32 width = 5;
  uint32 height = 6;
  bool connected = 7;
  uint64 link_speed = 8;
  ErrorType error_type = 9;
  optional string v4l2_device_id = 10;
  string firmware_version = 11;
  optional string latest_firmware_version = 12;
}

message GetCameraInfoResponse { repeated CameraInfo camera_info = 1; }

message GetLightweightBurstRecordRequest {}

message GetLightweightBurstRecordResponse {
  bytes zip_file = 1;
  bytes metadata_file = 2;
}

message GetBootedRequest {}
message GetBootedResponse { bool booted = 1; }

message GetReadyRequest {}
message GetReadyResponse {
  bool ready = 1;
  map<string, bool> deepweed_ready_state = 2; // camera_id -> ready
  map<string, bool> p2p_ready_state = 3;      // camera_id -> ready
  bool booted = 4;
}

message GetErrorStateResponse {
  bool plant_profile_error = 1;
  bool model_unsupported_embeddings = 2;
}

message DeepweedDetection {
  float x = 1;
  float y = 2;
  float size = 3;
  float score = 4;
  HitClass hit_class = 6;
  repeated uint32 mask_intersections = 7;
  float weed_score = 8;
  float crop_score = 9;
  repeated float weed_detection_class_scores = 10;
  repeated float embedding = 11;
  float plant_score = 12;
  repeated float embedding_category_distances = 13;
  repeated weed_tracking.CategoryPrediction embedding_similarity_scores = 15;
  uint32 detection_id = 14;
}

message DeepweedOutput {
  repeated DeepweedDetection detections = 1;
  uint32 mask_width = 2;
  uint32 mask_height = 3;
  uint32 mask_channels = 4;
  bytes mask = 5;
  repeated string mask_channel_classes = 6;
  bool predict_in_distance_buffer = 7;
  int64 timestamp_ms = 8;
  repeated string weed_detection_classes = 9;
  repeated string embedding_categories = 10;
  bool available_for_snapshotting = 11;
}

message GetDeepweedOutputByTimestampRequest {
  string cam_id = 1;
  int64 timestamp_ms = 2;
}

message GetRecommendedStrobeSettingsRequest {}

message GetRecommendedStrobeSettingsResponse {
  float target_camera_fps = 1;
  int32 targets_per_predict_ratio = 2;
}

message StartP2PBufferringRequest {
  option deprecated = true;
  string cam_id = 1;
}
message StartP2PBufferringResponse { option deprecated = true; }
message StopP2PBufferringRequest {
  option deprecated = true;
  bool save_burst = 1;
  string cam_id = 2;
  string path = 3;
  bool dont_capture_predict_image = 4;
  int64 start_timestamp_ms = 5;
  int64 end_timestamp_ms = 6;
}
message StopP2PBufferringResponse { option deprecated = true; }
message P2PCaptureRequest {
  string cam_id = 1;
  string name = 2;
  int64 timestamp_ms = 3;
  bool write_to_disk = 4;
  carbon.aimbot.cv.P2PCaptureReason reason = 5;
}
message P2PCaptureResponse {}

message P2PBufferingBurstPredictMetadata { float plant_size_px = 1; }
message P2PBufferringBurstCaptureRequest {
  string cam_id = 2;
  string path = 3;
  bool dont_capture_predict_image = 4;
  int64 start_timestamp_ms = 5;
  int64 end_timestamp_ms = 6;
  optional string predict_path = 7;
  bool predict_path_exists = 8;
  bool save_predict_metadata = 9;
  P2PBufferingBurstPredictMetadata predict_metadata = 10;
}
message P2PBufferringBurstCaptureResponse {}
message GetNextDeepweedOutputRequest {
  int64 timestamp_ms = 1;
  int64 timeout_ms = 2;
  string cam_id = 3;
}

message SetTargetingStateRequest {
  bool weeding_enabled = 1;
  bool thinning_enabled = 2;
}
message SetTargetingStateResponse {}
message GetNextFocusMetricRequest {
  string cam_id = 1;
  int64 timestamp_ms = 2;
}
message GetNextFocusMetricResponse {
  float focus_metric = 1;
  int64 timestamp_ms = 2;
}
message RemoveDataDirRequest { string path = 1; }
message RemoveDataDirResponse {}

message LastNImageRequest {
  string cam_id = 1;
  uint64 num_images = 2;
}

message ComputeCapabilitiesResponse { repeated string capabilities = 1; }

message SupportedTensorRTVersionsResponse { repeated string versions = 1; }

message Empty {}

message ScoreQueueAndCount {
  string score_queue = 1;
  uint64 num_items = 2;
}

message ListScoreQueuesResponse { repeated ScoreQueueAndCount score_queue = 1; }

message GetCategoryCollectionResponse {
  string category_collection_id = 1;
  int64 last_updated_timestamp_ms = 2;
}

message SnapshotPredictImagesRequest {}

message PcamSnapshot {
  string pcam_id = 1;
  int64 timestamp_ms = 2;
}

message SnapshotPredictImagesResponse { repeated PcamSnapshot snapshots = 1; }

message GetChipForPredictImageRequest {
  string pcam_id = 1;
  int64 timestamp_ms = 2;
  int32 center_x_px = 3;
  int32 center_y_px = 4;
}

message GetChipForPredictImageResponse {
  ImageAndMetadataResponse image_and_metadata = 1;
}

service CVRuntimeService {
  rpc SetP2PContext(SetP2PContextRequest) returns (SetP2PContextResponse) {}
  rpc GetCameraDimensions(GetCameraDimensionsRequest)
      returns (GetCameraDimensionsResponse) {}
  rpc GetCameraInfo(GetCameraInfoRequest) returns (GetCameraInfoResponse) {}
  rpc GetDeepweedIndexToCategory(GetDeepweedIndexToCategoryRequest)
      returns (GetDeepweedIndexToCategoryResponse) {}
  rpc SetDeepweedDetectionCriteria(SetDeepweedDetectionCriteriaRequest)
      returns (SetDeepweedDetectionCriteriaResponse) {}
  rpc GetDeepweedDetectionCriteria(GetDeepweedDetectionCriteriaRequest)
      returns (GetDeepweedDetectionCriteriaResponse) {}
  rpc GetDeepweedSupportedCategories(GetDeepweedSupportedCategoriesRequest)
      returns (GetDeepweedSupportedCategoriesResponse) {}
  rpc GetCameraTemperatures(GetCameraTemperaturesRequest)
      returns (GetCameraTemperaturesResponse) {}

  rpc SetCameraSettings(SetCameraSettingsRequest)
      returns (SetCameraSettingsResponse) {}
  rpc GetCameraSettings(GetCameraSettingsRequest)
      returns (GetCameraSettingsResponse) {}
  rpc StartBurstRecordFrames(StartBurstRecordFramesRequest)
      returns (StartBurstRecordFramesResponse) {}
  rpc StopBurstRecordFrames(StopBurstRecordFramesRequest)
      returns (StopBurstRecordFramesResponse) {}
  rpc GetConnectors(GetConnectorsRequest) returns (GetConnectorsResponse) {}
  rpc SetConnectors(SetConnectorsRequest) returns (SetConnectorsResponse) {}
  rpc GetTiming(GetTimingRequest) returns (GetTimingResponse) {}
  rpc Predict(PredictRequest) returns (PredictResponse) {}
  rpc LoadAndQueue(LoadAndQueueRequest) returns (LoadAndQueueResponse) {}
  rpc SetImage(SetImageRequest) returns (SetImageResponse) {}
  rpc UnsetImage(UnsetImageRequest) returns (UnsetImageResponse) {}
  rpc GetModelPaths(GetModelPathsRequest) returns (GetModelPathsResponse) {}
  rpc SetGPSLocation(SetGPSLocationRequest) returns (SetGPSLocationResponse) {}
  rpc SetImplementStatus(SetImplementStatusRequest)
      returns (SetImplementStatusResponse) {}
  rpc SetImageScore(SetImageScoreRequest) returns (SetImageScoreResponse) {}
  rpc GetScoreQueue(GetScoreQueueRequest) returns (GetScoreQueueResponse) {}
  rpc ListScoreQueues(Empty) returns (ListScoreQueuesResponse) {}
  rpc GetMaxImageScore(GetMaxImageScoreRequest)
      returns (GetMaxImageScoreResponse) {}
  rpc GetMaxScoredImage(GetMaxScoredImageRequest)
      returns (ImageAndMetadataResponse) {}
  rpc GetLatestP2PImage(GetLatestP2PImageRequest)
      returns (P2PImageAndMetadataResponse) {}
  rpc GetChipImage(GetChipImageRequest) returns (ChipImageAndMetadataResponse) {
  }
  rpc GetChipQueueInformation(ChipQueueInformationRequest)
      returns (ChipQueueInformationResponse) {}
  rpc FlushQueues(FlushQueuesRequest) returns (FlushQueuesResponse) {}
  rpc GetLatestImage(GetLatestImageRequest) returns (ImageAndMetadataResponse) {
  }
  rpc GetImageNearTimestamp(GetImageNearTimestampRequest)
      returns (ImageAndMetadataResponse) {}
  rpc GetLightweightBurstRecord(GetLightweightBurstRecordRequest)
      returns (GetLightweightBurstRecordResponse) {}
  rpc GetBooted(GetBootedRequest) returns (GetBootedResponse) {}
  rpc GetReady(GetReadyRequest) returns (GetReadyResponse) {}
  rpc GetDeepweedOutputByTimestamp(GetDeepweedOutputByTimestampRequest)
      returns (DeepweedOutput) {}
  rpc GetRecommendedStrobeSettings(GetRecommendedStrobeSettingsRequest)
      returns (GetRecommendedStrobeSettingsResponse) {}
  rpc P2PCapture(P2PCaptureRequest) returns (P2PCaptureResponse) {}
  rpc SetAutoWhitebalance(SetAutoWhitebalanceRequest)
      returns (SetAutoWhitebalanceResponse) {}
  rpc GetNextDeepweedOutput(GetNextDeepweedOutputRequest)
      returns (DeepweedOutput) {}
  rpc GetNextP2POutput(GetNextP2POutputRequest) returns (P2POutputProto) {}
  rpc SetTargetingState(SetTargetingStateRequest)
      returns (SetTargetingStateResponse) {}
  rpc P2PBufferringBurstCapture(P2PBufferringBurstCaptureRequest)
      returns (P2PBufferringBurstCaptureResponse) {}
  rpc GetNextFocusMetric(GetNextFocusMetricRequest)
      returns (GetNextFocusMetricResponse) {}
  rpc RemoveDataDir(RemoveDataDirRequest) returns (RemoveDataDirResponse) {}
  rpc GetLastNImages(LastNImageRequest)
      returns (stream ImageAndMetadataResponse) {}
  rpc GetComputeCapabilities(Empty) returns (ComputeCapabilitiesResponse) {}
  rpc GetSupportedTensorRTVersions(Empty)
      returns (SupportedTensorRTVersionsResponse) {}
  rpc ReloadCategoryCollection(Empty) returns (Empty) {}
  rpc GetCategoryCollection(Empty) returns (GetCategoryCollectionResponse) {}
  rpc GetErrorState(Empty) returns (GetErrorStateResponse) {}
  rpc SnapshotPredictImages(SnapshotPredictImagesRequest)
      returns (SnapshotPredictImagesResponse) {}
  rpc GetChipForPredictImage(GetChipForPredictImageRequest)
      returns (GetChipForPredictImageResponse) {}
}
