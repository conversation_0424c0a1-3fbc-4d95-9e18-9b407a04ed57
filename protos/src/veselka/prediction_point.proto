syntax = "proto3";

package carbon.veselka.prediction_point;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/veselka/prediction_point";

import "veselka/pagination.proto";

message Image {
  string id = 1;
  string url = 2; // s3 url
  float ppcm = 3; // Pixels per centimeter of the image
  int64 captured_at = 4;
  bool uploaded_by_operator = 5;
}

message PredictionPoint {
  string id = 1;
  float x = 2;
  float y = 3;
  float radius = 4;
  Image image = 5;
  string category_id = 6;
}

message ListPredictionPointsResponse {
  repeated PredictionPoint data = 1;
  carbon.veselka.pagination.Pagination pagination = 2;
}

message CategoryProfile {
  string id = 1;
  repeated string prediction_point_ids = 2;
}

message CategoryCollectionProfile {
  string id = 1;
  repeated CategoryProfile category_profiles = 2;
}

message CreatePredictionPointSessionRequest {
  string model_id = 1;
  repeated string robot_ids = 2;
  CategoryCollectionProfile category_collection_profile = 3;
}

enum SessionActivityState {
  SESSION_ACTIVITY_STATE_UNSPECIFIED = 0;
  ACTIVE = 1;
  INACTIVE = 2;
}

message CreatePredictionPointSessionResponse {
  string session_id = 1;
  SessionActivityState status = 2;
}

message SessionInfo {
  string model_id = 1;
  repeated string robot_ids = 2;
  CategoryCollectionProfile category_collection_profile = 3;
  string session_id = 4;
}

message SessionStatus {
  int64 count_of_results = 1;
  int64 expected_count = 2;
  SessionActivityState session_status = 3;
}

message GetPredictionPointSessionResponse {
  SessionInfo session_info = 1;
  SessionStatus session_status = 2;
}
