// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: robot_syncer/audit.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { ConfigValue } from "../config/api/config_service";
import { Timestamp } from "../google/protobuf/timestamp";

export const protobufPackage = "carbon.robot_syncer.audit";

export interface ConfigAuditLog {
  /**
   * Time that this operation was recorded in RoSy, i.e. synced up to the
   * cloud. If this operation happened on the robot, it may have occurred at an
   * earlier date.
   */
  createTime:
    | string
    | undefined;
  /**
   * Auth0 user ID of the user under whose authority this change was made, or
   * simply the robot hostname (e.g., "reaper114") if this was made directly on
   * a robot with no end-user credentials available.
   */
  userId: string;
  /**
   * The method that was called in this operation, e.g. "UpdateConfigNode" or
   * "SetTree". These do not necessarily correspond 1:1 with gRPC method calls
   * or REST endpoints.
   */
  method: string;
  /** Serial of the robot whose config was updated. */
  serial: string;
  /**
   * Full config key that was updated, under the root of the updated robot:
   * e.g., "common/software_manager/target_version".
   */
  key: string;
  /**
   * Value of this config node before the change, if applicable for this
   * operation. This is set when the operation changes a scalar, like
   * `SetValue`. It's not set when the node is a list or a tree.
   */
  oldValue:
    | ConfigValue
    | undefined;
  /**
   * Value of this config node after the change, if applicable for this
   * operation. This is set when the operation changes a scalar, like
   * `SetValue`. It's not set when the node is a list or a tree.
   */
  newValue: ConfigValue | undefined;
}

export interface GetConfigAuditLogsResponse {
  auditLogs: ConfigAuditLog[];
}

function createBaseConfigAuditLog(): ConfigAuditLog {
  return {
    createTime: undefined,
    userId: "",
    method: "",
    serial: "",
    key: "",
    oldValue: undefined,
    newValue: undefined,
  };
}

export const ConfigAuditLog: MessageFns<ConfigAuditLog> = {
  encode(message: ConfigAuditLog, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(10).fork()).join();
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.method !== "") {
      writer.uint32(26).string(message.method);
    }
    if (message.serial !== "") {
      writer.uint32(34).string(message.serial);
    }
    if (message.key !== "") {
      writer.uint32(42).string(message.key);
    }
    if (message.oldValue !== undefined) {
      ConfigValue.encode(message.oldValue, writer.uint32(50).fork()).join();
    }
    if (message.newValue !== undefined) {
      ConfigValue.encode(message.newValue, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfigAuditLog {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigAuditLog();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.method = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.key = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.oldValue = ConfigValue.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.newValue = ConfigValue.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigAuditLog {
    return {
      createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : undefined,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      method: isSet(object.method) ? globalThis.String(object.method) : "",
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      oldValue: isSet(object.oldValue) ? ConfigValue.fromJSON(object.oldValue) : undefined,
      newValue: isSet(object.newValue) ? ConfigValue.fromJSON(object.newValue) : undefined,
    };
  },

  toJSON(message: ConfigAuditLog): unknown {
    const obj: any = {};
    if (message.createTime !== undefined) {
      obj.createTime = message.createTime;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.method !== "") {
      obj.method = message.method;
    }
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.oldValue !== undefined) {
      obj.oldValue = ConfigValue.toJSON(message.oldValue);
    }
    if (message.newValue !== undefined) {
      obj.newValue = ConfigValue.toJSON(message.newValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigAuditLog>, I>>(base?: I): ConfigAuditLog {
    return ConfigAuditLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigAuditLog>, I>>(object: I): ConfigAuditLog {
    const message = createBaseConfigAuditLog();
    message.createTime = object.createTime ?? undefined;
    message.userId = object.userId ?? "";
    message.method = object.method ?? "";
    message.serial = object.serial ?? "";
    message.key = object.key ?? "";
    message.oldValue = (object.oldValue !== undefined && object.oldValue !== null)
      ? ConfigValue.fromPartial(object.oldValue)
      : undefined;
    message.newValue = (object.newValue !== undefined && object.newValue !== null)
      ? ConfigValue.fromPartial(object.newValue)
      : undefined;
    return message;
  },
};

function createBaseGetConfigAuditLogsResponse(): GetConfigAuditLogsResponse {
  return { auditLogs: [] };
}

export const GetConfigAuditLogsResponse: MessageFns<GetConfigAuditLogsResponse> = {
  encode(message: GetConfigAuditLogsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.auditLogs) {
      ConfigAuditLog.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetConfigAuditLogsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetConfigAuditLogsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.auditLogs.push(ConfigAuditLog.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetConfigAuditLogsResponse {
    return {
      auditLogs: globalThis.Array.isArray(object?.auditLogs)
        ? object.auditLogs.map((e: any) => ConfigAuditLog.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetConfigAuditLogsResponse): unknown {
    const obj: any = {};
    if (message.auditLogs?.length) {
      obj.auditLogs = message.auditLogs.map((e) => ConfigAuditLog.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetConfigAuditLogsResponse>, I>>(base?: I): GetConfigAuditLogsResponse {
    return GetConfigAuditLogsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigAuditLogsResponse>, I>>(object: I): GetConfigAuditLogsResponse {
    const message = createBaseGetConfigAuditLogsResponse();
    message.auditLogs = object.auditLogs?.map((e) => ConfigAuditLog.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(dateStr: string): Timestamp {
  const date = new globalThis.Date(dateStr);
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): string {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis).toISOString();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
