// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: veselka/prediction_point.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Pagination } from "./pagination";

export const protobufPackage = "carbon.veselka.prediction_point";

export enum SessionActivityState {
  SESSION_ACTIVITY_STATE_UNSPECIFIED = 0,
  ACTIVE = 1,
  INACTIVE = 2,
  UNRECOGNIZED = -1,
}

export function sessionActivityStateFromJSON(object: any): SessionActivityState {
  switch (object) {
    case 0:
    case "SESSION_ACTIVITY_STATE_UNSPECIFIED":
      return SessionActivityState.SESSION_ACTIVITY_STATE_UNSPECIFIED;
    case 1:
    case "ACTIVE":
      return SessionActivityState.ACTIVE;
    case 2:
    case "INACTIVE":
      return SessionActivityState.INACTIVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SessionActivityState.UNRECOGNIZED;
  }
}

export function sessionActivityStateToJSON(object: SessionActivityState): string {
  switch (object) {
    case SessionActivityState.SESSION_ACTIVITY_STATE_UNSPECIFIED:
      return "SESSION_ACTIVITY_STATE_UNSPECIFIED";
    case SessionActivityState.ACTIVE:
      return "ACTIVE";
    case SessionActivityState.INACTIVE:
      return "INACTIVE";
    case SessionActivityState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface Image {
  id: string;
  /** s3 url */
  url: string;
  /** Pixels per centimeter of the image */
  ppcm: number;
  capturedAt: number;
  uploadedByOperator: boolean;
}

export interface PredictionPoint {
  id: string;
  x: number;
  y: number;
  radius: number;
  image: Image | undefined;
  categoryId: string;
}

export interface ListPredictionPointsResponse {
  data: PredictionPoint[];
  pagination: Pagination | undefined;
}

export interface CategoryProfile {
  id: string;
  predictionPointIds: string[];
}

export interface CategoryCollectionProfile {
  id: string;
  categoryProfiles: CategoryProfile[];
}

export interface CreatePredictionPointSessionRequest {
  modelId: string;
  robotIds: string[];
  categoryCollectionProfile: CategoryCollectionProfile | undefined;
}

export interface CreatePredictionPointSessionResponse {
  sessionId: string;
  status: SessionActivityState;
}

export interface SessionInfo {
  modelId: string;
  robotIds: string[];
  categoryCollectionProfile: CategoryCollectionProfile | undefined;
  sessionId: string;
}

export interface SessionStatus {
  countOfResults: number;
  expectedCount: number;
  sessionStatus: SessionActivityState;
}

export interface GetPredictionPointSessionResponse {
  sessionInfo: SessionInfo | undefined;
  sessionStatus: SessionStatus | undefined;
}

function createBaseImage(): Image {
  return { id: "", url: "", ppcm: 0, capturedAt: 0, uploadedByOperator: false };
}

export const Image: MessageFns<Image> = {
  encode(message: Image, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.url !== "") {
      writer.uint32(18).string(message.url);
    }
    if (message.ppcm !== 0) {
      writer.uint32(29).float(message.ppcm);
    }
    if (message.capturedAt !== 0) {
      writer.uint32(32).int64(message.capturedAt);
    }
    if (message.uploadedByOperator !== false) {
      writer.uint32(40).bool(message.uploadedByOperator);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Image {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseImage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.url = reader.string();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.ppcm = reader.float();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.capturedAt = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.uploadedByOperator = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Image {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      url: isSet(object.url) ? globalThis.String(object.url) : "",
      ppcm: isSet(object.ppcm) ? globalThis.Number(object.ppcm) : 0,
      capturedAt: isSet(object.capturedAt) ? globalThis.Number(object.capturedAt) : 0,
      uploadedByOperator: isSet(object.uploadedByOperator) ? globalThis.Boolean(object.uploadedByOperator) : false,
    };
  },

  toJSON(message: Image): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.url !== "") {
      obj.url = message.url;
    }
    if (message.ppcm !== 0) {
      obj.ppcm = message.ppcm;
    }
    if (message.capturedAt !== 0) {
      obj.capturedAt = Math.round(message.capturedAt);
    }
    if (message.uploadedByOperator !== false) {
      obj.uploadedByOperator = message.uploadedByOperator;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Image>, I>>(base?: I): Image {
    return Image.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Image>, I>>(object: I): Image {
    const message = createBaseImage();
    message.id = object.id ?? "";
    message.url = object.url ?? "";
    message.ppcm = object.ppcm ?? 0;
    message.capturedAt = object.capturedAt ?? 0;
    message.uploadedByOperator = object.uploadedByOperator ?? false;
    return message;
  },
};

function createBasePredictionPoint(): PredictionPoint {
  return { id: "", x: 0, y: 0, radius: 0, image: undefined, categoryId: "" };
}

export const PredictionPoint: MessageFns<PredictionPoint> = {
  encode(message: PredictionPoint, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.x !== 0) {
      writer.uint32(21).float(message.x);
    }
    if (message.y !== 0) {
      writer.uint32(29).float(message.y);
    }
    if (message.radius !== 0) {
      writer.uint32(37).float(message.radius);
    }
    if (message.image !== undefined) {
      Image.encode(message.image, writer.uint32(42).fork()).join();
    }
    if (message.categoryId !== "") {
      writer.uint32(50).string(message.categoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PredictionPoint {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePredictionPoint();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.x = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.y = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.radius = reader.float();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.image = Image.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.categoryId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PredictionPoint {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      x: isSet(object.x) ? globalThis.Number(object.x) : 0,
      y: isSet(object.y) ? globalThis.Number(object.y) : 0,
      radius: isSet(object.radius) ? globalThis.Number(object.radius) : 0,
      image: isSet(object.image) ? Image.fromJSON(object.image) : undefined,
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
    };
  },

  toJSON(message: PredictionPoint): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.x !== 0) {
      obj.x = message.x;
    }
    if (message.y !== 0) {
      obj.y = message.y;
    }
    if (message.radius !== 0) {
      obj.radius = message.radius;
    }
    if (message.image !== undefined) {
      obj.image = Image.toJSON(message.image);
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PredictionPoint>, I>>(base?: I): PredictionPoint {
    return PredictionPoint.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PredictionPoint>, I>>(object: I): PredictionPoint {
    const message = createBasePredictionPoint();
    message.id = object.id ?? "";
    message.x = object.x ?? 0;
    message.y = object.y ?? 0;
    message.radius = object.radius ?? 0;
    message.image = (object.image !== undefined && object.image !== null) ? Image.fromPartial(object.image) : undefined;
    message.categoryId = object.categoryId ?? "";
    return message;
  },
};

function createBaseListPredictionPointsResponse(): ListPredictionPointsResponse {
  return { data: [], pagination: undefined };
}

export const ListPredictionPointsResponse: MessageFns<ListPredictionPointsResponse> = {
  encode(message: ListPredictionPointsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      PredictionPoint.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      Pagination.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListPredictionPointsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListPredictionPointsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data.push(PredictionPoint.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.pagination = Pagination.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListPredictionPointsResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => PredictionPoint.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: ListPredictionPointsResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => PredictionPoint.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = Pagination.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListPredictionPointsResponse>, I>>(base?: I): ListPredictionPointsResponse {
    return ListPredictionPointsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPredictionPointsResponse>, I>>(object: I): ListPredictionPointsResponse {
    const message = createBaseListPredictionPointsResponse();
    message.data = object.data?.map((e) => PredictionPoint.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? Pagination.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseCategoryProfile(): CategoryProfile {
  return { id: "", predictionPointIds: [] };
}

export const CategoryProfile: MessageFns<CategoryProfile> = {
  encode(message: CategoryProfile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    for (const v of message.predictionPointIds) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CategoryProfile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCategoryProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.predictionPointIds.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CategoryProfile {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      predictionPointIds: globalThis.Array.isArray(object?.predictionPointIds)
        ? object.predictionPointIds.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: CategoryProfile): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.predictionPointIds?.length) {
      obj.predictionPointIds = message.predictionPointIds;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CategoryProfile>, I>>(base?: I): CategoryProfile {
    return CategoryProfile.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CategoryProfile>, I>>(object: I): CategoryProfile {
    const message = createBaseCategoryProfile();
    message.id = object.id ?? "";
    message.predictionPointIds = object.predictionPointIds?.map((e) => e) || [];
    return message;
  },
};

function createBaseCategoryCollectionProfile(): CategoryCollectionProfile {
  return { id: "", categoryProfiles: [] };
}

export const CategoryCollectionProfile: MessageFns<CategoryCollectionProfile> = {
  encode(message: CategoryCollectionProfile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    for (const v of message.categoryProfiles) {
      CategoryProfile.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CategoryCollectionProfile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCategoryCollectionProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.categoryProfiles.push(CategoryProfile.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CategoryCollectionProfile {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      categoryProfiles: globalThis.Array.isArray(object?.categoryProfiles)
        ? object.categoryProfiles.map((e: any) => CategoryProfile.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CategoryCollectionProfile): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.categoryProfiles?.length) {
      obj.categoryProfiles = message.categoryProfiles.map((e) => CategoryProfile.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CategoryCollectionProfile>, I>>(base?: I): CategoryCollectionProfile {
    return CategoryCollectionProfile.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CategoryCollectionProfile>, I>>(object: I): CategoryCollectionProfile {
    const message = createBaseCategoryCollectionProfile();
    message.id = object.id ?? "";
    message.categoryProfiles = object.categoryProfiles?.map((e) => CategoryProfile.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreatePredictionPointSessionRequest(): CreatePredictionPointSessionRequest {
  return { modelId: "", robotIds: [], categoryCollectionProfile: undefined };
}

export const CreatePredictionPointSessionRequest: MessageFns<CreatePredictionPointSessionRequest> = {
  encode(message: CreatePredictionPointSessionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.modelId !== "") {
      writer.uint32(10).string(message.modelId);
    }
    for (const v of message.robotIds) {
      writer.uint32(18).string(v!);
    }
    if (message.categoryCollectionProfile !== undefined) {
      CategoryCollectionProfile.encode(message.categoryCollectionProfile, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreatePredictionPointSessionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreatePredictionPointSessionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.modelId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.robotIds.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.categoryCollectionProfile = CategoryCollectionProfile.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreatePredictionPointSessionRequest {
    return {
      modelId: isSet(object.modelId) ? globalThis.String(object.modelId) : "",
      robotIds: globalThis.Array.isArray(object?.robotIds) ? object.robotIds.map((e: any) => globalThis.String(e)) : [],
      categoryCollectionProfile: isSet(object.categoryCollectionProfile)
        ? CategoryCollectionProfile.fromJSON(object.categoryCollectionProfile)
        : undefined,
    };
  },

  toJSON(message: CreatePredictionPointSessionRequest): unknown {
    const obj: any = {};
    if (message.modelId !== "") {
      obj.modelId = message.modelId;
    }
    if (message.robotIds?.length) {
      obj.robotIds = message.robotIds;
    }
    if (message.categoryCollectionProfile !== undefined) {
      obj.categoryCollectionProfile = CategoryCollectionProfile.toJSON(message.categoryCollectionProfile);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreatePredictionPointSessionRequest>, I>>(
    base?: I,
  ): CreatePredictionPointSessionRequest {
    return CreatePredictionPointSessionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePredictionPointSessionRequest>, I>>(
    object: I,
  ): CreatePredictionPointSessionRequest {
    const message = createBaseCreatePredictionPointSessionRequest();
    message.modelId = object.modelId ?? "";
    message.robotIds = object.robotIds?.map((e) => e) || [];
    message.categoryCollectionProfile =
      (object.categoryCollectionProfile !== undefined && object.categoryCollectionProfile !== null)
        ? CategoryCollectionProfile.fromPartial(object.categoryCollectionProfile)
        : undefined;
    return message;
  },
};

function createBaseCreatePredictionPointSessionResponse(): CreatePredictionPointSessionResponse {
  return { sessionId: "", status: 0 };
}

export const CreatePredictionPointSessionResponse: MessageFns<CreatePredictionPointSessionResponse> = {
  encode(message: CreatePredictionPointSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sessionId !== "") {
      writer.uint32(10).string(message.sessionId);
    }
    if (message.status !== 0) {
      writer.uint32(16).int32(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreatePredictionPointSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreatePredictionPointSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.sessionId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreatePredictionPointSessionResponse {
    return {
      sessionId: isSet(object.sessionId) ? globalThis.String(object.sessionId) : "",
      status: isSet(object.status) ? sessionActivityStateFromJSON(object.status) : 0,
    };
  },

  toJSON(message: CreatePredictionPointSessionResponse): unknown {
    const obj: any = {};
    if (message.sessionId !== "") {
      obj.sessionId = message.sessionId;
    }
    if (message.status !== 0) {
      obj.status = sessionActivityStateToJSON(message.status);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreatePredictionPointSessionResponse>, I>>(
    base?: I,
  ): CreatePredictionPointSessionResponse {
    return CreatePredictionPointSessionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePredictionPointSessionResponse>, I>>(
    object: I,
  ): CreatePredictionPointSessionResponse {
    const message = createBaseCreatePredictionPointSessionResponse();
    message.sessionId = object.sessionId ?? "";
    message.status = object.status ?? 0;
    return message;
  },
};

function createBaseSessionInfo(): SessionInfo {
  return { modelId: "", robotIds: [], categoryCollectionProfile: undefined, sessionId: "" };
}

export const SessionInfo: MessageFns<SessionInfo> = {
  encode(message: SessionInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.modelId !== "") {
      writer.uint32(10).string(message.modelId);
    }
    for (const v of message.robotIds) {
      writer.uint32(18).string(v!);
    }
    if (message.categoryCollectionProfile !== undefined) {
      CategoryCollectionProfile.encode(message.categoryCollectionProfile, writer.uint32(26).fork()).join();
    }
    if (message.sessionId !== "") {
      writer.uint32(34).string(message.sessionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SessionInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSessionInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.modelId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.robotIds.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.categoryCollectionProfile = CategoryCollectionProfile.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.sessionId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SessionInfo {
    return {
      modelId: isSet(object.modelId) ? globalThis.String(object.modelId) : "",
      robotIds: globalThis.Array.isArray(object?.robotIds) ? object.robotIds.map((e: any) => globalThis.String(e)) : [],
      categoryCollectionProfile: isSet(object.categoryCollectionProfile)
        ? CategoryCollectionProfile.fromJSON(object.categoryCollectionProfile)
        : undefined,
      sessionId: isSet(object.sessionId) ? globalThis.String(object.sessionId) : "",
    };
  },

  toJSON(message: SessionInfo): unknown {
    const obj: any = {};
    if (message.modelId !== "") {
      obj.modelId = message.modelId;
    }
    if (message.robotIds?.length) {
      obj.robotIds = message.robotIds;
    }
    if (message.categoryCollectionProfile !== undefined) {
      obj.categoryCollectionProfile = CategoryCollectionProfile.toJSON(message.categoryCollectionProfile);
    }
    if (message.sessionId !== "") {
      obj.sessionId = message.sessionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SessionInfo>, I>>(base?: I): SessionInfo {
    return SessionInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SessionInfo>, I>>(object: I): SessionInfo {
    const message = createBaseSessionInfo();
    message.modelId = object.modelId ?? "";
    message.robotIds = object.robotIds?.map((e) => e) || [];
    message.categoryCollectionProfile =
      (object.categoryCollectionProfile !== undefined && object.categoryCollectionProfile !== null)
        ? CategoryCollectionProfile.fromPartial(object.categoryCollectionProfile)
        : undefined;
    message.sessionId = object.sessionId ?? "";
    return message;
  },
};

function createBaseSessionStatus(): SessionStatus {
  return { countOfResults: 0, expectedCount: 0, sessionStatus: 0 };
}

export const SessionStatus: MessageFns<SessionStatus> = {
  encode(message: SessionStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.countOfResults !== 0) {
      writer.uint32(8).int64(message.countOfResults);
    }
    if (message.expectedCount !== 0) {
      writer.uint32(16).int64(message.expectedCount);
    }
    if (message.sessionStatus !== 0) {
      writer.uint32(24).int32(message.sessionStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SessionStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSessionStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.countOfResults = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.expectedCount = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.sessionStatus = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SessionStatus {
    return {
      countOfResults: isSet(object.countOfResults) ? globalThis.Number(object.countOfResults) : 0,
      expectedCount: isSet(object.expectedCount) ? globalThis.Number(object.expectedCount) : 0,
      sessionStatus: isSet(object.sessionStatus) ? sessionActivityStateFromJSON(object.sessionStatus) : 0,
    };
  },

  toJSON(message: SessionStatus): unknown {
    const obj: any = {};
    if (message.countOfResults !== 0) {
      obj.countOfResults = Math.round(message.countOfResults);
    }
    if (message.expectedCount !== 0) {
      obj.expectedCount = Math.round(message.expectedCount);
    }
    if (message.sessionStatus !== 0) {
      obj.sessionStatus = sessionActivityStateToJSON(message.sessionStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SessionStatus>, I>>(base?: I): SessionStatus {
    return SessionStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SessionStatus>, I>>(object: I): SessionStatus {
    const message = createBaseSessionStatus();
    message.countOfResults = object.countOfResults ?? 0;
    message.expectedCount = object.expectedCount ?? 0;
    message.sessionStatus = object.sessionStatus ?? 0;
    return message;
  },
};

function createBaseGetPredictionPointSessionResponse(): GetPredictionPointSessionResponse {
  return { sessionInfo: undefined, sessionStatus: undefined };
}

export const GetPredictionPointSessionResponse: MessageFns<GetPredictionPointSessionResponse> = {
  encode(message: GetPredictionPointSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sessionInfo !== undefined) {
      SessionInfo.encode(message.sessionInfo, writer.uint32(10).fork()).join();
    }
    if (message.sessionStatus !== undefined) {
      SessionStatus.encode(message.sessionStatus, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetPredictionPointSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPredictionPointSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.sessionInfo = SessionInfo.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.sessionStatus = SessionStatus.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPredictionPointSessionResponse {
    return {
      sessionInfo: isSet(object.sessionInfo) ? SessionInfo.fromJSON(object.sessionInfo) : undefined,
      sessionStatus: isSet(object.sessionStatus) ? SessionStatus.fromJSON(object.sessionStatus) : undefined,
    };
  },

  toJSON(message: GetPredictionPointSessionResponse): unknown {
    const obj: any = {};
    if (message.sessionInfo !== undefined) {
      obj.sessionInfo = SessionInfo.toJSON(message.sessionInfo);
    }
    if (message.sessionStatus !== undefined) {
      obj.sessionStatus = SessionStatus.toJSON(message.sessionStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPredictionPointSessionResponse>, I>>(
    base?: I,
  ): GetPredictionPointSessionResponse {
    return GetPredictionPointSessionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPredictionPointSessionResponse>, I>>(
    object: I,
  ): GetPredictionPointSessionResponse {
    const message = createBaseGetPredictionPointSessionResponse();
    message.sessionInfo = (object.sessionInfo !== undefined && object.sessionInfo !== null)
      ? SessionInfo.fromPartial(object.sessionInfo)
      : undefined;
    message.sessionStatus = (object.sessionStatus !== undefined && object.sessionStatus !== null)
      ? SessionStatus.fromPartial(object.sessionStatus)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
