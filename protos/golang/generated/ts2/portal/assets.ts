// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: portal/assets.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "../google/protobuf/timestamp";

export const protobufPackage = "carbon.portal.assets";

export enum ReaperModuleAbLocation {
  LOCATION_UNSPECIFIED = 0,
  LOCATION_A = 1,
  LOCATION_B = 2,
  UNRECOGNIZED = -1,
}

export function reaperModuleAbLocationFromJSON(object: any): ReaperModuleAbLocation {
  switch (object) {
    case 0:
    case "LOCATION_UNSPECIFIED":
      return ReaperModuleAbLocation.LOCATION_UNSPECIFIED;
    case 1:
    case "LOCATION_A":
      return ReaperModuleAbLocation.LOCATION_A;
    case 2:
    case "LOCATION_B":
      return ReaperModuleAbLocation.LOCATION_B;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReaperModuleAbLocation.UNRECOGNIZED;
  }
}

export function reaperModuleAbLocationToJSON(object: ReaperModuleAbLocation): string {
  switch (object) {
    case ReaperModuleAbLocation.LOCATION_UNSPECIFIED:
      return "LOCATION_UNSPECIFIED";
    case ReaperModuleAbLocation.LOCATION_A:
      return "LOCATION_A";
    case ReaperModuleAbLocation.LOCATION_B:
      return "LOCATION_B";
    case ReaperModuleAbLocation.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SlayerRowComputerType {
  COMPUTER_UNKNOWN = 0,
  COMPUTER_SINGLE = 1,
  COMPUTER_PRIMARY = 2,
  COMPUTER_SECONDARY = 3,
  UNRECOGNIZED = -1,
}

export function slayerRowComputerTypeFromJSON(object: any): SlayerRowComputerType {
  switch (object) {
    case 0:
    case "COMPUTER_UNKNOWN":
      return SlayerRowComputerType.COMPUTER_UNKNOWN;
    case 1:
    case "COMPUTER_SINGLE":
      return SlayerRowComputerType.COMPUTER_SINGLE;
    case 2:
    case "COMPUTER_PRIMARY":
      return SlayerRowComputerType.COMPUTER_PRIMARY;
    case 3:
    case "COMPUTER_SECONDARY":
      return SlayerRowComputerType.COMPUTER_SECONDARY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SlayerRowComputerType.UNRECOGNIZED;
  }
}

export function slayerRowComputerTypeToJSON(object: SlayerRowComputerType): string {
  switch (object) {
    case SlayerRowComputerType.COMPUTER_UNKNOWN:
      return "COMPUTER_UNKNOWN";
    case SlayerRowComputerType.COMPUTER_SINGLE:
      return "COMPUTER_SINGLE";
    case SlayerRowComputerType.COMPUTER_PRIMARY:
      return "COMPUTER_PRIMARY";
    case SlayerRowComputerType.COMPUTER_SECONDARY:
      return "COMPUTER_SECONDARY";
    case SlayerRowComputerType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ReaperModuleAbAssetLocation {
  weedingModuleSerial: string;
  abLocation: ReaperModuleAbLocation;
}

export interface ReaperModuleSingleAssetLocation {
  weedingModuleSerial: string;
}

export interface SlayerRowAssetLocation {
  robotSerial: string;
  row: number;
  id: number;
}

export interface Scanner {
  serial: string;
  location: ScannerLocation | undefined;
}

export interface ScannerLocation {
  slayer?: SlayerRowAssetLocation | undefined;
  reaper?: ReaperModuleAbAssetLocation | undefined;
}

export interface Laser {
  serial: string;
  location: LaserLocation | undefined;
}

export interface LaserLocation {
  slayer?: SlayerRowAssetLocation | undefined;
  reaper?: ReaperModuleAbAssetLocation | undefined;
}

export interface TargetCam {
  serial: string;
  location: TargetCamLocation | undefined;
}

export interface TargetCamLocation {
  scannerSerial: string;
}

export interface PredictCam {
  serial: string;
  location: PredictCamLocation | undefined;
}

export interface PredictCamLocation {
  slayer?: SlayerRowAssetLocation | undefined;
  reaper?: ReaperModuleSingleAssetLocation | undefined;
}

export interface CommandComputer {
  serial: string;
  location: CommandComputerLocation | undefined;
}

export interface CommandComputerLocation {
  robotSerial: string;
}

export interface RowComputer {
  serial: string;
  location: RowComputerLocation | undefined;
}

export interface RowComputerLocation {
  slayer?: SlayerRowComputerLocation | undefined;
  reaper?: ReaperModuleSingleAssetLocation | undefined;
}

export interface SlayerRowComputerLocation {
  robotSerial: string;
  row: number;
  type: SlayerRowComputerType;
}

export interface WeedingModule {
  serial: string;
  location: WeedingModuleLocation | undefined;
}

export interface WeedingModuleLocation {
  robotSerial: string;
  moduleNumber: number;
}

export interface Starlink {
  serial: string;
  location: StarlinkLocation | undefined;
}

export interface StarlinkLocation {
  robotSerial: string;
}

export interface Modem {
  serial: string;
  location: ModemLocation | undefined;
}

export interface ModemLocation {
  robotSerial: string;
}

export interface AssetManifest {
  createTime: string | undefined;
  robotSerial: string;
  commandComputer: CommandComputer | undefined;
  weedingModules: WeedingModule[];
  rowComputers: RowComputer[];
  scanners: Scanner[];
  lasers: Laser[];
  targetCams: TargetCam[];
  predictCams: PredictCam[];
  modem: Modem | undefined;
  starlink: Starlink | undefined;
}

function createBaseReaperModuleAbAssetLocation(): ReaperModuleAbAssetLocation {
  return { weedingModuleSerial: "", abLocation: 0 };
}

export const ReaperModuleAbAssetLocation: MessageFns<ReaperModuleAbAssetLocation> = {
  encode(message: ReaperModuleAbAssetLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.weedingModuleSerial !== "") {
      writer.uint32(10).string(message.weedingModuleSerial);
    }
    if (message.abLocation !== 0) {
      writer.uint32(16).int32(message.abLocation);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReaperModuleAbAssetLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReaperModuleAbAssetLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.weedingModuleSerial = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.abLocation = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReaperModuleAbAssetLocation {
    return {
      weedingModuleSerial: isSet(object.weedingModuleSerial) ? globalThis.String(object.weedingModuleSerial) : "",
      abLocation: isSet(object.abLocation) ? reaperModuleAbLocationFromJSON(object.abLocation) : 0,
    };
  },

  toJSON(message: ReaperModuleAbAssetLocation): unknown {
    const obj: any = {};
    if (message.weedingModuleSerial !== "") {
      obj.weedingModuleSerial = message.weedingModuleSerial;
    }
    if (message.abLocation !== 0) {
      obj.abLocation = reaperModuleAbLocationToJSON(message.abLocation);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReaperModuleAbAssetLocation>, I>>(base?: I): ReaperModuleAbAssetLocation {
    return ReaperModuleAbAssetLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReaperModuleAbAssetLocation>, I>>(object: I): ReaperModuleAbAssetLocation {
    const message = createBaseReaperModuleAbAssetLocation();
    message.weedingModuleSerial = object.weedingModuleSerial ?? "";
    message.abLocation = object.abLocation ?? 0;
    return message;
  },
};

function createBaseReaperModuleSingleAssetLocation(): ReaperModuleSingleAssetLocation {
  return { weedingModuleSerial: "" };
}

export const ReaperModuleSingleAssetLocation: MessageFns<ReaperModuleSingleAssetLocation> = {
  encode(message: ReaperModuleSingleAssetLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.weedingModuleSerial !== "") {
      writer.uint32(10).string(message.weedingModuleSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReaperModuleSingleAssetLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReaperModuleSingleAssetLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.weedingModuleSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReaperModuleSingleAssetLocation {
    return {
      weedingModuleSerial: isSet(object.weedingModuleSerial) ? globalThis.String(object.weedingModuleSerial) : "",
    };
  },

  toJSON(message: ReaperModuleSingleAssetLocation): unknown {
    const obj: any = {};
    if (message.weedingModuleSerial !== "") {
      obj.weedingModuleSerial = message.weedingModuleSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReaperModuleSingleAssetLocation>, I>>(base?: I): ReaperModuleSingleAssetLocation {
    return ReaperModuleSingleAssetLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReaperModuleSingleAssetLocation>, I>>(
    object: I,
  ): ReaperModuleSingleAssetLocation {
    const message = createBaseReaperModuleSingleAssetLocation();
    message.weedingModuleSerial = object.weedingModuleSerial ?? "";
    return message;
  },
};

function createBaseSlayerRowAssetLocation(): SlayerRowAssetLocation {
  return { robotSerial: "", row: 0, id: 0 };
}

export const SlayerRowAssetLocation: MessageFns<SlayerRowAssetLocation> = {
  encode(message: SlayerRowAssetLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    if (message.row !== 0) {
      writer.uint32(16).int32(message.row);
    }
    if (message.id !== 0) {
      writer.uint32(24).int32(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SlayerRowAssetLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSlayerRowAssetLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.row = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.id = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SlayerRowAssetLocation {
    return {
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
      row: isSet(object.row) ? globalThis.Number(object.row) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
    };
  },

  toJSON(message: SlayerRowAssetLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    if (message.row !== 0) {
      obj.row = Math.round(message.row);
    }
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SlayerRowAssetLocation>, I>>(base?: I): SlayerRowAssetLocation {
    return SlayerRowAssetLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SlayerRowAssetLocation>, I>>(object: I): SlayerRowAssetLocation {
    const message = createBaseSlayerRowAssetLocation();
    message.robotSerial = object.robotSerial ?? "";
    message.row = object.row ?? 0;
    message.id = object.id ?? 0;
    return message;
  },
};

function createBaseScanner(): Scanner {
  return { serial: "", location: undefined };
}

export const Scanner: MessageFns<Scanner> = {
  encode(message: Scanner, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      ScannerLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Scanner {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseScanner();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = ScannerLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Scanner {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? ScannerLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: Scanner): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = ScannerLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Scanner>, I>>(base?: I): Scanner {
    return Scanner.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Scanner>, I>>(object: I): Scanner {
    const message = createBaseScanner();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? ScannerLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseScannerLocation(): ScannerLocation {
  return { slayer: undefined, reaper: undefined };
}

export const ScannerLocation: MessageFns<ScannerLocation> = {
  encode(message: ScannerLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slayer !== undefined) {
      SlayerRowAssetLocation.encode(message.slayer, writer.uint32(10).fork()).join();
    }
    if (message.reaper !== undefined) {
      ReaperModuleAbAssetLocation.encode(message.reaper, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ScannerLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseScannerLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.slayer = SlayerRowAssetLocation.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.reaper = ReaperModuleAbAssetLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ScannerLocation {
    return {
      slayer: isSet(object.slayer) ? SlayerRowAssetLocation.fromJSON(object.slayer) : undefined,
      reaper: isSet(object.reaper) ? ReaperModuleAbAssetLocation.fromJSON(object.reaper) : undefined,
    };
  },

  toJSON(message: ScannerLocation): unknown {
    const obj: any = {};
    if (message.slayer !== undefined) {
      obj.slayer = SlayerRowAssetLocation.toJSON(message.slayer);
    }
    if (message.reaper !== undefined) {
      obj.reaper = ReaperModuleAbAssetLocation.toJSON(message.reaper);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ScannerLocation>, I>>(base?: I): ScannerLocation {
    return ScannerLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScannerLocation>, I>>(object: I): ScannerLocation {
    const message = createBaseScannerLocation();
    message.slayer = (object.slayer !== undefined && object.slayer !== null)
      ? SlayerRowAssetLocation.fromPartial(object.slayer)
      : undefined;
    message.reaper = (object.reaper !== undefined && object.reaper !== null)
      ? ReaperModuleAbAssetLocation.fromPartial(object.reaper)
      : undefined;
    return message;
  },
};

function createBaseLaser(): Laser {
  return { serial: "", location: undefined };
}

export const Laser: MessageFns<Laser> = {
  encode(message: Laser, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      LaserLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Laser {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLaser();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = LaserLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Laser {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? LaserLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: Laser): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = LaserLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Laser>, I>>(base?: I): Laser {
    return Laser.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Laser>, I>>(object: I): Laser {
    const message = createBaseLaser();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? LaserLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseLaserLocation(): LaserLocation {
  return { slayer: undefined, reaper: undefined };
}

export const LaserLocation: MessageFns<LaserLocation> = {
  encode(message: LaserLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slayer !== undefined) {
      SlayerRowAssetLocation.encode(message.slayer, writer.uint32(10).fork()).join();
    }
    if (message.reaper !== undefined) {
      ReaperModuleAbAssetLocation.encode(message.reaper, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LaserLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLaserLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.slayer = SlayerRowAssetLocation.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.reaper = ReaperModuleAbAssetLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LaserLocation {
    return {
      slayer: isSet(object.slayer) ? SlayerRowAssetLocation.fromJSON(object.slayer) : undefined,
      reaper: isSet(object.reaper) ? ReaperModuleAbAssetLocation.fromJSON(object.reaper) : undefined,
    };
  },

  toJSON(message: LaserLocation): unknown {
    const obj: any = {};
    if (message.slayer !== undefined) {
      obj.slayer = SlayerRowAssetLocation.toJSON(message.slayer);
    }
    if (message.reaper !== undefined) {
      obj.reaper = ReaperModuleAbAssetLocation.toJSON(message.reaper);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LaserLocation>, I>>(base?: I): LaserLocation {
    return LaserLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LaserLocation>, I>>(object: I): LaserLocation {
    const message = createBaseLaserLocation();
    message.slayer = (object.slayer !== undefined && object.slayer !== null)
      ? SlayerRowAssetLocation.fromPartial(object.slayer)
      : undefined;
    message.reaper = (object.reaper !== undefined && object.reaper !== null)
      ? ReaperModuleAbAssetLocation.fromPartial(object.reaper)
      : undefined;
    return message;
  },
};

function createBaseTargetCam(): TargetCam {
  return { serial: "", location: undefined };
}

export const TargetCam: MessageFns<TargetCam> = {
  encode(message: TargetCam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      TargetCamLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TargetCam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTargetCam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = TargetCamLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TargetCam {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? TargetCamLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: TargetCam): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = TargetCamLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TargetCam>, I>>(base?: I): TargetCam {
    return TargetCam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TargetCam>, I>>(object: I): TargetCam {
    const message = createBaseTargetCam();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? TargetCamLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseTargetCamLocation(): TargetCamLocation {
  return { scannerSerial: "" };
}

export const TargetCamLocation: MessageFns<TargetCamLocation> = {
  encode(message: TargetCamLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.scannerSerial !== "") {
      writer.uint32(10).string(message.scannerSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TargetCamLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTargetCamLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.scannerSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TargetCamLocation {
    return { scannerSerial: isSet(object.scannerSerial) ? globalThis.String(object.scannerSerial) : "" };
  },

  toJSON(message: TargetCamLocation): unknown {
    const obj: any = {};
    if (message.scannerSerial !== "") {
      obj.scannerSerial = message.scannerSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TargetCamLocation>, I>>(base?: I): TargetCamLocation {
    return TargetCamLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TargetCamLocation>, I>>(object: I): TargetCamLocation {
    const message = createBaseTargetCamLocation();
    message.scannerSerial = object.scannerSerial ?? "";
    return message;
  },
};

function createBasePredictCam(): PredictCam {
  return { serial: "", location: undefined };
}

export const PredictCam: MessageFns<PredictCam> = {
  encode(message: PredictCam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      PredictCamLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PredictCam {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePredictCam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = PredictCamLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PredictCam {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? PredictCamLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: PredictCam): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = PredictCamLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PredictCam>, I>>(base?: I): PredictCam {
    return PredictCam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PredictCam>, I>>(object: I): PredictCam {
    const message = createBasePredictCam();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? PredictCamLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBasePredictCamLocation(): PredictCamLocation {
  return { slayer: undefined, reaper: undefined };
}

export const PredictCamLocation: MessageFns<PredictCamLocation> = {
  encode(message: PredictCamLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slayer !== undefined) {
      SlayerRowAssetLocation.encode(message.slayer, writer.uint32(10).fork()).join();
    }
    if (message.reaper !== undefined) {
      ReaperModuleSingleAssetLocation.encode(message.reaper, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PredictCamLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePredictCamLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.slayer = SlayerRowAssetLocation.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.reaper = ReaperModuleSingleAssetLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PredictCamLocation {
    return {
      slayer: isSet(object.slayer) ? SlayerRowAssetLocation.fromJSON(object.slayer) : undefined,
      reaper: isSet(object.reaper) ? ReaperModuleSingleAssetLocation.fromJSON(object.reaper) : undefined,
    };
  },

  toJSON(message: PredictCamLocation): unknown {
    const obj: any = {};
    if (message.slayer !== undefined) {
      obj.slayer = SlayerRowAssetLocation.toJSON(message.slayer);
    }
    if (message.reaper !== undefined) {
      obj.reaper = ReaperModuleSingleAssetLocation.toJSON(message.reaper);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PredictCamLocation>, I>>(base?: I): PredictCamLocation {
    return PredictCamLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PredictCamLocation>, I>>(object: I): PredictCamLocation {
    const message = createBasePredictCamLocation();
    message.slayer = (object.slayer !== undefined && object.slayer !== null)
      ? SlayerRowAssetLocation.fromPartial(object.slayer)
      : undefined;
    message.reaper = (object.reaper !== undefined && object.reaper !== null)
      ? ReaperModuleSingleAssetLocation.fromPartial(object.reaper)
      : undefined;
    return message;
  },
};

function createBaseCommandComputer(): CommandComputer {
  return { serial: "", location: undefined };
}

export const CommandComputer: MessageFns<CommandComputer> = {
  encode(message: CommandComputer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      CommandComputerLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommandComputer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandComputer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = CommandComputerLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandComputer {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? CommandComputerLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: CommandComputer): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = CommandComputerLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandComputer>, I>>(base?: I): CommandComputer {
    return CommandComputer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandComputer>, I>>(object: I): CommandComputer {
    const message = createBaseCommandComputer();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? CommandComputerLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseCommandComputerLocation(): CommandComputerLocation {
  return { robotSerial: "" };
}

export const CommandComputerLocation: MessageFns<CommandComputerLocation> = {
  encode(message: CommandComputerLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommandComputerLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandComputerLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandComputerLocation {
    return { robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "" };
  },

  toJSON(message: CommandComputerLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandComputerLocation>, I>>(base?: I): CommandComputerLocation {
    return CommandComputerLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandComputerLocation>, I>>(object: I): CommandComputerLocation {
    const message = createBaseCommandComputerLocation();
    message.robotSerial = object.robotSerial ?? "";
    return message;
  },
};

function createBaseRowComputer(): RowComputer {
  return { serial: "", location: undefined };
}

export const RowComputer: MessageFns<RowComputer> = {
  encode(message: RowComputer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      RowComputerLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RowComputer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRowComputer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = RowComputerLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RowComputer {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? RowComputerLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: RowComputer): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = RowComputerLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RowComputer>, I>>(base?: I): RowComputer {
    return RowComputer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RowComputer>, I>>(object: I): RowComputer {
    const message = createBaseRowComputer();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? RowComputerLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseRowComputerLocation(): RowComputerLocation {
  return { slayer: undefined, reaper: undefined };
}

export const RowComputerLocation: MessageFns<RowComputerLocation> = {
  encode(message: RowComputerLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slayer !== undefined) {
      SlayerRowComputerLocation.encode(message.slayer, writer.uint32(10).fork()).join();
    }
    if (message.reaper !== undefined) {
      ReaperModuleSingleAssetLocation.encode(message.reaper, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RowComputerLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRowComputerLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.slayer = SlayerRowComputerLocation.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.reaper = ReaperModuleSingleAssetLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RowComputerLocation {
    return {
      slayer: isSet(object.slayer) ? SlayerRowComputerLocation.fromJSON(object.slayer) : undefined,
      reaper: isSet(object.reaper) ? ReaperModuleSingleAssetLocation.fromJSON(object.reaper) : undefined,
    };
  },

  toJSON(message: RowComputerLocation): unknown {
    const obj: any = {};
    if (message.slayer !== undefined) {
      obj.slayer = SlayerRowComputerLocation.toJSON(message.slayer);
    }
    if (message.reaper !== undefined) {
      obj.reaper = ReaperModuleSingleAssetLocation.toJSON(message.reaper);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RowComputerLocation>, I>>(base?: I): RowComputerLocation {
    return RowComputerLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RowComputerLocation>, I>>(object: I): RowComputerLocation {
    const message = createBaseRowComputerLocation();
    message.slayer = (object.slayer !== undefined && object.slayer !== null)
      ? SlayerRowComputerLocation.fromPartial(object.slayer)
      : undefined;
    message.reaper = (object.reaper !== undefined && object.reaper !== null)
      ? ReaperModuleSingleAssetLocation.fromPartial(object.reaper)
      : undefined;
    return message;
  },
};

function createBaseSlayerRowComputerLocation(): SlayerRowComputerLocation {
  return { robotSerial: "", row: 0, type: 0 };
}

export const SlayerRowComputerLocation: MessageFns<SlayerRowComputerLocation> = {
  encode(message: SlayerRowComputerLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    if (message.row !== 0) {
      writer.uint32(16).int32(message.row);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SlayerRowComputerLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSlayerRowComputerLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.row = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SlayerRowComputerLocation {
    return {
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
      row: isSet(object.row) ? globalThis.Number(object.row) : 0,
      type: isSet(object.type) ? slayerRowComputerTypeFromJSON(object.type) : 0,
    };
  },

  toJSON(message: SlayerRowComputerLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    if (message.row !== 0) {
      obj.row = Math.round(message.row);
    }
    if (message.type !== 0) {
      obj.type = slayerRowComputerTypeToJSON(message.type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SlayerRowComputerLocation>, I>>(base?: I): SlayerRowComputerLocation {
    return SlayerRowComputerLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SlayerRowComputerLocation>, I>>(object: I): SlayerRowComputerLocation {
    const message = createBaseSlayerRowComputerLocation();
    message.robotSerial = object.robotSerial ?? "";
    message.row = object.row ?? 0;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseWeedingModule(): WeedingModule {
  return { serial: "", location: undefined };
}

export const WeedingModule: MessageFns<WeedingModule> = {
  encode(message: WeedingModule, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      WeedingModuleLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WeedingModule {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWeedingModule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = WeedingModuleLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WeedingModule {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? WeedingModuleLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: WeedingModule): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = WeedingModuleLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WeedingModule>, I>>(base?: I): WeedingModule {
    return WeedingModule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WeedingModule>, I>>(object: I): WeedingModule {
    const message = createBaseWeedingModule();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? WeedingModuleLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseWeedingModuleLocation(): WeedingModuleLocation {
  return { robotSerial: "", moduleNumber: 0 };
}

export const WeedingModuleLocation: MessageFns<WeedingModuleLocation> = {
  encode(message: WeedingModuleLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    if (message.moduleNumber !== 0) {
      writer.uint32(16).int32(message.moduleNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WeedingModuleLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWeedingModuleLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.moduleNumber = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WeedingModuleLocation {
    return {
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
      moduleNumber: isSet(object.moduleNumber) ? globalThis.Number(object.moduleNumber) : 0,
    };
  },

  toJSON(message: WeedingModuleLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    if (message.moduleNumber !== 0) {
      obj.moduleNumber = Math.round(message.moduleNumber);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WeedingModuleLocation>, I>>(base?: I): WeedingModuleLocation {
    return WeedingModuleLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WeedingModuleLocation>, I>>(object: I): WeedingModuleLocation {
    const message = createBaseWeedingModuleLocation();
    message.robotSerial = object.robotSerial ?? "";
    message.moduleNumber = object.moduleNumber ?? 0;
    return message;
  },
};

function createBaseStarlink(): Starlink {
  return { serial: "", location: undefined };
}

export const Starlink: MessageFns<Starlink> = {
  encode(message: Starlink, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      StarlinkLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Starlink {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStarlink();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = StarlinkLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Starlink {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? StarlinkLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: Starlink): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = StarlinkLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Starlink>, I>>(base?: I): Starlink {
    return Starlink.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Starlink>, I>>(object: I): Starlink {
    const message = createBaseStarlink();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? StarlinkLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseStarlinkLocation(): StarlinkLocation {
  return { robotSerial: "" };
}

export const StarlinkLocation: MessageFns<StarlinkLocation> = {
  encode(message: StarlinkLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StarlinkLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStarlinkLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StarlinkLocation {
    return { robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "" };
  },

  toJSON(message: StarlinkLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StarlinkLocation>, I>>(base?: I): StarlinkLocation {
    return StarlinkLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StarlinkLocation>, I>>(object: I): StarlinkLocation {
    const message = createBaseStarlinkLocation();
    message.robotSerial = object.robotSerial ?? "";
    return message;
  },
};

function createBaseModem(): Modem {
  return { serial: "", location: undefined };
}

export const Modem: MessageFns<Modem> = {
  encode(message: Modem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.serial !== "") {
      writer.uint32(10).string(message.serial);
    }
    if (message.location !== undefined) {
      ModemLocation.encode(message.location, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Modem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseModem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.serial = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.location = ModemLocation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Modem {
    return {
      serial: isSet(object.serial) ? globalThis.String(object.serial) : "",
      location: isSet(object.location) ? ModemLocation.fromJSON(object.location) : undefined,
    };
  },

  toJSON(message: Modem): unknown {
    const obj: any = {};
    if (message.serial !== "") {
      obj.serial = message.serial;
    }
    if (message.location !== undefined) {
      obj.location = ModemLocation.toJSON(message.location);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Modem>, I>>(base?: I): Modem {
    return Modem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Modem>, I>>(object: I): Modem {
    const message = createBaseModem();
    message.serial = object.serial ?? "";
    message.location = (object.location !== undefined && object.location !== null)
      ? ModemLocation.fromPartial(object.location)
      : undefined;
    return message;
  },
};

function createBaseModemLocation(): ModemLocation {
  return { robotSerial: "" };
}

export const ModemLocation: MessageFns<ModemLocation> = {
  encode(message: ModemLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ModemLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseModemLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ModemLocation {
    return { robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "" };
  },

  toJSON(message: ModemLocation): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ModemLocation>, I>>(base?: I): ModemLocation {
    return ModemLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ModemLocation>, I>>(object: I): ModemLocation {
    const message = createBaseModemLocation();
    message.robotSerial = object.robotSerial ?? "";
    return message;
  },
};

function createBaseAssetManifest(): AssetManifest {
  return {
    createTime: undefined,
    robotSerial: "",
    commandComputer: undefined,
    weedingModules: [],
    rowComputers: [],
    scanners: [],
    lasers: [],
    targetCams: [],
    predictCams: [],
    modem: undefined,
    starlink: undefined,
  };
}

export const AssetManifest: MessageFns<AssetManifest> = {
  encode(message: AssetManifest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(10).fork()).join();
    }
    if (message.robotSerial !== "") {
      writer.uint32(18).string(message.robotSerial);
    }
    if (message.commandComputer !== undefined) {
      CommandComputer.encode(message.commandComputer, writer.uint32(34).fork()).join();
    }
    for (const v of message.weedingModules) {
      WeedingModule.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.rowComputers) {
      RowComputer.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.scanners) {
      Scanner.encode(v!, writer.uint32(50).fork()).join();
    }
    for (const v of message.lasers) {
      Laser.encode(v!, writer.uint32(58).fork()).join();
    }
    for (const v of message.targetCams) {
      TargetCam.encode(v!, writer.uint32(66).fork()).join();
    }
    for (const v of message.predictCams) {
      PredictCam.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.modem !== undefined) {
      Modem.encode(message.modem, writer.uint32(82).fork()).join();
    }
    if (message.starlink !== undefined) {
      Starlink.encode(message.starlink, writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssetManifest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssetManifest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.commandComputer = CommandComputer.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.weedingModules.push(WeedingModule.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.rowComputers.push(RowComputer.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.scanners.push(Scanner.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.lasers.push(Laser.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.targetCams.push(TargetCam.decode(reader, reader.uint32()));
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.predictCams.push(PredictCam.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.modem = Modem.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.starlink = Starlink.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AssetManifest {
    return {
      createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : undefined,
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
      commandComputer: isSet(object.commandComputer) ? CommandComputer.fromJSON(object.commandComputer) : undefined,
      weedingModules: globalThis.Array.isArray(object?.weedingModules)
        ? object.weedingModules.map((e: any) => WeedingModule.fromJSON(e))
        : [],
      rowComputers: globalThis.Array.isArray(object?.rowComputers)
        ? object.rowComputers.map((e: any) => RowComputer.fromJSON(e))
        : [],
      scanners: globalThis.Array.isArray(object?.scanners) ? object.scanners.map((e: any) => Scanner.fromJSON(e)) : [],
      lasers: globalThis.Array.isArray(object?.lasers) ? object.lasers.map((e: any) => Laser.fromJSON(e)) : [],
      targetCams: globalThis.Array.isArray(object?.targetCams)
        ? object.targetCams.map((e: any) => TargetCam.fromJSON(e))
        : [],
      predictCams: globalThis.Array.isArray(object?.predictCams)
        ? object.predictCams.map((e: any) => PredictCam.fromJSON(e))
        : [],
      modem: isSet(object.modem) ? Modem.fromJSON(object.modem) : undefined,
      starlink: isSet(object.starlink) ? Starlink.fromJSON(object.starlink) : undefined,
    };
  },

  toJSON(message: AssetManifest): unknown {
    const obj: any = {};
    if (message.createTime !== undefined) {
      obj.createTime = message.createTime;
    }
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    if (message.commandComputer !== undefined) {
      obj.commandComputer = CommandComputer.toJSON(message.commandComputer);
    }
    if (message.weedingModules?.length) {
      obj.weedingModules = message.weedingModules.map((e) => WeedingModule.toJSON(e));
    }
    if (message.rowComputers?.length) {
      obj.rowComputers = message.rowComputers.map((e) => RowComputer.toJSON(e));
    }
    if (message.scanners?.length) {
      obj.scanners = message.scanners.map((e) => Scanner.toJSON(e));
    }
    if (message.lasers?.length) {
      obj.lasers = message.lasers.map((e) => Laser.toJSON(e));
    }
    if (message.targetCams?.length) {
      obj.targetCams = message.targetCams.map((e) => TargetCam.toJSON(e));
    }
    if (message.predictCams?.length) {
      obj.predictCams = message.predictCams.map((e) => PredictCam.toJSON(e));
    }
    if (message.modem !== undefined) {
      obj.modem = Modem.toJSON(message.modem);
    }
    if (message.starlink !== undefined) {
      obj.starlink = Starlink.toJSON(message.starlink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AssetManifest>, I>>(base?: I): AssetManifest {
    return AssetManifest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssetManifest>, I>>(object: I): AssetManifest {
    const message = createBaseAssetManifest();
    message.createTime = object.createTime ?? undefined;
    message.robotSerial = object.robotSerial ?? "";
    message.commandComputer = (object.commandComputer !== undefined && object.commandComputer !== null)
      ? CommandComputer.fromPartial(object.commandComputer)
      : undefined;
    message.weedingModules = object.weedingModules?.map((e) => WeedingModule.fromPartial(e)) || [];
    message.rowComputers = object.rowComputers?.map((e) => RowComputer.fromPartial(e)) || [];
    message.scanners = object.scanners?.map((e) => Scanner.fromPartial(e)) || [];
    message.lasers = object.lasers?.map((e) => Laser.fromPartial(e)) || [];
    message.targetCams = object.targetCams?.map((e) => TargetCam.fromPartial(e)) || [];
    message.predictCams = object.predictCams?.map((e) => PredictCam.fromPartial(e)) || [];
    message.modem = (object.modem !== undefined && object.modem !== null) ? Modem.fromPartial(object.modem) : undefined;
    message.starlink = (object.starlink !== undefined && object.starlink !== null)
      ? Starlink.fromPartial(object.starlink)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(dateStr: string): Timestamp {
  const date = new globalThis.Date(dateStr);
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): string {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis).toISOString();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
