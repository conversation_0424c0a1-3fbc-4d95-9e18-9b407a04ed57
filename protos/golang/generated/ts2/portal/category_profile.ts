// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: portal/category_profile.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Category, CategoryCollection } from "../category_profile/category_profile";

export const protobufPackage = "carbon.portal.category_profile";

export interface Metadata {
  updatedAt?: number | undefined;
}

export interface SavedCategoryCollection {
  profile: CategoryCollection | undefined;
  metadata: Metadata | undefined;
}

export interface SavedCategory {
  profile: Category | undefined;
  metadata: Metadata | undefined;
}

export interface SavedExpandedCategoryCollection {
  profile: SavedCategoryCollection | undefined;
  categories: SavedCategory[];
}

export interface UnsavedExpandedCategoryCollection {
  profile: CategoryCollection | undefined;
  categories: Category[];
}

function createBaseMetadata(): Metadata {
  return { updatedAt: undefined };
}

export const Metadata: MessageFns<Metadata> = {
  encode(message: Metadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.updatedAt !== undefined) {
      writer.uint32(48).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Metadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 48) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Metadata {
    return { updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : undefined };
  },

  toJSON(message: Metadata): unknown {
    const obj: any = {};
    if (message.updatedAt !== undefined) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Metadata>, I>>(base?: I): Metadata {
    return Metadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Metadata>, I>>(object: I): Metadata {
    const message = createBaseMetadata();
    message.updatedAt = object.updatedAt ?? undefined;
    return message;
  },
};

function createBaseSavedCategoryCollection(): SavedCategoryCollection {
  return { profile: undefined, metadata: undefined };
}

export const SavedCategoryCollection: MessageFns<SavedCategoryCollection> = {
  encode(message: SavedCategoryCollection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.profile !== undefined) {
      CategoryCollection.encode(message.profile, writer.uint32(10).fork()).join();
    }
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SavedCategoryCollection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSavedCategoryCollection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.profile = CategoryCollection.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SavedCategoryCollection {
    return {
      profile: isSet(object.profile) ? CategoryCollection.fromJSON(object.profile) : undefined,
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
    };
  },

  toJSON(message: SavedCategoryCollection): unknown {
    const obj: any = {};
    if (message.profile !== undefined) {
      obj.profile = CategoryCollection.toJSON(message.profile);
    }
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SavedCategoryCollection>, I>>(base?: I): SavedCategoryCollection {
    return SavedCategoryCollection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavedCategoryCollection>, I>>(object: I): SavedCategoryCollection {
    const message = createBaseSavedCategoryCollection();
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? CategoryCollection.fromPartial(object.profile)
      : undefined;
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    return message;
  },
};

function createBaseSavedCategory(): SavedCategory {
  return { profile: undefined, metadata: undefined };
}

export const SavedCategory: MessageFns<SavedCategory> = {
  encode(message: SavedCategory, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.profile !== undefined) {
      Category.encode(message.profile, writer.uint32(10).fork()).join();
    }
    if (message.metadata !== undefined) {
      Metadata.encode(message.metadata, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SavedCategory {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSavedCategory();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.profile = Category.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.metadata = Metadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SavedCategory {
    return {
      profile: isSet(object.profile) ? Category.fromJSON(object.profile) : undefined,
      metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
    };
  },

  toJSON(message: SavedCategory): unknown {
    const obj: any = {};
    if (message.profile !== undefined) {
      obj.profile = Category.toJSON(message.profile);
    }
    if (message.metadata !== undefined) {
      obj.metadata = Metadata.toJSON(message.metadata);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SavedCategory>, I>>(base?: I): SavedCategory {
    return SavedCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavedCategory>, I>>(object: I): SavedCategory {
    const message = createBaseSavedCategory();
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? Category.fromPartial(object.profile)
      : undefined;
    message.metadata = (object.metadata !== undefined && object.metadata !== null)
      ? Metadata.fromPartial(object.metadata)
      : undefined;
    return message;
  },
};

function createBaseSavedExpandedCategoryCollection(): SavedExpandedCategoryCollection {
  return { profile: undefined, categories: [] };
}

export const SavedExpandedCategoryCollection: MessageFns<SavedExpandedCategoryCollection> = {
  encode(message: SavedExpandedCategoryCollection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.profile !== undefined) {
      SavedCategoryCollection.encode(message.profile, writer.uint32(10).fork()).join();
    }
    for (const v of message.categories) {
      SavedCategory.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SavedExpandedCategoryCollection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSavedExpandedCategoryCollection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.profile = SavedCategoryCollection.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.categories.push(SavedCategory.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SavedExpandedCategoryCollection {
    return {
      profile: isSet(object.profile) ? SavedCategoryCollection.fromJSON(object.profile) : undefined,
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => SavedCategory.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SavedExpandedCategoryCollection): unknown {
    const obj: any = {};
    if (message.profile !== undefined) {
      obj.profile = SavedCategoryCollection.toJSON(message.profile);
    }
    if (message.categories?.length) {
      obj.categories = message.categories.map((e) => SavedCategory.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SavedExpandedCategoryCollection>, I>>(base?: I): SavedExpandedCategoryCollection {
    return SavedExpandedCategoryCollection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavedExpandedCategoryCollection>, I>>(
    object: I,
  ): SavedExpandedCategoryCollection {
    const message = createBaseSavedExpandedCategoryCollection();
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? SavedCategoryCollection.fromPartial(object.profile)
      : undefined;
    message.categories = object.categories?.map((e) => SavedCategory.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUnsavedExpandedCategoryCollection(): UnsavedExpandedCategoryCollection {
  return { profile: undefined, categories: [] };
}

export const UnsavedExpandedCategoryCollection: MessageFns<UnsavedExpandedCategoryCollection> = {
  encode(message: UnsavedExpandedCategoryCollection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.profile !== undefined) {
      CategoryCollection.encode(message.profile, writer.uint32(10).fork()).join();
    }
    for (const v of message.categories) {
      Category.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UnsavedExpandedCategoryCollection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUnsavedExpandedCategoryCollection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.profile = CategoryCollection.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.categories.push(Category.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UnsavedExpandedCategoryCollection {
    return {
      profile: isSet(object.profile) ? CategoryCollection.fromJSON(object.profile) : undefined,
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => Category.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UnsavedExpandedCategoryCollection): unknown {
    const obj: any = {};
    if (message.profile !== undefined) {
      obj.profile = CategoryCollection.toJSON(message.profile);
    }
    if (message.categories?.length) {
      obj.categories = message.categories.map((e) => Category.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UnsavedExpandedCategoryCollection>, I>>(
    base?: I,
  ): UnsavedExpandedCategoryCollection {
    return UnsavedExpandedCategoryCollection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnsavedExpandedCategoryCollection>, I>>(
    object: I,
  ): UnsavedExpandedCategoryCollection {
    const message = createBaseUnsavedExpandedCategoryCollection();
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? CategoryCollection.fromPartial(object.profile)
      : undefined;
    message.categories = object.categories?.map((e) => Category.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
