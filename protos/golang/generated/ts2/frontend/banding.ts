// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: frontend/banding.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "../util/util";
import { BandDefinition, DiagnosticsSnapshot } from "../weed_tracking/weed_tracking";

export const protobufPackage = "carbon.frontend.banding";

export enum VisualizationTypeToInclude {
  DUPLICATE_WEED = 0,
  DUPLICATE_CROP = 1,
  KILLED_WEED = 2,
  KILLED_CROP = 3,
  KILLING_WEED = 4,
  IGNORED_WEED = 5,
  KILLING_CROP = 6,
  ERROR_WEED = 7,
  ERROR_CROP = 8,
  IGNORED_CROP = 9,
  WEED = 10,
  CROP = 11,
  CROP_RADIUS = 12,
  CROP_KEPT = 13,
  THINNING_BOX = 14,
  UNRECOGNIZED = -1,
}

export function visualizationTypeToIncludeFromJSON(object: any): VisualizationTypeToInclude {
  switch (object) {
    case 0:
    case "DUPLICATE_WEED":
      return VisualizationTypeToInclude.DUPLICATE_WEED;
    case 1:
    case "DUPLICATE_CROP":
      return VisualizationTypeToInclude.DUPLICATE_CROP;
    case 2:
    case "KILLED_WEED":
      return VisualizationTypeToInclude.KILLED_WEED;
    case 3:
    case "KILLED_CROP":
      return VisualizationTypeToInclude.KILLED_CROP;
    case 4:
    case "KILLING_WEED":
      return VisualizationTypeToInclude.KILLING_WEED;
    case 5:
    case "IGNORED_WEED":
      return VisualizationTypeToInclude.IGNORED_WEED;
    case 6:
    case "KILLING_CROP":
      return VisualizationTypeToInclude.KILLING_CROP;
    case 7:
    case "ERROR_WEED":
      return VisualizationTypeToInclude.ERROR_WEED;
    case 8:
    case "ERROR_CROP":
      return VisualizationTypeToInclude.ERROR_CROP;
    case 9:
    case "IGNORED_CROP":
      return VisualizationTypeToInclude.IGNORED_CROP;
    case 10:
    case "WEED":
      return VisualizationTypeToInclude.WEED;
    case 11:
    case "CROP":
      return VisualizationTypeToInclude.CROP;
    case 12:
    case "CROP_RADIUS":
      return VisualizationTypeToInclude.CROP_RADIUS;
    case 13:
    case "CROP_KEPT":
      return VisualizationTypeToInclude.CROP_KEPT;
    case 14:
    case "THINNING_BOX":
      return VisualizationTypeToInclude.THINNING_BOX;
    case -1:
    case "UNRECOGNIZED":
    default:
      return VisualizationTypeToInclude.UNRECOGNIZED;
  }
}

export function visualizationTypeToIncludeToJSON(object: VisualizationTypeToInclude): string {
  switch (object) {
    case VisualizationTypeToInclude.DUPLICATE_WEED:
      return "DUPLICATE_WEED";
    case VisualizationTypeToInclude.DUPLICATE_CROP:
      return "DUPLICATE_CROP";
    case VisualizationTypeToInclude.KILLED_WEED:
      return "KILLED_WEED";
    case VisualizationTypeToInclude.KILLED_CROP:
      return "KILLED_CROP";
    case VisualizationTypeToInclude.KILLING_WEED:
      return "KILLING_WEED";
    case VisualizationTypeToInclude.IGNORED_WEED:
      return "IGNORED_WEED";
    case VisualizationTypeToInclude.KILLING_CROP:
      return "KILLING_CROP";
    case VisualizationTypeToInclude.ERROR_WEED:
      return "ERROR_WEED";
    case VisualizationTypeToInclude.ERROR_CROP:
      return "ERROR_CROP";
    case VisualizationTypeToInclude.IGNORED_CROP:
      return "IGNORED_CROP";
    case VisualizationTypeToInclude.WEED:
      return "WEED";
    case VisualizationTypeToInclude.CROP:
      return "CROP";
    case VisualizationTypeToInclude.CROP_RADIUS:
      return "CROP_RADIUS";
    case VisualizationTypeToInclude.CROP_KEPT:
      return "CROP_KEPT";
    case VisualizationTypeToInclude.THINNING_BOX:
      return "THINNING_BOX";
    case VisualizationTypeToInclude.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ThresholdState {
  ANY = 0,
  PASS = 1,
  FAIL = 2,
  UNRECOGNIZED = -1,
}

export function thresholdStateFromJSON(object: any): ThresholdState {
  switch (object) {
    case 0:
    case "ANY":
      return ThresholdState.ANY;
    case 1:
    case "PASS":
      return ThresholdState.PASS;
    case 2:
    case "FAIL":
      return ThresholdState.FAIL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ThresholdState.UNRECOGNIZED;
  }
}

export function thresholdStateToJSON(object: ThresholdState): string {
  switch (object) {
    case ThresholdState.ANY:
      return "ANY";
    case ThresholdState.PASS:
      return "PASS";
    case ThresholdState.FAIL:
      return "FAIL";
    case ThresholdState.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface BandingRow {
  rowId: number;
  bands: BandDefinition[];
}

export interface BandingDef {
  name: string;
  rows: BandingRow[];
  uuid: string;
}

export interface SaveBandingDefRequest {
  bandingDef: BandingDef | undefined;
  setActive: boolean;
}

export interface LoadBandingDefsResponse {
  bandingDefs: BandingDef[];
  /** @deprecated */
  activeDef: string;
  activeDefUUID: string;
}

export interface SetActiveBandingDefRequest {
  /** @deprecated */
  name: string;
  uuid: string;
}

export interface GetActiveBandingDefResponse {
  /** @deprecated */
  name: string;
  uuid: string;
}

export interface DeleteBandingDefRequest {
  /** @deprecated */
  name: string;
  uuid: string;
}

export interface VisualizationData {
  xMm: number;
  yMm: number;
  zMm: number;
  isWeed: boolean;
}

export interface GetNextVisualizationDataRequest {
  ts: Timestamp | undefined;
  rowId: number;
  typesToInclude: VisualizationTypeToInclude[];
  thresholdFilters: ThresholdFilters | undefined;
  includeDetailedMetadata?: boolean | undefined;
}

export interface GetNextVisualizationDataResponse {
  ts: Timestamp | undefined;
  data: VisualizationData[];
  bands: BandDefinition[];
}

export interface GetNextVisualizationData2Response {
  data: DiagnosticsSnapshot | undefined;
  ts: Timestamp | undefined;
}

export interface GetDimensionsRequest {
  rowId: number;
}

export interface SetBandingEnabledRequest {
  enabled: boolean;
}

export interface IsBandingEnabledResponse {
  enabled: boolean;
}

export interface GetVisualizationMetadataResponse {
  cropSafetyRadiusMmPerRow: { [key: number]: number };
}

export interface GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
  key: number;
  value: number;
}

export interface ThresholdFilter {
  weeding: ThresholdState;
  thinning: ThresholdState;
  banding: ThresholdState;
}

export interface ThresholdFilters {
  crop: ThresholdFilter | undefined;
  weed: ThresholdFilter | undefined;
}

export interface GetNextVisualizationDataForAllRowsRequest {
  ts: Timestamp | undefined;
  typesToInclude: VisualizationTypeToInclude[];
  thresholdFilters: ThresholdFilters | undefined;
  includeDetailedMetadata?: boolean | undefined;
}

export interface GetNextVisualizationDataForAllRowsResponse {
  dataPerRow: { [key: number]: DiagnosticsSnapshot };
  ts: Timestamp | undefined;
  typesToInclude: VisualizationTypeToInclude[];
}

export interface GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
  key: number;
  value: DiagnosticsSnapshot | undefined;
}

export interface GetNextBandingStateResponse {
  ts: Timestamp | undefined;
  bandingDefs: BandingDef[];
  activeDefUUID: string;
  isBandingEnabled: boolean;
  isDynamicBandingEnabled: boolean;
}

function createBaseBandingRow(): BandingRow {
  return { rowId: 0, bands: [] };
}

export const BandingRow: MessageFns<BandingRow> = {
  encode(message: BandingRow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rowId !== 0) {
      writer.uint32(8).int32(message.rowId);
    }
    for (const v of message.bands) {
      BandDefinition.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BandingRow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBandingRow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.rowId = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.bands.push(BandDefinition.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BandingRow {
    return {
      rowId: isSet(object.rowId) ? globalThis.Number(object.rowId) : 0,
      bands: globalThis.Array.isArray(object?.bands) ? object.bands.map((e: any) => BandDefinition.fromJSON(e)) : [],
    };
  },

  toJSON(message: BandingRow): unknown {
    const obj: any = {};
    if (message.rowId !== 0) {
      obj.rowId = Math.round(message.rowId);
    }
    if (message.bands?.length) {
      obj.bands = message.bands.map((e) => BandDefinition.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BandingRow>, I>>(base?: I): BandingRow {
    return BandingRow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BandingRow>, I>>(object: I): BandingRow {
    const message = createBaseBandingRow();
    message.rowId = object.rowId ?? 0;
    message.bands = object.bands?.map((e) => BandDefinition.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBandingDef(): BandingDef {
  return { name: "", rows: [], uuid: "" };
}

export const BandingDef: MessageFns<BandingDef> = {
  encode(message: BandingDef, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.rows) {
      BandingRow.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.uuid !== "") {
      writer.uint32(26).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BandingDef {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBandingDef();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.rows.push(BandingRow.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.uuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BandingDef {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      rows: globalThis.Array.isArray(object?.rows) ? object.rows.map((e: any) => BandingRow.fromJSON(e)) : [],
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: BandingDef): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.rows?.length) {
      obj.rows = message.rows.map((e) => BandingRow.toJSON(e));
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BandingDef>, I>>(base?: I): BandingDef {
    return BandingDef.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BandingDef>, I>>(object: I): BandingDef {
    const message = createBaseBandingDef();
    message.name = object.name ?? "";
    message.rows = object.rows?.map((e) => BandingRow.fromPartial(e)) || [];
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseSaveBandingDefRequest(): SaveBandingDefRequest {
  return { bandingDef: undefined, setActive: false };
}

export const SaveBandingDefRequest: MessageFns<SaveBandingDefRequest> = {
  encode(message: SaveBandingDefRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bandingDef !== undefined) {
      BandingDef.encode(message.bandingDef, writer.uint32(10).fork()).join();
    }
    if (message.setActive !== false) {
      writer.uint32(16).bool(message.setActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveBandingDefRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveBandingDefRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.bandingDef = BandingDef.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.setActive = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SaveBandingDefRequest {
    return {
      bandingDef: isSet(object.bandingDef) ? BandingDef.fromJSON(object.bandingDef) : undefined,
      setActive: isSet(object.setActive) ? globalThis.Boolean(object.setActive) : false,
    };
  },

  toJSON(message: SaveBandingDefRequest): unknown {
    const obj: any = {};
    if (message.bandingDef !== undefined) {
      obj.bandingDef = BandingDef.toJSON(message.bandingDef);
    }
    if (message.setActive !== false) {
      obj.setActive = message.setActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveBandingDefRequest>, I>>(base?: I): SaveBandingDefRequest {
    return SaveBandingDefRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveBandingDefRequest>, I>>(object: I): SaveBandingDefRequest {
    const message = createBaseSaveBandingDefRequest();
    message.bandingDef = (object.bandingDef !== undefined && object.bandingDef !== null)
      ? BandingDef.fromPartial(object.bandingDef)
      : undefined;
    message.setActive = object.setActive ?? false;
    return message;
  },
};

function createBaseLoadBandingDefsResponse(): LoadBandingDefsResponse {
  return { bandingDefs: [], activeDef: "", activeDefUUID: "" };
}

export const LoadBandingDefsResponse: MessageFns<LoadBandingDefsResponse> = {
  encode(message: LoadBandingDefsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bandingDefs) {
      BandingDef.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.activeDef !== "") {
      writer.uint32(18).string(message.activeDef);
    }
    if (message.activeDefUUID !== "") {
      writer.uint32(26).string(message.activeDefUUID);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LoadBandingDefsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLoadBandingDefsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.bandingDefs.push(BandingDef.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.activeDef = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.activeDefUUID = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LoadBandingDefsResponse {
    return {
      bandingDefs: globalThis.Array.isArray(object?.bandingDefs)
        ? object.bandingDefs.map((e: any) => BandingDef.fromJSON(e))
        : [],
      activeDef: isSet(object.activeDef) ? globalThis.String(object.activeDef) : "",
      activeDefUUID: isSet(object.activeDefUUID) ? globalThis.String(object.activeDefUUID) : "",
    };
  },

  toJSON(message: LoadBandingDefsResponse): unknown {
    const obj: any = {};
    if (message.bandingDefs?.length) {
      obj.bandingDefs = message.bandingDefs.map((e) => BandingDef.toJSON(e));
    }
    if (message.activeDef !== "") {
      obj.activeDef = message.activeDef;
    }
    if (message.activeDefUUID !== "") {
      obj.activeDefUUID = message.activeDefUUID;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LoadBandingDefsResponse>, I>>(base?: I): LoadBandingDefsResponse {
    return LoadBandingDefsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoadBandingDefsResponse>, I>>(object: I): LoadBandingDefsResponse {
    const message = createBaseLoadBandingDefsResponse();
    message.bandingDefs = object.bandingDefs?.map((e) => BandingDef.fromPartial(e)) || [];
    message.activeDef = object.activeDef ?? "";
    message.activeDefUUID = object.activeDefUUID ?? "";
    return message;
  },
};

function createBaseSetActiveBandingDefRequest(): SetActiveBandingDefRequest {
  return { name: "", uuid: "" };
}

export const SetActiveBandingDefRequest: MessageFns<SetActiveBandingDefRequest> = {
  encode(message: SetActiveBandingDefRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.uuid !== "") {
      writer.uint32(18).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetActiveBandingDefRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetActiveBandingDefRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.uuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetActiveBandingDefRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: SetActiveBandingDefRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetActiveBandingDefRequest>, I>>(base?: I): SetActiveBandingDefRequest {
    return SetActiveBandingDefRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetActiveBandingDefRequest>, I>>(object: I): SetActiveBandingDefRequest {
    const message = createBaseSetActiveBandingDefRequest();
    message.name = object.name ?? "";
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseGetActiveBandingDefResponse(): GetActiveBandingDefResponse {
  return { name: "", uuid: "" };
}

export const GetActiveBandingDefResponse: MessageFns<GetActiveBandingDefResponse> = {
  encode(message: GetActiveBandingDefResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.uuid !== "") {
      writer.uint32(18).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetActiveBandingDefResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetActiveBandingDefResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.uuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetActiveBandingDefResponse {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: GetActiveBandingDefResponse): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetActiveBandingDefResponse>, I>>(base?: I): GetActiveBandingDefResponse {
    return GetActiveBandingDefResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActiveBandingDefResponse>, I>>(object: I): GetActiveBandingDefResponse {
    const message = createBaseGetActiveBandingDefResponse();
    message.name = object.name ?? "";
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseDeleteBandingDefRequest(): DeleteBandingDefRequest {
  return { name: "", uuid: "" };
}

export const DeleteBandingDefRequest: MessageFns<DeleteBandingDefRequest> = {
  encode(message: DeleteBandingDefRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.uuid !== "") {
      writer.uint32(18).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteBandingDefRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteBandingDefRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.uuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteBandingDefRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: DeleteBandingDefRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteBandingDefRequest>, I>>(base?: I): DeleteBandingDefRequest {
    return DeleteBandingDefRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBandingDefRequest>, I>>(object: I): DeleteBandingDefRequest {
    const message = createBaseDeleteBandingDefRequest();
    message.name = object.name ?? "";
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseVisualizationData(): VisualizationData {
  return { xMm: 0, yMm: 0, zMm: 0, isWeed: false };
}

export const VisualizationData: MessageFns<VisualizationData> = {
  encode(message: VisualizationData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.xMm !== 0) {
      writer.uint32(8).int32(message.xMm);
    }
    if (message.yMm !== 0) {
      writer.uint32(16).int32(message.yMm);
    }
    if (message.zMm !== 0) {
      writer.uint32(24).int32(message.zMm);
    }
    if (message.isWeed !== false) {
      writer.uint32(32).bool(message.isWeed);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VisualizationData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVisualizationData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.xMm = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.yMm = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.zMm = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.isWeed = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VisualizationData {
    return {
      xMm: isSet(object.xMm) ? globalThis.Number(object.xMm) : 0,
      yMm: isSet(object.yMm) ? globalThis.Number(object.yMm) : 0,
      zMm: isSet(object.zMm) ? globalThis.Number(object.zMm) : 0,
      isWeed: isSet(object.isWeed) ? globalThis.Boolean(object.isWeed) : false,
    };
  },

  toJSON(message: VisualizationData): unknown {
    const obj: any = {};
    if (message.xMm !== 0) {
      obj.xMm = Math.round(message.xMm);
    }
    if (message.yMm !== 0) {
      obj.yMm = Math.round(message.yMm);
    }
    if (message.zMm !== 0) {
      obj.zMm = Math.round(message.zMm);
    }
    if (message.isWeed !== false) {
      obj.isWeed = message.isWeed;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VisualizationData>, I>>(base?: I): VisualizationData {
    return VisualizationData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VisualizationData>, I>>(object: I): VisualizationData {
    const message = createBaseVisualizationData();
    message.xMm = object.xMm ?? 0;
    message.yMm = object.yMm ?? 0;
    message.zMm = object.zMm ?? 0;
    message.isWeed = object.isWeed ?? false;
    return message;
  },
};

function createBaseGetNextVisualizationDataRequest(): GetNextVisualizationDataRequest {
  return {
    ts: undefined,
    rowId: 0,
    typesToInclude: [],
    thresholdFilters: undefined,
    includeDetailedMetadata: undefined,
  };
}

export const GetNextVisualizationDataRequest: MessageFns<GetNextVisualizationDataRequest> = {
  encode(message: GetNextVisualizationDataRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(10).fork()).join();
    }
    if (message.rowId !== 0) {
      writer.uint32(16).int32(message.rowId);
    }
    writer.uint32(26).fork();
    for (const v of message.typesToInclude) {
      writer.int32(v);
    }
    writer.join();
    if (message.thresholdFilters !== undefined) {
      ThresholdFilters.encode(message.thresholdFilters, writer.uint32(34).fork()).join();
    }
    if (message.includeDetailedMetadata !== undefined) {
      writer.uint32(40).bool(message.includeDetailedMetadata);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextVisualizationDataRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationDataRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.rowId = reader.int32();
          continue;
        case 3:
          if (tag === 24) {
            message.typesToInclude.push(reader.int32() as any);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.typesToInclude.push(reader.int32() as any);
            }

            continue;
          }

          break;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.thresholdFilters = ThresholdFilters.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.includeDetailedMetadata = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationDataRequest {
    return {
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      rowId: isSet(object.rowId) ? globalThis.Number(object.rowId) : 0,
      typesToInclude: globalThis.Array.isArray(object?.typesToInclude)
        ? object.typesToInclude.map((e: any) => visualizationTypeToIncludeFromJSON(e))
        : [],
      thresholdFilters: isSet(object.thresholdFilters) ? ThresholdFilters.fromJSON(object.thresholdFilters) : undefined,
      includeDetailedMetadata: isSet(object.includeDetailedMetadata)
        ? globalThis.Boolean(object.includeDetailedMetadata)
        : undefined,
    };
  },

  toJSON(message: GetNextVisualizationDataRequest): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.rowId !== 0) {
      obj.rowId = Math.round(message.rowId);
    }
    if (message.typesToInclude?.length) {
      obj.typesToInclude = message.typesToInclude.map((e) => visualizationTypeToIncludeToJSON(e));
    }
    if (message.thresholdFilters !== undefined) {
      obj.thresholdFilters = ThresholdFilters.toJSON(message.thresholdFilters);
    }
    if (message.includeDetailedMetadata !== undefined) {
      obj.includeDetailedMetadata = message.includeDetailedMetadata;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationDataRequest>, I>>(base?: I): GetNextVisualizationDataRequest {
    return GetNextVisualizationDataRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationDataRequest>, I>>(
    object: I,
  ): GetNextVisualizationDataRequest {
    const message = createBaseGetNextVisualizationDataRequest();
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.rowId = object.rowId ?? 0;
    message.typesToInclude = object.typesToInclude?.map((e) => e) || [];
    message.thresholdFilters = (object.thresholdFilters !== undefined && object.thresholdFilters !== null)
      ? ThresholdFilters.fromPartial(object.thresholdFilters)
      : undefined;
    message.includeDetailedMetadata = object.includeDetailedMetadata ?? undefined;
    return message;
  },
};

function createBaseGetNextVisualizationDataResponse(): GetNextVisualizationDataResponse {
  return { ts: undefined, data: [], bands: [] };
}

export const GetNextVisualizationDataResponse: MessageFns<GetNextVisualizationDataResponse> = {
  encode(message: GetNextVisualizationDataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(10).fork()).join();
    }
    for (const v of message.data) {
      VisualizationData.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.bands) {
      BandDefinition.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextVisualizationDataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationDataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(VisualizationData.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.bands.push(BandDefinition.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationDataResponse {
    return {
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => VisualizationData.fromJSON(e)) : [],
      bands: globalThis.Array.isArray(object?.bands) ? object.bands.map((e: any) => BandDefinition.fromJSON(e)) : [],
    };
  },

  toJSON(message: GetNextVisualizationDataResponse): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => VisualizationData.toJSON(e));
    }
    if (message.bands?.length) {
      obj.bands = message.bands.map((e) => BandDefinition.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationDataResponse>, I>>(
    base?: I,
  ): GetNextVisualizationDataResponse {
    return GetNextVisualizationDataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationDataResponse>, I>>(
    object: I,
  ): GetNextVisualizationDataResponse {
    const message = createBaseGetNextVisualizationDataResponse();
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.data = object.data?.map((e) => VisualizationData.fromPartial(e)) || [];
    message.bands = object.bands?.map((e) => BandDefinition.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetNextVisualizationData2Response(): GetNextVisualizationData2Response {
  return { data: undefined, ts: undefined };
}

export const GetNextVisualizationData2Response: MessageFns<GetNextVisualizationData2Response> = {
  encode(message: GetNextVisualizationData2Response, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.data !== undefined) {
      DiagnosticsSnapshot.encode(message.data, writer.uint32(10).fork()).join();
    }
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextVisualizationData2Response {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationData2Response();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data = DiagnosticsSnapshot.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationData2Response {
    return {
      data: isSet(object.data) ? DiagnosticsSnapshot.fromJSON(object.data) : undefined,
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
    };
  },

  toJSON(message: GetNextVisualizationData2Response): unknown {
    const obj: any = {};
    if (message.data !== undefined) {
      obj.data = DiagnosticsSnapshot.toJSON(message.data);
    }
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationData2Response>, I>>(
    base?: I,
  ): GetNextVisualizationData2Response {
    return GetNextVisualizationData2Response.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationData2Response>, I>>(
    object: I,
  ): GetNextVisualizationData2Response {
    const message = createBaseGetNextVisualizationData2Response();
    message.data = (object.data !== undefined && object.data !== null)
      ? DiagnosticsSnapshot.fromPartial(object.data)
      : undefined;
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    return message;
  },
};

function createBaseGetDimensionsRequest(): GetDimensionsRequest {
  return { rowId: 0 };
}

export const GetDimensionsRequest: MessageFns<GetDimensionsRequest> = {
  encode(message: GetDimensionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rowId !== 0) {
      writer.uint32(8).int32(message.rowId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetDimensionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDimensionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.rowId = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetDimensionsRequest {
    return { rowId: isSet(object.rowId) ? globalThis.Number(object.rowId) : 0 };
  },

  toJSON(message: GetDimensionsRequest): unknown {
    const obj: any = {};
    if (message.rowId !== 0) {
      obj.rowId = Math.round(message.rowId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetDimensionsRequest>, I>>(base?: I): GetDimensionsRequest {
    return GetDimensionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDimensionsRequest>, I>>(object: I): GetDimensionsRequest {
    const message = createBaseGetDimensionsRequest();
    message.rowId = object.rowId ?? 0;
    return message;
  },
};

function createBaseSetBandingEnabledRequest(): SetBandingEnabledRequest {
  return { enabled: false };
}

export const SetBandingEnabledRequest: MessageFns<SetBandingEnabledRequest> = {
  encode(message: SetBandingEnabledRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.enabled !== false) {
      writer.uint32(8).bool(message.enabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetBandingEnabledRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetBandingEnabledRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.enabled = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetBandingEnabledRequest {
    return { enabled: isSet(object.enabled) ? globalThis.Boolean(object.enabled) : false };
  },

  toJSON(message: SetBandingEnabledRequest): unknown {
    const obj: any = {};
    if (message.enabled !== false) {
      obj.enabled = message.enabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetBandingEnabledRequest>, I>>(base?: I): SetBandingEnabledRequest {
    return SetBandingEnabledRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetBandingEnabledRequest>, I>>(object: I): SetBandingEnabledRequest {
    const message = createBaseSetBandingEnabledRequest();
    message.enabled = object.enabled ?? false;
    return message;
  },
};

function createBaseIsBandingEnabledResponse(): IsBandingEnabledResponse {
  return { enabled: false };
}

export const IsBandingEnabledResponse: MessageFns<IsBandingEnabledResponse> = {
  encode(message: IsBandingEnabledResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.enabled !== false) {
      writer.uint32(8).bool(message.enabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IsBandingEnabledResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIsBandingEnabledResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.enabled = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IsBandingEnabledResponse {
    return { enabled: isSet(object.enabled) ? globalThis.Boolean(object.enabled) : false };
  },

  toJSON(message: IsBandingEnabledResponse): unknown {
    const obj: any = {};
    if (message.enabled !== false) {
      obj.enabled = message.enabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IsBandingEnabledResponse>, I>>(base?: I): IsBandingEnabledResponse {
    return IsBandingEnabledResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IsBandingEnabledResponse>, I>>(object: I): IsBandingEnabledResponse {
    const message = createBaseIsBandingEnabledResponse();
    message.enabled = object.enabled ?? false;
    return message;
  },
};

function createBaseGetVisualizationMetadataResponse(): GetVisualizationMetadataResponse {
  return { cropSafetyRadiusMmPerRow: {} };
}

export const GetVisualizationMetadataResponse: MessageFns<GetVisualizationMetadataResponse> = {
  encode(message: GetVisualizationMetadataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.cropSafetyRadiusMmPerRow).forEach(([key, value]) => {
      GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry.encode(
        { key: key as any, value },
        writer.uint32(10).fork(),
      ).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetVisualizationMetadataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVisualizationMetadataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.cropSafetyRadiusMmPerRow[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVisualizationMetadataResponse {
    return {
      cropSafetyRadiusMmPerRow: isObject(object.cropSafetyRadiusMmPerRow)
        ? Object.entries(object.cropSafetyRadiusMmPerRow).reduce<{ [key: number]: number }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = Number(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: GetVisualizationMetadataResponse): unknown {
    const obj: any = {};
    if (message.cropSafetyRadiusMmPerRow) {
      const entries = Object.entries(message.cropSafetyRadiusMmPerRow);
      if (entries.length > 0) {
        obj.cropSafetyRadiusMmPerRow = {};
        entries.forEach(([k, v]) => {
          obj.cropSafetyRadiusMmPerRow[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVisualizationMetadataResponse>, I>>(
    base?: I,
  ): GetVisualizationMetadataResponse {
    return GetVisualizationMetadataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVisualizationMetadataResponse>, I>>(
    object: I,
  ): GetVisualizationMetadataResponse {
    const message = createBaseGetVisualizationMetadataResponse();
    message.cropSafetyRadiusMmPerRow = Object.entries(object.cropSafetyRadiusMmPerRow ?? {}).reduce<
      { [key: number]: number }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseGetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry(): GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
  return { key: 0, value: 0 };
}

export const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry: MessageFns<
  GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry
> = {
  encode(
    message: GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(21).float(message.value);
    }
    return writer;
  },

  decode(
    input: BinaryReader | Uint8Array,
    length?: number,
  ): GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.value = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry>, I>>(
    base?: I,
  ): GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
    return GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry>, I>>(
    object: I,
  ): GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry {
    const message = createBaseGetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseThresholdFilter(): ThresholdFilter {
  return { weeding: 0, thinning: 0, banding: 0 };
}

export const ThresholdFilter: MessageFns<ThresholdFilter> = {
  encode(message: ThresholdFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.weeding !== 0) {
      writer.uint32(8).int32(message.weeding);
    }
    if (message.thinning !== 0) {
      writer.uint32(16).int32(message.thinning);
    }
    if (message.banding !== 0) {
      writer.uint32(24).int32(message.banding);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ThresholdFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseThresholdFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.weeding = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.thinning = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.banding = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ThresholdFilter {
    return {
      weeding: isSet(object.weeding) ? thresholdStateFromJSON(object.weeding) : 0,
      thinning: isSet(object.thinning) ? thresholdStateFromJSON(object.thinning) : 0,
      banding: isSet(object.banding) ? thresholdStateFromJSON(object.banding) : 0,
    };
  },

  toJSON(message: ThresholdFilter): unknown {
    const obj: any = {};
    if (message.weeding !== 0) {
      obj.weeding = thresholdStateToJSON(message.weeding);
    }
    if (message.thinning !== 0) {
      obj.thinning = thresholdStateToJSON(message.thinning);
    }
    if (message.banding !== 0) {
      obj.banding = thresholdStateToJSON(message.banding);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ThresholdFilter>, I>>(base?: I): ThresholdFilter {
    return ThresholdFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ThresholdFilter>, I>>(object: I): ThresholdFilter {
    const message = createBaseThresholdFilter();
    message.weeding = object.weeding ?? 0;
    message.thinning = object.thinning ?? 0;
    message.banding = object.banding ?? 0;
    return message;
  },
};

function createBaseThresholdFilters(): ThresholdFilters {
  return { crop: undefined, weed: undefined };
}

export const ThresholdFilters: MessageFns<ThresholdFilters> = {
  encode(message: ThresholdFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.crop !== undefined) {
      ThresholdFilter.encode(message.crop, writer.uint32(10).fork()).join();
    }
    if (message.weed !== undefined) {
      ThresholdFilter.encode(message.weed, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ThresholdFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseThresholdFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.crop = ThresholdFilter.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.weed = ThresholdFilter.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ThresholdFilters {
    return {
      crop: isSet(object.crop) ? ThresholdFilter.fromJSON(object.crop) : undefined,
      weed: isSet(object.weed) ? ThresholdFilter.fromJSON(object.weed) : undefined,
    };
  },

  toJSON(message: ThresholdFilters): unknown {
    const obj: any = {};
    if (message.crop !== undefined) {
      obj.crop = ThresholdFilter.toJSON(message.crop);
    }
    if (message.weed !== undefined) {
      obj.weed = ThresholdFilter.toJSON(message.weed);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ThresholdFilters>, I>>(base?: I): ThresholdFilters {
    return ThresholdFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ThresholdFilters>, I>>(object: I): ThresholdFilters {
    const message = createBaseThresholdFilters();
    message.crop = (object.crop !== undefined && object.crop !== null)
      ? ThresholdFilter.fromPartial(object.crop)
      : undefined;
    message.weed = (object.weed !== undefined && object.weed !== null)
      ? ThresholdFilter.fromPartial(object.weed)
      : undefined;
    return message;
  },
};

function createBaseGetNextVisualizationDataForAllRowsRequest(): GetNextVisualizationDataForAllRowsRequest {
  return { ts: undefined, typesToInclude: [], thresholdFilters: undefined, includeDetailedMetadata: undefined };
}

export const GetNextVisualizationDataForAllRowsRequest: MessageFns<GetNextVisualizationDataForAllRowsRequest> = {
  encode(message: GetNextVisualizationDataForAllRowsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.typesToInclude) {
      writer.int32(v);
    }
    writer.join();
    if (message.thresholdFilters !== undefined) {
      ThresholdFilters.encode(message.thresholdFilters, writer.uint32(26).fork()).join();
    }
    if (message.includeDetailedMetadata !== undefined) {
      writer.uint32(32).bool(message.includeDetailedMetadata);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextVisualizationDataForAllRowsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationDataForAllRowsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag === 16) {
            message.typesToInclude.push(reader.int32() as any);

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.typesToInclude.push(reader.int32() as any);
            }

            continue;
          }

          break;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.thresholdFilters = ThresholdFilters.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.includeDetailedMetadata = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationDataForAllRowsRequest {
    return {
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      typesToInclude: globalThis.Array.isArray(object?.typesToInclude)
        ? object.typesToInclude.map((e: any) => visualizationTypeToIncludeFromJSON(e))
        : [],
      thresholdFilters: isSet(object.thresholdFilters) ? ThresholdFilters.fromJSON(object.thresholdFilters) : undefined,
      includeDetailedMetadata: isSet(object.includeDetailedMetadata)
        ? globalThis.Boolean(object.includeDetailedMetadata)
        : undefined,
    };
  },

  toJSON(message: GetNextVisualizationDataForAllRowsRequest): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.typesToInclude?.length) {
      obj.typesToInclude = message.typesToInclude.map((e) => visualizationTypeToIncludeToJSON(e));
    }
    if (message.thresholdFilters !== undefined) {
      obj.thresholdFilters = ThresholdFilters.toJSON(message.thresholdFilters);
    }
    if (message.includeDetailedMetadata !== undefined) {
      obj.includeDetailedMetadata = message.includeDetailedMetadata;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsRequest>, I>>(
    base?: I,
  ): GetNextVisualizationDataForAllRowsRequest {
    return GetNextVisualizationDataForAllRowsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsRequest>, I>>(
    object: I,
  ): GetNextVisualizationDataForAllRowsRequest {
    const message = createBaseGetNextVisualizationDataForAllRowsRequest();
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.typesToInclude = object.typesToInclude?.map((e) => e) || [];
    message.thresholdFilters = (object.thresholdFilters !== undefined && object.thresholdFilters !== null)
      ? ThresholdFilters.fromPartial(object.thresholdFilters)
      : undefined;
    message.includeDetailedMetadata = object.includeDetailedMetadata ?? undefined;
    return message;
  },
};

function createBaseGetNextVisualizationDataForAllRowsResponse(): GetNextVisualizationDataForAllRowsResponse {
  return { dataPerRow: {}, ts: undefined, typesToInclude: [] };
}

export const GetNextVisualizationDataForAllRowsResponse: MessageFns<GetNextVisualizationDataForAllRowsResponse> = {
  encode(message: GetNextVisualizationDataForAllRowsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.dataPerRow).forEach(([key, value]) => {
      GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry.encode(
        { key: key as any, value },
        writer.uint32(10).fork(),
      ).join();
    });
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(18).fork()).join();
    }
    writer.uint32(26).fork();
    for (const v of message.typesToInclude) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextVisualizationDataForAllRowsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationDataForAllRowsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.dataPerRow[entry1.key] = entry1.value;
          }
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag === 24) {
            message.typesToInclude.push(reader.int32() as any);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.typesToInclude.push(reader.int32() as any);
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationDataForAllRowsResponse {
    return {
      dataPerRow: isObject(object.dataPerRow)
        ? Object.entries(object.dataPerRow).reduce<{ [key: number]: DiagnosticsSnapshot }>((acc, [key, value]) => {
          acc[globalThis.Number(key)] = DiagnosticsSnapshot.fromJSON(value);
          return acc;
        }, {})
        : {},
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      typesToInclude: globalThis.Array.isArray(object?.typesToInclude)
        ? object.typesToInclude.map((e: any) => visualizationTypeToIncludeFromJSON(e))
        : [],
    };
  },

  toJSON(message: GetNextVisualizationDataForAllRowsResponse): unknown {
    const obj: any = {};
    if (message.dataPerRow) {
      const entries = Object.entries(message.dataPerRow);
      if (entries.length > 0) {
        obj.dataPerRow = {};
        entries.forEach(([k, v]) => {
          obj.dataPerRow[k] = DiagnosticsSnapshot.toJSON(v);
        });
      }
    }
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.typesToInclude?.length) {
      obj.typesToInclude = message.typesToInclude.map((e) => visualizationTypeToIncludeToJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsResponse>, I>>(
    base?: I,
  ): GetNextVisualizationDataForAllRowsResponse {
    return GetNextVisualizationDataForAllRowsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsResponse>, I>>(
    object: I,
  ): GetNextVisualizationDataForAllRowsResponse {
    const message = createBaseGetNextVisualizationDataForAllRowsResponse();
    message.dataPerRow = Object.entries(object.dataPerRow ?? {}).reduce<{ [key: number]: DiagnosticsSnapshot }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = DiagnosticsSnapshot.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.typesToInclude = object.typesToInclude?.map((e) => e) || [];
    return message;
  },
};

function createBaseGetNextVisualizationDataForAllRowsResponse_DataPerRowEntry(): GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
  return { key: 0, value: undefined };
}

export const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry: MessageFns<
  GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry
> = {
  encode(
    message: GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      DiagnosticsSnapshot.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(
    input: BinaryReader | Uint8Array,
    length?: number,
  ): GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextVisualizationDataForAllRowsResponse_DataPerRowEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = DiagnosticsSnapshot.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? DiagnosticsSnapshot.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry): unknown {
    const obj: any = {};
    if (message.key !== 0) {
      obj.key = Math.round(message.key);
    }
    if (message.value !== undefined) {
      obj.value = DiagnosticsSnapshot.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry>, I>>(
    base?: I,
  ): GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
    return GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry>, I>>(
    object: I,
  ): GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry {
    const message = createBaseGetNextVisualizationDataForAllRowsResponse_DataPerRowEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? DiagnosticsSnapshot.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseGetNextBandingStateResponse(): GetNextBandingStateResponse {
  return { ts: undefined, bandingDefs: [], activeDefUUID: "", isBandingEnabled: false, isDynamicBandingEnabled: false };
}

export const GetNextBandingStateResponse: MessageFns<GetNextBandingStateResponse> = {
  encode(message: GetNextBandingStateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(10).fork()).join();
    }
    for (const v of message.bandingDefs) {
      BandingDef.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.activeDefUUID !== "") {
      writer.uint32(26).string(message.activeDefUUID);
    }
    if (message.isBandingEnabled !== false) {
      writer.uint32(32).bool(message.isBandingEnabled);
    }
    if (message.isDynamicBandingEnabled !== false) {
      writer.uint32(40).bool(message.isDynamicBandingEnabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextBandingStateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextBandingStateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.bandingDefs.push(BandingDef.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.activeDefUUID = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.isBandingEnabled = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.isDynamicBandingEnabled = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextBandingStateResponse {
    return {
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      bandingDefs: globalThis.Array.isArray(object?.bandingDefs)
        ? object.bandingDefs.map((e: any) => BandingDef.fromJSON(e))
        : [],
      activeDefUUID: isSet(object.activeDefUUID) ? globalThis.String(object.activeDefUUID) : "",
      isBandingEnabled: isSet(object.isBandingEnabled) ? globalThis.Boolean(object.isBandingEnabled) : false,
      isDynamicBandingEnabled: isSet(object.isDynamicBandingEnabled)
        ? globalThis.Boolean(object.isDynamicBandingEnabled)
        : false,
    };
  },

  toJSON(message: GetNextBandingStateResponse): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.bandingDefs?.length) {
      obj.bandingDefs = message.bandingDefs.map((e) => BandingDef.toJSON(e));
    }
    if (message.activeDefUUID !== "") {
      obj.activeDefUUID = message.activeDefUUID;
    }
    if (message.isBandingEnabled !== false) {
      obj.isBandingEnabled = message.isBandingEnabled;
    }
    if (message.isDynamicBandingEnabled !== false) {
      obj.isDynamicBandingEnabled = message.isDynamicBandingEnabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextBandingStateResponse>, I>>(base?: I): GetNextBandingStateResponse {
    return GetNextBandingStateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextBandingStateResponse>, I>>(object: I): GetNextBandingStateResponse {
    const message = createBaseGetNextBandingStateResponse();
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.bandingDefs = object.bandingDefs?.map((e) => BandingDef.fromPartial(e)) || [];
    message.activeDefUUID = object.activeDefUUID ?? "";
    message.isBandingEnabled = object.isBandingEnabled ?? false;
    message.isDynamicBandingEnabled = object.isDynamicBandingEnabled ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
