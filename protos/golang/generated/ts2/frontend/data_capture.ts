// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: frontend/data_capture.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "../util/util";

export const protobufPackage = "carbon.frontend.data_capture";

export enum UploadMethod {
  WIRELESS = 0,
  USB = 1,
  UNRECOGNIZED = -1,
}

export function uploadMethodFromJSON(object: any): UploadMethod {
  switch (object) {
    case 0:
    case "WIRELESS":
      return UploadMethod.WIRELESS;
    case 1:
    case "USB":
      return UploadMethod.USB;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UploadMethod.UNRECOGNIZED;
  }
}

export function uploadMethodToJSON(object: UploadMethod): string {
  switch (object) {
    case UploadMethod.WIRELESS:
      return "WIRELESS";
    case UploadMethod.USB:
      return "USB";
    case UploadMethod.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ProcedureStep {
  NEW = 0,
  CAPTURING = 1,
  CAPTURE_PAUSED = 2,
  CAPTURE_COMPLETE = 3,
  UPLOADING_WIRELESS = 4,
  UPLOADING_WIRELESS_PAUSED = 5,
  UPLOADING_USB = 6,
  UPLOADING_USB_PAUSED = 7,
  UPLOADING_COMPLETE = 8,
  UNRECOGNIZED = -1,
}

export function procedureStepFromJSON(object: any): ProcedureStep {
  switch (object) {
    case 0:
    case "NEW":
      return ProcedureStep.NEW;
    case 1:
    case "CAPTURING":
      return ProcedureStep.CAPTURING;
    case 2:
    case "CAPTURE_PAUSED":
      return ProcedureStep.CAPTURE_PAUSED;
    case 3:
    case "CAPTURE_COMPLETE":
      return ProcedureStep.CAPTURE_COMPLETE;
    case 4:
    case "UPLOADING_WIRELESS":
      return ProcedureStep.UPLOADING_WIRELESS;
    case 5:
    case "UPLOADING_WIRELESS_PAUSED":
      return ProcedureStep.UPLOADING_WIRELESS_PAUSED;
    case 6:
    case "UPLOADING_USB":
      return ProcedureStep.UPLOADING_USB;
    case 7:
    case "UPLOADING_USB_PAUSED":
      return ProcedureStep.UPLOADING_USB_PAUSED;
    case 8:
    case "UPLOADING_COMPLETE":
      return ProcedureStep.UPLOADING_COMPLETE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProcedureStep.UNRECOGNIZED;
  }
}

export function procedureStepToJSON(object: ProcedureStep): string {
  switch (object) {
    case ProcedureStep.NEW:
      return "NEW";
    case ProcedureStep.CAPTURING:
      return "CAPTURING";
    case ProcedureStep.CAPTURE_PAUSED:
      return "CAPTURE_PAUSED";
    case ProcedureStep.CAPTURE_COMPLETE:
      return "CAPTURE_COMPLETE";
    case ProcedureStep.UPLOADING_WIRELESS:
      return "UPLOADING_WIRELESS";
    case ProcedureStep.UPLOADING_WIRELESS_PAUSED:
      return "UPLOADING_WIRELESS_PAUSED";
    case ProcedureStep.UPLOADING_USB:
      return "UPLOADING_USB";
    case ProcedureStep.UPLOADING_USB_PAUSED:
      return "UPLOADING_USB_PAUSED";
    case ProcedureStep.UPLOADING_COMPLETE:
      return "UPLOADING_COMPLETE";
    case ProcedureStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DataCaptureRate {
  /** feet per image */
  rate: number;
}

export interface DataCaptureState {
  ts: Timestamp | undefined;
  imagesTaken: number;
  targetImagesTaken: number;
  estimatedCaptureRemainingTimeMs: number;
  imagesUploaded: number;
  targetImagesUploaded: number;
  estimatedUploadRemainingTimeMs: number;
  rate: DataCaptureRate | undefined;
  wirelessUploadAvailable: boolean;
  usbStorageConnected: boolean;
  captureStatus: string;
  uploadStatus: string;
  sessionName: string;
  step: ProcedureStep;
  crop: string;
  errorMessage: string;
  cropId: string;
}

export interface DataCaptureSession {
  name: string;
}

export interface StartDataCaptureRequest {
  name: string;
  /** feet per image */
  rate: number;
  crop: string;
  cropId: string;
  snapCapture: boolean;
}

export interface SnapImagesRequest {
  crop: string;
  cropId: string;
  camId?: string | undefined;
  timestampMs?: number | undefined;
  sessionName: string;
}

export interface Session {
  name: string;
  imagesRemaining: number;
  isUploading: boolean;
  hasCompleted: boolean;
  isCapturing: boolean;
}

export interface AvailableSessionResponse {
  sessions: Session[];
}

export interface SessionName {
  name: string;
}

export interface RegularCaptureStatus {
  uploaded: number;
  budget: number;
  lastUploadTimestamp: number;
}

function createBaseDataCaptureRate(): DataCaptureRate {
  return { rate: 0 };
}

export const DataCaptureRate: MessageFns<DataCaptureRate> = {
  encode(message: DataCaptureRate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rate !== 0) {
      writer.uint32(9).double(message.rate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataCaptureRate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataCaptureRate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.rate = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataCaptureRate {
    return { rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0 };
  },

  toJSON(message: DataCaptureRate): unknown {
    const obj: any = {};
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataCaptureRate>, I>>(base?: I): DataCaptureRate {
    return DataCaptureRate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataCaptureRate>, I>>(object: I): DataCaptureRate {
    const message = createBaseDataCaptureRate();
    message.rate = object.rate ?? 0;
    return message;
  },
};

function createBaseDataCaptureState(): DataCaptureState {
  return {
    ts: undefined,
    imagesTaken: 0,
    targetImagesTaken: 0,
    estimatedCaptureRemainingTimeMs: 0,
    imagesUploaded: 0,
    targetImagesUploaded: 0,
    estimatedUploadRemainingTimeMs: 0,
    rate: undefined,
    wirelessUploadAvailable: false,
    usbStorageConnected: false,
    captureStatus: "",
    uploadStatus: "",
    sessionName: "",
    step: 0,
    crop: "",
    errorMessage: "",
    cropId: "",
  };
}

export const DataCaptureState: MessageFns<DataCaptureState> = {
  encode(message: DataCaptureState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(message.ts, writer.uint32(10).fork()).join();
    }
    if (message.imagesTaken !== 0) {
      writer.uint32(16).uint32(message.imagesTaken);
    }
    if (message.targetImagesTaken !== 0) {
      writer.uint32(24).uint32(message.targetImagesTaken);
    }
    if (message.estimatedCaptureRemainingTimeMs !== 0) {
      writer.uint32(32).uint64(message.estimatedCaptureRemainingTimeMs);
    }
    if (message.imagesUploaded !== 0) {
      writer.uint32(40).uint32(message.imagesUploaded);
    }
    if (message.targetImagesUploaded !== 0) {
      writer.uint32(48).uint32(message.targetImagesUploaded);
    }
    if (message.estimatedUploadRemainingTimeMs !== 0) {
      writer.uint32(56).uint64(message.estimatedUploadRemainingTimeMs);
    }
    if (message.rate !== undefined) {
      DataCaptureRate.encode(message.rate, writer.uint32(66).fork()).join();
    }
    if (message.wirelessUploadAvailable !== false) {
      writer.uint32(72).bool(message.wirelessUploadAvailable);
    }
    if (message.usbStorageConnected !== false) {
      writer.uint32(80).bool(message.usbStorageConnected);
    }
    if (message.captureStatus !== "") {
      writer.uint32(90).string(message.captureStatus);
    }
    if (message.uploadStatus !== "") {
      writer.uint32(98).string(message.uploadStatus);
    }
    if (message.sessionName !== "") {
      writer.uint32(106).string(message.sessionName);
    }
    if (message.step !== 0) {
      writer.uint32(112).int32(message.step);
    }
    if (message.crop !== "") {
      writer.uint32(122).string(message.crop);
    }
    if (message.errorMessage !== "") {
      writer.uint32(130).string(message.errorMessage);
    }
    if (message.cropId !== "") {
      writer.uint32(138).string(message.cropId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataCaptureState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataCaptureState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ts = Timestamp.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.imagesTaken = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.targetImagesTaken = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.estimatedCaptureRemainingTimeMs = longToNumber(reader.uint64());
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.imagesUploaded = reader.uint32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.targetImagesUploaded = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.estimatedUploadRemainingTimeMs = longToNumber(reader.uint64());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.rate = DataCaptureRate.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.wirelessUploadAvailable = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.usbStorageConnected = reader.bool();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.captureStatus = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.uploadStatus = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.sessionName = reader.string();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.crop = reader.string();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.errorMessage = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.cropId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataCaptureState {
    return {
      ts: isSet(object.ts) ? Timestamp.fromJSON(object.ts) : undefined,
      imagesTaken: isSet(object.imagesTaken) ? globalThis.Number(object.imagesTaken) : 0,
      targetImagesTaken: isSet(object.targetImagesTaken) ? globalThis.Number(object.targetImagesTaken) : 0,
      estimatedCaptureRemainingTimeMs: isSet(object.estimatedCaptureRemainingTimeMs)
        ? globalThis.Number(object.estimatedCaptureRemainingTimeMs)
        : 0,
      imagesUploaded: isSet(object.imagesUploaded) ? globalThis.Number(object.imagesUploaded) : 0,
      targetImagesUploaded: isSet(object.targetImagesUploaded) ? globalThis.Number(object.targetImagesUploaded) : 0,
      estimatedUploadRemainingTimeMs: isSet(object.estimatedUploadRemainingTimeMs)
        ? globalThis.Number(object.estimatedUploadRemainingTimeMs)
        : 0,
      rate: isSet(object.rate) ? DataCaptureRate.fromJSON(object.rate) : undefined,
      wirelessUploadAvailable: isSet(object.wirelessUploadAvailable)
        ? globalThis.Boolean(object.wirelessUploadAvailable)
        : false,
      usbStorageConnected: isSet(object.usbStorageConnected) ? globalThis.Boolean(object.usbStorageConnected) : false,
      captureStatus: isSet(object.captureStatus) ? globalThis.String(object.captureStatus) : "",
      uploadStatus: isSet(object.uploadStatus) ? globalThis.String(object.uploadStatus) : "",
      sessionName: isSet(object.sessionName) ? globalThis.String(object.sessionName) : "",
      step: isSet(object.step) ? procedureStepFromJSON(object.step) : 0,
      crop: isSet(object.crop) ? globalThis.String(object.crop) : "",
      errorMessage: isSet(object.errorMessage) ? globalThis.String(object.errorMessage) : "",
      cropId: isSet(object.cropId) ? globalThis.String(object.cropId) : "",
    };
  },

  toJSON(message: DataCaptureState): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = Timestamp.toJSON(message.ts);
    }
    if (message.imagesTaken !== 0) {
      obj.imagesTaken = Math.round(message.imagesTaken);
    }
    if (message.targetImagesTaken !== 0) {
      obj.targetImagesTaken = Math.round(message.targetImagesTaken);
    }
    if (message.estimatedCaptureRemainingTimeMs !== 0) {
      obj.estimatedCaptureRemainingTimeMs = Math.round(message.estimatedCaptureRemainingTimeMs);
    }
    if (message.imagesUploaded !== 0) {
      obj.imagesUploaded = Math.round(message.imagesUploaded);
    }
    if (message.targetImagesUploaded !== 0) {
      obj.targetImagesUploaded = Math.round(message.targetImagesUploaded);
    }
    if (message.estimatedUploadRemainingTimeMs !== 0) {
      obj.estimatedUploadRemainingTimeMs = Math.round(message.estimatedUploadRemainingTimeMs);
    }
    if (message.rate !== undefined) {
      obj.rate = DataCaptureRate.toJSON(message.rate);
    }
    if (message.wirelessUploadAvailable !== false) {
      obj.wirelessUploadAvailable = message.wirelessUploadAvailable;
    }
    if (message.usbStorageConnected !== false) {
      obj.usbStorageConnected = message.usbStorageConnected;
    }
    if (message.captureStatus !== "") {
      obj.captureStatus = message.captureStatus;
    }
    if (message.uploadStatus !== "") {
      obj.uploadStatus = message.uploadStatus;
    }
    if (message.sessionName !== "") {
      obj.sessionName = message.sessionName;
    }
    if (message.step !== 0) {
      obj.step = procedureStepToJSON(message.step);
    }
    if (message.crop !== "") {
      obj.crop = message.crop;
    }
    if (message.errorMessage !== "") {
      obj.errorMessage = message.errorMessage;
    }
    if (message.cropId !== "") {
      obj.cropId = message.cropId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataCaptureState>, I>>(base?: I): DataCaptureState {
    return DataCaptureState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataCaptureState>, I>>(object: I): DataCaptureState {
    const message = createBaseDataCaptureState();
    message.ts = (object.ts !== undefined && object.ts !== null) ? Timestamp.fromPartial(object.ts) : undefined;
    message.imagesTaken = object.imagesTaken ?? 0;
    message.targetImagesTaken = object.targetImagesTaken ?? 0;
    message.estimatedCaptureRemainingTimeMs = object.estimatedCaptureRemainingTimeMs ?? 0;
    message.imagesUploaded = object.imagesUploaded ?? 0;
    message.targetImagesUploaded = object.targetImagesUploaded ?? 0;
    message.estimatedUploadRemainingTimeMs = object.estimatedUploadRemainingTimeMs ?? 0;
    message.rate = (object.rate !== undefined && object.rate !== null)
      ? DataCaptureRate.fromPartial(object.rate)
      : undefined;
    message.wirelessUploadAvailable = object.wirelessUploadAvailable ?? false;
    message.usbStorageConnected = object.usbStorageConnected ?? false;
    message.captureStatus = object.captureStatus ?? "";
    message.uploadStatus = object.uploadStatus ?? "";
    message.sessionName = object.sessionName ?? "";
    message.step = object.step ?? 0;
    message.crop = object.crop ?? "";
    message.errorMessage = object.errorMessage ?? "";
    message.cropId = object.cropId ?? "";
    return message;
  },
};

function createBaseDataCaptureSession(): DataCaptureSession {
  return { name: "" };
}

export const DataCaptureSession: MessageFns<DataCaptureSession> = {
  encode(message: DataCaptureSession, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataCaptureSession {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataCaptureSession();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataCaptureSession {
    return { name: isSet(object.name) ? globalThis.String(object.name) : "" };
  },

  toJSON(message: DataCaptureSession): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataCaptureSession>, I>>(base?: I): DataCaptureSession {
    return DataCaptureSession.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataCaptureSession>, I>>(object: I): DataCaptureSession {
    const message = createBaseDataCaptureSession();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseStartDataCaptureRequest(): StartDataCaptureRequest {
  return { name: "", rate: 0, crop: "", cropId: "", snapCapture: false };
}

export const StartDataCaptureRequest: MessageFns<StartDataCaptureRequest> = {
  encode(message: StartDataCaptureRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.rate !== 0) {
      writer.uint32(17).double(message.rate);
    }
    if (message.crop !== "") {
      writer.uint32(26).string(message.crop);
    }
    if (message.cropId !== "") {
      writer.uint32(34).string(message.cropId);
    }
    if (message.snapCapture !== false) {
      writer.uint32(40).bool(message.snapCapture);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartDataCaptureRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartDataCaptureRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.rate = reader.double();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.crop = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.cropId = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.snapCapture = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StartDataCaptureRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      crop: isSet(object.crop) ? globalThis.String(object.crop) : "",
      cropId: isSet(object.cropId) ? globalThis.String(object.cropId) : "",
      snapCapture: isSet(object.snapCapture) ? globalThis.Boolean(object.snapCapture) : false,
    };
  },

  toJSON(message: StartDataCaptureRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.crop !== "") {
      obj.crop = message.crop;
    }
    if (message.cropId !== "") {
      obj.cropId = message.cropId;
    }
    if (message.snapCapture !== false) {
      obj.snapCapture = message.snapCapture;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StartDataCaptureRequest>, I>>(base?: I): StartDataCaptureRequest {
    return StartDataCaptureRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartDataCaptureRequest>, I>>(object: I): StartDataCaptureRequest {
    const message = createBaseStartDataCaptureRequest();
    message.name = object.name ?? "";
    message.rate = object.rate ?? 0;
    message.crop = object.crop ?? "";
    message.cropId = object.cropId ?? "";
    message.snapCapture = object.snapCapture ?? false;
    return message;
  },
};

function createBaseSnapImagesRequest(): SnapImagesRequest {
  return { crop: "", cropId: "", camId: undefined, timestampMs: undefined, sessionName: "" };
}

export const SnapImagesRequest: MessageFns<SnapImagesRequest> = {
  encode(message: SnapImagesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.crop !== "") {
      writer.uint32(10).string(message.crop);
    }
    if (message.cropId !== "") {
      writer.uint32(18).string(message.cropId);
    }
    if (message.camId !== undefined) {
      writer.uint32(26).string(message.camId);
    }
    if (message.timestampMs !== undefined) {
      writer.uint32(32).int64(message.timestampMs);
    }
    if (message.sessionName !== "") {
      writer.uint32(42).string(message.sessionName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SnapImagesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSnapImagesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.crop = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.cropId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.camId = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.timestampMs = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.sessionName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SnapImagesRequest {
    return {
      crop: isSet(object.crop) ? globalThis.String(object.crop) : "",
      cropId: isSet(object.cropId) ? globalThis.String(object.cropId) : "",
      camId: isSet(object.camId) ? globalThis.String(object.camId) : undefined,
      timestampMs: isSet(object.timestampMs) ? globalThis.Number(object.timestampMs) : undefined,
      sessionName: isSet(object.sessionName) ? globalThis.String(object.sessionName) : "",
    };
  },

  toJSON(message: SnapImagesRequest): unknown {
    const obj: any = {};
    if (message.crop !== "") {
      obj.crop = message.crop;
    }
    if (message.cropId !== "") {
      obj.cropId = message.cropId;
    }
    if (message.camId !== undefined) {
      obj.camId = message.camId;
    }
    if (message.timestampMs !== undefined) {
      obj.timestampMs = Math.round(message.timestampMs);
    }
    if (message.sessionName !== "") {
      obj.sessionName = message.sessionName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SnapImagesRequest>, I>>(base?: I): SnapImagesRequest {
    return SnapImagesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SnapImagesRequest>, I>>(object: I): SnapImagesRequest {
    const message = createBaseSnapImagesRequest();
    message.crop = object.crop ?? "";
    message.cropId = object.cropId ?? "";
    message.camId = object.camId ?? undefined;
    message.timestampMs = object.timestampMs ?? undefined;
    message.sessionName = object.sessionName ?? "";
    return message;
  },
};

function createBaseSession(): Session {
  return { name: "", imagesRemaining: 0, isUploading: false, hasCompleted: false, isCapturing: false };
}

export const Session: MessageFns<Session> = {
  encode(message: Session, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.imagesRemaining !== 0) {
      writer.uint32(16).uint32(message.imagesRemaining);
    }
    if (message.isUploading !== false) {
      writer.uint32(24).bool(message.isUploading);
    }
    if (message.hasCompleted !== false) {
      writer.uint32(32).bool(message.hasCompleted);
    }
    if (message.isCapturing !== false) {
      writer.uint32(40).bool(message.isCapturing);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Session {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSession();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.imagesRemaining = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.isUploading = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.hasCompleted = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.isCapturing = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Session {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      imagesRemaining: isSet(object.imagesRemaining) ? globalThis.Number(object.imagesRemaining) : 0,
      isUploading: isSet(object.isUploading) ? globalThis.Boolean(object.isUploading) : false,
      hasCompleted: isSet(object.hasCompleted) ? globalThis.Boolean(object.hasCompleted) : false,
      isCapturing: isSet(object.isCapturing) ? globalThis.Boolean(object.isCapturing) : false,
    };
  },

  toJSON(message: Session): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.imagesRemaining !== 0) {
      obj.imagesRemaining = Math.round(message.imagesRemaining);
    }
    if (message.isUploading !== false) {
      obj.isUploading = message.isUploading;
    }
    if (message.hasCompleted !== false) {
      obj.hasCompleted = message.hasCompleted;
    }
    if (message.isCapturing !== false) {
      obj.isCapturing = message.isCapturing;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Session>, I>>(base?: I): Session {
    return Session.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Session>, I>>(object: I): Session {
    const message = createBaseSession();
    message.name = object.name ?? "";
    message.imagesRemaining = object.imagesRemaining ?? 0;
    message.isUploading = object.isUploading ?? false;
    message.hasCompleted = object.hasCompleted ?? false;
    message.isCapturing = object.isCapturing ?? false;
    return message;
  },
};

function createBaseAvailableSessionResponse(): AvailableSessionResponse {
  return { sessions: [] };
}

export const AvailableSessionResponse: MessageFns<AvailableSessionResponse> = {
  encode(message: AvailableSessionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.sessions) {
      Session.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AvailableSessionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAvailableSessionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.sessions.push(Session.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AvailableSessionResponse {
    return {
      sessions: globalThis.Array.isArray(object?.sessions) ? object.sessions.map((e: any) => Session.fromJSON(e)) : [],
    };
  },

  toJSON(message: AvailableSessionResponse): unknown {
    const obj: any = {};
    if (message.sessions?.length) {
      obj.sessions = message.sessions.map((e) => Session.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AvailableSessionResponse>, I>>(base?: I): AvailableSessionResponse {
    return AvailableSessionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvailableSessionResponse>, I>>(object: I): AvailableSessionResponse {
    const message = createBaseAvailableSessionResponse();
    message.sessions = object.sessions?.map((e) => Session.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSessionName(): SessionName {
  return { name: "" };
}

export const SessionName: MessageFns<SessionName> = {
  encode(message: SessionName, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SessionName {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSessionName();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SessionName {
    return { name: isSet(object.name) ? globalThis.String(object.name) : "" };
  },

  toJSON(message: SessionName): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SessionName>, I>>(base?: I): SessionName {
    return SessionName.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SessionName>, I>>(object: I): SessionName {
    const message = createBaseSessionName();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseRegularCaptureStatus(): RegularCaptureStatus {
  return { uploaded: 0, budget: 0, lastUploadTimestamp: 0 };
}

export const RegularCaptureStatus: MessageFns<RegularCaptureStatus> = {
  encode(message: RegularCaptureStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uploaded !== 0) {
      writer.uint32(8).uint32(message.uploaded);
    }
    if (message.budget !== 0) {
      writer.uint32(16).uint32(message.budget);
    }
    if (message.lastUploadTimestamp !== 0) {
      writer.uint32(24).int64(message.lastUploadTimestamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RegularCaptureStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegularCaptureStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.uploaded = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.budget = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.lastUploadTimestamp = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RegularCaptureStatus {
    return {
      uploaded: isSet(object.uploaded) ? globalThis.Number(object.uploaded) : 0,
      budget: isSet(object.budget) ? globalThis.Number(object.budget) : 0,
      lastUploadTimestamp: isSet(object.lastUploadTimestamp) ? globalThis.Number(object.lastUploadTimestamp) : 0,
    };
  },

  toJSON(message: RegularCaptureStatus): unknown {
    const obj: any = {};
    if (message.uploaded !== 0) {
      obj.uploaded = Math.round(message.uploaded);
    }
    if (message.budget !== 0) {
      obj.budget = Math.round(message.budget);
    }
    if (message.lastUploadTimestamp !== 0) {
      obj.lastUploadTimestamp = Math.round(message.lastUploadTimestamp);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RegularCaptureStatus>, I>>(base?: I): RegularCaptureStatus {
    return RegularCaptureStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegularCaptureStatus>, I>>(object: I): RegularCaptureStatus {
    const message = createBaseRegularCaptureStatus();
    message.uploaded = object.uploaded ?? 0;
    message.budget = object.budget ?? 0;
    message.lastUploadTimestamp = object.lastUploadTimestamp ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
