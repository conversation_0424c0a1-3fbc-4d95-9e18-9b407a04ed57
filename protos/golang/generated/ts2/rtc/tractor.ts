// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: rtc/tractor.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "carbon.rtc";

export interface RelativePosition {
  xM: number;
  yM: number;
}

export interface TractorDefinition {
  rearAxleCenterPt: RelativePosition | undefined;
  fixedHitchPt: RelativePosition | undefined;
  threePtHitchPt: RelativePosition | undefined;
  nosePt: RelativePosition | undefined;
  maxWidthM: number;
}

function createBaseRelativePosition(): RelativePosition {
  return { xM: 0, yM: 0 };
}

export const RelativePosition: MessageFns<RelativePosition> = {
  encode(message: RelativePosition, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.xM !== 0) {
      writer.uint32(13).float(message.xM);
    }
    if (message.yM !== 0) {
      writer.uint32(21).float(message.yM);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RelativePosition {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRelativePosition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }

          message.xM = reader.float();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.yM = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RelativePosition {
    return {
      xM: isSet(object.xM) ? globalThis.Number(object.xM) : 0,
      yM: isSet(object.yM) ? globalThis.Number(object.yM) : 0,
    };
  },

  toJSON(message: RelativePosition): unknown {
    const obj: any = {};
    if (message.xM !== 0) {
      obj.xM = message.xM;
    }
    if (message.yM !== 0) {
      obj.yM = message.yM;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RelativePosition>, I>>(base?: I): RelativePosition {
    return RelativePosition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RelativePosition>, I>>(object: I): RelativePosition {
    const message = createBaseRelativePosition();
    message.xM = object.xM ?? 0;
    message.yM = object.yM ?? 0;
    return message;
  },
};

function createBaseTractorDefinition(): TractorDefinition {
  return {
    rearAxleCenterPt: undefined,
    fixedHitchPt: undefined,
    threePtHitchPt: undefined,
    nosePt: undefined,
    maxWidthM: 0,
  };
}

export const TractorDefinition: MessageFns<TractorDefinition> = {
  encode(message: TractorDefinition, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rearAxleCenterPt !== undefined) {
      RelativePosition.encode(message.rearAxleCenterPt, writer.uint32(10).fork()).join();
    }
    if (message.fixedHitchPt !== undefined) {
      RelativePosition.encode(message.fixedHitchPt, writer.uint32(18).fork()).join();
    }
    if (message.threePtHitchPt !== undefined) {
      RelativePosition.encode(message.threePtHitchPt, writer.uint32(26).fork()).join();
    }
    if (message.nosePt !== undefined) {
      RelativePosition.encode(message.nosePt, writer.uint32(34).fork()).join();
    }
    if (message.maxWidthM !== 0) {
      writer.uint32(45).float(message.maxWidthM);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TractorDefinition {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTractorDefinition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.rearAxleCenterPt = RelativePosition.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.fixedHitchPt = RelativePosition.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.threePtHitchPt = RelativePosition.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.nosePt = RelativePosition.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.maxWidthM = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TractorDefinition {
    return {
      rearAxleCenterPt: isSet(object.rearAxleCenterPt) ? RelativePosition.fromJSON(object.rearAxleCenterPt) : undefined,
      fixedHitchPt: isSet(object.fixedHitchPt) ? RelativePosition.fromJSON(object.fixedHitchPt) : undefined,
      threePtHitchPt: isSet(object.threePtHitchPt) ? RelativePosition.fromJSON(object.threePtHitchPt) : undefined,
      nosePt: isSet(object.nosePt) ? RelativePosition.fromJSON(object.nosePt) : undefined,
      maxWidthM: isSet(object.maxWidthM) ? globalThis.Number(object.maxWidthM) : 0,
    };
  },

  toJSON(message: TractorDefinition): unknown {
    const obj: any = {};
    if (message.rearAxleCenterPt !== undefined) {
      obj.rearAxleCenterPt = RelativePosition.toJSON(message.rearAxleCenterPt);
    }
    if (message.fixedHitchPt !== undefined) {
      obj.fixedHitchPt = RelativePosition.toJSON(message.fixedHitchPt);
    }
    if (message.threePtHitchPt !== undefined) {
      obj.threePtHitchPt = RelativePosition.toJSON(message.threePtHitchPt);
    }
    if (message.nosePt !== undefined) {
      obj.nosePt = RelativePosition.toJSON(message.nosePt);
    }
    if (message.maxWidthM !== 0) {
      obj.maxWidthM = message.maxWidthM;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TractorDefinition>, I>>(base?: I): TractorDefinition {
    return TractorDefinition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TractorDefinition>, I>>(object: I): TractorDefinition {
    const message = createBaseTractorDefinition();
    message.rearAxleCenterPt = (object.rearAxleCenterPt !== undefined && object.rearAxleCenterPt !== null)
      ? RelativePosition.fromPartial(object.rearAxleCenterPt)
      : undefined;
    message.fixedHitchPt = (object.fixedHitchPt !== undefined && object.fixedHitchPt !== null)
      ? RelativePosition.fromPartial(object.fixedHitchPt)
      : undefined;
    message.threePtHitchPt = (object.threePtHitchPt !== undefined && object.threePtHitchPt !== null)
      ? RelativePosition.fromPartial(object.threePtHitchPt)
      : undefined;
    message.nosePt = (object.nosePt !== undefined && object.nosePt !== null)
      ? RelativePosition.fromPartial(object.nosePt)
      : undefined;
    message.maxWidthM = object.maxWidthM ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
