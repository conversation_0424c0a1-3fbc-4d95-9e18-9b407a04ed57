// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: rtc/path_plan.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "carbon.rtc";

export enum Direction {
  DIRECTION_UNSPECIFIED = 0,
  CW = 1,
  CCW = 2,
  UNRECOGNIZED = -1,
}

export function directionFromJSON(object: any): Direction {
  switch (object) {
    case 0:
    case "DIRECTION_UNSPECIFIED":
      return Direction.DIRECTION_UNSPECIFIED;
    case 1:
    case "CW":
      return Direction.CW;
    case 2:
    case "CCW":
      return Direction.CCW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Direction.UNRECOGNIZED;
  }
}

export function directionToJSON(object: Direction): string {
  switch (object) {
    case Direction.DIRECTION_UNSPECIFIED:
      return "DIRECTION_UNSPECIFIED";
    case Direction.CW:
      return "CW";
    case Direction.CCW:
      return "CCW";
    case Direction.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface PathPlanConfiguration {
  doHeadlandFirst: boolean;
  rowHeadingDeg: number;
  headlandWidthM: number;
  turnDirection: Direction;
  numHeadlandPasses: number;
  combinedTurnRadiusM: number;
}

function createBasePathPlanConfiguration(): PathPlanConfiguration {
  return {
    doHeadlandFirst: false,
    rowHeadingDeg: 0,
    headlandWidthM: 0,
    turnDirection: 0,
    numHeadlandPasses: 0,
    combinedTurnRadiusM: 0,
  };
}

export const PathPlanConfiguration: MessageFns<PathPlanConfiguration> = {
  encode(message: PathPlanConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.doHeadlandFirst !== false) {
      writer.uint32(8).bool(message.doHeadlandFirst);
    }
    if (message.rowHeadingDeg !== 0) {
      writer.uint32(21).float(message.rowHeadingDeg);
    }
    if (message.headlandWidthM !== 0) {
      writer.uint32(29).float(message.headlandWidthM);
    }
    if (message.turnDirection !== 0) {
      writer.uint32(32).int32(message.turnDirection);
    }
    if (message.numHeadlandPasses !== 0) {
      writer.uint32(40).int32(message.numHeadlandPasses);
    }
    if (message.combinedTurnRadiusM !== 0) {
      writer.uint32(53).float(message.combinedTurnRadiusM);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PathPlanConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePathPlanConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.doHeadlandFirst = reader.bool();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.rowHeadingDeg = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.headlandWidthM = reader.float();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.turnDirection = reader.int32() as any;
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.numHeadlandPasses = reader.int32();
          continue;
        case 6:
          if (tag !== 53) {
            break;
          }

          message.combinedTurnRadiusM = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PathPlanConfiguration {
    return {
      doHeadlandFirst: isSet(object.doHeadlandFirst) ? globalThis.Boolean(object.doHeadlandFirst) : false,
      rowHeadingDeg: isSet(object.rowHeadingDeg) ? globalThis.Number(object.rowHeadingDeg) : 0,
      headlandWidthM: isSet(object.headlandWidthM) ? globalThis.Number(object.headlandWidthM) : 0,
      turnDirection: isSet(object.turnDirection) ? directionFromJSON(object.turnDirection) : 0,
      numHeadlandPasses: isSet(object.numHeadlandPasses) ? globalThis.Number(object.numHeadlandPasses) : 0,
      combinedTurnRadiusM: isSet(object.combinedTurnRadiusM) ? globalThis.Number(object.combinedTurnRadiusM) : 0,
    };
  },

  toJSON(message: PathPlanConfiguration): unknown {
    const obj: any = {};
    if (message.doHeadlandFirst !== false) {
      obj.doHeadlandFirst = message.doHeadlandFirst;
    }
    if (message.rowHeadingDeg !== 0) {
      obj.rowHeadingDeg = message.rowHeadingDeg;
    }
    if (message.headlandWidthM !== 0) {
      obj.headlandWidthM = message.headlandWidthM;
    }
    if (message.turnDirection !== 0) {
      obj.turnDirection = directionToJSON(message.turnDirection);
    }
    if (message.numHeadlandPasses !== 0) {
      obj.numHeadlandPasses = Math.round(message.numHeadlandPasses);
    }
    if (message.combinedTurnRadiusM !== 0) {
      obj.combinedTurnRadiusM = message.combinedTurnRadiusM;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PathPlanConfiguration>, I>>(base?: I): PathPlanConfiguration {
    return PathPlanConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PathPlanConfiguration>, I>>(object: I): PathPlanConfiguration {
    const message = createBasePathPlanConfiguration();
    message.doHeadlandFirst = object.doHeadlandFirst ?? false;
    message.rowHeadingDeg = object.rowHeadingDeg ?? 0;
    message.headlandWidthM = object.headlandWidthM ?? 0;
    message.turnDirection = object.turnDirection ?? 0;
    message.numHeadlandPasses = object.numHeadlandPasses ?? 0;
    message.combinedTurnRadiusM = object.combinedTurnRadiusM ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
