// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: rtc/implement.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "carbon.rtc";

export enum HitchType {
  HITCH_TYPE_UNSPECIFIED = 0,
  FIXED = 1,
  THREE_POINT = 2,
  UNRECOGNIZED = -1,
}

export function hitchTypeFromJSON(object: any): HitchType {
  switch (object) {
    case 0:
    case "HITCH_TYPE_UNSPECIFIED":
      return HitchType.HITCH_TYPE_UNSPECIFIED;
    case 1:
    case "FIXED":
      return HitchType.FIXED;
    case 2:
    case "THREE_POINT":
      return HitchType.THREE_POINT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HitchType.UNRECOGNIZED;
  }
}

export function hitchTypeToJSON(object: HitchType): string {
  switch (object) {
    case HitchType.HITCH_TYPE_UNSPECIFIED:
      return "HITCH_TYPE_UNSPECIFIED";
    case HitchType.FIXED:
      return "FIXED";
    case HitchType.THREE_POINT:
      return "THREE_POINT";
    case HitchType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ImplementDefinition {
  hitchType: HitchType;
  widthM: number;
  hitchToStartM: number;
  hitchToEndM: number;
  toolSpeedKmh: number;
  transitionSpeedKmh: number;
  runUpDistanceM: number;
  straightenDistanceM: number;
}

function createBaseImplementDefinition(): ImplementDefinition {
  return {
    hitchType: 0,
    widthM: 0,
    hitchToStartM: 0,
    hitchToEndM: 0,
    toolSpeedKmh: 0,
    transitionSpeedKmh: 0,
    runUpDistanceM: 0,
    straightenDistanceM: 0,
  };
}

export const ImplementDefinition: MessageFns<ImplementDefinition> = {
  encode(message: ImplementDefinition, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hitchType !== 0) {
      writer.uint32(8).int32(message.hitchType);
    }
    if (message.widthM !== 0) {
      writer.uint32(21).float(message.widthM);
    }
    if (message.hitchToStartM !== 0) {
      writer.uint32(29).float(message.hitchToStartM);
    }
    if (message.hitchToEndM !== 0) {
      writer.uint32(37).float(message.hitchToEndM);
    }
    if (message.toolSpeedKmh !== 0) {
      writer.uint32(45).float(message.toolSpeedKmh);
    }
    if (message.transitionSpeedKmh !== 0) {
      writer.uint32(53).float(message.transitionSpeedKmh);
    }
    if (message.runUpDistanceM !== 0) {
      writer.uint32(61).float(message.runUpDistanceM);
    }
    if (message.straightenDistanceM !== 0) {
      writer.uint32(69).float(message.straightenDistanceM);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ImplementDefinition {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseImplementDefinition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.hitchType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.widthM = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.hitchToStartM = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.hitchToEndM = reader.float();
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.toolSpeedKmh = reader.float();
          continue;
        case 6:
          if (tag !== 53) {
            break;
          }

          message.transitionSpeedKmh = reader.float();
          continue;
        case 7:
          if (tag !== 61) {
            break;
          }

          message.runUpDistanceM = reader.float();
          continue;
        case 8:
          if (tag !== 69) {
            break;
          }

          message.straightenDistanceM = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ImplementDefinition {
    return {
      hitchType: isSet(object.hitchType) ? hitchTypeFromJSON(object.hitchType) : 0,
      widthM: isSet(object.widthM) ? globalThis.Number(object.widthM) : 0,
      hitchToStartM: isSet(object.hitchToStartM) ? globalThis.Number(object.hitchToStartM) : 0,
      hitchToEndM: isSet(object.hitchToEndM) ? globalThis.Number(object.hitchToEndM) : 0,
      toolSpeedKmh: isSet(object.toolSpeedKmh) ? globalThis.Number(object.toolSpeedKmh) : 0,
      transitionSpeedKmh: isSet(object.transitionSpeedKmh) ? globalThis.Number(object.transitionSpeedKmh) : 0,
      runUpDistanceM: isSet(object.runUpDistanceM) ? globalThis.Number(object.runUpDistanceM) : 0,
      straightenDistanceM: isSet(object.straightenDistanceM) ? globalThis.Number(object.straightenDistanceM) : 0,
    };
  },

  toJSON(message: ImplementDefinition): unknown {
    const obj: any = {};
    if (message.hitchType !== 0) {
      obj.hitchType = hitchTypeToJSON(message.hitchType);
    }
    if (message.widthM !== 0) {
      obj.widthM = message.widthM;
    }
    if (message.hitchToStartM !== 0) {
      obj.hitchToStartM = message.hitchToStartM;
    }
    if (message.hitchToEndM !== 0) {
      obj.hitchToEndM = message.hitchToEndM;
    }
    if (message.toolSpeedKmh !== 0) {
      obj.toolSpeedKmh = message.toolSpeedKmh;
    }
    if (message.transitionSpeedKmh !== 0) {
      obj.transitionSpeedKmh = message.transitionSpeedKmh;
    }
    if (message.runUpDistanceM !== 0) {
      obj.runUpDistanceM = message.runUpDistanceM;
    }
    if (message.straightenDistanceM !== 0) {
      obj.straightenDistanceM = message.straightenDistanceM;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ImplementDefinition>, I>>(base?: I): ImplementDefinition {
    return ImplementDefinition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImplementDefinition>, I>>(object: I): ImplementDefinition {
    const message = createBaseImplementDefinition();
    message.hitchType = object.hitchType ?? 0;
    message.widthM = object.widthM ?? 0;
    message.hitchToStartM = object.hitchToStartM ?? 0;
    message.hitchToEndM = object.hitchToEndM ?? 0;
    message.toolSpeedKmh = object.toolSpeedKmh ?? 0;
    message.transitionSpeedKmh = object.transitionSpeedKmh ?? 0;
    message.runUpDistanceM = object.runUpDistanceM ?? 0;
    message.straightenDistanceM = object.straightenDistanceM ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
