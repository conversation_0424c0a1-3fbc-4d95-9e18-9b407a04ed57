// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.0
//   protoc               v3.21.12
// source: rtc/jobs.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { AbLine, LineString, Point, PolygonRing } from "../geo/geo";
import { Duration } from "../google/protobuf/duration";
import { Empty } from "../google/protobuf/empty";
import { Struct } from "../google/protobuf/struct";
import { Timestamp } from "../google/protobuf/timestamp";
import { ImplementDefinition } from "./implement";
import { PathPlanConfiguration } from "./path_plan";
import { TractorDefinition } from "./tractor";

export const protobufPackage = "carbon.rtc";

/** Unified State for all objects */
export enum State {
  STATE_UNSPECIFIED = 0,
  PENDING = 1,
  READY = 2,
  IN_PROGRESS = 3,
  COMPLETED = 4,
  CANCELLED = 5,
  PAUSED = 6,
  FAILED = 7,
  ACKNOWLEDGED = 8,
  NEW = 9,
  UNRECOGNIZED = -1,
}

export function stateFromJSON(object: any): State {
  switch (object) {
    case 0:
    case "STATE_UNSPECIFIED":
      return State.STATE_UNSPECIFIED;
    case 1:
    case "PENDING":
      return State.PENDING;
    case 2:
    case "READY":
      return State.READY;
    case 3:
    case "IN_PROGRESS":
      return State.IN_PROGRESS;
    case 4:
    case "COMPLETED":
      return State.COMPLETED;
    case 5:
    case "CANCELLED":
      return State.CANCELLED;
    case 6:
    case "PAUSED":
      return State.PAUSED;
    case 7:
    case "FAILED":
      return State.FAILED;
    case 8:
    case "ACKNOWLEDGED":
      return State.ACKNOWLEDGED;
    case 9:
    case "NEW":
      return State.NEW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return State.UNRECOGNIZED;
  }
}

export function stateToJSON(object: State): string {
  switch (object) {
    case State.STATE_UNSPECIFIED:
      return "STATE_UNSPECIFIED";
    case State.PENDING:
      return "PENDING";
    case State.READY:
      return "READY";
    case State.IN_PROGRESS:
      return "IN_PROGRESS";
    case State.COMPLETED:
      return "COMPLETED";
    case State.CANCELLED:
      return "CANCELLED";
    case State.PAUSED:
      return "PAUSED";
    case State.FAILED:
      return "FAILED";
    case State.ACKNOWLEDGED:
      return "ACKNOWLEDGED";
    case State.NEW:
      return "NEW";
    case State.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum JobType {
  JOB_TYPE_UNSPECIFIED = 0,
  LASER_WEED = 1,
  GROUND_PREP = 2,
  UNRECOGNIZED = -1,
}

export function jobTypeFromJSON(object: any): JobType {
  switch (object) {
    case 0:
    case "JOB_TYPE_UNSPECIFIED":
      return JobType.JOB_TYPE_UNSPECIFIED;
    case 1:
    case "LASER_WEED":
      return JobType.LASER_WEED;
    case 2:
    case "GROUND_PREP":
      return JobType.GROUND_PREP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return JobType.UNRECOGNIZED;
  }
}

export function jobTypeToJSON(object: JobType): string {
  switch (object) {
    case JobType.JOB_TYPE_UNSPECIFIED:
      return "JOB_TYPE_UNSPECIFIED";
    case JobType.LASER_WEED:
      return "LASER_WEED";
    case JobType.GROUND_PREP:
      return "GROUND_PREP";
    case JobType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Objective API */
export interface Objective {
  id: number;
  name: string;
  description: string;
  /** (0-100) */
  progressPercent: number;
  /** for ordering */
  priority: number;
  type: Objective_ObjectiveType;
  /**
   * dynamic/json to define, per objective info
   *  this is nice and flexible so for example laser weeding can be a row (start
   *  -> stop) but also be more.
   */
  data: { [key: string]: any } | undefined;
  assignment: ObjectiveAssignment | undefined;
  jobId: number;
}

export enum Objective_ObjectiveType {
  OBJECTIVE_TYPE_UNSPECIFIED = 0,
  LASER_WEED_ROW = 1,
  GROUND_PREP_SECTION = 2,
  UNRECOGNIZED = -1,
}

export function objective_ObjectiveTypeFromJSON(object: any): Objective_ObjectiveType {
  switch (object) {
    case 0:
    case "OBJECTIVE_TYPE_UNSPECIFIED":
      return Objective_ObjectiveType.OBJECTIVE_TYPE_UNSPECIFIED;
    case 1:
    case "LASER_WEED_ROW":
      return Objective_ObjectiveType.LASER_WEED_ROW;
    case 2:
    case "GROUND_PREP_SECTION":
      return Objective_ObjectiveType.GROUND_PREP_SECTION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Objective_ObjectiveType.UNRECOGNIZED;
  }
}

export function objective_ObjectiveTypeToJSON(object: Objective_ObjectiveType): string {
  switch (object) {
    case Objective_ObjectiveType.OBJECTIVE_TYPE_UNSPECIFIED:
      return "OBJECTIVE_TYPE_UNSPECIFIED";
    case Objective_ObjectiveType.LASER_WEED_ROW:
      return "LASER_WEED_ROW";
    case Objective_ObjectiveType.GROUND_PREP_SECTION:
      return "GROUND_PREP_SECTION";
    case Objective_ObjectiveType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface LaserWeedRowObjectiveData {
  rowNum: number;
  abLine: AbLine | undefined;
}

export interface GroundPrepSectionObjectiveData {
  sectionNum: number;
  boundary: PolygonRing | undefined;
}

export interface ObjectiveList {
  objectives: Objective[];
}

export interface ListObjectivesResponse {
  pageToken: string;
  objectives: Objective[];
}

export interface GetNextActiveObjectiveRequest {
  objectiveId: number;
}

export interface GetNextActiveObjectiveResponse {
  objective: Objective | undefined;
}

/** Objective Assignments API */
export interface ObjectiveAssignment {
  id: number;
  objectiveId: number;
  robotSerial: string;
}

export interface ObjectiveAssignmentList {
  assignments: ObjectiveAssignment[];
}

export interface ListObjectiveAssignmentsResponse {
  pageToken: string;
  assignments: ObjectiveAssignment[];
}

/** Robot Whitelist API */
export interface RobotWhitelistEntry {
  robotSerial: string;
}

export interface RobotWhitelist {
  entries: RobotWhitelistEntry[];
}

/** Task API */
export interface Task {
  id: number;
  name: string;
  startedAt: string | undefined;
  endedAt: string | undefined;
  expectedDuration: Duration | undefined;
  statusInfo: string;
  expectedTractorState: TractorState[];
  state: State;
  priority: number;
  objectiveId: number;
  startLocation: Point | undefined;
  startHeading: number;
  endLocation: Point | undefined;
  endHeading: number;
  manuallyAssisted: boolean;
  progressUpdatedAt: string | undefined;
  progressLocation: Point | undefined;
  progressHeading: number;
  /** core structural task types */
  sequence?: SequenceTask | undefined;
  manual?:
    | ManualTask
    | undefined;
  /** tasks with specific automations available */
  goToAndFace?: GoToAndFaceTask | undefined;
  followPath?: FollowPathTask | undefined;
  tractorState?: SetTractorStateTask | undefined;
  laserWeed?: LaserWeedTask | undefined;
  stopAutonomy?: StopAutonomyTask | undefined;
  goToReversiblePath?: GoToReversiblePathTask | undefined;
  moveCenterPivot?: MoveCenterPivotTask | undefined;
}

/**
 * A `StopAutonomyTask` is to signal completion, allowing the robot to exit
 * autonomy mode and do nothing.
 */
export interface StopAutonomyTask {
}

export interface LaserWeedTask {
  /** start as a path / row */
  path: LineString | undefined;
  pathIsReversible: boolean;
  weedingEnabled: boolean;
  thinningEnabled: boolean;
  tolerances: SpatialPathTolerance | undefined;
}

/**
 * A `SequenceTask` requires completing all of its child tasks, in order, with
 * no overlap in time.
 */
export interface SequenceTask {
  items: Task[];
  atomic: boolean;
}

/**
 * A `ManualTask` can only be completed by a human and will always require an
 * intervention request.
 */
export interface ManualTask {
  instructions: string;
}

/**
 * A `GoToAndFaceTask` requires that the robot move to a particular position
 * and face in a particular direction. For example, this might be used to line
 * up at the start of a row.
 */
export interface GoToAndFaceTask {
  point: Point | undefined;
  heading: number;
}

/**
 * A `MoveCenterPivotTask` requires that the center pivot be moved so that it
 * sits anywhere between the specified min and max headings. This is necessary
 * to ensure that the center pivot does not block the tractor's path in a
 * following task, for example when we are preparing to ground prep a section
 * of a circular field.
 */
export interface MoveCenterPivotTask {
  minHeading: number;
  maxHeading: number;
}

/**
 * A `GoToReversiblePathTask` requires that the robot move to one end of a path,
 * facing the other end of the first path segment. This is useful for lining
 * up at either end of a row (whichever is most convenient), or the start of a
 * more complex path like a ground prep pass.
 */
export interface GoToReversiblePathTask {
  path: LineString | undefined;
  tolerances: SpatialPathTolerance | undefined;
}

export interface FollowPathTask {
  path: LineString | undefined;
  speed: SpeedSetting | undefined;
  stopOnCompletion: boolean;
}

export interface SpeedSetting {
  constantMph?: number | undefined;
  remoteOperatorControlled?: Empty | undefined;
  implementControlled?: Empty | undefined;
}

export interface SetTractorStateTask {
  state: TractorState[];
}

export interface TractorState {
  gear?: TractorState_Gear | undefined;
  hitch?: HitchState | undefined;
}

export enum TractorState_Gear {
  GEAR_UNSPECIFIED = 0,
  PARK = 1,
  REVERSE = 2,
  NEUTRAL = 3,
  FORWARD = 4,
  POWERZERO = 5,
  UNRECOGNIZED = -1,
}

export function tractorState_GearFromJSON(object: any): TractorState_Gear {
  switch (object) {
    case 0:
    case "GEAR_UNSPECIFIED":
      return TractorState_Gear.GEAR_UNSPECIFIED;
    case 1:
    case "PARK":
      return TractorState_Gear.PARK;
    case 2:
    case "REVERSE":
      return TractorState_Gear.REVERSE;
    case 3:
    case "NEUTRAL":
      return TractorState_Gear.NEUTRAL;
    case 4:
    case "FORWARD":
      return TractorState_Gear.FORWARD;
    case 5:
    case "POWERZERO":
      return TractorState_Gear.POWERZERO;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TractorState_Gear.UNRECOGNIZED;
  }
}

export function tractorState_GearToJSON(object: TractorState_Gear): string {
  switch (object) {
    case TractorState_Gear.GEAR_UNSPECIFIED:
      return "GEAR_UNSPECIFIED";
    case TractorState_Gear.PARK:
      return "PARK";
    case TractorState_Gear.REVERSE:
      return "REVERSE";
    case TractorState_Gear.NEUTRAL:
      return "NEUTRAL";
    case TractorState_Gear.FORWARD:
      return "FORWARD";
    case TractorState_Gear.POWERZERO:
      return "POWERZERO";
    case TractorState_Gear.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface HitchState {
  command?:
    | HitchState_HitchCommand
    | undefined;
  /** Target position, between 0.0 (fully lowered) and 1.0 (fully raised). */
  position?: number | undefined;
}

export enum HitchState_HitchCommand {
  HITCH_COMMAND_UNSPECIFIED = 0,
  RAISED = 1,
  LOWERED = 2,
  UNRECOGNIZED = -1,
}

export function hitchState_HitchCommandFromJSON(object: any): HitchState_HitchCommand {
  switch (object) {
    case 0:
    case "HITCH_COMMAND_UNSPECIFIED":
      return HitchState_HitchCommand.HITCH_COMMAND_UNSPECIFIED;
    case 1:
    case "RAISED":
      return HitchState_HitchCommand.RAISED;
    case 2:
    case "LOWERED":
      return HitchState_HitchCommand.LOWERED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HitchState_HitchCommand.UNRECOGNIZED;
  }
}

export function hitchState_HitchCommandToJSON(object: HitchState_HitchCommand): string {
  switch (object) {
    case HitchState_HitchCommand.HITCH_COMMAND_UNSPECIFIED:
      return "HITCH_COMMAND_UNSPECIFIED";
    case HitchState_HitchCommand.RAISED:
      return "RAISED";
    case HitchState_HitchCommand.LOWERED:
      return "LOWERED";
    case HitchState_HitchCommand.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface TaskList {
  tasks: Task[];
}

export interface ListTasksResponse {
  pageToken: string;
  tasks: Task[];
}

/**
 * SpatialPathTolerance is a set of tolerance values used to evaluate geo
 * spatial criteria based on location and/or heading.
 */
export interface SpatialPathTolerance {
  heading: number;
  crosstrack: number;
  distance: number;
  continuousCrosstrack: number;
}

/** Jobs API */
export interface Job {
  id: number;
  name: string;
  startedAt: string | undefined;
  endedAt: string | undefined;
  objectives: Objective[];
  state: State;
  type: JobType;
  workOrderId?:
    | number
    | undefined;
  /** Farm ID per Portal, typically a 12-character alphanumeric string. */
  farmId: string;
  fieldId: string;
  /** Customer ID per Portal, typically a UUID. */
  customerId: string;
  priority: number;
  robotWhitelist: RobotWhitelist | undefined;
}

export interface GroundPrepJobConfiguration {
  pathPlan: PathPlanConfiguration | undefined;
  tractorDefinition: TractorDefinition | undefined;
  implementDefinition: ImplementDefinition | undefined;
  entryPoint?: Point | undefined;
  exitPoint?: Point | undefined;
}

export interface LaserWeedJobConfiguration {
}

export interface CreateJobRequest {
  type: JobType;
  name: string;
  farmId: string;
  fieldId: string;
  customerId: string;
  priority: number;
  generateObjectives: boolean;
  groundPrep?: GroundPrepJobConfiguration | undefined;
  laserWeed?: LaserWeedJobConfiguration | undefined;
}

export interface JobList {
  jobs: Job[];
}

export interface ListJobsResponse {
  pageToken: string;
  jobs: Job[];
}

/** Work Orders APIs */
export interface WorkOrder {
  id: number;
  name: string;
  scheduledAt: string | undefined;
  durationMinutes: number;
  jobs: Job[];
  state: State;
}

export interface WorkOrderList {
  workOrders: WorkOrder[];
}

export interface ListWorkOrdersResponse {
  pageToken: string;
  workOrders: WorkOrder[];
}

/** Intervention APIs */
export interface Intervention {
  id: number;
  taskId: number;
  qualification: string;
  description: string;
  state: State;
  robotSerial: string;
  jobId: number;
  cause: Intervention_InterventionCause;
  priority: number;
  assignment: InterventionAssignment | undefined;
  createdAt: string | undefined;
}

export enum Intervention_InterventionCause {
  INTERVENTION_CAUSE_UNSPECIFIED = 0,
  SENSOR_TRIGGERED = 1,
  SAFETY_DRIVER_ACTION = 2,
  TRACTOR_REQUEST = 3,
  UNRECOGNIZED = -1,
}

export function intervention_InterventionCauseFromJSON(object: any): Intervention_InterventionCause {
  switch (object) {
    case 0:
    case "INTERVENTION_CAUSE_UNSPECIFIED":
      return Intervention_InterventionCause.INTERVENTION_CAUSE_UNSPECIFIED;
    case 1:
    case "SENSOR_TRIGGERED":
      return Intervention_InterventionCause.SENSOR_TRIGGERED;
    case 2:
    case "SAFETY_DRIVER_ACTION":
      return Intervention_InterventionCause.SAFETY_DRIVER_ACTION;
    case 3:
    case "TRACTOR_REQUEST":
      return Intervention_InterventionCause.TRACTOR_REQUEST;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Intervention_InterventionCause.UNRECOGNIZED;
  }
}

export function intervention_InterventionCauseToJSON(object: Intervention_InterventionCause): string {
  switch (object) {
    case Intervention_InterventionCause.INTERVENTION_CAUSE_UNSPECIFIED:
      return "INTERVENTION_CAUSE_UNSPECIFIED";
    case Intervention_InterventionCause.SENSOR_TRIGGERED:
      return "SENSOR_TRIGGERED";
    case Intervention_InterventionCause.SAFETY_DRIVER_ACTION:
      return "SAFETY_DRIVER_ACTION";
    case Intervention_InterventionCause.TRACTOR_REQUEST:
      return "TRACTOR_REQUEST";
    case Intervention_InterventionCause.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface InterventionList {
  intervention: Intervention[];
}

export interface InterventionAssignment {
  userId: string;
  assignedAt: string | undefined;
}

export interface ListInterventionsRequest {
  pageSize: number;
  pageToken: string;
  robotSerials: string;
  assigedUserIds: string;
  assigned: boolean;
  taskIds: string;
  jobIds: string;
  causes: string;
  states: string;
}

export interface ListInterventionsResponse {
  pageToken: string;
  interventions: Intervention[];
}

export interface CreateInterventionRequest {
  intervention: Intervention | undefined;
}

export interface CreateInterventionResponse {
  intervention: Intervention | undefined;
}

export interface GetActiveTaskRequest {
  /** TODO:(smt) what other state should we provide? */
  currentLocation: Point | undefined;
}

export interface GetActiveTaskResponse {
  task: Task | undefined;
}

export interface GetTaskRequest {
  taskId: number;
}

export interface GetTaskResponse {
  task: Task | undefined;
}

export interface UpdateTaskRequest {
  taskId: number;
  state?: State | undefined;
  startedAt?: string | undefined;
  startLocation?: Point | undefined;
  startHeading?: number | undefined;
  endedAt?: string | undefined;
  endLocation?: Point | undefined;
  endHeading?: number | undefined;
  statusInfo?: string | undefined;
  progressUpdatedAt?: string | undefined;
  progressLocation?: Point | undefined;
  progressHeading?: number | undefined;
}

export interface UpdateTaskResponse {
  task: Task | undefined;
}

export interface StartManualTaskRequest {
  startLocation: Point | undefined;
  startHeading?: number | undefined;
}

export interface StopManualTaskRequest {
  endLocation: Point | undefined;
  endHeading?: number | undefined;
}

function createBaseObjective(): Objective {
  return {
    id: 0,
    name: "",
    description: "",
    progressPercent: 0,
    priority: 0,
    type: 0,
    data: undefined,
    assignment: undefined,
    jobId: 0,
  };
}

export const Objective: MessageFns<Objective> = {
  encode(message: Objective, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.progressPercent !== 0) {
      writer.uint32(32).int32(message.progressPercent);
    }
    if (message.priority !== 0) {
      writer.uint32(40).int32(message.priority);
    }
    if (message.type !== 0) {
      writer.uint32(48).int32(message.type);
    }
    if (message.data !== undefined) {
      Struct.encode(Struct.wrap(message.data), writer.uint32(58).fork()).join();
    }
    if (message.assignment !== undefined) {
      ObjectiveAssignment.encode(message.assignment, writer.uint32(66).fork()).join();
    }
    if (message.jobId !== 0) {
      writer.uint32(72).uint64(message.jobId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Objective {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjective();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.progressPercent = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.priority = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.data = Struct.unwrap(Struct.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.assignment = ObjectiveAssignment.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.jobId = longToNumber(reader.uint64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Objective {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      progressPercent: isSet(object.progressPercent) ? globalThis.Number(object.progressPercent) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      type: isSet(object.type) ? objective_ObjectiveTypeFromJSON(object.type) : 0,
      data: isObject(object.data) ? object.data : undefined,
      assignment: isSet(object.assignment) ? ObjectiveAssignment.fromJSON(object.assignment) : undefined,
      jobId: isSet(object.jobId) ? globalThis.Number(object.jobId) : 0,
    };
  },

  toJSON(message: Objective): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.progressPercent !== 0) {
      obj.progressPercent = Math.round(message.progressPercent);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.type !== 0) {
      obj.type = objective_ObjectiveTypeToJSON(message.type);
    }
    if (message.data !== undefined) {
      obj.data = message.data;
    }
    if (message.assignment !== undefined) {
      obj.assignment = ObjectiveAssignment.toJSON(message.assignment);
    }
    if (message.jobId !== 0) {
      obj.jobId = Math.round(message.jobId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Objective>, I>>(base?: I): Objective {
    return Objective.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Objective>, I>>(object: I): Objective {
    const message = createBaseObjective();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.description = object.description ?? "";
    message.progressPercent = object.progressPercent ?? 0;
    message.priority = object.priority ?? 0;
    message.type = object.type ?? 0;
    message.data = object.data ?? undefined;
    message.assignment = (object.assignment !== undefined && object.assignment !== null)
      ? ObjectiveAssignment.fromPartial(object.assignment)
      : undefined;
    message.jobId = object.jobId ?? 0;
    return message;
  },
};

function createBaseLaserWeedRowObjectiveData(): LaserWeedRowObjectiveData {
  return { rowNum: 0, abLine: undefined };
}

export const LaserWeedRowObjectiveData: MessageFns<LaserWeedRowObjectiveData> = {
  encode(message: LaserWeedRowObjectiveData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rowNum !== 0) {
      writer.uint32(8).uint32(message.rowNum);
    }
    if (message.abLine !== undefined) {
      AbLine.encode(message.abLine, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LaserWeedRowObjectiveData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLaserWeedRowObjectiveData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.rowNum = reader.uint32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.abLine = AbLine.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LaserWeedRowObjectiveData {
    return {
      rowNum: isSet(object.rowNum) ? globalThis.Number(object.rowNum) : 0,
      abLine: isSet(object.abLine) ? AbLine.fromJSON(object.abLine) : undefined,
    };
  },

  toJSON(message: LaserWeedRowObjectiveData): unknown {
    const obj: any = {};
    if (message.rowNum !== 0) {
      obj.rowNum = Math.round(message.rowNum);
    }
    if (message.abLine !== undefined) {
      obj.abLine = AbLine.toJSON(message.abLine);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LaserWeedRowObjectiveData>, I>>(base?: I): LaserWeedRowObjectiveData {
    return LaserWeedRowObjectiveData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LaserWeedRowObjectiveData>, I>>(object: I): LaserWeedRowObjectiveData {
    const message = createBaseLaserWeedRowObjectiveData();
    message.rowNum = object.rowNum ?? 0;
    message.abLine = (object.abLine !== undefined && object.abLine !== null)
      ? AbLine.fromPartial(object.abLine)
      : undefined;
    return message;
  },
};

function createBaseGroundPrepSectionObjectiveData(): GroundPrepSectionObjectiveData {
  return { sectionNum: 0, boundary: undefined };
}

export const GroundPrepSectionObjectiveData: MessageFns<GroundPrepSectionObjectiveData> = {
  encode(message: GroundPrepSectionObjectiveData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sectionNum !== 0) {
      writer.uint32(8).uint32(message.sectionNum);
    }
    if (message.boundary !== undefined) {
      PolygonRing.encode(message.boundary, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroundPrepSectionObjectiveData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroundPrepSectionObjectiveData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.sectionNum = reader.uint32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.boundary = PolygonRing.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroundPrepSectionObjectiveData {
    return {
      sectionNum: isSet(object.sectionNum) ? globalThis.Number(object.sectionNum) : 0,
      boundary: isSet(object.boundary) ? PolygonRing.fromJSON(object.boundary) : undefined,
    };
  },

  toJSON(message: GroundPrepSectionObjectiveData): unknown {
    const obj: any = {};
    if (message.sectionNum !== 0) {
      obj.sectionNum = Math.round(message.sectionNum);
    }
    if (message.boundary !== undefined) {
      obj.boundary = PolygonRing.toJSON(message.boundary);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroundPrepSectionObjectiveData>, I>>(base?: I): GroundPrepSectionObjectiveData {
    return GroundPrepSectionObjectiveData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroundPrepSectionObjectiveData>, I>>(
    object: I,
  ): GroundPrepSectionObjectiveData {
    const message = createBaseGroundPrepSectionObjectiveData();
    message.sectionNum = object.sectionNum ?? 0;
    message.boundary = (object.boundary !== undefined && object.boundary !== null)
      ? PolygonRing.fromPartial(object.boundary)
      : undefined;
    return message;
  },
};

function createBaseObjectiveList(): ObjectiveList {
  return { objectives: [] };
}

export const ObjectiveList: MessageFns<ObjectiveList> = {
  encode(message: ObjectiveList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.objectives) {
      Objective.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ObjectiveList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectiveList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.objectives.push(Objective.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ObjectiveList {
    return {
      objectives: globalThis.Array.isArray(object?.objectives)
        ? object.objectives.map((e: any) => Objective.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ObjectiveList): unknown {
    const obj: any = {};
    if (message.objectives?.length) {
      obj.objectives = message.objectives.map((e) => Objective.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ObjectiveList>, I>>(base?: I): ObjectiveList {
    return ObjectiveList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ObjectiveList>, I>>(object: I): ObjectiveList {
    const message = createBaseObjectiveList();
    message.objectives = object.objectives?.map((e) => Objective.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListObjectivesResponse(): ListObjectivesResponse {
  return { pageToken: "", objectives: [] };
}

export const ListObjectivesResponse: MessageFns<ListObjectivesResponse> = {
  encode(message: ListObjectivesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.objectives) {
      Objective.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListObjectivesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListObjectivesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.objectives.push(Objective.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListObjectivesResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      objectives: globalThis.Array.isArray(object?.objectives)
        ? object.objectives.map((e: any) => Objective.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ListObjectivesResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.objectives?.length) {
      obj.objectives = message.objectives.map((e) => Objective.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListObjectivesResponse>, I>>(base?: I): ListObjectivesResponse {
    return ListObjectivesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListObjectivesResponse>, I>>(object: I): ListObjectivesResponse {
    const message = createBaseListObjectivesResponse();
    message.pageToken = object.pageToken ?? "";
    message.objectives = object.objectives?.map((e) => Objective.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetNextActiveObjectiveRequest(): GetNextActiveObjectiveRequest {
  return { objectiveId: 0 };
}

export const GetNextActiveObjectiveRequest: MessageFns<GetNextActiveObjectiveRequest> = {
  encode(message: GetNextActiveObjectiveRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.objectiveId !== 0) {
      writer.uint32(8).uint64(message.objectiveId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextActiveObjectiveRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextActiveObjectiveRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.objectiveId = longToNumber(reader.uint64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextActiveObjectiveRequest {
    return { objectiveId: isSet(object.objectiveId) ? globalThis.Number(object.objectiveId) : 0 };
  },

  toJSON(message: GetNextActiveObjectiveRequest): unknown {
    const obj: any = {};
    if (message.objectiveId !== 0) {
      obj.objectiveId = Math.round(message.objectiveId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextActiveObjectiveRequest>, I>>(base?: I): GetNextActiveObjectiveRequest {
    return GetNextActiveObjectiveRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextActiveObjectiveRequest>, I>>(
    object: I,
  ): GetNextActiveObjectiveRequest {
    const message = createBaseGetNextActiveObjectiveRequest();
    message.objectiveId = object.objectiveId ?? 0;
    return message;
  },
};

function createBaseGetNextActiveObjectiveResponse(): GetNextActiveObjectiveResponse {
  return { objective: undefined };
}

export const GetNextActiveObjectiveResponse: MessageFns<GetNextActiveObjectiveResponse> = {
  encode(message: GetNextActiveObjectiveResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.objective !== undefined) {
      Objective.encode(message.objective, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextActiveObjectiveResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextActiveObjectiveResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2:
          if (tag !== 18) {
            break;
          }

          message.objective = Objective.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextActiveObjectiveResponse {
    return { objective: isSet(object.objective) ? Objective.fromJSON(object.objective) : undefined };
  },

  toJSON(message: GetNextActiveObjectiveResponse): unknown {
    const obj: any = {};
    if (message.objective !== undefined) {
      obj.objective = Objective.toJSON(message.objective);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextActiveObjectiveResponse>, I>>(base?: I): GetNextActiveObjectiveResponse {
    return GetNextActiveObjectiveResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextActiveObjectiveResponse>, I>>(
    object: I,
  ): GetNextActiveObjectiveResponse {
    const message = createBaseGetNextActiveObjectiveResponse();
    message.objective = (object.objective !== undefined && object.objective !== null)
      ? Objective.fromPartial(object.objective)
      : undefined;
    return message;
  },
};

function createBaseObjectiveAssignment(): ObjectiveAssignment {
  return { id: 0, objectiveId: 0, robotSerial: "" };
}

export const ObjectiveAssignment: MessageFns<ObjectiveAssignment> = {
  encode(message: ObjectiveAssignment, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.objectiveId !== 0) {
      writer.uint32(16).uint64(message.objectiveId);
    }
    if (message.robotSerial !== "") {
      writer.uint32(26).string(message.robotSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ObjectiveAssignment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectiveAssignment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.objectiveId = longToNumber(reader.uint64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ObjectiveAssignment {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      objectiveId: isSet(object.objectiveId) ? globalThis.Number(object.objectiveId) : 0,
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
    };
  },

  toJSON(message: ObjectiveAssignment): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.objectiveId !== 0) {
      obj.objectiveId = Math.round(message.objectiveId);
    }
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ObjectiveAssignment>, I>>(base?: I): ObjectiveAssignment {
    return ObjectiveAssignment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ObjectiveAssignment>, I>>(object: I): ObjectiveAssignment {
    const message = createBaseObjectiveAssignment();
    message.id = object.id ?? 0;
    message.objectiveId = object.objectiveId ?? 0;
    message.robotSerial = object.robotSerial ?? "";
    return message;
  },
};

function createBaseObjectiveAssignmentList(): ObjectiveAssignmentList {
  return { assignments: [] };
}

export const ObjectiveAssignmentList: MessageFns<ObjectiveAssignmentList> = {
  encode(message: ObjectiveAssignmentList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.assignments) {
      ObjectiveAssignment.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ObjectiveAssignmentList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectiveAssignmentList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.assignments.push(ObjectiveAssignment.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ObjectiveAssignmentList {
    return {
      assignments: globalThis.Array.isArray(object?.assignments)
        ? object.assignments.map((e: any) => ObjectiveAssignment.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ObjectiveAssignmentList): unknown {
    const obj: any = {};
    if (message.assignments?.length) {
      obj.assignments = message.assignments.map((e) => ObjectiveAssignment.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ObjectiveAssignmentList>, I>>(base?: I): ObjectiveAssignmentList {
    return ObjectiveAssignmentList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ObjectiveAssignmentList>, I>>(object: I): ObjectiveAssignmentList {
    const message = createBaseObjectiveAssignmentList();
    message.assignments = object.assignments?.map((e) => ObjectiveAssignment.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListObjectiveAssignmentsResponse(): ListObjectiveAssignmentsResponse {
  return { pageToken: "", assignments: [] };
}

export const ListObjectiveAssignmentsResponse: MessageFns<ListObjectiveAssignmentsResponse> = {
  encode(message: ListObjectiveAssignmentsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.assignments) {
      ObjectiveAssignment.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListObjectiveAssignmentsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListObjectiveAssignmentsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.assignments.push(ObjectiveAssignment.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListObjectiveAssignmentsResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      assignments: globalThis.Array.isArray(object?.assignments)
        ? object.assignments.map((e: any) => ObjectiveAssignment.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ListObjectiveAssignmentsResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.assignments?.length) {
      obj.assignments = message.assignments.map((e) => ObjectiveAssignment.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListObjectiveAssignmentsResponse>, I>>(
    base?: I,
  ): ListObjectiveAssignmentsResponse {
    return ListObjectiveAssignmentsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListObjectiveAssignmentsResponse>, I>>(
    object: I,
  ): ListObjectiveAssignmentsResponse {
    const message = createBaseListObjectiveAssignmentsResponse();
    message.pageToken = object.pageToken ?? "";
    message.assignments = object.assignments?.map((e) => ObjectiveAssignment.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRobotWhitelistEntry(): RobotWhitelistEntry {
  return { robotSerial: "" };
}

export const RobotWhitelistEntry: MessageFns<RobotWhitelistEntry> = {
  encode(message: RobotWhitelistEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.robotSerial !== "") {
      writer.uint32(10).string(message.robotSerial);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RobotWhitelistEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRobotWhitelistEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RobotWhitelistEntry {
    return { robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "" };
  },

  toJSON(message: RobotWhitelistEntry): unknown {
    const obj: any = {};
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RobotWhitelistEntry>, I>>(base?: I): RobotWhitelistEntry {
    return RobotWhitelistEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RobotWhitelistEntry>, I>>(object: I): RobotWhitelistEntry {
    const message = createBaseRobotWhitelistEntry();
    message.robotSerial = object.robotSerial ?? "";
    return message;
  },
};

function createBaseRobotWhitelist(): RobotWhitelist {
  return { entries: [] };
}

export const RobotWhitelist: MessageFns<RobotWhitelist> = {
  encode(message: RobotWhitelist, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.entries) {
      RobotWhitelistEntry.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RobotWhitelist {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRobotWhitelist();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.entries.push(RobotWhitelistEntry.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RobotWhitelist {
    return {
      entries: globalThis.Array.isArray(object?.entries)
        ? object.entries.map((e: any) => RobotWhitelistEntry.fromJSON(e))
        : [],
    };
  },

  toJSON(message: RobotWhitelist): unknown {
    const obj: any = {};
    if (message.entries?.length) {
      obj.entries = message.entries.map((e) => RobotWhitelistEntry.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RobotWhitelist>, I>>(base?: I): RobotWhitelist {
    return RobotWhitelist.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RobotWhitelist>, I>>(object: I): RobotWhitelist {
    const message = createBaseRobotWhitelist();
    message.entries = object.entries?.map((e) => RobotWhitelistEntry.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTask(): Task {
  return {
    id: 0,
    name: "",
    startedAt: undefined,
    endedAt: undefined,
    expectedDuration: undefined,
    statusInfo: "",
    expectedTractorState: [],
    state: 0,
    priority: 0,
    objectiveId: 0,
    startLocation: undefined,
    startHeading: 0,
    endLocation: undefined,
    endHeading: 0,
    manuallyAssisted: false,
    progressUpdatedAt: undefined,
    progressLocation: undefined,
    progressHeading: 0,
    sequence: undefined,
    manual: undefined,
    goToAndFace: undefined,
    followPath: undefined,
    tractorState: undefined,
    laserWeed: undefined,
    stopAutonomy: undefined,
    goToReversiblePath: undefined,
    moveCenterPivot: undefined,
  };
}

export const Task: MessageFns<Task> = {
  encode(message: Task, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.startedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.startedAt), writer.uint32(26).fork()).join();
    }
    if (message.endedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.endedAt), writer.uint32(34).fork()).join();
    }
    if (message.expectedDuration !== undefined) {
      Duration.encode(message.expectedDuration, writer.uint32(42).fork()).join();
    }
    if (message.statusInfo !== "") {
      writer.uint32(50).string(message.statusInfo);
    }
    for (const v of message.expectedTractorState) {
      TractorState.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.state !== 0) {
      writer.uint32(64).int32(message.state);
    }
    if (message.priority !== 0) {
      writer.uint32(72).int32(message.priority);
    }
    if (message.objectiveId !== 0) {
      writer.uint32(128).uint64(message.objectiveId);
    }
    if (message.startLocation !== undefined) {
      Point.encode(message.startLocation, writer.uint32(146).fork()).join();
    }
    if (message.startHeading !== 0) {
      writer.uint32(153).double(message.startHeading);
    }
    if (message.endLocation !== undefined) {
      Point.encode(message.endLocation, writer.uint32(162).fork()).join();
    }
    if (message.endHeading !== 0) {
      writer.uint32(169).double(message.endHeading);
    }
    if (message.manuallyAssisted !== false) {
      writer.uint32(184).bool(message.manuallyAssisted);
    }
    if (message.progressUpdatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.progressUpdatedAt), writer.uint32(194).fork()).join();
    }
    if (message.progressLocation !== undefined) {
      Point.encode(message.progressLocation, writer.uint32(202).fork()).join();
    }
    if (message.progressHeading !== 0) {
      writer.uint32(209).double(message.progressHeading);
    }
    if (message.sequence !== undefined) {
      SequenceTask.encode(message.sequence, writer.uint32(82).fork()).join();
    }
    if (message.manual !== undefined) {
      ManualTask.encode(message.manual, writer.uint32(90).fork()).join();
    }
    if (message.goToAndFace !== undefined) {
      GoToAndFaceTask.encode(message.goToAndFace, writer.uint32(98).fork()).join();
    }
    if (message.followPath !== undefined) {
      FollowPathTask.encode(message.followPath, writer.uint32(106).fork()).join();
    }
    if (message.tractorState !== undefined) {
      SetTractorStateTask.encode(message.tractorState, writer.uint32(114).fork()).join();
    }
    if (message.laserWeed !== undefined) {
      LaserWeedTask.encode(message.laserWeed, writer.uint32(122).fork()).join();
    }
    if (message.stopAutonomy !== undefined) {
      StopAutonomyTask.encode(message.stopAutonomy, writer.uint32(138).fork()).join();
    }
    if (message.goToReversiblePath !== undefined) {
      GoToReversiblePathTask.encode(message.goToReversiblePath, writer.uint32(178).fork()).join();
    }
    if (message.moveCenterPivot !== undefined) {
      MoveCenterPivotTask.encode(message.moveCenterPivot, writer.uint32(218).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Task {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.startedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.endedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.expectedDuration = Duration.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.statusInfo = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.expectedTractorState.push(TractorState.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.priority = reader.int32();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.objectiveId = longToNumber(reader.uint64());
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.startLocation = Point.decode(reader, reader.uint32());
          continue;
        case 19:
          if (tag !== 153) {
            break;
          }

          message.startHeading = reader.double();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.endLocation = Point.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 169) {
            break;
          }

          message.endHeading = reader.double();
          continue;
        case 23:
          if (tag !== 184) {
            break;
          }

          message.manuallyAssisted = reader.bool();
          continue;
        case 24:
          if (tag !== 194) {
            break;
          }

          message.progressUpdatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.progressLocation = Point.decode(reader, reader.uint32());
          continue;
        case 26:
          if (tag !== 209) {
            break;
          }

          message.progressHeading = reader.double();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.sequence = SequenceTask.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.manual = ManualTask.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.goToAndFace = GoToAndFaceTask.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.followPath = FollowPathTask.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.tractorState = SetTractorStateTask.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.laserWeed = LaserWeedTask.decode(reader, reader.uint32());
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.stopAutonomy = StopAutonomyTask.decode(reader, reader.uint32());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.goToReversiblePath = GoToReversiblePathTask.decode(reader, reader.uint32());
          continue;
        case 27:
          if (tag !== 218) {
            break;
          }

          message.moveCenterPivot = MoveCenterPivotTask.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Task {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      startedAt: isSet(object.startedAt) ? globalThis.String(object.startedAt) : undefined,
      endedAt: isSet(object.endedAt) ? globalThis.String(object.endedAt) : undefined,
      expectedDuration: isSet(object.expectedDuration) ? Duration.fromJSON(object.expectedDuration) : undefined,
      statusInfo: isSet(object.statusInfo) ? globalThis.String(object.statusInfo) : "",
      expectedTractorState: globalThis.Array.isArray(object?.expectedTractorState)
        ? object.expectedTractorState.map((e: any) => TractorState.fromJSON(e))
        : [],
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      objectiveId: isSet(object.objectiveId) ? globalThis.Number(object.objectiveId) : 0,
      startLocation: isSet(object.startLocation) ? Point.fromJSON(object.startLocation) : undefined,
      startHeading: isSet(object.startHeading) ? globalThis.Number(object.startHeading) : 0,
      endLocation: isSet(object.endLocation) ? Point.fromJSON(object.endLocation) : undefined,
      endHeading: isSet(object.endHeading) ? globalThis.Number(object.endHeading) : 0,
      manuallyAssisted: isSet(object.manuallyAssisted) ? globalThis.Boolean(object.manuallyAssisted) : false,
      progressUpdatedAt: isSet(object.progressUpdatedAt) ? globalThis.String(object.progressUpdatedAt) : undefined,
      progressLocation: isSet(object.progressLocation) ? Point.fromJSON(object.progressLocation) : undefined,
      progressHeading: isSet(object.progressHeading) ? globalThis.Number(object.progressHeading) : 0,
      sequence: isSet(object.sequence) ? SequenceTask.fromJSON(object.sequence) : undefined,
      manual: isSet(object.manual) ? ManualTask.fromJSON(object.manual) : undefined,
      goToAndFace: isSet(object.goToAndFace) ? GoToAndFaceTask.fromJSON(object.goToAndFace) : undefined,
      followPath: isSet(object.followPath) ? FollowPathTask.fromJSON(object.followPath) : undefined,
      tractorState: isSet(object.tractorState) ? SetTractorStateTask.fromJSON(object.tractorState) : undefined,
      laserWeed: isSet(object.laserWeed) ? LaserWeedTask.fromJSON(object.laserWeed) : undefined,
      stopAutonomy: isSet(object.stopAutonomy) ? StopAutonomyTask.fromJSON(object.stopAutonomy) : undefined,
      goToReversiblePath: isSet(object.goToReversiblePath)
        ? GoToReversiblePathTask.fromJSON(object.goToReversiblePath)
        : undefined,
      moveCenterPivot: isSet(object.moveCenterPivot) ? MoveCenterPivotTask.fromJSON(object.moveCenterPivot) : undefined,
    };
  },

  toJSON(message: Task): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.startedAt !== undefined) {
      obj.startedAt = message.startedAt;
    }
    if (message.endedAt !== undefined) {
      obj.endedAt = message.endedAt;
    }
    if (message.expectedDuration !== undefined) {
      obj.expectedDuration = Duration.toJSON(message.expectedDuration);
    }
    if (message.statusInfo !== "") {
      obj.statusInfo = message.statusInfo;
    }
    if (message.expectedTractorState?.length) {
      obj.expectedTractorState = message.expectedTractorState.map((e) => TractorState.toJSON(e));
    }
    if (message.state !== 0) {
      obj.state = stateToJSON(message.state);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.objectiveId !== 0) {
      obj.objectiveId = Math.round(message.objectiveId);
    }
    if (message.startLocation !== undefined) {
      obj.startLocation = Point.toJSON(message.startLocation);
    }
    if (message.startHeading !== 0) {
      obj.startHeading = message.startHeading;
    }
    if (message.endLocation !== undefined) {
      obj.endLocation = Point.toJSON(message.endLocation);
    }
    if (message.endHeading !== 0) {
      obj.endHeading = message.endHeading;
    }
    if (message.manuallyAssisted !== false) {
      obj.manuallyAssisted = message.manuallyAssisted;
    }
    if (message.progressUpdatedAt !== undefined) {
      obj.progressUpdatedAt = message.progressUpdatedAt;
    }
    if (message.progressLocation !== undefined) {
      obj.progressLocation = Point.toJSON(message.progressLocation);
    }
    if (message.progressHeading !== 0) {
      obj.progressHeading = message.progressHeading;
    }
    if (message.sequence !== undefined) {
      obj.sequence = SequenceTask.toJSON(message.sequence);
    }
    if (message.manual !== undefined) {
      obj.manual = ManualTask.toJSON(message.manual);
    }
    if (message.goToAndFace !== undefined) {
      obj.goToAndFace = GoToAndFaceTask.toJSON(message.goToAndFace);
    }
    if (message.followPath !== undefined) {
      obj.followPath = FollowPathTask.toJSON(message.followPath);
    }
    if (message.tractorState !== undefined) {
      obj.tractorState = SetTractorStateTask.toJSON(message.tractorState);
    }
    if (message.laserWeed !== undefined) {
      obj.laserWeed = LaserWeedTask.toJSON(message.laserWeed);
    }
    if (message.stopAutonomy !== undefined) {
      obj.stopAutonomy = StopAutonomyTask.toJSON(message.stopAutonomy);
    }
    if (message.goToReversiblePath !== undefined) {
      obj.goToReversiblePath = GoToReversiblePathTask.toJSON(message.goToReversiblePath);
    }
    if (message.moveCenterPivot !== undefined) {
      obj.moveCenterPivot = MoveCenterPivotTask.toJSON(message.moveCenterPivot);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Task>, I>>(base?: I): Task {
    return Task.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Task>, I>>(object: I): Task {
    const message = createBaseTask();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.startedAt = object.startedAt ?? undefined;
    message.endedAt = object.endedAt ?? undefined;
    message.expectedDuration = (object.expectedDuration !== undefined && object.expectedDuration !== null)
      ? Duration.fromPartial(object.expectedDuration)
      : undefined;
    message.statusInfo = object.statusInfo ?? "";
    message.expectedTractorState = object.expectedTractorState?.map((e) => TractorState.fromPartial(e)) || [];
    message.state = object.state ?? 0;
    message.priority = object.priority ?? 0;
    message.objectiveId = object.objectiveId ?? 0;
    message.startLocation = (object.startLocation !== undefined && object.startLocation !== null)
      ? Point.fromPartial(object.startLocation)
      : undefined;
    message.startHeading = object.startHeading ?? 0;
    message.endLocation = (object.endLocation !== undefined && object.endLocation !== null)
      ? Point.fromPartial(object.endLocation)
      : undefined;
    message.endHeading = object.endHeading ?? 0;
    message.manuallyAssisted = object.manuallyAssisted ?? false;
    message.progressUpdatedAt = object.progressUpdatedAt ?? undefined;
    message.progressLocation = (object.progressLocation !== undefined && object.progressLocation !== null)
      ? Point.fromPartial(object.progressLocation)
      : undefined;
    message.progressHeading = object.progressHeading ?? 0;
    message.sequence = (object.sequence !== undefined && object.sequence !== null)
      ? SequenceTask.fromPartial(object.sequence)
      : undefined;
    message.manual = (object.manual !== undefined && object.manual !== null)
      ? ManualTask.fromPartial(object.manual)
      : undefined;
    message.goToAndFace = (object.goToAndFace !== undefined && object.goToAndFace !== null)
      ? GoToAndFaceTask.fromPartial(object.goToAndFace)
      : undefined;
    message.followPath = (object.followPath !== undefined && object.followPath !== null)
      ? FollowPathTask.fromPartial(object.followPath)
      : undefined;
    message.tractorState = (object.tractorState !== undefined && object.tractorState !== null)
      ? SetTractorStateTask.fromPartial(object.tractorState)
      : undefined;
    message.laserWeed = (object.laserWeed !== undefined && object.laserWeed !== null)
      ? LaserWeedTask.fromPartial(object.laserWeed)
      : undefined;
    message.stopAutonomy = (object.stopAutonomy !== undefined && object.stopAutonomy !== null)
      ? StopAutonomyTask.fromPartial(object.stopAutonomy)
      : undefined;
    message.goToReversiblePath = (object.goToReversiblePath !== undefined && object.goToReversiblePath !== null)
      ? GoToReversiblePathTask.fromPartial(object.goToReversiblePath)
      : undefined;
    message.moveCenterPivot = (object.moveCenterPivot !== undefined && object.moveCenterPivot !== null)
      ? MoveCenterPivotTask.fromPartial(object.moveCenterPivot)
      : undefined;
    return message;
  },
};

function createBaseStopAutonomyTask(): StopAutonomyTask {
  return {};
}

export const StopAutonomyTask: MessageFns<StopAutonomyTask> = {
  encode(_: StopAutonomyTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StopAutonomyTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStopAutonomyTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): StopAutonomyTask {
    return {};
  },

  toJSON(_: StopAutonomyTask): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<StopAutonomyTask>, I>>(base?: I): StopAutonomyTask {
    return StopAutonomyTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StopAutonomyTask>, I>>(_: I): StopAutonomyTask {
    const message = createBaseStopAutonomyTask();
    return message;
  },
};

function createBaseLaserWeedTask(): LaserWeedTask {
  return {
    path: undefined,
    pathIsReversible: false,
    weedingEnabled: false,
    thinningEnabled: false,
    tolerances: undefined,
  };
}

export const LaserWeedTask: MessageFns<LaserWeedTask> = {
  encode(message: LaserWeedTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== undefined) {
      LineString.encode(message.path, writer.uint32(10).fork()).join();
    }
    if (message.pathIsReversible !== false) {
      writer.uint32(16).bool(message.pathIsReversible);
    }
    if (message.weedingEnabled !== false) {
      writer.uint32(24).bool(message.weedingEnabled);
    }
    if (message.thinningEnabled !== false) {
      writer.uint32(32).bool(message.thinningEnabled);
    }
    if (message.tolerances !== undefined) {
      SpatialPathTolerance.encode(message.tolerances, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LaserWeedTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLaserWeedTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.path = LineString.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.pathIsReversible = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.weedingEnabled = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.thinningEnabled = reader.bool();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.tolerances = SpatialPathTolerance.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LaserWeedTask {
    return {
      path: isSet(object.path) ? LineString.fromJSON(object.path) : undefined,
      pathIsReversible: isSet(object.pathIsReversible) ? globalThis.Boolean(object.pathIsReversible) : false,
      weedingEnabled: isSet(object.weedingEnabled) ? globalThis.Boolean(object.weedingEnabled) : false,
      thinningEnabled: isSet(object.thinningEnabled) ? globalThis.Boolean(object.thinningEnabled) : false,
      tolerances: isSet(object.tolerances) ? SpatialPathTolerance.fromJSON(object.tolerances) : undefined,
    };
  },

  toJSON(message: LaserWeedTask): unknown {
    const obj: any = {};
    if (message.path !== undefined) {
      obj.path = LineString.toJSON(message.path);
    }
    if (message.pathIsReversible !== false) {
      obj.pathIsReversible = message.pathIsReversible;
    }
    if (message.weedingEnabled !== false) {
      obj.weedingEnabled = message.weedingEnabled;
    }
    if (message.thinningEnabled !== false) {
      obj.thinningEnabled = message.thinningEnabled;
    }
    if (message.tolerances !== undefined) {
      obj.tolerances = SpatialPathTolerance.toJSON(message.tolerances);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LaserWeedTask>, I>>(base?: I): LaserWeedTask {
    return LaserWeedTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LaserWeedTask>, I>>(object: I): LaserWeedTask {
    const message = createBaseLaserWeedTask();
    message.path = (object.path !== undefined && object.path !== null)
      ? LineString.fromPartial(object.path)
      : undefined;
    message.pathIsReversible = object.pathIsReversible ?? false;
    message.weedingEnabled = object.weedingEnabled ?? false;
    message.thinningEnabled = object.thinningEnabled ?? false;
    message.tolerances = (object.tolerances !== undefined && object.tolerances !== null)
      ? SpatialPathTolerance.fromPartial(object.tolerances)
      : undefined;
    return message;
  },
};

function createBaseSequenceTask(): SequenceTask {
  return { items: [], atomic: false };
}

export const SequenceTask: MessageFns<SequenceTask> = {
  encode(message: SequenceTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      Task.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.atomic !== false) {
      writer.uint32(16).bool(message.atomic);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SequenceTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSequenceTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.items.push(Task.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.atomic = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SequenceTask {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => Task.fromJSON(e)) : [],
      atomic: isSet(object.atomic) ? globalThis.Boolean(object.atomic) : false,
    };
  },

  toJSON(message: SequenceTask): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => Task.toJSON(e));
    }
    if (message.atomic !== false) {
      obj.atomic = message.atomic;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SequenceTask>, I>>(base?: I): SequenceTask {
    return SequenceTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SequenceTask>, I>>(object: I): SequenceTask {
    const message = createBaseSequenceTask();
    message.items = object.items?.map((e) => Task.fromPartial(e)) || [];
    message.atomic = object.atomic ?? false;
    return message;
  },
};

function createBaseManualTask(): ManualTask {
  return { instructions: "" };
}

export const ManualTask: MessageFns<ManualTask> = {
  encode(message: ManualTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.instructions !== "") {
      writer.uint32(10).string(message.instructions);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ManualTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseManualTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.instructions = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ManualTask {
    return { instructions: isSet(object.instructions) ? globalThis.String(object.instructions) : "" };
  },

  toJSON(message: ManualTask): unknown {
    const obj: any = {};
    if (message.instructions !== "") {
      obj.instructions = message.instructions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ManualTask>, I>>(base?: I): ManualTask {
    return ManualTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ManualTask>, I>>(object: I): ManualTask {
    const message = createBaseManualTask();
    message.instructions = object.instructions ?? "";
    return message;
  },
};

function createBaseGoToAndFaceTask(): GoToAndFaceTask {
  return { point: undefined, heading: 0 };
}

export const GoToAndFaceTask: MessageFns<GoToAndFaceTask> = {
  encode(message: GoToAndFaceTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== undefined) {
      Point.encode(message.point, writer.uint32(10).fork()).join();
    }
    if (message.heading !== 0) {
      writer.uint32(17).double(message.heading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoToAndFaceTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoToAndFaceTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.point = Point.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.heading = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoToAndFaceTask {
    return {
      point: isSet(object.point) ? Point.fromJSON(object.point) : undefined,
      heading: isSet(object.heading) ? globalThis.Number(object.heading) : 0,
    };
  },

  toJSON(message: GoToAndFaceTask): unknown {
    const obj: any = {};
    if (message.point !== undefined) {
      obj.point = Point.toJSON(message.point);
    }
    if (message.heading !== 0) {
      obj.heading = message.heading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoToAndFaceTask>, I>>(base?: I): GoToAndFaceTask {
    return GoToAndFaceTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoToAndFaceTask>, I>>(object: I): GoToAndFaceTask {
    const message = createBaseGoToAndFaceTask();
    message.point = (object.point !== undefined && object.point !== null) ? Point.fromPartial(object.point) : undefined;
    message.heading = object.heading ?? 0;
    return message;
  },
};

function createBaseMoveCenterPivotTask(): MoveCenterPivotTask {
  return { minHeading: 0, maxHeading: 0 };
}

export const MoveCenterPivotTask: MessageFns<MoveCenterPivotTask> = {
  encode(message: MoveCenterPivotTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.minHeading !== 0) {
      writer.uint32(9).double(message.minHeading);
    }
    if (message.maxHeading !== 0) {
      writer.uint32(17).double(message.maxHeading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MoveCenterPivotTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMoveCenterPivotTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.minHeading = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.maxHeading = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MoveCenterPivotTask {
    return {
      minHeading: isSet(object.minHeading) ? globalThis.Number(object.minHeading) : 0,
      maxHeading: isSet(object.maxHeading) ? globalThis.Number(object.maxHeading) : 0,
    };
  },

  toJSON(message: MoveCenterPivotTask): unknown {
    const obj: any = {};
    if (message.minHeading !== 0) {
      obj.minHeading = message.minHeading;
    }
    if (message.maxHeading !== 0) {
      obj.maxHeading = message.maxHeading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MoveCenterPivotTask>, I>>(base?: I): MoveCenterPivotTask {
    return MoveCenterPivotTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MoveCenterPivotTask>, I>>(object: I): MoveCenterPivotTask {
    const message = createBaseMoveCenterPivotTask();
    message.minHeading = object.minHeading ?? 0;
    message.maxHeading = object.maxHeading ?? 0;
    return message;
  },
};

function createBaseGoToReversiblePathTask(): GoToReversiblePathTask {
  return { path: undefined, tolerances: undefined };
}

export const GoToReversiblePathTask: MessageFns<GoToReversiblePathTask> = {
  encode(message: GoToReversiblePathTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== undefined) {
      LineString.encode(message.path, writer.uint32(10).fork()).join();
    }
    if (message.tolerances !== undefined) {
      SpatialPathTolerance.encode(message.tolerances, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoToReversiblePathTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoToReversiblePathTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.path = LineString.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.tolerances = SpatialPathTolerance.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoToReversiblePathTask {
    return {
      path: isSet(object.path) ? LineString.fromJSON(object.path) : undefined,
      tolerances: isSet(object.tolerances) ? SpatialPathTolerance.fromJSON(object.tolerances) : undefined,
    };
  },

  toJSON(message: GoToReversiblePathTask): unknown {
    const obj: any = {};
    if (message.path !== undefined) {
      obj.path = LineString.toJSON(message.path);
    }
    if (message.tolerances !== undefined) {
      obj.tolerances = SpatialPathTolerance.toJSON(message.tolerances);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoToReversiblePathTask>, I>>(base?: I): GoToReversiblePathTask {
    return GoToReversiblePathTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoToReversiblePathTask>, I>>(object: I): GoToReversiblePathTask {
    const message = createBaseGoToReversiblePathTask();
    message.path = (object.path !== undefined && object.path !== null)
      ? LineString.fromPartial(object.path)
      : undefined;
    message.tolerances = (object.tolerances !== undefined && object.tolerances !== null)
      ? SpatialPathTolerance.fromPartial(object.tolerances)
      : undefined;
    return message;
  },
};

function createBaseFollowPathTask(): FollowPathTask {
  return { path: undefined, speed: undefined, stopOnCompletion: false };
}

export const FollowPathTask: MessageFns<FollowPathTask> = {
  encode(message: FollowPathTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== undefined) {
      LineString.encode(message.path, writer.uint32(10).fork()).join();
    }
    if (message.speed !== undefined) {
      SpeedSetting.encode(message.speed, writer.uint32(18).fork()).join();
    }
    if (message.stopOnCompletion !== false) {
      writer.uint32(24).bool(message.stopOnCompletion);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FollowPathTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFollowPathTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.path = LineString.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.speed = SpeedSetting.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.stopOnCompletion = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FollowPathTask {
    return {
      path: isSet(object.path) ? LineString.fromJSON(object.path) : undefined,
      speed: isSet(object.speed) ? SpeedSetting.fromJSON(object.speed) : undefined,
      stopOnCompletion: isSet(object.stopOnCompletion) ? globalThis.Boolean(object.stopOnCompletion) : false,
    };
  },

  toJSON(message: FollowPathTask): unknown {
    const obj: any = {};
    if (message.path !== undefined) {
      obj.path = LineString.toJSON(message.path);
    }
    if (message.speed !== undefined) {
      obj.speed = SpeedSetting.toJSON(message.speed);
    }
    if (message.stopOnCompletion !== false) {
      obj.stopOnCompletion = message.stopOnCompletion;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FollowPathTask>, I>>(base?: I): FollowPathTask {
    return FollowPathTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowPathTask>, I>>(object: I): FollowPathTask {
    const message = createBaseFollowPathTask();
    message.path = (object.path !== undefined && object.path !== null)
      ? LineString.fromPartial(object.path)
      : undefined;
    message.speed = (object.speed !== undefined && object.speed !== null)
      ? SpeedSetting.fromPartial(object.speed)
      : undefined;
    message.stopOnCompletion = object.stopOnCompletion ?? false;
    return message;
  },
};

function createBaseSpeedSetting(): SpeedSetting {
  return { constantMph: undefined, remoteOperatorControlled: undefined, implementControlled: undefined };
}

export const SpeedSetting: MessageFns<SpeedSetting> = {
  encode(message: SpeedSetting, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.constantMph !== undefined) {
      writer.uint32(9).double(message.constantMph);
    }
    if (message.remoteOperatorControlled !== undefined) {
      Empty.encode(message.remoteOperatorControlled, writer.uint32(18).fork()).join();
    }
    if (message.implementControlled !== undefined) {
      Empty.encode(message.implementControlled, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SpeedSetting {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSpeedSetting();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.constantMph = reader.double();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.remoteOperatorControlled = Empty.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.implementControlled = Empty.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SpeedSetting {
    return {
      constantMph: isSet(object.constantMph) ? globalThis.Number(object.constantMph) : undefined,
      remoteOperatorControlled: isSet(object.remoteOperatorControlled)
        ? Empty.fromJSON(object.remoteOperatorControlled)
        : undefined,
      implementControlled: isSet(object.implementControlled) ? Empty.fromJSON(object.implementControlled) : undefined,
    };
  },

  toJSON(message: SpeedSetting): unknown {
    const obj: any = {};
    if (message.constantMph !== undefined) {
      obj.constantMph = message.constantMph;
    }
    if (message.remoteOperatorControlled !== undefined) {
      obj.remoteOperatorControlled = Empty.toJSON(message.remoteOperatorControlled);
    }
    if (message.implementControlled !== undefined) {
      obj.implementControlled = Empty.toJSON(message.implementControlled);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SpeedSetting>, I>>(base?: I): SpeedSetting {
    return SpeedSetting.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpeedSetting>, I>>(object: I): SpeedSetting {
    const message = createBaseSpeedSetting();
    message.constantMph = object.constantMph ?? undefined;
    message.remoteOperatorControlled =
      (object.remoteOperatorControlled !== undefined && object.remoteOperatorControlled !== null)
        ? Empty.fromPartial(object.remoteOperatorControlled)
        : undefined;
    message.implementControlled = (object.implementControlled !== undefined && object.implementControlled !== null)
      ? Empty.fromPartial(object.implementControlled)
      : undefined;
    return message;
  },
};

function createBaseSetTractorStateTask(): SetTractorStateTask {
  return { state: [] };
}

export const SetTractorStateTask: MessageFns<SetTractorStateTask> = {
  encode(message: SetTractorStateTask, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.state) {
      TractorState.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetTractorStateTask {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetTractorStateTask();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.state.push(TractorState.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetTractorStateTask {
    return {
      state: globalThis.Array.isArray(object?.state) ? object.state.map((e: any) => TractorState.fromJSON(e)) : [],
    };
  },

  toJSON(message: SetTractorStateTask): unknown {
    const obj: any = {};
    if (message.state?.length) {
      obj.state = message.state.map((e) => TractorState.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetTractorStateTask>, I>>(base?: I): SetTractorStateTask {
    return SetTractorStateTask.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetTractorStateTask>, I>>(object: I): SetTractorStateTask {
    const message = createBaseSetTractorStateTask();
    message.state = object.state?.map((e) => TractorState.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTractorState(): TractorState {
  return { gear: undefined, hitch: undefined };
}

export const TractorState: MessageFns<TractorState> = {
  encode(message: TractorState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.gear !== undefined) {
      writer.uint32(8).int32(message.gear);
    }
    if (message.hitch !== undefined) {
      HitchState.encode(message.hitch, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TractorState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTractorState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.gear = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.hitch = HitchState.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TractorState {
    return {
      gear: isSet(object.gear) ? tractorState_GearFromJSON(object.gear) : undefined,
      hitch: isSet(object.hitch) ? HitchState.fromJSON(object.hitch) : undefined,
    };
  },

  toJSON(message: TractorState): unknown {
    const obj: any = {};
    if (message.gear !== undefined) {
      obj.gear = tractorState_GearToJSON(message.gear);
    }
    if (message.hitch !== undefined) {
      obj.hitch = HitchState.toJSON(message.hitch);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TractorState>, I>>(base?: I): TractorState {
    return TractorState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TractorState>, I>>(object: I): TractorState {
    const message = createBaseTractorState();
    message.gear = object.gear ?? undefined;
    message.hitch = (object.hitch !== undefined && object.hitch !== null)
      ? HitchState.fromPartial(object.hitch)
      : undefined;
    return message;
  },
};

function createBaseHitchState(): HitchState {
  return { command: undefined, position: undefined };
}

export const HitchState: MessageFns<HitchState> = {
  encode(message: HitchState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.command !== undefined) {
      writer.uint32(8).int32(message.command);
    }
    if (message.position !== undefined) {
      writer.uint32(17).double(message.position);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HitchState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHitchState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.command = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.position = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HitchState {
    return {
      command: isSet(object.command) ? hitchState_HitchCommandFromJSON(object.command) : undefined,
      position: isSet(object.position) ? globalThis.Number(object.position) : undefined,
    };
  },

  toJSON(message: HitchState): unknown {
    const obj: any = {};
    if (message.command !== undefined) {
      obj.command = hitchState_HitchCommandToJSON(message.command);
    }
    if (message.position !== undefined) {
      obj.position = message.position;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HitchState>, I>>(base?: I): HitchState {
    return HitchState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HitchState>, I>>(object: I): HitchState {
    const message = createBaseHitchState();
    message.command = object.command ?? undefined;
    message.position = object.position ?? undefined;
    return message;
  },
};

function createBaseTaskList(): TaskList {
  return { tasks: [] };
}

export const TaskList: MessageFns<TaskList> = {
  encode(message: TaskList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.tasks) {
      Task.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TaskList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTaskList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.tasks.push(Task.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TaskList {
    return { tasks: globalThis.Array.isArray(object?.tasks) ? object.tasks.map((e: any) => Task.fromJSON(e)) : [] };
  },

  toJSON(message: TaskList): unknown {
    const obj: any = {};
    if (message.tasks?.length) {
      obj.tasks = message.tasks.map((e) => Task.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TaskList>, I>>(base?: I): TaskList {
    return TaskList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskList>, I>>(object: I): TaskList {
    const message = createBaseTaskList();
    message.tasks = object.tasks?.map((e) => Task.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListTasksResponse(): ListTasksResponse {
  return { pageToken: "", tasks: [] };
}

export const ListTasksResponse: MessageFns<ListTasksResponse> = {
  encode(message: ListTasksResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.tasks) {
      Task.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListTasksResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListTasksResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.tasks.push(Task.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListTasksResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      tasks: globalThis.Array.isArray(object?.tasks) ? object.tasks.map((e: any) => Task.fromJSON(e)) : [],
    };
  },

  toJSON(message: ListTasksResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.tasks?.length) {
      obj.tasks = message.tasks.map((e) => Task.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListTasksResponse>, I>>(base?: I): ListTasksResponse {
    return ListTasksResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTasksResponse>, I>>(object: I): ListTasksResponse {
    const message = createBaseListTasksResponse();
    message.pageToken = object.pageToken ?? "";
    message.tasks = object.tasks?.map((e) => Task.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSpatialPathTolerance(): SpatialPathTolerance {
  return { heading: 0, crosstrack: 0, distance: 0, continuousCrosstrack: 0 };
}

export const SpatialPathTolerance: MessageFns<SpatialPathTolerance> = {
  encode(message: SpatialPathTolerance, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heading !== 0) {
      writer.uint32(13).float(message.heading);
    }
    if (message.crosstrack !== 0) {
      writer.uint32(21).float(message.crosstrack);
    }
    if (message.distance !== 0) {
      writer.uint32(29).float(message.distance);
    }
    if (message.continuousCrosstrack !== 0) {
      writer.uint32(37).float(message.continuousCrosstrack);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SpatialPathTolerance {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSpatialPathTolerance();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }

          message.heading = reader.float();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.crosstrack = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.distance = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.continuousCrosstrack = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SpatialPathTolerance {
    return {
      heading: isSet(object.heading) ? globalThis.Number(object.heading) : 0,
      crosstrack: isSet(object.crosstrack) ? globalThis.Number(object.crosstrack) : 0,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : 0,
      continuousCrosstrack: isSet(object.continuousCrosstrack) ? globalThis.Number(object.continuousCrosstrack) : 0,
    };
  },

  toJSON(message: SpatialPathTolerance): unknown {
    const obj: any = {};
    if (message.heading !== 0) {
      obj.heading = message.heading;
    }
    if (message.crosstrack !== 0) {
      obj.crosstrack = message.crosstrack;
    }
    if (message.distance !== 0) {
      obj.distance = message.distance;
    }
    if (message.continuousCrosstrack !== 0) {
      obj.continuousCrosstrack = message.continuousCrosstrack;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SpatialPathTolerance>, I>>(base?: I): SpatialPathTolerance {
    return SpatialPathTolerance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpatialPathTolerance>, I>>(object: I): SpatialPathTolerance {
    const message = createBaseSpatialPathTolerance();
    message.heading = object.heading ?? 0;
    message.crosstrack = object.crosstrack ?? 0;
    message.distance = object.distance ?? 0;
    message.continuousCrosstrack = object.continuousCrosstrack ?? 0;
    return message;
  },
};

function createBaseJob(): Job {
  return {
    id: 0,
    name: "",
    startedAt: undefined,
    endedAt: undefined,
    objectives: [],
    state: 0,
    type: 0,
    workOrderId: undefined,
    farmId: "",
    fieldId: "",
    customerId: "",
    priority: 0,
    robotWhitelist: undefined,
  };
}

export const Job: MessageFns<Job> = {
  encode(message: Job, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.startedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.startedAt), writer.uint32(26).fork()).join();
    }
    if (message.endedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.endedAt), writer.uint32(34).fork()).join();
    }
    for (const v of message.objectives) {
      Objective.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.state !== 0) {
      writer.uint32(48).int32(message.state);
    }
    if (message.type !== 0) {
      writer.uint32(56).int32(message.type);
    }
    if (message.workOrderId !== undefined) {
      writer.uint32(64).uint64(message.workOrderId);
    }
    if (message.farmId !== "") {
      writer.uint32(74).string(message.farmId);
    }
    if (message.fieldId !== "") {
      writer.uint32(82).string(message.fieldId);
    }
    if (message.customerId !== "") {
      writer.uint32(90).string(message.customerId);
    }
    if (message.priority !== 0) {
      writer.uint32(96).int32(message.priority);
    }
    if (message.robotWhitelist !== undefined) {
      RobotWhitelist.encode(message.robotWhitelist, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Job {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJob();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.startedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.endedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.objectives.push(Objective.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.workOrderId = longToNumber(reader.uint64());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.farmId = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.fieldId = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.customerId = reader.string();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.priority = reader.int32();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.robotWhitelist = RobotWhitelist.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Job {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      startedAt: isSet(object.startedAt) ? globalThis.String(object.startedAt) : undefined,
      endedAt: isSet(object.endedAt) ? globalThis.String(object.endedAt) : undefined,
      objectives: globalThis.Array.isArray(object?.objectives)
        ? object.objectives.map((e: any) => Objective.fromJSON(e))
        : [],
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      type: isSet(object.type) ? jobTypeFromJSON(object.type) : 0,
      workOrderId: isSet(object.workOrderId) ? globalThis.Number(object.workOrderId) : undefined,
      farmId: isSet(object.farmId) ? globalThis.String(object.farmId) : "",
      fieldId: isSet(object.fieldId) ? globalThis.String(object.fieldId) : "",
      customerId: isSet(object.customerId) ? globalThis.String(object.customerId) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      robotWhitelist: isSet(object.robotWhitelist) ? RobotWhitelist.fromJSON(object.robotWhitelist) : undefined,
    };
  },

  toJSON(message: Job): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.startedAt !== undefined) {
      obj.startedAt = message.startedAt;
    }
    if (message.endedAt !== undefined) {
      obj.endedAt = message.endedAt;
    }
    if (message.objectives?.length) {
      obj.objectives = message.objectives.map((e) => Objective.toJSON(e));
    }
    if (message.state !== 0) {
      obj.state = stateToJSON(message.state);
    }
    if (message.type !== 0) {
      obj.type = jobTypeToJSON(message.type);
    }
    if (message.workOrderId !== undefined) {
      obj.workOrderId = Math.round(message.workOrderId);
    }
    if (message.farmId !== "") {
      obj.farmId = message.farmId;
    }
    if (message.fieldId !== "") {
      obj.fieldId = message.fieldId;
    }
    if (message.customerId !== "") {
      obj.customerId = message.customerId;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.robotWhitelist !== undefined) {
      obj.robotWhitelist = RobotWhitelist.toJSON(message.robotWhitelist);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Job>, I>>(base?: I): Job {
    return Job.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Job>, I>>(object: I): Job {
    const message = createBaseJob();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.startedAt = object.startedAt ?? undefined;
    message.endedAt = object.endedAt ?? undefined;
    message.objectives = object.objectives?.map((e) => Objective.fromPartial(e)) || [];
    message.state = object.state ?? 0;
    message.type = object.type ?? 0;
    message.workOrderId = object.workOrderId ?? undefined;
    message.farmId = object.farmId ?? "";
    message.fieldId = object.fieldId ?? "";
    message.customerId = object.customerId ?? "";
    message.priority = object.priority ?? 0;
    message.robotWhitelist = (object.robotWhitelist !== undefined && object.robotWhitelist !== null)
      ? RobotWhitelist.fromPartial(object.robotWhitelist)
      : undefined;
    return message;
  },
};

function createBaseGroundPrepJobConfiguration(): GroundPrepJobConfiguration {
  return {
    pathPlan: undefined,
    tractorDefinition: undefined,
    implementDefinition: undefined,
    entryPoint: undefined,
    exitPoint: undefined,
  };
}

export const GroundPrepJobConfiguration: MessageFns<GroundPrepJobConfiguration> = {
  encode(message: GroundPrepJobConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pathPlan !== undefined) {
      PathPlanConfiguration.encode(message.pathPlan, writer.uint32(10).fork()).join();
    }
    if (message.tractorDefinition !== undefined) {
      TractorDefinition.encode(message.tractorDefinition, writer.uint32(18).fork()).join();
    }
    if (message.implementDefinition !== undefined) {
      ImplementDefinition.encode(message.implementDefinition, writer.uint32(26).fork()).join();
    }
    if (message.entryPoint !== undefined) {
      Point.encode(message.entryPoint, writer.uint32(34).fork()).join();
    }
    if (message.exitPoint !== undefined) {
      Point.encode(message.exitPoint, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GroundPrepJobConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroundPrepJobConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pathPlan = PathPlanConfiguration.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.tractorDefinition = TractorDefinition.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.implementDefinition = ImplementDefinition.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.entryPoint = Point.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.exitPoint = Point.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GroundPrepJobConfiguration {
    return {
      pathPlan: isSet(object.pathPlan) ? PathPlanConfiguration.fromJSON(object.pathPlan) : undefined,
      tractorDefinition: isSet(object.tractorDefinition)
        ? TractorDefinition.fromJSON(object.tractorDefinition)
        : undefined,
      implementDefinition: isSet(object.implementDefinition)
        ? ImplementDefinition.fromJSON(object.implementDefinition)
        : undefined,
      entryPoint: isSet(object.entryPoint) ? Point.fromJSON(object.entryPoint) : undefined,
      exitPoint: isSet(object.exitPoint) ? Point.fromJSON(object.exitPoint) : undefined,
    };
  },

  toJSON(message: GroundPrepJobConfiguration): unknown {
    const obj: any = {};
    if (message.pathPlan !== undefined) {
      obj.pathPlan = PathPlanConfiguration.toJSON(message.pathPlan);
    }
    if (message.tractorDefinition !== undefined) {
      obj.tractorDefinition = TractorDefinition.toJSON(message.tractorDefinition);
    }
    if (message.implementDefinition !== undefined) {
      obj.implementDefinition = ImplementDefinition.toJSON(message.implementDefinition);
    }
    if (message.entryPoint !== undefined) {
      obj.entryPoint = Point.toJSON(message.entryPoint);
    }
    if (message.exitPoint !== undefined) {
      obj.exitPoint = Point.toJSON(message.exitPoint);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GroundPrepJobConfiguration>, I>>(base?: I): GroundPrepJobConfiguration {
    return GroundPrepJobConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GroundPrepJobConfiguration>, I>>(object: I): GroundPrepJobConfiguration {
    const message = createBaseGroundPrepJobConfiguration();
    message.pathPlan = (object.pathPlan !== undefined && object.pathPlan !== null)
      ? PathPlanConfiguration.fromPartial(object.pathPlan)
      : undefined;
    message.tractorDefinition = (object.tractorDefinition !== undefined && object.tractorDefinition !== null)
      ? TractorDefinition.fromPartial(object.tractorDefinition)
      : undefined;
    message.implementDefinition = (object.implementDefinition !== undefined && object.implementDefinition !== null)
      ? ImplementDefinition.fromPartial(object.implementDefinition)
      : undefined;
    message.entryPoint = (object.entryPoint !== undefined && object.entryPoint !== null)
      ? Point.fromPartial(object.entryPoint)
      : undefined;
    message.exitPoint = (object.exitPoint !== undefined && object.exitPoint !== null)
      ? Point.fromPartial(object.exitPoint)
      : undefined;
    return message;
  },
};

function createBaseLaserWeedJobConfiguration(): LaserWeedJobConfiguration {
  return {};
}

export const LaserWeedJobConfiguration: MessageFns<LaserWeedJobConfiguration> = {
  encode(_: LaserWeedJobConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LaserWeedJobConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLaserWeedJobConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): LaserWeedJobConfiguration {
    return {};
  },

  toJSON(_: LaserWeedJobConfiguration): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<LaserWeedJobConfiguration>, I>>(base?: I): LaserWeedJobConfiguration {
    return LaserWeedJobConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LaserWeedJobConfiguration>, I>>(_: I): LaserWeedJobConfiguration {
    const message = createBaseLaserWeedJobConfiguration();
    return message;
  },
};

function createBaseCreateJobRequest(): CreateJobRequest {
  return {
    type: 0,
    name: "",
    farmId: "",
    fieldId: "",
    customerId: "",
    priority: 0,
    generateObjectives: false,
    groundPrep: undefined,
    laserWeed: undefined,
  };
}

export const CreateJobRequest: MessageFns<CreateJobRequest> = {
  encode(message: CreateJobRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.farmId !== "") {
      writer.uint32(34).string(message.farmId);
    }
    if (message.fieldId !== "") {
      writer.uint32(42).string(message.fieldId);
    }
    if (message.customerId !== "") {
      writer.uint32(50).string(message.customerId);
    }
    if (message.priority !== 0) {
      writer.uint32(56).int32(message.priority);
    }
    if (message.generateObjectives !== false) {
      writer.uint32(64).bool(message.generateObjectives);
    }
    if (message.groundPrep !== undefined) {
      GroundPrepJobConfiguration.encode(message.groundPrep, writer.uint32(74).fork()).join();
    }
    if (message.laserWeed !== undefined) {
      LaserWeedJobConfiguration.encode(message.laserWeed, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateJobRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateJobRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.farmId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.fieldId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.customerId = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.priority = reader.int32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.generateObjectives = reader.bool();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.groundPrep = GroundPrepJobConfiguration.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.laserWeed = LaserWeedJobConfiguration.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateJobRequest {
    return {
      type: isSet(object.type) ? jobTypeFromJSON(object.type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      farmId: isSet(object.farmId) ? globalThis.String(object.farmId) : "",
      fieldId: isSet(object.fieldId) ? globalThis.String(object.fieldId) : "",
      customerId: isSet(object.customerId) ? globalThis.String(object.customerId) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      generateObjectives: isSet(object.generateObjectives) ? globalThis.Boolean(object.generateObjectives) : false,
      groundPrep: isSet(object.groundPrep) ? GroundPrepJobConfiguration.fromJSON(object.groundPrep) : undefined,
      laserWeed: isSet(object.laserWeed) ? LaserWeedJobConfiguration.fromJSON(object.laserWeed) : undefined,
    };
  },

  toJSON(message: CreateJobRequest): unknown {
    const obj: any = {};
    if (message.type !== 0) {
      obj.type = jobTypeToJSON(message.type);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.farmId !== "") {
      obj.farmId = message.farmId;
    }
    if (message.fieldId !== "") {
      obj.fieldId = message.fieldId;
    }
    if (message.customerId !== "") {
      obj.customerId = message.customerId;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.generateObjectives !== false) {
      obj.generateObjectives = message.generateObjectives;
    }
    if (message.groundPrep !== undefined) {
      obj.groundPrep = GroundPrepJobConfiguration.toJSON(message.groundPrep);
    }
    if (message.laserWeed !== undefined) {
      obj.laserWeed = LaserWeedJobConfiguration.toJSON(message.laserWeed);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateJobRequest>, I>>(base?: I): CreateJobRequest {
    return CreateJobRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateJobRequest>, I>>(object: I): CreateJobRequest {
    const message = createBaseCreateJobRequest();
    message.type = object.type ?? 0;
    message.name = object.name ?? "";
    message.farmId = object.farmId ?? "";
    message.fieldId = object.fieldId ?? "";
    message.customerId = object.customerId ?? "";
    message.priority = object.priority ?? 0;
    message.generateObjectives = object.generateObjectives ?? false;
    message.groundPrep = (object.groundPrep !== undefined && object.groundPrep !== null)
      ? GroundPrepJobConfiguration.fromPartial(object.groundPrep)
      : undefined;
    message.laserWeed = (object.laserWeed !== undefined && object.laserWeed !== null)
      ? LaserWeedJobConfiguration.fromPartial(object.laserWeed)
      : undefined;
    return message;
  },
};

function createBaseJobList(): JobList {
  return { jobs: [] };
}

export const JobList: MessageFns<JobList> = {
  encode(message: JobList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.jobs) {
      Job.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JobList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJobList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.jobs.push(Job.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): JobList {
    return { jobs: globalThis.Array.isArray(object?.jobs) ? object.jobs.map((e: any) => Job.fromJSON(e)) : [] };
  },

  toJSON(message: JobList): unknown {
    const obj: any = {};
    if (message.jobs?.length) {
      obj.jobs = message.jobs.map((e) => Job.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<JobList>, I>>(base?: I): JobList {
    return JobList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JobList>, I>>(object: I): JobList {
    const message = createBaseJobList();
    message.jobs = object.jobs?.map((e) => Job.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListJobsResponse(): ListJobsResponse {
  return { pageToken: "", jobs: [] };
}

export const ListJobsResponse: MessageFns<ListJobsResponse> = {
  encode(message: ListJobsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.jobs) {
      Job.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListJobsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListJobsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.jobs.push(Job.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListJobsResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      jobs: globalThis.Array.isArray(object?.jobs) ? object.jobs.map((e: any) => Job.fromJSON(e)) : [],
    };
  },

  toJSON(message: ListJobsResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.jobs?.length) {
      obj.jobs = message.jobs.map((e) => Job.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListJobsResponse>, I>>(base?: I): ListJobsResponse {
    return ListJobsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListJobsResponse>, I>>(object: I): ListJobsResponse {
    const message = createBaseListJobsResponse();
    message.pageToken = object.pageToken ?? "";
    message.jobs = object.jobs?.map((e) => Job.fromPartial(e)) || [];
    return message;
  },
};

function createBaseWorkOrder(): WorkOrder {
  return { id: 0, name: "", scheduledAt: undefined, durationMinutes: 0, jobs: [], state: 0 };
}

export const WorkOrder: MessageFns<WorkOrder> = {
  encode(message: WorkOrder, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.scheduledAt !== undefined) {
      Timestamp.encode(toTimestamp(message.scheduledAt), writer.uint32(26).fork()).join();
    }
    if (message.durationMinutes !== 0) {
      writer.uint32(32).int32(message.durationMinutes);
    }
    for (const v of message.jobs) {
      Job.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.state !== 0) {
      writer.uint32(48).int32(message.state);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WorkOrder {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWorkOrder();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.scheduledAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.durationMinutes = reader.int32();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.jobs.push(Job.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WorkOrder {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      scheduledAt: isSet(object.scheduledAt) ? globalThis.String(object.scheduledAt) : undefined,
      durationMinutes: isSet(object.durationMinutes) ? globalThis.Number(object.durationMinutes) : 0,
      jobs: globalThis.Array.isArray(object?.jobs) ? object.jobs.map((e: any) => Job.fromJSON(e)) : [],
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
    };
  },

  toJSON(message: WorkOrder): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.scheduledAt !== undefined) {
      obj.scheduledAt = message.scheduledAt;
    }
    if (message.durationMinutes !== 0) {
      obj.durationMinutes = Math.round(message.durationMinutes);
    }
    if (message.jobs?.length) {
      obj.jobs = message.jobs.map((e) => Job.toJSON(e));
    }
    if (message.state !== 0) {
      obj.state = stateToJSON(message.state);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WorkOrder>, I>>(base?: I): WorkOrder {
    return WorkOrder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WorkOrder>, I>>(object: I): WorkOrder {
    const message = createBaseWorkOrder();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.scheduledAt = object.scheduledAt ?? undefined;
    message.durationMinutes = object.durationMinutes ?? 0;
    message.jobs = object.jobs?.map((e) => Job.fromPartial(e)) || [];
    message.state = object.state ?? 0;
    return message;
  },
};

function createBaseWorkOrderList(): WorkOrderList {
  return { workOrders: [] };
}

export const WorkOrderList: MessageFns<WorkOrderList> = {
  encode(message: WorkOrderList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.workOrders) {
      WorkOrder.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WorkOrderList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWorkOrderList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.workOrders.push(WorkOrder.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WorkOrderList {
    return {
      workOrders: globalThis.Array.isArray(object?.workOrders)
        ? object.workOrders.map((e: any) => WorkOrder.fromJSON(e))
        : [],
    };
  },

  toJSON(message: WorkOrderList): unknown {
    const obj: any = {};
    if (message.workOrders?.length) {
      obj.workOrders = message.workOrders.map((e) => WorkOrder.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WorkOrderList>, I>>(base?: I): WorkOrderList {
    return WorkOrderList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WorkOrderList>, I>>(object: I): WorkOrderList {
    const message = createBaseWorkOrderList();
    message.workOrders = object.workOrders?.map((e) => WorkOrder.fromPartial(e)) || [];
    return message;
  },
};

function createBaseListWorkOrdersResponse(): ListWorkOrdersResponse {
  return { pageToken: "", workOrders: [] };
}

export const ListWorkOrdersResponse: MessageFns<ListWorkOrdersResponse> = {
  encode(message: ListWorkOrdersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.workOrders) {
      WorkOrder.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListWorkOrdersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListWorkOrdersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.workOrders.push(WorkOrder.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListWorkOrdersResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      workOrders: globalThis.Array.isArray(object?.workOrders)
        ? object.workOrders.map((e: any) => WorkOrder.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ListWorkOrdersResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.workOrders?.length) {
      obj.workOrders = message.workOrders.map((e) => WorkOrder.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListWorkOrdersResponse>, I>>(base?: I): ListWorkOrdersResponse {
    return ListWorkOrdersResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListWorkOrdersResponse>, I>>(object: I): ListWorkOrdersResponse {
    const message = createBaseListWorkOrdersResponse();
    message.pageToken = object.pageToken ?? "";
    message.workOrders = object.workOrders?.map((e) => WorkOrder.fromPartial(e)) || [];
    return message;
  },
};

function createBaseIntervention(): Intervention {
  return {
    id: 0,
    taskId: 0,
    qualification: "",
    description: "",
    state: 0,
    robotSerial: "",
    jobId: 0,
    cause: 0,
    priority: 0,
    assignment: undefined,
    createdAt: undefined,
  };
}

export const Intervention: MessageFns<Intervention> = {
  encode(message: Intervention, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).uint64(message.id);
    }
    if (message.taskId !== 0) {
      writer.uint32(16).uint64(message.taskId);
    }
    if (message.qualification !== "") {
      writer.uint32(26).string(message.qualification);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.state !== 0) {
      writer.uint32(40).int32(message.state);
    }
    if (message.robotSerial !== "") {
      writer.uint32(50).string(message.robotSerial);
    }
    if (message.jobId !== 0) {
      writer.uint32(56).uint64(message.jobId);
    }
    if (message.cause !== 0) {
      writer.uint32(64).int32(message.cause);
    }
    if (message.priority !== 0) {
      writer.uint32(72).int32(message.priority);
    }
    if (message.assignment !== undefined) {
      InterventionAssignment.encode(message.assignment, writer.uint32(82).fork()).join();
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Intervention {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntervention();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.taskId = longToNumber(reader.uint64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.qualification = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.robotSerial = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.jobId = longToNumber(reader.uint64());
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.cause = reader.int32() as any;
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.priority = reader.int32();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.assignment = InterventionAssignment.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Intervention {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      taskId: isSet(object.taskId) ? globalThis.Number(object.taskId) : 0,
      qualification: isSet(object.qualification) ? globalThis.String(object.qualification) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      robotSerial: isSet(object.robotSerial) ? globalThis.String(object.robotSerial) : "",
      jobId: isSet(object.jobId) ? globalThis.Number(object.jobId) : 0,
      cause: isSet(object.cause) ? intervention_InterventionCauseFromJSON(object.cause) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      assignment: isSet(object.assignment) ? InterventionAssignment.fromJSON(object.assignment) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : undefined,
    };
  },

  toJSON(message: Intervention): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.taskId !== 0) {
      obj.taskId = Math.round(message.taskId);
    }
    if (message.qualification !== "") {
      obj.qualification = message.qualification;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.state !== 0) {
      obj.state = stateToJSON(message.state);
    }
    if (message.robotSerial !== "") {
      obj.robotSerial = message.robotSerial;
    }
    if (message.jobId !== 0) {
      obj.jobId = Math.round(message.jobId);
    }
    if (message.cause !== 0) {
      obj.cause = intervention_InterventionCauseToJSON(message.cause);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.assignment !== undefined) {
      obj.assignment = InterventionAssignment.toJSON(message.assignment);
    }
    if (message.createdAt !== undefined) {
      obj.createdAt = message.createdAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Intervention>, I>>(base?: I): Intervention {
    return Intervention.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Intervention>, I>>(object: I): Intervention {
    const message = createBaseIntervention();
    message.id = object.id ?? 0;
    message.taskId = object.taskId ?? 0;
    message.qualification = object.qualification ?? "";
    message.description = object.description ?? "";
    message.state = object.state ?? 0;
    message.robotSerial = object.robotSerial ?? "";
    message.jobId = object.jobId ?? 0;
    message.cause = object.cause ?? 0;
    message.priority = object.priority ?? 0;
    message.assignment = (object.assignment !== undefined && object.assignment !== null)
      ? InterventionAssignment.fromPartial(object.assignment)
      : undefined;
    message.createdAt = object.createdAt ?? undefined;
    return message;
  },
};

function createBaseInterventionList(): InterventionList {
  return { intervention: [] };
}

export const InterventionList: MessageFns<InterventionList> = {
  encode(message: InterventionList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.intervention) {
      Intervention.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InterventionList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInterventionList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.intervention.push(Intervention.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InterventionList {
    return {
      intervention: globalThis.Array.isArray(object?.intervention)
        ? object.intervention.map((e: any) => Intervention.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InterventionList): unknown {
    const obj: any = {};
    if (message.intervention?.length) {
      obj.intervention = message.intervention.map((e) => Intervention.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InterventionList>, I>>(base?: I): InterventionList {
    return InterventionList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InterventionList>, I>>(object: I): InterventionList {
    const message = createBaseInterventionList();
    message.intervention = object.intervention?.map((e) => Intervention.fromPartial(e)) || [];
    return message;
  },
};

function createBaseInterventionAssignment(): InterventionAssignment {
  return { userId: "", assignedAt: undefined };
}

export const InterventionAssignment: MessageFns<InterventionAssignment> = {
  encode(message: InterventionAssignment, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.assignedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.assignedAt), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InterventionAssignment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInterventionAssignment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.assignedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InterventionAssignment {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      assignedAt: isSet(object.assignedAt) ? globalThis.String(object.assignedAt) : undefined,
    };
  },

  toJSON(message: InterventionAssignment): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.assignedAt !== undefined) {
      obj.assignedAt = message.assignedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InterventionAssignment>, I>>(base?: I): InterventionAssignment {
    return InterventionAssignment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InterventionAssignment>, I>>(object: I): InterventionAssignment {
    const message = createBaseInterventionAssignment();
    message.userId = object.userId ?? "";
    message.assignedAt = object.assignedAt ?? undefined;
    return message;
  },
};

function createBaseListInterventionsRequest(): ListInterventionsRequest {
  return {
    pageSize: 0,
    pageToken: "",
    robotSerials: "",
    assigedUserIds: "",
    assigned: false,
    taskIds: "",
    jobIds: "",
    causes: "",
    states: "",
  };
}

export const ListInterventionsRequest: MessageFns<ListInterventionsRequest> = {
  encode(message: ListInterventionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).int32(message.pageSize);
    }
    if (message.pageToken !== "") {
      writer.uint32(18).string(message.pageToken);
    }
    if (message.robotSerials !== "") {
      writer.uint32(26).string(message.robotSerials);
    }
    if (message.assigedUserIds !== "") {
      writer.uint32(34).string(message.assigedUserIds);
    }
    if (message.assigned !== false) {
      writer.uint32(40).bool(message.assigned);
    }
    if (message.taskIds !== "") {
      writer.uint32(50).string(message.taskIds);
    }
    if (message.jobIds !== "") {
      writer.uint32(58).string(message.jobIds);
    }
    if (message.causes !== "") {
      writer.uint32(66).string(message.causes);
    }
    if (message.states !== "") {
      writer.uint32(74).string(message.states);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListInterventionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListInterventionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.robotSerials = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.assigedUserIds = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.assigned = reader.bool();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.taskIds = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.jobIds = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.causes = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.states = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListInterventionsRequest {
    return {
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      robotSerials: isSet(object.robotSerials) ? globalThis.String(object.robotSerials) : "",
      assigedUserIds: isSet(object.assigedUserIds) ? globalThis.String(object.assigedUserIds) : "",
      assigned: isSet(object.assigned) ? globalThis.Boolean(object.assigned) : false,
      taskIds: isSet(object.taskIds) ? globalThis.String(object.taskIds) : "",
      jobIds: isSet(object.jobIds) ? globalThis.String(object.jobIds) : "",
      causes: isSet(object.causes) ? globalThis.String(object.causes) : "",
      states: isSet(object.states) ? globalThis.String(object.states) : "",
    };
  },

  toJSON(message: ListInterventionsRequest): unknown {
    const obj: any = {};
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.robotSerials !== "") {
      obj.robotSerials = message.robotSerials;
    }
    if (message.assigedUserIds !== "") {
      obj.assigedUserIds = message.assigedUserIds;
    }
    if (message.assigned !== false) {
      obj.assigned = message.assigned;
    }
    if (message.taskIds !== "") {
      obj.taskIds = message.taskIds;
    }
    if (message.jobIds !== "") {
      obj.jobIds = message.jobIds;
    }
    if (message.causes !== "") {
      obj.causes = message.causes;
    }
    if (message.states !== "") {
      obj.states = message.states;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListInterventionsRequest>, I>>(base?: I): ListInterventionsRequest {
    return ListInterventionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListInterventionsRequest>, I>>(object: I): ListInterventionsRequest {
    const message = createBaseListInterventionsRequest();
    message.pageSize = object.pageSize ?? 0;
    message.pageToken = object.pageToken ?? "";
    message.robotSerials = object.robotSerials ?? "";
    message.assigedUserIds = object.assigedUserIds ?? "";
    message.assigned = object.assigned ?? false;
    message.taskIds = object.taskIds ?? "";
    message.jobIds = object.jobIds ?? "";
    message.causes = object.causes ?? "";
    message.states = object.states ?? "";
    return message;
  },
};

function createBaseListInterventionsResponse(): ListInterventionsResponse {
  return { pageToken: "", interventions: [] };
}

export const ListInterventionsResponse: MessageFns<ListInterventionsResponse> = {
  encode(message: ListInterventionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageToken !== "") {
      writer.uint32(10).string(message.pageToken);
    }
    for (const v of message.interventions) {
      Intervention.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ListInterventionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListInterventionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pageToken = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.interventions.push(Intervention.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListInterventionsResponse {
    return {
      pageToken: isSet(object.pageToken) ? globalThis.String(object.pageToken) : "",
      interventions: globalThis.Array.isArray(object?.interventions)
        ? object.interventions.map((e: any) => Intervention.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ListInterventionsResponse): unknown {
    const obj: any = {};
    if (message.pageToken !== "") {
      obj.pageToken = message.pageToken;
    }
    if (message.interventions?.length) {
      obj.interventions = message.interventions.map((e) => Intervention.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListInterventionsResponse>, I>>(base?: I): ListInterventionsResponse {
    return ListInterventionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListInterventionsResponse>, I>>(object: I): ListInterventionsResponse {
    const message = createBaseListInterventionsResponse();
    message.pageToken = object.pageToken ?? "";
    message.interventions = object.interventions?.map((e) => Intervention.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreateInterventionRequest(): CreateInterventionRequest {
  return { intervention: undefined };
}

export const CreateInterventionRequest: MessageFns<CreateInterventionRequest> = {
  encode(message: CreateInterventionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.intervention !== undefined) {
      Intervention.encode(message.intervention, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateInterventionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateInterventionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.intervention = Intervention.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateInterventionRequest {
    return { intervention: isSet(object.intervention) ? Intervention.fromJSON(object.intervention) : undefined };
  },

  toJSON(message: CreateInterventionRequest): unknown {
    const obj: any = {};
    if (message.intervention !== undefined) {
      obj.intervention = Intervention.toJSON(message.intervention);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateInterventionRequest>, I>>(base?: I): CreateInterventionRequest {
    return CreateInterventionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateInterventionRequest>, I>>(object: I): CreateInterventionRequest {
    const message = createBaseCreateInterventionRequest();
    message.intervention = (object.intervention !== undefined && object.intervention !== null)
      ? Intervention.fromPartial(object.intervention)
      : undefined;
    return message;
  },
};

function createBaseCreateInterventionResponse(): CreateInterventionResponse {
  return { intervention: undefined };
}

export const CreateInterventionResponse: MessageFns<CreateInterventionResponse> = {
  encode(message: CreateInterventionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.intervention !== undefined) {
      Intervention.encode(message.intervention, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateInterventionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateInterventionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.intervention = Intervention.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateInterventionResponse {
    return { intervention: isSet(object.intervention) ? Intervention.fromJSON(object.intervention) : undefined };
  },

  toJSON(message: CreateInterventionResponse): unknown {
    const obj: any = {};
    if (message.intervention !== undefined) {
      obj.intervention = Intervention.toJSON(message.intervention);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateInterventionResponse>, I>>(base?: I): CreateInterventionResponse {
    return CreateInterventionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateInterventionResponse>, I>>(object: I): CreateInterventionResponse {
    const message = createBaseCreateInterventionResponse();
    message.intervention = (object.intervention !== undefined && object.intervention !== null)
      ? Intervention.fromPartial(object.intervention)
      : undefined;
    return message;
  },
};

function createBaseGetActiveTaskRequest(): GetActiveTaskRequest {
  return { currentLocation: undefined };
}

export const GetActiveTaskRequest: MessageFns<GetActiveTaskRequest> = {
  encode(message: GetActiveTaskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentLocation !== undefined) {
      Point.encode(message.currentLocation, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetActiveTaskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetActiveTaskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2:
          if (tag !== 18) {
            break;
          }

          message.currentLocation = Point.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetActiveTaskRequest {
    return { currentLocation: isSet(object.currentLocation) ? Point.fromJSON(object.currentLocation) : undefined };
  },

  toJSON(message: GetActiveTaskRequest): unknown {
    const obj: any = {};
    if (message.currentLocation !== undefined) {
      obj.currentLocation = Point.toJSON(message.currentLocation);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetActiveTaskRequest>, I>>(base?: I): GetActiveTaskRequest {
    return GetActiveTaskRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActiveTaskRequest>, I>>(object: I): GetActiveTaskRequest {
    const message = createBaseGetActiveTaskRequest();
    message.currentLocation = (object.currentLocation !== undefined && object.currentLocation !== null)
      ? Point.fromPartial(object.currentLocation)
      : undefined;
    return message;
  },
};

function createBaseGetActiveTaskResponse(): GetActiveTaskResponse {
  return { task: undefined };
}

export const GetActiveTaskResponse: MessageFns<GetActiveTaskResponse> = {
  encode(message: GetActiveTaskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.task !== undefined) {
      Task.encode(message.task, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetActiveTaskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetActiveTaskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.task = Task.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetActiveTaskResponse {
    return { task: isSet(object.task) ? Task.fromJSON(object.task) : undefined };
  },

  toJSON(message: GetActiveTaskResponse): unknown {
    const obj: any = {};
    if (message.task !== undefined) {
      obj.task = Task.toJSON(message.task);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetActiveTaskResponse>, I>>(base?: I): GetActiveTaskResponse {
    return GetActiveTaskResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActiveTaskResponse>, I>>(object: I): GetActiveTaskResponse {
    const message = createBaseGetActiveTaskResponse();
    message.task = (object.task !== undefined && object.task !== null) ? Task.fromPartial(object.task) : undefined;
    return message;
  },
};

function createBaseGetTaskRequest(): GetTaskRequest {
  return { taskId: 0 };
}

export const GetTaskRequest: MessageFns<GetTaskRequest> = {
  encode(message: GetTaskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.taskId !== 0) {
      writer.uint32(8).uint64(message.taskId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTaskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTaskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.taskId = longToNumber(reader.uint64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTaskRequest {
    return { taskId: isSet(object.taskId) ? globalThis.Number(object.taskId) : 0 };
  },

  toJSON(message: GetTaskRequest): unknown {
    const obj: any = {};
    if (message.taskId !== 0) {
      obj.taskId = Math.round(message.taskId);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTaskRequest>, I>>(base?: I): GetTaskRequest {
    return GetTaskRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskRequest>, I>>(object: I): GetTaskRequest {
    const message = createBaseGetTaskRequest();
    message.taskId = object.taskId ?? 0;
    return message;
  },
};

function createBaseGetTaskResponse(): GetTaskResponse {
  return { task: undefined };
}

export const GetTaskResponse: MessageFns<GetTaskResponse> = {
  encode(message: GetTaskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.task !== undefined) {
      Task.encode(message.task, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTaskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTaskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.task = Task.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTaskResponse {
    return { task: isSet(object.task) ? Task.fromJSON(object.task) : undefined };
  },

  toJSON(message: GetTaskResponse): unknown {
    const obj: any = {};
    if (message.task !== undefined) {
      obj.task = Task.toJSON(message.task);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTaskResponse>, I>>(base?: I): GetTaskResponse {
    return GetTaskResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskResponse>, I>>(object: I): GetTaskResponse {
    const message = createBaseGetTaskResponse();
    message.task = (object.task !== undefined && object.task !== null) ? Task.fromPartial(object.task) : undefined;
    return message;
  },
};

function createBaseUpdateTaskRequest(): UpdateTaskRequest {
  return {
    taskId: 0,
    state: undefined,
    startedAt: undefined,
    startLocation: undefined,
    startHeading: undefined,
    endedAt: undefined,
    endLocation: undefined,
    endHeading: undefined,
    statusInfo: undefined,
    progressUpdatedAt: undefined,
    progressLocation: undefined,
    progressHeading: undefined,
  };
}

export const UpdateTaskRequest: MessageFns<UpdateTaskRequest> = {
  encode(message: UpdateTaskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.taskId !== 0) {
      writer.uint32(8).uint64(message.taskId);
    }
    if (message.state !== undefined) {
      writer.uint32(16).int32(message.state);
    }
    if (message.startedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.startedAt), writer.uint32(26).fork()).join();
    }
    if (message.startLocation !== undefined) {
      Point.encode(message.startLocation, writer.uint32(34).fork()).join();
    }
    if (message.startHeading !== undefined) {
      writer.uint32(41).double(message.startHeading);
    }
    if (message.endedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.endedAt), writer.uint32(50).fork()).join();
    }
    if (message.endLocation !== undefined) {
      Point.encode(message.endLocation, writer.uint32(58).fork()).join();
    }
    if (message.endHeading !== undefined) {
      writer.uint32(65).double(message.endHeading);
    }
    if (message.statusInfo !== undefined) {
      writer.uint32(74).string(message.statusInfo);
    }
    if (message.progressUpdatedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.progressUpdatedAt), writer.uint32(82).fork()).join();
    }
    if (message.progressLocation !== undefined) {
      Point.encode(message.progressLocation, writer.uint32(90).fork()).join();
    }
    if (message.progressHeading !== undefined) {
      writer.uint32(97).double(message.progressHeading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTaskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTaskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.taskId = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.startedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.startLocation = Point.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }

          message.startHeading = reader.double();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.endedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.endLocation = Point.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }

          message.endHeading = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.statusInfo = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.progressUpdatedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.progressLocation = Point.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 97) {
            break;
          }

          message.progressHeading = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateTaskRequest {
    return {
      taskId: isSet(object.taskId) ? globalThis.Number(object.taskId) : 0,
      state: isSet(object.state) ? stateFromJSON(object.state) : undefined,
      startedAt: isSet(object.startedAt) ? globalThis.String(object.startedAt) : undefined,
      startLocation: isSet(object.startLocation) ? Point.fromJSON(object.startLocation) : undefined,
      startHeading: isSet(object.startHeading) ? globalThis.Number(object.startHeading) : undefined,
      endedAt: isSet(object.endedAt) ? globalThis.String(object.endedAt) : undefined,
      endLocation: isSet(object.endLocation) ? Point.fromJSON(object.endLocation) : undefined,
      endHeading: isSet(object.endHeading) ? globalThis.Number(object.endHeading) : undefined,
      statusInfo: isSet(object.statusInfo) ? globalThis.String(object.statusInfo) : undefined,
      progressUpdatedAt: isSet(object.progressUpdatedAt) ? globalThis.String(object.progressUpdatedAt) : undefined,
      progressLocation: isSet(object.progressLocation) ? Point.fromJSON(object.progressLocation) : undefined,
      progressHeading: isSet(object.progressHeading) ? globalThis.Number(object.progressHeading) : undefined,
    };
  },

  toJSON(message: UpdateTaskRequest): unknown {
    const obj: any = {};
    if (message.taskId !== 0) {
      obj.taskId = Math.round(message.taskId);
    }
    if (message.state !== undefined) {
      obj.state = stateToJSON(message.state);
    }
    if (message.startedAt !== undefined) {
      obj.startedAt = message.startedAt;
    }
    if (message.startLocation !== undefined) {
      obj.startLocation = Point.toJSON(message.startLocation);
    }
    if (message.startHeading !== undefined) {
      obj.startHeading = message.startHeading;
    }
    if (message.endedAt !== undefined) {
      obj.endedAt = message.endedAt;
    }
    if (message.endLocation !== undefined) {
      obj.endLocation = Point.toJSON(message.endLocation);
    }
    if (message.endHeading !== undefined) {
      obj.endHeading = message.endHeading;
    }
    if (message.statusInfo !== undefined) {
      obj.statusInfo = message.statusInfo;
    }
    if (message.progressUpdatedAt !== undefined) {
      obj.progressUpdatedAt = message.progressUpdatedAt;
    }
    if (message.progressLocation !== undefined) {
      obj.progressLocation = Point.toJSON(message.progressLocation);
    }
    if (message.progressHeading !== undefined) {
      obj.progressHeading = message.progressHeading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTaskRequest>, I>>(base?: I): UpdateTaskRequest {
    return UpdateTaskRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTaskRequest>, I>>(object: I): UpdateTaskRequest {
    const message = createBaseUpdateTaskRequest();
    message.taskId = object.taskId ?? 0;
    message.state = object.state ?? undefined;
    message.startedAt = object.startedAt ?? undefined;
    message.startLocation = (object.startLocation !== undefined && object.startLocation !== null)
      ? Point.fromPartial(object.startLocation)
      : undefined;
    message.startHeading = object.startHeading ?? undefined;
    message.endedAt = object.endedAt ?? undefined;
    message.endLocation = (object.endLocation !== undefined && object.endLocation !== null)
      ? Point.fromPartial(object.endLocation)
      : undefined;
    message.endHeading = object.endHeading ?? undefined;
    message.statusInfo = object.statusInfo ?? undefined;
    message.progressUpdatedAt = object.progressUpdatedAt ?? undefined;
    message.progressLocation = (object.progressLocation !== undefined && object.progressLocation !== null)
      ? Point.fromPartial(object.progressLocation)
      : undefined;
    message.progressHeading = object.progressHeading ?? undefined;
    return message;
  },
};

function createBaseUpdateTaskResponse(): UpdateTaskResponse {
  return { task: undefined };
}

export const UpdateTaskResponse: MessageFns<UpdateTaskResponse> = {
  encode(message: UpdateTaskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.task !== undefined) {
      Task.encode(message.task, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTaskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTaskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.task = Task.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateTaskResponse {
    return { task: isSet(object.task) ? Task.fromJSON(object.task) : undefined };
  },

  toJSON(message: UpdateTaskResponse): unknown {
    const obj: any = {};
    if (message.task !== undefined) {
      obj.task = Task.toJSON(message.task);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTaskResponse>, I>>(base?: I): UpdateTaskResponse {
    return UpdateTaskResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTaskResponse>, I>>(object: I): UpdateTaskResponse {
    const message = createBaseUpdateTaskResponse();
    message.task = (object.task !== undefined && object.task !== null) ? Task.fromPartial(object.task) : undefined;
    return message;
  },
};

function createBaseStartManualTaskRequest(): StartManualTaskRequest {
  return { startLocation: undefined, startHeading: undefined };
}

export const StartManualTaskRequest: MessageFns<StartManualTaskRequest> = {
  encode(message: StartManualTaskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startLocation !== undefined) {
      Point.encode(message.startLocation, writer.uint32(10).fork()).join();
    }
    if (message.startHeading !== undefined) {
      writer.uint32(17).double(message.startHeading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartManualTaskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartManualTaskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.startLocation = Point.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.startHeading = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StartManualTaskRequest {
    return {
      startLocation: isSet(object.startLocation) ? Point.fromJSON(object.startLocation) : undefined,
      startHeading: isSet(object.startHeading) ? globalThis.Number(object.startHeading) : undefined,
    };
  },

  toJSON(message: StartManualTaskRequest): unknown {
    const obj: any = {};
    if (message.startLocation !== undefined) {
      obj.startLocation = Point.toJSON(message.startLocation);
    }
    if (message.startHeading !== undefined) {
      obj.startHeading = message.startHeading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StartManualTaskRequest>, I>>(base?: I): StartManualTaskRequest {
    return StartManualTaskRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartManualTaskRequest>, I>>(object: I): StartManualTaskRequest {
    const message = createBaseStartManualTaskRequest();
    message.startLocation = (object.startLocation !== undefined && object.startLocation !== null)
      ? Point.fromPartial(object.startLocation)
      : undefined;
    message.startHeading = object.startHeading ?? undefined;
    return message;
  },
};

function createBaseStopManualTaskRequest(): StopManualTaskRequest {
  return { endLocation: undefined, endHeading: undefined };
}

export const StopManualTaskRequest: MessageFns<StopManualTaskRequest> = {
  encode(message: StopManualTaskRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.endLocation !== undefined) {
      Point.encode(message.endLocation, writer.uint32(10).fork()).join();
    }
    if (message.endHeading !== undefined) {
      writer.uint32(17).double(message.endHeading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StopManualTaskRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStopManualTaskRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.endLocation = Point.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.endHeading = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StopManualTaskRequest {
    return {
      endLocation: isSet(object.endLocation) ? Point.fromJSON(object.endLocation) : undefined,
      endHeading: isSet(object.endHeading) ? globalThis.Number(object.endHeading) : undefined,
    };
  },

  toJSON(message: StopManualTaskRequest): unknown {
    const obj: any = {};
    if (message.endLocation !== undefined) {
      obj.endLocation = Point.toJSON(message.endLocation);
    }
    if (message.endHeading !== undefined) {
      obj.endHeading = message.endHeading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StopManualTaskRequest>, I>>(base?: I): StopManualTaskRequest {
    return StopManualTaskRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StopManualTaskRequest>, I>>(object: I): StopManualTaskRequest {
    const message = createBaseStopManualTaskRequest();
    message.endLocation = (object.endLocation !== undefined && object.endLocation !== null)
      ? Point.fromPartial(object.endLocation)
      : undefined;
    message.endHeading = object.endHeading ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(dateStr: string): Timestamp {
  const date = new globalThis.Date(dateStr);
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): string {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis).toISOString();
}

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
