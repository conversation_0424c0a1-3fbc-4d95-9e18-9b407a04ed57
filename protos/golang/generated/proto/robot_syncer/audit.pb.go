// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: robot_syncer/audit.proto

package robot_syncer

import (
	config_service "github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigAuditLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Time that this operation was recorded in RoSy, i.e. synced up to the
	// cloud. If this operation happened on the robot, it may have occurred at an
	// earlier date.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Auth0 user ID of the user under whose authority this change was made, or
	// simply the robot hostname (e.g., "reaper114") if this was made directly on
	// a robot with no end-user credentials available.
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The method that was called in this operation, e.g. "UpdateConfigNode" or
	// "SetTree". These do not necessarily correspond 1:1 with gRPC method calls
	// or REST endpoints.
	Method string `protobuf:"bytes,3,opt,name=method,proto3" json:"method,omitempty"`
	// Serial of the robot whose config was updated.
	Serial string `protobuf:"bytes,4,opt,name=serial,proto3" json:"serial,omitempty"`
	// Full config key that was updated, under the root of the updated robot:
	// e.g., "common/software_manager/target_version".
	Key string `protobuf:"bytes,5,opt,name=key,proto3" json:"key,omitempty"`
	// Value of this config node before the change, if applicable for this
	// operation. This is set when the operation changes a scalar, like
	// `SetValue`. It's not set when the node is a list or a tree.
	OldValue *config_service.ConfigValue `protobuf:"bytes,6,opt,name=old_value,json=oldValue,proto3" json:"old_value,omitempty"`
	// Value of this config node after the change, if applicable for this
	// operation. This is set when the operation changes a scalar, like
	// `SetValue`. It's not set when the node is a list or a tree.
	NewValue *config_service.ConfigValue `protobuf:"bytes,7,opt,name=new_value,json=newValue,proto3" json:"new_value,omitempty"`
}

func (x *ConfigAuditLog) Reset() {
	*x = ConfigAuditLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_audit_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigAuditLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigAuditLog) ProtoMessage() {}

func (x *ConfigAuditLog) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_audit_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigAuditLog.ProtoReflect.Descriptor instead.
func (*ConfigAuditLog) Descriptor() ([]byte, []int) {
	return file_robot_syncer_audit_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigAuditLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ConfigAuditLog) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ConfigAuditLog) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ConfigAuditLog) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *ConfigAuditLog) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ConfigAuditLog) GetOldValue() *config_service.ConfigValue {
	if x != nil {
		return x.OldValue
	}
	return nil
}

func (x *ConfigAuditLog) GetNewValue() *config_service.ConfigValue {
	if x != nil {
		return x.NewValue
	}
	return nil
}

type GetConfigAuditLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuditLogs []*ConfigAuditLog `protobuf:"bytes,1,rep,name=audit_logs,json=auditLogs,proto3" json:"audit_logs,omitempty"`
}

func (x *GetConfigAuditLogsResponse) Reset() {
	*x = GetConfigAuditLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_robot_syncer_audit_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConfigAuditLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigAuditLogsResponse) ProtoMessage() {}

func (x *GetConfigAuditLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_robot_syncer_audit_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigAuditLogsResponse.ProtoReflect.Descriptor instead.
func (*GetConfigAuditLogsResponse) Descriptor() ([]byte, []int) {
	return file_robot_syncer_audit_proto_rawDescGZIP(), []int{1}
}

func (x *GetConfigAuditLogsResponse) GetAuditLogs() []*ConfigAuditLog {
	if x != nil {
		return x.AuditLogs
	}
	return nil
}

var File_robot_syncer_audit_proto protoreflect.FileDescriptor

var file_robot_syncer_audit_proto_rawDesc = []byte{
	0x0a, 0x18, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2f, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa6, 0x02, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x3d, 0x0a, 0x09, 0x6f, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6f, 0x6c, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x3d, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x66, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48,
	0x0a, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x74, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x09, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x42, 0x46, 0x5a, 0x44, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_robot_syncer_audit_proto_rawDescOnce sync.Once
	file_robot_syncer_audit_proto_rawDescData = file_robot_syncer_audit_proto_rawDesc
)

func file_robot_syncer_audit_proto_rawDescGZIP() []byte {
	file_robot_syncer_audit_proto_rawDescOnce.Do(func() {
		file_robot_syncer_audit_proto_rawDescData = protoimpl.X.CompressGZIP(file_robot_syncer_audit_proto_rawDescData)
	})
	return file_robot_syncer_audit_proto_rawDescData
}

var file_robot_syncer_audit_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_robot_syncer_audit_proto_goTypes = []interface{}{
	(*ConfigAuditLog)(nil),             // 0: carbon.robot_syncer.audit.ConfigAuditLog
	(*GetConfigAuditLogsResponse)(nil), // 1: carbon.robot_syncer.audit.GetConfigAuditLogsResponse
	(*timestamppb.Timestamp)(nil),      // 2: google.protobuf.Timestamp
	(*config_service.ConfigValue)(nil), // 3: carbon.config.proto.ConfigValue
}
var file_robot_syncer_audit_proto_depIdxs = []int32{
	2, // 0: carbon.robot_syncer.audit.ConfigAuditLog.create_time:type_name -> google.protobuf.Timestamp
	3, // 1: carbon.robot_syncer.audit.ConfigAuditLog.old_value:type_name -> carbon.config.proto.ConfigValue
	3, // 2: carbon.robot_syncer.audit.ConfigAuditLog.new_value:type_name -> carbon.config.proto.ConfigValue
	0, // 3: carbon.robot_syncer.audit.GetConfigAuditLogsResponse.audit_logs:type_name -> carbon.robot_syncer.audit.ConfigAuditLog
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_robot_syncer_audit_proto_init() }
func file_robot_syncer_audit_proto_init() {
	if File_robot_syncer_audit_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_robot_syncer_audit_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigAuditLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_robot_syncer_audit_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConfigAuditLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_robot_syncer_audit_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_robot_syncer_audit_proto_goTypes,
		DependencyIndexes: file_robot_syncer_audit_proto_depIdxs,
		MessageInfos:      file_robot_syncer_audit_proto_msgTypes,
	}.Build()
	File_robot_syncer_audit_proto = out.File
	file_robot_syncer_audit_proto_rawDesc = nil
	file_robot_syncer_audit_proto_goTypes = nil
	file_robot_syncer_audit_proto_depIdxs = nil
}
