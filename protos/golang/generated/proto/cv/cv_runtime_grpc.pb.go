// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: cv/runtime/cv_runtime.proto

package cv

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CVRuntimeServiceClient is the client API for CVRuntimeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CVRuntimeServiceClient interface {
	SetP2PContext(ctx context.Context, in *SetP2PContextRequest, opts ...grpc.CallOption) (*SetP2PContextResponse, error)
	GetCameraDimensions(ctx context.Context, in *GetCameraDimensionsRequest, opts ...grpc.CallOption) (*GetCameraDimensionsResponse, error)
	GetCameraInfo(ctx context.Context, in *GetCameraInfoRequest, opts ...grpc.CallOption) (*GetCameraInfoResponse, error)
	GetDeepweedIndexToCategory(ctx context.Context, in *GetDeepweedIndexToCategoryRequest, opts ...grpc.CallOption) (*GetDeepweedIndexToCategoryResponse, error)
	SetDeepweedDetectionCriteria(ctx context.Context, in *SetDeepweedDetectionCriteriaRequest, opts ...grpc.CallOption) (*SetDeepweedDetectionCriteriaResponse, error)
	GetDeepweedDetectionCriteria(ctx context.Context, in *GetDeepweedDetectionCriteriaRequest, opts ...grpc.CallOption) (*GetDeepweedDetectionCriteriaResponse, error)
	GetDeepweedSupportedCategories(ctx context.Context, in *GetDeepweedSupportedCategoriesRequest, opts ...grpc.CallOption) (*GetDeepweedSupportedCategoriesResponse, error)
	GetCameraTemperatures(ctx context.Context, in *GetCameraTemperaturesRequest, opts ...grpc.CallOption) (*GetCameraTemperaturesResponse, error)
	SetCameraSettings(ctx context.Context, in *SetCameraSettingsRequest, opts ...grpc.CallOption) (*SetCameraSettingsResponse, error)
	GetCameraSettings(ctx context.Context, in *GetCameraSettingsRequest, opts ...grpc.CallOption) (*GetCameraSettingsResponse, error)
	StartBurstRecordFrames(ctx context.Context, in *StartBurstRecordFramesRequest, opts ...grpc.CallOption) (*StartBurstRecordFramesResponse, error)
	StopBurstRecordFrames(ctx context.Context, in *StopBurstRecordFramesRequest, opts ...grpc.CallOption) (*StopBurstRecordFramesResponse, error)
	GetConnectors(ctx context.Context, in *GetConnectorsRequest, opts ...grpc.CallOption) (*GetConnectorsResponse, error)
	SetConnectors(ctx context.Context, in *SetConnectorsRequest, opts ...grpc.CallOption) (*SetConnectorsResponse, error)
	GetTiming(ctx context.Context, in *GetTimingRequest, opts ...grpc.CallOption) (*GetTimingResponse, error)
	Predict(ctx context.Context, in *PredictRequest, opts ...grpc.CallOption) (*PredictResponse, error)
	LoadAndQueue(ctx context.Context, in *LoadAndQueueRequest, opts ...grpc.CallOption) (*LoadAndQueueResponse, error)
	SetImage(ctx context.Context, in *SetImageRequest, opts ...grpc.CallOption) (*SetImageResponse, error)
	UnsetImage(ctx context.Context, in *UnsetImageRequest, opts ...grpc.CallOption) (*UnsetImageResponse, error)
	GetModelPaths(ctx context.Context, in *GetModelPathsRequest, opts ...grpc.CallOption) (*GetModelPathsResponse, error)
	SetGPSLocation(ctx context.Context, in *SetGPSLocationRequest, opts ...grpc.CallOption) (*SetGPSLocationResponse, error)
	SetImplementStatus(ctx context.Context, in *SetImplementStatusRequest, opts ...grpc.CallOption) (*SetImplementStatusResponse, error)
	SetImageScore(ctx context.Context, in *SetImageScoreRequest, opts ...grpc.CallOption) (*SetImageScoreResponse, error)
	GetScoreQueue(ctx context.Context, in *GetScoreQueueRequest, opts ...grpc.CallOption) (*GetScoreQueueResponse, error)
	ListScoreQueues(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ListScoreQueuesResponse, error)
	GetMaxImageScore(ctx context.Context, in *GetMaxImageScoreRequest, opts ...grpc.CallOption) (*GetMaxImageScoreResponse, error)
	GetMaxScoredImage(ctx context.Context, in *GetMaxScoredImageRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error)
	GetLatestP2PImage(ctx context.Context, in *GetLatestP2PImageRequest, opts ...grpc.CallOption) (*P2PImageAndMetadataResponse, error)
	GetChipImage(ctx context.Context, in *GetChipImageRequest, opts ...grpc.CallOption) (*ChipImageAndMetadataResponse, error)
	GetChipQueueInformation(ctx context.Context, in *ChipQueueInformationRequest, opts ...grpc.CallOption) (*ChipQueueInformationResponse, error)
	FlushQueues(ctx context.Context, in *FlushQueuesRequest, opts ...grpc.CallOption) (*FlushQueuesResponse, error)
	GetLatestImage(ctx context.Context, in *GetLatestImageRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error)
	GetImageNearTimestamp(ctx context.Context, in *GetImageNearTimestampRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error)
	GetLightweightBurstRecord(ctx context.Context, in *GetLightweightBurstRecordRequest, opts ...grpc.CallOption) (*GetLightweightBurstRecordResponse, error)
	GetBooted(ctx context.Context, in *GetBootedRequest, opts ...grpc.CallOption) (*GetBootedResponse, error)
	GetReady(ctx context.Context, in *GetReadyRequest, opts ...grpc.CallOption) (*GetReadyResponse, error)
	GetDeepweedOutputByTimestamp(ctx context.Context, in *GetDeepweedOutputByTimestampRequest, opts ...grpc.CallOption) (*DeepweedOutput, error)
	GetRecommendedStrobeSettings(ctx context.Context, in *GetRecommendedStrobeSettingsRequest, opts ...grpc.CallOption) (*GetRecommendedStrobeSettingsResponse, error)
	P2PCapture(ctx context.Context, in *P2PCaptureRequest, opts ...grpc.CallOption) (*P2PCaptureResponse, error)
	SetAutoWhitebalance(ctx context.Context, in *SetAutoWhitebalanceRequest, opts ...grpc.CallOption) (*SetAutoWhitebalanceResponse, error)
	GetNextDeepweedOutput(ctx context.Context, in *GetNextDeepweedOutputRequest, opts ...grpc.CallOption) (*DeepweedOutput, error)
	GetNextP2POutput(ctx context.Context, in *GetNextP2POutputRequest, opts ...grpc.CallOption) (*P2POutputProto, error)
	SetTargetingState(ctx context.Context, in *SetTargetingStateRequest, opts ...grpc.CallOption) (*SetTargetingStateResponse, error)
	P2PBufferringBurstCapture(ctx context.Context, in *P2PBufferringBurstCaptureRequest, opts ...grpc.CallOption) (*P2PBufferringBurstCaptureResponse, error)
	GetNextFocusMetric(ctx context.Context, in *GetNextFocusMetricRequest, opts ...grpc.CallOption) (*GetNextFocusMetricResponse, error)
	RemoveDataDir(ctx context.Context, in *RemoveDataDirRequest, opts ...grpc.CallOption) (*RemoveDataDirResponse, error)
	GetLastNImages(ctx context.Context, in *LastNImageRequest, opts ...grpc.CallOption) (CVRuntimeService_GetLastNImagesClient, error)
	GetComputeCapabilities(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ComputeCapabilitiesResponse, error)
	GetSupportedTensorRTVersions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SupportedTensorRTVersionsResponse, error)
	ReloadCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	GetCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCategoryCollectionResponse, error)
	GetErrorState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetErrorStateResponse, error)
	SnapshotPredictImages(ctx context.Context, in *SnapshotPredictImagesRequest, opts ...grpc.CallOption) (*SnapshotPredictImagesResponse, error)
	GetChipForPredictImage(ctx context.Context, in *GetChipForPredictImageRequest, opts ...grpc.CallOption) (*GetChipForPredictImageResponse, error)
}

type cVRuntimeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCVRuntimeServiceClient(cc grpc.ClientConnInterface) CVRuntimeServiceClient {
	return &cVRuntimeServiceClient{cc}
}

func (c *cVRuntimeServiceClient) SetP2PContext(ctx context.Context, in *SetP2PContextRequest, opts ...grpc.CallOption) (*SetP2PContextResponse, error) {
	out := new(SetP2PContextResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetP2PContext", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetCameraDimensions(ctx context.Context, in *GetCameraDimensionsRequest, opts ...grpc.CallOption) (*GetCameraDimensionsResponse, error) {
	out := new(GetCameraDimensionsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetCameraDimensions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetCameraInfo(ctx context.Context, in *GetCameraInfoRequest, opts ...grpc.CallOption) (*GetCameraInfoResponse, error) {
	out := new(GetCameraInfoResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetCameraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetDeepweedIndexToCategory(ctx context.Context, in *GetDeepweedIndexToCategoryRequest, opts ...grpc.CallOption) (*GetDeepweedIndexToCategoryResponse, error) {
	out := new(GetDeepweedIndexToCategoryResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetDeepweedDetectionCriteria(ctx context.Context, in *SetDeepweedDetectionCriteriaRequest, opts ...grpc.CallOption) (*SetDeepweedDetectionCriteriaResponse, error) {
	out := new(SetDeepweedDetectionCriteriaResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetDeepweedDetectionCriteria(ctx context.Context, in *GetDeepweedDetectionCriteriaRequest, opts ...grpc.CallOption) (*GetDeepweedDetectionCriteriaResponse, error) {
	out := new(GetDeepweedDetectionCriteriaResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetDeepweedSupportedCategories(ctx context.Context, in *GetDeepweedSupportedCategoriesRequest, opts ...grpc.CallOption) (*GetDeepweedSupportedCategoriesResponse, error) {
	out := new(GetDeepweedSupportedCategoriesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetCameraTemperatures(ctx context.Context, in *GetCameraTemperaturesRequest, opts ...grpc.CallOption) (*GetCameraTemperaturesResponse, error) {
	out := new(GetCameraTemperaturesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetCameraSettings(ctx context.Context, in *SetCameraSettingsRequest, opts ...grpc.CallOption) (*SetCameraSettingsResponse, error) {
	out := new(SetCameraSettingsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetCameraSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetCameraSettings(ctx context.Context, in *GetCameraSettingsRequest, opts ...grpc.CallOption) (*GetCameraSettingsResponse, error) {
	out := new(GetCameraSettingsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetCameraSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) StartBurstRecordFrames(ctx context.Context, in *StartBurstRecordFramesRequest, opts ...grpc.CallOption) (*StartBurstRecordFramesResponse, error) {
	out := new(StartBurstRecordFramesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) StopBurstRecordFrames(ctx context.Context, in *StopBurstRecordFramesRequest, opts ...grpc.CallOption) (*StopBurstRecordFramesResponse, error) {
	out := new(StopBurstRecordFramesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetConnectors(ctx context.Context, in *GetConnectorsRequest, opts ...grpc.CallOption) (*GetConnectorsResponse, error) {
	out := new(GetConnectorsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetConnectors", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetConnectors(ctx context.Context, in *SetConnectorsRequest, opts ...grpc.CallOption) (*SetConnectorsResponse, error) {
	out := new(SetConnectorsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetConnectors", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetTiming(ctx context.Context, in *GetTimingRequest, opts ...grpc.CallOption) (*GetTimingResponse, error) {
	out := new(GetTimingResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetTiming", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) Predict(ctx context.Context, in *PredictRequest, opts ...grpc.CallOption) (*PredictResponse, error) {
	out := new(PredictResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/Predict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) LoadAndQueue(ctx context.Context, in *LoadAndQueueRequest, opts ...grpc.CallOption) (*LoadAndQueueResponse, error) {
	out := new(LoadAndQueueResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/LoadAndQueue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetImage(ctx context.Context, in *SetImageRequest, opts ...grpc.CallOption) (*SetImageResponse, error) {
	out := new(SetImageResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) UnsetImage(ctx context.Context, in *UnsetImageRequest, opts ...grpc.CallOption) (*UnsetImageResponse, error) {
	out := new(UnsetImageResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/UnsetImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetModelPaths(ctx context.Context, in *GetModelPathsRequest, opts ...grpc.CallOption) (*GetModelPathsResponse, error) {
	out := new(GetModelPathsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetModelPaths", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetGPSLocation(ctx context.Context, in *SetGPSLocationRequest, opts ...grpc.CallOption) (*SetGPSLocationResponse, error) {
	out := new(SetGPSLocationResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetGPSLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetImplementStatus(ctx context.Context, in *SetImplementStatusRequest, opts ...grpc.CallOption) (*SetImplementStatusResponse, error) {
	out := new(SetImplementStatusResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetImplementStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetImageScore(ctx context.Context, in *SetImageScoreRequest, opts ...grpc.CallOption) (*SetImageScoreResponse, error) {
	out := new(SetImageScoreResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetImageScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetScoreQueue(ctx context.Context, in *GetScoreQueueRequest, opts ...grpc.CallOption) (*GetScoreQueueResponse, error) {
	out := new(GetScoreQueueResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetScoreQueue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) ListScoreQueues(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ListScoreQueuesResponse, error) {
	out := new(ListScoreQueuesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/ListScoreQueues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetMaxImageScore(ctx context.Context, in *GetMaxImageScoreRequest, opts ...grpc.CallOption) (*GetMaxImageScoreResponse, error) {
	out := new(GetMaxImageScoreResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetMaxImageScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetMaxScoredImage(ctx context.Context, in *GetMaxScoredImageRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error) {
	out := new(ImageAndMetadataResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetLatestP2PImage(ctx context.Context, in *GetLatestP2PImageRequest, opts ...grpc.CallOption) (*P2PImageAndMetadataResponse, error) {
	out := new(P2PImageAndMetadataResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetChipImage(ctx context.Context, in *GetChipImageRequest, opts ...grpc.CallOption) (*ChipImageAndMetadataResponse, error) {
	out := new(ChipImageAndMetadataResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetChipImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetChipQueueInformation(ctx context.Context, in *ChipQueueInformationRequest, opts ...grpc.CallOption) (*ChipQueueInformationResponse, error) {
	out := new(ChipQueueInformationResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) FlushQueues(ctx context.Context, in *FlushQueuesRequest, opts ...grpc.CallOption) (*FlushQueuesResponse, error) {
	out := new(FlushQueuesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/FlushQueues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetLatestImage(ctx context.Context, in *GetLatestImageRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error) {
	out := new(ImageAndMetadataResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetLatestImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetImageNearTimestamp(ctx context.Context, in *GetImageNearTimestampRequest, opts ...grpc.CallOption) (*ImageAndMetadataResponse, error) {
	out := new(ImageAndMetadataResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetLightweightBurstRecord(ctx context.Context, in *GetLightweightBurstRecordRequest, opts ...grpc.CallOption) (*GetLightweightBurstRecordResponse, error) {
	out := new(GetLightweightBurstRecordResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetBooted(ctx context.Context, in *GetBootedRequest, opts ...grpc.CallOption) (*GetBootedResponse, error) {
	out := new(GetBootedResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetBooted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetReady(ctx context.Context, in *GetReadyRequest, opts ...grpc.CallOption) (*GetReadyResponse, error) {
	out := new(GetReadyResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetReady", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetDeepweedOutputByTimestamp(ctx context.Context, in *GetDeepweedOutputByTimestampRequest, opts ...grpc.CallOption) (*DeepweedOutput, error) {
	out := new(DeepweedOutput)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetRecommendedStrobeSettings(ctx context.Context, in *GetRecommendedStrobeSettingsRequest, opts ...grpc.CallOption) (*GetRecommendedStrobeSettingsResponse, error) {
	out := new(GetRecommendedStrobeSettingsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) P2PCapture(ctx context.Context, in *P2PCaptureRequest, opts ...grpc.CallOption) (*P2PCaptureResponse, error) {
	out := new(P2PCaptureResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/P2PCapture", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetAutoWhitebalance(ctx context.Context, in *SetAutoWhitebalanceRequest, opts ...grpc.CallOption) (*SetAutoWhitebalanceResponse, error) {
	out := new(SetAutoWhitebalanceResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetNextDeepweedOutput(ctx context.Context, in *GetNextDeepweedOutputRequest, opts ...grpc.CallOption) (*DeepweedOutput, error) {
	out := new(DeepweedOutput)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetNextP2POutput(ctx context.Context, in *GetNextP2POutputRequest, opts ...grpc.CallOption) (*P2POutputProto, error) {
	out := new(P2POutputProto)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetNextP2POutput", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SetTargetingState(ctx context.Context, in *SetTargetingStateRequest, opts ...grpc.CallOption) (*SetTargetingStateResponse, error) {
	out := new(SetTargetingStateResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SetTargetingState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) P2PBufferringBurstCapture(ctx context.Context, in *P2PBufferringBurstCaptureRequest, opts ...grpc.CallOption) (*P2PBufferringBurstCaptureResponse, error) {
	out := new(P2PBufferringBurstCaptureResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetNextFocusMetric(ctx context.Context, in *GetNextFocusMetricRequest, opts ...grpc.CallOption) (*GetNextFocusMetricResponse, error) {
	out := new(GetNextFocusMetricResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) RemoveDataDir(ctx context.Context, in *RemoveDataDirRequest, opts ...grpc.CallOption) (*RemoveDataDirResponse, error) {
	out := new(RemoveDataDirResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/RemoveDataDir", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetLastNImages(ctx context.Context, in *LastNImageRequest, opts ...grpc.CallOption) (CVRuntimeService_GetLastNImagesClient, error) {
	stream, err := c.cc.NewStream(ctx, &CVRuntimeService_ServiceDesc.Streams[0], "/cv.runtime.proto.CVRuntimeService/GetLastNImages", opts...)
	if err != nil {
		return nil, err
	}
	x := &cVRuntimeServiceGetLastNImagesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type CVRuntimeService_GetLastNImagesClient interface {
	Recv() (*ImageAndMetadataResponse, error)
	grpc.ClientStream
}

type cVRuntimeServiceGetLastNImagesClient struct {
	grpc.ClientStream
}

func (x *cVRuntimeServiceGetLastNImagesClient) Recv() (*ImageAndMetadataResponse, error) {
	m := new(ImageAndMetadataResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *cVRuntimeServiceClient) GetComputeCapabilities(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ComputeCapabilitiesResponse, error) {
	out := new(ComputeCapabilitiesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetSupportedTensorRTVersions(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SupportedTensorRTVersionsResponse, error) {
	out := new(SupportedTensorRTVersionsResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) ReloadCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetCategoryCollection(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetCategoryCollectionResponse, error) {
	out := new(GetCategoryCollectionResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetCategoryCollection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetErrorState(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetErrorStateResponse, error) {
	out := new(GetErrorStateResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetErrorState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) SnapshotPredictImages(ctx context.Context, in *SnapshotPredictImagesRequest, opts ...grpc.CallOption) (*SnapshotPredictImagesResponse, error) {
	out := new(SnapshotPredictImagesResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cVRuntimeServiceClient) GetChipForPredictImage(ctx context.Context, in *GetChipForPredictImageRequest, opts ...grpc.CallOption) (*GetChipForPredictImageResponse, error) {
	out := new(GetChipForPredictImageResponse)
	err := c.cc.Invoke(ctx, "/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CVRuntimeServiceServer is the server API for CVRuntimeService service.
// All implementations must embed UnimplementedCVRuntimeServiceServer
// for forward compatibility
type CVRuntimeServiceServer interface {
	SetP2PContext(context.Context, *SetP2PContextRequest) (*SetP2PContextResponse, error)
	GetCameraDimensions(context.Context, *GetCameraDimensionsRequest) (*GetCameraDimensionsResponse, error)
	GetCameraInfo(context.Context, *GetCameraInfoRequest) (*GetCameraInfoResponse, error)
	GetDeepweedIndexToCategory(context.Context, *GetDeepweedIndexToCategoryRequest) (*GetDeepweedIndexToCategoryResponse, error)
	SetDeepweedDetectionCriteria(context.Context, *SetDeepweedDetectionCriteriaRequest) (*SetDeepweedDetectionCriteriaResponse, error)
	GetDeepweedDetectionCriteria(context.Context, *GetDeepweedDetectionCriteriaRequest) (*GetDeepweedDetectionCriteriaResponse, error)
	GetDeepweedSupportedCategories(context.Context, *GetDeepweedSupportedCategoriesRequest) (*GetDeepweedSupportedCategoriesResponse, error)
	GetCameraTemperatures(context.Context, *GetCameraTemperaturesRequest) (*GetCameraTemperaturesResponse, error)
	SetCameraSettings(context.Context, *SetCameraSettingsRequest) (*SetCameraSettingsResponse, error)
	GetCameraSettings(context.Context, *GetCameraSettingsRequest) (*GetCameraSettingsResponse, error)
	StartBurstRecordFrames(context.Context, *StartBurstRecordFramesRequest) (*StartBurstRecordFramesResponse, error)
	StopBurstRecordFrames(context.Context, *StopBurstRecordFramesRequest) (*StopBurstRecordFramesResponse, error)
	GetConnectors(context.Context, *GetConnectorsRequest) (*GetConnectorsResponse, error)
	SetConnectors(context.Context, *SetConnectorsRequest) (*SetConnectorsResponse, error)
	GetTiming(context.Context, *GetTimingRequest) (*GetTimingResponse, error)
	Predict(context.Context, *PredictRequest) (*PredictResponse, error)
	LoadAndQueue(context.Context, *LoadAndQueueRequest) (*LoadAndQueueResponse, error)
	SetImage(context.Context, *SetImageRequest) (*SetImageResponse, error)
	UnsetImage(context.Context, *UnsetImageRequest) (*UnsetImageResponse, error)
	GetModelPaths(context.Context, *GetModelPathsRequest) (*GetModelPathsResponse, error)
	SetGPSLocation(context.Context, *SetGPSLocationRequest) (*SetGPSLocationResponse, error)
	SetImplementStatus(context.Context, *SetImplementStatusRequest) (*SetImplementStatusResponse, error)
	SetImageScore(context.Context, *SetImageScoreRequest) (*SetImageScoreResponse, error)
	GetScoreQueue(context.Context, *GetScoreQueueRequest) (*GetScoreQueueResponse, error)
	ListScoreQueues(context.Context, *Empty) (*ListScoreQueuesResponse, error)
	GetMaxImageScore(context.Context, *GetMaxImageScoreRequest) (*GetMaxImageScoreResponse, error)
	GetMaxScoredImage(context.Context, *GetMaxScoredImageRequest) (*ImageAndMetadataResponse, error)
	GetLatestP2PImage(context.Context, *GetLatestP2PImageRequest) (*P2PImageAndMetadataResponse, error)
	GetChipImage(context.Context, *GetChipImageRequest) (*ChipImageAndMetadataResponse, error)
	GetChipQueueInformation(context.Context, *ChipQueueInformationRequest) (*ChipQueueInformationResponse, error)
	FlushQueues(context.Context, *FlushQueuesRequest) (*FlushQueuesResponse, error)
	GetLatestImage(context.Context, *GetLatestImageRequest) (*ImageAndMetadataResponse, error)
	GetImageNearTimestamp(context.Context, *GetImageNearTimestampRequest) (*ImageAndMetadataResponse, error)
	GetLightweightBurstRecord(context.Context, *GetLightweightBurstRecordRequest) (*GetLightweightBurstRecordResponse, error)
	GetBooted(context.Context, *GetBootedRequest) (*GetBootedResponse, error)
	GetReady(context.Context, *GetReadyRequest) (*GetReadyResponse, error)
	GetDeepweedOutputByTimestamp(context.Context, *GetDeepweedOutputByTimestampRequest) (*DeepweedOutput, error)
	GetRecommendedStrobeSettings(context.Context, *GetRecommendedStrobeSettingsRequest) (*GetRecommendedStrobeSettingsResponse, error)
	P2PCapture(context.Context, *P2PCaptureRequest) (*P2PCaptureResponse, error)
	SetAutoWhitebalance(context.Context, *SetAutoWhitebalanceRequest) (*SetAutoWhitebalanceResponse, error)
	GetNextDeepweedOutput(context.Context, *GetNextDeepweedOutputRequest) (*DeepweedOutput, error)
	GetNextP2POutput(context.Context, *GetNextP2POutputRequest) (*P2POutputProto, error)
	SetTargetingState(context.Context, *SetTargetingStateRequest) (*SetTargetingStateResponse, error)
	P2PBufferringBurstCapture(context.Context, *P2PBufferringBurstCaptureRequest) (*P2PBufferringBurstCaptureResponse, error)
	GetNextFocusMetric(context.Context, *GetNextFocusMetricRequest) (*GetNextFocusMetricResponse, error)
	RemoveDataDir(context.Context, *RemoveDataDirRequest) (*RemoveDataDirResponse, error)
	GetLastNImages(*LastNImageRequest, CVRuntimeService_GetLastNImagesServer) error
	GetComputeCapabilities(context.Context, *Empty) (*ComputeCapabilitiesResponse, error)
	GetSupportedTensorRTVersions(context.Context, *Empty) (*SupportedTensorRTVersionsResponse, error)
	ReloadCategoryCollection(context.Context, *Empty) (*Empty, error)
	GetCategoryCollection(context.Context, *Empty) (*GetCategoryCollectionResponse, error)
	GetErrorState(context.Context, *Empty) (*GetErrorStateResponse, error)
	SnapshotPredictImages(context.Context, *SnapshotPredictImagesRequest) (*SnapshotPredictImagesResponse, error)
	GetChipForPredictImage(context.Context, *GetChipForPredictImageRequest) (*GetChipForPredictImageResponse, error)
	mustEmbedUnimplementedCVRuntimeServiceServer()
}

// UnimplementedCVRuntimeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCVRuntimeServiceServer struct {
}

func (UnimplementedCVRuntimeServiceServer) SetP2PContext(context.Context, *SetP2PContextRequest) (*SetP2PContextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetP2PContext not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetCameraDimensions(context.Context, *GetCameraDimensionsRequest) (*GetCameraDimensionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraDimensions not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetCameraInfo(context.Context, *GetCameraInfoRequest) (*GetCameraInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraInfo not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetDeepweedIndexToCategory(context.Context, *GetDeepweedIndexToCategoryRequest) (*GetDeepweedIndexToCategoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedIndexToCategory not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetDeepweedDetectionCriteria(context.Context, *SetDeepweedDetectionCriteriaRequest) (*SetDeepweedDetectionCriteriaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDeepweedDetectionCriteria not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetDeepweedDetectionCriteria(context.Context, *GetDeepweedDetectionCriteriaRequest) (*GetDeepweedDetectionCriteriaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedDetectionCriteria not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetDeepweedSupportedCategories(context.Context, *GetDeepweedSupportedCategoriesRequest) (*GetDeepweedSupportedCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedSupportedCategories not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetCameraTemperatures(context.Context, *GetCameraTemperaturesRequest) (*GetCameraTemperaturesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraTemperatures not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetCameraSettings(context.Context, *SetCameraSettingsRequest) (*SetCameraSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCameraSettings not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetCameraSettings(context.Context, *GetCameraSettingsRequest) (*GetCameraSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCameraSettings not implemented")
}
func (UnimplementedCVRuntimeServiceServer) StartBurstRecordFrames(context.Context, *StartBurstRecordFramesRequest) (*StartBurstRecordFramesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBurstRecordFrames not implemented")
}
func (UnimplementedCVRuntimeServiceServer) StopBurstRecordFrames(context.Context, *StopBurstRecordFramesRequest) (*StopBurstRecordFramesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBurstRecordFrames not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetConnectors(context.Context, *GetConnectorsRequest) (*GetConnectorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConnectors not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetConnectors(context.Context, *SetConnectorsRequest) (*SetConnectorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetConnectors not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetTiming(context.Context, *GetTimingRequest) (*GetTimingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTiming not implemented")
}
func (UnimplementedCVRuntimeServiceServer) Predict(context.Context, *PredictRequest) (*PredictResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Predict not implemented")
}
func (UnimplementedCVRuntimeServiceServer) LoadAndQueue(context.Context, *LoadAndQueueRequest) (*LoadAndQueueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadAndQueue not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetImage(context.Context, *SetImageRequest) (*SetImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) UnsetImage(context.Context, *UnsetImageRequest) (*UnsetImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnsetImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetModelPaths(context.Context, *GetModelPathsRequest) (*GetModelPathsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelPaths not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetGPSLocation(context.Context, *SetGPSLocationRequest) (*SetGPSLocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGPSLocation not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetImplementStatus(context.Context, *SetImplementStatusRequest) (*SetImplementStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetImplementStatus not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetImageScore(context.Context, *SetImageScoreRequest) (*SetImageScoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetImageScore not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetScoreQueue(context.Context, *GetScoreQueueRequest) (*GetScoreQueueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScoreQueue not implemented")
}
func (UnimplementedCVRuntimeServiceServer) ListScoreQueues(context.Context, *Empty) (*ListScoreQueuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListScoreQueues not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetMaxImageScore(context.Context, *GetMaxImageScoreRequest) (*GetMaxImageScoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaxImageScore not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetMaxScoredImage(context.Context, *GetMaxScoredImageRequest) (*ImageAndMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMaxScoredImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetLatestP2PImage(context.Context, *GetLatestP2PImageRequest) (*P2PImageAndMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestP2PImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetChipImage(context.Context, *GetChipImageRequest) (*ChipImageAndMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChipImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetChipQueueInformation(context.Context, *ChipQueueInformationRequest) (*ChipQueueInformationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChipQueueInformation not implemented")
}
func (UnimplementedCVRuntimeServiceServer) FlushQueues(context.Context, *FlushQueuesRequest) (*FlushQueuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushQueues not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetLatestImage(context.Context, *GetLatestImageRequest) (*ImageAndMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetImageNearTimestamp(context.Context, *GetImageNearTimestampRequest) (*ImageAndMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetImageNearTimestamp not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetLightweightBurstRecord(context.Context, *GetLightweightBurstRecordRequest) (*GetLightweightBurstRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLightweightBurstRecord not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetBooted(context.Context, *GetBootedRequest) (*GetBootedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBooted not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetReady(context.Context, *GetReadyRequest) (*GetReadyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReady not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetDeepweedOutputByTimestamp(context.Context, *GetDeepweedOutputByTimestampRequest) (*DeepweedOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedOutputByTimestamp not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetRecommendedStrobeSettings(context.Context, *GetRecommendedStrobeSettingsRequest) (*GetRecommendedStrobeSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendedStrobeSettings not implemented")
}
func (UnimplementedCVRuntimeServiceServer) P2PCapture(context.Context, *P2PCaptureRequest) (*P2PCaptureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method P2PCapture not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetAutoWhitebalance(context.Context, *SetAutoWhitebalanceRequest) (*SetAutoWhitebalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAutoWhitebalance not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetNextDeepweedOutput(context.Context, *GetNextDeepweedOutputRequest) (*DeepweedOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextDeepweedOutput not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetNextP2POutput(context.Context, *GetNextP2POutputRequest) (*P2POutputProto, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextP2POutput not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SetTargetingState(context.Context, *SetTargetingStateRequest) (*SetTargetingStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTargetingState not implemented")
}
func (UnimplementedCVRuntimeServiceServer) P2PBufferringBurstCapture(context.Context, *P2PBufferringBurstCaptureRequest) (*P2PBufferringBurstCaptureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method P2PBufferringBurstCapture not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetNextFocusMetric(context.Context, *GetNextFocusMetricRequest) (*GetNextFocusMetricResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextFocusMetric not implemented")
}
func (UnimplementedCVRuntimeServiceServer) RemoveDataDir(context.Context, *RemoveDataDirRequest) (*RemoveDataDirResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveDataDir not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetLastNImages(*LastNImageRequest, CVRuntimeService_GetLastNImagesServer) error {
	return status.Errorf(codes.Unimplemented, "method GetLastNImages not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetComputeCapabilities(context.Context, *Empty) (*ComputeCapabilitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetComputeCapabilities not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetSupportedTensorRTVersions(context.Context, *Empty) (*SupportedTensorRTVersionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportedTensorRTVersions not implemented")
}
func (UnimplementedCVRuntimeServiceServer) ReloadCategoryCollection(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadCategoryCollection not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetCategoryCollection(context.Context, *Empty) (*GetCategoryCollectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategoryCollection not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetErrorState(context.Context, *Empty) (*GetErrorStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetErrorState not implemented")
}
func (UnimplementedCVRuntimeServiceServer) SnapshotPredictImages(context.Context, *SnapshotPredictImagesRequest) (*SnapshotPredictImagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SnapshotPredictImages not implemented")
}
func (UnimplementedCVRuntimeServiceServer) GetChipForPredictImage(context.Context, *GetChipForPredictImageRequest) (*GetChipForPredictImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChipForPredictImage not implemented")
}
func (UnimplementedCVRuntimeServiceServer) mustEmbedUnimplementedCVRuntimeServiceServer() {}

// UnsafeCVRuntimeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CVRuntimeServiceServer will
// result in compilation errors.
type UnsafeCVRuntimeServiceServer interface {
	mustEmbedUnimplementedCVRuntimeServiceServer()
}

func RegisterCVRuntimeServiceServer(s grpc.ServiceRegistrar, srv CVRuntimeServiceServer) {
	s.RegisterService(&CVRuntimeService_ServiceDesc, srv)
}

func _CVRuntimeService_SetP2PContext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetP2PContextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetP2PContext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetP2PContext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetP2PContext(ctx, req.(*SetP2PContextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetCameraDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCameraDimensionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetCameraDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetCameraDimensions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetCameraDimensions(ctx, req.(*GetCameraDimensionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetCameraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCameraInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetCameraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetCameraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetCameraInfo(ctx, req.(*GetCameraInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetDeepweedIndexToCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedIndexToCategoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetDeepweedIndexToCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetDeepweedIndexToCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetDeepweedIndexToCategory(ctx, req.(*GetDeepweedIndexToCategoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetDeepweedDetectionCriteria_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDeepweedDetectionCriteriaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetDeepweedDetectionCriteria(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetDeepweedDetectionCriteria",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetDeepweedDetectionCriteria(ctx, req.(*SetDeepweedDetectionCriteriaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetDeepweedDetectionCriteria_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedDetectionCriteriaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetDeepweedDetectionCriteria(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetDeepweedDetectionCriteria",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetDeepweedDetectionCriteria(ctx, req.(*GetDeepweedDetectionCriteriaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetDeepweedSupportedCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedSupportedCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetDeepweedSupportedCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetDeepweedSupportedCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetDeepweedSupportedCategories(ctx, req.(*GetDeepweedSupportedCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetCameraTemperatures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCameraTemperaturesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetCameraTemperatures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetCameraTemperatures",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetCameraTemperatures(ctx, req.(*GetCameraTemperaturesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetCameraSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCameraSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetCameraSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetCameraSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetCameraSettings(ctx, req.(*SetCameraSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetCameraSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCameraSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetCameraSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetCameraSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetCameraSettings(ctx, req.(*GetCameraSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_StartBurstRecordFrames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartBurstRecordFramesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).StartBurstRecordFrames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/StartBurstRecordFrames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).StartBurstRecordFrames(ctx, req.(*StartBurstRecordFramesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_StopBurstRecordFrames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopBurstRecordFramesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).StopBurstRecordFrames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/StopBurstRecordFrames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).StopBurstRecordFrames(ctx, req.(*StopBurstRecordFramesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetConnectors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConnectorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetConnectors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetConnectors",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetConnectors(ctx, req.(*GetConnectorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetConnectors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetConnectorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetConnectors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetConnectors",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetConnectors(ctx, req.(*SetConnectorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetTiming_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTimingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetTiming(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetTiming",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetTiming(ctx, req.(*GetTimingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_Predict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PredictRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).Predict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/Predict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).Predict(ctx, req.(*PredictRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_LoadAndQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadAndQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).LoadAndQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/LoadAndQueue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).LoadAndQueue(ctx, req.(*LoadAndQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetImage(ctx, req.(*SetImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_UnsetImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsetImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).UnsetImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/UnsetImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).UnsetImage(ctx, req.(*UnsetImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetModelPaths_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModelPathsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetModelPaths(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetModelPaths",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetModelPaths(ctx, req.(*GetModelPathsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetGPSLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGPSLocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetGPSLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetGPSLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetGPSLocation(ctx, req.(*SetGPSLocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetImplementStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetImplementStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetImplementStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetImplementStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetImplementStatus(ctx, req.(*SetImplementStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetImageScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetImageScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetImageScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetImageScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetImageScore(ctx, req.(*SetImageScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetScoreQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScoreQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetScoreQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetScoreQueue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetScoreQueue(ctx, req.(*GetScoreQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_ListScoreQueues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).ListScoreQueues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/ListScoreQueues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).ListScoreQueues(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetMaxImageScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaxImageScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetMaxImageScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetMaxImageScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetMaxImageScore(ctx, req.(*GetMaxImageScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetMaxScoredImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMaxScoredImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetMaxScoredImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetMaxScoredImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetMaxScoredImage(ctx, req.(*GetMaxScoredImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetLatestP2PImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestP2PImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetLatestP2PImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetLatestP2PImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetLatestP2PImage(ctx, req.(*GetLatestP2PImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetChipImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChipImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetChipImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetChipImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetChipImage(ctx, req.(*GetChipImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetChipQueueInformation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChipQueueInformationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetChipQueueInformation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetChipQueueInformation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetChipQueueInformation(ctx, req.(*ChipQueueInformationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_FlushQueues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushQueuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).FlushQueues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/FlushQueues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).FlushQueues(ctx, req.(*FlushQueuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetLatestImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetLatestImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetLatestImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetLatestImage(ctx, req.(*GetLatestImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetImageNearTimestamp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetImageNearTimestampRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetImageNearTimestamp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetImageNearTimestamp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetImageNearTimestamp(ctx, req.(*GetImageNearTimestampRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetLightweightBurstRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLightweightBurstRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetLightweightBurstRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetLightweightBurstRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetLightweightBurstRecord(ctx, req.(*GetLightweightBurstRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetBooted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBootedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetBooted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetBooted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetBooted(ctx, req.(*GetBootedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetReady_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReadyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetReady(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetReady",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetReady(ctx, req.(*GetReadyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetDeepweedOutputByTimestamp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedOutputByTimestampRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetDeepweedOutputByTimestamp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetDeepweedOutputByTimestamp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetDeepweedOutputByTimestamp(ctx, req.(*GetDeepweedOutputByTimestampRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetRecommendedStrobeSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendedStrobeSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetRecommendedStrobeSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetRecommendedStrobeSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetRecommendedStrobeSettings(ctx, req.(*GetRecommendedStrobeSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_P2PCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(P2PCaptureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).P2PCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/P2PCapture",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).P2PCapture(ctx, req.(*P2PCaptureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetAutoWhitebalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAutoWhitebalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetAutoWhitebalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetAutoWhitebalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetAutoWhitebalance(ctx, req.(*SetAutoWhitebalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetNextDeepweedOutput_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextDeepweedOutputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetNextDeepweedOutput(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetNextDeepweedOutput",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetNextDeepweedOutput(ctx, req.(*GetNextDeepweedOutputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetNextP2POutput_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextP2POutputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetNextP2POutput(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetNextP2POutput",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetNextP2POutput(ctx, req.(*GetNextP2POutputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SetTargetingState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTargetingStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SetTargetingState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SetTargetingState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SetTargetingState(ctx, req.(*SetTargetingStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_P2PBufferringBurstCapture_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(P2PBufferringBurstCaptureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).P2PBufferringBurstCapture(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/P2PBufferringBurstCapture",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).P2PBufferringBurstCapture(ctx, req.(*P2PBufferringBurstCaptureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetNextFocusMetric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextFocusMetricRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetNextFocusMetric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetNextFocusMetric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetNextFocusMetric(ctx, req.(*GetNextFocusMetricRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_RemoveDataDir_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveDataDirRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).RemoveDataDir(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/RemoveDataDir",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).RemoveDataDir(ctx, req.(*RemoveDataDirRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetLastNImages_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(LastNImageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(CVRuntimeServiceServer).GetLastNImages(m, &cVRuntimeServiceGetLastNImagesServer{stream})
}

type CVRuntimeService_GetLastNImagesServer interface {
	Send(*ImageAndMetadataResponse) error
	grpc.ServerStream
}

type cVRuntimeServiceGetLastNImagesServer struct {
	grpc.ServerStream
}

func (x *cVRuntimeServiceGetLastNImagesServer) Send(m *ImageAndMetadataResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _CVRuntimeService_GetComputeCapabilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetComputeCapabilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetComputeCapabilities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetComputeCapabilities(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetSupportedTensorRTVersions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetSupportedTensorRTVersions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetSupportedTensorRTVersions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetSupportedTensorRTVersions(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_ReloadCategoryCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).ReloadCategoryCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/ReloadCategoryCollection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).ReloadCategoryCollection(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetCategoryCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetCategoryCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetCategoryCollection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetCategoryCollection(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetErrorState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetErrorState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetErrorState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetErrorState(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_SnapshotPredictImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SnapshotPredictImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).SnapshotPredictImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/SnapshotPredictImages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).SnapshotPredictImages(ctx, req.(*SnapshotPredictImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CVRuntimeService_GetChipForPredictImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChipForPredictImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CVRuntimeServiceServer).GetChipForPredictImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cv.runtime.proto.CVRuntimeService/GetChipForPredictImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CVRuntimeServiceServer).GetChipForPredictImage(ctx, req.(*GetChipForPredictImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CVRuntimeService_ServiceDesc is the grpc.ServiceDesc for CVRuntimeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CVRuntimeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cv.runtime.proto.CVRuntimeService",
	HandlerType: (*CVRuntimeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetP2PContext",
			Handler:    _CVRuntimeService_SetP2PContext_Handler,
		},
		{
			MethodName: "GetCameraDimensions",
			Handler:    _CVRuntimeService_GetCameraDimensions_Handler,
		},
		{
			MethodName: "GetCameraInfo",
			Handler:    _CVRuntimeService_GetCameraInfo_Handler,
		},
		{
			MethodName: "GetDeepweedIndexToCategory",
			Handler:    _CVRuntimeService_GetDeepweedIndexToCategory_Handler,
		},
		{
			MethodName: "SetDeepweedDetectionCriteria",
			Handler:    _CVRuntimeService_SetDeepweedDetectionCriteria_Handler,
		},
		{
			MethodName: "GetDeepweedDetectionCriteria",
			Handler:    _CVRuntimeService_GetDeepweedDetectionCriteria_Handler,
		},
		{
			MethodName: "GetDeepweedSupportedCategories",
			Handler:    _CVRuntimeService_GetDeepweedSupportedCategories_Handler,
		},
		{
			MethodName: "GetCameraTemperatures",
			Handler:    _CVRuntimeService_GetCameraTemperatures_Handler,
		},
		{
			MethodName: "SetCameraSettings",
			Handler:    _CVRuntimeService_SetCameraSettings_Handler,
		},
		{
			MethodName: "GetCameraSettings",
			Handler:    _CVRuntimeService_GetCameraSettings_Handler,
		},
		{
			MethodName: "StartBurstRecordFrames",
			Handler:    _CVRuntimeService_StartBurstRecordFrames_Handler,
		},
		{
			MethodName: "StopBurstRecordFrames",
			Handler:    _CVRuntimeService_StopBurstRecordFrames_Handler,
		},
		{
			MethodName: "GetConnectors",
			Handler:    _CVRuntimeService_GetConnectors_Handler,
		},
		{
			MethodName: "SetConnectors",
			Handler:    _CVRuntimeService_SetConnectors_Handler,
		},
		{
			MethodName: "GetTiming",
			Handler:    _CVRuntimeService_GetTiming_Handler,
		},
		{
			MethodName: "Predict",
			Handler:    _CVRuntimeService_Predict_Handler,
		},
		{
			MethodName: "LoadAndQueue",
			Handler:    _CVRuntimeService_LoadAndQueue_Handler,
		},
		{
			MethodName: "SetImage",
			Handler:    _CVRuntimeService_SetImage_Handler,
		},
		{
			MethodName: "UnsetImage",
			Handler:    _CVRuntimeService_UnsetImage_Handler,
		},
		{
			MethodName: "GetModelPaths",
			Handler:    _CVRuntimeService_GetModelPaths_Handler,
		},
		{
			MethodName: "SetGPSLocation",
			Handler:    _CVRuntimeService_SetGPSLocation_Handler,
		},
		{
			MethodName: "SetImplementStatus",
			Handler:    _CVRuntimeService_SetImplementStatus_Handler,
		},
		{
			MethodName: "SetImageScore",
			Handler:    _CVRuntimeService_SetImageScore_Handler,
		},
		{
			MethodName: "GetScoreQueue",
			Handler:    _CVRuntimeService_GetScoreQueue_Handler,
		},
		{
			MethodName: "ListScoreQueues",
			Handler:    _CVRuntimeService_ListScoreQueues_Handler,
		},
		{
			MethodName: "GetMaxImageScore",
			Handler:    _CVRuntimeService_GetMaxImageScore_Handler,
		},
		{
			MethodName: "GetMaxScoredImage",
			Handler:    _CVRuntimeService_GetMaxScoredImage_Handler,
		},
		{
			MethodName: "GetLatestP2PImage",
			Handler:    _CVRuntimeService_GetLatestP2PImage_Handler,
		},
		{
			MethodName: "GetChipImage",
			Handler:    _CVRuntimeService_GetChipImage_Handler,
		},
		{
			MethodName: "GetChipQueueInformation",
			Handler:    _CVRuntimeService_GetChipQueueInformation_Handler,
		},
		{
			MethodName: "FlushQueues",
			Handler:    _CVRuntimeService_FlushQueues_Handler,
		},
		{
			MethodName: "GetLatestImage",
			Handler:    _CVRuntimeService_GetLatestImage_Handler,
		},
		{
			MethodName: "GetImageNearTimestamp",
			Handler:    _CVRuntimeService_GetImageNearTimestamp_Handler,
		},
		{
			MethodName: "GetLightweightBurstRecord",
			Handler:    _CVRuntimeService_GetLightweightBurstRecord_Handler,
		},
		{
			MethodName: "GetBooted",
			Handler:    _CVRuntimeService_GetBooted_Handler,
		},
		{
			MethodName: "GetReady",
			Handler:    _CVRuntimeService_GetReady_Handler,
		},
		{
			MethodName: "GetDeepweedOutputByTimestamp",
			Handler:    _CVRuntimeService_GetDeepweedOutputByTimestamp_Handler,
		},
		{
			MethodName: "GetRecommendedStrobeSettings",
			Handler:    _CVRuntimeService_GetRecommendedStrobeSettings_Handler,
		},
		{
			MethodName: "P2PCapture",
			Handler:    _CVRuntimeService_P2PCapture_Handler,
		},
		{
			MethodName: "SetAutoWhitebalance",
			Handler:    _CVRuntimeService_SetAutoWhitebalance_Handler,
		},
		{
			MethodName: "GetNextDeepweedOutput",
			Handler:    _CVRuntimeService_GetNextDeepweedOutput_Handler,
		},
		{
			MethodName: "GetNextP2POutput",
			Handler:    _CVRuntimeService_GetNextP2POutput_Handler,
		},
		{
			MethodName: "SetTargetingState",
			Handler:    _CVRuntimeService_SetTargetingState_Handler,
		},
		{
			MethodName: "P2PBufferringBurstCapture",
			Handler:    _CVRuntimeService_P2PBufferringBurstCapture_Handler,
		},
		{
			MethodName: "GetNextFocusMetric",
			Handler:    _CVRuntimeService_GetNextFocusMetric_Handler,
		},
		{
			MethodName: "RemoveDataDir",
			Handler:    _CVRuntimeService_RemoveDataDir_Handler,
		},
		{
			MethodName: "GetComputeCapabilities",
			Handler:    _CVRuntimeService_GetComputeCapabilities_Handler,
		},
		{
			MethodName: "GetSupportedTensorRTVersions",
			Handler:    _CVRuntimeService_GetSupportedTensorRTVersions_Handler,
		},
		{
			MethodName: "ReloadCategoryCollection",
			Handler:    _CVRuntimeService_ReloadCategoryCollection_Handler,
		},
		{
			MethodName: "GetCategoryCollection",
			Handler:    _CVRuntimeService_GetCategoryCollection_Handler,
		},
		{
			MethodName: "GetErrorState",
			Handler:    _CVRuntimeService_GetErrorState_Handler,
		},
		{
			MethodName: "SnapshotPredictImages",
			Handler:    _CVRuntimeService_SnapshotPredictImages_Handler,
		},
		{
			MethodName: "GetChipForPredictImage",
			Handler:    _CVRuntimeService_GetChipForPredictImage_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetLastNImages",
			Handler:       _CVRuntimeService_GetLastNImages_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "cv/runtime/cv_runtime.proto",
}
