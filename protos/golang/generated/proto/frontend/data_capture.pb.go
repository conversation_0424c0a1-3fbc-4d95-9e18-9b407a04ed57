// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: frontend/data_capture.proto

package frontend

import (
	util "github.com/carbonrobotics/protos/golang/generated/proto/util"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadMethod int32

const (
	UploadMethod_WIRELESS UploadMethod = 0
	UploadMethod_USB      UploadMethod = 1
)

// Enum value maps for UploadMethod.
var (
	UploadMethod_name = map[int32]string{
		0: "WIRELESS",
		1: "USB",
	}
	UploadMethod_value = map[string]int32{
		"WIRELESS": 0,
		"USB":      1,
	}
)

func (x UploadMethod) Enum() *UploadMethod {
	p := new(UploadMethod)
	*p = x
	return p
}

func (x UploadMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_data_capture_proto_enumTypes[0].Descriptor()
}

func (UploadMethod) Type() protoreflect.EnumType {
	return &file_frontend_data_capture_proto_enumTypes[0]
}

func (x UploadMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadMethod.Descriptor instead.
func (UploadMethod) EnumDescriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{0}
}

type ProcedureStep int32

const (
	ProcedureStep_NEW                       ProcedureStep = 0
	ProcedureStep_CAPTURING                 ProcedureStep = 1
	ProcedureStep_CAPTURE_PAUSED            ProcedureStep = 2
	ProcedureStep_CAPTURE_COMPLETE          ProcedureStep = 3
	ProcedureStep_UPLOADING_WIRELESS        ProcedureStep = 4
	ProcedureStep_UPLOADING_WIRELESS_PAUSED ProcedureStep = 5
	ProcedureStep_UPLOADING_USB             ProcedureStep = 6
	ProcedureStep_UPLOADING_USB_PAUSED      ProcedureStep = 7
	ProcedureStep_UPLOADING_COMPLETE        ProcedureStep = 8
)

// Enum value maps for ProcedureStep.
var (
	ProcedureStep_name = map[int32]string{
		0: "NEW",
		1: "CAPTURING",
		2: "CAPTURE_PAUSED",
		3: "CAPTURE_COMPLETE",
		4: "UPLOADING_WIRELESS",
		5: "UPLOADING_WIRELESS_PAUSED",
		6: "UPLOADING_USB",
		7: "UPLOADING_USB_PAUSED",
		8: "UPLOADING_COMPLETE",
	}
	ProcedureStep_value = map[string]int32{
		"NEW":                       0,
		"CAPTURING":                 1,
		"CAPTURE_PAUSED":            2,
		"CAPTURE_COMPLETE":          3,
		"UPLOADING_WIRELESS":        4,
		"UPLOADING_WIRELESS_PAUSED": 5,
		"UPLOADING_USB":             6,
		"UPLOADING_USB_PAUSED":      7,
		"UPLOADING_COMPLETE":        8,
	}
)

func (x ProcedureStep) Enum() *ProcedureStep {
	p := new(ProcedureStep)
	*p = x
	return p
}

func (x ProcedureStep) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcedureStep) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_data_capture_proto_enumTypes[1].Descriptor()
}

func (ProcedureStep) Type() protoreflect.EnumType {
	return &file_frontend_data_capture_proto_enumTypes[1]
}

func (x ProcedureStep) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcedureStep.Descriptor instead.
func (ProcedureStep) EnumDescriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{1}
}

type DataCaptureRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rate float64 `protobuf:"fixed64,1,opt,name=rate,proto3" json:"rate,omitempty"` // feet per image
}

func (x *DataCaptureRate) Reset() {
	*x = DataCaptureRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataCaptureRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataCaptureRate) ProtoMessage() {}

func (x *DataCaptureRate) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataCaptureRate.ProtoReflect.Descriptor instead.
func (*DataCaptureRate) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{0}
}

func (x *DataCaptureRate) GetRate() float64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

type DataCaptureState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                              *util.Timestamp  `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ImagesTaken                     uint32           `protobuf:"varint,2,opt,name=images_taken,json=imagesTaken,proto3" json:"images_taken,omitempty"`
	TargetImagesTaken               uint32           `protobuf:"varint,3,opt,name=target_images_taken,json=targetImagesTaken,proto3" json:"target_images_taken,omitempty"`
	EstimatedCaptureRemainingTimeMs uint64           `protobuf:"varint,4,opt,name=estimated_capture_remaining_time_ms,json=estimatedCaptureRemainingTimeMs,proto3" json:"estimated_capture_remaining_time_ms,omitempty"`
	ImagesUploaded                  uint32           `protobuf:"varint,5,opt,name=images_uploaded,json=imagesUploaded,proto3" json:"images_uploaded,omitempty"`
	TargetImagesUploaded            uint32           `protobuf:"varint,6,opt,name=target_images_uploaded,json=targetImagesUploaded,proto3" json:"target_images_uploaded,omitempty"`
	EstimatedUploadRemainingTimeMs  uint64           `protobuf:"varint,7,opt,name=estimated_upload_remaining_time_ms,json=estimatedUploadRemainingTimeMs,proto3" json:"estimated_upload_remaining_time_ms,omitempty"`
	Rate                            *DataCaptureRate `protobuf:"bytes,8,opt,name=rate,proto3" json:"rate,omitempty"`
	WirelessUploadAvailable         bool             `protobuf:"varint,9,opt,name=wireless_upload_available,json=wirelessUploadAvailable,proto3" json:"wireless_upload_available,omitempty"`
	UsbStorageConnected             bool             `protobuf:"varint,10,opt,name=usb_storage_connected,json=usbStorageConnected,proto3" json:"usb_storage_connected,omitempty"`
	CaptureStatus                   string           `protobuf:"bytes,11,opt,name=capture_status,json=captureStatus,proto3" json:"capture_status,omitempty"`
	UploadStatus                    string           `protobuf:"bytes,12,opt,name=upload_status,json=uploadStatus,proto3" json:"upload_status,omitempty"`
	SessionName                     string           `protobuf:"bytes,13,opt,name=session_name,json=sessionName,proto3" json:"session_name,omitempty"`
	Step                            ProcedureStep    `protobuf:"varint,14,opt,name=step,proto3,enum=carbon.frontend.data_capture.ProcedureStep" json:"step,omitempty"`
	Crop                            string           `protobuf:"bytes,15,opt,name=crop,proto3" json:"crop,omitempty"`
	ErrorMessage                    string           `protobuf:"bytes,16,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	CropId                          string           `protobuf:"bytes,17,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
}

func (x *DataCaptureState) Reset() {
	*x = DataCaptureState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataCaptureState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataCaptureState) ProtoMessage() {}

func (x *DataCaptureState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataCaptureState.ProtoReflect.Descriptor instead.
func (*DataCaptureState) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{1}
}

func (x *DataCaptureState) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *DataCaptureState) GetImagesTaken() uint32 {
	if x != nil {
		return x.ImagesTaken
	}
	return 0
}

func (x *DataCaptureState) GetTargetImagesTaken() uint32 {
	if x != nil {
		return x.TargetImagesTaken
	}
	return 0
}

func (x *DataCaptureState) GetEstimatedCaptureRemainingTimeMs() uint64 {
	if x != nil {
		return x.EstimatedCaptureRemainingTimeMs
	}
	return 0
}

func (x *DataCaptureState) GetImagesUploaded() uint32 {
	if x != nil {
		return x.ImagesUploaded
	}
	return 0
}

func (x *DataCaptureState) GetTargetImagesUploaded() uint32 {
	if x != nil {
		return x.TargetImagesUploaded
	}
	return 0
}

func (x *DataCaptureState) GetEstimatedUploadRemainingTimeMs() uint64 {
	if x != nil {
		return x.EstimatedUploadRemainingTimeMs
	}
	return 0
}

func (x *DataCaptureState) GetRate() *DataCaptureRate {
	if x != nil {
		return x.Rate
	}
	return nil
}

func (x *DataCaptureState) GetWirelessUploadAvailable() bool {
	if x != nil {
		return x.WirelessUploadAvailable
	}
	return false
}

func (x *DataCaptureState) GetUsbStorageConnected() bool {
	if x != nil {
		return x.UsbStorageConnected
	}
	return false
}

func (x *DataCaptureState) GetCaptureStatus() string {
	if x != nil {
		return x.CaptureStatus
	}
	return ""
}

func (x *DataCaptureState) GetUploadStatus() string {
	if x != nil {
		return x.UploadStatus
	}
	return ""
}

func (x *DataCaptureState) GetSessionName() string {
	if x != nil {
		return x.SessionName
	}
	return ""
}

func (x *DataCaptureState) GetStep() ProcedureStep {
	if x != nil {
		return x.Step
	}
	return ProcedureStep_NEW
}

func (x *DataCaptureState) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *DataCaptureState) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *DataCaptureState) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

type DataCaptureSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DataCaptureSession) Reset() {
	*x = DataCaptureSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataCaptureSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataCaptureSession) ProtoMessage() {}

func (x *DataCaptureSession) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataCaptureSession.ProtoReflect.Descriptor instead.
func (*DataCaptureSession) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{2}
}

func (x *DataCaptureSession) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type StartDataCaptureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Rate        float64 `protobuf:"fixed64,2,opt,name=rate,proto3" json:"rate,omitempty"` // feet per image
	Crop        string  `protobuf:"bytes,3,opt,name=crop,proto3" json:"crop,omitempty"`
	CropId      string  `protobuf:"bytes,4,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	SnapCapture bool    `protobuf:"varint,5,opt,name=snap_capture,json=snapCapture,proto3" json:"snap_capture,omitempty"`
}

func (x *StartDataCaptureRequest) Reset() {
	*x = StartDataCaptureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDataCaptureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDataCaptureRequest) ProtoMessage() {}

func (x *StartDataCaptureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDataCaptureRequest.ProtoReflect.Descriptor instead.
func (*StartDataCaptureRequest) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{3}
}

func (x *StartDataCaptureRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StartDataCaptureRequest) GetRate() float64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *StartDataCaptureRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *StartDataCaptureRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *StartDataCaptureRequest) GetSnapCapture() bool {
	if x != nil {
		return x.SnapCapture
	}
	return false
}

type SnapImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crop        string  `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"`
	CropId      string  `protobuf:"bytes,2,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	CamId       *string `protobuf:"bytes,3,opt,name=cam_id,json=camId,proto3,oneof" json:"cam_id,omitempty"`
	TimestampMs *int64  `protobuf:"varint,4,opt,name=timestamp_ms,json=timestampMs,proto3,oneof" json:"timestamp_ms,omitempty"`
	SessionName string  `protobuf:"bytes,5,opt,name=session_name,json=sessionName,proto3" json:"session_name,omitempty"`
}

func (x *SnapImagesRequest) Reset() {
	*x = SnapImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapImagesRequest) ProtoMessage() {}

func (x *SnapImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapImagesRequest.ProtoReflect.Descriptor instead.
func (*SnapImagesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{4}
}

func (x *SnapImagesRequest) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *SnapImagesRequest) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *SnapImagesRequest) GetCamId() string {
	if x != nil && x.CamId != nil {
		return *x.CamId
	}
	return ""
}

func (x *SnapImagesRequest) GetTimestampMs() int64 {
	if x != nil && x.TimestampMs != nil {
		return *x.TimestampMs
	}
	return 0
}

func (x *SnapImagesRequest) GetSessionName() string {
	if x != nil {
		return x.SessionName
	}
	return ""
}

type Session struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name            string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ImagesRemaining uint32 `protobuf:"varint,2,opt,name=images_remaining,json=imagesRemaining,proto3" json:"images_remaining,omitempty"`
	IsUploading     bool   `protobuf:"varint,3,opt,name=is_uploading,json=isUploading,proto3" json:"is_uploading,omitempty"`
	HasCompleted    bool   `protobuf:"varint,4,opt,name=has_completed,json=hasCompleted,proto3" json:"has_completed,omitempty"`
	IsCapturing     bool   `protobuf:"varint,5,opt,name=is_capturing,json=isCapturing,proto3" json:"is_capturing,omitempty"`
}

func (x *Session) Reset() {
	*x = Session{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Session) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Session) ProtoMessage() {}

func (x *Session) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Session.ProtoReflect.Descriptor instead.
func (*Session) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{5}
}

func (x *Session) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Session) GetImagesRemaining() uint32 {
	if x != nil {
		return x.ImagesRemaining
	}
	return 0
}

func (x *Session) GetIsUploading() bool {
	if x != nil {
		return x.IsUploading
	}
	return false
}

func (x *Session) GetHasCompleted() bool {
	if x != nil {
		return x.HasCompleted
	}
	return false
}

func (x *Session) GetIsCapturing() bool {
	if x != nil {
		return x.IsCapturing
	}
	return false
}

type AvailableSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sessions []*Session `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
}

func (x *AvailableSessionResponse) Reset() {
	*x = AvailableSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvailableSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableSessionResponse) ProtoMessage() {}

func (x *AvailableSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableSessionResponse.ProtoReflect.Descriptor instead.
func (*AvailableSessionResponse) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{6}
}

func (x *AvailableSessionResponse) GetSessions() []*Session {
	if x != nil {
		return x.Sessions
	}
	return nil
}

type SessionName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SessionName) Reset() {
	*x = SessionName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionName) ProtoMessage() {}

func (x *SessionName) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionName.ProtoReflect.Descriptor instead.
func (*SessionName) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{7}
}

func (x *SessionName) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RegularCaptureStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uploaded            uint32 `protobuf:"varint,1,opt,name=uploaded,proto3" json:"uploaded,omitempty"`
	Budget              uint32 `protobuf:"varint,2,opt,name=budget,proto3" json:"budget,omitempty"`
	LastUploadTimestamp int64  `protobuf:"varint,3,opt,name=last_upload_timestamp,json=lastUploadTimestamp,proto3" json:"last_upload_timestamp,omitempty"`
}

func (x *RegularCaptureStatus) Reset() {
	*x = RegularCaptureStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_data_capture_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegularCaptureStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegularCaptureStatus) ProtoMessage() {}

func (x *RegularCaptureStatus) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_data_capture_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegularCaptureStatus.ProtoReflect.Descriptor instead.
func (*RegularCaptureStatus) Descriptor() ([]byte, []int) {
	return file_frontend_data_capture_proto_rawDescGZIP(), []int{8}
}

func (x *RegularCaptureStatus) GetUploaded() uint32 {
	if x != nil {
		return x.Uploaded
	}
	return 0
}

func (x *RegularCaptureStatus) GetBudget() uint32 {
	if x != nil {
		return x.Budget
	}
	return 0
}

func (x *RegularCaptureStatus) GetLastUploadTimestamp() int64 {
	if x != nil {
		return x.LastUploadTimestamp
	}
	return 0
}

var File_frontend_data_capture_proto protoreflect.FileDescriptor

var file_frontend_data_capture_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x1a, 0x0f, 0x75, 0x74, 0x69,
	0x6c, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x25, 0x0a, 0x0f,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x72,
	0x61, 0x74, 0x65, 0x22, 0xbb, 0x06, 0x0a, 0x10, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x54, 0x61,
	0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x54, 0x61,
	0x6b, 0x65, 0x6e, 0x12, 0x4c, 0x0a, 0x23, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x1f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64,
	0x12, 0x4a, 0x0a, 0x22, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x1e, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x41, 0x0a, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12,
	0x3a, 0x0a, 0x19, 0x77, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x17, 0x77, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x75,
	0x73, 0x62, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75, 0x73, 0x62, 0x53,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f,
	0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x64, 0x75, 0x72, 0x65, 0x53, 0x74, 0x65, 0x70, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x72, 0x6f, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49,
	0x64, 0x22, 0x28, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x17,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x72, 0x6f, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x6e, 0x61, 0x70, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x73, 0x6e, 0x61, 0x70, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x22,
	0xc3, 0x01, 0x0a, 0x11, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x4d, 0x73, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x63, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x5d, 0x0a, 0x18, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x21, 0x0a, 0x0b, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x7e, 0x0a,
	0x14, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2a, 0x25, 0x0a,
	0x0c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x0c, 0x0a,
	0x08, 0x57, 0x49, 0x52, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x55,
	0x53, 0x42, 0x10, 0x01, 0x2a, 0xcd, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x65, 0x70, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x45, 0x57, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x52, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x04,
	0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49,
	0x52, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44, 0x10, 0x05, 0x12,
	0x11, 0x0a, 0x0d, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x53, 0x42,
	0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x55, 0x53, 0x42, 0x5f, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12,
	0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x10, 0x08, 0x32, 0xa9, 0x0c, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x10, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3a, 0x0a, 0x10, 0x50, 0x61,
	0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x3b, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d,
	0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x48, 0x0a,
	0x1e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x57, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x43, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x55, 0x53, 0x42, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3f, 0x0a, 0x15,
	0x53, 0x74, 0x6f, 0x70, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x40, 0x0a,
	0x16, 0x50, 0x61, 0x75, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72,
	0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x41, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x69, 0x0a, 0x28, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x57, 0x69, 0x72, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x29,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x64, 0x0a,
	0x23, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x55, 0x53, 0x42, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x1a,
	0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x1f, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61,
	0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x20, 0x50, 0x61, 0x75, 0x73, 0x65, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74,
	0x75, 0x72, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x62, 0x0a, 0x21, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x29, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a,
	0x2e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x51, 0x0a, 0x0a, 0x53, 0x6e, 0x61, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x6e, 0x61,
	0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x59, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x36, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70,
	0x74, 0x75, 0x72, 0x65, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x32, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x43, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_data_capture_proto_rawDescOnce sync.Once
	file_frontend_data_capture_proto_rawDescData = file_frontend_data_capture_proto_rawDesc
)

func file_frontend_data_capture_proto_rawDescGZIP() []byte {
	file_frontend_data_capture_proto_rawDescOnce.Do(func() {
		file_frontend_data_capture_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_data_capture_proto_rawDescData)
	})
	return file_frontend_data_capture_proto_rawDescData
}

var file_frontend_data_capture_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_frontend_data_capture_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_frontend_data_capture_proto_goTypes = []interface{}{
	(UploadMethod)(0),                // 0: carbon.frontend.data_capture.UploadMethod
	(ProcedureStep)(0),               // 1: carbon.frontend.data_capture.ProcedureStep
	(*DataCaptureRate)(nil),          // 2: carbon.frontend.data_capture.DataCaptureRate
	(*DataCaptureState)(nil),         // 3: carbon.frontend.data_capture.DataCaptureState
	(*DataCaptureSession)(nil),       // 4: carbon.frontend.data_capture.DataCaptureSession
	(*StartDataCaptureRequest)(nil),  // 5: carbon.frontend.data_capture.StartDataCaptureRequest
	(*SnapImagesRequest)(nil),        // 6: carbon.frontend.data_capture.SnapImagesRequest
	(*Session)(nil),                  // 7: carbon.frontend.data_capture.Session
	(*AvailableSessionResponse)(nil), // 8: carbon.frontend.data_capture.AvailableSessionResponse
	(*SessionName)(nil),              // 9: carbon.frontend.data_capture.SessionName
	(*RegularCaptureStatus)(nil),     // 10: carbon.frontend.data_capture.RegularCaptureStatus
	(*util.Timestamp)(nil),           // 11: carbon.util.Timestamp
	(*util.Empty)(nil),               // 12: carbon.util.Empty
}
var file_frontend_data_capture_proto_depIdxs = []int32{
	11, // 0: carbon.frontend.data_capture.DataCaptureState.ts:type_name -> carbon.util.Timestamp
	2,  // 1: carbon.frontend.data_capture.DataCaptureState.rate:type_name -> carbon.frontend.data_capture.DataCaptureRate
	1,  // 2: carbon.frontend.data_capture.DataCaptureState.step:type_name -> carbon.frontend.data_capture.ProcedureStep
	7,  // 3: carbon.frontend.data_capture.AvailableSessionResponse.sessions:type_name -> carbon.frontend.data_capture.Session
	5,  // 4: carbon.frontend.data_capture.DataCaptureService.StartDataCapture:input_type -> carbon.frontend.data_capture.StartDataCaptureRequest
	12, // 5: carbon.frontend.data_capture.DataCaptureService.PauseDataCapture:input_type -> carbon.util.Empty
	12, // 6: carbon.frontend.data_capture.DataCaptureService.StopDataCapture:input_type -> carbon.util.Empty
	12, // 7: carbon.frontend.data_capture.DataCaptureService.ResumeDataCapture:input_type -> carbon.util.Empty
	12, // 8: carbon.frontend.data_capture.DataCaptureService.CompleteDataCapture:input_type -> carbon.util.Empty
	12, // 9: carbon.frontend.data_capture.DataCaptureService.StartDataCaptureWirelessUpload:input_type -> carbon.util.Empty
	12, // 10: carbon.frontend.data_capture.DataCaptureService.StartDataCaptureUSBUpload:input_type -> carbon.util.Empty
	12, // 11: carbon.frontend.data_capture.DataCaptureService.StopDataCaptureUpload:input_type -> carbon.util.Empty
	12, // 12: carbon.frontend.data_capture.DataCaptureService.PauseDataCaptureUpload:input_type -> carbon.util.Empty
	12, // 13: carbon.frontend.data_capture.DataCaptureService.ResumeDataCaptureUpload:input_type -> carbon.util.Empty
	9,  // 14: carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureWirelessUpload:input_type -> carbon.frontend.data_capture.SessionName
	9,  // 15: carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureUSBUpload:input_type -> carbon.frontend.data_capture.SessionName
	9,  // 16: carbon.frontend.data_capture.DataCaptureService.StopBackgroundDataCaptureUpload:input_type -> carbon.frontend.data_capture.SessionName
	9,  // 17: carbon.frontend.data_capture.DataCaptureService.PauseBackgroundDataCaptureUpload:input_type -> carbon.frontend.data_capture.SessionName
	9,  // 18: carbon.frontend.data_capture.DataCaptureService.ResumeBackgroundDataCaptureUpload:input_type -> carbon.frontend.data_capture.SessionName
	11, // 19: carbon.frontend.data_capture.DataCaptureService.GetNextDataCaptureState:input_type -> carbon.util.Timestamp
	6,  // 20: carbon.frontend.data_capture.DataCaptureService.SnapImages:input_type -> carbon.frontend.data_capture.SnapImagesRequest
	12, // 21: carbon.frontend.data_capture.DataCaptureService.GetSessions:input_type -> carbon.util.Empty
	12, // 22: carbon.frontend.data_capture.DataCaptureService.GetRegularCaptureStatus:input_type -> carbon.util.Empty
	12, // 23: carbon.frontend.data_capture.DataCaptureService.StartDataCapture:output_type -> carbon.util.Empty
	12, // 24: carbon.frontend.data_capture.DataCaptureService.PauseDataCapture:output_type -> carbon.util.Empty
	12, // 25: carbon.frontend.data_capture.DataCaptureService.StopDataCapture:output_type -> carbon.util.Empty
	12, // 26: carbon.frontend.data_capture.DataCaptureService.ResumeDataCapture:output_type -> carbon.util.Empty
	12, // 27: carbon.frontend.data_capture.DataCaptureService.CompleteDataCapture:output_type -> carbon.util.Empty
	12, // 28: carbon.frontend.data_capture.DataCaptureService.StartDataCaptureWirelessUpload:output_type -> carbon.util.Empty
	12, // 29: carbon.frontend.data_capture.DataCaptureService.StartDataCaptureUSBUpload:output_type -> carbon.util.Empty
	12, // 30: carbon.frontend.data_capture.DataCaptureService.StopDataCaptureUpload:output_type -> carbon.util.Empty
	12, // 31: carbon.frontend.data_capture.DataCaptureService.PauseDataCaptureUpload:output_type -> carbon.util.Empty
	12, // 32: carbon.frontend.data_capture.DataCaptureService.ResumeDataCaptureUpload:output_type -> carbon.util.Empty
	12, // 33: carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureWirelessUpload:output_type -> carbon.util.Empty
	12, // 34: carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureUSBUpload:output_type -> carbon.util.Empty
	12, // 35: carbon.frontend.data_capture.DataCaptureService.StopBackgroundDataCaptureUpload:output_type -> carbon.util.Empty
	12, // 36: carbon.frontend.data_capture.DataCaptureService.PauseBackgroundDataCaptureUpload:output_type -> carbon.util.Empty
	12, // 37: carbon.frontend.data_capture.DataCaptureService.ResumeBackgroundDataCaptureUpload:output_type -> carbon.util.Empty
	3,  // 38: carbon.frontend.data_capture.DataCaptureService.GetNextDataCaptureState:output_type -> carbon.frontend.data_capture.DataCaptureState
	12, // 39: carbon.frontend.data_capture.DataCaptureService.SnapImages:output_type -> carbon.util.Empty
	8,  // 40: carbon.frontend.data_capture.DataCaptureService.GetSessions:output_type -> carbon.frontend.data_capture.AvailableSessionResponse
	10, // 41: carbon.frontend.data_capture.DataCaptureService.GetRegularCaptureStatus:output_type -> carbon.frontend.data_capture.RegularCaptureStatus
	23, // [23:42] is the sub-list for method output_type
	4,  // [4:23] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_frontend_data_capture_proto_init() }
func file_frontend_data_capture_proto_init() {
	if File_frontend_data_capture_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_frontend_data_capture_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataCaptureRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataCaptureState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataCaptureSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDataCaptureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Session); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvailableSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_data_capture_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegularCaptureStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_frontend_data_capture_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_data_capture_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_data_capture_proto_goTypes,
		DependencyIndexes: file_frontend_data_capture_proto_depIdxs,
		EnumInfos:         file_frontend_data_capture_proto_enumTypes,
		MessageInfos:      file_frontend_data_capture_proto_msgTypes,
	}.Build()
	File_frontend_data_capture_proto = out.File
	file_frontend_data_capture_proto_rawDesc = nil
	file_frontend_data_capture_proto_goTypes = nil
	file_frontend_data_capture_proto_depIdxs = nil
}
