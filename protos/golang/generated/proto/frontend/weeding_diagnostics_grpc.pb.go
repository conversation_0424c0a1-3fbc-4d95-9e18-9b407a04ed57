// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: frontend/weeding_diagnostics.proto

package frontend

import (
	context "context"
	util "github.com/carbonrobotics/protos/golang/generated/proto/util"
	weed_tracking "github.com/carbonrobotics/protos/golang/generated/proto/weed_tracking"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WeedingDiagnosticsServiceClient is the client API for WeedingDiagnosticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WeedingDiagnosticsServiceClient interface {
	RecordWeedingDiagnostics(ctx context.Context, in *RecordWeedingDiagnosticsRequest, opts ...grpc.CallOption) (*util.Empty, error)
	GetCurrentTrajectories(ctx context.Context, in *GetCurrentTrajectoriesRequest, opts ...grpc.CallOption) (*weed_tracking.DiagnosticsSnapshot, error)
	GetRecordingsList(ctx context.Context, in *GetRecordingsListRequest, opts ...grpc.CallOption) (*GetRecordingsListResponse, error)
	OpenRecording(ctx context.Context, in *OpenRecordingRequest, opts ...grpc.CallOption) (*OpenRecordingResponse, error)
	GetSnapshot(ctx context.Context, in *GetSnapshotRequest, opts ...grpc.CallOption) (*GetSnapshotResponse, error)
	DeleteRecording(ctx context.Context, in *DeleteRecordingRequest, opts ...grpc.CallOption) (*util.Empty, error)
	GetTrajectoryData(ctx context.Context, in *GetTrajectoryDataRequest, opts ...grpc.CallOption) (*TrajectoryData, error)
	GetTrajectoryPredictImage(ctx context.Context, in *GetTrajectoryPredictImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetTrajectoryPredictImageClient, error)
	GetTrajectoryTargetImage(ctx context.Context, in *GetTrajectoryTargetImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetTrajectoryTargetImageClient, error)
	GetPredictImageMetadata(ctx context.Context, in *GetPredictImageMetadataRequest, opts ...grpc.CallOption) (*GetPredictImageMetadataResponse, error)
	GetPredictImage(ctx context.Context, in *GetPredictImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetPredictImageClient, error)
	StartUpload(ctx context.Context, in *StartUploadRequest, opts ...grpc.CallOption) (*util.Empty, error)
	GetNextUploadState(ctx context.Context, in *GetNextUploadStateRequest, opts ...grpc.CallOption) (*GetNextUploadStateResponse, error)
	GetDeepweedPredictionsCount(ctx context.Context, in *GetDeepweedPredictionsCountRequest, opts ...grpc.CallOption) (*GetDeepweedPredictionsCountResponse, error)
	GetDeepweedPredictions(ctx context.Context, in *GetDeepweedPredictionsRequest, opts ...grpc.CallOption) (*GetDeepweedPredictionsResponse, error)
	FindTrajectory(ctx context.Context, in *FindTrajectoryRequest, opts ...grpc.CallOption) (*FindTrajectoryResponse, error)
	GetRotaryTicks(ctx context.Context, in *GetRotaryTicksRequest, opts ...grpc.CallOption) (*GetRotaryTicksResponse, error)
	SnapshotPredictImages(ctx context.Context, in *SnapshotPredictImagesRequest, opts ...grpc.CallOption) (*SnapshotPredictImagesResponse, error)
	GetChipForPredictImage(ctx context.Context, in *GetChipForPredictImageRequest, opts ...grpc.CallOption) (*GetChipForPredictImageResponse, error)
	UploadChip(ctx context.Context, in *UploadChipRequest, opts ...grpc.CallOption) (*util.Empty, error)
}

type weedingDiagnosticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWeedingDiagnosticsServiceClient(cc grpc.ClientConnInterface) WeedingDiagnosticsServiceClient {
	return &weedingDiagnosticsServiceClient{cc}
}

func (c *weedingDiagnosticsServiceClient) RecordWeedingDiagnostics(ctx context.Context, in *RecordWeedingDiagnosticsRequest, opts ...grpc.CallOption) (*util.Empty, error) {
	out := new(util.Empty)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetCurrentTrajectories(ctx context.Context, in *GetCurrentTrajectoriesRequest, opts ...grpc.CallOption) (*weed_tracking.DiagnosticsSnapshot, error) {
	out := new(weed_tracking.DiagnosticsSnapshot)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetRecordingsList(ctx context.Context, in *GetRecordingsListRequest, opts ...grpc.CallOption) (*GetRecordingsListResponse, error) {
	out := new(GetRecordingsListResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) OpenRecording(ctx context.Context, in *OpenRecordingRequest, opts ...grpc.CallOption) (*OpenRecordingResponse, error) {
	out := new(OpenRecordingResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetSnapshot(ctx context.Context, in *GetSnapshotRequest, opts ...grpc.CallOption) (*GetSnapshotResponse, error) {
	out := new(GetSnapshotResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) DeleteRecording(ctx context.Context, in *DeleteRecordingRequest, opts ...grpc.CallOption) (*util.Empty, error) {
	out := new(util.Empty)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetTrajectoryData(ctx context.Context, in *GetTrajectoryDataRequest, opts ...grpc.CallOption) (*TrajectoryData, error) {
	out := new(TrajectoryData)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetTrajectoryPredictImage(ctx context.Context, in *GetTrajectoryPredictImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetTrajectoryPredictImageClient, error) {
	stream, err := c.cc.NewStream(ctx, &WeedingDiagnosticsService_ServiceDesc.Streams[0], "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryPredictImage", opts...)
	if err != nil {
		return nil, err
	}
	x := &weedingDiagnosticsServiceGetTrajectoryPredictImageClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type WeedingDiagnosticsService_GetTrajectoryPredictImageClient interface {
	Recv() (*ImageChunk, error)
	grpc.ClientStream
}

type weedingDiagnosticsServiceGetTrajectoryPredictImageClient struct {
	grpc.ClientStream
}

func (x *weedingDiagnosticsServiceGetTrajectoryPredictImageClient) Recv() (*ImageChunk, error) {
	m := new(ImageChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *weedingDiagnosticsServiceClient) GetTrajectoryTargetImage(ctx context.Context, in *GetTrajectoryTargetImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetTrajectoryTargetImageClient, error) {
	stream, err := c.cc.NewStream(ctx, &WeedingDiagnosticsService_ServiceDesc.Streams[1], "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryTargetImage", opts...)
	if err != nil {
		return nil, err
	}
	x := &weedingDiagnosticsServiceGetTrajectoryTargetImageClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type WeedingDiagnosticsService_GetTrajectoryTargetImageClient interface {
	Recv() (*ImageChunk, error)
	grpc.ClientStream
}

type weedingDiagnosticsServiceGetTrajectoryTargetImageClient struct {
	grpc.ClientStream
}

func (x *weedingDiagnosticsServiceGetTrajectoryTargetImageClient) Recv() (*ImageChunk, error) {
	m := new(ImageChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *weedingDiagnosticsServiceClient) GetPredictImageMetadata(ctx context.Context, in *GetPredictImageMetadataRequest, opts ...grpc.CallOption) (*GetPredictImageMetadataResponse, error) {
	out := new(GetPredictImageMetadataResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetPredictImage(ctx context.Context, in *GetPredictImageRequest, opts ...grpc.CallOption) (WeedingDiagnosticsService_GetPredictImageClient, error) {
	stream, err := c.cc.NewStream(ctx, &WeedingDiagnosticsService_ServiceDesc.Streams[2], "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImage", opts...)
	if err != nil {
		return nil, err
	}
	x := &weedingDiagnosticsServiceGetPredictImageClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type WeedingDiagnosticsService_GetPredictImageClient interface {
	Recv() (*ImageChunk, error)
	grpc.ClientStream
}

type weedingDiagnosticsServiceGetPredictImageClient struct {
	grpc.ClientStream
}

func (x *weedingDiagnosticsServiceGetPredictImageClient) Recv() (*ImageChunk, error) {
	m := new(ImageChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *weedingDiagnosticsServiceClient) StartUpload(ctx context.Context, in *StartUploadRequest, opts ...grpc.CallOption) (*util.Empty, error) {
	out := new(util.Empty)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetNextUploadState(ctx context.Context, in *GetNextUploadStateRequest, opts ...grpc.CallOption) (*GetNextUploadStateResponse, error) {
	out := new(GetNextUploadStateResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetDeepweedPredictionsCount(ctx context.Context, in *GetDeepweedPredictionsCountRequest, opts ...grpc.CallOption) (*GetDeepweedPredictionsCountResponse, error) {
	out := new(GetDeepweedPredictionsCountResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetDeepweedPredictions(ctx context.Context, in *GetDeepweedPredictionsRequest, opts ...grpc.CallOption) (*GetDeepweedPredictionsResponse, error) {
	out := new(GetDeepweedPredictionsResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) FindTrajectory(ctx context.Context, in *FindTrajectoryRequest, opts ...grpc.CallOption) (*FindTrajectoryResponse, error) {
	out := new(FindTrajectoryResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetRotaryTicks(ctx context.Context, in *GetRotaryTicksRequest, opts ...grpc.CallOption) (*GetRotaryTicksResponse, error) {
	out := new(GetRotaryTicksResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) SnapshotPredictImages(ctx context.Context, in *SnapshotPredictImagesRequest, opts ...grpc.CallOption) (*SnapshotPredictImagesResponse, error) {
	out := new(SnapshotPredictImagesResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) GetChipForPredictImage(ctx context.Context, in *GetChipForPredictImageRequest, opts ...grpc.CallOption) (*GetChipForPredictImageResponse, error) {
	out := new(GetChipForPredictImageResponse)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *weedingDiagnosticsServiceClient) UploadChip(ctx context.Context, in *UploadChipRequest, opts ...grpc.CallOption) (*util.Empty, error) {
	out := new(util.Empty)
	err := c.cc.Invoke(ctx, "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/UploadChip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WeedingDiagnosticsServiceServer is the server API for WeedingDiagnosticsService service.
// All implementations must embed UnimplementedWeedingDiagnosticsServiceServer
// for forward compatibility
type WeedingDiagnosticsServiceServer interface {
	RecordWeedingDiagnostics(context.Context, *RecordWeedingDiagnosticsRequest) (*util.Empty, error)
	GetCurrentTrajectories(context.Context, *GetCurrentTrajectoriesRequest) (*weed_tracking.DiagnosticsSnapshot, error)
	GetRecordingsList(context.Context, *GetRecordingsListRequest) (*GetRecordingsListResponse, error)
	OpenRecording(context.Context, *OpenRecordingRequest) (*OpenRecordingResponse, error)
	GetSnapshot(context.Context, *GetSnapshotRequest) (*GetSnapshotResponse, error)
	DeleteRecording(context.Context, *DeleteRecordingRequest) (*util.Empty, error)
	GetTrajectoryData(context.Context, *GetTrajectoryDataRequest) (*TrajectoryData, error)
	GetTrajectoryPredictImage(*GetTrajectoryPredictImageRequest, WeedingDiagnosticsService_GetTrajectoryPredictImageServer) error
	GetTrajectoryTargetImage(*GetTrajectoryTargetImageRequest, WeedingDiagnosticsService_GetTrajectoryTargetImageServer) error
	GetPredictImageMetadata(context.Context, *GetPredictImageMetadataRequest) (*GetPredictImageMetadataResponse, error)
	GetPredictImage(*GetPredictImageRequest, WeedingDiagnosticsService_GetPredictImageServer) error
	StartUpload(context.Context, *StartUploadRequest) (*util.Empty, error)
	GetNextUploadState(context.Context, *GetNextUploadStateRequest) (*GetNextUploadStateResponse, error)
	GetDeepweedPredictionsCount(context.Context, *GetDeepweedPredictionsCountRequest) (*GetDeepweedPredictionsCountResponse, error)
	GetDeepweedPredictions(context.Context, *GetDeepweedPredictionsRequest) (*GetDeepweedPredictionsResponse, error)
	FindTrajectory(context.Context, *FindTrajectoryRequest) (*FindTrajectoryResponse, error)
	GetRotaryTicks(context.Context, *GetRotaryTicksRequest) (*GetRotaryTicksResponse, error)
	SnapshotPredictImages(context.Context, *SnapshotPredictImagesRequest) (*SnapshotPredictImagesResponse, error)
	GetChipForPredictImage(context.Context, *GetChipForPredictImageRequest) (*GetChipForPredictImageResponse, error)
	UploadChip(context.Context, *UploadChipRequest) (*util.Empty, error)
	mustEmbedUnimplementedWeedingDiagnosticsServiceServer()
}

// UnimplementedWeedingDiagnosticsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWeedingDiagnosticsServiceServer struct {
}

func (UnimplementedWeedingDiagnosticsServiceServer) RecordWeedingDiagnostics(context.Context, *RecordWeedingDiagnosticsRequest) (*util.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordWeedingDiagnostics not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetCurrentTrajectories(context.Context, *GetCurrentTrajectoriesRequest) (*weed_tracking.DiagnosticsSnapshot, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrentTrajectories not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetRecordingsList(context.Context, *GetRecordingsListRequest) (*GetRecordingsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordingsList not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) OpenRecording(context.Context, *OpenRecordingRequest) (*OpenRecordingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenRecording not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetSnapshot(context.Context, *GetSnapshotRequest) (*GetSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSnapshot not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) DeleteRecording(context.Context, *DeleteRecordingRequest) (*util.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecording not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetTrajectoryData(context.Context, *GetTrajectoryDataRequest) (*TrajectoryData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrajectoryData not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetTrajectoryPredictImage(*GetTrajectoryPredictImageRequest, WeedingDiagnosticsService_GetTrajectoryPredictImageServer) error {
	return status.Errorf(codes.Unimplemented, "method GetTrajectoryPredictImage not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetTrajectoryTargetImage(*GetTrajectoryTargetImageRequest, WeedingDiagnosticsService_GetTrajectoryTargetImageServer) error {
	return status.Errorf(codes.Unimplemented, "method GetTrajectoryTargetImage not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetPredictImageMetadata(context.Context, *GetPredictImageMetadataRequest) (*GetPredictImageMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPredictImageMetadata not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetPredictImage(*GetPredictImageRequest, WeedingDiagnosticsService_GetPredictImageServer) error {
	return status.Errorf(codes.Unimplemented, "method GetPredictImage not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) StartUpload(context.Context, *StartUploadRequest) (*util.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartUpload not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetNextUploadState(context.Context, *GetNextUploadStateRequest) (*GetNextUploadStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextUploadState not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetDeepweedPredictionsCount(context.Context, *GetDeepweedPredictionsCountRequest) (*GetDeepweedPredictionsCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedPredictionsCount not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetDeepweedPredictions(context.Context, *GetDeepweedPredictionsRequest) (*GetDeepweedPredictionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeepweedPredictions not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) FindTrajectory(context.Context, *FindTrajectoryRequest) (*FindTrajectoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTrajectory not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetRotaryTicks(context.Context, *GetRotaryTicksRequest) (*GetRotaryTicksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRotaryTicks not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) SnapshotPredictImages(context.Context, *SnapshotPredictImagesRequest) (*SnapshotPredictImagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SnapshotPredictImages not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) GetChipForPredictImage(context.Context, *GetChipForPredictImageRequest) (*GetChipForPredictImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChipForPredictImage not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) UploadChip(context.Context, *UploadChipRequest) (*util.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadChip not implemented")
}
func (UnimplementedWeedingDiagnosticsServiceServer) mustEmbedUnimplementedWeedingDiagnosticsServiceServer() {
}

// UnsafeWeedingDiagnosticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WeedingDiagnosticsServiceServer will
// result in compilation errors.
type UnsafeWeedingDiagnosticsServiceServer interface {
	mustEmbedUnimplementedWeedingDiagnosticsServiceServer()
}

func RegisterWeedingDiagnosticsServiceServer(s grpc.ServiceRegistrar, srv WeedingDiagnosticsServiceServer) {
	s.RegisterService(&WeedingDiagnosticsService_ServiceDesc, srv)
}

func _WeedingDiagnosticsService_RecordWeedingDiagnostics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordWeedingDiagnosticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).RecordWeedingDiagnostics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).RecordWeedingDiagnostics(ctx, req.(*RecordWeedingDiagnosticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetCurrentTrajectories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrentTrajectoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetCurrentTrajectories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetCurrentTrajectories(ctx, req.(*GetCurrentTrajectoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetRecordingsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordingsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetRecordingsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetRecordingsList(ctx, req.(*GetRecordingsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_OpenRecording_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenRecordingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).OpenRecording(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).OpenRecording(ctx, req.(*OpenRecordingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetSnapshot(ctx, req.(*GetSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_DeleteRecording_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecordingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).DeleteRecording(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).DeleteRecording(ctx, req.(*DeleteRecordingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetTrajectoryData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrajectoryDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetTrajectoryData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetTrajectoryData(ctx, req.(*GetTrajectoryDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetTrajectoryPredictImage_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetTrajectoryPredictImageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(WeedingDiagnosticsServiceServer).GetTrajectoryPredictImage(m, &weedingDiagnosticsServiceGetTrajectoryPredictImageServer{stream})
}

type WeedingDiagnosticsService_GetTrajectoryPredictImageServer interface {
	Send(*ImageChunk) error
	grpc.ServerStream
}

type weedingDiagnosticsServiceGetTrajectoryPredictImageServer struct {
	grpc.ServerStream
}

func (x *weedingDiagnosticsServiceGetTrajectoryPredictImageServer) Send(m *ImageChunk) error {
	return x.ServerStream.SendMsg(m)
}

func _WeedingDiagnosticsService_GetTrajectoryTargetImage_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetTrajectoryTargetImageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(WeedingDiagnosticsServiceServer).GetTrajectoryTargetImage(m, &weedingDiagnosticsServiceGetTrajectoryTargetImageServer{stream})
}

type WeedingDiagnosticsService_GetTrajectoryTargetImageServer interface {
	Send(*ImageChunk) error
	grpc.ServerStream
}

type weedingDiagnosticsServiceGetTrajectoryTargetImageServer struct {
	grpc.ServerStream
}

func (x *weedingDiagnosticsServiceGetTrajectoryTargetImageServer) Send(m *ImageChunk) error {
	return x.ServerStream.SendMsg(m)
}

func _WeedingDiagnosticsService_GetPredictImageMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPredictImageMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetPredictImageMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetPredictImageMetadata(ctx, req.(*GetPredictImageMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetPredictImage_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetPredictImageRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(WeedingDiagnosticsServiceServer).GetPredictImage(m, &weedingDiagnosticsServiceGetPredictImageServer{stream})
}

type WeedingDiagnosticsService_GetPredictImageServer interface {
	Send(*ImageChunk) error
	grpc.ServerStream
}

type weedingDiagnosticsServiceGetPredictImageServer struct {
	grpc.ServerStream
}

func (x *weedingDiagnosticsServiceGetPredictImageServer) Send(m *ImageChunk) error {
	return x.ServerStream.SendMsg(m)
}

func _WeedingDiagnosticsService_StartUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartUploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).StartUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).StartUpload(ctx, req.(*StartUploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetNextUploadState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextUploadStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetNextUploadState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetNextUploadState(ctx, req.(*GetNextUploadStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetDeepweedPredictionsCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedPredictionsCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetDeepweedPredictionsCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetDeepweedPredictionsCount(ctx, req.(*GetDeepweedPredictionsCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetDeepweedPredictions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeepweedPredictionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetDeepweedPredictions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetDeepweedPredictions(ctx, req.(*GetDeepweedPredictionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_FindTrajectory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTrajectoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).FindTrajectory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).FindTrajectory(ctx, req.(*FindTrajectoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetRotaryTicks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRotaryTicksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetRotaryTicks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetRotaryTicks(ctx, req.(*GetRotaryTicksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_SnapshotPredictImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SnapshotPredictImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).SnapshotPredictImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).SnapshotPredictImages(ctx, req.(*SnapshotPredictImagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_GetChipForPredictImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChipForPredictImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).GetChipForPredictImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).GetChipForPredictImage(ctx, req.(*GetChipForPredictImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WeedingDiagnosticsService_UploadChip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadChipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WeedingDiagnosticsServiceServer).UploadChip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/UploadChip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WeedingDiagnosticsServiceServer).UploadChip(ctx, req.(*UploadChipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WeedingDiagnosticsService_ServiceDesc is the grpc.ServiceDesc for WeedingDiagnosticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WeedingDiagnosticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService",
	HandlerType: (*WeedingDiagnosticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordWeedingDiagnostics",
			Handler:    _WeedingDiagnosticsService_RecordWeedingDiagnostics_Handler,
		},
		{
			MethodName: "GetCurrentTrajectories",
			Handler:    _WeedingDiagnosticsService_GetCurrentTrajectories_Handler,
		},
		{
			MethodName: "GetRecordingsList",
			Handler:    _WeedingDiagnosticsService_GetRecordingsList_Handler,
		},
		{
			MethodName: "OpenRecording",
			Handler:    _WeedingDiagnosticsService_OpenRecording_Handler,
		},
		{
			MethodName: "GetSnapshot",
			Handler:    _WeedingDiagnosticsService_GetSnapshot_Handler,
		},
		{
			MethodName: "DeleteRecording",
			Handler:    _WeedingDiagnosticsService_DeleteRecording_Handler,
		},
		{
			MethodName: "GetTrajectoryData",
			Handler:    _WeedingDiagnosticsService_GetTrajectoryData_Handler,
		},
		{
			MethodName: "GetPredictImageMetadata",
			Handler:    _WeedingDiagnosticsService_GetPredictImageMetadata_Handler,
		},
		{
			MethodName: "StartUpload",
			Handler:    _WeedingDiagnosticsService_StartUpload_Handler,
		},
		{
			MethodName: "GetNextUploadState",
			Handler:    _WeedingDiagnosticsService_GetNextUploadState_Handler,
		},
		{
			MethodName: "GetDeepweedPredictionsCount",
			Handler:    _WeedingDiagnosticsService_GetDeepweedPredictionsCount_Handler,
		},
		{
			MethodName: "GetDeepweedPredictions",
			Handler:    _WeedingDiagnosticsService_GetDeepweedPredictions_Handler,
		},
		{
			MethodName: "FindTrajectory",
			Handler:    _WeedingDiagnosticsService_FindTrajectory_Handler,
		},
		{
			MethodName: "GetRotaryTicks",
			Handler:    _WeedingDiagnosticsService_GetRotaryTicks_Handler,
		},
		{
			MethodName: "SnapshotPredictImages",
			Handler:    _WeedingDiagnosticsService_SnapshotPredictImages_Handler,
		},
		{
			MethodName: "GetChipForPredictImage",
			Handler:    _WeedingDiagnosticsService_GetChipForPredictImage_Handler,
		},
		{
			MethodName: "UploadChip",
			Handler:    _WeedingDiagnosticsService_UploadChip_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetTrajectoryPredictImage",
			Handler:       _WeedingDiagnosticsService_GetTrajectoryPredictImage_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetTrajectoryTargetImage",
			Handler:       _WeedingDiagnosticsService_GetTrajectoryTargetImage_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetPredictImage",
			Handler:       _WeedingDiagnosticsService_GetPredictImage_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "frontend/weeding_diagnostics.proto",
}
