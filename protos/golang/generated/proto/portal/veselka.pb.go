// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/veselka.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Location         string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	RobotId          string `protobuf:"bytes,3,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	UseCase          string `protobuf:"bytes,4,opt,name=use_case,json=useCase,proto3" json:"use_case,omitempty"`
	Role             string `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
	Created          int64  `protobuf:"varint,6,opt,name=created,proto3" json:"created,omitempty"`
	Url              string `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	Height           int64  `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`
	Width            int64  `protobuf:"varint,9,opt,name=width,proto3" json:"width,omitempty"`
	Ppi              int64  `protobuf:"varint,10,opt,name=ppi,proto3" json:"ppi,omitempty"`
	Valid            int64  `protobuf:"varint,11,opt,name=valid,proto3" json:"valid,omitempty"`
	CapturedAt       int64  `protobuf:"varint,12,opt,name=captured_at,json=capturedAt,proto3" json:"captured_at,omitempty"`
	DetectionJson    string `protobuf:"bytes,13,opt,name=detection_json,json=detectionJson,proto3" json:"detection_json,omitempty"`
	ReasonJson       string `protobuf:"bytes,14,opt,name=reason_json,json=reasonJson,proto3" json:"reason_json,omitempty"`
	Priority         string `protobuf:"bytes,15,opt,name=priority,proto3" json:"priority,omitempty"`
	CamId            string `protobuf:"bytes,16,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	RowId            string `protobuf:"bytes,17,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	GeoJson          string `protobuf:"bytes,18,opt,name=geo_json,json=geoJson,proto3" json:"geo_json,omitempty"`
	Crop             string `protobuf:"bytes,19,opt,name=crop,proto3" json:"crop,omitempty"`
	ImageType        string `protobuf:"bytes,20,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	City             string `protobuf:"bytes,21,opt,name=city,proto3" json:"city,omitempty"`
	CorrectedHw      bool   `protobuf:"varint,22,opt,name=corrected_hw,json=correctedHw,proto3" json:"corrected_hw,omitempty"`
	SessionName      string `protobuf:"bytes,23,opt,name=session_name,json=sessionName,proto3" json:"session_name,omitempty"`
	CropId           string `protobuf:"bytes,24,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	FocusMetric      int64  `protobuf:"varint,25,opt,name=focus_metric,json=focusMetric,proto3" json:"focus_metric,omitempty"`
	Geohash          string `protobuf:"bytes,26,opt,name=geohash,proto3" json:"geohash,omitempty"`
	QuarantineReason string `protobuf:"bytes,27,opt,name=quarantine_reason,json=quarantineReason,proto3" json:"quarantine_reason,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{0}
}

func (x *Image) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Image) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Image) GetRobotId() string {
	if x != nil {
		return x.RobotId
	}
	return ""
}

func (x *Image) GetUseCase() string {
	if x != nil {
		return x.UseCase
	}
	return ""
}

func (x *Image) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Image) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Image) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Image) GetWidth() int64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Image) GetPpi() int64 {
	if x != nil {
		return x.Ppi
	}
	return 0
}

func (x *Image) GetValid() int64 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *Image) GetCapturedAt() int64 {
	if x != nil {
		return x.CapturedAt
	}
	return 0
}

func (x *Image) GetDetectionJson() string {
	if x != nil {
		return x.DetectionJson
	}
	return ""
}

func (x *Image) GetReasonJson() string {
	if x != nil {
		return x.ReasonJson
	}
	return ""
}

func (x *Image) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *Image) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *Image) GetRowId() string {
	if x != nil {
		return x.RowId
	}
	return ""
}

func (x *Image) GetGeoJson() string {
	if x != nil {
		return x.GeoJson
	}
	return ""
}

func (x *Image) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *Image) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *Image) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Image) GetCorrectedHw() bool {
	if x != nil {
		return x.CorrectedHw
	}
	return false
}

func (x *Image) GetSessionName() string {
	if x != nil {
		return x.SessionName
	}
	return ""
}

func (x *Image) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *Image) GetFocusMetric() int64 {
	if x != nil {
		return x.FocusMetric
	}
	return 0
}

func (x *Image) GetGeohash() string {
	if x != nil {
		return x.Geohash
	}
	return ""
}

func (x *Image) GetQuarantineReason() string {
	if x != nil {
		return x.QuarantineReason
	}
	return ""
}

type CropConfidence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db                *DB     `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	RobotId           int64   `protobuf:"varint,2,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	CropId            string  `protobuf:"bytes,3,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	Latitude          float32 `protobuf:"fixed32,4,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude         float32 `protobuf:"fixed32,5,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Precision         int64   `protobuf:"varint,6,opt,name=precision,proto3" json:"precision,omitempty"`
	NumTotalImages    int64   `protobuf:"varint,7,opt,name=num_total_images,json=numTotalImages,proto3" json:"num_total_images,omitempty"`
	NumRegionalImages int64   `protobuf:"varint,8,opt,name=num_regional_images,json=numRegionalImages,proto3" json:"num_regional_images,omitempty"`
	Confidence        string  `protobuf:"bytes,9,opt,name=confidence,proto3" json:"confidence,omitempty"`
}

func (x *CropConfidence) Reset() {
	*x = CropConfidence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CropConfidence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CropConfidence) ProtoMessage() {}

func (x *CropConfidence) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CropConfidence.ProtoReflect.Descriptor instead.
func (*CropConfidence) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{1}
}

func (x *CropConfidence) GetDb() *DB {
	if x != nil {
		return x.Db
	}
	return nil
}

func (x *CropConfidence) GetRobotId() int64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *CropConfidence) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *CropConfidence) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *CropConfidence) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *CropConfidence) GetPrecision() int64 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *CropConfidence) GetNumTotalImages() int64 {
	if x != nil {
		return x.NumTotalImages
	}
	return 0
}

func (x *CropConfidence) GetNumRegionalImages() int64 {
	if x != nil {
		return x.NumRegionalImages
	}
	return 0
}

func (x *CropConfidence) GetConfidence() string {
	if x != nil {
		return x.Confidence
	}
	return ""
}

type Crop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Archived       bool   `protobuf:"varint,1,opt,name=archived,proto3" json:"archived,omitempty"`
	CarbonName     string `protobuf:"bytes,2,opt,name=carbon_name,json=carbonName,proto3" json:"carbon_name,omitempty"`
	CommonName     string `protobuf:"bytes,3,opt,name=common_name,json=commonName,proto3" json:"common_name,omitempty"`
	Created        int64  `protobuf:"varint,4,opt,name=created,proto3" json:"created,omitempty"`
	Description    string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Id             string `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty"`
	LegacyCropName string `protobuf:"bytes,7,opt,name=legacy_crop_name,json=legacyCropName,proto3" json:"legacy_crop_name,omitempty"`
	Notes          string `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`
	Updated        int64  `protobuf:"varint,9,opt,name=updated,proto3" json:"updated,omitempty"`
}

func (x *Crop) Reset() {
	*x = Crop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Crop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Crop) ProtoMessage() {}

func (x *Crop) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Crop.ProtoReflect.Descriptor instead.
func (*Crop) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{2}
}

func (x *Crop) GetArchived() bool {
	if x != nil {
		return x.Archived
	}
	return false
}

func (x *Crop) GetCarbonName() string {
	if x != nil {
		return x.CarbonName
	}
	return ""
}

func (x *Crop) GetCommonName() string {
	if x != nil {
		return x.CommonName
	}
	return ""
}

func (x *Crop) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *Crop) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Crop) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Crop) GetLegacyCropName() string {
	if x != nil {
		return x.LegacyCropName
	}
	return ""
}

func (x *Crop) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

func (x *Crop) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

type RobotCrop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crop       *Crop           `protobuf:"bytes,1,opt,name=crop,proto3" json:"crop,omitempty"`
	Confidence *CropConfidence `protobuf:"bytes,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
}

func (x *RobotCrop) Reset() {
	*x = RobotCrop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotCrop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotCrop) ProtoMessage() {}

func (x *RobotCrop) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotCrop.ProtoReflect.Descriptor instead.
func (*RobotCrop) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{3}
}

func (x *RobotCrop) GetCrop() *Crop {
	if x != nil {
		return x.Crop
	}
	return nil
}

func (x *RobotCrop) GetConfidence() *CropConfidence {
	if x != nil {
		return x.Confidence
	}
	return nil
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                         string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Created                    int64    `protobuf:"varint,2,opt,name=created,proto3" json:"created,omitempty"`
	Updated                    int64    `protobuf:"varint,3,opt,name=updated,proto3" json:"updated,omitempty"`
	Url                        string   `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	Customer                   string   `protobuf:"bytes,5,opt,name=customer,proto3" json:"customer,omitempty"`
	Crop                       string   `protobuf:"bytes,6,opt,name=crop,proto3" json:"crop,omitempty"`
	Version                    int64    `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	TrainingDockerTag          string   `protobuf:"bytes,8,opt,name=training_docker_tag,json=trainingDockerTag,proto3" json:"training_docker_tag,omitempty"`
	GitSha                     string   `protobuf:"bytes,9,opt,name=gitSha,proto3" json:"gitSha,omitempty"`
	Checksum                   string   `protobuf:"bytes,10,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Location                   string   `protobuf:"bytes,11,opt,name=location,proto3" json:"location,omitempty"`
	TrainedAt                  int64    `protobuf:"varint,12,opt,name=trained_at,json=trainedAt,proto3" json:"trained_at,omitempty"`
	Type                       string   `protobuf:"bytes,13,opt,name=type,proto3" json:"type,omitempty"`
	Description                string   `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	MetadataJson               string   `protobuf:"bytes,15,opt,name=metadata_json,json=metadataJson,proto3" json:"metadata_json,omitempty"`
	ProductionContainerVersion string   `protobuf:"bytes,16,opt,name=production_container_version,json=productionContainerVersion,proto3" json:"production_container_version,omitempty"`
	TestResultsJson            string   `protobuf:"bytes,17,opt,name=test_results_json,json=testResultsJson,proto3" json:"test_results_json,omitempty"`
	WandbJson                  string   `protobuf:"bytes,18,opt,name=wandb_json,json=wandbJson,proto3" json:"wandb_json,omitempty"`
	SnapshotJson               string   `protobuf:"bytes,19,opt,name=snapshot_json,json=snapshotJson,proto3" json:"snapshot_json,omitempty"`
	IsStub                     bool     `protobuf:"varint,20,opt,name=is_stub,json=isStub,proto3" json:"is_stub,omitempty"`
	IsGoodToDeploy             bool     `protobuf:"varint,21,opt,name=is_good_to_deploy,json=isGoodToDeploy,proto3" json:"is_good_to_deploy,omitempty"`
	RobotName                  string   `protobuf:"bytes,22,opt,name=robot_name,json=robotName,proto3" json:"robot_name,omitempty"`
	Environment                string   `protobuf:"bytes,23,opt,name=environment,proto3" json:"environment,omitempty"`
	Deploy                     bool     `protobuf:"varint,24,opt,name=deploy,proto3" json:"deploy,omitempty"`
	IsPretraining              bool     `protobuf:"varint,25,opt,name=is_pretraining,json=isPretraining,proto3" json:"is_pretraining,omitempty"`
	SubType                    string   `protobuf:"bytes,26,opt,name=sub_type,json=subType,proto3" json:"sub_type,omitempty"`
	DatasetId                  string   `protobuf:"bytes,27,opt,name=dataset_id,json=datasetId,proto3" json:"dataset_id,omitempty"`
	ContainerVersion           string   `protobuf:"bytes,28,opt,name=container_version,json=containerVersion,proto3" json:"container_version,omitempty"`
	ContainerId                string   `protobuf:"bytes,29,opt,name=container_id,json=containerId,proto3" json:"container_id,omitempty"`
	PipelineId                 string   `protobuf:"bytes,30,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	ParentModelId              string   `protobuf:"bytes,31,opt,name=parent_model_id,json=parentModelId,proto3" json:"parent_model_id,omitempty"`
	ViableCropIds              []string `protobuf:"bytes,32,rep,name=viable_crop_ids,json=viableCropIds,proto3" json:"viable_crop_ids,omitempty"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{4}
}

func (x *Model) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Model) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *Model) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

func (x *Model) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Model) GetCustomer() string {
	if x != nil {
		return x.Customer
	}
	return ""
}

func (x *Model) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *Model) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Model) GetTrainingDockerTag() string {
	if x != nil {
		return x.TrainingDockerTag
	}
	return ""
}

func (x *Model) GetGitSha() string {
	if x != nil {
		return x.GitSha
	}
	return ""
}

func (x *Model) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *Model) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Model) GetTrainedAt() int64 {
	if x != nil {
		return x.TrainedAt
	}
	return 0
}

func (x *Model) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Model) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Model) GetMetadataJson() string {
	if x != nil {
		return x.MetadataJson
	}
	return ""
}

func (x *Model) GetProductionContainerVersion() string {
	if x != nil {
		return x.ProductionContainerVersion
	}
	return ""
}

func (x *Model) GetTestResultsJson() string {
	if x != nil {
		return x.TestResultsJson
	}
	return ""
}

func (x *Model) GetWandbJson() string {
	if x != nil {
		return x.WandbJson
	}
	return ""
}

func (x *Model) GetSnapshotJson() string {
	if x != nil {
		return x.SnapshotJson
	}
	return ""
}

func (x *Model) GetIsStub() bool {
	if x != nil {
		return x.IsStub
	}
	return false
}

func (x *Model) GetIsGoodToDeploy() bool {
	if x != nil {
		return x.IsGoodToDeploy
	}
	return false
}

func (x *Model) GetRobotName() string {
	if x != nil {
		return x.RobotName
	}
	return ""
}

func (x *Model) GetEnvironment() string {
	if x != nil {
		return x.Environment
	}
	return ""
}

func (x *Model) GetDeploy() bool {
	if x != nil {
		return x.Deploy
	}
	return false
}

func (x *Model) GetIsPretraining() bool {
	if x != nil {
		return x.IsPretraining
	}
	return false
}

func (x *Model) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *Model) GetDatasetId() string {
	if x != nil {
		return x.DatasetId
	}
	return ""
}

func (x *Model) GetContainerVersion() string {
	if x != nil {
		return x.ContainerVersion
	}
	return ""
}

func (x *Model) GetContainerId() string {
	if x != nil {
		return x.ContainerId
	}
	return ""
}

func (x *Model) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *Model) GetParentModelId() string {
	if x != nil {
		return x.ParentModelId
	}
	return ""
}

func (x *Model) GetViableCropIds() []string {
	if x != nil {
		return x.ViableCropIds
	}
	return nil
}

type CreateCategoryCollectionSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId                   string                             `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	CustomerId                *uint64                            `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	CategoryCollectionProfile *UnsavedExpandedCategoryCollection `protobuf:"bytes,3,opt,name=category_collection_profile,json=categoryCollectionProfile,proto3" json:"category_collection_profile,omitempty"`
}

func (x *CreateCategoryCollectionSessionRequest) Reset() {
	*x = CreateCategoryCollectionSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_veselka_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCategoryCollectionSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCategoryCollectionSessionRequest) ProtoMessage() {}

func (x *CreateCategoryCollectionSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_veselka_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCategoryCollectionSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateCategoryCollectionSessionRequest) Descriptor() ([]byte, []int) {
	return file_portal_veselka_proto_rawDescGZIP(), []int{5}
}

func (x *CreateCategoryCollectionSessionRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CreateCategoryCollectionSessionRequest) GetCustomerId() uint64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *CreateCategoryCollectionSessionRequest) GetCategoryCollectionProfile() *UnsavedExpandedCategoryCollection {
	if x != nil {
		return x.CategoryCollectionProfile
	}
	return nil
}

var File_portal_veselka_proto protoreflect.FileDescriptor

var file_portal_veselka_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x1a, 0x0f, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x64, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdd, 0x05,
	0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x70, 0x69, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x70, 0x70, 0x69, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6a, 0x73,
	0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6f, 0x77,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x65, 0x6f, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x65, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x68, 0x77, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x48, 0x77, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72,
	0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f,
	0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x65, 0x6f, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x65, 0x6f, 0x68, 0x61, 0x73, 0x68,
	0x12, 0x2b, 0x0a, 0x11, 0x71, 0x75, 0x61, 0x72, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x71, 0x75, 0x61,
	0x72, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xbc, 0x02,
	0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x24, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x64, 0x62, 0x2e,
	0x44, 0x42, 0x52, 0x02, 0x64, 0x62, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61,
	0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x75, 0x6d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6e, 0x75,
	0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x8a, 0x02, 0x0a,
	0x04, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x65, 0x67, 0x61, 0x63,
	0x79, 0x43, 0x72, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x09, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x2f, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x43, 0x72,
	0x6f, 0x70, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x65, 0x73,
	0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x22,
	0x92, 0x08, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x72, 0x6f, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x74,
	0x53, 0x68, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x74, 0x53, 0x68,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4a,
	0x73, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x1c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x4a, 0x73, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x6e, 0x64, 0x62, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x61, 0x6e, 0x64, 0x62, 0x4a, 0x73, 0x6f, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x6a, 0x73, 0x6f,
	0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x75, 0x62,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53, 0x74, 0x75, 0x62, 0x12, 0x29,
	0x0a, 0x11, 0x69, 0x73, 0x5f, 0x67, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x47, 0x6f, 0x6f,
	0x64, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x69,
	0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x50, 0x72,
	0x65, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x76, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x20, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x6f,
	0x70, 0x49, 0x64, 0x73, 0x22, 0xfd, 0x01, 0x0a, 0x26, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48,
	0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x81, 0x01, 0x0a, 0x1b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x55, 0x6e, 0x73, 0x61, 0x76, 0x65, 0x64, 0x45,
	0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x42, 0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_veselka_proto_rawDescOnce sync.Once
	file_portal_veselka_proto_rawDescData = file_portal_veselka_proto_rawDesc
)

func file_portal_veselka_proto_rawDescGZIP() []byte {
	file_portal_veselka_proto_rawDescOnce.Do(func() {
		file_portal_veselka_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_veselka_proto_rawDescData)
	})
	return file_portal_veselka_proto_rawDescData
}

var file_portal_veselka_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_portal_veselka_proto_goTypes = []interface{}{
	(*Image)(nil),          // 0: carbon.portal.veselka.Image
	(*CropConfidence)(nil), // 1: carbon.portal.veselka.CropConfidence
	(*Crop)(nil),           // 2: carbon.portal.veselka.Crop
	(*RobotCrop)(nil),      // 3: carbon.portal.veselka.RobotCrop
	(*Model)(nil),          // 4: carbon.portal.veselka.Model
	(*CreateCategoryCollectionSessionRequest)(nil), // 5: carbon.portal.veselka.CreateCategoryCollectionSessionRequest
	(*DB)(nil), // 6: carbon.portal.db.DB
	(*UnsavedExpandedCategoryCollection)(nil), // 7: carbon.portal.category_profile.UnsavedExpandedCategoryCollection
}
var file_portal_veselka_proto_depIdxs = []int32{
	6, // 0: carbon.portal.veselka.CropConfidence.db:type_name -> carbon.portal.db.DB
	2, // 1: carbon.portal.veselka.RobotCrop.crop:type_name -> carbon.portal.veselka.Crop
	1, // 2: carbon.portal.veselka.RobotCrop.confidence:type_name -> carbon.portal.veselka.CropConfidence
	7, // 3: carbon.portal.veselka.CreateCategoryCollectionSessionRequest.category_collection_profile:type_name -> carbon.portal.category_profile.UnsavedExpandedCategoryCollection
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_portal_veselka_proto_init() }
func file_portal_veselka_proto_init() {
	if File_portal_veselka_proto != nil {
		return
	}
	file_portal_db_proto_init()
	file_portal_category_profile_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_veselka_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_veselka_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CropConfidence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_veselka_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Crop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_veselka_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotCrop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_veselka_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_veselka_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCategoryCollectionSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_portal_veselka_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_veselka_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_portal_veselka_proto_goTypes,
		DependencyIndexes: file_portal_veselka_proto_depIdxs,
		MessageInfos:      file_portal_veselka_proto_msgTypes,
	}.Build()
	File_portal_veselka_proto = out.File
	file_portal_veselka_proto_rawDesc = nil
	file_portal_veselka_proto_goTypes = nil
	file_portal_veselka_proto_depIdxs = nil
}
