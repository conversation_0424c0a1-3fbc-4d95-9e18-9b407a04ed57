// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/farm.proto

package portal

import (
	geo "github.com/carbonrobotics/protos/golang/generated/proto/geo"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Farm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         *geo.Id            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Version    *VersionInfo       `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Name       string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CustomerId int64              `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	PointDefs  []*PointDefinition `protobuf:"bytes,5,rep,name=point_defs,json=pointDefs,proto3" json:"point_defs,omitempty"`
	Zones      []*Zone            `protobuf:"bytes,6,rep,name=zones,proto3" json:"zones,omitempty"`
}

func (x *Farm) Reset() {
	*x = Farm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Farm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Farm) ProtoMessage() {}

func (x *Farm) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Farm.ProtoReflect.Descriptor instead.
func (*Farm) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{0}
}

func (x *Farm) GetId() *geo.Id {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Farm) GetVersion() *VersionInfo {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *Farm) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Farm) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Farm) GetPointDefs() []*PointDefinition {
	if x != nil {
		return x.PointDefs
	}
	return nil
}

func (x *Farm) GetZones() []*Zone {
	if x != nil {
		return x.Zones
	}
	return nil
}

type VersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Sequence number for this version. Monotonically increasing.
	Ordinal int64 `protobuf:"varint,1,opt,name=ordinal,proto3" json:"ordinal,omitempty"`
	// Update time for this version.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Whether this entity is intended to be deleted in the latest version. The
	// entity is kept around as a tombstone so that we can tell that future syncs
	// aren't intending to recreate it.
	Deleted bool `protobuf:"varint,3,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// Input-only field: whether this entity is intended to be changed in a given
	// patch request. If this is false, then no edits to this entity's fields
	// will be incorporated into the merge, though edits to its descendants may
	// still be incorporated.
	Changed bool `protobuf:"varint,4,opt,name=changed,proto3" json:"changed,omitempty"`
}

func (x *VersionInfo) Reset() {
	*x = VersionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionInfo) ProtoMessage() {}

func (x *VersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionInfo.ProtoReflect.Descriptor instead.
func (*VersionInfo) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{1}
}

func (x *VersionInfo) GetOrdinal() int64 {
	if x != nil {
		return x.Ordinal
	}
	return 0
}

func (x *VersionInfo) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *VersionInfo) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *VersionInfo) GetChanged() bool {
	if x != nil {
		return x.Changed
	}
	return false
}

type PointDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Point   *geo.Point   `protobuf:"bytes,1,opt,name=point,proto3" json:"point,omitempty"`
	Version *VersionInfo `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *PointDefinition) Reset() {
	*x = PointDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PointDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PointDefinition) ProtoMessage() {}

func (x *PointDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PointDefinition.ProtoReflect.Descriptor instead.
func (*PointDefinition) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{2}
}

func (x *PointDefinition) GetPoint() *geo.Point {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *PointDefinition) GetVersion() *VersionInfo {
	if x != nil {
		return x.Version
	}
	return nil
}

type Zone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      *geo.Id      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Version *VersionInfo `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// Human-readable name for this zone.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// The physical areas that this zone includes.
	Areas    []*Area       `protobuf:"bytes,4,rep,name=areas,proto3" json:"areas,omitempty"`
	Contents *ZoneContents `protobuf:"bytes,5,opt,name=contents,proto3" json:"contents,omitempty"`
}

func (x *Zone) Reset() {
	*x = Zone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Zone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Zone) ProtoMessage() {}

func (x *Zone) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Zone.ProtoReflect.Descriptor instead.
func (*Zone) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{3}
}

func (x *Zone) GetId() *geo.Id {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Zone) GetVersion() *VersionInfo {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *Zone) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Zone) GetAreas() []*Area {
	if x != nil {
		return x.Areas
	}
	return nil
}

func (x *Zone) GetContents() *ZoneContents {
	if x != nil {
		return x.Contents
	}
	return nil
}

// This is a full message because `protoc-gen-go` generates bad code for
// `oneof`s: https://github.com/golang/protobuf/issues/1326
type ZoneContents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//	*ZoneContents_FarmBoundary
	//	*ZoneContents_Field
	//	*ZoneContents_Headland
	//	*ZoneContents_PrivateRoad
	//	*ZoneContents_Obstacle
	Data isZoneContents_Data `protobuf_oneof:"data"`
}

func (x *ZoneContents) Reset() {
	*x = ZoneContents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZoneContents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZoneContents) ProtoMessage() {}

func (x *ZoneContents) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZoneContents.ProtoReflect.Descriptor instead.
func (*ZoneContents) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{4}
}

func (m *ZoneContents) GetData() isZoneContents_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ZoneContents) GetFarmBoundary() *FarmBoundaryData {
	if x, ok := x.GetData().(*ZoneContents_FarmBoundary); ok {
		return x.FarmBoundary
	}
	return nil
}

func (x *ZoneContents) GetField() *FieldData {
	if x, ok := x.GetData().(*ZoneContents_Field); ok {
		return x.Field
	}
	return nil
}

func (x *ZoneContents) GetHeadland() *HeadlandData {
	if x, ok := x.GetData().(*ZoneContents_Headland); ok {
		return x.Headland
	}
	return nil
}

func (x *ZoneContents) GetPrivateRoad() *PrivateRoadData {
	if x, ok := x.GetData().(*ZoneContents_PrivateRoad); ok {
		return x.PrivateRoad
	}
	return nil
}

func (x *ZoneContents) GetObstacle() *ObstacleData {
	if x, ok := x.GetData().(*ZoneContents_Obstacle); ok {
		return x.Obstacle
	}
	return nil
}

type isZoneContents_Data interface {
	isZoneContents_Data()
}

type ZoneContents_FarmBoundary struct {
	FarmBoundary *FarmBoundaryData `protobuf:"bytes,9,opt,name=farm_boundary,json=farmBoundary,proto3,oneof"`
}

type ZoneContents_Field struct {
	Field *FieldData `protobuf:"bytes,5,opt,name=field,proto3,oneof"`
}

type ZoneContents_Headland struct {
	Headland *HeadlandData `protobuf:"bytes,6,opt,name=headland,proto3,oneof"`
}

type ZoneContents_PrivateRoad struct {
	PrivateRoad *PrivateRoadData `protobuf:"bytes,7,opt,name=private_road,json=privateRoad,proto3,oneof"`
}

type ZoneContents_Obstacle struct {
	Obstacle *ObstacleData `protobuf:"bytes,8,opt,name=obstacle,proto3,oneof"`
}

func (*ZoneContents_FarmBoundary) isZoneContents_Data() {}

func (*ZoneContents_Field) isZoneContents_Data() {}

func (*ZoneContents_Headland) isZoneContents_Data() {}

func (*ZoneContents_PrivateRoad) isZoneContents_Data() {}

func (*ZoneContents_Obstacle) isZoneContents_Data() {}

// An area is a contiguous region of space. It comprises all points within
// `buffer_meters` of the `geometry`. For example, a `line_string` in the shape
// of a circle describes an annulus, whereas a `polygon` in the same shape
// describes a disk.
type Area struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BufferMeters float64 `protobuf:"fixed64,1,opt,name=buffer_meters,json=bufferMeters,proto3" json:"buffer_meters,omitempty"`
	// Types that are assignable to Geometry:
	//	*Area_Point
	//	*Area_LineString
	//	*Area_Polygon
	Geometry isArea_Geometry `protobuf_oneof:"geometry"`
}

func (x *Area) Reset() {
	*x = Area{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Area) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Area) ProtoMessage() {}

func (x *Area) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Area.ProtoReflect.Descriptor instead.
func (*Area) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{5}
}

func (x *Area) GetBufferMeters() float64 {
	if x != nil {
		return x.BufferMeters
	}
	return 0
}

func (m *Area) GetGeometry() isArea_Geometry {
	if m != nil {
		return m.Geometry
	}
	return nil
}

func (x *Area) GetPoint() *geo.Point {
	if x, ok := x.GetGeometry().(*Area_Point); ok {
		return x.Point
	}
	return nil
}

func (x *Area) GetLineString() *geo.LineString {
	if x, ok := x.GetGeometry().(*Area_LineString); ok {
		return x.LineString
	}
	return nil
}

func (x *Area) GetPolygon() *geo.Polygon {
	if x, ok := x.GetGeometry().(*Area_Polygon); ok {
		return x.Polygon
	}
	return nil
}

type isArea_Geometry interface {
	isArea_Geometry()
}

type Area_Point struct {
	Point *geo.Point `protobuf:"bytes,2,opt,name=point,proto3,oneof"`
}

type Area_LineString struct {
	LineString *geo.LineString `protobuf:"bytes,3,opt,name=line_string,json=lineString,proto3,oneof"`
}

type Area_Polygon struct {
	Polygon *geo.Polygon `protobuf:"bytes,4,opt,name=polygon,proto3,oneof"`
}

func (*Area_Point) isArea_Geometry() {}

func (*Area_LineString) isArea_Geometry() {}

func (*Area_Polygon) isArea_Geometry() {}

// A farm boundary denotes the maximal extent of the farm. Under no
// circumstance should autonomous vehicles exist this boundary.
//
// A farm should have at most one zone of this type.
type FarmBoundaryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FarmBoundaryData) Reset() {
	*x = FarmBoundaryData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FarmBoundaryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FarmBoundaryData) ProtoMessage() {}

func (x *FarmBoundaryData) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FarmBoundaryData.ProtoReflect.Descriptor instead.
func (*FarmBoundaryData) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{6}
}

type FieldData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Heading along which crops are planted. Used for determining a default
	// direction of travel when plotting a GPS path through this field.
	PlantingHeading *PlantingHeading `protobuf:"bytes,1,opt,name=planting_heading,json=plantingHeading,proto3" json:"planting_heading,omitempty"`
	// For circular fields that use center-pivot irrigation, this message
	// describes the geometry of the center pivot. See:
	// https://en.wikipedia.org/wiki/Center-pivot_irrigation
	CenterPivot *CenterPivot `protobuf:"bytes,2,opt,name=center_pivot,json=centerPivot,proto3" json:"center_pivot,omitempty"`
}

func (x *FieldData) Reset() {
	*x = FieldData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldData) ProtoMessage() {}

func (x *FieldData) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldData.ProtoReflect.Descriptor instead.
func (*FieldData) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{7}
}

func (x *FieldData) GetPlantingHeading() *PlantingHeading {
	if x != nil {
		return x.PlantingHeading
	}
	return nil
}

func (x *FieldData) GetCenterPivot() *CenterPivot {
	if x != nil {
		return x.CenterPivot
	}
	return nil
}

type HeadlandData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HeadlandData) Reset() {
	*x = HeadlandData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeadlandData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeadlandData) ProtoMessage() {}

func (x *HeadlandData) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeadlandData.ProtoReflect.Descriptor instead.
func (*HeadlandData) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{8}
}

type PrivateRoadData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrivateRoadData) Reset() {
	*x = PrivateRoadData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrivateRoadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateRoadData) ProtoMessage() {}

func (x *PrivateRoadData) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateRoadData.ProtoReflect.Descriptor instead.
func (*PrivateRoadData) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{9}
}

type ObstacleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If the obstacle is passable, it can be driven through, but it's still an
	// obstacle that should not be operated on with implements.
	Passable bool `protobuf:"varint,1,opt,name=passable,proto3" json:"passable,omitempty"`
}

func (x *ObstacleData) Reset() {
	*x = ObstacleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObstacleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObstacleData) ProtoMessage() {}

func (x *ObstacleData) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObstacleData.ProtoReflect.Descriptor instead.
func (*ObstacleData) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{10}
}

func (x *ObstacleData) GetPassable() bool {
	if x != nil {
		return x.Passable
	}
	return false
}

// A planting heading can either be specified as an A-B line of recorded
// points, or just a fixed numeric heading.
type PlantingHeading struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Heading:
	//	*PlantingHeading_AzimuthDegrees
	//	*PlantingHeading_AbLine
	Heading isPlantingHeading_Heading `protobuf_oneof:"heading"`
}

func (x *PlantingHeading) Reset() {
	*x = PlantingHeading{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantingHeading) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantingHeading) ProtoMessage() {}

func (x *PlantingHeading) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantingHeading.ProtoReflect.Descriptor instead.
func (*PlantingHeading) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{11}
}

func (m *PlantingHeading) GetHeading() isPlantingHeading_Heading {
	if m != nil {
		return m.Heading
	}
	return nil
}

func (x *PlantingHeading) GetAzimuthDegrees() float64 {
	if x, ok := x.GetHeading().(*PlantingHeading_AzimuthDegrees); ok {
		return x.AzimuthDegrees
	}
	return 0
}

func (x *PlantingHeading) GetAbLine() *geo.AbLine {
	if x, ok := x.GetHeading().(*PlantingHeading_AbLine); ok {
		return x.AbLine
	}
	return nil
}

type isPlantingHeading_Heading interface {
	isPlantingHeading_Heading()
}

type PlantingHeading_AzimuthDegrees struct {
	// Degrees clockwise from north in the middle of the field.
	AzimuthDegrees float64 `protobuf:"fixed64,1,opt,name=azimuth_degrees,json=azimuthDegrees,proto3,oneof"`
}

type PlantingHeading_AbLine struct {
	// A vector between two points along the centerline of a single row.
	AbLine *geo.AbLine `protobuf:"bytes,2,opt,name=ab_line,json=abLine,proto3,oneof"`
}

func (*PlantingHeading_AzimuthDegrees) isPlantingHeading_Heading() {}

func (*PlantingHeading_AbLine) isPlantingHeading_Heading() {}

type CenterPivot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The center point of a center pivot.
	Center *geo.Point `protobuf:"bytes,1,opt,name=center,proto3" json:"center,omitempty"`
	// The width of the arm of the center pivot.
	WidthMeters float64 `protobuf:"fixed64,2,opt,name=width_meters,json=widthMeters,proto3" json:"width_meters,omitempty"`
	// Radius of the center pivot, from the center point to the farthest point on
	// the physical arm.
	LengthMeters float64 `protobuf:"fixed64,3,opt,name=length_meters,json=lengthMeters,proto3" json:"length_meters,omitempty"`
	// Device ID of a GPS tracker attached to the arm of the center pivot, near
	// the distal end of that arm, so that we can track the arm's angular
	// position as it traverses the field. Devices and their IDs are managed by
	// the RTC location service. Empty implies no such tracker.
	EndpointDeviceId string `protobuf:"bytes,4,opt,name=endpoint_device_id,json=endpointDeviceId,proto3" json:"endpoint_device_id,omitempty"`
}

func (x *CenterPivot) Reset() {
	*x = CenterPivot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CenterPivot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CenterPivot) ProtoMessage() {}

func (x *CenterPivot) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CenterPivot.ProtoReflect.Descriptor instead.
func (*CenterPivot) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{12}
}

func (x *CenterPivot) GetCenter() *geo.Point {
	if x != nil {
		return x.Center
	}
	return nil
}

func (x *CenterPivot) GetWidthMeters() float64 {
	if x != nil {
		return x.WidthMeters
	}
	return 0
}

func (x *CenterPivot) GetLengthMeters() float64 {
	if x != nil {
		return x.LengthMeters
	}
	return 0
}

func (x *CenterPivot) GetEndpointDeviceId() string {
	if x != nil {
		return x.EndpointDeviceId
	}
	return ""
}

type CreateFarmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Farm *Farm `protobuf:"bytes,1,opt,name=farm,proto3" json:"farm,omitempty"`
}

func (x *CreateFarmRequest) Reset() {
	*x = CreateFarmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateFarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFarmRequest) ProtoMessage() {}

func (x *CreateFarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFarmRequest.ProtoReflect.Descriptor instead.
func (*CreateFarmRequest) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{13}
}

func (x *CreateFarmRequest) GetFarm() *Farm {
	if x != nil {
		return x.Farm
	}
	return nil
}

type UpdateFarmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Patch updates to the farm. Descendant `VersionInfo`s must retain the
	// `ordinal` of the last copy that this client received from the server, for
	// merging purposes. To delete an entity, flip `deleted` from `false` to
	// `true`. To update an entity, set `changed` to `true` and edit the fields
	// on the entity.
	Farm *Farm `protobuf:"bytes,1,opt,name=farm,proto3" json:"farm,omitempty"`
}

func (x *UpdateFarmRequest) Reset() {
	*x = UpdateFarmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFarmRequest) ProtoMessage() {}

func (x *UpdateFarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFarmRequest.ProtoReflect.Descriptor instead.
func (*UpdateFarmRequest) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateFarmRequest) GetFarm() *Farm {
	if x != nil {
		return x.Farm
	}
	return nil
}

type ListFarmsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cursor from `ListFarmsResponse.next_page_token` on a previous response.
	// If blank, starts from the beginning.
	PageToken string `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListFarmsRequest) Reset() {
	*x = ListFarmsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFarmsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFarmsRequest) ProtoMessage() {}

func (x *ListFarmsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFarmsRequest.ProtoReflect.Descriptor instead.
func (*ListFarmsRequest) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{15}
}

func (x *ListFarmsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListFarmsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Farms []*Farm `protobuf:"bytes,1,rep,name=farms,proto3" json:"farms,omitempty"`
	// Cursor for `ListFarmsRequest.page_token`. If blank, there are no further
	// pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListFarmsResponse) Reset() {
	*x = ListFarmsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFarmsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFarmsResponse) ProtoMessage() {}

func (x *ListFarmsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFarmsResponse.ProtoReflect.Descriptor instead.
func (*ListFarmsResponse) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{16}
}

func (x *ListFarmsResponse) GetFarms() []*Farm {
	if x != nil {
		return x.Farms
	}
	return nil
}

func (x *ListFarmsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type GetFarmRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the farm to get.
	Id *geo.Id `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// If specified, this is a conditional request; see `FarmsService.GetFarm`.
	IfModifiedSince *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=if_modified_since,json=ifModifiedSince,proto3" json:"if_modified_since,omitempty"`
}

func (x *GetFarmRequest) Reset() {
	*x = GetFarmRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFarmRequest) ProtoMessage() {}

func (x *GetFarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFarmRequest.ProtoReflect.Descriptor instead.
func (*GetFarmRequest) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{17}
}

func (x *GetFarmRequest) GetId() *geo.Id {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *GetFarmRequest) GetIfModifiedSince() *timestamppb.Timestamp {
	if x != nil {
		return x.IfModifiedSince
	}
	return nil
}

type GetFarmResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Farm *Farm `protobuf:"bytes,1,opt,name=farm,proto3" json:"farm,omitempty"`
}

func (x *GetFarmResponse) Reset() {
	*x = GetFarmResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_farm_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFarmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFarmResponse) ProtoMessage() {}

func (x *GetFarmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_farm_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFarmResponse.ProtoReflect.Descriptor instead.
func (*GetFarmResponse) Descriptor() ([]byte, []int) {
	return file_portal_farm_proto_rawDescGZIP(), []int{18}
}

func (x *GetFarmResponse) GetFarm() *Farm {
	if x != nil {
		return x.Farm
	}
	return nil
}

var File_portal_farm_proto protoreflect.FileDescriptor

var file_portal_farm_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x12, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x1a, 0x0d, 0x67, 0x65, 0x6f, 0x2f, 0x67, 0x65, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x02, 0x0a, 0x04, 0x46, 0x61, 0x72, 0x6d,
	0x12, 0x1e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x49, 0x64, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x39, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x44, 0x65, 0x66, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x05, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x3b,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x22,
	0x75, 0x0a, 0x0f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72,
	0x6d, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xe3, 0x01, 0x0a, 0x04, 0x5a, 0x6f, 0x6e, 0x65, 0x12,
	0x1e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x49, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x39, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61,
	0x72, 0x6d, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x12, 0x3c,
	0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x5a, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xe4, 0x02, 0x0a,
	0x0c, 0x5a, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x4b, 0x0a,
	0x0d, 0x66, 0x61, 0x72, 0x6d, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46, 0x61, 0x72, 0x6d, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x66, 0x61,
	0x72, 0x6d, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x3e, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e,
	0x64, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x6f, 0x61,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x61, 0x64, 0x12, 0x3e, 0x0a, 0x08, 0x6f,
	0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61,
	0x72, 0x6d, 0x2e, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x08, 0x6f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xce, 0x01, 0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x12, 0x23, 0x0a, 0x0d,
	0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x29, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0b,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x4c,
	0x69, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0a, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2f, 0x0a, 0x07, 0x70, 0x6f, 0x6c, 0x79, 0x67,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x07, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x42, 0x0a, 0x0a, 0x08, 0x67, 0x65, 0x6f, 0x6d,
	0x65, 0x74, 0x72, 0x79, 0x22, 0x12, 0x0a, 0x10, 0x46, 0x61, 0x72, 0x6d, 0x42, 0x6f, 0x75, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9f, 0x01, 0x0a, 0x09, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x48,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x42, 0x0a, 0x0c, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x5f, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72,
	0x6d, 0x2e, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x52, 0x0b, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x22, 0x0e, 0x0a, 0x0c, 0x48, 0x65,
	0x61, 0x64, 0x6c, 0x61, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x11, 0x0a, 0x0f, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a,
	0x0c, 0x4f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x76, 0x0a, 0x0f, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x0f,
	0x61, 0x7a, 0x69, 0x6d, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x7a, 0x69, 0x6d, 0x75, 0x74, 0x68,
	0x44, 0x65, 0x67, 0x72, 0x65, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x07, 0x61, 0x62, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x41, 0x62, 0x4c, 0x69, 0x6e, 0x65, 0x48, 0x00, 0x52, 0x06,
	0x61, 0x62, 0x4c, 0x69, 0x6e, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x22, 0xae, 0x01, 0x0a, 0x0b, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x69, 0x76, 0x6f,
	0x74, 0x12, 0x29, 0x0a, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0b, 0x77, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x41, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x61, 0x72, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x61, 0x72, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46, 0x61, 0x72, 0x6d, 0x52,
	0x04, 0x66, 0x61, 0x72, 0x6d, 0x22, 0x41, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x61,
	0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46, 0x61,
	0x72, 0x6d, 0x52, 0x04, 0x66, 0x61, 0x72, 0x6d, 0x22, 0x31, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x46, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x6b, 0x0a, 0x11, 0x4c,
	0x69, 0x73, 0x74, 0x46, 0x61, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2e, 0x0a, 0x05, 0x66, 0x61, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46, 0x61, 0x72, 0x6d, 0x52, 0x05, 0x66, 0x61, 0x72, 0x6d, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x78, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x46,
	0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x67, 0x65, 0x6f, 0x2e, 0x49, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x69, 0x66,
	0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0f, 0x69, 0x66, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x53, 0x69, 0x6e,
	0x63, 0x65, 0x22, 0x3f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x66, 0x61, 0x72, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x46, 0x61, 0x72, 0x6d, 0x52, 0x04, 0x66,
	0x61, 0x72, 0x6d, 0x32, 0x62, 0x0a, 0x0c, 0x46, 0x61, 0x72, 0x6d, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x46, 0x61, 0x72, 0x6d, 0x12, 0x22,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x66,
	0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x66, 0x61, 0x72, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x72, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61,
	0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_portal_farm_proto_rawDescOnce sync.Once
	file_portal_farm_proto_rawDescData = file_portal_farm_proto_rawDesc
)

func file_portal_farm_proto_rawDescGZIP() []byte {
	file_portal_farm_proto_rawDescOnce.Do(func() {
		file_portal_farm_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_farm_proto_rawDescData)
	})
	return file_portal_farm_proto_rawDescData
}

var file_portal_farm_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_portal_farm_proto_goTypes = []interface{}{
	(*Farm)(nil),                  // 0: carbon.portal.farm.Farm
	(*VersionInfo)(nil),           // 1: carbon.portal.farm.VersionInfo
	(*PointDefinition)(nil),       // 2: carbon.portal.farm.PointDefinition
	(*Zone)(nil),                  // 3: carbon.portal.farm.Zone
	(*ZoneContents)(nil),          // 4: carbon.portal.farm.ZoneContents
	(*Area)(nil),                  // 5: carbon.portal.farm.Area
	(*FarmBoundaryData)(nil),      // 6: carbon.portal.farm.FarmBoundaryData
	(*FieldData)(nil),             // 7: carbon.portal.farm.FieldData
	(*HeadlandData)(nil),          // 8: carbon.portal.farm.HeadlandData
	(*PrivateRoadData)(nil),       // 9: carbon.portal.farm.PrivateRoadData
	(*ObstacleData)(nil),          // 10: carbon.portal.farm.ObstacleData
	(*PlantingHeading)(nil),       // 11: carbon.portal.farm.PlantingHeading
	(*CenterPivot)(nil),           // 12: carbon.portal.farm.CenterPivot
	(*CreateFarmRequest)(nil),     // 13: carbon.portal.farm.CreateFarmRequest
	(*UpdateFarmRequest)(nil),     // 14: carbon.portal.farm.UpdateFarmRequest
	(*ListFarmsRequest)(nil),      // 15: carbon.portal.farm.ListFarmsRequest
	(*ListFarmsResponse)(nil),     // 16: carbon.portal.farm.ListFarmsResponse
	(*GetFarmRequest)(nil),        // 17: carbon.portal.farm.GetFarmRequest
	(*GetFarmResponse)(nil),       // 18: carbon.portal.farm.GetFarmResponse
	(*geo.Id)(nil),                // 19: carbon.geo.Id
	(*timestamppb.Timestamp)(nil), // 20: google.protobuf.Timestamp
	(*geo.Point)(nil),             // 21: carbon.geo.Point
	(*geo.LineString)(nil),        // 22: carbon.geo.LineString
	(*geo.Polygon)(nil),           // 23: carbon.geo.Polygon
	(*geo.AbLine)(nil),            // 24: carbon.geo.AbLine
}
var file_portal_farm_proto_depIdxs = []int32{
	19, // 0: carbon.portal.farm.Farm.id:type_name -> carbon.geo.Id
	1,  // 1: carbon.portal.farm.Farm.version:type_name -> carbon.portal.farm.VersionInfo
	2,  // 2: carbon.portal.farm.Farm.point_defs:type_name -> carbon.portal.farm.PointDefinition
	3,  // 3: carbon.portal.farm.Farm.zones:type_name -> carbon.portal.farm.Zone
	20, // 4: carbon.portal.farm.VersionInfo.update_time:type_name -> google.protobuf.Timestamp
	21, // 5: carbon.portal.farm.PointDefinition.point:type_name -> carbon.geo.Point
	1,  // 6: carbon.portal.farm.PointDefinition.version:type_name -> carbon.portal.farm.VersionInfo
	19, // 7: carbon.portal.farm.Zone.id:type_name -> carbon.geo.Id
	1,  // 8: carbon.portal.farm.Zone.version:type_name -> carbon.portal.farm.VersionInfo
	5,  // 9: carbon.portal.farm.Zone.areas:type_name -> carbon.portal.farm.Area
	4,  // 10: carbon.portal.farm.Zone.contents:type_name -> carbon.portal.farm.ZoneContents
	6,  // 11: carbon.portal.farm.ZoneContents.farm_boundary:type_name -> carbon.portal.farm.FarmBoundaryData
	7,  // 12: carbon.portal.farm.ZoneContents.field:type_name -> carbon.portal.farm.FieldData
	8,  // 13: carbon.portal.farm.ZoneContents.headland:type_name -> carbon.portal.farm.HeadlandData
	9,  // 14: carbon.portal.farm.ZoneContents.private_road:type_name -> carbon.portal.farm.PrivateRoadData
	10, // 15: carbon.portal.farm.ZoneContents.obstacle:type_name -> carbon.portal.farm.ObstacleData
	21, // 16: carbon.portal.farm.Area.point:type_name -> carbon.geo.Point
	22, // 17: carbon.portal.farm.Area.line_string:type_name -> carbon.geo.LineString
	23, // 18: carbon.portal.farm.Area.polygon:type_name -> carbon.geo.Polygon
	11, // 19: carbon.portal.farm.FieldData.planting_heading:type_name -> carbon.portal.farm.PlantingHeading
	12, // 20: carbon.portal.farm.FieldData.center_pivot:type_name -> carbon.portal.farm.CenterPivot
	24, // 21: carbon.portal.farm.PlantingHeading.ab_line:type_name -> carbon.geo.AbLine
	21, // 22: carbon.portal.farm.CenterPivot.center:type_name -> carbon.geo.Point
	0,  // 23: carbon.portal.farm.CreateFarmRequest.farm:type_name -> carbon.portal.farm.Farm
	0,  // 24: carbon.portal.farm.UpdateFarmRequest.farm:type_name -> carbon.portal.farm.Farm
	0,  // 25: carbon.portal.farm.ListFarmsResponse.farms:type_name -> carbon.portal.farm.Farm
	19, // 26: carbon.portal.farm.GetFarmRequest.id:type_name -> carbon.geo.Id
	20, // 27: carbon.portal.farm.GetFarmRequest.if_modified_since:type_name -> google.protobuf.Timestamp
	0,  // 28: carbon.portal.farm.GetFarmResponse.farm:type_name -> carbon.portal.farm.Farm
	17, // 29: carbon.portal.farm.FarmsService.GetFarm:input_type -> carbon.portal.farm.GetFarmRequest
	18, // 30: carbon.portal.farm.FarmsService.GetFarm:output_type -> carbon.portal.farm.GetFarmResponse
	30, // [30:31] is the sub-list for method output_type
	29, // [29:30] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_portal_farm_proto_init() }
func file_portal_farm_proto_init() {
	if File_portal_farm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_portal_farm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Farm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PointDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Zone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZoneContents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Area); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FarmBoundaryData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeadlandData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrivateRoadData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObstacleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantingHeading); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CenterPivot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateFarmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFarmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFarmsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFarmsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFarmRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_farm_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFarmResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_portal_farm_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ZoneContents_FarmBoundary)(nil),
		(*ZoneContents_Field)(nil),
		(*ZoneContents_Headland)(nil),
		(*ZoneContents_PrivateRoad)(nil),
		(*ZoneContents_Obstacle)(nil),
	}
	file_portal_farm_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Area_Point)(nil),
		(*Area_LineString)(nil),
		(*Area_Polygon)(nil),
	}
	file_portal_farm_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*PlantingHeading_AzimuthDegrees)(nil),
		(*PlantingHeading_AbLine)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_farm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_farm_proto_goTypes,
		DependencyIndexes: file_portal_farm_proto_depIdxs,
		MessageInfos:      file_portal_farm_proto_msgTypes,
	}.Build()
	File_portal_farm_proto = out.File
	file_portal_farm_proto_rawDesc = nil
	file_portal_farm_proto_goTypes = nil
	file_portal_farm_proto_depIdxs = nil
}
