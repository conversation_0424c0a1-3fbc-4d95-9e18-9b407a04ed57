// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/metrics.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DailyMetricResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db                            *DB     `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Serial                        string  `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
	RobotId                       uint64  `protobuf:"varint,3,opt,name=robot_id,json=robotId,proto3" json:"robot_id,omitempty"`
	Date                          string  `protobuf:"bytes,25,opt,name=date,proto3" json:"date,omitempty"`
	JobId                         string  `protobuf:"bytes,44,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	JobName                       string  `protobuf:"bytes,45,opt,name=job_name,json=jobName,proto3" json:"job_name,omitempty"`
	AcresWeeded                   float32 `protobuf:"fixed32,4,opt,name=acres_weeded,json=acresWeeded,proto3" json:"acres_weeded,omitempty"`
	AvgSpeedMph                   float32 `protobuf:"fixed32,5,opt,name=avg_speed_mph,json=avgSpeedMph,proto3" json:"avg_speed_mph,omitempty"`
	AvgWeedSizeMm                 float32 `protobuf:"fixed32,6,opt,name=avg_weed_size_mm,json=avgWeedSizeMm,proto3" json:"avg_weed_size_mm,omitempty"`
	BandingConfigName             string  `protobuf:"bytes,7,opt,name=banding_config_name,json=bandingConfigName,proto3" json:"banding_config_name,omitempty"`
	BandingEnabled                bool    `protobuf:"varint,8,opt,name=banding_enabled,json=bandingEnabled,proto3" json:"banding_enabled,omitempty"`
	CoverageSpeedAcresHr          float32 `protobuf:"fixed32,9,opt,name=coverage_speed_acres_hr,json=coverageSpeedAcresHr,proto3" json:"coverage_speed_acres_hr,omitempty"`
	DistanceWeededMeters          float32 `protobuf:"fixed32,10,opt,name=distance_weeded_meters,json=distanceWeededMeters,proto3" json:"distance_weeded_meters,omitempty"`
	KilledWeeds                   int64   `protobuf:"varint,11,opt,name=killed_weeds,json=killedWeeds,proto3" json:"killed_weeds,omitempty"`
	MissedWeeds                   int64   `protobuf:"varint,12,opt,name=missed_weeds,json=missedWeeds,proto3" json:"missed_weeds,omitempty"`
	SkippedWeeds                  int64   `protobuf:"varint,13,opt,name=skipped_weeds,json=skippedWeeds,proto3" json:"skipped_weeds,omitempty"`
	TimeEfficiency                float32 `protobuf:"fixed32,14,opt,name=time_efficiency,json=timeEfficiency,proto3" json:"time_efficiency,omitempty"`
	TotalWeeds                    int64   `protobuf:"varint,15,opt,name=total_weeds,json=totalWeeds,proto3" json:"total_weeds,omitempty"`
	UptimeSeconds                 float32 `protobuf:"fixed32,16,opt,name=uptime_seconds,json=uptimeSeconds,proto3" json:"uptime_seconds,omitempty"`
	WeedDensitySqFt               float32 `protobuf:"fixed32,17,opt,name=weed_density_sq_ft,json=weedDensitySqFt,proto3" json:"weed_density_sq_ft,omitempty"`
	WeedingUptimeSeconds          float32 `protobuf:"fixed32,18,opt,name=weeding_uptime_seconds,json=weedingUptimeSeconds,proto3" json:"weeding_uptime_seconds,omitempty"`
	WeedingEfficiency             float32 `protobuf:"fixed32,19,opt,name=weeding_efficiency,json=weedingEfficiency,proto3" json:"weeding_efficiency,omitempty"`
	WeedsTypeCountBroadleaf       int64   `protobuf:"varint,20,opt,name=weeds_type_count_broadleaf,json=weedsTypeCountBroadleaf,proto3" json:"weeds_type_count_broadleaf,omitempty"`
	WeedsTypeCountGrass           int64   `protobuf:"varint,21,opt,name=weeds_type_count_grass,json=weedsTypeCountGrass,proto3" json:"weeds_type_count_grass,omitempty"`
	WeedsTypeCountOffshoot        int64   `protobuf:"varint,22,opt,name=weeds_type_count_offshoot,json=weedsTypeCountOffshoot,proto3" json:"weeds_type_count_offshoot,omitempty"`
	WeedsTypeCountPurslane        int64   `protobuf:"varint,23,opt,name=weeds_type_count_purslane,json=weedsTypeCountPurslane,proto3" json:"weeds_type_count_purslane,omitempty"`
	NotWeedingWeeds               int64   `protobuf:"varint,24,opt,name=not_weeding_weeds,json=notWeedingWeeds,proto3" json:"not_weeding_weeds,omitempty"`
	KeptCrops                     int64   `protobuf:"varint,26,opt,name=kept_crops,json=keptCrops,proto3" json:"kept_crops,omitempty"`
	MissedCrops                   int64   `protobuf:"varint,27,opt,name=missed_crops,json=missedCrops,proto3" json:"missed_crops,omitempty"`
	NotThinning                   int64   `protobuf:"varint,28,opt,name=not_thinning,json=notThinning,proto3" json:"not_thinning,omitempty"`
	NotWeeding                    int64   `protobuf:"varint,29,opt,name=not_weeding,json=notWeeding,proto3" json:"not_weeding,omitempty"`
	SkippedCrops                  int64   `protobuf:"varint,30,opt,name=skipped_crops,json=skippedCrops,proto3" json:"skipped_crops,omitempty"`
	ThinnedCrops                  int64   `protobuf:"varint,31,opt,name=thinned_crops,json=thinnedCrops,proto3" json:"thinned_crops,omitempty"`
	TotalCrops                    int64   `protobuf:"varint,32,opt,name=total_crops,json=totalCrops,proto3" json:"total_crops,omitempty"`
	BandingPercentage             float32 `protobuf:"fixed32,33,opt,name=banding_percentage,json=bandingPercentage,proto3" json:"banding_percentage,omitempty"`
	ThinningEfficiency            float32 `protobuf:"fixed32,34,opt,name=thinning_efficiency,json=thinningEfficiency,proto3" json:"thinning_efficiency,omitempty"`
	CropId                        string  `protobuf:"bytes,35,opt,name=crop_id,json=cropId,proto3" json:"crop_id,omitempty"`
	Crop                          string  `protobuf:"bytes,36,opt,name=crop,proto3" json:"crop,omitempty"`
	CropDensitySqFt               float32 `protobuf:"fixed32,37,opt,name=crop_density_sq_ft,json=cropDensitySqFt,proto3" json:"crop_density_sq_ft,omitempty"`
	AvgCropSizeMm                 float32 `protobuf:"fixed32,38,opt,name=avg_crop_size_mm,json=avgCropSizeMm,proto3" json:"avg_crop_size_mm,omitempty"`
	AvgTargetableReqLaserTime     int64   `protobuf:"varint,39,opt,name=avg_targetable_req_laser_time,json=avgTargetableReqLaserTime,proto3" json:"avg_targetable_req_laser_time,omitempty"`
	AvgUntargetableReqLaserTime   int64   `protobuf:"varint,40,opt,name=avg_untargetable_req_laser_time,json=avgUntargetableReqLaserTime,proto3" json:"avg_untargetable_req_laser_time,omitempty"`
	ValidCrops                    int64   `protobuf:"varint,41,opt,name=valid_crops,json=validCrops,proto3" json:"valid_crops,omitempty"`
	OperatorEffectiveness         float32 `protobuf:"fixed32,42,opt,name=operator_effectiveness,json=operatorEffectiveness,proto3" json:"operator_effectiveness,omitempty"`
	TargetWeedingTimeSeconds      int64   `protobuf:"varint,43,opt,name=target_weeding_time_seconds,json=targetWeedingTimeSeconds,proto3" json:"target_weeding_time_seconds,omitempty"`
	EmbeddingsActiveUptimeSeconds float32 `protobuf:"fixed32,46,opt,name=embeddings_active_uptime_seconds,json=embeddingsActiveUptimeSeconds,proto3" json:"embeddings_active_uptime_seconds,omitempty"`
}

func (x *DailyMetricResponse) Reset() {
	*x = DailyMetricResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_metrics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyMetricResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyMetricResponse) ProtoMessage() {}

func (x *DailyMetricResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_metrics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyMetricResponse.ProtoReflect.Descriptor instead.
func (*DailyMetricResponse) Descriptor() ([]byte, []int) {
	return file_portal_metrics_proto_rawDescGZIP(), []int{0}
}

func (x *DailyMetricResponse) GetDb() *DB {
	if x != nil {
		return x.Db
	}
	return nil
}

func (x *DailyMetricResponse) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *DailyMetricResponse) GetRobotId() uint64 {
	if x != nil {
		return x.RobotId
	}
	return 0
}

func (x *DailyMetricResponse) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DailyMetricResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *DailyMetricResponse) GetJobName() string {
	if x != nil {
		return x.JobName
	}
	return ""
}

func (x *DailyMetricResponse) GetAcresWeeded() float32 {
	if x != nil {
		return x.AcresWeeded
	}
	return 0
}

func (x *DailyMetricResponse) GetAvgSpeedMph() float32 {
	if x != nil {
		return x.AvgSpeedMph
	}
	return 0
}

func (x *DailyMetricResponse) GetAvgWeedSizeMm() float32 {
	if x != nil {
		return x.AvgWeedSizeMm
	}
	return 0
}

func (x *DailyMetricResponse) GetBandingConfigName() string {
	if x != nil {
		return x.BandingConfigName
	}
	return ""
}

func (x *DailyMetricResponse) GetBandingEnabled() bool {
	if x != nil {
		return x.BandingEnabled
	}
	return false
}

func (x *DailyMetricResponse) GetCoverageSpeedAcresHr() float32 {
	if x != nil {
		return x.CoverageSpeedAcresHr
	}
	return 0
}

func (x *DailyMetricResponse) GetDistanceWeededMeters() float32 {
	if x != nil {
		return x.DistanceWeededMeters
	}
	return 0
}

func (x *DailyMetricResponse) GetKilledWeeds() int64 {
	if x != nil {
		return x.KilledWeeds
	}
	return 0
}

func (x *DailyMetricResponse) GetMissedWeeds() int64 {
	if x != nil {
		return x.MissedWeeds
	}
	return 0
}

func (x *DailyMetricResponse) GetSkippedWeeds() int64 {
	if x != nil {
		return x.SkippedWeeds
	}
	return 0
}

func (x *DailyMetricResponse) GetTimeEfficiency() float32 {
	if x != nil {
		return x.TimeEfficiency
	}
	return 0
}

func (x *DailyMetricResponse) GetTotalWeeds() int64 {
	if x != nil {
		return x.TotalWeeds
	}
	return 0
}

func (x *DailyMetricResponse) GetUptimeSeconds() float32 {
	if x != nil {
		return x.UptimeSeconds
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedDensitySqFt() float32 {
	if x != nil {
		return x.WeedDensitySqFt
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedingUptimeSeconds() float32 {
	if x != nil {
		return x.WeedingUptimeSeconds
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedingEfficiency() float32 {
	if x != nil {
		return x.WeedingEfficiency
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedsTypeCountBroadleaf() int64 {
	if x != nil {
		return x.WeedsTypeCountBroadleaf
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedsTypeCountGrass() int64 {
	if x != nil {
		return x.WeedsTypeCountGrass
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedsTypeCountOffshoot() int64 {
	if x != nil {
		return x.WeedsTypeCountOffshoot
	}
	return 0
}

func (x *DailyMetricResponse) GetWeedsTypeCountPurslane() int64 {
	if x != nil {
		return x.WeedsTypeCountPurslane
	}
	return 0
}

func (x *DailyMetricResponse) GetNotWeedingWeeds() int64 {
	if x != nil {
		return x.NotWeedingWeeds
	}
	return 0
}

func (x *DailyMetricResponse) GetKeptCrops() int64 {
	if x != nil {
		return x.KeptCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetMissedCrops() int64 {
	if x != nil {
		return x.MissedCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetNotThinning() int64 {
	if x != nil {
		return x.NotThinning
	}
	return 0
}

func (x *DailyMetricResponse) GetNotWeeding() int64 {
	if x != nil {
		return x.NotWeeding
	}
	return 0
}

func (x *DailyMetricResponse) GetSkippedCrops() int64 {
	if x != nil {
		return x.SkippedCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetThinnedCrops() int64 {
	if x != nil {
		return x.ThinnedCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetTotalCrops() int64 {
	if x != nil {
		return x.TotalCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetBandingPercentage() float32 {
	if x != nil {
		return x.BandingPercentage
	}
	return 0
}

func (x *DailyMetricResponse) GetThinningEfficiency() float32 {
	if x != nil {
		return x.ThinningEfficiency
	}
	return 0
}

func (x *DailyMetricResponse) GetCropId() string {
	if x != nil {
		return x.CropId
	}
	return ""
}

func (x *DailyMetricResponse) GetCrop() string {
	if x != nil {
		return x.Crop
	}
	return ""
}

func (x *DailyMetricResponse) GetCropDensitySqFt() float32 {
	if x != nil {
		return x.CropDensitySqFt
	}
	return 0
}

func (x *DailyMetricResponse) GetAvgCropSizeMm() float32 {
	if x != nil {
		return x.AvgCropSizeMm
	}
	return 0
}

func (x *DailyMetricResponse) GetAvgTargetableReqLaserTime() int64 {
	if x != nil {
		return x.AvgTargetableReqLaserTime
	}
	return 0
}

func (x *DailyMetricResponse) GetAvgUntargetableReqLaserTime() int64 {
	if x != nil {
		return x.AvgUntargetableReqLaserTime
	}
	return 0
}

func (x *DailyMetricResponse) GetValidCrops() int64 {
	if x != nil {
		return x.ValidCrops
	}
	return 0
}

func (x *DailyMetricResponse) GetOperatorEffectiveness() float32 {
	if x != nil {
		return x.OperatorEffectiveness
	}
	return 0
}

func (x *DailyMetricResponse) GetTargetWeedingTimeSeconds() int64 {
	if x != nil {
		return x.TargetWeedingTimeSeconds
	}
	return 0
}

func (x *DailyMetricResponse) GetEmbeddingsActiveUptimeSeconds() float32 {
	if x != nil {
		return x.EmbeddingsActiveUptimeSeconds
	}
	return 0
}

type DailyMetricsByDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics map[string]*DailyMetricResponse `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DailyMetricsByDateResponse) Reset() {
	*x = DailyMetricsByDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_metrics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyMetricsByDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyMetricsByDateResponse) ProtoMessage() {}

func (x *DailyMetricsByDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_metrics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyMetricsByDateResponse.ProtoReflect.Descriptor instead.
func (*DailyMetricsByDateResponse) Descriptor() ([]byte, []int) {
	return file_portal_metrics_proto_rawDescGZIP(), []int{1}
}

func (x *DailyMetricsByDateResponse) GetMetrics() map[string]*DailyMetricResponse {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type DailyMetricsByDateByRobotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metrics map[string]*DailyMetricsByDateResponse `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DailyMetricsByDateByRobotResponse) Reset() {
	*x = DailyMetricsByDateByRobotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_metrics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyMetricsByDateByRobotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyMetricsByDateByRobotResponse) ProtoMessage() {}

func (x *DailyMetricsByDateByRobotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_metrics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyMetricsByDateByRobotResponse.ProtoReflect.Descriptor instead.
func (*DailyMetricsByDateByRobotResponse) Descriptor() ([]byte, []int) {
	return file_portal_metrics_proto_rawDescGZIP(), []int{2}
}

func (x *DailyMetricsByDateByRobotResponse) GetMetrics() map[string]*DailyMetricsByDateResponse {
	if x != nil {
		return x.Metrics
	}
	return nil
}

var File_portal_metrics_proto protoreflect.FileDescriptor

var file_portal_metrics_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0x0f, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x64, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8,
	0x0f, 0x0a, 0x13, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x64, 0x62, 0x2e, 0x44, 0x42, 0x52, 0x02, 0x64, 0x62, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x2c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x6f,
	0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x6f,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x72, 0x65, 0x73, 0x5f, 0x77,
	0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x61, 0x63, 0x72,
	0x65, 0x73, 0x57, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x76, 0x67, 0x5f,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x61, 0x76, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4d, 0x70, 0x68, 0x12, 0x27, 0x0a, 0x10,
	0x61, 0x76, 0x67, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x6d,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x61, 0x76, 0x67, 0x57, 0x65, 0x65, 0x64, 0x53,
	0x69, 0x7a, 0x65, 0x4d, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x35,
	0x0a, 0x17, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x72, 0x65, 0x73, 0x5f, 0x68, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x14, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x53, 0x70, 0x65, 0x65, 0x64, 0x41, 0x63,
	0x72, 0x65, 0x73, 0x48, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x57,
	0x65, 0x65, 0x64, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6b,
	0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x6b, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x57, 0x65, 0x65, 0x64, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x57, 0x65, 0x65, 0x64,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65,
	0x64, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65,
	0x64, 0x57, 0x65, 0x65, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0e, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x73, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x57, 0x65, 0x65, 0x64, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x12, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x71, 0x5f, 0x66, 0x74, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x44, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79,
	0x53, 0x71, 0x46, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x55, 0x70, 0x74,
	0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x45,
	0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3b, 0x0a, 0x1a, 0x77, 0x65, 0x65,
	0x64, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x72,
	0x6f, 0x61, 0x64, 0x6c, 0x65, 0x61, 0x66, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x77,
	0x65, 0x65, 0x64, 0x73, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x72, 0x6f,
	0x61, 0x64, 0x6c, 0x65, 0x61, 0x66, 0x12, 0x33, 0x0a, 0x16, 0x77, 0x65, 0x65, 0x64, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x73, 0x73,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x77, 0x65, 0x65, 0x64, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x72, 0x61, 0x73, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x77,
	0x65, 0x65, 0x64, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6f, 0x66, 0x66, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16,
	0x77, 0x65, 0x65, 0x64, 0x73, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x66,
	0x66, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x77, 0x65, 0x65, 0x64, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x72, 0x73, 0x6c,
	0x61, 0x6e, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x77, 0x65, 0x65, 0x64, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x75, 0x72, 0x73, 0x6c, 0x61, 0x6e,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x77, 0x65, 0x65, 0x64, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6e, 0x6f,
	0x74, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x65, 0x65, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x6b, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6b, 0x65, 0x70, 0x74, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x6d, 0x69, 0x73, 0x73, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x57, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x5f, 0x63,
	0x72, 0x6f, 0x70, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x6b, 0x69, 0x70,
	0x70, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x68, 0x69, 0x6e,
	0x6e, 0x65, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x2d,
	0x0a, 0x12, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a,
	0x13, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x74, 0x68, 0x69, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x72, 0x6f, 0x70, 0x12, 0x2b, 0x0a, 0x12, 0x63,
	0x72, 0x6f, 0x70, 0x5f, 0x64, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x71, 0x5f, 0x66,
	0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x63, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x79, 0x53, 0x71, 0x46, 0x74, 0x12, 0x27, 0x0a, 0x10, 0x61, 0x76, 0x67, 0x5f,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x6d, 0x18, 0x26, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0d, 0x61, 0x76, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x4d,
	0x6d, 0x12, 0x40, 0x0a, 0x1d, 0x61, 0x76, 0x67, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x61, 0x76, 0x67, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x1f, 0x61, 0x76, 0x67, 0x5f, 0x75, 0x6e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1b, 0x61, 0x76,
	0x67, 0x55, 0x6e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x4c, 0x61, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x35, 0x0a, 0x16, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x57, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x47, 0x0a, 0x20, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x73, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1d, 0x65, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x55, 0x70, 0x74, 0x69,
	0x6d, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0xde, 0x01, 0x0a, 0x1a, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x1a, 0x66, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf3, 0x01, 0x0a, 0x21, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x42, 0x79, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x1a, 0x6d, 0x0a, 0x0c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x42, 0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_metrics_proto_rawDescOnce sync.Once
	file_portal_metrics_proto_rawDescData = file_portal_metrics_proto_rawDesc
)

func file_portal_metrics_proto_rawDescGZIP() []byte {
	file_portal_metrics_proto_rawDescOnce.Do(func() {
		file_portal_metrics_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_metrics_proto_rawDescData)
	})
	return file_portal_metrics_proto_rawDescData
}

var file_portal_metrics_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_portal_metrics_proto_goTypes = []interface{}{
	(*DailyMetricResponse)(nil),               // 0: carbon.portal.metrics.DailyMetricResponse
	(*DailyMetricsByDateResponse)(nil),        // 1: carbon.portal.metrics.DailyMetricsByDateResponse
	(*DailyMetricsByDateByRobotResponse)(nil), // 2: carbon.portal.metrics.DailyMetricsByDateByRobotResponse
	nil,        // 3: carbon.portal.metrics.DailyMetricsByDateResponse.MetricsEntry
	nil,        // 4: carbon.portal.metrics.DailyMetricsByDateByRobotResponse.MetricsEntry
	(*DB)(nil), // 5: carbon.portal.db.DB
}
var file_portal_metrics_proto_depIdxs = []int32{
	5, // 0: carbon.portal.metrics.DailyMetricResponse.db:type_name -> carbon.portal.db.DB
	3, // 1: carbon.portal.metrics.DailyMetricsByDateResponse.metrics:type_name -> carbon.portal.metrics.DailyMetricsByDateResponse.MetricsEntry
	4, // 2: carbon.portal.metrics.DailyMetricsByDateByRobotResponse.metrics:type_name -> carbon.portal.metrics.DailyMetricsByDateByRobotResponse.MetricsEntry
	0, // 3: carbon.portal.metrics.DailyMetricsByDateResponse.MetricsEntry.value:type_name -> carbon.portal.metrics.DailyMetricResponse
	1, // 4: carbon.portal.metrics.DailyMetricsByDateByRobotResponse.MetricsEntry.value:type_name -> carbon.portal.metrics.DailyMetricsByDateResponse
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_portal_metrics_proto_init() }
func file_portal_metrics_proto_init() {
	if File_portal_metrics_proto != nil {
		return
	}
	file_portal_db_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_metrics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyMetricResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_metrics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyMetricsByDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_metrics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyMetricsByDateByRobotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_metrics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_portal_metrics_proto_goTypes,
		DependencyIndexes: file_portal_metrics_proto_depIdxs,
		MessageInfos:      file_portal_metrics_proto_msgTypes,
	}.Build()
	File_portal_metrics_proto = out.File
	file_portal_metrics_proto_rawDesc = nil
	file_portal_metrics_proto_goTypes = nil
	file_portal_metrics_proto_depIdxs = nil
}
