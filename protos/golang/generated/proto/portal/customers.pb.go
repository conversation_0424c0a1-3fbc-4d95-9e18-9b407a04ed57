// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/customers.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FeatureFlags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reports            bool `protobuf:"varint,1,opt,name=reports,proto3" json:"reports,omitempty"`
	Almanac            bool `protobuf:"varint,2,opt,name=almanac,proto3" json:"almanac,omitempty"`
	Jobs               bool `protobuf:"varint,3,opt,name=jobs,proto3" json:"jobs,omitempty"`
	UnvalidatedMetrics bool `protobuf:"varint,4,opt,name=unvalidated_metrics,json=unvalidatedMetrics,proto3" json:"unvalidated_metrics,omitempty"`
	Spatial            bool `protobuf:"varint,5,opt,name=spatial,proto3" json:"spatial,omitempty"`
	VelocityEstimator  bool `protobuf:"varint,6,opt,name=velocity_estimator,json=velocityEstimator,proto3" json:"velocity_estimator,omitempty"`
	// Deprecated: Do not use.
	Explore            bool `protobuf:"varint,7,opt,name=explore,proto3" json:"explore,omitempty"`
	CategoryCollection bool `protobuf:"varint,8,opt,name=category_collection,json=categoryCollection,proto3" json:"category_collection,omitempty"`
	MetricsRedesign    bool `protobuf:"varint,9,opt,name=metrics_redesign,json=metricsRedesign,proto3" json:"metrics_redesign,omitempty"`
}

func (x *FeatureFlags) Reset() {
	*x = FeatureFlags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_customers_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureFlags) ProtoMessage() {}

func (x *FeatureFlags) ProtoReflect() protoreflect.Message {
	mi := &file_portal_customers_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureFlags.ProtoReflect.Descriptor instead.
func (*FeatureFlags) Descriptor() ([]byte, []int) {
	return file_portal_customers_proto_rawDescGZIP(), []int{0}
}

func (x *FeatureFlags) GetReports() bool {
	if x != nil {
		return x.Reports
	}
	return false
}

func (x *FeatureFlags) GetAlmanac() bool {
	if x != nil {
		return x.Almanac
	}
	return false
}

func (x *FeatureFlags) GetJobs() bool {
	if x != nil {
		return x.Jobs
	}
	return false
}

func (x *FeatureFlags) GetUnvalidatedMetrics() bool {
	if x != nil {
		return x.UnvalidatedMetrics
	}
	return false
}

func (x *FeatureFlags) GetSpatial() bool {
	if x != nil {
		return x.Spatial
	}
	return false
}

func (x *FeatureFlags) GetVelocityEstimator() bool {
	if x != nil {
		return x.VelocityEstimator
	}
	return false
}

// Deprecated: Do not use.
func (x *FeatureFlags) GetExplore() bool {
	if x != nil {
		return x.Explore
	}
	return false
}

func (x *FeatureFlags) GetCategoryCollection() bool {
	if x != nil {
		return x.CategoryCollection
	}
	return false
}

func (x *FeatureFlags) GetMetricsRedesign() bool {
	if x != nil {
		return x.MetricsRedesign
	}
	return false
}

type CustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db   *DB    `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Deprecated: Do not use.
	SfdcAccountId            string        `protobuf:"bytes,3,opt,name=sfdc_account_id,json=sfdcAccountId,proto3" json:"sfdc_account_id,omitempty"`
	Emails                   []string      `protobuf:"bytes,4,rep,name=emails,proto3" json:"emails,omitempty"`
	FeatureFlags             *FeatureFlags `protobuf:"bytes,5,opt,name=featureFlags,proto3" json:"featureFlags,omitempty"`
	WeeklyReportHour         int64         `protobuf:"varint,6,opt,name=weekly_report_hour,json=weeklyReportHour,proto3" json:"weekly_report_hour,omitempty"`
	WeeklyReportDay          int64         `protobuf:"varint,7,opt,name=weekly_report_day,json=weeklyReportDay,proto3" json:"weekly_report_day,omitempty"`
	WeeklyReportEnabled      bool          `protobuf:"varint,8,opt,name=weekly_report_enabled,json=weeklyReportEnabled,proto3" json:"weekly_report_enabled,omitempty"`
	WeeklyReportLookbackDays int64         `protobuf:"varint,9,opt,name=weekly_report_lookback_days,json=weeklyReportLookbackDays,proto3" json:"weekly_report_lookback_days,omitempty"`
	WeeklyReportTimezone     string        `protobuf:"bytes,10,opt,name=weekly_report_timezone,json=weeklyReportTimezone,proto3" json:"weekly_report_timezone,omitempty"`
}

func (x *CustomerResponse) Reset() {
	*x = CustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_customers_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerResponse) ProtoMessage() {}

func (x *CustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_customers_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerResponse.ProtoReflect.Descriptor instead.
func (*CustomerResponse) Descriptor() ([]byte, []int) {
	return file_portal_customers_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerResponse) GetDb() *DB {
	if x != nil {
		return x.Db
	}
	return nil
}

func (x *CustomerResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Deprecated: Do not use.
func (x *CustomerResponse) GetSfdcAccountId() string {
	if x != nil {
		return x.SfdcAccountId
	}
	return ""
}

func (x *CustomerResponse) GetEmails() []string {
	if x != nil {
		return x.Emails
	}
	return nil
}

func (x *CustomerResponse) GetFeatureFlags() *FeatureFlags {
	if x != nil {
		return x.FeatureFlags
	}
	return nil
}

func (x *CustomerResponse) GetWeeklyReportHour() int64 {
	if x != nil {
		return x.WeeklyReportHour
	}
	return 0
}

func (x *CustomerResponse) GetWeeklyReportDay() int64 {
	if x != nil {
		return x.WeeklyReportDay
	}
	return 0
}

func (x *CustomerResponse) GetWeeklyReportEnabled() bool {
	if x != nil {
		return x.WeeklyReportEnabled
	}
	return false
}

func (x *CustomerResponse) GetWeeklyReportLookbackDays() int64 {
	if x != nil {
		return x.WeeklyReportLookbackDays
	}
	return 0
}

func (x *CustomerResponse) GetWeeklyReportTimezone() string {
	if x != nil {
		return x.WeeklyReportTimezone
	}
	return ""
}

type Customer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Customer) Reset() {
	*x = Customer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_customers_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_portal_customers_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_portal_customers_proto_rawDescGZIP(), []int{2}
}

func (x *Customer) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Customer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListCustomersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cursor from `ListCustomersResponse.next_page_token` on a previous response.
	// If blank, starts from the beginning.
	PageToken string `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListCustomersRequest) Reset() {
	*x = ListCustomersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_customers_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest) ProtoMessage() {}

func (x *ListCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_customers_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest) Descriptor() ([]byte, []int) {
	return file_portal_customers_proto_rawDescGZIP(), []int{3}
}

func (x *ListCustomersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListCustomersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Customers []*Customer `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	// Cursor for `ListCustomersRequest.page_token`. If blank, there are no
	// further pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListCustomersResponse) Reset() {
	*x = ListCustomersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_customers_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersResponse) ProtoMessage() {}

func (x *ListCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_portal_customers_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListCustomersResponse) Descriptor() ([]byte, []int) {
	return file_portal_customers_proto_rawDescGZIP(), []int{4}
}

func (x *ListCustomersResponse) GetCustomers() []*Customer {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *ListCustomersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_portal_customers_proto protoreflect.FileDescriptor

var file_portal_customers_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x1a, 0x0f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x64, 0x62, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xca, 0x02, 0x0a, 0x0c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c,
	0x61, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x75,
	0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x75, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x12, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69,
	0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x76, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x45, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f,
	0x72, 0x65, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x22,
	0xde, 0x03, 0x0a, 0x10, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x64, 0x62, 0x2e, 0x44, 0x42, 0x52, 0x02, 0x64, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x0f, 0x73, 0x66, 0x64, 0x63, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x73, 0x66, 0x64,
	0x63, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x49, 0x0a, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52,
	0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x2c, 0x0a,
	0x12, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x68,
	0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x77, 0x65, 0x65, 0x6b, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x77,
	0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x77, 0x65, 0x65, 0x6b, 0x6c,
	0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x77,
	0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x6f,
	0x6b, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x18, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f,
	0x6f, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x79, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x77, 0x65,
	0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x77, 0x65, 0x65, 0x6b,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65,
	0x22, 0x32, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x35, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x80, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x09, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x40,
	0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_customers_proto_rawDescOnce sync.Once
	file_portal_customers_proto_rawDescData = file_portal_customers_proto_rawDesc
)

func file_portal_customers_proto_rawDescGZIP() []byte {
	file_portal_customers_proto_rawDescOnce.Do(func() {
		file_portal_customers_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_customers_proto_rawDescData)
	})
	return file_portal_customers_proto_rawDescData
}

var file_portal_customers_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_portal_customers_proto_goTypes = []interface{}{
	(*FeatureFlags)(nil),          // 0: carbon.portal.customers.FeatureFlags
	(*CustomerResponse)(nil),      // 1: carbon.portal.customers.CustomerResponse
	(*Customer)(nil),              // 2: carbon.portal.customers.Customer
	(*ListCustomersRequest)(nil),  // 3: carbon.portal.customers.ListCustomersRequest
	(*ListCustomersResponse)(nil), // 4: carbon.portal.customers.ListCustomersResponse
	(*DB)(nil),                    // 5: carbon.portal.db.DB
}
var file_portal_customers_proto_depIdxs = []int32{
	5, // 0: carbon.portal.customers.CustomerResponse.db:type_name -> carbon.portal.db.DB
	0, // 1: carbon.portal.customers.CustomerResponse.featureFlags:type_name -> carbon.portal.customers.FeatureFlags
	2, // 2: carbon.portal.customers.ListCustomersResponse.customers:type_name -> carbon.portal.customers.Customer
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_portal_customers_proto_init() }
func file_portal_customers_proto_init() {
	if File_portal_customers_proto != nil {
		return
	}
	file_portal_db_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_portal_customers_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureFlags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_customers_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_customers_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_customers_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_customers_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCustomersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_customers_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_portal_customers_proto_goTypes,
		DependencyIndexes: file_portal_customers_proto_depIdxs,
		MessageInfos:      file_portal_customers_proto_msgTypes,
	}.Build()
	File_portal_customers_proto = out.File
	file_portal_customers_proto_rawDesc = nil
	file_portal_customers_proto_goTypes = nil
	file_portal_customers_proto_depIdxs = nil
}
