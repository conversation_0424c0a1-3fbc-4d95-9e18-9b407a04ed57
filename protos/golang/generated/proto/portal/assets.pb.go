// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/assets.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReaperModuleAbLocation int32

const (
	ReaperModuleAbLocation_LOCATION_UNSPECIFIED ReaperModuleAbLocation = 0
	ReaperModuleAbLocation_LOCATION_A           ReaperModuleAbLocation = 1
	ReaperModuleAbLocation_LOCATION_B           ReaperModuleAbLocation = 2
)

// Enum value maps for ReaperModuleAbLocation.
var (
	ReaperModuleAbLocation_name = map[int32]string{
		0: "LOCATION_UNSPECIFIED",
		1: "LOCATION_A",
		2: "LOCATION_B",
	}
	ReaperModuleAbLocation_value = map[string]int32{
		"LOCATION_UNSPECIFIED": 0,
		"LOCATION_A":           1,
		"LOCATION_B":           2,
	}
)

func (x ReaperModuleAbLocation) Enum() *ReaperModuleAbLocation {
	p := new(ReaperModuleAbLocation)
	*p = x
	return p
}

func (x ReaperModuleAbLocation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReaperModuleAbLocation) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_assets_proto_enumTypes[0].Descriptor()
}

func (ReaperModuleAbLocation) Type() protoreflect.EnumType {
	return &file_portal_assets_proto_enumTypes[0]
}

func (x ReaperModuleAbLocation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReaperModuleAbLocation.Descriptor instead.
func (ReaperModuleAbLocation) EnumDescriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{0}
}

type SlayerRowComputerType int32

const (
	SlayerRowComputerType_COMPUTER_UNKNOWN   SlayerRowComputerType = 0
	SlayerRowComputerType_COMPUTER_SINGLE    SlayerRowComputerType = 1
	SlayerRowComputerType_COMPUTER_PRIMARY   SlayerRowComputerType = 2
	SlayerRowComputerType_COMPUTER_SECONDARY SlayerRowComputerType = 3
)

// Enum value maps for SlayerRowComputerType.
var (
	SlayerRowComputerType_name = map[int32]string{
		0: "COMPUTER_UNKNOWN",
		1: "COMPUTER_SINGLE",
		2: "COMPUTER_PRIMARY",
		3: "COMPUTER_SECONDARY",
	}
	SlayerRowComputerType_value = map[string]int32{
		"COMPUTER_UNKNOWN":   0,
		"COMPUTER_SINGLE":    1,
		"COMPUTER_PRIMARY":   2,
		"COMPUTER_SECONDARY": 3,
	}
)

func (x SlayerRowComputerType) Enum() *SlayerRowComputerType {
	p := new(SlayerRowComputerType)
	*p = x
	return p
}

func (x SlayerRowComputerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SlayerRowComputerType) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_assets_proto_enumTypes[1].Descriptor()
}

func (SlayerRowComputerType) Type() protoreflect.EnumType {
	return &file_portal_assets_proto_enumTypes[1]
}

func (x SlayerRowComputerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SlayerRowComputerType.Descriptor instead.
func (SlayerRowComputerType) EnumDescriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{1}
}

type ReaperModuleAbAssetLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedingModuleSerial string                 `protobuf:"bytes,1,opt,name=weeding_module_serial,json=weedingModuleSerial,proto3" json:"weeding_module_serial,omitempty"`
	AbLocation          ReaperModuleAbLocation `protobuf:"varint,2,opt,name=ab_location,json=abLocation,proto3,enum=carbon.portal.assets.ReaperModuleAbLocation" json:"ab_location,omitempty"`
}

func (x *ReaperModuleAbAssetLocation) Reset() {
	*x = ReaperModuleAbAssetLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperModuleAbAssetLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperModuleAbAssetLocation) ProtoMessage() {}

func (x *ReaperModuleAbAssetLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperModuleAbAssetLocation.ProtoReflect.Descriptor instead.
func (*ReaperModuleAbAssetLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{0}
}

func (x *ReaperModuleAbAssetLocation) GetWeedingModuleSerial() string {
	if x != nil {
		return x.WeedingModuleSerial
	}
	return ""
}

func (x *ReaperModuleAbAssetLocation) GetAbLocation() ReaperModuleAbLocation {
	if x != nil {
		return x.AbLocation
	}
	return ReaperModuleAbLocation_LOCATION_UNSPECIFIED
}

type ReaperModuleSingleAssetLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedingModuleSerial string `protobuf:"bytes,1,opt,name=weeding_module_serial,json=weedingModuleSerial,proto3" json:"weeding_module_serial,omitempty"`
}

func (x *ReaperModuleSingleAssetLocation) Reset() {
	*x = ReaperModuleSingleAssetLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReaperModuleSingleAssetLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReaperModuleSingleAssetLocation) ProtoMessage() {}

func (x *ReaperModuleSingleAssetLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReaperModuleSingleAssetLocation.ProtoReflect.Descriptor instead.
func (*ReaperModuleSingleAssetLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{1}
}

func (x *ReaperModuleSingleAssetLocation) GetWeedingModuleSerial() string {
	if x != nil {
		return x.WeedingModuleSerial
	}
	return ""
}

type SlayerRowAssetLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	Row         int32  `protobuf:"varint,2,opt,name=row,proto3" json:"row,omitempty"`
	Id          int32  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SlayerRowAssetLocation) Reset() {
	*x = SlayerRowAssetLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlayerRowAssetLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlayerRowAssetLocation) ProtoMessage() {}

func (x *SlayerRowAssetLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlayerRowAssetLocation.ProtoReflect.Descriptor instead.
func (*SlayerRowAssetLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{2}
}

func (x *SlayerRowAssetLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *SlayerRowAssetLocation) GetRow() int32 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *SlayerRowAssetLocation) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Scanner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string           `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *ScannerLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *Scanner) Reset() {
	*x = Scanner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scanner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scanner) ProtoMessage() {}

func (x *Scanner) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scanner.ProtoReflect.Descriptor instead.
func (*Scanner) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{3}
}

func (x *Scanner) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Scanner) GetLocation() *ScannerLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type ScannerLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Generation:
	//	*ScannerLocation_Slayer
	//	*ScannerLocation_Reaper
	Generation isScannerLocation_Generation `protobuf_oneof:"generation"`
}

func (x *ScannerLocation) Reset() {
	*x = ScannerLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScannerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScannerLocation) ProtoMessage() {}

func (x *ScannerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScannerLocation.ProtoReflect.Descriptor instead.
func (*ScannerLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{4}
}

func (m *ScannerLocation) GetGeneration() isScannerLocation_Generation {
	if m != nil {
		return m.Generation
	}
	return nil
}

func (x *ScannerLocation) GetSlayer() *SlayerRowAssetLocation {
	if x, ok := x.GetGeneration().(*ScannerLocation_Slayer); ok {
		return x.Slayer
	}
	return nil
}

func (x *ScannerLocation) GetReaper() *ReaperModuleAbAssetLocation {
	if x, ok := x.GetGeneration().(*ScannerLocation_Reaper); ok {
		return x.Reaper
	}
	return nil
}

type isScannerLocation_Generation interface {
	isScannerLocation_Generation()
}

type ScannerLocation_Slayer struct {
	Slayer *SlayerRowAssetLocation `protobuf:"bytes,1,opt,name=slayer,proto3,oneof"`
}

type ScannerLocation_Reaper struct {
	Reaper *ReaperModuleAbAssetLocation `protobuf:"bytes,2,opt,name=reaper,proto3,oneof"`
}

func (*ScannerLocation_Slayer) isScannerLocation_Generation() {}

func (*ScannerLocation_Reaper) isScannerLocation_Generation() {}

type Laser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string         `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *LaserLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *Laser) Reset() {
	*x = Laser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Laser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Laser) ProtoMessage() {}

func (x *Laser) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Laser.ProtoReflect.Descriptor instead.
func (*Laser) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{5}
}

func (x *Laser) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Laser) GetLocation() *LaserLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type LaserLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Generation:
	//	*LaserLocation_Slayer
	//	*LaserLocation_Reaper
	Generation isLaserLocation_Generation `protobuf_oneof:"generation"`
}

func (x *LaserLocation) Reset() {
	*x = LaserLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserLocation) ProtoMessage() {}

func (x *LaserLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserLocation.ProtoReflect.Descriptor instead.
func (*LaserLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{6}
}

func (m *LaserLocation) GetGeneration() isLaserLocation_Generation {
	if m != nil {
		return m.Generation
	}
	return nil
}

func (x *LaserLocation) GetSlayer() *SlayerRowAssetLocation {
	if x, ok := x.GetGeneration().(*LaserLocation_Slayer); ok {
		return x.Slayer
	}
	return nil
}

func (x *LaserLocation) GetReaper() *ReaperModuleAbAssetLocation {
	if x, ok := x.GetGeneration().(*LaserLocation_Reaper); ok {
		return x.Reaper
	}
	return nil
}

type isLaserLocation_Generation interface {
	isLaserLocation_Generation()
}

type LaserLocation_Slayer struct {
	Slayer *SlayerRowAssetLocation `protobuf:"bytes,1,opt,name=slayer,proto3,oneof"`
}

type LaserLocation_Reaper struct {
	Reaper *ReaperModuleAbAssetLocation `protobuf:"bytes,2,opt,name=reaper,proto3,oneof"`
}

func (*LaserLocation_Slayer) isLaserLocation_Generation() {}

func (*LaserLocation_Reaper) isLaserLocation_Generation() {}

type TargetCam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string             `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *TargetCamLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *TargetCam) Reset() {
	*x = TargetCam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetCam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetCam) ProtoMessage() {}

func (x *TargetCam) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetCam.ProtoReflect.Descriptor instead.
func (*TargetCam) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{7}
}

func (x *TargetCam) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *TargetCam) GetLocation() *TargetCamLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type TargetCamLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerSerial string `protobuf:"bytes,1,opt,name=scanner_serial,json=scannerSerial,proto3" json:"scanner_serial,omitempty"`
}

func (x *TargetCamLocation) Reset() {
	*x = TargetCamLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetCamLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetCamLocation) ProtoMessage() {}

func (x *TargetCamLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetCamLocation.ProtoReflect.Descriptor instead.
func (*TargetCamLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{8}
}

func (x *TargetCamLocation) GetScannerSerial() string {
	if x != nil {
		return x.ScannerSerial
	}
	return ""
}

type PredictCam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string              `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *PredictCamLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *PredictCam) Reset() {
	*x = PredictCam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictCam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictCam) ProtoMessage() {}

func (x *PredictCam) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictCam.ProtoReflect.Descriptor instead.
func (*PredictCam) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{9}
}

func (x *PredictCam) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *PredictCam) GetLocation() *PredictCamLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type PredictCamLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Generation:
	//	*PredictCamLocation_Slayer
	//	*PredictCamLocation_Reaper
	Generation isPredictCamLocation_Generation `protobuf_oneof:"generation"`
}

func (x *PredictCamLocation) Reset() {
	*x = PredictCamLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictCamLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictCamLocation) ProtoMessage() {}

func (x *PredictCamLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictCamLocation.ProtoReflect.Descriptor instead.
func (*PredictCamLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{10}
}

func (m *PredictCamLocation) GetGeneration() isPredictCamLocation_Generation {
	if m != nil {
		return m.Generation
	}
	return nil
}

func (x *PredictCamLocation) GetSlayer() *SlayerRowAssetLocation {
	if x, ok := x.GetGeneration().(*PredictCamLocation_Slayer); ok {
		return x.Slayer
	}
	return nil
}

func (x *PredictCamLocation) GetReaper() *ReaperModuleSingleAssetLocation {
	if x, ok := x.GetGeneration().(*PredictCamLocation_Reaper); ok {
		return x.Reaper
	}
	return nil
}

type isPredictCamLocation_Generation interface {
	isPredictCamLocation_Generation()
}

type PredictCamLocation_Slayer struct {
	Slayer *SlayerRowAssetLocation `protobuf:"bytes,1,opt,name=slayer,proto3,oneof"`
}

type PredictCamLocation_Reaper struct {
	Reaper *ReaperModuleSingleAssetLocation `protobuf:"bytes,2,opt,name=reaper,proto3,oneof"`
}

func (*PredictCamLocation_Slayer) isPredictCamLocation_Generation() {}

func (*PredictCamLocation_Reaper) isPredictCamLocation_Generation() {}

type CommandComputer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string                   `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *CommandComputerLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *CommandComputer) Reset() {
	*x = CommandComputer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommandComputer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommandComputer) ProtoMessage() {}

func (x *CommandComputer) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommandComputer.ProtoReflect.Descriptor instead.
func (*CommandComputer) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{11}
}

func (x *CommandComputer) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *CommandComputer) GetLocation() *CommandComputerLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type CommandComputerLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
}

func (x *CommandComputerLocation) Reset() {
	*x = CommandComputerLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommandComputerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommandComputerLocation) ProtoMessage() {}

func (x *CommandComputerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommandComputerLocation.ProtoReflect.Descriptor instead.
func (*CommandComputerLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{12}
}

func (x *CommandComputerLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

type RowComputer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string               `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *RowComputerLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *RowComputer) Reset() {
	*x = RowComputer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowComputer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowComputer) ProtoMessage() {}

func (x *RowComputer) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowComputer.ProtoReflect.Descriptor instead.
func (*RowComputer) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{13}
}

func (x *RowComputer) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *RowComputer) GetLocation() *RowComputerLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type RowComputerLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Generation:
	//	*RowComputerLocation_Slayer
	//	*RowComputerLocation_Reaper
	Generation isRowComputerLocation_Generation `protobuf_oneof:"generation"`
}

func (x *RowComputerLocation) Reset() {
	*x = RowComputerLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowComputerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowComputerLocation) ProtoMessage() {}

func (x *RowComputerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowComputerLocation.ProtoReflect.Descriptor instead.
func (*RowComputerLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{14}
}

func (m *RowComputerLocation) GetGeneration() isRowComputerLocation_Generation {
	if m != nil {
		return m.Generation
	}
	return nil
}

func (x *RowComputerLocation) GetSlayer() *SlayerRowComputerLocation {
	if x, ok := x.GetGeneration().(*RowComputerLocation_Slayer); ok {
		return x.Slayer
	}
	return nil
}

func (x *RowComputerLocation) GetReaper() *ReaperModuleSingleAssetLocation {
	if x, ok := x.GetGeneration().(*RowComputerLocation_Reaper); ok {
		return x.Reaper
	}
	return nil
}

type isRowComputerLocation_Generation interface {
	isRowComputerLocation_Generation()
}

type RowComputerLocation_Slayer struct {
	Slayer *SlayerRowComputerLocation `protobuf:"bytes,1,opt,name=slayer,proto3,oneof"`
}

type RowComputerLocation_Reaper struct {
	Reaper *ReaperModuleSingleAssetLocation `protobuf:"bytes,2,opt,name=reaper,proto3,oneof"`
}

func (*RowComputerLocation_Slayer) isRowComputerLocation_Generation() {}

func (*RowComputerLocation_Reaper) isRowComputerLocation_Generation() {}

type SlayerRowComputerLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string                `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	Row         int32                 `protobuf:"varint,2,opt,name=row,proto3" json:"row,omitempty"`
	Type        SlayerRowComputerType `protobuf:"varint,3,opt,name=type,proto3,enum=carbon.portal.assets.SlayerRowComputerType" json:"type,omitempty"`
}

func (x *SlayerRowComputerLocation) Reset() {
	*x = SlayerRowComputerLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlayerRowComputerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlayerRowComputerLocation) ProtoMessage() {}

func (x *SlayerRowComputerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlayerRowComputerLocation.ProtoReflect.Descriptor instead.
func (*SlayerRowComputerLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{15}
}

func (x *SlayerRowComputerLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *SlayerRowComputerLocation) GetRow() int32 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *SlayerRowComputerLocation) GetType() SlayerRowComputerType {
	if x != nil {
		return x.Type
	}
	return SlayerRowComputerType_COMPUTER_UNKNOWN
}

type WeedingModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string                 `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *WeedingModuleLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *WeedingModule) Reset() {
	*x = WeedingModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedingModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedingModule) ProtoMessage() {}

func (x *WeedingModule) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedingModule.ProtoReflect.Descriptor instead.
func (*WeedingModule) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{16}
}

func (x *WeedingModule) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *WeedingModule) GetLocation() *WeedingModuleLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type WeedingModuleLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial  string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	ModuleNumber int32  `protobuf:"varint,2,opt,name=module_number,json=moduleNumber,proto3" json:"module_number,omitempty"`
}

func (x *WeedingModuleLocation) Reset() {
	*x = WeedingModuleLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedingModuleLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedingModuleLocation) ProtoMessage() {}

func (x *WeedingModuleLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedingModuleLocation.ProtoReflect.Descriptor instead.
func (*WeedingModuleLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{17}
}

func (x *WeedingModuleLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *WeedingModuleLocation) GetModuleNumber() int32 {
	if x != nil {
		return x.ModuleNumber
	}
	return 0
}

type Starlink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string            `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *StarlinkLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *Starlink) Reset() {
	*x = Starlink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Starlink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Starlink) ProtoMessage() {}

func (x *Starlink) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Starlink.ProtoReflect.Descriptor instead.
func (*Starlink) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{18}
}

func (x *Starlink) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Starlink) GetLocation() *StarlinkLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type StarlinkLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
}

func (x *StarlinkLocation) Reset() {
	*x = StarlinkLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StarlinkLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarlinkLocation) ProtoMessage() {}

func (x *StarlinkLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarlinkLocation.ProtoReflect.Descriptor instead.
func (*StarlinkLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{19}
}

func (x *StarlinkLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

type Modem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial   string         `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	Location *ModemLocation `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *Modem) Reset() {
	*x = Modem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Modem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Modem) ProtoMessage() {}

func (x *Modem) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Modem.ProtoReflect.Descriptor instead.
func (*Modem) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{20}
}

func (x *Modem) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *Modem) GetLocation() *ModemLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

type ModemLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RobotSerial string `protobuf:"bytes,1,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
}

func (x *ModemLocation) Reset() {
	*x = ModemLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModemLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModemLocation) ProtoMessage() {}

func (x *ModemLocation) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModemLocation.ProtoReflect.Descriptor instead.
func (*ModemLocation) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{21}
}

func (x *ModemLocation) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

type AssetManifest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime      *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	RobotSerial     string                 `protobuf:"bytes,2,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	CommandComputer *CommandComputer       `protobuf:"bytes,4,opt,name=command_computer,json=commandComputer,proto3" json:"command_computer,omitempty"`
	WeedingModules  []*WeedingModule       `protobuf:"bytes,3,rep,name=weeding_modules,json=weedingModules,proto3" json:"weeding_modules,omitempty"`
	RowComputers    []*RowComputer         `protobuf:"bytes,5,rep,name=row_computers,json=rowComputers,proto3" json:"row_computers,omitempty"`
	Scanners        []*Scanner             `protobuf:"bytes,6,rep,name=scanners,proto3" json:"scanners,omitempty"`
	Lasers          []*Laser               `protobuf:"bytes,7,rep,name=lasers,proto3" json:"lasers,omitempty"`
	TargetCams      []*TargetCam           `protobuf:"bytes,8,rep,name=target_cams,json=targetCams,proto3" json:"target_cams,omitempty"`
	PredictCams     []*PredictCam          `protobuf:"bytes,9,rep,name=predict_cams,json=predictCams,proto3" json:"predict_cams,omitempty"`
	Modem           *Modem                 `protobuf:"bytes,10,opt,name=modem,proto3" json:"modem,omitempty"`
	Starlink        *Starlink              `protobuf:"bytes,11,opt,name=starlink,proto3" json:"starlink,omitempty"`
}

func (x *AssetManifest) Reset() {
	*x = AssetManifest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_assets_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetManifest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetManifest) ProtoMessage() {}

func (x *AssetManifest) ProtoReflect() protoreflect.Message {
	mi := &file_portal_assets_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetManifest.ProtoReflect.Descriptor instead.
func (*AssetManifest) Descriptor() ([]byte, []int) {
	return file_portal_assets_proto_rawDescGZIP(), []int{22}
}

func (x *AssetManifest) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AssetManifest) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *AssetManifest) GetCommandComputer() *CommandComputer {
	if x != nil {
		return x.CommandComputer
	}
	return nil
}

func (x *AssetManifest) GetWeedingModules() []*WeedingModule {
	if x != nil {
		return x.WeedingModules
	}
	return nil
}

func (x *AssetManifest) GetRowComputers() []*RowComputer {
	if x != nil {
		return x.RowComputers
	}
	return nil
}

func (x *AssetManifest) GetScanners() []*Scanner {
	if x != nil {
		return x.Scanners
	}
	return nil
}

func (x *AssetManifest) GetLasers() []*Laser {
	if x != nil {
		return x.Lasers
	}
	return nil
}

func (x *AssetManifest) GetTargetCams() []*TargetCam {
	if x != nil {
		return x.TargetCams
	}
	return nil
}

func (x *AssetManifest) GetPredictCams() []*PredictCam {
	if x != nil {
		return x.PredictCams
	}
	return nil
}

func (x *AssetManifest) GetModem() *Modem {
	if x != nil {
		return x.Modem
	}
	return nil
}

func (x *AssetManifest) GetStarlink() *Starlink {
	if x != nil {
		return x.Starlink
	}
	return nil
}

var File_portal_assets_proto protoreflect.FileDescriptor

var file_portal_assets_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x01, 0x0a, 0x1b, 0x52, 0x65,
	0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x62, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x4d, 0x0a,
	0x0b, 0x61, 0x62, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x62, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x61, 0x62, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x1f,
	0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x32, 0x0a, 0x15, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x16, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72,
	0x6f, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x64, 0x0a, 0x07, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x41, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb4, 0x01, 0x0a, 0x0f, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x06,
	0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x61, 0x70,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x62, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x60, 0x0a, 0x05, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x3f, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xb2, 0x01, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x52, 0x6f, 0x77, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41,
	0x62, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x68, 0x0a, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x43, 0x61, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x43, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x3a, 0x0a, 0x11, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x6a, 0x0a, 0x0a,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x44, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbb, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x46, 0x0a, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x52,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x67, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x74, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x49, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3c, 0x0a, 0x17,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0x6c, 0x0a, 0x0b, 0x52, 0x6f,
	0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x45, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbf, 0x01, 0x0a, 0x13, 0x52, 0x6f, 0x77,
	0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x49, 0x0a, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f,
	0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x69, 0x6e, 0x67, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65, 0x61, 0x70, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x91, 0x01, 0x0a, 0x19, 0x53,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x72,
	0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72, 0x6f, 0x77, 0x12, 0x3f, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x53, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x70,
	0x0a, 0x0d, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x5f, 0x0a, 0x15, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x22, 0x66, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x10, 0x53, 0x74, 0x61,
	0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x22, 0x60, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x3f, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6d,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x32, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0xbd, 0x05, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x50, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x0f, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72,
	0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x0e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x0d, 0x72, 0x6f, 0x77, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x72, 0x52, 0x0c, 0x72, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x39, 0x0a, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x40, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6d, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x61,
	0x6d, 0x73, 0x12, 0x43, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x63, 0x61,
	0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x43, 0x61, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6d,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6d, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6d, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x2a, 0x52, 0x0a, 0x16, 0x52, 0x65, 0x61, 0x70, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x62, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x14, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x10, 0x02, 0x2a, 0x70, 0x0a, 0x15, 0x53, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4d,
	0x50, 0x55, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41,
	0x52, 0x59, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52,
	0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x41, 0x52, 0x59, 0x10, 0x03, 0x32, 0x6a, 0x0a, 0x14,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x52, 0x0a, 0x13, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_portal_assets_proto_rawDescOnce sync.Once
	file_portal_assets_proto_rawDescData = file_portal_assets_proto_rawDesc
)

func file_portal_assets_proto_rawDescGZIP() []byte {
	file_portal_assets_proto_rawDescOnce.Do(func() {
		file_portal_assets_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_assets_proto_rawDescData)
	})
	return file_portal_assets_proto_rawDescData
}

var file_portal_assets_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_portal_assets_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_portal_assets_proto_goTypes = []interface{}{
	(ReaperModuleAbLocation)(0),             // 0: carbon.portal.assets.ReaperModuleAbLocation
	(SlayerRowComputerType)(0),              // 1: carbon.portal.assets.SlayerRowComputerType
	(*ReaperModuleAbAssetLocation)(nil),     // 2: carbon.portal.assets.ReaperModuleAbAssetLocation
	(*ReaperModuleSingleAssetLocation)(nil), // 3: carbon.portal.assets.ReaperModuleSingleAssetLocation
	(*SlayerRowAssetLocation)(nil),          // 4: carbon.portal.assets.SlayerRowAssetLocation
	(*Scanner)(nil),                         // 5: carbon.portal.assets.Scanner
	(*ScannerLocation)(nil),                 // 6: carbon.portal.assets.ScannerLocation
	(*Laser)(nil),                           // 7: carbon.portal.assets.Laser
	(*LaserLocation)(nil),                   // 8: carbon.portal.assets.LaserLocation
	(*TargetCam)(nil),                       // 9: carbon.portal.assets.TargetCam
	(*TargetCamLocation)(nil),               // 10: carbon.portal.assets.TargetCamLocation
	(*PredictCam)(nil),                      // 11: carbon.portal.assets.PredictCam
	(*PredictCamLocation)(nil),              // 12: carbon.portal.assets.PredictCamLocation
	(*CommandComputer)(nil),                 // 13: carbon.portal.assets.CommandComputer
	(*CommandComputerLocation)(nil),         // 14: carbon.portal.assets.CommandComputerLocation
	(*RowComputer)(nil),                     // 15: carbon.portal.assets.RowComputer
	(*RowComputerLocation)(nil),             // 16: carbon.portal.assets.RowComputerLocation
	(*SlayerRowComputerLocation)(nil),       // 17: carbon.portal.assets.SlayerRowComputerLocation
	(*WeedingModule)(nil),                   // 18: carbon.portal.assets.WeedingModule
	(*WeedingModuleLocation)(nil),           // 19: carbon.portal.assets.WeedingModuleLocation
	(*Starlink)(nil),                        // 20: carbon.portal.assets.Starlink
	(*StarlinkLocation)(nil),                // 21: carbon.portal.assets.StarlinkLocation
	(*Modem)(nil),                           // 22: carbon.portal.assets.Modem
	(*ModemLocation)(nil),                   // 23: carbon.portal.assets.ModemLocation
	(*AssetManifest)(nil),                   // 24: carbon.portal.assets.AssetManifest
	(*timestamppb.Timestamp)(nil),           // 25: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                   // 26: google.protobuf.Empty
}
var file_portal_assets_proto_depIdxs = []int32{
	0,  // 0: carbon.portal.assets.ReaperModuleAbAssetLocation.ab_location:type_name -> carbon.portal.assets.ReaperModuleAbLocation
	6,  // 1: carbon.portal.assets.Scanner.location:type_name -> carbon.portal.assets.ScannerLocation
	4,  // 2: carbon.portal.assets.ScannerLocation.slayer:type_name -> carbon.portal.assets.SlayerRowAssetLocation
	2,  // 3: carbon.portal.assets.ScannerLocation.reaper:type_name -> carbon.portal.assets.ReaperModuleAbAssetLocation
	8,  // 4: carbon.portal.assets.Laser.location:type_name -> carbon.portal.assets.LaserLocation
	4,  // 5: carbon.portal.assets.LaserLocation.slayer:type_name -> carbon.portal.assets.SlayerRowAssetLocation
	2,  // 6: carbon.portal.assets.LaserLocation.reaper:type_name -> carbon.portal.assets.ReaperModuleAbAssetLocation
	10, // 7: carbon.portal.assets.TargetCam.location:type_name -> carbon.portal.assets.TargetCamLocation
	12, // 8: carbon.portal.assets.PredictCam.location:type_name -> carbon.portal.assets.PredictCamLocation
	4,  // 9: carbon.portal.assets.PredictCamLocation.slayer:type_name -> carbon.portal.assets.SlayerRowAssetLocation
	3,  // 10: carbon.portal.assets.PredictCamLocation.reaper:type_name -> carbon.portal.assets.ReaperModuleSingleAssetLocation
	14, // 11: carbon.portal.assets.CommandComputer.location:type_name -> carbon.portal.assets.CommandComputerLocation
	16, // 12: carbon.portal.assets.RowComputer.location:type_name -> carbon.portal.assets.RowComputerLocation
	17, // 13: carbon.portal.assets.RowComputerLocation.slayer:type_name -> carbon.portal.assets.SlayerRowComputerLocation
	3,  // 14: carbon.portal.assets.RowComputerLocation.reaper:type_name -> carbon.portal.assets.ReaperModuleSingleAssetLocation
	1,  // 15: carbon.portal.assets.SlayerRowComputerLocation.type:type_name -> carbon.portal.assets.SlayerRowComputerType
	19, // 16: carbon.portal.assets.WeedingModule.location:type_name -> carbon.portal.assets.WeedingModuleLocation
	21, // 17: carbon.portal.assets.Starlink.location:type_name -> carbon.portal.assets.StarlinkLocation
	23, // 18: carbon.portal.assets.Modem.location:type_name -> carbon.portal.assets.ModemLocation
	25, // 19: carbon.portal.assets.AssetManifest.create_time:type_name -> google.protobuf.Timestamp
	13, // 20: carbon.portal.assets.AssetManifest.command_computer:type_name -> carbon.portal.assets.CommandComputer
	18, // 21: carbon.portal.assets.AssetManifest.weeding_modules:type_name -> carbon.portal.assets.WeedingModule
	15, // 22: carbon.portal.assets.AssetManifest.row_computers:type_name -> carbon.portal.assets.RowComputer
	5,  // 23: carbon.portal.assets.AssetManifest.scanners:type_name -> carbon.portal.assets.Scanner
	7,  // 24: carbon.portal.assets.AssetManifest.lasers:type_name -> carbon.portal.assets.Laser
	9,  // 25: carbon.portal.assets.AssetManifest.target_cams:type_name -> carbon.portal.assets.TargetCam
	11, // 26: carbon.portal.assets.AssetManifest.predict_cams:type_name -> carbon.portal.assets.PredictCam
	22, // 27: carbon.portal.assets.AssetManifest.modem:type_name -> carbon.portal.assets.Modem
	20, // 28: carbon.portal.assets.AssetManifest.starlink:type_name -> carbon.portal.assets.Starlink
	24, // 29: carbon.portal.assets.AssetManifestService.UploadAssetManifest:input_type -> carbon.portal.assets.AssetManifest
	26, // 30: carbon.portal.assets.AssetManifestService.UploadAssetManifest:output_type -> google.protobuf.Empty
	30, // [30:31] is the sub-list for method output_type
	29, // [29:30] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_portal_assets_proto_init() }
func file_portal_assets_proto_init() {
	if File_portal_assets_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_portal_assets_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperModuleAbAssetLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReaperModuleSingleAssetLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlayerRowAssetLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scanner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScannerLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Laser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetCam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetCamLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictCam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictCamLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommandComputer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommandComputerLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowComputer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowComputerLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlayerRowComputerLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedingModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedingModuleLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Starlink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StarlinkLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Modem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModemLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_assets_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetManifest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_portal_assets_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ScannerLocation_Slayer)(nil),
		(*ScannerLocation_Reaper)(nil),
	}
	file_portal_assets_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*LaserLocation_Slayer)(nil),
		(*LaserLocation_Reaper)(nil),
	}
	file_portal_assets_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*PredictCamLocation_Slayer)(nil),
		(*PredictCamLocation_Reaper)(nil),
	}
	file_portal_assets_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*RowComputerLocation_Slayer)(nil),
		(*RowComputerLocation_Reaper)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_assets_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_assets_proto_goTypes,
		DependencyIndexes: file_portal_assets_proto_depIdxs,
		EnumInfos:         file_portal_assets_proto_enumTypes,
		MessageInfos:      file_portal_assets_proto_msgTypes,
	}.Build()
	File_portal_assets_proto = out.File
	file_portal_assets_proto_rawDesc = nil
	file_portal_assets_proto_goTypes = nil
	file_portal_assets_proto_depIdxs = nil
}
