// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.12
// source: portal/assets.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AssetManifestServiceClient is the client API for AssetManifestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetManifestServiceClient interface {
	UploadAssetManifest(ctx context.Context, in *AssetManifest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type assetManifestServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetManifestServiceClient(cc grpc.ClientConnInterface) AssetManifestServiceClient {
	return &assetManifestServiceClient{cc}
}

func (c *assetManifestServiceClient) UploadAssetManifest(ctx context.Context, in *AssetManifest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/carbon.portal.assets.AssetManifestService/UploadAssetManifest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetManifestServiceServer is the server API for AssetManifestService service.
// All implementations must embed UnimplementedAssetManifestServiceServer
// for forward compatibility
type AssetManifestServiceServer interface {
	UploadAssetManifest(context.Context, *AssetManifest) (*emptypb.Empty, error)
	mustEmbedUnimplementedAssetManifestServiceServer()
}

// UnimplementedAssetManifestServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAssetManifestServiceServer struct {
}

func (UnimplementedAssetManifestServiceServer) UploadAssetManifest(context.Context, *AssetManifest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadAssetManifest not implemented")
}
func (UnimplementedAssetManifestServiceServer) mustEmbedUnimplementedAssetManifestServiceServer() {}

// UnsafeAssetManifestServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetManifestServiceServer will
// result in compilation errors.
type UnsafeAssetManifestServiceServer interface {
	mustEmbedUnimplementedAssetManifestServiceServer()
}

func RegisterAssetManifestServiceServer(s grpc.ServiceRegistrar, srv AssetManifestServiceServer) {
	s.RegisterService(&AssetManifestService_ServiceDesc, srv)
}

func _AssetManifestService_UploadAssetManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssetManifest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetManifestServiceServer).UploadAssetManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/carbon.portal.assets.AssetManifestService/UploadAssetManifest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetManifestServiceServer).UploadAssetManifest(ctx, req.(*AssetManifest))
	}
	return interceptor(ctx, in, info, handler)
}

// AssetManifestService_ServiceDesc is the grpc.ServiceDesc for AssetManifestService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssetManifestService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.portal.assets.AssetManifestService",
	HandlerType: (*AssetManifestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadAssetManifest",
			Handler:    _AssetManifestService_UploadAssetManifest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portal/assets.proto",
}
