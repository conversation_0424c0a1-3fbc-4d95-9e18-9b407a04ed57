// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/category_profile.proto

package portal

import (
	category_profile "github.com/carbonrobotics/protos/golang/generated/proto/category_profile"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedAt *int64 `protobuf:"varint,6,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_category_profile_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_portal_category_profile_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_portal_category_profile_proto_rawDescGZIP(), []int{0}
}

func (x *Metadata) GetUpdatedAt() int64 {
	if x != nil && x.UpdatedAt != nil {
		return *x.UpdatedAt
	}
	return 0
}

type SavedCategoryCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile  *category_profile.CategoryCollection `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	Metadata *Metadata                            `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *SavedCategoryCollection) Reset() {
	*x = SavedCategoryCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_category_profile_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedCategoryCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedCategoryCollection) ProtoMessage() {}

func (x *SavedCategoryCollection) ProtoReflect() protoreflect.Message {
	mi := &file_portal_category_profile_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedCategoryCollection.ProtoReflect.Descriptor instead.
func (*SavedCategoryCollection) Descriptor() ([]byte, []int) {
	return file_portal_category_profile_proto_rawDescGZIP(), []int{1}
}

func (x *SavedCategoryCollection) GetProfile() *category_profile.CategoryCollection {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *SavedCategoryCollection) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type SavedCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile  *category_profile.Category `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	Metadata *Metadata                  `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *SavedCategory) Reset() {
	*x = SavedCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_category_profile_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedCategory) ProtoMessage() {}

func (x *SavedCategory) ProtoReflect() protoreflect.Message {
	mi := &file_portal_category_profile_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedCategory.ProtoReflect.Descriptor instead.
func (*SavedCategory) Descriptor() ([]byte, []int) {
	return file_portal_category_profile_proto_rawDescGZIP(), []int{2}
}

func (x *SavedCategory) GetProfile() *category_profile.Category {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *SavedCategory) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type SavedExpandedCategoryCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile    *SavedCategoryCollection `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	Categories []*SavedCategory         `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *SavedExpandedCategoryCollection) Reset() {
	*x = SavedExpandedCategoryCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_category_profile_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavedExpandedCategoryCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavedExpandedCategoryCollection) ProtoMessage() {}

func (x *SavedExpandedCategoryCollection) ProtoReflect() protoreflect.Message {
	mi := &file_portal_category_profile_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavedExpandedCategoryCollection.ProtoReflect.Descriptor instead.
func (*SavedExpandedCategoryCollection) Descriptor() ([]byte, []int) {
	return file_portal_category_profile_proto_rawDescGZIP(), []int{3}
}

func (x *SavedExpandedCategoryCollection) GetProfile() *SavedCategoryCollection {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *SavedExpandedCategoryCollection) GetCategories() []*SavedCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type UnsavedExpandedCategoryCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile    *category_profile.CategoryCollection `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	Categories []*category_profile.Category         `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *UnsavedExpandedCategoryCollection) Reset() {
	*x = UnsavedExpandedCategoryCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_portal_category_profile_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsavedExpandedCategoryCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsavedExpandedCategoryCollection) ProtoMessage() {}

func (x *UnsavedExpandedCategoryCollection) ProtoReflect() protoreflect.Message {
	mi := &file_portal_category_profile_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsavedExpandedCategoryCollection.ProtoReflect.Descriptor instead.
func (*UnsavedExpandedCategoryCollection) Descriptor() ([]byte, []int) {
	return file_portal_category_profile_proto_rawDescGZIP(), []int{4}
}

func (x *UnsavedExpandedCategoryCollection) GetProfile() *category_profile.CategoryCollection {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *UnsavedExpandedCategoryCollection) GetCategories() []*category_profile.Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_portal_category_profile_proto protoreflect.FileDescriptor

var file_portal_category_profile_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x1a,
	0x27, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3d, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xa6, 0x01, 0x0a, 0x17, 0x53, 0x61, 0x76, 0x65,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x92, 0x01, 0x0a, 0x0d, 0x53, 0x61, 0x76, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x3b, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x44, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61,
	0x6c, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc3, 0x01, 0x0a, 0x1f, 0x53, 0x61, 0x76, 0x65, 0x64, 0x45,
	0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x4d, 0x0a, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xad, 0x01, 0x0a, 0x21,
	0x55, 0x6e, 0x73, 0x61, 0x76, 0x65, 0x64, 0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x45, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x42, 0x40, 0x5a, 0x3e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_category_profile_proto_rawDescOnce sync.Once
	file_portal_category_profile_proto_rawDescData = file_portal_category_profile_proto_rawDesc
)

func file_portal_category_profile_proto_rawDescGZIP() []byte {
	file_portal_category_profile_proto_rawDescOnce.Do(func() {
		file_portal_category_profile_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_category_profile_proto_rawDescData)
	})
	return file_portal_category_profile_proto_rawDescData
}

var file_portal_category_profile_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_portal_category_profile_proto_goTypes = []interface{}{
	(*Metadata)(nil),                            // 0: carbon.portal.category_profile.Metadata
	(*SavedCategoryCollection)(nil),             // 1: carbon.portal.category_profile.SavedCategoryCollection
	(*SavedCategory)(nil),                       // 2: carbon.portal.category_profile.SavedCategory
	(*SavedExpandedCategoryCollection)(nil),     // 3: carbon.portal.category_profile.SavedExpandedCategoryCollection
	(*UnsavedExpandedCategoryCollection)(nil),   // 4: carbon.portal.category_profile.UnsavedExpandedCategoryCollection
	(*category_profile.CategoryCollection)(nil), // 5: carbon.category_profile.CategoryCollection
	(*category_profile.Category)(nil),           // 6: carbon.category_profile.Category
}
var file_portal_category_profile_proto_depIdxs = []int32{
	5, // 0: carbon.portal.category_profile.SavedCategoryCollection.profile:type_name -> carbon.category_profile.CategoryCollection
	0, // 1: carbon.portal.category_profile.SavedCategoryCollection.metadata:type_name -> carbon.portal.category_profile.Metadata
	6, // 2: carbon.portal.category_profile.SavedCategory.profile:type_name -> carbon.category_profile.Category
	0, // 3: carbon.portal.category_profile.SavedCategory.metadata:type_name -> carbon.portal.category_profile.Metadata
	1, // 4: carbon.portal.category_profile.SavedExpandedCategoryCollection.profile:type_name -> carbon.portal.category_profile.SavedCategoryCollection
	2, // 5: carbon.portal.category_profile.SavedExpandedCategoryCollection.categories:type_name -> carbon.portal.category_profile.SavedCategory
	5, // 6: carbon.portal.category_profile.UnsavedExpandedCategoryCollection.profile:type_name -> carbon.category_profile.CategoryCollection
	6, // 7: carbon.portal.category_profile.UnsavedExpandedCategoryCollection.categories:type_name -> carbon.category_profile.Category
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_portal_category_profile_proto_init() }
func file_portal_category_profile_proto_init() {
	if File_portal_category_profile_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_portal_category_profile_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_category_profile_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedCategoryCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_category_profile_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_category_profile_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavedExpandedCategoryCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_portal_category_profile_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnsavedExpandedCategoryCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_portal_category_profile_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_category_profile_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_portal_category_profile_proto_goTypes,
		DependencyIndexes: file_portal_category_profile_proto_depIdxs,
		MessageInfos:      file_portal_category_profile_proto_msgTypes,
	}.Build()
	File_portal_category_profile_proto = out.File
	file_portal_category_profile_proto_rawDesc = nil
	file_portal_category_profile_proto_goTypes = nil
	file_portal_category_profile_proto_depIdxs = nil
}
