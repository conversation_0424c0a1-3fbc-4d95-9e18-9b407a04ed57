// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: rtc/path_plan.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Direction int32

const (
	Direction_DIRECTION_UNSPECIFIED Direction = 0
	Direction_CW                    Direction = 1
	Direction_CCW                   Direction = 2
)

// Enum value maps for Direction.
var (
	Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "CW",
		2: "CCW",
	}
	Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"CW":                    1,
		"CCW":                   2,
	}
)

func (x Direction) Enum() *Direction {
	p := new(Direction)
	*p = x
	return p
}

func (x Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_rtc_path_plan_proto_enumTypes[0].Descriptor()
}

func (Direction) Type() protoreflect.EnumType {
	return &file_rtc_path_plan_proto_enumTypes[0]
}

func (x Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Direction.Descriptor instead.
func (Direction) EnumDescriptor() ([]byte, []int) {
	return file_rtc_path_plan_proto_rawDescGZIP(), []int{0}
}

type PathPlanConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DoHeadlandFirst     bool      `protobuf:"varint,1,opt,name=do_headland_first,json=doHeadlandFirst,proto3" json:"do_headland_first,omitempty"`
	RowHeadingDeg       float32   `protobuf:"fixed32,2,opt,name=row_heading_deg,json=rowHeadingDeg,proto3" json:"row_heading_deg,omitempty"`
	HeadlandWidthM      float32   `protobuf:"fixed32,3,opt,name=headland_width_m,json=headlandWidthM,proto3" json:"headland_width_m,omitempty"`
	TurnDirection       Direction `protobuf:"varint,4,opt,name=turn_direction,json=turnDirection,proto3,enum=carbon.rtc.Direction" json:"turn_direction,omitempty"`
	NumHeadlandPasses   int32     `protobuf:"varint,5,opt,name=num_headland_passes,json=numHeadlandPasses,proto3" json:"num_headland_passes,omitempty"`
	CombinedTurnRadiusM float32   `protobuf:"fixed32,6,opt,name=combined_turn_radius_m,json=combinedTurnRadiusM,proto3" json:"combined_turn_radius_m,omitempty"`
}

func (x *PathPlanConfiguration) Reset() {
	*x = PathPlanConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rtc_path_plan_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PathPlanConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathPlanConfiguration) ProtoMessage() {}

func (x *PathPlanConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_rtc_path_plan_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathPlanConfiguration.ProtoReflect.Descriptor instead.
func (*PathPlanConfiguration) Descriptor() ([]byte, []int) {
	return file_rtc_path_plan_proto_rawDescGZIP(), []int{0}
}

func (x *PathPlanConfiguration) GetDoHeadlandFirst() bool {
	if x != nil {
		return x.DoHeadlandFirst
	}
	return false
}

func (x *PathPlanConfiguration) GetRowHeadingDeg() float32 {
	if x != nil {
		return x.RowHeadingDeg
	}
	return 0
}

func (x *PathPlanConfiguration) GetHeadlandWidthM() float32 {
	if x != nil {
		return x.HeadlandWidthM
	}
	return 0
}

func (x *PathPlanConfiguration) GetTurnDirection() Direction {
	if x != nil {
		return x.TurnDirection
	}
	return Direction_DIRECTION_UNSPECIFIED
}

func (x *PathPlanConfiguration) GetNumHeadlandPasses() int32 {
	if x != nil {
		return x.NumHeadlandPasses
	}
	return 0
}

func (x *PathPlanConfiguration) GetCombinedTurnRadiusM() float32 {
	if x != nil {
		return x.CombinedTurnRadiusM
	}
	return 0
}

var File_rtc_path_plan_proto protoreflect.FileDescriptor

var file_rtc_path_plan_proto_rawDesc = []byte{
	0x0a, 0x13, 0x72, 0x74, 0x63, 0x2f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x22, 0xb8, 0x02, 0x0a, 0x15, 0x50, 0x61, 0x74, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x64,
	0x6f, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x64, 0x6f, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x61,
	0x6e, 0x64, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x6f, 0x77, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0d, 0x72, 0x6f, 0x77, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x67, 0x12,
	0x28, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e, 0x64, 0x5f, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x5f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x6c,
	0x61, 0x6e, 0x64, 0x57, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x75, 0x6d, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x48, 0x65, 0x61, 0x64, 0x6c, 0x61, 0x6e,
	0x64, 0x50, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x62, 0x69,
	0x6e, 0x65, 0x64, 0x5f, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65,
	0x64, 0x54, 0x75, 0x72, 0x6e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4d, 0x2a, 0x37, 0x0a, 0x09,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x49, 0x52,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x43, 0x57, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03,
	0x43, 0x43, 0x57, 0x10, 0x02, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rtc_path_plan_proto_rawDescOnce sync.Once
	file_rtc_path_plan_proto_rawDescData = file_rtc_path_plan_proto_rawDesc
)

func file_rtc_path_plan_proto_rawDescGZIP() []byte {
	file_rtc_path_plan_proto_rawDescOnce.Do(func() {
		file_rtc_path_plan_proto_rawDescData = protoimpl.X.CompressGZIP(file_rtc_path_plan_proto_rawDescData)
	})
	return file_rtc_path_plan_proto_rawDescData
}

var file_rtc_path_plan_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rtc_path_plan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rtc_path_plan_proto_goTypes = []interface{}{
	(Direction)(0),                // 0: carbon.rtc.Direction
	(*PathPlanConfiguration)(nil), // 1: carbon.rtc.PathPlanConfiguration
}
var file_rtc_path_plan_proto_depIdxs = []int32{
	0, // 0: carbon.rtc.PathPlanConfiguration.turn_direction:type_name -> carbon.rtc.Direction
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rtc_path_plan_proto_init() }
func file_rtc_path_plan_proto_init() {
	if File_rtc_path_plan_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rtc_path_plan_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PathPlanConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rtc_path_plan_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rtc_path_plan_proto_goTypes,
		DependencyIndexes: file_rtc_path_plan_proto_depIdxs,
		EnumInfos:         file_rtc_path_plan_proto_enumTypes,
		MessageInfos:      file_rtc_path_plan_proto_msgTypes,
	}.Build()
	File_rtc_path_plan_proto = out.File
	file_rtc_path_plan_proto_rawDesc = nil
	file_rtc_path_plan_proto_goTypes = nil
	file_rtc_path_plan_proto_depIdxs = nil
}
