// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: rtc/implement.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HitchType int32

const (
	HitchType_HITCH_TYPE_UNSPECIFIED HitchType = 0
	HitchType_FIXED                  HitchType = 1
	HitchType_THREE_POINT            HitchType = 2
)

// Enum value maps for HitchType.
var (
	HitchType_name = map[int32]string{
		0: "HITCH_TYPE_UNSPECIFIED",
		1: "FIXED",
		2: "THREE_POINT",
	}
	HitchType_value = map[string]int32{
		"HITCH_TYPE_UNSPECIFIED": 0,
		"FIXED":                  1,
		"THREE_POINT":            2,
	}
)

func (x HitchType) Enum() *HitchType {
	p := new(HitchType)
	*p = x
	return p
}

func (x HitchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitchType) Descriptor() protoreflect.EnumDescriptor {
	return file_rtc_implement_proto_enumTypes[0].Descriptor()
}

func (HitchType) Type() protoreflect.EnumType {
	return &file_rtc_implement_proto_enumTypes[0]
}

func (x HitchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitchType.Descriptor instead.
func (HitchType) EnumDescriptor() ([]byte, []int) {
	return file_rtc_implement_proto_rawDescGZIP(), []int{0}
}

type ImplementDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitchType           HitchType `protobuf:"varint,1,opt,name=hitch_type,json=hitchType,proto3,enum=carbon.rtc.HitchType" json:"hitch_type,omitempty"`
	WidthM              float32   `protobuf:"fixed32,2,opt,name=width_m,json=widthM,proto3" json:"width_m,omitempty"`
	HitchToStartM       float32   `protobuf:"fixed32,3,opt,name=hitch_to_start_m,json=hitchToStartM,proto3" json:"hitch_to_start_m,omitempty"`
	HitchToEndM         float32   `protobuf:"fixed32,4,opt,name=hitch_to_end_m,json=hitchToEndM,proto3" json:"hitch_to_end_m,omitempty"`
	ToolSpeedKmh        float32   `protobuf:"fixed32,5,opt,name=tool_speed_kmh,json=toolSpeedKmh,proto3" json:"tool_speed_kmh,omitempty"`
	TransitionSpeedKmh  float32   `protobuf:"fixed32,6,opt,name=transition_speed_kmh,json=transitionSpeedKmh,proto3" json:"transition_speed_kmh,omitempty"`
	RunUpDistanceM      float32   `protobuf:"fixed32,7,opt,name=run_up_distance_m,json=runUpDistanceM,proto3" json:"run_up_distance_m,omitempty"`
	StraightenDistanceM float32   `protobuf:"fixed32,8,opt,name=straighten_distance_m,json=straightenDistanceM,proto3" json:"straighten_distance_m,omitempty"`
}

func (x *ImplementDefinition) Reset() {
	*x = ImplementDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rtc_implement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImplementDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImplementDefinition) ProtoMessage() {}

func (x *ImplementDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_rtc_implement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImplementDefinition.ProtoReflect.Descriptor instead.
func (*ImplementDefinition) Descriptor() ([]byte, []int) {
	return file_rtc_implement_proto_rawDescGZIP(), []int{0}
}

func (x *ImplementDefinition) GetHitchType() HitchType {
	if x != nil {
		return x.HitchType
	}
	return HitchType_HITCH_TYPE_UNSPECIFIED
}

func (x *ImplementDefinition) GetWidthM() float32 {
	if x != nil {
		return x.WidthM
	}
	return 0
}

func (x *ImplementDefinition) GetHitchToStartM() float32 {
	if x != nil {
		return x.HitchToStartM
	}
	return 0
}

func (x *ImplementDefinition) GetHitchToEndM() float32 {
	if x != nil {
		return x.HitchToEndM
	}
	return 0
}

func (x *ImplementDefinition) GetToolSpeedKmh() float32 {
	if x != nil {
		return x.ToolSpeedKmh
	}
	return 0
}

func (x *ImplementDefinition) GetTransitionSpeedKmh() float32 {
	if x != nil {
		return x.TransitionSpeedKmh
	}
	return 0
}

func (x *ImplementDefinition) GetRunUpDistanceM() float32 {
	if x != nil {
		return x.RunUpDistanceM
	}
	return 0
}

func (x *ImplementDefinition) GetStraightenDistanceM() float32 {
	if x != nil {
		return x.StraightenDistanceM
	}
	return 0
}

var File_rtc_implement_proto protoreflect.FileDescriptor

var file_rtc_implement_proto_rawDesc = []byte{
	0x0a, 0x13, 0x72, 0x74, 0x63, 0x2f, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x22, 0xe9, 0x02, 0x0a, 0x13, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0a, 0x68, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x68, 0x69, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x06, 0x77, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x12, 0x27, 0x0a, 0x10, 0x68, 0x69, 0x74, 0x63,
	0x68, 0x5f, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0d, 0x68, 0x69, 0x74, 0x63, 0x68, 0x54, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4d, 0x12, 0x23, 0x0a, 0x0e, 0x68, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x68, 0x69, 0x74, 0x63, 0x68,
	0x54, 0x6f, 0x45, 0x6e, 0x64, 0x4d, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x6b, 0x6d, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x74, 0x6f, 0x6f, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4b, 0x6d, 0x68, 0x12, 0x30, 0x0a, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x5f, 0x6b, 0x6d, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4b, 0x6d, 0x68, 0x12, 0x29,
	0x0a, 0x11, 0x72, 0x75, 0x6e, 0x5f, 0x75, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x72, 0x75, 0x6e, 0x55, 0x70,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x67, 0x68, 0x74, 0x65, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x73, 0x74, 0x72, 0x61, 0x69, 0x67,
	0x68, 0x74, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x2a, 0x43, 0x0a,
	0x09, 0x48, 0x69, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x48, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x48, 0x52, 0x45, 0x45, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x10, 0x02, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rtc_implement_proto_rawDescOnce sync.Once
	file_rtc_implement_proto_rawDescData = file_rtc_implement_proto_rawDesc
)

func file_rtc_implement_proto_rawDescGZIP() []byte {
	file_rtc_implement_proto_rawDescOnce.Do(func() {
		file_rtc_implement_proto_rawDescData = protoimpl.X.CompressGZIP(file_rtc_implement_proto_rawDescData)
	})
	return file_rtc_implement_proto_rawDescData
}

var file_rtc_implement_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rtc_implement_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rtc_implement_proto_goTypes = []interface{}{
	(HitchType)(0),              // 0: carbon.rtc.HitchType
	(*ImplementDefinition)(nil), // 1: carbon.rtc.ImplementDefinition
}
var file_rtc_implement_proto_depIdxs = []int32{
	0, // 0: carbon.rtc.ImplementDefinition.hitch_type:type_name -> carbon.rtc.HitchType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rtc_implement_proto_init() }
func file_rtc_implement_proto_init() {
	if File_rtc_implement_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rtc_implement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImplementDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rtc_implement_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rtc_implement_proto_goTypes,
		DependencyIndexes: file_rtc_implement_proto_depIdxs,
		EnumInfos:         file_rtc_implement_proto_enumTypes,
		MessageInfos:      file_rtc_implement_proto_msgTypes,
	}.Build()
	File_rtc_implement_proto = out.File
	file_rtc_implement_proto_rawDesc = nil
	file_rtc_implement_proto_goTypes = nil
	file_rtc_implement_proto_depIdxs = nil
}
