// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: rtc/tractor.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RelativePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	XM float32 `protobuf:"fixed32,1,opt,name=x_m,json=xM,proto3" json:"x_m,omitempty"`
	YM float32 `protobuf:"fixed32,2,opt,name=y_m,json=yM,proto3" json:"y_m,omitempty"`
}

func (x *RelativePosition) Reset() {
	*x = RelativePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rtc_tractor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelativePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelativePosition) ProtoMessage() {}

func (x *RelativePosition) ProtoReflect() protoreflect.Message {
	mi := &file_rtc_tractor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelativePosition.ProtoReflect.Descriptor instead.
func (*RelativePosition) Descriptor() ([]byte, []int) {
	return file_rtc_tractor_proto_rawDescGZIP(), []int{0}
}

func (x *RelativePosition) GetXM() float32 {
	if x != nil {
		return x.XM
	}
	return 0
}

func (x *RelativePosition) GetYM() float32 {
	if x != nil {
		return x.YM
	}
	return 0
}

type TractorDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RearAxleCenterPt *RelativePosition `protobuf:"bytes,1,opt,name=rear_axle_center_pt,json=rearAxleCenterPt,proto3" json:"rear_axle_center_pt,omitempty"`
	FixedHitchPt     *RelativePosition `protobuf:"bytes,2,opt,name=fixed_hitch_pt,json=fixedHitchPt,proto3" json:"fixed_hitch_pt,omitempty"`
	ThreePtHitchPt   *RelativePosition `protobuf:"bytes,3,opt,name=three_pt_hitch_pt,json=threePtHitchPt,proto3" json:"three_pt_hitch_pt,omitempty"`
	NosePt           *RelativePosition `protobuf:"bytes,4,opt,name=nose_pt,json=nosePt,proto3" json:"nose_pt,omitempty"`
	MaxWidthM        float32           `protobuf:"fixed32,5,opt,name=max_width_m,json=maxWidthM,proto3" json:"max_width_m,omitempty"`
}

func (x *TractorDefinition) Reset() {
	*x = TractorDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rtc_tractor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorDefinition) ProtoMessage() {}

func (x *TractorDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_rtc_tractor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorDefinition.ProtoReflect.Descriptor instead.
func (*TractorDefinition) Descriptor() ([]byte, []int) {
	return file_rtc_tractor_proto_rawDescGZIP(), []int{1}
}

func (x *TractorDefinition) GetRearAxleCenterPt() *RelativePosition {
	if x != nil {
		return x.RearAxleCenterPt
	}
	return nil
}

func (x *TractorDefinition) GetFixedHitchPt() *RelativePosition {
	if x != nil {
		return x.FixedHitchPt
	}
	return nil
}

func (x *TractorDefinition) GetThreePtHitchPt() *RelativePosition {
	if x != nil {
		return x.ThreePtHitchPt
	}
	return nil
}

func (x *TractorDefinition) GetNosePt() *RelativePosition {
	if x != nil {
		return x.NosePt
	}
	return nil
}

func (x *TractorDefinition) GetMaxWidthM() float32 {
	if x != nil {
		return x.MaxWidthM
	}
	return 0
}

var File_rtc_tractor_proto protoreflect.FileDescriptor

var file_rtc_tractor_proto_rawDesc = []byte{
	0x0a, 0x11, 0x72, 0x74, 0x63, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x22,
	0x34, 0x0a, 0x10, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x0a, 0x03, 0x78, 0x5f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x02, 0x78, 0x4d, 0x12, 0x0f, 0x0a, 0x03, 0x79, 0x5f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x02, 0x79, 0x4d, 0x22, 0xc4, 0x02, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x13, 0x72,
	0x65, 0x61, 0x72, 0x5f, 0x61, 0x78, 0x6c, 0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x72, 0x65, 0x61, 0x72, 0x41, 0x78, 0x6c, 0x65,
	0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x50, 0x74, 0x12, 0x42, 0x0a, 0x0e, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x5f, 0x68, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x48, 0x69, 0x74, 0x63, 0x68, 0x50, 0x74, 0x12, 0x47, 0x0a, 0x11,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x70, 0x74, 0x5f, 0x68, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x70,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x74, 0x68, 0x72, 0x65, 0x65, 0x50, 0x74, 0x48, 0x69,
	0x74, 0x63, 0x68, 0x50, 0x74, 0x12, 0x35, 0x0a, 0x07, 0x6e, 0x6f, 0x73, 0x65, 0x5f, 0x70, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6e, 0x6f, 0x73, 0x65, 0x50, 0x74, 0x12, 0x1e, 0x0a, 0x0b,
	0x6d, 0x61, 0x78, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x57, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x42, 0x3d, 0x5a, 0x3b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_rtc_tractor_proto_rawDescOnce sync.Once
	file_rtc_tractor_proto_rawDescData = file_rtc_tractor_proto_rawDesc
)

func file_rtc_tractor_proto_rawDescGZIP() []byte {
	file_rtc_tractor_proto_rawDescOnce.Do(func() {
		file_rtc_tractor_proto_rawDescData = protoimpl.X.CompressGZIP(file_rtc_tractor_proto_rawDescData)
	})
	return file_rtc_tractor_proto_rawDescData
}

var file_rtc_tractor_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rtc_tractor_proto_goTypes = []interface{}{
	(*RelativePosition)(nil),  // 0: carbon.rtc.RelativePosition
	(*TractorDefinition)(nil), // 1: carbon.rtc.TractorDefinition
}
var file_rtc_tractor_proto_depIdxs = []int32{
	0, // 0: carbon.rtc.TractorDefinition.rear_axle_center_pt:type_name -> carbon.rtc.RelativePosition
	0, // 1: carbon.rtc.TractorDefinition.fixed_hitch_pt:type_name -> carbon.rtc.RelativePosition
	0, // 2: carbon.rtc.TractorDefinition.three_pt_hitch_pt:type_name -> carbon.rtc.RelativePosition
	0, // 3: carbon.rtc.TractorDefinition.nose_pt:type_name -> carbon.rtc.RelativePosition
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_rtc_tractor_proto_init() }
func file_rtc_tractor_proto_init() {
	if File_rtc_tractor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rtc_tractor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelativePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rtc_tractor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rtc_tractor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rtc_tractor_proto_goTypes,
		DependencyIndexes: file_rtc_tractor_proto_depIdxs,
		MessageInfos:      file_rtc_tractor_proto_msgTypes,
	}.Build()
	File_rtc_tractor_proto = out.File
	file_rtc_tractor_proto_rawDesc = nil
	file_rtc_tractor_proto_goTypes = nil
	file_rtc_tractor_proto_depIdxs = nil
}
