// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: veselka/prediction_point.proto

package prediction_point

import (
	pagination "github.com/carbonrobotics/protos/golang/generated/proto/veselka/pagination"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SessionActivityState int32

const (
	SessionActivityState_SESSION_ACTIVITY_STATE_UNSPECIFIED SessionActivityState = 0
	SessionActivityState_ACTIVE                             SessionActivityState = 1
	SessionActivityState_INACTIVE                           SessionActivityState = 2
)

// Enum value maps for SessionActivityState.
var (
	SessionActivityState_name = map[int32]string{
		0: "SESSION_ACTIVITY_STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	SessionActivityState_value = map[string]int32{
		"SESSION_ACTIVITY_STATE_UNSPECIFIED": 0,
		"ACTIVE":                             1,
		"INACTIVE":                           2,
	}
)

func (x SessionActivityState) Enum() *SessionActivityState {
	p := new(SessionActivityState)
	*p = x
	return p
}

func (x SessionActivityState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionActivityState) Descriptor() protoreflect.EnumDescriptor {
	return file_veselka_prediction_point_proto_enumTypes[0].Descriptor()
}

func (SessionActivityState) Type() protoreflect.EnumType {
	return &file_veselka_prediction_point_proto_enumTypes[0]
}

func (x SessionActivityState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionActivityState.Descriptor instead.
func (SessionActivityState) EnumDescriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{0}
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                string  `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`     // s3 url
	Ppcm               float32 `protobuf:"fixed32,3,opt,name=ppcm,proto3" json:"ppcm,omitempty"` // Pixels per centimeter of the image
	CapturedAt         int64   `protobuf:"varint,4,opt,name=captured_at,json=capturedAt,proto3" json:"captured_at,omitempty"`
	UploadedByOperator bool    `protobuf:"varint,5,opt,name=uploaded_by_operator,json=uploadedByOperator,proto3" json:"uploaded_by_operator,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{0}
}

func (x *Image) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Image) GetPpcm() float32 {
	if x != nil {
		return x.Ppcm
	}
	return 0
}

func (x *Image) GetCapturedAt() int64 {
	if x != nil {
		return x.CapturedAt
	}
	return 0
}

func (x *Image) GetUploadedByOperator() bool {
	if x != nil {
		return x.UploadedByOperator
	}
	return false
}

type PredictionPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	X          float32 `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"`
	Y          float32 `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
	Radius     float32 `protobuf:"fixed32,4,opt,name=radius,proto3" json:"radius,omitempty"`
	Image      *Image  `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	CategoryId string  `protobuf:"bytes,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
}

func (x *PredictionPoint) Reset() {
	*x = PredictionPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictionPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictionPoint) ProtoMessage() {}

func (x *PredictionPoint) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictionPoint.ProtoReflect.Descriptor instead.
func (*PredictionPoint) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{1}
}

func (x *PredictionPoint) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PredictionPoint) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *PredictionPoint) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *PredictionPoint) GetRadius() float32 {
	if x != nil {
		return x.Radius
	}
	return 0
}

func (x *PredictionPoint) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *PredictionPoint) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

type ListPredictionPointsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*PredictionPoint     `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Pagination *pagination.Pagination `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPredictionPointsResponse) Reset() {
	*x = ListPredictionPointsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPredictionPointsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPredictionPointsResponse) ProtoMessage() {}

func (x *ListPredictionPointsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPredictionPointsResponse.ProtoReflect.Descriptor instead.
func (*ListPredictionPointsResponse) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{2}
}

func (x *ListPredictionPointsResponse) GetData() []*PredictionPoint {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListPredictionPointsResponse) GetPagination() *pagination.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CategoryProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PredictionPointIds []string `protobuf:"bytes,2,rep,name=prediction_point_ids,json=predictionPointIds,proto3" json:"prediction_point_ids,omitempty"`
}

func (x *CategoryProfile) Reset() {
	*x = CategoryProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryProfile) ProtoMessage() {}

func (x *CategoryProfile) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryProfile.ProtoReflect.Descriptor instead.
func (*CategoryProfile) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{3}
}

func (x *CategoryProfile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CategoryProfile) GetPredictionPointIds() []string {
	if x != nil {
		return x.PredictionPointIds
	}
	return nil
}

type CategoryCollectionProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryProfiles []*CategoryProfile `protobuf:"bytes,2,rep,name=category_profiles,json=categoryProfiles,proto3" json:"category_profiles,omitempty"`
}

func (x *CategoryCollectionProfile) Reset() {
	*x = CategoryCollectionProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryCollectionProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryCollectionProfile) ProtoMessage() {}

func (x *CategoryCollectionProfile) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryCollectionProfile.ProtoReflect.Descriptor instead.
func (*CategoryCollectionProfile) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{4}
}

func (x *CategoryCollectionProfile) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CategoryCollectionProfile) GetCategoryProfiles() []*CategoryProfile {
	if x != nil {
		return x.CategoryProfiles
	}
	return nil
}

type CreatePredictionPointSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId                   string                     `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	RobotIds                  []string                   `protobuf:"bytes,2,rep,name=robot_ids,json=robotIds,proto3" json:"robot_ids,omitempty"`
	CategoryCollectionProfile *CategoryCollectionProfile `protobuf:"bytes,3,opt,name=category_collection_profile,json=categoryCollectionProfile,proto3" json:"category_collection_profile,omitempty"`
}

func (x *CreatePredictionPointSessionRequest) Reset() {
	*x = CreatePredictionPointSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePredictionPointSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePredictionPointSessionRequest) ProtoMessage() {}

func (x *CreatePredictionPointSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePredictionPointSessionRequest.ProtoReflect.Descriptor instead.
func (*CreatePredictionPointSessionRequest) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePredictionPointSessionRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *CreatePredictionPointSessionRequest) GetRobotIds() []string {
	if x != nil {
		return x.RobotIds
	}
	return nil
}

func (x *CreatePredictionPointSessionRequest) GetCategoryCollectionProfile() *CategoryCollectionProfile {
	if x != nil {
		return x.CategoryCollectionProfile
	}
	return nil
}

type CreatePredictionPointSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionId string               `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Status    SessionActivityState `protobuf:"varint,2,opt,name=status,proto3,enum=carbon.veselka.prediction_point.SessionActivityState" json:"status,omitempty"`
}

func (x *CreatePredictionPointSessionResponse) Reset() {
	*x = CreatePredictionPointSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePredictionPointSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePredictionPointSessionResponse) ProtoMessage() {}

func (x *CreatePredictionPointSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePredictionPointSessionResponse.ProtoReflect.Descriptor instead.
func (*CreatePredictionPointSessionResponse) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{6}
}

func (x *CreatePredictionPointSessionResponse) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *CreatePredictionPointSessionResponse) GetStatus() SessionActivityState {
	if x != nil {
		return x.Status
	}
	return SessionActivityState_SESSION_ACTIVITY_STATE_UNSPECIFIED
}

type SessionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelId                   string                     `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	RobotIds                  []string                   `protobuf:"bytes,2,rep,name=robot_ids,json=robotIds,proto3" json:"robot_ids,omitempty"`
	CategoryCollectionProfile *CategoryCollectionProfile `protobuf:"bytes,3,opt,name=category_collection_profile,json=categoryCollectionProfile,proto3" json:"category_collection_profile,omitempty"`
	SessionId                 string                     `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{7}
}

func (x *SessionInfo) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *SessionInfo) GetRobotIds() []string {
	if x != nil {
		return x.RobotIds
	}
	return nil
}

func (x *SessionInfo) GetCategoryCollectionProfile() *CategoryCollectionProfile {
	if x != nil {
		return x.CategoryCollectionProfile
	}
	return nil
}

func (x *SessionInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type SessionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountOfResults int64                `protobuf:"varint,1,opt,name=count_of_results,json=countOfResults,proto3" json:"count_of_results,omitempty"`
	ExpectedCount  int64                `protobuf:"varint,2,opt,name=expected_count,json=expectedCount,proto3" json:"expected_count,omitempty"`
	SessionStatus  SessionActivityState `protobuf:"varint,3,opt,name=session_status,json=sessionStatus,proto3,enum=carbon.veselka.prediction_point.SessionActivityState" json:"session_status,omitempty"`
}

func (x *SessionStatus) Reset() {
	*x = SessionStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionStatus) ProtoMessage() {}

func (x *SessionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionStatus.ProtoReflect.Descriptor instead.
func (*SessionStatus) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{8}
}

func (x *SessionStatus) GetCountOfResults() int64 {
	if x != nil {
		return x.CountOfResults
	}
	return 0
}

func (x *SessionStatus) GetExpectedCount() int64 {
	if x != nil {
		return x.ExpectedCount
	}
	return 0
}

func (x *SessionStatus) GetSessionStatus() SessionActivityState {
	if x != nil {
		return x.SessionStatus
	}
	return SessionActivityState_SESSION_ACTIVITY_STATE_UNSPECIFIED
}

type GetPredictionPointSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionInfo   *SessionInfo   `protobuf:"bytes,1,opt,name=session_info,json=sessionInfo,proto3" json:"session_info,omitempty"`
	SessionStatus *SessionStatus `protobuf:"bytes,2,opt,name=session_status,json=sessionStatus,proto3" json:"session_status,omitempty"`
}

func (x *GetPredictionPointSessionResponse) Reset() {
	*x = GetPredictionPointSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_veselka_prediction_point_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictionPointSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictionPointSessionResponse) ProtoMessage() {}

func (x *GetPredictionPointSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_veselka_prediction_point_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictionPointSessionResponse.ProtoReflect.Descriptor instead.
func (*GetPredictionPointSessionResponse) Descriptor() ([]byte, []int) {
	return file_veselka_prediction_point_proto_rawDescGZIP(), []int{9}
}

func (x *GetPredictionPointSessionResponse) GetSessionInfo() *SessionInfo {
	if x != nil {
		return x.SessionInfo
	}
	return nil
}

func (x *GetPredictionPointSessionResponse) GetSessionStatus() *SessionStatus {
	if x != nil {
		return x.SessionStatus
	}
	return nil
}

var File_veselka_prediction_point_proto protoreflect.FileDescriptor

var file_veselka_prediction_point_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61,
	0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x1a, 0x18, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x63, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x70, 0x63, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x41, 0x74, 0x12, 0x30, 0x0a, 0x14,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x65, 0x64, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0xb4,
	0x01, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76,
	0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65,
	0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b,
	0x61, 0x2e, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x53, 0x0a, 0x0f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x19, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c,
	0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x10, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x49, 0x64, 0x73, 0x12, 0x7a, 0x0a, 0x1b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x19, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x22, 0x94, 0x01, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe0, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x7a, 0x0a, 0x1b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76,
	0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x19, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xbe, 0x01, 0x0a, 0x0d,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a,
	0x10, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x66,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c,
	0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcb, 0x01, 0x0a,
	0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x55, 0x0a, 0x0e, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2e, 0x70, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x58, 0x0a, 0x14, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x02, 0x42, 0x52, 0x5a, 0x50, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x76, 0x65, 0x73, 0x65, 0x6c, 0x6b, 0x61, 0x2f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_veselka_prediction_point_proto_rawDescOnce sync.Once
	file_veselka_prediction_point_proto_rawDescData = file_veselka_prediction_point_proto_rawDesc
)

func file_veselka_prediction_point_proto_rawDescGZIP() []byte {
	file_veselka_prediction_point_proto_rawDescOnce.Do(func() {
		file_veselka_prediction_point_proto_rawDescData = protoimpl.X.CompressGZIP(file_veselka_prediction_point_proto_rawDescData)
	})
	return file_veselka_prediction_point_proto_rawDescData
}

var file_veselka_prediction_point_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_veselka_prediction_point_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_veselka_prediction_point_proto_goTypes = []interface{}{
	(SessionActivityState)(0),                    // 0: carbon.veselka.prediction_point.SessionActivityState
	(*Image)(nil),                                // 1: carbon.veselka.prediction_point.Image
	(*PredictionPoint)(nil),                      // 2: carbon.veselka.prediction_point.PredictionPoint
	(*ListPredictionPointsResponse)(nil),         // 3: carbon.veselka.prediction_point.ListPredictionPointsResponse
	(*CategoryProfile)(nil),                      // 4: carbon.veselka.prediction_point.CategoryProfile
	(*CategoryCollectionProfile)(nil),            // 5: carbon.veselka.prediction_point.CategoryCollectionProfile
	(*CreatePredictionPointSessionRequest)(nil),  // 6: carbon.veselka.prediction_point.CreatePredictionPointSessionRequest
	(*CreatePredictionPointSessionResponse)(nil), // 7: carbon.veselka.prediction_point.CreatePredictionPointSessionResponse
	(*SessionInfo)(nil),                          // 8: carbon.veselka.prediction_point.SessionInfo
	(*SessionStatus)(nil),                        // 9: carbon.veselka.prediction_point.SessionStatus
	(*GetPredictionPointSessionResponse)(nil),    // 10: carbon.veselka.prediction_point.GetPredictionPointSessionResponse
	(*pagination.Pagination)(nil),                // 11: carbon.veselka.pagination.Pagination
}
var file_veselka_prediction_point_proto_depIdxs = []int32{
	1,  // 0: carbon.veselka.prediction_point.PredictionPoint.image:type_name -> carbon.veselka.prediction_point.Image
	2,  // 1: carbon.veselka.prediction_point.ListPredictionPointsResponse.data:type_name -> carbon.veselka.prediction_point.PredictionPoint
	11, // 2: carbon.veselka.prediction_point.ListPredictionPointsResponse.pagination:type_name -> carbon.veselka.pagination.Pagination
	4,  // 3: carbon.veselka.prediction_point.CategoryCollectionProfile.category_profiles:type_name -> carbon.veselka.prediction_point.CategoryProfile
	5,  // 4: carbon.veselka.prediction_point.CreatePredictionPointSessionRequest.category_collection_profile:type_name -> carbon.veselka.prediction_point.CategoryCollectionProfile
	0,  // 5: carbon.veselka.prediction_point.CreatePredictionPointSessionResponse.status:type_name -> carbon.veselka.prediction_point.SessionActivityState
	5,  // 6: carbon.veselka.prediction_point.SessionInfo.category_collection_profile:type_name -> carbon.veselka.prediction_point.CategoryCollectionProfile
	0,  // 7: carbon.veselka.prediction_point.SessionStatus.session_status:type_name -> carbon.veselka.prediction_point.SessionActivityState
	8,  // 8: carbon.veselka.prediction_point.GetPredictionPointSessionResponse.session_info:type_name -> carbon.veselka.prediction_point.SessionInfo
	9,  // 9: carbon.veselka.prediction_point.GetPredictionPointSessionResponse.session_status:type_name -> carbon.veselka.prediction_point.SessionStatus
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_veselka_prediction_point_proto_init() }
func file_veselka_prediction_point_proto_init() {
	if File_veselka_prediction_point_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_veselka_prediction_point_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictionPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPredictionPointsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryCollectionProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePredictionPointSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePredictionPointSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_veselka_prediction_point_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictionPointSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_veselka_prediction_point_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_veselka_prediction_point_proto_goTypes,
		DependencyIndexes: file_veselka_prediction_point_proto_depIdxs,
		EnumInfos:         file_veselka_prediction_point_proto_enumTypes,
		MessageInfos:      file_veselka_prediction_point_proto_msgTypes,
	}.Build()
	File_veselka_prediction_point_proto = out.File
	file_veselka_prediction_point_proto_rawDesc = nil
	file_veselka_prediction_point_proto_goTypes = nil
	file_veselka_prediction_point_proto_depIdxs = nil
}
