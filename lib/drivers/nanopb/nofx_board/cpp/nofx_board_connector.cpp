#include "lib/drivers/nanopb/nofx_board/cpp/nofx_board_connector.hpp"
#include "lib/drivers/nanopb/cpp/udp_client.hpp"

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/generation.hpp>

#include <arpa/inet.h>
#include <chrono>
#include <string.h>
#include <sys/socket.h>

#include <errno.h>
#include <spdlog/spdlog.h>

#define PORT 4243
#define PORT_FB 4244
#define PORT_FB_BROADCAST 4424

constexpr size_t div_ceil(size_t numer, size_t denom) {
  float num = static_cast<float>(numer) / static_cast<float>(denom);
  return (static_cast<float>(static_cast<size_t>(num)) == num) ? static_cast<size_t>(num)
                                                               : static_cast<size_t>(num) + 1;
}
// Want to keep at least 2000ms of data
constexpr size_t ROTARY_BUFFER_SIZE = div_ceil(2000, FASTBIN_BROADCAST_DELAY);

namespace carbon {
namespace nanopb {
namespace nofx_board {
FastBinConnector::FastBinConnector(const std::string &ip, bool use_broadcast)
    : ip_(ip), id_(0), use_broadcast_(use_broadcast), broadcast_data_(ROTARY_BUFFER_SIZE),
      broadcast_thread_(&FastBinConnector::broadcast_loop, this) {}

FastBinConnector::~FastBinConnector() {
  use_broadcast_ = false;
  broadcast_thread_.join();
}

std::unique_ptr<fastbin_Rotary_Reply> FastBinConnector::rotary() {
  if (use_broadcast_) {
    const std::shared_lock lock(mut_);
    if (!broadcast_data_.empty()) {
      return std::make_unique<fastbin_Rotary_Reply>(broadcast_data_.back());
    }
  }
  constexpr size_t buf_size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Reply);
  UDP_Client client(ip_, PORT_FB, buf_size + 4);
  fastbin_Header *hdr = (fastbin_Header *)client.buffer();
  hdr->request_id = ++id_;
  hdr->opcode = ROTARY_TICKS_OPCODE;
  client.send_buff(sizeof(fastbin_Header));
  int received = client.recv_buff();
  if (received < 0 || (size_t)received != buf_size) {
    throw NoFXBoardError("Failed to get response.");
  }
  hdr = (fastbin_Header *)client.buffer();
  if (hdr->opcode != ROTARY_TICKS_REPLY_OPCODE) {
    throw NoFXBoardError("Invalid response");
  }
  return std::make_unique<fastbin_Rotary_Reply>(*((fastbin_Rotary_Reply *)(client.buffer() + sizeof(fastbin_Header))));
}

std::unique_ptr<fastbin_Rotary_Snapshot_Reply> FastBinConnector::rotary_snapshot(uint64_t time_first,
                                                                                 uint64_t time_last) {
  constexpr size_t buf_size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Reply);
  UDP_Client client(ip_, PORT_FB, buf_size + 4);
  fastbin_Header *hdr = (fastbin_Header *)client.buffer();
  fastbin_Rotary_Snapshot_Request *req = (fastbin_Rotary_Snapshot_Request *)(client.buffer() + sizeof(fastbin_Header));
  hdr->request_id = ++id_;
  hdr->opcode = ROTARY_SNAPSHOT_OPCODE;
  req->first_us = time_first;
  req->last_us = time_last;

  client.send_buff(sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Request));
  int received = client.recv_buff();
  if (received < 0 || (size_t)received != buf_size) {
    throw NoFXBoardError("Failed to get response.");
  }
  hdr = (fastbin_Header *)client.buffer();
  if (hdr->opcode != ROTARY_SNAPSHOT_REPLY_OPCODE) {
    throw NoFXBoardError("Invalid response");
  }
  return std::make_unique<fastbin_Rotary_Snapshot_Reply>(
      *((fastbin_Rotary_Snapshot_Reply *)(client.buffer() + sizeof(fastbin_Header))));
}

bool FastBinConnector::await_next(uint32_t timeout_ms) {
  if (!use_broadcast_) {
    return false;
  }
  uint64_t last_ts = 0;
  {
    const std::shared_lock lock(mut_);
    if (!broadcast_data_.empty()) {
      last_ts = broadcast_data_.back().usec;
    }
  }
  return get_next(last_ts, timeout_ms) != nullptr;
}
std::unique_ptr<fastbin_Rotary_Reply> FastBinConnector::get_next(uint64_t prev_time_us, uint32_t timeout_ms) {
  if (!use_broadcast_ || lib::common::bot::BotStopHandler::get().is_stopped()) {
    return nullptr;
  }
  {
    std::shared_lock lock(mut_);
    if (!broadcast_data_.empty() && broadcast_data_.back().usec > prev_time_us) {
      return std::make_unique<fastbin_Rotary_Reply>(broadcast_data_.back());
    }
    auto start_time =
        std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
            .count();
    while (true) {
      auto time_remaining =
          (int64_t)timeout_ms -
          (std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
               .count() -
           start_time);
      if (time_remaining <= 0) {
        return nullptr;
      }
      if (cv_.wait_for(lock, std::chrono::milliseconds(time_remaining)) == std::cv_status::timeout) {
        return nullptr;
      }
      if (!broadcast_data_.empty() && broadcast_data_.back().usec > prev_time_us) {
        return std::make_unique<fastbin_Rotary_Reply>(broadcast_data_.back());
      }
    }
  }
}

void FastBinConnector::broadcast_loop() {
  if (!use_broadcast_) {
    return;
  }
  int sock;
  sockaddr_in broadcast_addr{};
  size_t size = sizeof(fastbin_Rotary_Reply) * (FASTBIN_BROADCAST_COUNT + 1);
  uint8_t *buffer = new uint8_t[size];
  ssize_t recv_len;
  sock = socket(AF_INET, SOCK_DGRAM, 0);
  if (sock < 0) {
    spdlog::warn("Failed to create broadcast socket.");
    return;
  }

  struct timeval tv;
  tv.tv_sec = 1;
  tv.tv_usec = 0;
  if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(struct timeval)) < 0) {
    close(sock);
    throw std::runtime_error("Failed to set socket receive timeout.");
  }

  int optval = 1;
  if (setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval)) < 0) {
    close(sock);
    throw std::runtime_error("Failed to set socket address reuse.");
  }

  if (setsockopt(sock, SOL_SOCKET, SO_REUSEPORT, &optval, sizeof(optval)) < 0) {
    close(sock);
    throw std::runtime_error("Failed to set socket port reuse.");
  }

  // Try to bind to available port
  broadcast_addr.sin_family = AF_INET;
  broadcast_addr.sin_addr.s_addr = htonl(INADDR_ANY);

  broadcast_addr.sin_port = htons(PORT_FB_BROADCAST);
  if (bind(sock, (struct sockaddr *)&broadcast_addr, sizeof(broadcast_addr)) < 0) {
    close(sock);
    throw std::runtime_error("Failed to bind socket.");
  }

  auto bse(lib::common::bot::BotStopHandler::get().create_scoped_event("nofx_bse"));
  while (!bse.is_stopped() && use_broadcast_) {
    recv_len = recvfrom(sock, buffer, size, 0, NULL, 0);
    if (recv_len <= 0) {
      continue;
    }
    {
      const std::unique_lock lock(mut_);
      for (size_t i = 0; i < FASTBIN_BROADCAST_COUNT; ++i) {
        fastbin_Rotary_Reply *data = (fastbin_Rotary_Reply *)(&buffer[sizeof(fastbin_Rotary_Reply) * i]);
        broadcast_data_.push_back(*data);
      }
    }
    cv_.notify_all();
  }
  close(sock);
  return;
}

NoFXBoardConnector::NoFXBoardConnector(const std::string &ip, bool use_broadcast)
    : connector_(ip, PORT, nofx_board_Request_fields, nofx_board_Reply_fields), fb_connector_(ip, use_broadcast) {}
float NoFXBoardConnector::ping() {
  const static int32_t payload = 42;
  auto req = connector_.get();
  req.which_request = nofx_board_Request_ping_tag;
  req.request.ping.x = payload;
  nofx_board_Reply reply = {};
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
                 .count();
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_pong_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  if (reply.reply.pong.x != payload) {
    throw NoFXBoardError("Invalid response payload.");
  }
  return (float)(std::chrono::duration_cast<std::chrono::milliseconds>(
                     std::chrono::system_clock::now().time_since_epoch())
                     .count() -
                 now) /
         1000;
}
void NoFXBoardConnector::hard_reset() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_reset_tag;
  // This will break the connection
  connector_.send_request(req);
}
std::unique_ptr<FirmwareVersion> NoFXBoardConnector::get_version() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_version_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_version_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  return std::make_unique<FirmwareVersion>(reply.reply.version.major, reply.reply.version.minor, 0u);
}
void NoFXBoardConnector::drive(bool forward, bool backward, bool stop, float duty_cycle) {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_drive_solenoids_tag;
  req.request.drive_solenoids.which_request = drive_solenoids_Request_drive_tag;
  req.request.drive_solenoids.request.drive.duty_cycle = duty_cycle;
  if (forward) {
    req.request.drive_solenoids.request.drive.dir = drive_solenoids_Drive_Request_Direction_forward;
  } else if (backward) {
    req.request.drive_solenoids.request.drive.dir = drive_solenoids_Drive_Request_Direction_backward;
  } else if (stop) {
    req.request.drive_solenoids.request.drive.dir = drive_solenoids_Drive_Request_Direction_stop;
  }
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_drive_solenoids_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
}

void NoFXBoardConnector::turn(bool left, bool right, bool straight, float duty_cycle) {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_drive_solenoids_tag;
  req.request.drive_solenoids.which_request = drive_solenoids_Request_turn_tag;
  req.request.drive_solenoids.request.turn.duty_cycle = duty_cycle;
  if (left) {
    req.request.drive_solenoids.request.turn.dir = drive_solenoids_Turn_Request_Direction_left;
  } else if (right) {
    req.request.drive_solenoids.request.turn.dir = drive_solenoids_Turn_Request_Direction_right;
  } else if (straight) {
    req.request.drive_solenoids.request.turn.dir = drive_solenoids_Turn_Request_Direction_straight;
  }
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_drive_solenoids_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
}
void NoFXBoardConnector::park_brake(bool onoff) {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_park_brake_tag;
  req.request.park_brake.onoff = onoff;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_park_brake_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
}
bool NoFXBoardConnector::park_brake_query() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_park_brake_query_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_park_brake_query_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  return reply.reply.park_brake_query.onoff;
}
float NoFXBoardConnector::fuel_gauge() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_sensors_tag;
  req.request.sensors.which_request = sensors_Request_fuel_gauge_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_sensors_tag ||
      reply.reply.sensors.which_reply != sensors_Reply_fuel_gauge_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  return reply.reply.sensors.reply.fuel_gauge.value;
}
void NoFXBoardConnector::config(NoFXRotaryType fl, NoFXRotaryType fr, NoFXRotaryType bl, NoFXRotaryType br) {
  // rotary_encoder_RotaryEncodersConfig_Request_Type
  auto req = connector_.get();
  req.which_request = nofx_board_Request_rotary_encoder_tag;
  req.request.rotary_encoder.which_request = rotary_encoder_Request_config_tag;
  req.request.rotary_encoder.request.config.FL_type = (rotary_encoder_RotaryEncodersConfig_Request_Type)fl;
  req.request.rotary_encoder.request.config.FR_type = (rotary_encoder_RotaryEncodersConfig_Request_Type)fr;
  req.request.rotary_encoder.request.config.BL_type = (rotary_encoder_RotaryEncodersConfig_Request_Type)bl;
  req.request.rotary_encoder.request.config.BR_type = (rotary_encoder_RotaryEncodersConfig_Request_Type)br;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_rotary_encoder_tag ||
      reply.reply.rotary_encoder.which_reply != rotary_encoder_Reply_config_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
}
void NoFXBoardConnector::set_epoch_time() {
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
                 .count();
  auto req = connector_.get();
  req.which_request = nofx_board_Request_time_tag;
  req.request.time.which_request = time_Request_set_tag;
  req.request.time.request.set.has_timestamp = true;
  req.request.time.request.set.timestamp.seconds = (uint32_t)(now / 1000);
  req.request.time.request.set.timestamp.micros = (uint32_t)(now % 1000) * 1000;

  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_time_tag || reply.reply.time.which_reply != time_Reply_ack_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
}
uint64_t NoFXBoardConnector::get_timestamp() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_time_tag;
  req.request.time.which_request = time_Request_get_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_time_tag || reply.reply.time.which_reply != time_Reply_timestamp_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  return (((uint64_t)reply.reply.time.reply.timestamp.seconds) * 1000) +
         (((uint64_t)reply.reply.time.reply.timestamp.micros) / 1000);
}
std::unique_ptr<TimeDebug> NoFXBoardConnector::get_time_debug() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_time_tag;
  req.request.time.which_request = time_Request_debug_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_time_tag || reply.reply.time.which_reply != time_Reply_debug_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  const auto &resp = reply.reply.time.reply.debug;
  return std::make_unique<TimeDebug>((((uint64_t)resp.timestamp.seconds) * 1000000) + (uint64_t)resp.timestamp.micros,
                                     resp.pps_timer_val, resp.pps_ticks, resp.freq_mul, resp.error_ticks,
                                     resp.error_ticks2);
}
void NoFXBoardConnector::verify_history() {
  auto req = connector_.get();
  req.which_request = nofx_board_Request_rotary_encoder_tag;
  req.request.rotary_encoder.which_request = rotary_encoder_Request_history_verify_tag;
  nofx_board_Reply reply = {};
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw NoFXBoardError("Failed to get response.");
  }
  if (reply.which_reply != nofx_board_Reply_rotary_encoder_tag ||
      reply.reply.rotary_encoder.which_reply != rotary_encoder_Reply_history_verify_tag) {
    throw NoFXBoardError("Invalid response type.");
  }
  // TODO return history verify object
}

std::unique_ptr<fastbin_Rotary_Reply> NoFXBoardConnector::rotary() { return fb_connector_.rotary(); }
std::unique_ptr<fastbin_Rotary_Snapshot_Reply> NoFXBoardConnector::rotary_snapshot(uint64_t time_first,
                                                                                   uint64_t time_last) {
  return fb_connector_.rotary_snapshot(time_first, time_last);
}

} // namespace nofx_board
} // namespace nanopb
} // namespace carbon