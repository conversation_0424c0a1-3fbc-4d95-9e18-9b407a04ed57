package nofx

import (
	"encoding/binary"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/nofx_board"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/rotary_encoder"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulation"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

const mph2mmps = 447.04
const broadcastPort = 4424

type NofxBoardSim struct {
	configNode      *config.ConfigTree
	velocityMmSec   float64
	wheelDiameterMm float64
	mode            *atomic.Value
	replayer        *simulation.RecordingReplayer
	lastTimestampMs int64

	TerminateChannel chan bool
	StoppedChannel   chan bool

	started atomic.Bool

	lock sync.Mutex
}

func NewNofxBoardSim(configSubscriber *config.ConfigSubscriber, replayer *simulation.RecordingReplayer) *NofxBoardSim {
	s := &NofxBoardSim{
		configNode:       configSubscriber.GetConfigNode("simulator", ""),
		TerminateChannel: make(chan bool, 1),
		StoppedChannel:   make(chan bool, 1),
		replayer:         replayer,
		lastTimestampMs:  0,
	}
	s.configNode.RegisterCallback(s.readConfig)
	s.readConfig()

	pc, err := net.ListenPacket("udp", ":64548")
	if err != nil {
		log.Fatal(err)
	}

	go func() {
		for {
			buf := make([]byte, 1024)
			n, addr, err := pc.ReadFrom(buf)
			if err != nil {
				continue
			}
			go s.serve(pc, addr, buf[:n])
		}
	}()
	return s
}

func (s *NofxBoardSim) Reload(replayer *simulation.RecordingReplayer) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.replayer = replayer
	s.lastTimestampMs = 0
}

func (s *NofxBoardSim) SetMode(mode *atomic.Value) {
	s.mode = mode
}

func (s *NofxBoardSim) readConfig() {
	s.velocityMmSec = s.configNode.GetNode("velocity_mph").GetFloatValue() * mph2mmps
	s.wheelDiameterMm = s.configNode.GetNode("wheel_diameter_in").GetFloatValue() * 25.4
	logrus.Debugf("NofxBoardSim: Read velocity %v diameter %v", s.velocityMmSec, s.wheelDiameterMm)
}

func (s *NofxBoardSim) SetVelocity(velocityMPH float64) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.velocityMmSec = velocityMPH * mph2mmps
}

func (s *NofxBoardSim) Start() {
	go s.broadcast()
	s.started.Store(true)
}

func (s *NofxBoardSim) Stop() {
	if !s.started.Load() {
		logrus.Infof("Nofx: not started, skipping stop")
		return
	}
	logrus.Infof("Nofx: sending stop to running replay")
	s.TerminateChannel <- true
	logrus.Infof("Nofx: awaiting stop response from running replay")
	<-s.StoppedChannel
	logrus.Infof("Nofx: replay stopped")
	s.started.Store(false)
}

type RotaryReplyRecord struct {
	usec uint64
	fl   uint32
	fr   uint32
	bl   uint32
	br   uint32
}

func (r *RotaryReplyRecord) toBuffer() []byte {
	buf := make([]byte, 24)
	binary.LittleEndian.PutUint64(buf[0:8], r.usec)
	binary.LittleEndian.PutUint32(buf[8:12], r.fl)
	binary.LittleEndian.PutUint32(buf[12:16], r.fr)
	binary.LittleEndian.PutUint32(buf[16:20], r.bl)
	binary.LittleEndian.PutUint32(buf[20:24], r.br)
	return buf
}

func (s *NofxBoardSim) waitForTerminateSignal(sleepTimeUs uint64) bool {
	select {
	case <-s.TerminateChannel:
		logrus.Infof("Nofx: received request to terminate replay")
		return true
	case <-time.After(time.Duration(sleepTimeUs) * time.Microsecond):
		return false
	}
}

func (s *NofxBoardSim) broadcast() {
	conn, err := net.DialUDP("udp", nil, &net.UDPAddr{
		IP:   net.IPv4(255, 255, 255, 255),
		Port: broadcastPort,
	})
	if err != nil {
		panic(err)
	}
	defer conn.Close()

	sleepTimeMs := 25
	tickResolution := 20000
	ticks := uint32(0)
	for {
		if s.mode.Load() == types.Generate {
			if environment.IsReaper() {
				sendTicks(conn, ticks, ticks, ticks, ticks)
			} else if environment.IsSlayer() {
				sendTicks(conn, ticks, -ticks, ticks, -ticks)
			}
			if s.waitForTerminateSignal(uint64(sleepTimeMs * 1000)) {
				break
			}
			s.lock.Lock()
			deltaTicks := float32(s.velocityMmSec) * float32(sleepTimeMs) * float32(tickResolution) / (3.14 * float32(s.wheelDiameterMm) * 1000)
			s.lock.Unlock()
			ticks += uint32(deltaTicks)
		} else if s.mode.Load() == types.Replay {
			if s.waitForTerminateSignal(1) {
				break
			}
			frame, err := s.replayer.GetNextRotary(s.lastTimestampMs)
			if err != nil {
				logrus.WithError(err).Errorf("Error in Get Next Rotary")
				time.Sleep(1 * time.Second)
				continue
			}
			s.lastTimestampMs = int64(frame.GetTimestampUs() / 1000)
			if environment.IsReaper() {
				sendTicksWithTimestamp(conn, uint32(frame.Fl), uint32(frame.Fr), uint32(frame.Bl), uint32(frame.Br), frame.GetTimestampUs())
			} else if environment.IsSlayer() {
				sendTicksWithTimestamp(conn, uint32(frame.Fl), uint32(frame.Fr), uint32(frame.Bl), uint32(frame.Br), frame.GetTimestampUs())
			}
		} else {
			logrus.Fatalf("Nofx: invalid mode %v", s.mode.Load())
		}
	}
	s.StoppedChannel <- true

}

func sendTicksWithTimestamp(conn *net.UDPConn, fl uint32, fr uint32, bl uint32, br uint32, timestampUs uint64) {
	buf1 := &RotaryReplyRecord{usec: timestampUs, fl: fl, fr: fr, bl: bl, br: br}
	buf2 := &RotaryReplyRecord{usec: timestampUs, fl: fl, fr: fr, bl: bl, br: br}
	buf3 := &RotaryReplyRecord{usec: timestampUs, fl: fl, fr: fr, bl: bl, br: br}
	buf4 := &RotaryReplyRecord{usec: timestampUs, fl: fl, fr: fr, bl: bl, br: br}
	buf5 := &RotaryReplyRecord{usec: timestampUs, fl: fl, fr: fr, bl: bl, br: br}
	buf := append(buf1.toBuffer()[:], buf2.toBuffer()[:]...)
	buf = append(buf, buf3.toBuffer()[:]...)
	buf = append(buf, buf4.toBuffer()[:]...)
	buf = append(buf, buf5.toBuffer()[:]...)
	_, err := conn.Write(buf)
	if err != nil {
		log.Fatal(err)
	}
}

func sendTicks(conn *net.UDPConn, fl uint32, fr uint32, bl uint32, br uint32) {
	sendTicksWithTimestamp(conn, fl, fr, bl, br, uint64(time.Now().UnixMicro()))
}

func (s *NofxBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	logrus.Debugf("Nofx_Sim: received request from %v, content_len=(%v)", addr, len(buf))

	req := &nofx_board.Request{}
	proto.Unmarshal(buf, req)

	breqs, _ := protojson.Marshal(req)
	logrus.Debugf("NofxRequest=%v", string(breqs))

	if req.GetPing() != nil {
		reply := &nofx_board.Reply{
			Header: &request.RequestHeader{RequestId: req.GetHeader().RequestId},
			Reply: &nofx_board.Reply_Pong{
				Pong: &diagnostic.Pong{
					X: req.GetPing().X,
				},
			},
		}
		msg, err := proto.Marshal(reply)
		if err != nil {
			logrus.Error(err)
		} else {
			n, err := pc.WriteTo(msg, addr)
			logrus.Debugf("Nofx_sim: Write (ping) done to %v with %v and err=%v", addr, n, err)
		}
	}

	if req.GetRotaryEncoder() != nil && req.GetRotaryEncoder().GetConfig() != nil {
		logrus.Debugf("Nofx_sim received config request")

		reply := &nofx_board.Reply{
			Header: &request.RequestHeader{RequestId: req.GetHeader().RequestId},
			Reply: &nofx_board.Reply_RotaryEncoder{
				RotaryEncoder: &rotary_encoder.Reply{
					Reply: &rotary_encoder.Reply_Config{
						Config: &rotary_encoder.RotaryEncodersConfig_Reply{},
					},
				},
			},
		}
		msg, err := proto.Marshal(reply)
		if err != nil {
			logrus.Error(err)
		} else {
			n, err := pc.WriteTo(msg, addr)
			logrus.Debugf("Nofx_sim: Write (config) done to %v with %v and err=%v", addr, n, err)
		}
	}
}
