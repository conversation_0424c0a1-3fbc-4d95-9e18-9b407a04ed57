# Ingest

A service to accept incoming data in the form of
* Image uploads
* Metrics (remote write) requests

## Running Locally

### One-time Setup

1. Install [ngrok](https://ngrok.com/download) `brew install ngrok/ngrok/ngrok`
2. Get your GITHUB_TOKEN and expose it `GITHUB_TOKEN=<your_token>` (or set it in your `.zshrc`)

### Running

1. Start ngrok proxy `ngrok http 8080` and set an environment variable to the address it forwards to (eg. `NGROK_ADDRESS=https://cd8f-2601-602-8081-9290-b880-69ed-e339-175.ngrok.io` )
2. Go to this project's root directory (`/cloud/golang/services/ingest`) and build the ingest image `make dbuild`
3. Then run ingest: `make drunaws`. Note the AWS defaults are set to the `carbon-automation-test` bucket and `etc`. There may be potential to target maka-pono and overwrite real data.
4. Go to `/cloud/golang/cmd/crtool` directory and follow the instructions to build the tool in the [README](../../cmd/crtool/README.md)
5. Then run the crtool to test a upload:
`./bin/crtool upload --ingest $NGROK_ADDRESS ../../services/ingest/testdata/testImageMetaPair --no-auth`

## Configuration

| Environment Variable                 | Default Value                                         | Description                                                                                                                                      |
|--------------------------------------|-------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|
| AWS_INGEST_BUCKET                    | carbon-automation-testing                             | s3 bucket to upload into (temp)                                                                                                                  |
| AWS_INGEST_KEY_PREFIX                | ingest/upload/                                        | s3 key prefix for uploads (temp)                                                                                                                 |
| AWS_STORAGE_BUCKET                   | carbon-automation-testing                             | s3 bucket to upload final artifacts into                                                                                                         |
| AWS_STORAGE_KEY_PREFIX               | media/                                                | key prefix for storage                                                                                                                           |
| AWS_FURROWS_STORAGE_BUCKET           | carbon-automation-testing                             | key prefix for storage of furrow images                                                                                                          |
| AWS_FURROWS_STORAGE_KEY_PREFIX       | furrows/                                              | key prefix for storage of furrows images                                                                                                         |
| AWS_CHIP_IMAGE_STORAGE_BUCKET        | carbon-automation-testing                             | key prefix for storage of chip_image images                                                                                                      |
| AWS_CHIP_IMAGE_STORAGE_KEY_PREFIX    | chip_image/                                           | key prefix for storage of chip_image images                                                                                                      |
| AWS_PREDICT_BURST_STORAGE_BUCKET     | carbon-automation-testing                             | key prefix for storage of predict bursts                                                                                                         |
| AWS_PREDICT_BURST_STORAGE_KEY_PREFIX |                                                       | key prefix for storage of predict bursts                                                                                                         |
| AWS_REGION                           | us-west-2                                             | aws region                                                                                                                                       |
| AWS_CREDENTIALS_FILE                 |                                                       | optional file with aws creds                                                                                                                     |
| AWS_DIAGNOSTIC_BUCKET                | carbon-automation-testing                             | s3 bucket to upload diagnostic zips into                                                                                                         |
| AWS_DIAGNOSTIC_PREFIX                | diagnostics/                                          | key prefix that uploaded diagnostic zips will have prepended                                                                                     |
| AWS_PLANT_CAPTCHA_BUCKET             | carbon-automation-testing                             | s3 bucket to upload plant-captcha zips into                                                                                                      |
| AWS_PLANT_CAPTCHA_PREFIX             | plant-captcha/                                        | key prefix that uploaded plant-captcha zips will have prepended                                                                                  |
| AUTH0_DOMAIN                         |                                                       | auth0 domain for robot authentication.                                                                                                           |
| AUTH0_ROBOT_AUDIENCE                 | https://robot.carbonrobotics.com                      | auth0 audience for robot authentication.                                                                                                         |
| AUTH0_DATACENTER_AUDIENCE            | https://datacenter.carbonrobotics.com                 | auth0 audience for datacenter authentication.                                                                                                    |
| AUTHORIZED_DATACENTERS               |                                                       | comma separated list of datacenters to accept metrics from (must appear in header)                                                               |
| CONFIG_FILE                          | config.json                                           |                                                                                                                                                  |
| PORT                                 | 8080                                                  | http service port                                                                                                                                |
| ROBOT_PROMETHEUS_TARGET_URL          |                                                       | target robot prometheus write url for forwarding                                                                                                 |
| DATACENTER_PROMETHEUS_TARGET_URL     |                                                       | target datacenter prometheus write url for forwarding                                                                                            |
| VESELKA_URL                          | https://veselka-test.cloud.carbonrobotics.com         | service url of veselka for /internal api                                                                                                         |
| INGEST_SOURCES                       | s3://seth-automation-testing/ingest/watched           | secret session key                                                                                                                               |
| BACKGROUND_INGEST_INTERVAL           | 10m                                                   | interval on which to process background ingestions                                                                                               |
| REDIS_URL                            |                                                       | redis host:port                                                                                                                                  |
| NAMESPACE                            | default                                               | namespace app in which is running                                                                                                                |
| DISABLE_INGESTION                    |                                                       | if this is set to true, files will still be accepted, but not processed. (this should not be on for long periods of time)                        |
| DISABLE_ARTIFACT_TYPES               |                                                       | comma separated list of artifact types that are disabled, attempting to ingest one of these artifact times will be rejected with 400 status code |
| DISABLE_VESELKA_POST_ARTIFACT_TYPES  |                                                       | comma separated list of artifact types that will skip posting to veselka                                                                         |
| TEST_MODE                            |                                                       | if this is set to true, auth is disabled and solo runner enabled                                                                                 |
| PERF_MODE                            |                                                       | enables go perf tools on /internal/perf  *note* also enabled in test mode                                                                        |

### Config File
A json configuration file exists for configuration of values that can dynamically change during runtime. 

An example of this file exists as config.json.example with the following configuration values:

| Json Key                           | Type             | Description                                                                  |
|------------------------------------|------------------|------------------------------------------------------------------------------|
| robot_metric_target_overrides      | map              | robod id to prometheus remote write target                                   |
| datacenter_metric_target_overrides | map              | datacenter id to prometheus remote write target                              |

## Prerequisites
Required AWS Permissions (example) *This Policy should be created by [terraform](../../../terraform/aws/apps.tf)
```
{
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Action": [
          "s3:ListBucket",
          "s3:AbortMultipartUpload",
          "s3:ListBucketMultipartUploads"
        ],
        "Resource": [
          "arn:aws:s3:::<upload-bucket>",
          "arn:aws:s3:::<storage-bucket>",
          "arn:aws:s3:::<diagnostic-bucket>"
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "s3:PutObject",
          "s3:PutObjectTagging",
          "s3:GetObject",
          "s3:ListMultipartUploadParts",
          "s3:AbortMultipartUpload"
        ],
        "Resource": [
          "arn:aws:s3:::<upload-bucket>/*",
          "arn:aws:s3:::<storage-bucket>/*",
          "arn:aws:s3:::<diagnostic-bucket>/*",
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "s3:DeleteObject"
        ],
        "Resource": [
          "arn:aws:s3:::<upload-bucket>/*",
          "arn:aws:s3:::<storage-bucket>/*",
          "arn:aws:s3:::<diagnostic-bucket>/*"
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectTagging",
          "s3:DeleteObject"
        ],
        "Resource": [
          "arn:aws:s3:::<alternate-ingest-bucket>",
          "arn:aws:s3:::<alternate-ingest-bucket>/*"
        ]
      }
    ]
  }
```

## API
| method | route                        | params | body | description                                              |
|--------|------------------------------|--------|------|----------------------------------------------------------|
| GET    | /health                      |        |      |                                                          |
| GET    | /metrics                     |        |      | prometheus metrics                                       |
| POST   | /robot/chunked               |        |      | chunked robot upload                                     |
| POST   | /robot/prometheus/write      |        |      | auth0 robot api protected prometheus write endpoint      |
| POST   | /datacenter/prometheus/write |        |      | auth0 datacenter api protected prometheus write endpoint |
| ANY    | /internal/perf               |        |      | go pprof endpoint if perf mode enabled                   |
