package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"path"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/carbonrobotics/crgo/metrics"
	"github.com/fsnotify/fsnotify"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/kelseyhightower/envconfig"
	"github.com/spf13/viper"

	"github.com/carbonrobotics/cloud/golang/pkg/auth/middleware"
	craws "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/build"
	"github.com/carbonrobotics/cloud/golang/pkg/carbon"
	crstr "github.com/carbonrobotics/cloud/golang/pkg/strings"
	"github.com/carbonrobotics/cloud/golang/pkg/veselka"
)

const (
	configRobotKeyMetricTargetOverride      = "robot_metric_target_overrides"
	configDatacenterKeyMetricTargetOverride = "datacenter_metric_target_overrides"
)

var banner = strings.ReplaceAll(`
  ___                  _   
 |_ _|_ _  __ _ ___ __| |_ 
  | || ' \/ _q / -_|_-<  _|
 |___|_||_\__, \___/__/\__|
           |___/`, "q", "`")

type IngestService struct {
	AWSIngestBucket                 string        `envconfig:"AWS_INGEST_BUCKET" default:"carbon-automation-testing"`
	AWSIngestKeyPrefix              string        `envconfig:"AWS_INGEST_KEY_PREFIX" default:"test/ingest/upload/"`
	AWSStorageBucket                string        `envconfig:"AWS_STORAGE_BUCKET" default:"carbon-automation-testing"`
	AWSStorageKeyPrefix             string        `envconfig:"AWS_STORAGE_KEY_PREFIX" default:"media/"`
	AWSPredictBurstStorageBucket    string        `envconfig:"AWS_PREDICT_BURST_STORAGE_BUCKET" default:"carbon-automation-testing"`
	AWSPredictBurstStorageKeyPrefix string        `envconfig:"AWS_PREDICT_BURST_STORAGE_KEY_PREFIX"`
	AWSFurrowsStorageBucket         string        `envconfig:"AWS_FURROWS_STORAGE_BUCKET" default:"carbon-automation-testing"`
	AWSFurrowsStorageKeyPrefix      string        `envconfig:"AWS_FURROWS_STORAGE_KEY_PREFIX" default:"furrows/"`
	AWSChipImageStorageBucket       string        `envconfig:"AWS_CHIP_IMAGE_STORAGE_BUCKET" default:"carbon-automation-testing"`
	AWSChipImageStorageKeyPrefix    string        `envconfig:"AWS_CHIP_IMAGE_STORAGE_KEY_PREFIX" default:"chip_image/"`
	AWSRegion                       string        `envconfig:"AWS_REGION" default:"us-west-2"`
	AWSCredentialsFile              string        `envconfig:"AWS_CREDENTIALS_FILE"`
	AWSDiagnosticsBucket            string        `envconfig:"AWS_DIAGNOSTIC_BUCKET" default:"carbon-automation-testing"`
	AWSDiagnosticsPrefix            string        `envconfig:"AWS_DIAGNOSTIC_PREFIX" default:"diagnostics/"`
	AWSPlantCaptchaBucket           string        `envconfig:"AWS_PLANT_CAPTCHA_BUCKET" default:"carbon-automation-testing"`
	AWSPlantCaptchaPrefix           string        `envconfig:"AWS_PLANT_CAPTCHA_PREFIX" default:"plant-captcha/"`
	Auth0Domain                     string        `envconfig:"AUTH0_DOMAIN"`
	Auth0RobotAudience              string        `envconfig:"AUTH0_ROBOT_AUDIENCE" default:"https://robot.carbonrobotics.com"`
	Auth0DatacenterAudience         string        `envconfig:"AUTH0_DATACENTER_AUDIENCE" default:"https://datacenter.carbonrobotics.com"`
	AuthorizedDatacenters           []string      `envconfig:"AUTHORIZED_DATACENTERS"`
	ConfigFile                      string        `envconfig:"CONFIG_FILE" default:"config.json"`
	Port                            int           `envconfig:"PORT" default:"8080"`
	RobotPrometheusTargetURL        string        `envconfig:"ROBOT_PROMETHEUS_TARGET_URL"`
	DatacenterPrometheusTargetURL   string        `envconfig:"DATACENTER_PROMETHEUS_TARGET_URL"`
	VeselkaURL                      string        `envconfig:"VESELKA_URL" default:"https://veselka-test.cloud.carbonrobotics.com"`
	IngestSources                   []string      `envconfig:"INGEST_SOURCES" default:"s3://seth-automation-testing/ingest/watched/"`
	BackgroundIngestInterval        time.Duration `envconfig:"BACKGROUND_INGEST_INTERVAL" default:"10m"`
	RedisURL                        string        `envconfig:"REDIS_URL"`
	Namespace                       string        `envconfig:"NAMESPACE" default:"default"`
	DisableIngestion                bool          `envconfig:"DISABLE_INGESTION"`
	DisabledArtifactTypes           []string      `envconfig:"DISABLE_ARTIFACT_TYPES"`
	DisableVeselkaPostArtifactTypes []string      `envconfig:"DISABLE_VESELKA_POST_ARTIFACT_TYPES"`
	TestMode                        bool          `envconfig:"TEST_MODE"`
	PerfMode                        bool          `envconfig:"PERF_MODE"`

	viper         *viper.Viper
	s3Facade      S3Facade
	ingestEventCh chan ingestEvent
	wg            sync.WaitGroup
	veselka       Veselka
}

func (is *IngestService) Run(ctx context.Context) (err error) {
	fmt.Println(banner)
	log.Println("Version:", build.Version)
	log.Println("Commit:", build.Commit)
	log.Println("BuiltOn:", build.BuiltOn)
	if err := envconfig.Process("", is); err != nil {
		return err
	}
	if is.TestMode {
		log.Println("🤘!!!TestMode Enabled!!!🤘")
	}

	if err := is.loadConfigFile(is.ConfigFile); err != nil {
		return err
	}

	var credential *credentials.Credentials
	if is.AWSCredentialsFile != "" {
		credential = credentials.NewSharedCredentials(is.AWSCredentialsFile, "")
	}
	sess, err := session.NewSession(&aws.Config{
		Credentials: credential,
		Region:      aws.String(is.AWSRegion),
	})
	if err != nil {
		return err
	}

	httpClient := retryablehttp.NewClient()
	httpClient.HTTPClient.CheckRedirect = CheckRedirectNoRedirect
	is.veselka = veselka.New(is.VeselkaURL, veselka.OptionHttpClient(httpClient.StandardClient()), veselka.OptionInternal())
	is.s3Facade = craws.NewS3Facade(sess)

	go is.startIngestEventProcessor(ctx)
	if err := is.startBackgroundActions(ctx); err != nil {
		return err
	}

	auth0RobotMiddleware, err := middleware.Auth0JWTValidatingGin(is.Auth0Domain, is.Auth0RobotAudience)
	if err != nil {
		return err
	}
	auth0DatacenterMiddleware, err := middleware.Auth0JWTValidatingGin(is.Auth0Domain, is.Auth0DatacenterAudience)
	if err != nil {
		return err
	}
	if is.TestMode {
		auth0RobotMiddleware = noOpMiddleware
		auth0DatacenterMiddleware = noOpMiddleware
	}

	router := gin.New()
	router.Use(gin.LoggerWithWriter(gin.DefaultWriter, "/health", "/metrics", "/prometheus/write"))
	router.Use(gin.Recovery())
	router.Use(metrics.PrometheusMiddlewareGin())

	router.GET("/health", is.healthHandler)
	router.GET("/metrics", metrics.HandlerGin())

	// robot upload
	router.POST("/robot/chunked", auth0RobotMiddleware, robotHeadersValidator, is.metaArtifactChunkedUploadHandler)
	router.POST("/robot/prometheus/write", auth0RobotMiddleware, robotHeadersValidator, is.promForwardHandler)

	// datacenter upload
	router.POST("/datacenter/prometheus/write", auth0DatacenterMiddleware, datacenterHeadersValidator(is.AuthorizedDatacenters), is.promForwardHandler)

	// perf
	if is.TestMode || is.PerfMode {
		pprof.Register(router, "internal/perf")
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", is.Port),
		Handler: router,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	<-ctx.Done()
	log.Println("shutting down...")
	is.wg.Wait()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		return fmt.Errorf("server forced to shutdown: %w", err)
	}
	return nil
}

func robotHeadersValidator(c *gin.Context) {
	robot := c.GetHeader(carbon.RobotHTTPHeader)
	gen := c.GetHeader(carbon.GenerationHTTPHeader)
	if len(robot) < 1 || len(gen) < 1 {
		log.Println("WARNING: robot request missing required header, robot:", robot, "gen:", gen)
	}
	c.Next()
}

func datacenterHeadersValidator(validDatacenters []string) func(c *gin.Context) {
	return func(c *gin.Context) {
		datacenter := c.GetHeader(carbon.DatacenterHTTPHeader)
		if !crstr.InSlice(validDatacenters, datacenter) {
			log.Println("invalid datacenter request header - datacenter:", datacenter)
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid datacenter"})
			return
		}
		c.Next()
	}
}

func (is *IngestService) loadConfigFile(filename string) error {
	is.viper = viper.New()
	is.viper.SetConfigFile(filename)
	if err := is.viper.ReadInConfig(); err != nil {
		return err
	}
	is.viper.OnConfigChange(func(e fsnotify.Event) {
		log.Println("Config file changed:", e.Name)
	})
	is.viper.WatchConfig()
	return nil
}

func (is *IngestService) healthHandler(c *gin.Context) {
	c.String(http.StatusOK, "I am Alive!")
}

func noOpMiddleware(c *gin.Context) {
	c.Next()
}

func (is *IngestService) ingestImageMetaPair(bucket, key string) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	imageExt := ""
	switch {
	case strings.HasSuffix(key, pngExt):
		imageExt = pngExt
	case strings.HasSuffix(key, npzExt):
		imageExt = npzExt
	default:
		return fmt.Errorf("unsupported image extension: %s", key)
	}

	base := strings.TrimSuffix(strings.TrimSuffix(key, imageExt), metaExt)
	originalImageKey := base + imageExt
	originalMetaKey := base + metaExt

	metaObj, err := is.s3Facade.GetObjectWithContext(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(originalMetaKey),
	})
	if err != nil {
		return fmt.Errorf("failed to get bucket: %s key: %s - %w", bucket, originalMetaKey, err)
	}
	defer metaObj.Body.Close()
	metaBytes, err := io.ReadAll(metaObj.Body)
	if err != nil {
		return fmt.Errorf("failed to read meta bucket: %s key: %s - %w", bucket, originalMetaKey, err)
	}
	// read meta, populate fields
	metadata, err := ParseImageMeta(metaBytes)
	if err != nil {
		return fmt.Errorf("failed to parse metadata: %w", err)
	}
	if err := metadata.Validate(); err != nil {
		return fmt.Errorf("invalid metadata: %w", err)
	}

	awsStorageKeyPrefix := is.AWSStorageKeyPrefix
	awsStorageBucket := is.AWSStorageBucket
	switch metadata.ArtifactType() {
	case FurrowsArtifact:
		awsStorageKeyPrefix = is.AWSFurrowsStorageKeyPrefix
		awsStorageBucket = is.AWSFurrowsStorageBucket
	case ChipImageArtifact:
		awsStorageKeyPrefix = is.AWSChipImageStorageKeyPrefix
		awsStorageBucket = is.AWSChipImageStorageBucket
	}

	prefix := metadata.CalculateStoragePrefix(awsStorageKeyPrefix)

	_, metaFilename := path.Split(originalMetaKey)
	_, imageFilename := path.Split(originalImageKey)
	targetImageKey := path.Join(prefix, imageFilename)
	targetMetaKey := path.Join(prefix, metaFilename)

	log.Println("processing image/meta pair:", base)
	log.Printf(" image: %s -> %s\n", originalImageKey, targetImageKey)
	// move image & meta files
	if err := is.MoveS3Object(ctx, bucket, originalImageKey, awsStorageBucket, targetImageKey); err != nil {
		return fmt.Errorf("failed to move image key: %q target: %q error: %w", key, targetImageKey, err)
	}
	log.Printf(" meta: %s -> %s\n", originalMetaKey, targetMetaKey)
	if err := is.MoveS3Object(ctx, bucket, originalMetaKey, awsStorageBucket, targetMetaKey); err != nil {
		return fmt.Errorf("failed to move meta key: %q target: %q error: %w", key, targetMetaKey, err)
	}
	artifactPairsCounter.WithLabelValues(metadata.ArtifactType()).Inc()

	if slices.Contains(is.DisableVeselkaPostArtifactTypes, metadata.ArtifactType()) {
		veselkaPostsSkipedtotal.WithLabelValues(metadata.ArtifactType()).Inc()
		log.Println("artifact type", metadata.ArtifactType(), "veselka posting disabled. skipping veselka post")
		return nil
	}

	// post to Veselka
	imageURL := fmt.Sprint("s3://", path.Join(awsStorageBucket, targetImageKey))
	metaURL := fmt.Sprint("s3://", path.Join(awsStorageBucket, targetMetaKey))
	switch metadata.ArtifactType() {
	case FurrowsArtifact:
		err = is.postVeselkaFurrowImage(ctx, veselka.PostFurrowImageParameters{
			ImageURL:    imageURL,
			MetadataURL: metaURL,
		})
	case ChipImageArtifact:
		err = is.postVeselkaChipImage(ctx, veselka.PostRobotChipImageParameters{
			ImageURL:    imageURL,
			MetadataURL: metaURL,
		})
	default:
		err = is.postVeselkaImage(ctx, veselka.PostImageParameters{
			ImageURL:    imageURL,
			MetadataURL: metaURL,
		})
	}
	return
}

func (is *IngestService) postVeselkaImage(ctx context.Context, params veselka.PostImageParameters) error {
	postCtx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	if err := is.veselka.PostImage(postCtx, params); err != nil {
		veselkaMetaPostErrorTotal.Inc()
		byt, _ := json.Marshal(params) // error thrown away because it doesn't help us
		return fmt.Errorf("failed to post image to veselka %s - %w", string(byt), err)
	}
	veselkaMetaPostTotal.Inc()
	return nil
}

func (is *IngestService) postVeselkaFurrowImage(ctx context.Context, params veselka.PostFurrowImageParameters) error {
	postCtx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	if err := is.veselka.PostFurrowImage(postCtx, params); err != nil {
		veselkaFurrowMetaPostErrorTotal.Inc()
		byt, _ := json.Marshal(params) // error thrown away because it doesn't help us
		return fmt.Errorf("failed to post furrow image to veselka %s - %w", string(byt), err)
	}
	veselkaFurrowMetaPostTotal.Inc()
	return nil
}

func (is *IngestService) postVeselkaChipImage(ctx context.Context, params veselka.PostRobotChipImageParameters) error {
	postCtx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	if err := is.veselka.PostRobotChipImage(postCtx, params); err != nil {
		veselkaChipImageMetaPostErrorTotal.Inc()
		byt, _ := json.Marshal(params) // error thrown away because it doesn't help us
		return fmt.Errorf("failed to post chip_image to veselka %s - %w", string(byt), err)
	}
	veselkaChipImageMetaPostTotal.Inc()
	return nil
}

func (is *IngestService) postVeselkaPlantCaptcha(ctx context.Context, params veselka.PostCaptchaParameters) error {
	postCtx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	if err := is.veselka.PostPlantCaptcha(postCtx, params); err != nil {
		veselkaPlantCaptchaPostErrorTotal.Inc()
		byt, _ := json.Marshal(params) // error thrown away because it doesn't help us
		return fmt.Errorf("failed to post plant captcha to veselka %s - %w", string(byt), err)
	}
	veselkaPlantCaptchaPostTotal.Inc()
	return nil
}

func (is *IngestService) ingestZipMetaPair(bucket, key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	base := strings.TrimSuffix(strings.TrimSuffix(key, zipExt), metaExt)
	originalZipKey := base + zipExt
	originalMetaKey := base + metaExt

	metaObj, err := is.s3Facade.GetObjectWithContext(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(originalMetaKey),
	})
	if err != nil {
		return fmt.Errorf("failed to get meta object %q - %w", originalMetaKey, err)
	}
	defer metaObj.Body.Close()
	b, err := io.ReadAll(metaObj.Body)
	if err != nil {
		return fmt.Errorf("failed to read meta from %q - %w", originalMetaKey, err)
	}
	metadata, err := ParseImageMeta(b)
	if err != nil {
		return fmt.Errorf("failed to parse meta from %q - %w", originalMetaKey, err)
	}

	artifactType := metadata.ArtifactType()
	storageBucket := is.AWSStorageBucket
	storagePrefix := is.AWSStorageKeyPrefix
	switch artifactType {
	case LightWeightBurstRecordArtifact, P2PArtifact:
		storagePrefix = path.Join(is.AWSStorageKeyPrefix, metadata.ArtifactType(), metadata.CapturedDate())
	case PredictBurstArtifact:
		storageBucket = is.AWSPredictBurstStorageBucket
		storagePrefix = metadata.CalculateStoragePrefix(path.Join(is.AWSPredictBurstStorageKeyPrefix, artifactType))
	default:
		return fmt.Errorf("unsupported artifact type: %s", metadata.ArtifactType())
	}

	_, baseFileName := path.Split(base)
	_, metaFilename := path.Split(originalMetaKey)
	_, zipFilename := path.Split(originalZipKey)

	targetMetaKey := path.Join(storagePrefix, metaFilename)
	targetZipKey := path.Join(storagePrefix, zipFilename)

	log.Println("processing meta/zip pair:", base)
	// move image & meta files
	var predictBurstFiles []string
	switch artifactType {
	case PredictBurstArtifact:
		log.Printf(" extracting %s zip: %s -> %s\n", PredictBurstArtifact, originalZipKey, storagePrefix)
		predictBurstFiles, err = is.UnzipAndMoveS3(ctx, bucket, originalZipKey, storageBucket, storagePrefix)
		if err != nil {
			return fmt.Errorf("failed to unzip and move artifact key: %q target: %q error: %w", key, storagePrefix, err)
		}
	case P2PArtifact:
		log.Printf(" extracting %s zip: %s -> %s\n", P2PArtifact, originalZipKey, storagePrefix)
		if _, err := is.UnzipAndMoveS3(ctx, bucket, originalZipKey, storageBucket, storagePrefix); err != nil {
			return fmt.Errorf("failed to unzip and move artifact key: %q target: %q error: %w", key, storagePrefix, err)
		}
	default:
		log.Printf(" zip: %s -> %s\n", originalZipKey, targetZipKey)
		if err := is.MoveS3Object(ctx, bucket, originalZipKey, storageBucket, targetZipKey); err != nil {
			return fmt.Errorf("failed to move artifact key: %q target: %q error: %w", key, targetZipKey, err)
		}
	}

	log.Printf(" meta: %s -> %s\n", originalMetaKey, targetMetaKey)
	if err := is.MoveS3Object(ctx, bucket, originalMetaKey, storageBucket, targetMetaKey); err != nil {
		return fmt.Errorf("failed to move meta key: %q target: %q error: %w", key, targetMetaKey, err)
	}
	artifactPairsCounter.WithLabelValues(metadata.ArtifactType()).Inc()

	if slices.Contains(is.DisableVeselkaPostArtifactTypes, metadata.ArtifactType()) {
		veselkaPostsSkipedtotal.WithLabelValues(metadata.ArtifactType()).Inc()
		log.Println("artifact type", metadata.ArtifactType(), "veselka posting disabled. skipping veselka post")
		return nil
	}

	if len(predictBurstFiles) > 0 {
		metaFile, imageFile, err := predictBurstCenter(predictBurstFiles)
		if err != nil {
			return fmt.Errorf("aborting veseka post (%s) error: %w", base, err)
		}

		metaURL := fmt.Sprint("s3://", path.Join(storageBucket, storagePrefix, metaFile))
		imageURL := fmt.Sprint("s3://", path.Join(storageBucket, storagePrefix, imageFile))
		predictBurstURL := fmt.Sprint("s3://", path.Join(storageBucket, storagePrefix, baseFileName))
		if err := is.postVeselkaImage(ctx, veselka.PostImageParameters{
			ImageURL:        imageURL,
			MetadataURL:     metaURL,
			PredictBurstURL: predictBurstURL,
		}); err != nil {
			return err
		}
	}
	return nil
}

func (is *IngestService) ingestDiagnosticZip(bucket, key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	log.Println("processing diagnostic zip:", key)
	_, keyFilename := path.Split(key)
	parts := strings.Split(keyFilename, "_")
	if len(parts) < 2 {
		return fmt.Errorf("invalid diagnostic zip: %s", key)
	}
	robot := parts[1]
	targetKey := path.Join(is.AWSDiagnosticsPrefix, robot, keyFilename)
	if err := is.MoveLargeS3Object(ctx, bucket, key, is.AWSDiagnosticsBucket, targetKey); err != nil {
		return fmt.Errorf("failed to move diagnostic zip key: %q target: %q error: %w", key, targetKey, err)
	}
	diagnosticZipCounter.Inc()
	return nil
}

func (is *IngestService) ingestPlantCaptchaZip(bucket, key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	log.Println("processing plant-captcha zip:", key)
	_, keyFilename := path.Split(key)
	parts := strings.Split(keyFilename, "_")
	if len(parts) < 2 {
		return fmt.Errorf("invalid plant-captcha zip: %s", key)
	}
	robot := parts[1]
	targetKey := path.Join(is.AWSPlantCaptchaPrefix, robot, keyFilename)

	log.Printf(" copying %s zip: %s -> %s\n", PlantCaptchaZip, key, targetKey)
	if _, err := is.s3Facade.CopyObjectWithContext(ctx, &s3.CopyObjectInput{
		CopySource: aws.String(path.Join(bucket, key)),
		Bucket:     aws.String(is.AWSPlantCaptchaBucket),
		Key:        aws.String(targetKey),
	}); err != nil {
		return fmt.Errorf("failed to copy plant-captcha zip key: %q target: %q error: %w", key, targetKey, err)
	}

	targetDir := strings.TrimSuffix(targetKey, ".zip")
	log.Printf(" extracting %s zip: %s -> %s\n", PlantCaptchaZip, key, targetDir)
	if _, err := is.UnzipAndMoveS3(ctx, bucket, key, is.AWSPlantCaptchaBucket, targetDir); err != nil {
		return fmt.Errorf("failed to unzip plant-captcha zip key: %q target: %q error: %w", key, targetDir, err)
	}
	plantCaptchaZipCounter.Inc()

	if err := is.postVeselkaPlantCaptcha(ctx, veselka.PostCaptchaParameters{
		PlantCaptchaURL: fmt.Sprintf("s3://%s/%s", is.AWSPlantCaptchaBucket, targetDir),
		RobotID:         robot,
	}); err != nil {
		return err
	}
	return nil
}
