#!/usr/bin/env python3

import os
import sys
import json
import argparse
import http.client
from urllib.parse import urlencode, urlparse
import time

"""
USAGE:
    python delete_all.py --customer-ids <id1> <id2> ... [--include-protected] [--dry-run] [--limit N]

DESCRIPTION:
    Marks Category Collection and Category Profiles as deleted for test environment.
"""

# Environment check
ROBOT_SYNCER_PORT = os.getenv("ROBOT_SYNCER_PORT", "8080")
AWS_BUCKET = os.getenv("AWS_BUCKET")
if AWS_BUCKET != "carbon-robot-syncer-testing":
    print(f"✖️ Refusing to run: AWS_BUCKET is '{AWS_BUCKET}', expected 'carbon-robot-syncer-testing'")
    sys.exit(1)

# CLI args
parser = argparse.ArgumentParser()
parser.add_argument("--customer-ids", nargs="+", required=False, help="Customer IDs to delete profiles for")
parser.add_argument("--include-protected", action="store_true", help="Delete Carbon provided profiles")
parser.add_argument("--dry-run", action="store_true", help="Test out the script without actually deleting anything")
parser.add_argument("--limit", type=int, default=None, help="If set, limits the batch of category collection profiles that can be deleted. Note many category profiles will be deleted for each category collection profile deleted.")
args = parser.parse_args()

BASE_HOST = "localhost"
BASE_PORT = int(ROBOT_SYNCER_PORT)
BASE_PATH = "/internal/v1/profiles"
HEADERS = {"Content-Type": "application/json"}

PROFILE_TYPE_CATEGORY_COLLECTION = 7
PROFILE_TYPE_CATEGORY = 8

def prompt_for_confirmation():
    try:
        response = input("⚠️  Are you sure you want to proceed? This script deletes all profiles from the environment that your local instance is currently pointed at (Make sure it's test or staging and not prod!) (y/n): ").strip().lower()
        if response not in ("y", "yes"):
            print("Aborted.")
            sys.exit(0)
    except (KeyboardInterrupt, EOFError):
        print("\nAborted.")
        sys.exit(0)

def http_get(path):
    conn = http.client.HTTPConnection(BASE_HOST, BASE_PORT)
    conn.request("GET", path, headers=HEADERS)
    res = conn.getresponse()
    if res.status != 200:
        raise Exception(f"GET {path} failed: {res.status} {res.reason}")
    data = res.read()
    return json.loads(data)


def http_post(path, body):
    conn = http.client.HTTPConnection(BASE_HOST, BASE_PORT)
    conn.request("POST", path, body=body, headers=HEADERS)
    res = conn.getresponse()
    if res.status != 200:
        raise Exception(f"POST {path} failed: {res.status} {res.reason}")
    print(f"✔ Deleted profile {path.split('/')[-1]}")


def get_profiles(endpoint):
    print(f"Calling endpoint {endpoint}")
    try:
        return http_get(endpoint)
    except Exception as e:
        print(f"Failed to get profiles: {e}")
        return []

def get_profile(endpoint):
    print(f"Calling endpoint {endpoint}")
    try:
        return http_get(endpoint)
    except Exception as e:
        print(f"Failed to get profile: {e}")
        return None

def delete_profile(profile):
    profile_id = profile["Id"]
    path = f"{BASE_PATH}/{profile_id}"
    payload = {
        **profile,
        "profile": profile["profile"],
        "updatedAt": int(time.time() * 1000),
        "deleted": True
    }
    try:
        http_post(path, json.dumps(payload))
    except Exception as e:
        print(f"✖ Failed to delete profile {profile_id}: {e}")


DRY_RUN_PREFIX = "[DRY RUN]: " if args.dry_run else ""
def main():
    if not args.include_protected and not args.customer_ids:
        print("✖️ Must specify at least --include-protected or --customer-ids")
        sys.exit(1)

    prompt_for_confirmation()
    deleted_count = 0

    def should_continue():
        return args.limit is None or deleted_count < args.limit

    def delete_profile_and_categories(collection_profile):
        nonlocal deleted_count
        if collection_profile["deleted"] or (not args.include_protected and collection_profile.get("protected", False)):
            print(f"{DRY_RUN_PREFIX}Skipping collection: {collection_profile['Id']}")
            return False

        print(f"{DRY_RUN_PREFIX}Deleting collection profile: {collection_profile['Id']}")
        print(json.dumps(collection_profile, indent=2))
        if should_continue():
            if not args.dry_run:
                delete_profile(collection_profile)
            deleted_count += 1

        # parse categoryIds
        try:
            parsed_profile = json.loads(collection_profile["profile"])
            category_ids = parsed_profile.get("categoryIds", [])
        except Exception as e:
            print(f"✖ Failed to parse Profile JSON for {collection_profile['Id']}: {e}")
            return False

        for category_id in category_ids: 
            category_profile = get_profile(f"{BASE_PATH}/{category_id}")
            if category_profile is None:
                print(f"{DRY_RUN_PREFIX}Skipping category: {category_id}")
                continue
            else:
                print(f"{DRY_RUN_PREFIX}Deleting linked category profile: {category_id}")
                print(json.dumps(category_profile, indent=2))
                if not args.dry_run:
                    delete_profile(category_profile)

        return False

    # Protected global category collections
    if args.include_protected:
        endpoint = f"{BASE_PATH}?{urlencode({'profileType': PROFILE_TYPE_CATEGORY_COLLECTION})}"
        for profile in get_profiles(endpoint):
            if should_continue() and delete_profile_and_categories(profile):
                return

    # Customer category collections
    if args.customer_ids is not None:
        for customer_id in args.customer_ids:
            endpoint = f"{BASE_PATH}/customer/{customer_id}?{urlencode({'profileType': PROFILE_TYPE_CATEGORY_COLLECTION})}"
            for profile in get_profiles(endpoint):
                if should_continue() and delete_profile_and_categories(profile):
                    return

    print(f"=== {DRY_RUN_PREFIX}Summary: {deleted_count} category collection profiles deleted ===")

if __name__ == "__main__":
    main()
