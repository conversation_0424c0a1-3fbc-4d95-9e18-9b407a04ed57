package main

import (
	"context"
	"errors"
	"flag"
	"maps"
	"os"
	"slices"
	"strconv"
	"strings"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrations"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

func up(ctx context.Context, args []string) {
	group := flag.NewFlagSet("up", flag.ExitOnError)
	force := group.Bool("force", false, "Apply migration even if it is not marked safe")
	group.Parse(args)
	queries := group.Args()

	ms := pickMigrations(migrations.Migrator, queries)
	apply(ctx, ms, migrator.DirectionUp, *force)
}

func down(ctx context.Context, args []string) {
	group := flag.NewFlagSet("down", flag.ExitOnError)
	force := group.Bool("force", false, "Apply migration even if it is not marked safe")
	group.Parse(args)
	queries := group.Args()

	ms := pickMigrations(migrations.Migrator, queries)
	apply(ctx, ms, migrator.DirectionDown, *force)
}

func apply(ctx context.Context, ms []*migrator.Migration, dir migrator.Direction, force bool) {
	if len(ms) == 0 {
		log.Fatal("No migrations requested")
	}
	var getAction func(m *migrator.Migration) *migrator.Action
	switch dir {
	case migrator.DirectionUp:
		getAction = func(m *migrator.Migration) *migrator.Action { return m.Up }
	case migrator.DirectionDown:
		getAction = func(m *migrator.Migration) *migrator.Action { return m.Down }
	default:
		log.WithField("direction", dir).Fatal("Internal error: unknown migration direction")
	}

	if !force {
		for _, m := range ms {
			if a := getAction(m); a != nil && a.Safety != migrator.Safe {
				log.WithField("migration", m.Name).Fatal("Migration is not marked as safe; run with --force to apply anyway")
			}
		}
	}

	pool := mustOpenPool(ctx)
	for _, m := range ms {
		err := migrations.Migrator.ApplyAndLog(ctx, pool, m, dir)
		if err == nil {
			continue
		}
		if errors.Is(err, migrator.ErrNoAction) {
			// skip
			continue
		}
		os.Exit(1)
	}
}

func pickMigrations(mg *migrator.Migrator, queries []string) []*migrator.Migration {
	all := mg.Migrations
	result := make([]*migrator.Migration, len(queries))
	for i, query := range queries {
		result[i] = pickMigration(all, query)
	}
	return result
}

// pickMigration finds the unique migration that matches the given query, or
// else exits the program. A query matches a migration if either (a) the query
// is a literal integer and the migration has that sequence number, or (b) the
// query is not a literal integer but is contained as a substring of the
// migration name, case-insensitive.
func pickMigration(all []*migrator.Migration, query string) *migrator.Migration {
	query = strings.ToLower(query)

	asInt, intErr := strconv.ParseInt(query, 10, 32)
	substringMatches := make(map[*migrator.Migration]struct{})
	for _, m := range all {
		if intErr == nil && m.Sequence == int32(asInt) {
			return m
		}
		if strings.Contains(strings.ToLower(m.Name), query) {
			substringMatches[m] = struct{}{}
		}
	}
	if len(substringMatches) == 0 {
		log.WithField("query", query).Fatal("No migration matching name or sequence number")
		return nil
	}
	if len(substringMatches) > 1 {
		matches := make([]string, 0, len(substringMatches))
		for m, _ := range substringMatches {
			matches = append(matches, m.Name)
		}
		log.WithField("matches", len(substringMatches)).WithField("query", query).Fatal("Multiple migrations match given name")
	}
	return slices.Collect(maps.Keys(substringMatches))[0]
}
