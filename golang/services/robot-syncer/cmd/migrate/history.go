package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

func history(ctx context.Context, args []string) {
	group := flag.NewFlagSet("history", flag.ExitOnError)
	group.Parse(args)

	pool := mustOpenPool(ctx)
	runs, err := migrator.ListRuns(ctx, pool)
	if err != nil {
		log.WithError(err).Error("Failed to list migration runs")
		os.Exit(1)
	}
	if len(runs) == 0 {
		log.Warn("No migrations have been run")
	}
	for _, r := range runs {
		fmt.Println(formatMigrationRun(r))
	}
}

func formatMigrationRun(r *migrator.MigrationRun) string {
	timestamp := r.ApplyTime.Format(time.RFC3339)
	return fmt.Sprintf("%s\t%s\t%s (%s)", timestamp, r.Direction, r.<PERSON>, r.<PERSON><PERSON><PERSON>ash)
}
