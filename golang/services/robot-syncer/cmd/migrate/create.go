package main

import (
	"context"
	_ "embed"
	"errors"
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strings"
	"text/template"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrations"
)

//go:embed migration.go.tmpl
var templateContents string

var reMigrationName = regexp.MustCompile(`^[a-z][0-9A-Za-z]*$`)
var reIndent = regexp.MustCompile(`^[\s]*`)

func create(ctx context.Context, args []string) {
	group := flag.NewFlagSet("create", flag.ExitOnError)
	group.Parse(args)

	if len(group.Args()) != 1 {
		log.Fatal("usage: create MIGRATION_NAME")
	}
	migrationName := group.Arg(0)
	if !reMigrationName.MatchString(migrationName) {
		log.
			WithField("name", migrationName).
			WithField("wantPattern", reMigrationName).
			Fatal("Invalid migration name")
	}
	sequence := len(migrations.Migrator.Migrations) + 1
	identPrefix := fmt.Sprintf("m%04d", sequence)
	fullName := fmt.Sprintf("%s_%s", identPrefix, migrationName)

	migrationFilePath := writeMigrationFileOrDie(sequence, identPrefix, fullName)
	updateMigrationListOrDie(fullName)
	// Use Infof instead of WithField("file", ...) because printing
	// "file=path/to/file.go" to the terminal makes it harder to automatically
	// jump to that file in an editor, since the "file=" looks like part of it.
	log.Infof("Created migration: %s", migrationFilePath)
}

func writeMigrationFileOrDie(sequence int, identPrefix string, fullName string) (path string) {
	tmpl := template.Must(template.New("migration").Parse(templateContents))

	filename := fmt.Sprintf("%s.go", fullName)
	path = filepath.Join("database", "migrations", filename)
	wr, err := os.Create(path)
	if err != nil {
		log.WithError(err).Error("Failed to create migration file")
		maybeSuggestChdir(err)
		os.Exit(1)
	}
	defer wr.Close()
	err = tmpl.Execute(wr, struct {
		Sequence    int
		IdentPrefix string
		FullName    string
	}{
		Sequence:    sequence,
		IdentPrefix: identPrefix,
		FullName:    fullName,
	})
	if err != nil {
		log.WithError(err).Fatal("Failed to write migration file")
	}
	return path
}

func updateMigrationListOrDie(fullName string) {
	const magicAnchor = "$NextMigrationAboveThisLine$"
	path := filepath.Join("database", "migrations", "list.go")
	oldContents, err := os.ReadFile(path)
	if err != nil {
		log.WithError(err).Error("Failed to read existing migration list")
		maybeSuggestChdir(err)
		os.Exit(1)
	}
	lines := strings.Split(string(oldContents), "\n")

	anchorIndex := slices.IndexFunc(lines, func(line string) bool {
		return strings.Contains(line, magicAnchor)
	})
	if anchorIndex < 0 {
		log.WithField("magicAnchor", magicAnchor).Fatal("Couldn't find magic anchor in migrations list")
	}
	anchorLine := lines[anchorIndex]

	indent := reIndent.FindString(anchorLine)
	newLine := fmt.Sprintf("%s%s,", indent, fullName)
	lines = slices.Insert(lines, anchorIndex, newLine)
	newContents := strings.Join(lines, "\n")

	wr, err := os.Create(path)
	if err != nil {
		log.WithError(err).Fatal("Failed to open migration list for writing")
	}
	defer wr.Close()
	_, err = io.WriteString(wr, newContents)
	if err != nil {
		log.WithError(err).Fatal("Failed to write migration list")
	}
}

func maybeSuggestChdir(err error) {
	if errors.Is(err, os.ErrNotExist) {
		log.Info("hint: Are you running from the robot-syncer directory?")
	}
}
