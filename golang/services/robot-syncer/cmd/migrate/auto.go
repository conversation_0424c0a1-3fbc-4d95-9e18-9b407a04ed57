package main

import (
	"context"
	"flag"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrations"
)

func auto(ctx context.Context, args []string) {
	group := flag.NewFlagSet("auto", flag.ExitOnError)
	group.Parse(args)

	pool := mustOpenPool(ctx)
	blockedOn, blockedBecause, err := migrations.Migrator.AutoMigrate(ctx, pool)
	if err != nil {
		log.WithError(err).Fatal("Failed to auto-migrate")
	}
	if blockedOn != nil {
		log.
			WithField("blockedOn", blockedOn.Name).
			WithField("reason", blockedBecause).
			Fatal("Auto-migration declined to execute migration")
	}
	log.Info("All migrations applied")
}
