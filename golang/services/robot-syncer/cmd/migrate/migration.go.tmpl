package migrations

import (
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

var {{.FullName}} = &migrator.Migration{
	Sequence: {{.Sequence}},
	Name:     "{{.FullName}}",
	Up:       migrator.SQLAction(migrator.TODO_SpecifySafeOrUnsafe, {{.IdentPrefix}}_sqlUp),
	Down:     migrator.SQLAction(migrator.TODO_SpecifySafeOrUnsafe, {{.IdentPrefix}}_sqlDown),

	// TODO: Remove this once the migration is ready to be executed.
	Draft: true,
}

const {{.IdentPrefix}}_sqlUp = `
-- TODO: Write SQL code to migrate up.
`

const {{.IdentPrefix}}_sqlDown = `-- TODO: Write SQL code to migrate down.`
