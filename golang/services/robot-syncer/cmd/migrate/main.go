package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/jackc/pgx/v5/pgxpool"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database"
)

var usage = strings.TrimSpace(`
Available commands:

Inspect state of migrations in the database:
	status
	history

Apply migrations to the database:
	up [--force] [<migration>...]
	down [--force] [<migration>...]
	auto

Generate source files for a new migration:
	create <name>

Show this message:
	help
`)

func main() {
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	if len(os.Args) < 2 {
		fmt.Fprintln(os.Stderr, usage)
		os.Exit(1)
	}
	cmd, args := os.Args[1], os.Args[2:]
	switch cmd {
	case "history":
		history(ctx, args)
	case "status":
		status(ctx, args)
	case "auto":
		auto(ctx, args)
	case "up":
		up(ctx, args)
	case "down":
		down(ctx, args)
	case "create":
		create(ctx, args)
	case "help", "-h", "-help", "--help":
		fmt.Println(usage)
	default:
		fmt.Fprintln(os.Stderr, usage)
		os.Exit(1)
	}
}

func mustOpenPool(ctx context.Context) *pgxpool.Pool {
	pool, err := database.Open(ctx, os.Getenv("PSQL_CONNECTION"))
	if err != nil {
		log.WithError(err).Fatal("Failed to connect to database")
	}
	return pool
}
