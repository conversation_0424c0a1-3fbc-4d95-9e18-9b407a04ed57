package main

import (
	"context"
	"flag"
	"fmt"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrations"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

func status(ctx context.Context, args []string) {
	group := flag.NewFlagSet("status", flag.ExitOnError)
	group.Parse(args)

	mg := migrations.Migrator

	pool := mustOpenPool(ctx)
	runs, err := migrator.ListRuns(ctx, pool)
	if err != nil {
		log.WithError(err).Fatal("Failed to list migration runs")
	}
	lastRuns, unrecognized := mg.LastRuns(runs)

	if len(unrecognized) > 0 {
		fmt.Println("unrecognized migration runs:")
		for _, r := range unrecognized {
			fmt.Println(formatMigrationRun(r))
		}
		fmt.Printf("---\n\n")
	}

	const (
		sigilUp   = "+"
		sigilDown = "~"
		sigilNext = ">"
	)
	fmt.Printf("legend:\n")
	fmt.Printf("  %s migration applied\n", sigilUp)
	fmt.Printf("  %s migration rolled back\n", sigilDown)
	fmt.Printf("%s   next migration to apply\n", sigilNext)
	fmt.Printf("---\n\n")

	nextMigrationIndex := -1
	for i, m := range mg.Migrations {
		lastRun := lastRuns[i]

		isNext := false
		var statusSigil string
		switch lastRun.GetDirection() {
		case migrator.DirectionUp:
			statusSigil = sigilUp
		case migrator.DirectionDown:
			statusSigil = sigilDown
			isNext = true
		case migrator.DirectionUnset:
			isNext = true
		default:
			statusSigil = fmt.Sprintf("?%s", lastRun.GetDirection())
		}

		var nextSigil string
		if isNext && nextMigrationIndex == -1 {
			nextSigil = sigilNext
			nextMigrationIndex = i
		}
		fmt.Printf("%-2s%-1s %s\n", nextSigil, statusSigil, m.Name)
	}

	fmt.Println()
	if nextMigrationIndex >= 0 {
		m, run := mg.Migrations[nextMigrationIndex], lastRuns[nextMigrationIndex]
		fmt.Printf("next migration to apply: %s\n", m.Name)
		if run.GetDirection() == migrator.DirectionDown {
			at := run.ApplyTime
			ago := time.Since(at).Truncate(time.Second)
			fmt.Printf("\tmanually rolled back at %s (%s ago)\n", at.Format(time.RFC1123), ago)
		} else {
			fmt.Println("\tnever applied")
		}
	} else {
		fmt.Println("all migrations applied")
	}
}
