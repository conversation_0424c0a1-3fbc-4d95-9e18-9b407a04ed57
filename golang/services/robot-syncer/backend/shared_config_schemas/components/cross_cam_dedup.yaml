type: "node"
children:
  radius:
    type: "float"
    units: "mm"
    default: 6.0
    default_recommended: true
    complexity: "expert"
    hint: "De-duplication radius to search for duplicates across cameras [mm]"
  delta_time_ms:
    type: "uint"
    units: "ms"
    default: 50
    default_recommended: true
    complexity: "developer"
    hint: "How often to run de-duplication [ms]"
  enabled:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
    hint: "Enable cross camera de-duplication"
  min_num_detections:
    type: "uint"
    default: 1
    default_recommended: true
    complexity: "developer"
    hint: "Deprecated: [v2.1.0-beta.23]"
  only_use_in_band:
    type: "bool"
    default: false
    default_recommended: true
    complexity: "expert"
    hint: "Deprecated: [v2.1.0-beta.23]"
  algorithm:
    type: "string"
    default: "classic"
    choices: ["classic", "ransac"]
    default_recommended: false
    hint: "Which algorithm to use for cross-cam deduplication"
  match_radius:
    type: "float"
    units: "mm"
    default: 5.0
    default_recommended: false
    complexity: "expert"
    hint: "In ransac algorithm, this is the max distance a shifted item can be from another item to deduplicate with it"
  inclusion_tolerance:
    type: "float"
    units: "px"
    default: 250.0
    default_recommended: true
    complexity: "expert"
    hint: "In ransac algorithm, this is the how far an item can be out of the camera's FOV before it is excluded in the matching process"
  use_latest_trajectory_timestamp:
    type: "bool"
    default: false
    default_recommended: true
    complexity: "developer"
    hint: "Use the latest trajectory timestamp instead of the current time for deduplication"
