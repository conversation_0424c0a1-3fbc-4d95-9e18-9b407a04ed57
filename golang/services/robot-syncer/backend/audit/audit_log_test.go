package audit

import (
	"strings"
	"testing"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cloudwatchlogs"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/aws/mocks"
)

func TestLogEvent(t *testing.T) {
	testRobotSerial := "slayer1234"
	testLogGroupName := "test-log-group"

	tests := []struct {
		name               string
		event              *AuditLogMessage
		existingLogGroups  []string
		existingLogStreams []*cloudwatchlogs.LogStream
		expectError        bool
		streamConfirmed    bool
	}{
		{
			"happy path pre confirmed stream",
			&AuditLogMessage{
				Level:       strings.ToUpper(log.InfoLevel.String()),
				Message:     "test message",
				Namespace:   "robot-syncer",
				RobotSerial: testRobotSerial,
				Timestamp:   1234567890,
				UserID:      "test-user",
				Key:         "simulators/devserver0",
				NewValue:    "asdf",
			},
			[]string{testLogGroupName},
			[]*cloudwatchlogs.LogStream{
				{
					LogStreamName: &testRobotSerial,
				},
			},
			false,
			true,
		},
		{
			"confirm existing stream",
			&AuditLogMessage{
				Level:       strings.ToUpper(log.InfoLevel.String()),
				Message:     "test message",
				Namespace:   "robot-syncer",
				RobotSerial: testRobotSerial,
				Timestamp:   1234567890,
				UserID:      "test-user",
			},
			[]string{testLogGroupName},
			[]*cloudwatchlogs.LogStream{
				{
					LogStreamName: &testRobotSerial,
				},
			},
			false,
			false,
		},
		{
			"create stream",
			&AuditLogMessage{
				Level:       strings.ToUpper(log.InfoLevel.String()),
				Message:     "test message",
				Namespace:   "robot-syncer",
				RobotSerial: testRobotSerial,
				Timestamp:   1234567890,
				UserID:      "test-user",
			},
			[]string{testLogGroupName},
			[]*cloudwatchlogs.LogStream{},
			false,
			false,
		},
		{
			"log group does not exist",
			nil,
			[]string{},
			[]*cloudwatchlogs.LogStream{},
			true,
			false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			requestorMock := new(mocks.CWLRequester)
			requestorMock.On("DescribeLogStreams", mock.AnythingOfType("*cloudwatchlogs.DescribeLogStreamsInput")).
				Return(&cloudwatchlogs.DescribeLogStreamsOutput{
					LogStreams: test.existingLogStreams,
					NextToken:  nil,
				}, nil)
			requestorMock.On("CreateLogStream", mock.AnythingOfType("*cloudwatchlogs.CreateLogStreamInput")).
				Return(&cloudwatchlogs.CreateLogStreamOutput{}, nil)
			requestorMock.On("PutLogEvents", mock.AnythingOfType("*cloudwatchlogs.PutLogEventsInput")).
				Return(&cloudwatchlogs.PutLogEventsOutput{}, nil)

			logGroups := make([]*cloudwatchlogs.LogGroup, 0)
			for _, logGroupName := range test.existingLogGroups {
				logGroups = append(logGroups, &cloudwatchlogs.LogGroup{
					LogGroupName: &logGroupName,
				})
			}
			requestorMock.On("DescribeLogGroups", mock.AnythingOfType("*cloudwatchlogs.DescribeLogGroupsInput")).
				Return(&cloudwatchlogs.DescribeLogGroupsOutput{
					LogGroups: logGroups,
				}, nil)

			auditLogger := NewAuditLogger(aws.NewCWLFacade(requestorMock), testLogGroupName, nil)
			if len(test.existingLogStreams) > 0 && test.streamConfirmed {
				auditLogger.RobotStreams[testRobotSerial] = test.event.Timestamp
			}

			err := auditLogger.LogEvent(test.event)

			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if test.streamConfirmed {
					requestorMock.AssertNotCalled(t, "DescribeLogGroups", mock.AnythingOfType("*cloudwatchlogs.DescribeLogGroupsInput"))
					requestorMock.AssertNotCalled(t, "DescribeLogStreams", mock.AnythingOfType("*cloudwatchlogs.DescribeLogStreamsInput"))
					requestorMock.AssertNotCalled(t, "CreateLogStream", mock.AnythingOfType("*cloudwatchlogs.CreateLogStreamInput"))
					requestorMock.AssertCalled(t, "PutLogEvents", mock.AnythingOfType("*cloudwatchlogs.PutLogEventsInput"))
				} else {
					requestorMock.AssertCalled(t, "DescribeLogGroups", mock.AnythingOfType("*cloudwatchlogs.DescribeLogGroupsInput"))
					requestorMock.AssertCalled(t, "DescribeLogStreams", mock.AnythingOfType("*cloudwatchlogs.DescribeLogStreamsInput"))
					requestorMock.AssertCalled(t, "PutLogEvents", mock.AnythingOfType("*cloudwatchlogs.PutLogEventsInput"))
				}
			}

		})
	}
}

func TestLogEventToAWS(t *testing.T) {
	t.SkipNow()
	sess := session.Must(session.NewSession(&awssdk.Config{
		Region: awssdk.String("us-west-2"),
	}))

	auditLogger := NewAuditLogger(aws.NewCWLFacade(cloudwatchlogs.New(sess)), "rosy-unit-test-log-group", nil)
	err := auditLogger.LogEvent(&AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     "test message",
		Namespace:   "robot-syncer",
		RobotSerial: "slayer1234",
		Timestamp:   1234567890,
		UserID:      "test-user",
	})
	assert.NoError(t, err)
}
