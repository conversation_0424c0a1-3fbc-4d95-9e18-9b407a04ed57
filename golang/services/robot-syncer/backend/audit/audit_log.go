package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/cloudwatchlogs"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
)

type Logger struct {
	CWLFacade    *aws.CWLFacade
	LogGroupName string

	DB database.Querier

	// cache of confirmed created streams and the timestamp of most recent log event
	RobotStreams     map[string]int64
	RobotStreamsLock sync.RWMutex
}

type AuditLogMessage struct {
	Level       string `json:"level"`
	Message     string `json:"message"`
	Namespace   string `json:"namespace"`
	RobotSerial string `json:"robotSerial"`
	Timestamp   int64  `json:"timestamp"`
	UserID      string `json:"userId"`
	Key         string `json:"key"`
	OldValue    string `json:"oldValue,omitempty"`
	NewValue    string `json:"newValue,omitempty"`

	OldValueProto *config_service.ConfigValue `json:"-"`
	NewValueProto *config_service.ConfigValue `json:"-"`
}

func NewAuditLogger(cwlf *aws.CWLFacade, logGroupName string, db database.Querier) *Logger {
	return &Logger{
		CWLFacade:        cwlf,
		LogGroupName:     logGroupName,
		DB:               db,
		RobotStreams:     make(map[string]int64),
		RobotStreamsLock: sync.RWMutex{},
	}
}

func (a *Logger) LogEvent(event *AuditLogMessage) error {
	return a.LogBatchEvents([]*AuditLogMessage{event})
}

func (a *Logger) LogBatchEvents(events []*AuditLogMessage) error {
	eventsByRobot := make(map[string][]*cloudwatchlogs.InputLogEvent)
	for _, event := range events {
		if event == nil {
			return fmt.Errorf("nil event in batch")
		}

		messageJSON, err := json.Marshal(event)
		if err != nil {
			return fmt.Errorf("failed to marshal log message %v to JSON: %w", event, err)
		}

		logEvent := &cloudwatchlogs.InputLogEvent{
			Message:   awssdk.String(string(messageJSON)),
			Timestamp: awssdk.Int64(event.Timestamp),
		}

		eventsByRobot[event.RobotSerial] = append(eventsByRobot[event.RobotSerial], logEvent)
	}

	if a.DB != nil {
		dbLogs := make([]*database.ConfigAuditLog, len(events))
		for i, event := range events {
			dbLogs[i] = &database.ConfigAuditLog{
				UserID:   event.UserID,
				Method:   event.Message,
				Serial:   event.RobotSerial,
				Key:      event.Key,
				OldValue: database.ConfigValuePB{Message: event.OldValueProto},
				NewValue: database.ConfigValuePB{Message: event.NewValueProto},
			}
		}
		func() {
			ctx, cancel := context.WithTimeout(context.TODO(), 15*time.Second)
			defer cancel()
			if err := database.WriteAuditLogs(ctx, a.DB, dbLogs); err != nil {
				log.WithError(err).WithField("dbLogs", dbLogs).Warn("Failed to write audit logs to Postgres")
			}
		}()
	}

	for robotSerial, robotEvents := range eventsByRobot {
		// if not confirmed, create the log stream
		if _, prevLogged := a.readRobotStreamsCache(robotSerial); !prevLogged {
			err := a.CWLFacade.CreateLogStream(a.LogGroupName, robotSerial)
			if err != nil {
				return fmt.Errorf("failed to create log stream for robot %s: %w", robotSerial, err)
			}
		}

		err := a.CWLFacade.PutLogEvents(a.LogGroupName, robotSerial, robotEvents)
		if err != nil {
			return fmt.Errorf("failed to log events for robot %s: %w", robotSerial, err)
		}

		latestTimestamp := robotEvents[len(robotEvents)-1].Timestamp
		a.writeRobotStreamsCache(robotSerial, *latestTimestamp)
	}

	return nil
}

func (a *Logger) readRobotStreamsCache(logStreamName string) (int64, bool) {
	a.RobotStreamsLock.RLock()
	defer a.RobotStreamsLock.RUnlock()
	ts, ok := a.RobotStreams[logStreamName]
	return ts, ok
}

func (a *Logger) writeRobotStreamsCache(logStreamName string, ts int64) {
	a.RobotStreamsLock.Lock()
	defer a.RobotStreamsLock.Unlock()
	a.RobotStreams[logStreamName] = ts
}
