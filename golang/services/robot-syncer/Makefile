SHELL := /bin/bash

IMAGE_NAME=robot-syncer

.PHONY: \
	dbuild \
	dpush \
	updateprotos \
	release \
	test-go \
	help

# To test the build in a local container, use
# docker compose build
# docker compose up
dbuild: ## Builds the development docker image
	docker buildx build --platform linux/amd64 --secret id=GITHUB_TOKEN -f Dockerfile -t $(IMAGE_NAME):dev ../../../

dpush: dbuild ## Pushes the development docker image
	docker tag $(IMAGE_NAME):dev ghcr.io/carbonrobotics/cloud/$(IMAGE_NAME):dev
	docker push ghcr.io/carbonrobotics/cloud/$(IMAGE_NAME):dev

dbshell:
	psql "$(PSQL_CONNECTION_HOST)"

updateprotos: ## Updates protos
	cd ../../../ && make updateprotos
	source .env && go get github.com/carbonrobotics/protos/golang && go mod tidy

release: ## Creates a new release. Run `make release` to autoincrement a new minor version release. Run `make release VERSION=v1.2` to create a hotfix with a specific version
	../../../scripts/create-release.sh rosy $(VERSION)

test-go: ## Run golang unit tests
	go test -v ./...

delete-all-profiles: ## Deletes all carbon profiles data (optionally accepts CUSTOMER_IDS="id1 id2") -- Make sure that your local env is pointed at test!
	@python3 ./scripts/delete_all.py --include-protected $(if $(CUSTOMER_IDS),--customer-ids $(CUSTOMER_IDS))

help: ## Show help message
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'
