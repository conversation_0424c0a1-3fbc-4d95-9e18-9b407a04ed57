package cache

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"google.golang.org/protobuf/proto"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/crgo/config/schema"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"
)

func TestUpdateConfigToMatchVersion(t *testing.T) {
	serial, err := carbon.ParseSerialWithClass("slayer1", "slayers")
	assert.NoError(t, err)

	schemaReader, err := schema.NewReader(fmt.Sprintf("../backend/%s", constants.SHARED_CONFIG_SCHEMAS_FOLDER), "roots")
	assert.NoError(t, err)
	cache := &ConfigCache{
		schemaReader: schemaReader,
	}
	schema, err := schemaReader.GetConfigSchema(carbon.ClassSlayers)
	assert.NoError(t, err)
	jsonData, err := os.ReadFile("testdata/test_robot_config.json")
	assert.NoError(t, err)
	base, err := config.UnmarshalConfigFromJson(jsonData, schema)
	assert.NoError(t, err)

	now := uint64(time.Now().UnixMilli())
	ts1 := now
	ts2 := now + 1000
	ts3 := now + 2000

	tests := []struct {
		name          string
		path          string
		mutateCurrent func(node *config_service.ConfigNode)
		mutateTarget  func(node *config_service.ConfigNode)
		check         func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode)
	}{
		{
			name: "float value update",
			path: "command/commander/alarm/cv_runtime_latency_threshold_ms",
			mutateCurrent: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/cv_runtime_latency_threshold_ms")
				if err != nil {
					t.Fatalf("Path not found in current config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_FloatVal{FloatVal: 3000.0}
				n.Value.TimestampMs = ts1
			},
			mutateTarget: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/cv_runtime_latency_threshold_ms")
				if err != nil {
					t.Fatalf("Path not found in target config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_FloatVal{FloatVal: 4000.0}
				n.Value.TimestampMs = ts2
			},
			check: func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode) {
				assert.Len(t, records, 1)
				record := records[0]
				assert.Equal(t, "command/commander/alarm/cv_runtime_latency_threshold_ms", record.Key)
				assert.Equal(t, robot_syncer.Outcome_SUCCESS, record.Outcome)
				setAction, ok := record.Action.Action.(*robot_syncer.Action_Set)
				assert.True(t, ok)
				assert.Equal(t, 4000.0, setAction.Set.Value.GetFloatVal())
				assert.Equal(t, ts3, setAction.Set.Value.GetTimestampMs())

				n, _ := config.GetNodeFromPath(current, "command/commander/alarm/cv_runtime_latency_threshold_ms")
				assert.Equal(t, 4000.0, n.Value.GetFloatVal())
				assert.Equal(t, ts3, n.Value.GetTimestampMs())
			},
		},
		{
			name: "uint value update",
			path: "command/commander/alarm/flicker_filter_s",
			mutateCurrent: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/flicker_filter_s")
				if err != nil {
					t.Fatalf("Path not found in current config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_Uint64Val{Uint64Val: 10}
				n.Value.TimestampMs = ts1
			},
			mutateTarget: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/flicker_filter_s")
				if err != nil {
					t.Fatalf("Path not found in target config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_Uint64Val{Uint64Val: 20}
				n.Value.TimestampMs = ts2
			},
			check: func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode) {
				assert.Len(t, records, 1)
				record := records[0]
				assert.Equal(t, "command/commander/alarm/flicker_filter_s", record.Key)
				assert.Equal(t, robot_syncer.Outcome_SUCCESS, record.Outcome)
				setAction, ok := record.Action.Action.(*robot_syncer.Action_Set)
				assert.True(t, ok)
				assert.Equal(t, uint64(20), setAction.Set.Value.GetUint64Val())
				assert.Equal(t, ts3, setAction.Set.Value.GetTimestampMs())

				n, _ := config.GetNodeFromPath(current, "command/commander/alarm/flicker_filter_s")
				assert.Equal(t, uint64(20), n.Value.GetUint64Val())
				assert.Equal(t, ts3, n.Value.GetTimestampMs())
			},
		},
		{
			name: "add to list",
			path: "command/commander/alarm/ignore_list",
			mutateCurrent: func(node *config_service.ConfigNode) {
				// ignore_list is a list, but let's ensure it's empty for current
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/ignore_list")
				if err != nil {
					t.Fatalf("Path not found in current config: %v", err)
				}
				n.Children = nil
			},
			mutateTarget: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/alarm/ignore_list")
				if err != nil {
					t.Fatalf("Path not found in target config: %v", err)
				}
				newChild := &config_service.ConfigNode{
					Name: "test-entry",
					Def: &config_service.ConfigDef{
						Type: config_service.ConfigType_NODE,
					},
					Children: []*config_service.ConfigNode{
						{
							Name: "enabled",
							Def: &config_service.ConfigDef{
								Type: config_service.ConfigType_BOOL,
							},
							Value: &config_service.ConfigValue{
								Value:       &config_service.ConfigValue_BoolVal{BoolVal: true},
								TimestampMs: ts2,
							},
						},
					},
				}
				n.Children = []*config_service.ConfigNode{newChild}
			},
			check: func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode) {
				// Should have one record for adding the new entry
				assert.Len(t, records, 1)
				record := records[0]
				assert.Equal(t, "command/commander/alarm/ignore_list/test-entry", record.Key)
				addAction, ok := record.Action.Action.(*robot_syncer.Action_Add)
				assert.True(t, ok)
				assert.Equal(t, "test-entry", addAction.Add.Name)

				// Verify the list was actually updated in the current config
				n, err := config.GetNodeFromPath(current, "command/commander/alarm/ignore_list")
				assert.NoError(t, err)
				assert.Len(t, n.Children, 1)
				assert.Equal(t, "test-entry", n.Children[0].Name)
				assert.Equal(t, true, n.Children[0].Children[0].Value.GetBoolVal())
				assert.Equal(t, ts3, n.Children[0].Children[0].Value.GetTimestampMs())
			},
		},
		{
			name: "string value update",
			path: "command/commander/log_level",
			mutateCurrent: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/log_level")
				if err != nil {
					t.Fatalf("Path not found in current config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_StringVal{StringVal: "info"}
				n.Value.TimestampMs = ts1
			},
			mutateTarget: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/commander/log_level")
				if err != nil {
					t.Fatalf("Path not found in target config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_StringVal{StringVal: "debug"}
				n.Value.TimestampMs = ts2
			},
			check: func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode) {
				assert.Len(t, records, 1)
				record := records[0]
				assert.Equal(t, "command/commander/log_level", record.Key)
				assert.Equal(t, robot_syncer.Outcome_SUCCESS, record.Outcome)
				setAction, ok := record.Action.Action.(*robot_syncer.Action_Set)
				assert.True(t, ok)
				assert.Equal(t, "debug", setAction.Set.Value.GetStringVal())
				assert.Equal(t, ts3, setAction.Set.Value.GetTimestampMs())

				n, _ := config.GetNodeFromPath(current, "command/commander/log_level")
				assert.Equal(t, "debug", n.Value.GetStringVal())
				assert.Equal(t, ts3, n.Value.GetTimestampMs())
			},
		},
		{
			name: "bool value update",
			path: "command/data_upload_manager/use_online_upload_flow",
			mutateCurrent: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/data_upload_manager/use_online_upload_flow")
				if err != nil {
					t.Fatalf("Path not found in current config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_BoolVal{BoolVal: false}
				n.Value.TimestampMs = ts1
			},
			mutateTarget: func(node *config_service.ConfigNode) {
				n, err := config.GetNodeFromPath(node, "command/data_upload_manager/use_online_upload_flow")
				if err != nil {
					t.Fatalf("Path not found in target config: %v", err)
				}
				n.Value.Value = &config_service.ConfigValue_BoolVal{BoolVal: true}
				n.Value.TimestampMs = ts2
			},
			check: func(t *testing.T, records []*robot_syncer.EditRecord, current *config_service.ConfigNode) {
				assert.Len(t, records, 1)
				record := records[0]
				assert.Equal(t, "command/data_upload_manager/use_online_upload_flow", record.Key)
				assert.Equal(t, robot_syncer.Outcome_SUCCESS, record.Outcome)
				setAction, ok := record.Action.Action.(*robot_syncer.Action_Set)
				assert.True(t, ok)
				assert.Equal(t, true, setAction.Set.Value.GetBoolVal())
				assert.Equal(t, ts3, setAction.Set.Value.GetTimestampMs())

				n, _ := config.GetNodeFromPath(current, "command/data_upload_manager/use_online_upload_flow")
				assert.Equal(t, true, n.Value.GetBoolVal())
				assert.Equal(t, ts3, n.Value.GetTimestampMs())
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			current := proto.Clone(base).(*config_service.ConfigNode)
			target := proto.Clone(base).(*config_service.ConfigNode)
			tc.mutateCurrent(current)
			tc.mutateTarget(target)
			oldValueCache := make(map[string]configValueAndString)
			records, err := cache.updateConfigToMatchVersion(context.Background(), serial, current, target, "", ts3, oldValueCache)
			assert.NoError(t, err)
			if err != nil {
				t.Fatalf("Error updating config: %v", err)
			}
			tc.check(t, records, current)
		})
	}
}
