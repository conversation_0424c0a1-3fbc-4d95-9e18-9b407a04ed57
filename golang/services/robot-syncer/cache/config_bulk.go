package cache

import (
	"context"
	"fmt"
	"path"
	"slices"
	"strings"

	log "github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

type RobotNotifier interface {
	NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) (success bool)
}

/**
 * Iterates over the operations provided for a single robot and performs each operation
 * on the robot's config. The operations are applied in the order they are provided.
 * The function returns a list of EditRecords, each containing the outcome of the operation
 * and any error messages.
 *
 * All operations are aggregated and only written to S3 once. The cached config remains
 * locked until all manipulations and the S3 write have been completed. That work is all
 * completed in the critical section below.
 *
 * After the critical section, the function writes audit logs to CloudWatch Logs and
 * notifies the robot of the changes.
 **/
type BulkEditConfigParameters struct {
	AuditLogger *audit.Logger
	Notifier    RobotNotifier
	Serial      *carbon.ValidSerial
	Timestamp   uint64
	UserID      string
	Operations  []*robot_syncer.Operation
}

func (c *ConfigCache) BulkEditConfig(ctx context.Context, parameters BulkEditConfigParameters) ([]*robot_syncer.EditRecord, error) {
	mu := c.keyedMutex.Get(parameters.Serial.String())
	mu.Lock()
	defer mu.Unlock()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	editRecords := make([]*robot_syncer.EditRecord, 0)
	oldValueCache := make(map[string]configValueAndString)
	cachedConfig, err := c.getRobotConfigRoot(ctx, parameters.Serial)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch %s config: %w", parameters.Serial, err)
	}

	///////////////////////////////////////////////////////////////////////////
	// CRITICAL SECTION
	// Cached config remains locked through all operations
	err = func() error {
		// Create a copy of the config to work on
		configCopy := proto.CloneOf[*config_service.ConfigNode](cachedConfig)

		for _, op := range parameters.Operations {
			if op.GetKeySpec() == nil || op.GetAction() == nil {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action:  op.GetAction(),
					Message: "invalid operation - missing key spec or action",
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}

			paths, err := c.composePathsFromKeySpec(ctx, configCopy, op.GetKeySpec())
			if err != nil {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action:  op.GetAction(),
					Message: fmt.Sprintf("failed to compose paths from key spec: %v", err),
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}

			if len(paths) == 0 {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action:  op.GetAction(),
					Message: "no paths found to operate on",
					Outcome: robot_syncer.Outcome_SUCCESS,
				})
				continue
			}

			switch action := op.GetAction().Action.(type) {
			case *robot_syncer.Action_Set:
				configValue := action.Set.Value
				configValue.TimestampMs = parameters.Timestamp

				for _, path := range paths {
					oldValue, err := c.updateConfigValueInPlace(parameters.Serial, configCopy, path, configValue)
					if err != nil {
						editRecords = append(editRecords, &robot_syncer.EditRecord{
							Action:  op.GetAction(),
							Key:     path,
							Message: err.Error(),
							Outcome: robot_syncer.Outcome_FAILURE,
						})
						continue
					}

					// keep oldest old value for each path
					if _, ok := oldValueCache[path]; !ok {
						oldValueCache[path] = oldValue
					}

					editRecords = append(editRecords, &robot_syncer.EditRecord{
						Action:  op.GetAction(),
						Key:     path,
						Outcome: robot_syncer.Outcome_SUCCESS,
					})
				}
			case *robot_syncer.Action_Add:
				configNodeName := action.Add.Name

				for _, path := range paths {
					if err := c.addConfigListNodeInPlace(parameters.Serial, configCopy, path, configNodeName, parameters.Timestamp); err != nil {
						editRecords = append(editRecords, &robot_syncer.EditRecord{
							Action:  op.GetAction(),
							Key:     path,
							Message: err.Error(),
							Outcome: robot_syncer.Outcome_FAILURE,
						})
						continue
					}

					editRecords = append(editRecords, &robot_syncer.EditRecord{
						Action:  op.GetAction(),
						Key:     path,
						Outcome: robot_syncer.Outcome_SUCCESS,
					})
				}
			case *robot_syncer.Action_Remove:
				configNodeName := action.Remove.Name
				for _, path := range paths {
					if err := c.removeConfigListNodeInPlace(ctx, parameters.Serial, configCopy, path, configNodeName, parameters.Timestamp); err != nil {
						editRecords = append(editRecords, &robot_syncer.EditRecord{
							Action:  op.GetAction(),
							Key:     path,
							Message: err.Error(),
							Outcome: robot_syncer.Outcome_FAILURE,
						})
						continue
					}

					editRecords = append(editRecords, &robot_syncer.EditRecord{
						Action:  op.GetAction(),
						Key:     path,
						Outcome: robot_syncer.Outcome_SUCCESS,
					})
				}
			default:
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action:  op.GetAction(),
					Message: "invalid operation - unknown action",
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}
		}

		// Write the updated config to S3 and update the cache
		cachedConfig = configCopy
		if err := c.writeConfig(parameters.Serial, cachedConfig); err != nil {
			return fmt.Errorf("failed to write config: %w", err)
		}

		return nil
	}()
	///////////////////////////////////////////////////////////////////////////

	if err != nil {
		return editRecords, err
	}
	go c.logAndNotifyEditRecords(
		parameters.AuditLogger,
		parameters.Notifier,
		parameters.Serial,
		parameters.Timestamp,
		parameters.UserID,
		editRecords,
		oldValueCache,
	)

	return editRecords, nil
}

// Weird typedef because legacy audit logs persist values as untyped strings
// instead of proto messages, so we keep both forms around in memory for a
// little bit.
type configValueAndString struct {
	proto *config_service.ConfigValue
	str   string
}

func (c *ConfigCache) logAndNotifyEditRecords(
	auditLogger *audit.Logger,
	streamCache RobotNotifier,
	serial *carbon.ValidSerial,
	timestamp uint64,
	userID string,
	editRecords []*robot_syncer.EditRecord,
	oldValueCache map[string]configValueAndString,
) {
	// write deduped audit logs to CWL and notify the robot for each successful operation.
	// EditRecords were applied in order so keep only audit log the latest value. List adds
	// and removals cannot be deduplicated as removing then adding a node populates the new
	// node with default vlaues from the config schema.
	setAuditLogs := make(map[string]*audit.AuditLogMessage)
	auditLogs := make([]*audit.AuditLogMessage, 0)
	unsyncedKeys := make([]config.UnsyncedKey, 0)
	unsyncedListRemovals := make([]config.UnsyncedKey, 0)

	for _, record := range editRecords {
		if record.Outcome == robot_syncer.Outcome_SUCCESS {
			key := record.GetKey()
			switch action := record.Action.Action.(type) {
			case *robot_syncer.Action_Set:
				newValue := configValueAndString{
					proto: action.Set.Value,
					str:   cloudconfig.ConfigValueToString(action.Set.Value),
				}
				if logMsg, ok := setAuditLogs[key]; !ok {
					oldValue := oldValueCache[key]
					setAuditLogs[key] = &audit.AuditLogMessage{
						Level:       strings.ToUpper(log.InfoLevel.String()),
						Message:     UpdateConfigNodeOperation,
						Namespace:   constants.ROSY_SERVICE_NAMESPACE,
						RobotSerial: serial.String(),
						Timestamp:   int64(timestamp),
						UserID:      userID,
						Key:         key,
						NewValue:    newValue.str,
						OldValue:    oldValue.str,

						OldValueProto: oldValue.proto,
						NewValueProto: newValue.proto,
					}
				} else {
					logMsg.NewValueProto = newValue.proto
					logMsg.NewValue = newValue.str
				}
			case *robot_syncer.Action_Add:
				childName := record.Action.GetAdd().GetName()
				auditLogs = append(auditLogs, &audit.AuditLogMessage{
					Level:       strings.ToUpper(log.InfoLevel.String()),
					Message:     AddToListOperation,
					Namespace:   constants.ROSY_SERVICE_NAMESPACE,
					RobotSerial: serial.String(),
					Timestamp:   int64(timestamp),
					UserID:      userID,
					Key:         key,
					NewValue:    childName,
				})

				if ok := streamCache.NotifyRobot(serial, key, timestamp); !ok {
					log.Debugf("Failed to notify robot %s of config change", serial)
					unsyncedKeys = append(unsyncedKeys, config.UnsyncedKey{
						Key:      key,
						Ts:       timestamp,
						NewValue: childName,
					})
				}
			case *robot_syncer.Action_Remove:
				childName := record.Action.GetRemove().GetName()
				auditLogs = append(auditLogs, &audit.AuditLogMessage{
					Level:       strings.ToUpper(log.InfoLevel.String()),
					Message:     RemoveFromListOperation,
					Namespace:   constants.ROSY_SERVICE_NAMESPACE,
					RobotSerial: serial.String(),
					Timestamp:   int64(timestamp),
					UserID:      userID,
					Key:         key,
				})

				if ok := streamCache.NotifyRobot(serial, key, timestamp); !ok {
					log.Debugf("Failed to notify robot %s of config change", serial)
					unsyncedKeys = append(unsyncedKeys, config.UnsyncedKey{
						Key: key,
						Ts:  timestamp,
					})
					unsyncedListRemovals = append(unsyncedListRemovals, config.UnsyncedKey{
						Key: path.Join(key, childName),
						Ts:  timestamp,
					})
				}
			default:
				log.Errorf("Unknown successful action %s for config node %s on %s", record.Action, record.Key, serial)
			}
		}
	}

	// convert map to slice (order no longer matters) and notify the robot
	for key, logMsg := range setAuditLogs {
		auditLogs = append(auditLogs, logMsg)
		if ok := streamCache.NotifyRobot(serial, key, timestamp); !ok {
			log.Debugf("Failed to notify robot %s of config change", serial)
			unsyncedKeys = append(unsyncedKeys, config.UnsyncedKey{
				Key:      key,
				Ts:       timestamp,
				NewValue: logMsg.NewValue,
				OldValue: logMsg.OldValue,
			})
		}
	}

	c.AddUnsyncedKeys(serial, unsyncedKeys)
	c.AddUnsyncedListRemovals(serial, unsyncedListRemovals)
	if err := auditLogger.LogBatchEvents(auditLogs); err != nil {
		log.Errorf("Failed to log audit events: %v", err)
	}
}

// updateConfigValueInPlace updates a config node in the cached config tree.
// Assumes the config tree is already locked. Does not perform S3 write.
func (c *ConfigCache) updateConfigValueInPlace(
	serial *carbon.ValidSerial,
	configRoot *config_service.ConfigNode,
	path string,
	value *config_service.ConfigValue,
) (oldValue configValueAndString, err error) {
	configSchema, err := c.schemaReader.GetConfigSchema(serial.Class())
	if err != nil {
		return configValueAndString{}, fmt.Errorf("failed to get %s config schema: %w", serial.Class(), err)
	}

	schemaNode, err := c.schemaReader.GetNodeSchemaFromPath(configSchema, serial.Class().String(), path)
	if err != nil {
		return configValueAndString{}, fmt.Errorf("failed to get node schema for path %s: %w", path, err)
	}

	node, err := config.GetNodeFromPath(configRoot, path)
	if err != nil {
		return configValueAndString{}, err
	}

	if schemaNode.Def.Type != node.Def.Type {
		return configValueAndString{}, fmt.Errorf("type mismatch for path %s: %s != %s", path, schemaNode.Def.Type, node.Def.Type)
	}

	oldValue = configValueAndString{
		proto: proto.CloneOf(node.Value),
		str:   cloudconfig.ConfigValueToString(node.Value),
	}
	_, err = config.UpdateConfigNodeValue(schemaNode, node, value, true)
	if err != nil {
		return configValueAndString{}, err
	}

	return oldValue, nil
}

// addConfigListNodeInPlace adds a config node in the cached config tree.
// Assumes the config tree is already locked. Does not perform S3 write.
func (c *ConfigCache) addConfigListNodeInPlace(
	serial *carbon.ValidSerial,
	configRoot *config_service.ConfigNode,
	path string,
	name string,
	ts uint64,
) error {
	parentNode, err := config.GetNodeFromPath(configRoot, path)
	if err != nil {
		return fmt.Errorf("unable to find parent node: %w", err)
	}

	if err = config.AddToConfigNodeList(c.schemaReader, parentNode, serial.Class(), path, name, ts); err != nil {
		return fmt.Errorf("failed to add config node to list %s: %w", path, err)
	}
	return nil
}

// removeConfigListNodeInPlace removes a config node in the cached config tree.
// Assumes the config tree is already locked. Does not perform S3 write.
func (c *ConfigCache) removeConfigListNodeInPlace(
	ctx context.Context,
	serial *carbon.ValidSerial,
	configRoot *config_service.ConfigNode,
	configPath string,
	name string,
	ts uint64,
) error {
	errorPrefix := fmt.Sprintf("failed to delete %s config list key", serial)
	parentNode, err := config.GetNodeFromPath(configRoot, configPath)
	if err != nil {
		return fmt.Errorf("unable to find parent node: %w", err)
	}

	childPath := path.Join(configPath, name)
	if err = config.RemoveFromConfigNodeList(ctx, parentNode, childPath, errorPrefix, ts); err != nil {
		return fmt.Errorf("failed to remove config node from list %s: %w", configPath, err)
	}
	return nil
}

/**
 * A `KeySpec` describes a pattern to select config keys. Each component of the
 * key spec describes how to descend one level in the config tree. A component
 * can be deterministic, if it has a single literal string (e.g., "common",
 * "cameras"), or nondeterministic, if it has multiple literals and/or wildcard
 * patterns (e.g., "target*", or "row1, row2, and row3").
 *
 * The `composePathsFromKeySpec` function takes a `KeySpec` and returns a list of
 * paths that match the key spec. The paths are relative to the root of the
 * config tree. The function traverses the config tree, starting from the root
 * node, and processes each component of the key spec. For each component, it
 * finds the matching child nodes and appends their names to the path. If a
 * component is a wildcard, it finds all child nodes that match the wildcard
 * pattern and appends their names to the path. The function returns a list of
 * paths that match the key spec.
 *
 * Assumes the config tree is already locked for reading.
 **/
func (c *ConfigCache) composePathsFromKeySpec(
	ctx context.Context,
	configRoot *config_service.ConfigNode,
	keySpec *robot_syncer.KeySpec,
) ([]string, error) {
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	paths, err := processKeySpecPaths(configRoot, keySpec.GetComponents(), "")
	if err != nil {
		return nil, fmt.Errorf("failed to process key spec paths: %w", err)
	}
	slices.Sort(paths)
	paths = slices.Compact(paths)

	// trim slash from the front of the path
	for i := range paths {
		if len(paths[i]) > 0 && paths[i][0] == '/' {
			paths[i] = paths[i][1:]
		}
	}
	return paths, nil
}

func processKeySpecPaths(currentNode *config_service.ConfigNode, remainingComponents []*robot_syncer.KeyComponent, configPath string) ([]string, error) {
	if len(remainingComponents) == 0 {
		return []string{configPath}, nil
	}
	newPaths := make([]string, 0)

	// process only the first component, then recurse
	for _, branch := range remainingComponents[0].GetBranches() {
		switch branch.Branch.(type) {
		case *robot_syncer.ComponentBranch_Literal:
			childNode, ok := config.FindChildNode(currentNode, branch.GetLiteral())
			if !ok {
				return nil, fmt.Errorf("failed to find child node %s/%s", configPath, branch.GetLiteral())
			}

			newPath := path.Join(configPath, branch.GetLiteral())
			appendPaths, err := processKeySpecPaths(childNode, remainingComponents[1:], newPath)
			if err != nil {
				return nil, err
			}

			newPaths = append(newPaths, appendPaths...)
		case *robot_syncer.ComponentBranch_Wildcard:
			// wildcard can be empty or have a prefix and/or suffix
			for _, child := range currentNode.GetChildren() {
				childName := child.GetName()
				if !matchesPattern(childName, branch.GetWildcard()) {
					continue
				}

				appendPaths, err := processKeySpecPaths(child, remainingComponents[1:], path.Join(configPath, childName))
				if err != nil {
					return nil, err
				}

				newPaths = append(newPaths, appendPaths...)
			}
		default:
			return nil, fmt.Errorf("invalid key spec: %s -- %v", configPath, remainingComponents[0])
		}
	}

	return newPaths, nil
}

func matchesPattern(s string, pattern *robot_syncer.Pattern) bool {
	prefix := pattern.GetPrefix()
	suffix := pattern.GetSuffix()
	return len(s) >= len(prefix)+len(suffix) &&
		strings.HasPrefix(s, prefix) &&
		strings.HasSuffix(s, suffix)
}
