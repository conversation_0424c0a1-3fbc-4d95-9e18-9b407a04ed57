name: robot-syncer
services:
  robot-syncer:
    build:
      context: ../../
      dockerfile: ./services/robot-syncer/Dockerfile.robot-syncer.dev
      secrets:
        - GITHUB_TOKEN
    container_name: robot-syncer
    deploy:
      mode: global
    ports:
      - ${ROBOT_SYNCER_PORT:-8080}:8080
      - ${ROBOT_SYNCER_GRPC_PORT:-9090}:9090
    networks:
      - default
      - carbon
    environment:
      AUTH0_AUTH_DOMAIN: ${AUTH0_AUTH_DOMAIN}
      AUTH0_GRPC_AUDIENCE: ${AUTH0_GRPC_AUDIENCE}
      AUTH0_GRPC_CLIENT_ID: ${AUTH0_GRPC_CLIENT_ID}
      AUTH0_GRPC_CLIENT_SECRET: ${AUTH0_GRPC_CLIENT_SECRET}
      AUTH0_SERVER_ID: ${AUTH0_SERVER_ID}
      AUTH0_SERVER_SECRET: ${AUTH0_SERVER_SECRET}
      AUTH0_TENANT_DOMAIN: ${AUTH0_TENANT_DOMAIN}
      AWS_BUCKET: ${AWS_BUCKET}
      AWS_CREDENTIALS_FILE: ${AWS_CREDENTIALS_FILE}
      ENVIRONMENT: ${ENVIRONMENT}
      PORTAL_PORT: ${PORTAL_PORT}
      PORTAL_URL: ${PORTAL_URL}
      PSQL_CONNECTION: ${PSQL_CONNECTION_CONTAINER:-}
      ROBOT_SYNCER_PORT: 8080
      ROBOT_SYNCER_GRPC_PORT: 9090
      SHARED_CONFIG_SCHEMA_PATH: ${SHARED_CONFIG_SCHEMA_PATH}
      # add gRPC debug logging
      GRPC_GO_LOG_VERBOSITY_LEVEL: 99
      GRPC_GO_LOG_SEVERITY_LEVEL: "info"
    volumes:
      - ../../../:/cloud
      - ${AWS_CREDENTIALS_FILE}:${AWS_CREDENTIALS_FILE}
      
  db:
    image: ghcr.io/baosystems/postgis:17
    container_name: rosy-db
    deploy:
      mode: global
    ports:
      - ${ROBOT_SYNCER_DB_PORT}:5432
    environment:
      POSTGRES_DB: ${PSQL_DB}
      POSTGRES_PASSWORD: ${PSQL_PASSWORD}
      POSTGRES_USER: ${PSQL_USER}
    volumes:
      - /tmp:/tmp

  portal:
    build:
      dockerfile: ./Dockerfile.portal.dev
    platform: linux/amd64
    deploy:
      mode: global
    container_name: robot-syncer-portal
    volumes:
      - ./:/robot-syncer
      - ~/.kube:/root/.kube
      - ~/.aws:/root/.aws
    ports:
      - ${PORTAL_PORT}:8080
    environment:
     ROBOT_SYNCER_ENV: ${ROBOT_SYNCER_ENV}

networks:
# Used to talk to containers running in other Docker Compose projects
  carbon:
    name: carbon_network

secrets:
  GITHUB_TOKEN:
    environment: GITHUB_TOKEN
