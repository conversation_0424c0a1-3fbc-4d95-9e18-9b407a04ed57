package rest

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/crgo/carbon"
	rosypb "github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database"
)

func GetAuditLogs(db database.Querier) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		const errorPrefix = "Failed to fetch audit logs"
		if db == nil {
			ReturnError(ctx, http.StatusInternalServerError, nil, errorPrefix, "no database connection")
			return
		}

		serial, err := carbon.ParseSerial(ctx.Param("serial"))
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		key := ctx.Query("key")
		if len(key) == 0 {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "no config key provided")
			return
		}

		dbLogs, err := database.GetAuditLogs(ctx, db, serial, key)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "database read failed")
			return
		}

		response := &rosypb.GetConfigAuditLogsResponse{
			AuditLogs: make([]*rosypb.ConfigAuditLog, len(dbLogs)),
		}
		for i, dbLog := range dbLogs {
			response.AuditLogs[i] = dbLog.ToProto()
		}
		ReturnProtoJSON(ctx, response, errorPrefix)
	}
}

func RegisterAuditLogRoutes(router *gin.RouterGroup, db database.Querier) {
	auditLogs := router.Group("/config-audit-logs")
	auditLogs.GET("/:serial", GetAuditLogs(db))
}
