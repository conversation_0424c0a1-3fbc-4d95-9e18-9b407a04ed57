package rest

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/crgo/carbon"
)

func GetUnsyncedConfigKeys(configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to get unsynced keys for %s", serial)

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		unsyncedKeys := configCache.GetUnsyncedKeys(validSerial)
		jsonData, err := json.Marshal(unsyncedKeys)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to marshal unsynced keys")
			return
		}

		ctx.Data(http.StatusOK, "application/json", jsonData)
	}
}

func GetUnsyncedListRemovals(configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to get unsynced list removals for %s", serial)

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		unsyncedKeys := configCache.GetUnsyncedListRemovals(validSerial)
		jsonData, err := json.Marshal(unsyncedKeys)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to marshal unsynced list removals")
			return
		}

		ctx.Data(http.StatusOK, "application/json", jsonData)
	}
}

func RegisterUnsyncedConfigKeysRoutes(
	router *gin.RouterGroup,
	configCache ConfigCache,
) {
	router.GET("/unsynced_config_changes/:serial", GetUnsyncedConfigKeys(configCache))
	router.GET("/unsynced_list_removals/:serial", GetUnsyncedListRemovals(configCache))
}
