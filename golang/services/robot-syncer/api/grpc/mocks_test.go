// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package grpc

import (
	"context"

	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	robot_syncer0 "github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"
	"github.com/google/uuid"
	mock "github.com/stretchr/testify/mock"
)

// NewMockConfigCache creates a new instance of MockConfigCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockConfigCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockConfigCache {
	mock := &MockConfigCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockConfigCache is an autogenerated mock type for the ConfigCache type
type MockConfigCache struct {
	mock.Mock
}

type MockConfigCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockConfigCache) EXPECT() *MockConfigCache_Expecter {
	return &MockConfigCache_Expecter{mock: &_m.Mock}
}

// AddToList provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) AddToList(ctx context.Context, serial *carbon.ValidSerial, path string, name string, ts uint64) error {
	ret := _mock.Called(ctx, serial, path, name, ts)

	if len(ret) == 0 {
		panic("no return value specified for AddToList")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string, string, uint64) error); ok {
		r0 = returnFunc(ctx, serial, path, name, ts)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_AddToList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddToList'
type MockConfigCache_AddToList_Call struct {
	*mock.Call
}

// AddToList is a helper method to define mock.On call
//   - ctx context.Context
//   - serial *carbon.ValidSerial
//   - path string
//   - name string
//   - ts uint64
func (_e *MockConfigCache_Expecter) AddToList(ctx interface{}, serial interface{}, path interface{}, name interface{}, ts interface{}) *MockConfigCache_AddToList_Call {
	return &MockConfigCache_AddToList_Call{Call: _e.mock.On("AddToList", ctx, serial, path, name, ts)}
}

func (_c *MockConfigCache_AddToList_Call) Run(run func(ctx context.Context, serial *carbon.ValidSerial, path string, name string, ts uint64)) *MockConfigCache_AddToList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *carbon.ValidSerial
		if args[1] != nil {
			arg1 = args[1].(*carbon.ValidSerial)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		var arg4 uint64
		if args[4] != nil {
			arg4 = args[4].(uint64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockConfigCache_AddToList_Call) Return(err error) *MockConfigCache_AddToList_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_AddToList_Call) RunAndReturn(run func(ctx context.Context, serial *carbon.ValidSerial, path string, name string, ts uint64) error) *MockConfigCache_AddToList_Call {
	_c.Call.Return(run)
	return _c
}

// AddUnsyncedKeys provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) AddUnsyncedKeys(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) {
	_mock.Called(serial, unsyncedKeys)
	return
}

// MockConfigCache_AddUnsyncedKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUnsyncedKeys'
type MockConfigCache_AddUnsyncedKeys_Call struct {
	*mock.Call
}

// AddUnsyncedKeys is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
//   - unsyncedKeys []config.UnsyncedKey
func (_e *MockConfigCache_Expecter) AddUnsyncedKeys(serial interface{}, unsyncedKeys interface{}) *MockConfigCache_AddUnsyncedKeys_Call {
	return &MockConfigCache_AddUnsyncedKeys_Call{Call: _e.mock.On("AddUnsyncedKeys", serial, unsyncedKeys)}
}

func (_c *MockConfigCache_AddUnsyncedKeys_Call) Run(run func(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)) *MockConfigCache_AddUnsyncedKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		var arg1 []config.UnsyncedKey
		if args[1] != nil {
			arg1 = args[1].([]config.UnsyncedKey)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockConfigCache_AddUnsyncedKeys_Call) Return() *MockConfigCache_AddUnsyncedKeys_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockConfigCache_AddUnsyncedKeys_Call) RunAndReturn(run func(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)) *MockConfigCache_AddUnsyncedKeys_Call {
	_c.Run(run)
	return _c
}

// AddUnsyncedListRemovals provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) AddUnsyncedListRemovals(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) {
	_mock.Called(serial, unsyncedKeys)
	return
}

// MockConfigCache_AddUnsyncedListRemovals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUnsyncedListRemovals'
type MockConfigCache_AddUnsyncedListRemovals_Call struct {
	*mock.Call
}

// AddUnsyncedListRemovals is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
//   - unsyncedKeys []config.UnsyncedKey
func (_e *MockConfigCache_Expecter) AddUnsyncedListRemovals(serial interface{}, unsyncedKeys interface{}) *MockConfigCache_AddUnsyncedListRemovals_Call {
	return &MockConfigCache_AddUnsyncedListRemovals_Call{Call: _e.mock.On("AddUnsyncedListRemovals", serial, unsyncedKeys)}
}

func (_c *MockConfigCache_AddUnsyncedListRemovals_Call) Run(run func(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)) *MockConfigCache_AddUnsyncedListRemovals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		var arg1 []config.UnsyncedKey
		if args[1] != nil {
			arg1 = args[1].([]config.UnsyncedKey)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockConfigCache_AddUnsyncedListRemovals_Call) Return() *MockConfigCache_AddUnsyncedListRemovals_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockConfigCache_AddUnsyncedListRemovals_Call) RunAndReturn(run func(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)) *MockConfigCache_AddUnsyncedListRemovals_Call {
	_c.Run(run)
	return _c
}

// ClearUnsyncedKeys provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) ClearUnsyncedKeys(serial *carbon.ValidSerial) error {
	ret := _mock.Called(serial)

	if len(ret) == 0 {
		panic("no return value specified for ClearUnsyncedKeys")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial) error); ok {
		r0 = returnFunc(serial)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_ClearUnsyncedKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClearUnsyncedKeys'
type MockConfigCache_ClearUnsyncedKeys_Call struct {
	*mock.Call
}

// ClearUnsyncedKeys is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
func (_e *MockConfigCache_Expecter) ClearUnsyncedKeys(serial interface{}) *MockConfigCache_ClearUnsyncedKeys_Call {
	return &MockConfigCache_ClearUnsyncedKeys_Call{Call: _e.mock.On("ClearUnsyncedKeys", serial)}
}

func (_c *MockConfigCache_ClearUnsyncedKeys_Call) Run(run func(serial *carbon.ValidSerial)) *MockConfigCache_ClearUnsyncedKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockConfigCache_ClearUnsyncedKeys_Call) Return(err error) *MockConfigCache_ClearUnsyncedKeys_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_ClearUnsyncedKeys_Call) RunAndReturn(run func(serial *carbon.ValidSerial) error) *MockConfigCache_ClearUnsyncedKeys_Call {
	_c.Call.Return(run)
	return _c
}

// ClearUnsyncedListRemovals provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) ClearUnsyncedListRemovals(serial *carbon.ValidSerial) error {
	ret := _mock.Called(serial)

	if len(ret) == 0 {
		panic("no return value specified for ClearUnsyncedListRemovals")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial) error); ok {
		r0 = returnFunc(serial)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_ClearUnsyncedListRemovals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClearUnsyncedListRemovals'
type MockConfigCache_ClearUnsyncedListRemovals_Call struct {
	*mock.Call
}

// ClearUnsyncedListRemovals is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
func (_e *MockConfigCache_Expecter) ClearUnsyncedListRemovals(serial interface{}) *MockConfigCache_ClearUnsyncedListRemovals_Call {
	return &MockConfigCache_ClearUnsyncedListRemovals_Call{Call: _e.mock.On("ClearUnsyncedListRemovals", serial)}
}

func (_c *MockConfigCache_ClearUnsyncedListRemovals_Call) Run(run func(serial *carbon.ValidSerial)) *MockConfigCache_ClearUnsyncedListRemovals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockConfigCache_ClearUnsyncedListRemovals_Call) Return(err error) *MockConfigCache_ClearUnsyncedListRemovals_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_ClearUnsyncedListRemovals_Call) RunAndReturn(run func(serial *carbon.ValidSerial) error) *MockConfigCache_ClearUnsyncedListRemovals_Call {
	_c.Call.Return(run)
	return _c
}

// FillTree provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) FillTree(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string) error {
	ret := _mock.Called(ctx, node, serial, key)

	if len(ret) == 0 {
		panic("no return value specified for FillTree")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *config_service.ConfigNode, *carbon.ValidSerial, string) error); ok {
		r0 = returnFunc(ctx, node, serial, key)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_FillTree_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FillTree'
type MockConfigCache_FillTree_Call struct {
	*mock.Call
}

// FillTree is a helper method to define mock.On call
//   - ctx context.Context
//   - node *config_service.ConfigNode
//   - serial *carbon.ValidSerial
//   - key string
func (_e *MockConfigCache_Expecter) FillTree(ctx interface{}, node interface{}, serial interface{}, key interface{}) *MockConfigCache_FillTree_Call {
	return &MockConfigCache_FillTree_Call{Call: _e.mock.On("FillTree", ctx, node, serial, key)}
}

func (_c *MockConfigCache_FillTree_Call) Run(run func(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string)) *MockConfigCache_FillTree_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *config_service.ConfigNode
		if args[1] != nil {
			arg1 = args[1].(*config_service.ConfigNode)
		}
		var arg2 *carbon.ValidSerial
		if args[2] != nil {
			arg2 = args[2].(*carbon.ValidSerial)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockConfigCache_FillTree_Call) Return(err error) *MockConfigCache_FillTree_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_FillTree_Call) RunAndReturn(run func(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string) error) *MockConfigCache_FillTree_Call {
	_c.Call.Return(run)
	return _c
}

// GetConfigNodeFromPath provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) GetConfigNodeFromPath(ctx context.Context, serial *carbon.ValidSerial, path string) (*config_service.ConfigNode, error) {
	ret := _mock.Called(ctx, serial, path)

	if len(ret) == 0 {
		panic("no return value specified for GetConfigNodeFromPath")
	}

	var r0 *config_service.ConfigNode
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string) (*config_service.ConfigNode, error)); ok {
		return returnFunc(ctx, serial, path)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string) *config_service.ConfigNode); ok {
		r0 = returnFunc(ctx, serial, path)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*config_service.ConfigNode)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *carbon.ValidSerial, string) error); ok {
		r1 = returnFunc(ctx, serial, path)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockConfigCache_GetConfigNodeFromPath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConfigNodeFromPath'
type MockConfigCache_GetConfigNodeFromPath_Call struct {
	*mock.Call
}

// GetConfigNodeFromPath is a helper method to define mock.On call
//   - ctx context.Context
//   - serial *carbon.ValidSerial
//   - path string
func (_e *MockConfigCache_Expecter) GetConfigNodeFromPath(ctx interface{}, serial interface{}, path interface{}) *MockConfigCache_GetConfigNodeFromPath_Call {
	return &MockConfigCache_GetConfigNodeFromPath_Call{Call: _e.mock.On("GetConfigNodeFromPath", ctx, serial, path)}
}

func (_c *MockConfigCache_GetConfigNodeFromPath_Call) Run(run func(ctx context.Context, serial *carbon.ValidSerial, path string)) *MockConfigCache_GetConfigNodeFromPath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *carbon.ValidSerial
		if args[1] != nil {
			arg1 = args[1].(*carbon.ValidSerial)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockConfigCache_GetConfigNodeFromPath_Call) Return(configNode *config_service.ConfigNode, err error) *MockConfigCache_GetConfigNodeFromPath_Call {
	_c.Call.Return(configNode, err)
	return _c
}

func (_c *MockConfigCache_GetConfigNodeFromPath_Call) RunAndReturn(run func(ctx context.Context, serial *carbon.ValidSerial, path string) (*config_service.ConfigNode, error)) *MockConfigCache_GetConfigNodeFromPath_Call {
	_c.Call.Return(run)
	return _c
}

// GetTemplateConfigNodeFromPath provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) GetTemplateConfigNodeFromPath(ctx context.Context, class carbon.Classification, path string) (*config_service.ConfigNode, error) {
	ret := _mock.Called(ctx, class, path)

	if len(ret) == 0 {
		panic("no return value specified for GetTemplateConfigNodeFromPath")
	}

	var r0 *config_service.ConfigNode
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, carbon.Classification, string) (*config_service.ConfigNode, error)); ok {
		return returnFunc(ctx, class, path)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, carbon.Classification, string) *config_service.ConfigNode); ok {
		r0 = returnFunc(ctx, class, path)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*config_service.ConfigNode)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, carbon.Classification, string) error); ok {
		r1 = returnFunc(ctx, class, path)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockConfigCache_GetTemplateConfigNodeFromPath_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTemplateConfigNodeFromPath'
type MockConfigCache_GetTemplateConfigNodeFromPath_Call struct {
	*mock.Call
}

// GetTemplateConfigNodeFromPath is a helper method to define mock.On call
//   - ctx context.Context
//   - class carbon.Classification
//   - path string
func (_e *MockConfigCache_Expecter) GetTemplateConfigNodeFromPath(ctx interface{}, class interface{}, path interface{}) *MockConfigCache_GetTemplateConfigNodeFromPath_Call {
	return &MockConfigCache_GetTemplateConfigNodeFromPath_Call{Call: _e.mock.On("GetTemplateConfigNodeFromPath", ctx, class, path)}
}

func (_c *MockConfigCache_GetTemplateConfigNodeFromPath_Call) Run(run func(ctx context.Context, class carbon.Classification, path string)) *MockConfigCache_GetTemplateConfigNodeFromPath_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 carbon.Classification
		if args[1] != nil {
			arg1 = args[1].(carbon.Classification)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockConfigCache_GetTemplateConfigNodeFromPath_Call) Return(configNode *config_service.ConfigNode, err error) *MockConfigCache_GetTemplateConfigNodeFromPath_Call {
	_c.Call.Return(configNode, err)
	return _c
}

func (_c *MockConfigCache_GetTemplateConfigNodeFromPath_Call) RunAndReturn(run func(ctx context.Context, class carbon.Classification, path string) (*config_service.ConfigNode, error)) *MockConfigCache_GetTemplateConfigNodeFromPath_Call {
	_c.Call.Return(run)
	return _c
}

// ReconcileUnsyncedListRemovals provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) ReconcileUnsyncedListRemovals(serial *carbon.ValidSerial, absolutPath string) error {
	ret := _mock.Called(serial, absolutPath)

	if len(ret) == 0 {
		panic("no return value specified for ReconcileUnsyncedListRemovals")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial, string) error); ok {
		r0 = returnFunc(serial, absolutPath)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_ReconcileUnsyncedListRemovals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReconcileUnsyncedListRemovals'
type MockConfigCache_ReconcileUnsyncedListRemovals_Call struct {
	*mock.Call
}

// ReconcileUnsyncedListRemovals is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
//   - absolutPath string
func (_e *MockConfigCache_Expecter) ReconcileUnsyncedListRemovals(serial interface{}, absolutPath interface{}) *MockConfigCache_ReconcileUnsyncedListRemovals_Call {
	return &MockConfigCache_ReconcileUnsyncedListRemovals_Call{Call: _e.mock.On("ReconcileUnsyncedListRemovals", serial, absolutPath)}
}

func (_c *MockConfigCache_ReconcileUnsyncedListRemovals_Call) Run(run func(serial *carbon.ValidSerial, absolutPath string)) *MockConfigCache_ReconcileUnsyncedListRemovals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockConfigCache_ReconcileUnsyncedListRemovals_Call) Return(err error) *MockConfigCache_ReconcileUnsyncedListRemovals_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_ReconcileUnsyncedListRemovals_Call) RunAndReturn(run func(serial *carbon.ValidSerial, absolutPath string) error) *MockConfigCache_ReconcileUnsyncedListRemovals_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFromList provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) RemoveFromList(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64) error {
	ret := _mock.Called(ctx, serial, configPath, ts)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFromList")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string, uint64) error); ok {
		r0 = returnFunc(ctx, serial, configPath, ts)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockConfigCache_RemoveFromList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFromList'
type MockConfigCache_RemoveFromList_Call struct {
	*mock.Call
}

// RemoveFromList is a helper method to define mock.On call
//   - ctx context.Context
//   - serial *carbon.ValidSerial
//   - configPath string
//   - ts uint64
func (_e *MockConfigCache_Expecter) RemoveFromList(ctx interface{}, serial interface{}, configPath interface{}, ts interface{}) *MockConfigCache_RemoveFromList_Call {
	return &MockConfigCache_RemoveFromList_Call{Call: _e.mock.On("RemoveFromList", ctx, serial, configPath, ts)}
}

func (_c *MockConfigCache_RemoveFromList_Call) Run(run func(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64)) *MockConfigCache_RemoveFromList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *carbon.ValidSerial
		if args[1] != nil {
			arg1 = args[1].(*carbon.ValidSerial)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 uint64
		if args[3] != nil {
			arg3 = args[3].(uint64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockConfigCache_RemoveFromList_Call) Return(err error) *MockConfigCache_RemoveFromList_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockConfigCache_RemoveFromList_Call) RunAndReturn(run func(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64) error) *MockConfigCache_RemoveFromList_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateConfigNode provides a mock function for the type MockConfigCache
func (_mock *MockConfigCache) UpdateConfigNode(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool) (*config_service.ConfigValue, *config_service.ConfigValue, error) {
	ret := _mock.Called(ctx, serial, key, value, ts, enforceSchemaChoices)

	if len(ret) == 0 {
		panic("no return value specified for UpdateConfigNode")
	}

	var r0 *config_service.ConfigValue
	var r1 *config_service.ConfigValue
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string, any, uint64, bool) (*config_service.ConfigValue, *config_service.ConfigValue, error)); ok {
		return returnFunc(ctx, serial, key, value, ts, enforceSchemaChoices)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *carbon.ValidSerial, string, any, uint64, bool) *config_service.ConfigValue); ok {
		r0 = returnFunc(ctx, serial, key, value, ts, enforceSchemaChoices)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*config_service.ConfigValue)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *carbon.ValidSerial, string, any, uint64, bool) *config_service.ConfigValue); ok {
		r1 = returnFunc(ctx, serial, key, value, ts, enforceSchemaChoices)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*config_service.ConfigValue)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, *carbon.ValidSerial, string, any, uint64, bool) error); ok {
		r2 = returnFunc(ctx, serial, key, value, ts, enforceSchemaChoices)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockConfigCache_UpdateConfigNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateConfigNode'
type MockConfigCache_UpdateConfigNode_Call struct {
	*mock.Call
}

// UpdateConfigNode is a helper method to define mock.On call
//   - ctx context.Context
//   - serial *carbon.ValidSerial
//   - key string
//   - value any
//   - ts uint64
//   - enforceSchemaChoices bool
func (_e *MockConfigCache_Expecter) UpdateConfigNode(ctx interface{}, serial interface{}, key interface{}, value interface{}, ts interface{}, enforceSchemaChoices interface{}) *MockConfigCache_UpdateConfigNode_Call {
	return &MockConfigCache_UpdateConfigNode_Call{Call: _e.mock.On("UpdateConfigNode", ctx, serial, key, value, ts, enforceSchemaChoices)}
}

func (_c *MockConfigCache_UpdateConfigNode_Call) Run(run func(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool)) *MockConfigCache_UpdateConfigNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *carbon.ValidSerial
		if args[1] != nil {
			arg1 = args[1].(*carbon.ValidSerial)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 any
		if args[3] != nil {
			arg3 = args[3].(any)
		}
		var arg4 uint64
		if args[4] != nil {
			arg4 = args[4].(uint64)
		}
		var arg5 bool
		if args[5] != nil {
			arg5 = args[5].(bool)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		)
	})
	return _c
}

func (_c *MockConfigCache_UpdateConfigNode_Call) Return(oldValue *config_service.ConfigValue, newValue *config_service.ConfigValue, err error) *MockConfigCache_UpdateConfigNode_Call {
	_c.Call.Return(oldValue, newValue, err)
	return _c
}

func (_c *MockConfigCache_UpdateConfigNode_Call) RunAndReturn(run func(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool) (*config_service.ConfigValue, *config_service.ConfigValue, error)) *MockConfigCache_UpdateConfigNode_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockStreamCache creates a new instance of MockStreamCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockStreamCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStreamCache {
	mock := &MockStreamCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockStreamCache is an autogenerated mock type for the StreamCache type
type MockStreamCache struct {
	mock.Mock
}

type MockStreamCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockStreamCache) EXPECT() *MockStreamCache_Expecter {
	return &MockStreamCache_Expecter{mock: &_m.Mock}
}

// AddStream provides a mock function for the type MockStreamCache
func (_mock *MockStreamCache) AddStream(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) *cache.StreamWithContext {
	ret := _mock.Called(streamID, serial, stream)

	if len(ret) == 0 {
		panic("no return value specified for AddStream")
	}

	var r0 *cache.StreamWithContext
	if returnFunc, ok := ret.Get(0).(func(string, *carbon.ValidSerial, config_service.ConfigNotificationService_SubscribeServer) *cache.StreamWithContext); ok {
		r0 = returnFunc(streamID, serial, stream)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cache.StreamWithContext)
		}
	}
	return r0
}

// MockStreamCache_AddStream_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddStream'
type MockStreamCache_AddStream_Call struct {
	*mock.Call
}

// AddStream is a helper method to define mock.On call
//   - streamID string
//   - serial *carbon.ValidSerial
//   - stream config_service.ConfigNotificationService_SubscribeServer
func (_e *MockStreamCache_Expecter) AddStream(streamID interface{}, serial interface{}, stream interface{}) *MockStreamCache_AddStream_Call {
	return &MockStreamCache_AddStream_Call{Call: _e.mock.On("AddStream", streamID, serial, stream)}
}

func (_c *MockStreamCache_AddStream_Call) Run(run func(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer)) *MockStreamCache_AddStream_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 *carbon.ValidSerial
		if args[1] != nil {
			arg1 = args[1].(*carbon.ValidSerial)
		}
		var arg2 config_service.ConfigNotificationService_SubscribeServer
		if args[2] != nil {
			arg2 = args[2].(config_service.ConfigNotificationService_SubscribeServer)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockStreamCache_AddStream_Call) Return(streamWithContext *cache.StreamWithContext) *MockStreamCache_AddStream_Call {
	_c.Call.Return(streamWithContext)
	return _c
}

func (_c *MockStreamCache_AddStream_Call) RunAndReturn(run func(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) *cache.StreamWithContext) *MockStreamCache_AddStream_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockStreamCache
func (_mock *MockStreamCache) Delete(streamID string) {
	_mock.Called(streamID)
	return
}

// MockStreamCache_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockStreamCache_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - streamID string
func (_e *MockStreamCache_Expecter) Delete(streamID interface{}) *MockStreamCache_Delete_Call {
	return &MockStreamCache_Delete_Call{Call: _e.mock.On("Delete", streamID)}
}

func (_c *MockStreamCache_Delete_Call) Run(run func(streamID string)) *MockStreamCache_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockStreamCache_Delete_Call) Return() *MockStreamCache_Delete_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockStreamCache_Delete_Call) RunAndReturn(run func(streamID string)) *MockStreamCache_Delete_Call {
	_c.Run(run)
	return _c
}

// GetActiveStream provides a mock function for the type MockStreamCache
func (_mock *MockStreamCache) GetActiveStream(serial *carbon.ValidSerial) (*cache.StreamWithContext, bool) {
	ret := _mock.Called(serial)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveStream")
	}

	var r0 *cache.StreamWithContext
	var r1 bool
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial) (*cache.StreamWithContext, bool)); ok {
		return returnFunc(serial)
	}
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial) *cache.StreamWithContext); ok {
		r0 = returnFunc(serial)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cache.StreamWithContext)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(*carbon.ValidSerial) bool); ok {
		r1 = returnFunc(serial)
	} else {
		r1 = ret.Get(1).(bool)
	}
	return r0, r1
}

// MockStreamCache_GetActiveStream_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActiveStream'
type MockStreamCache_GetActiveStream_Call struct {
	*mock.Call
}

// GetActiveStream is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
func (_e *MockStreamCache_Expecter) GetActiveStream(serial interface{}) *MockStreamCache_GetActiveStream_Call {
	return &MockStreamCache_GetActiveStream_Call{Call: _e.mock.On("GetActiveStream", serial)}
}

func (_c *MockStreamCache_GetActiveStream_Call) Run(run func(serial *carbon.ValidSerial)) *MockStreamCache_GetActiveStream_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockStreamCache_GetActiveStream_Call) Return(streamWithContext *cache.StreamWithContext, b bool) *MockStreamCache_GetActiveStream_Call {
	_c.Call.Return(streamWithContext, b)
	return _c
}

func (_c *MockStreamCache_GetActiveStream_Call) RunAndReturn(run func(serial *carbon.ValidSerial) (*cache.StreamWithContext, bool)) *MockStreamCache_GetActiveStream_Call {
	_c.Call.Return(run)
	return _c
}

// NotifyRobot provides a mock function for the type MockStreamCache
func (_mock *MockStreamCache) NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) bool {
	ret := _mock.Called(serial, key, ts)

	if len(ret) == 0 {
		panic("no return value specified for NotifyRobot")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(*carbon.ValidSerial, string, uint64) bool); ok {
		r0 = returnFunc(serial, key, ts)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// MockStreamCache_NotifyRobot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NotifyRobot'
type MockStreamCache_NotifyRobot_Call struct {
	*mock.Call
}

// NotifyRobot is a helper method to define mock.On call
//   - serial *carbon.ValidSerial
//   - key string
//   - ts uint64
func (_e *MockStreamCache_Expecter) NotifyRobot(serial interface{}, key interface{}, ts interface{}) *MockStreamCache_NotifyRobot_Call {
	return &MockStreamCache_NotifyRobot_Call{Call: _e.mock.On("NotifyRobot", serial, key, ts)}
}

func (_c *MockStreamCache_NotifyRobot_Call) Run(run func(serial *carbon.ValidSerial, key string, ts uint64)) *MockStreamCache_NotifyRobot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *carbon.ValidSerial
		if args[0] != nil {
			arg0 = args[0].(*carbon.ValidSerial)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 uint64
		if args[2] != nil {
			arg2 = args[2].(uint64)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockStreamCache_NotifyRobot_Call) Return(success bool) *MockStreamCache_NotifyRobot_Call {
	_c.Call.Return(success)
	return _c
}

func (_c *MockStreamCache_NotifyRobot_Call) RunAndReturn(run func(serial *carbon.ValidSerial, key string, ts uint64) bool) *MockStreamCache_NotifyRobot_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockAuditLogger creates a new instance of MockAuditLogger. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAuditLogger(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAuditLogger {
	mock := &MockAuditLogger{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockAuditLogger is an autogenerated mock type for the AuditLogger type
type MockAuditLogger struct {
	mock.Mock
}

type MockAuditLogger_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAuditLogger) EXPECT() *MockAuditLogger_Expecter {
	return &MockAuditLogger_Expecter{mock: &_m.Mock}
}

// LogEvent provides a mock function for the type MockAuditLogger
func (_mock *MockAuditLogger) LogEvent(event *audit.AuditLogMessage) error {
	ret := _mock.Called(event)

	if len(ret) == 0 {
		panic("no return value specified for LogEvent")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*audit.AuditLogMessage) error); ok {
		r0 = returnFunc(event)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockAuditLogger_LogEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LogEvent'
type MockAuditLogger_LogEvent_Call struct {
	*mock.Call
}

// LogEvent is a helper method to define mock.On call
//   - event *audit.AuditLogMessage
func (_e *MockAuditLogger_Expecter) LogEvent(event interface{}) *MockAuditLogger_LogEvent_Call {
	return &MockAuditLogger_LogEvent_Call{Call: _e.mock.On("LogEvent", event)}
}

func (_c *MockAuditLogger_LogEvent_Call) Run(run func(event *audit.AuditLogMessage)) *MockAuditLogger_LogEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *audit.AuditLogMessage
		if args[0] != nil {
			arg0 = args[0].(*audit.AuditLogMessage)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockAuditLogger_LogEvent_Call) Return(err error) *MockAuditLogger_LogEvent_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockAuditLogger_LogEvent_Call) RunAndReturn(run func(event *audit.AuditLogMessage) error) *MockAuditLogger_LogEvent_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockProfileCache creates a new instance of MockProfileCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockProfileCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProfileCache {
	mock := &MockProfileCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockProfileCache is an autogenerated mock type for the ProfileCache type
type MockProfileCache struct {
	mock.Mock
}

type MockProfileCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockProfileCache) EXPECT() *MockProfileCache_Expecter {
	return &MockProfileCache_Expecter{mock: &_m.Mock}
}

// DeleteCustomerProfile provides a mock function for the type MockProfileCache
func (_mock *MockProfileCache) DeleteCustomerProfile(profileId uuid.UUID) error {
	ret := _mock.Called(profileId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteCustomerProfile")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID) error); ok {
		r0 = returnFunc(profileId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockProfileCache_DeleteCustomerProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteCustomerProfile'
type MockProfileCache_DeleteCustomerProfile_Call struct {
	*mock.Call
}

// DeleteCustomerProfile is a helper method to define mock.On call
//   - profileId uuid.UUID
func (_e *MockProfileCache_Expecter) DeleteCustomerProfile(profileId interface{}) *MockProfileCache_DeleteCustomerProfile_Call {
	return &MockProfileCache_DeleteCustomerProfile_Call{Call: _e.mock.On("DeleteCustomerProfile", profileId)}
}

func (_c *MockProfileCache_DeleteCustomerProfile_Call) Run(run func(profileId uuid.UUID)) *MockProfileCache_DeleteCustomerProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 uuid.UUID
		if args[0] != nil {
			arg0 = args[0].(uuid.UUID)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockProfileCache_DeleteCustomerProfile_Call) Return(err error) *MockProfileCache_DeleteCustomerProfile_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockProfileCache_DeleteCustomerProfile_Call) RunAndReturn(run func(profileId uuid.UUID) error) *MockProfileCache_DeleteCustomerProfile_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomerProfile provides a mock function for the type MockProfileCache
func (_mock *MockProfileCache) GetCustomerProfile(profileId uuid.UUID) (*robot_syncer.Profile, bool) {
	ret := _mock.Called(profileId)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerProfile")
	}

	var r0 *robot_syncer.Profile
	var r1 bool
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID) (*robot_syncer.Profile, bool)); ok {
		return returnFunc(profileId)
	}
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID) *robot_syncer.Profile); ok {
		r0 = returnFunc(profileId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*robot_syncer.Profile)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(uuid.UUID) bool); ok {
		r1 = returnFunc(profileId)
	} else {
		r1 = ret.Get(1).(bool)
	}
	return r0, r1
}

// MockProfileCache_GetCustomerProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomerProfile'
type MockProfileCache_GetCustomerProfile_Call struct {
	*mock.Call
}

// GetCustomerProfile is a helper method to define mock.On call
//   - profileId uuid.UUID
func (_e *MockProfileCache_Expecter) GetCustomerProfile(profileId interface{}) *MockProfileCache_GetCustomerProfile_Call {
	return &MockProfileCache_GetCustomerProfile_Call{Call: _e.mock.On("GetCustomerProfile", profileId)}
}

func (_c *MockProfileCache_GetCustomerProfile_Call) Run(run func(profileId uuid.UUID)) *MockProfileCache_GetCustomerProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 uuid.UUID
		if args[0] != nil {
			arg0 = args[0].(uuid.UUID)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockProfileCache_GetCustomerProfile_Call) Return(profile *robot_syncer.Profile, b bool) *MockProfileCache_GetCustomerProfile_Call {
	_c.Call.Return(profile, b)
	return _c
}

func (_c *MockProfileCache_GetCustomerProfile_Call) RunAndReturn(run func(profileId uuid.UUID) (*robot_syncer.Profile, bool)) *MockProfileCache_GetCustomerProfile_Call {
	_c.Call.Return(run)
	return _c
}

// GetCustomerProfiles provides a mock function for the type MockProfileCache
func (_mock *MockProfileCache) GetCustomerProfiles(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*robot_syncer.Profile, error) {
	ret := _mock.Called(customerId, profileType)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerProfiles")
	}

	var r0 []*robot_syncer.Profile
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID, *frontend.ProfileType) ([]*robot_syncer.Profile, error)); ok {
		return returnFunc(customerId, profileType)
	}
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID, *frontend.ProfileType) []*robot_syncer.Profile); ok {
		r0 = returnFunc(customerId, profileType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*robot_syncer.Profile)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(uuid.UUID, *frontend.ProfileType) error); ok {
		r1 = returnFunc(customerId, profileType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockProfileCache_GetCustomerProfiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCustomerProfiles'
type MockProfileCache_GetCustomerProfiles_Call struct {
	*mock.Call
}

// GetCustomerProfiles is a helper method to define mock.On call
//   - customerId uuid.UUID
//   - profileType *frontend.ProfileType
func (_e *MockProfileCache_Expecter) GetCustomerProfiles(customerId interface{}, profileType interface{}) *MockProfileCache_GetCustomerProfiles_Call {
	return &MockProfileCache_GetCustomerProfiles_Call{Call: _e.mock.On("GetCustomerProfiles", customerId, profileType)}
}

func (_c *MockProfileCache_GetCustomerProfiles_Call) Run(run func(customerId uuid.UUID, profileType *frontend.ProfileType)) *MockProfileCache_GetCustomerProfiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 uuid.UUID
		if args[0] != nil {
			arg0 = args[0].(uuid.UUID)
		}
		var arg1 *frontend.ProfileType
		if args[1] != nil {
			arg1 = args[1].(*frontend.ProfileType)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockProfileCache_GetCustomerProfiles_Call) Return(profiles []*robot_syncer.Profile, err error) *MockProfileCache_GetCustomerProfiles_Call {
	_c.Call.Return(profiles, err)
	return _c
}

func (_c *MockProfileCache_GetCustomerProfiles_Call) RunAndReturn(run func(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*robot_syncer.Profile, error)) *MockProfileCache_GetCustomerProfiles_Call {
	_c.Call.Return(run)
	return _c
}

// GetProfileSyncData provides a mock function for the type MockProfileCache
func (_mock *MockProfileCache) GetProfileSyncData(customerId uuid.UUID) (*robot_syncer0.GetProfileSyncDataResponse, error) {
	ret := _mock.Called(customerId)

	if len(ret) == 0 {
		panic("no return value specified for GetProfileSyncData")
	}

	var r0 *robot_syncer0.GetProfileSyncDataResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID) (*robot_syncer0.GetProfileSyncDataResponse, error)); ok {
		return returnFunc(customerId)
	}
	if returnFunc, ok := ret.Get(0).(func(uuid.UUID) *robot_syncer0.GetProfileSyncDataResponse); ok {
		r0 = returnFunc(customerId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*robot_syncer0.GetProfileSyncDataResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = returnFunc(customerId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockProfileCache_GetProfileSyncData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProfileSyncData'
type MockProfileCache_GetProfileSyncData_Call struct {
	*mock.Call
}

// GetProfileSyncData is a helper method to define mock.On call
//   - customerId uuid.UUID
func (_e *MockProfileCache_Expecter) GetProfileSyncData(customerId interface{}) *MockProfileCache_GetProfileSyncData_Call {
	return &MockProfileCache_GetProfileSyncData_Call{Call: _e.mock.On("GetProfileSyncData", customerId)}
}

func (_c *MockProfileCache_GetProfileSyncData_Call) Run(run func(customerId uuid.UUID)) *MockProfileCache_GetProfileSyncData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 uuid.UUID
		if args[0] != nil {
			arg0 = args[0].(uuid.UUID)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockProfileCache_GetProfileSyncData_Call) Return(getProfileSyncDataResponse *robot_syncer0.GetProfileSyncDataResponse, err error) *MockProfileCache_GetProfileSyncData_Call {
	_c.Call.Return(getProfileSyncDataResponse, err)
	return _c
}

func (_c *MockProfileCache_GetProfileSyncData_Call) RunAndReturn(run func(customerId uuid.UUID) (*robot_syncer0.GetProfileSyncDataResponse, error)) *MockProfileCache_GetProfileSyncData_Call {
	_c.Call.Return(run)
	return _c
}

// SaveCustomerProfile provides a mock function for the type MockProfileCache
func (_mock *MockProfileCache) SaveCustomerProfile(profile *robot_syncer.Profile) error {
	ret := _mock.Called(profile)

	if len(ret) == 0 {
		panic("no return value specified for SaveCustomerProfile")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*robot_syncer.Profile) error); ok {
		r0 = returnFunc(profile)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockProfileCache_SaveCustomerProfile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveCustomerProfile'
type MockProfileCache_SaveCustomerProfile_Call struct {
	*mock.Call
}

// SaveCustomerProfile is a helper method to define mock.On call
//   - profile *robot_syncer.Profile
func (_e *MockProfileCache_Expecter) SaveCustomerProfile(profile interface{}) *MockProfileCache_SaveCustomerProfile_Call {
	return &MockProfileCache_SaveCustomerProfile_Call{Call: _e.mock.On("SaveCustomerProfile", profile)}
}

func (_c *MockProfileCache_SaveCustomerProfile_Call) Run(run func(profile *robot_syncer.Profile)) *MockProfileCache_SaveCustomerProfile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *robot_syncer.Profile
		if args[0] != nil {
			arg0 = args[0].(*robot_syncer.Profile)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockProfileCache_SaveCustomerProfile_Call) Return(err error) *MockProfileCache_SaveCustomerProfile_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockProfileCache_SaveCustomerProfile_Call) RunAndReturn(run func(profile *robot_syncer.Profile) error) *MockProfileCache_SaveCustomerProfile_Call {
	_c.Call.Return(run)
	return _c
}
