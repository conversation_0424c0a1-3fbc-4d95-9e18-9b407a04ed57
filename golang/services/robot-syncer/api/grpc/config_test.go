package grpc

import (
	"context"
	"path"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/carbonrobotics/crgo/config/config"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/metadata"
)

func TestValidateConfigKey(t *testing.T) {
	t.<PERSON>l()
	tests := []struct {
		name           string
		key            string
		expectedClass  string
		expectedPath   string
		expectedSerial string
		wantErr        bool
	}{
		{
			name:           "empty key",
			key:            "",
			expectedClass:  "",
			expectedSerial: "",
			expectedPath:   "",
			wantErr:        true,
		},
		{
			name:           "key with leading slash",
			key:            "/slayers/slayer65",
			expectedClass:  "",
			expectedSerial: "",
			expectedPath:   "",
			wantErr:        true,
		},
		{
			name:           "key with trailing slash",
			key:            "slayers/slayer65/",
			expectedClass:  "",
			expectedSerial: "",
			expectedPath:   "",
			wantErr:        true,
		},
		{
			name:           "key with leading and trailing slash",
			key:            "/slayers/slayer65/",
			expectedClass:  "",
			expectedSerial: "",
			expectedPath:   "",
			wantErr:        true,
		},
		{
			name:           "key with consecutive slashes",
			key:            "slayers//slayer65",
			expectedClass:  "",
			expectedSerial: "",
			expectedPath:   "",
			wantErr:        true,
		},
		{
			name:           "valid key",
			key:            "slayers/slayer65",
			expectedClass:  "slayers",
			expectedSerial: "slayer65",
			expectedPath:   "",
			wantErr:        false,
		},
		{
			name:           "valid longer key",
			key:            "slayers/slayer65/common/almanac/point_categories/broadleaf/enabled",
			expectedClass:  "slayers",
			expectedSerial: "slayer65",
			expectedPath:   "common/almanac/point_categories/broadleaf/enabled",
			wantErr:        false,
		},
		{
			name:           "mvs key",
			key:            "reapers/mvs1",
			expectedClass:  "module_validation_stations",
			expectedSerial: "mvs1",
			expectedPath:   "",
			wantErr:        false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			configKey, err := validateConfigKey(test.key)
			if test.wantErr {
				if err == nil {
					t.Fatal("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			assert.Equal(t, test.expectedClass, configKey.ValidSerial.Class().String())
			assert.Equal(t, test.expectedSerial, configKey.ValidSerial.String())
			assert.Equal(t, test.expectedPath, configKey.Path)
		})
	}
}

func TestConvertValidateTimestampMs(t *testing.T) {
	testTime := time.Now()
	futureTimestamp := testTime.Add(time.Hour)
	pastTimestamp := testTime.Add(-time.Hour)
	tests := []struct {
		name              string
		ts                uint64
		expectedTimestamp time.Time
		expectError       bool
	}{
		{
			"happy path",
			uint64(testTime.UnixMilli()),
			testTime,
			false,
		},
		{
			"future timestamp",
			uint64(futureTimestamp.UnixMilli()),
			futureTimestamp,
			true,
		},
		{
			"past timestamp",
			uint64(pastTimestamp.UnixMilli()),
			pastTimestamp,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tm, err := ConvertValidateTimestampMs(test.ts)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedTimestamp.UnixMilli(), tm.UnixMilli(), "timestamp mismatch")
			}
		})
	}
}

func TestConfigService_Ping(t *testing.T) {
	tests := []struct {
		name        string
		x           int32
		expectError bool
	}{
		{
			"ping 42",
			42,
			false,
		},
		{
			"no value",
			0,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			svc := &ConfigService{}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{"slayer1"}})
			req := &config_service.PingRequest{X: test.x}
			got, err := svc.Ping(ctx, req)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.x, got.GetX())
			}
		})
	}
}

func TestConfigService_SetValue(t *testing.T) {
	saveTimeNow := timeNow
	defer func() { timeNow = saveTimeNow }()

	testTime := time.Now()

	tests := []struct {
		name                     string
		robotSerial              string
		request                  *config_service.SetValueRequest
		response                 *config_service.SetValueResponse
		getConfigNodeFromPathErr error
		updateConfigNodeErr      error
		expectNotifyRobot        bool
		expectError              bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.SetValueRequest{
				Key:   "simulators/devserver1/command/commander/log_level",
				Value: makeConfigValue(t, "warn", time.Now()),
			},
			&config_service.SetValueResponse{},
			nil,
			nil,
			true,
			false,
		},
		{
			"GetConfigNodeFromPath failure",
			"devserver1",
			&config_service.SetValueRequest{
				Key:   "simulators/devserver1/command/commander/log_level",
				Value: makeConfigValue(t, "warn", time.Now()),
			},
			&config_service.SetValueResponse{},
			assert.AnError,
			nil,
			false,
			true,
		},
		{
			"UpdateConfigNode failure",
			"devserver1",
			&config_service.SetValueRequest{
				Key:   "simulators/devserver1/command/commander/log_level",
				Value: makeConfigValue(t, "warn", time.Now()),
			},
			&config_service.SetValueResponse{},
			nil,
			assert.AnError,
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			timeNow = func() time.Time { return testTime }

			parts := strings.Split(test.request.Key, "/")
			relativeConfigKey := path.Join(parts[2:]...)
			configName := parts[len(parts)-1]

			mockConfigCache := NewMockConfigCache(t)
			mockConfigCache.On("GetConfigNodeFromPath",
				mock.Anything,
				mock.MatchedBy(func(in *carbon.ValidSerial) bool {
					return in.String() == test.robotSerial
				}),
				relativeConfigKey,
			).Return(&config_service.ConfigNode{Name: configName, Value: nil}, test.getConfigNodeFromPathErr)

			if test.getConfigNodeFromPathErr == nil {
				mockConfigCache.On("UpdateConfigNode",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
					test.request.Value,
					uint64(testTime.UnixMilli()),
					false,
				).Return(nil, nil, test.updateConfigNodeErr)
			}

			if test.getConfigNodeFromPathErr == nil && test.updateConfigNodeErr == nil && !test.expectNotifyRobot {
				mockConfigCache.On("AddUnsyncedKeys",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					mock.AnythingOfType("[]config.UnsyncedKey"),
				).Return()
			}

			mockStreamCache := NewMockStreamCache(t)
			if test.getConfigNodeFromPathErr == nil && test.updateConfigNodeErr == nil {
				mockStreamCache.On("NotifyRobot",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
					uint64(testTime.UnixMilli()),
				).Return(test.expectNotifyRobot)
			}

			mockAuditLogger := NewMockAuditLogger(t)
			if test.getConfigNodeFromPathErr == nil && test.updateConfigNodeErr == nil {
				mockAuditLogger.On("LogEvent", mock.AnythingOfType("*audit.AuditLogMessage")).Return(nil)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
				streamCache: mockStreamCache,
				auditLogger: mockAuditLogger,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.SetValue(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assertEqualProto(t, test.response, got)
			}
		})
	}
}

func TestConfigService_GetTree(t *testing.T) {
	tests := []struct {
		name                             string
		robotSerial                      string
		request                          *config_service.GetTreeRequest
		response                         *config_service.GetTreeResponse
		existingConfigNode               *config_service.ConfigNode
		getTemplateConfigNodeFromPathErr error
		getConfigNodeFromPathErr         error
		expectTemplate                   bool
		expectError                      bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.GetTreeRequest{Key: "simulators/devserver1/command/commander/log_level"},
			&config_service.GetTreeResponse{Node: &config_service.ConfigNode{Name: "log_level"}},
			&config_service.ConfigNode{Name: "log_level"},
			nil,
			nil,
			false,
			false,
		},
		{
			"GetConfigNodeFromPath failed",
			"devserver1",
			&config_service.GetTreeRequest{Key: "simulators/devserver1/command/commander/log_level"},
			&config_service.GetTreeResponse{Node: &config_service.ConfigNode{Name: "log_level"}},
			&config_service.ConfigNode{Name: "log_level"},
			nil,
			assert.AnError,
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			parts := strings.Split(test.request.Key, "/")
			relativeConfigKey := path.Join(parts[2:]...)

			mockConfigCache := NewMockConfigCache(t)

			if test.expectTemplate {
				mockConfigCache.On("GetTemplateConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return slices.Contains(carbon.SupportedRobotClasses(), in.Class().String())
					}),
					relativeConfigKey,
				).Return(test.existingConfigNode, test.getTemplateConfigNodeFromPathErr)
			} else {
				mockConfigCache.On("GetConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
				).Return(test.existingConfigNode, test.getConfigNodeFromPathErr)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.GetTree(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assertEqualProto(t, test.response, got)
			}
		})
	}
}

func TestConfigService_SetTree(t *testing.T) {
	saveTimeNow := timeNow
	defer func() { timeNow = saveTimeNow }()

	existingTime := time.Now().Add(-time.Hour)
	testTime := time.Now()

	tests := []struct {
		name                     string
		robotSerial              string
		request                  *config_service.SetTreeRequest
		response                 *config_service.SetTreeResponse
		existingConfigNode       *config_service.ConfigNode
		getConfigNodeFromPathErr error
		fillTreeErr              error
		expectNotifyRobot        bool
		expectError              bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.SetTreeRequest{
				Key: "simulators/devserver1/command/host_check",
				Node: &config_service.ConfigNode{
					Name:  "host_check",
					Value: makeConfigValue(t, nil, testTime),
					Def: &config_service.ConfigDef{
						DefaultRecommended: true,
						DefaultValue:       &config_service.ConfigValue{},
					},
					Children: []*config_service.ConfigNode{
						{
							Name:  "disk_space_error_pct",
							Value: makeConfigValue(t, uint64(95), existingTime),
							Def: &config_service.ConfigDef{
								Type:               config_service.ConfigType_UINT,
								Complexity:         config_service.ConfigComplexity_EXPERT,
								Extra:              nil,
								Hint:               "",
								DefaultRecommended: true,
								DefaultValue:       makeConfigValue(t, uint64(95), time.Time{}),
								Units:              "%",
							},
						},
						{
							Name:  "disk_space_warn_pct",
							Value: makeConfigValue(t, uint64(75), existingTime),
							Def: &config_service.ConfigDef{
								Type:               config_service.ConfigType_UINT,
								Complexity:         config_service.ConfigComplexity_EXPERT,
								Extra:              nil,
								Hint:               "",
								DefaultRecommended: true,
								DefaultValue:       makeConfigValue(t, uint64(95), time.Time{}),
								Units:              "%",
							},
						},
						{
							Name:  "max_gpu_temp_C",
							Value: makeConfigValue(t, uint64(75), existingTime),
							Def: &config_service.ConfigDef{
								Type:               config_service.ConfigType_UINT,
								Complexity:         config_service.ConfigComplexity_EXPERT,
								Extra:              nil,
								Hint:               "",
								DefaultRecommended: true,
								DefaultValue:       makeConfigValue(t, uint64(82), time.Time{}),
								Units:              "C",
							},
						},
						{
							Name:  "ptp_alarm_wait_before_alert_time_sec",
							Value: makeConfigValue(t, uint64(10), existingTime),
							Def: &config_service.ConfigDef{
								Type:               config_service.ConfigType_UINT,
								Complexity:         config_service.ConfigComplexity_EXPERT,
								Extra:              nil,
								Hint:               "",
								DefaultRecommended: true,
								DefaultValue:       makeConfigValue(t, uint64(10), time.Time{}),
								Units:              "seconds",
							},
						},
					},
				},
			},
			&config_service.SetTreeResponse{},
			&config_service.ConfigNode{},
			nil,
			nil,
			true,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			parts := strings.Split(test.request.Key, "/")
			relativeConfigKey := path.Join(parts[2:]...)

			mockConfigCache := NewMockConfigCache(t)
			mockConfigCache.On("GetConfigNodeFromPath",
				mock.Anything,
				mock.MatchedBy(func(in *carbon.ValidSerial) bool {
					return in.String() == test.robotSerial
				}),
				relativeConfigKey,
			).Return(test.existingConfigNode, test.getConfigNodeFromPathErr)

			mockConfigCache.On("FillTree",
				mock.Anything,
				mock.MatchedBy(func(in *config_service.ConfigNode) bool { return in.String() == test.request.GetNode().String() }),
				mock.MatchedBy(func(in *carbon.ValidSerial) bool {
					return in.String() == test.robotSerial
				}),
				relativeConfigKey,
			).Return(test.fillTreeErr)

			if test.request.Node.Def.Type != config_service.ConfigType_NODE && test.request.Node.Def.Type != config_service.ConfigType_LIST {
				mockConfigCache.On("GetConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
				).Return(test.existingConfigNode, test.getConfigNodeFromPathErr)
			} else if test.expectNotifyRobot {
				mockConfigCache.On("ReconcileUnsyncedListRemovals",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
				).Return(nil)
			}

			mockStreamCache := NewMockStreamCache(t)
			if test.getConfigNodeFromPathErr == nil && test.fillTreeErr == nil {
				mockStreamCache.On("NotifyRobot",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
					uint64(testTime.UnixMilli()),
				).Return(test.expectNotifyRobot)
			}

			mockAuditLogger := NewMockAuditLogger(t)
			if test.getConfigNodeFromPathErr == nil && test.fillTreeErr == nil {
				mockAuditLogger.On("LogEvent", mock.AnythingOfType("*audit.AuditLogMessage")).Return(nil)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
				streamCache: mockStreamCache,
				auditLogger: mockAuditLogger,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.SetTree(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assertEqualProto(t, test.response, got)
			}
		})
	}
}

func TestConfigService_GetLeaves(t *testing.T) {
	tests := []struct {
		name                             string
		robotSerial                      string
		request                          *config_service.GetLeavesRequest
		response                         *config_service.GetLeavesResponse
		existingConfigNode               *config_service.ConfigNode
		getTemplateConfigNodeFromPathErr error
		getConfigNodeFromPathErr         error
		expectTemplate                   bool
		expectError                      bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.GetLeavesRequest{Key: "simulators/devserver1/command/commander/log_level"},
			&config_service.GetLeavesResponse{
				Leaves: []*config_service.ConfigLeaf{
					{
						Key:   "simulators/devserver1/command/commander/log_level",
						Value: makeConfigValue(t, "info", time.Time{}),
					},
				},
			},
			&config_service.ConfigNode{
				Name:  "log_level",
				Value: makeConfigValue(t, "info", time.Time{}),
				Def: &config_service.ConfigDef{
					Type:               config_service.ConfigType_STRING,
					Complexity:         config_service.ConfigComplexity_DEVELOPER,
					DefaultRecommended: true,
					DefaultValue:       makeConfigValue(t, "info", time.Time{}),
				},
			},
			nil,
			nil,
			false,
			false,
		},
		{
			"GetConfigNodeFromPath failed",
			"devserver1",
			&config_service.GetLeavesRequest{Key: "simulators/devserver1/command/commander/log_level"},
			&config_service.GetLeavesResponse{
				Leaves: []*config_service.ConfigLeaf{
					{
						Key:   "simulators/devserver1/command/commander/log_level",
						Value: makeConfigValue(t, "info", time.Time{}),
					},
				},
			},
			&config_service.ConfigNode{},
			nil,
			assert.AnError,
			false,
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			parts := strings.Split(test.request.Key, "/")
			relativeConfigKey := path.Join(parts[2:]...)

			mockConfigCache := NewMockConfigCache(t)

			if test.expectTemplate {
				mockConfigCache.On("GetTemplateConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return slices.Contains(carbon.SupportedRobotClasses(), in.Class().String())
					}),
					relativeConfigKey,
				).Return(test.existingConfigNode, test.getTemplateConfigNodeFromPathErr)
			} else {
				mockConfigCache.On("GetConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
				).Return(test.existingConfigNode, test.getConfigNodeFromPathErr)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.GetLeaves(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assertEqualProto(t, test.response, got)
			}
		})
	}
}

func TestConfigService_AddToList(t *testing.T) {
	saveTimeNow := timeNow
	defer func() { timeNow = saveTimeNow }()

	testTime := time.Now()

	tests := []struct {
		name               string
		robotSerial        string
		request            *config_service.AddToListRequest
		existingConfigNode *config_service.ConfigNode
		addToListErr       error
		expectError        bool
		expectNotifyRobot  bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.AddToListRequest{
				Key:  "simulators/devserver1/common/disabled_rows",
				Name: "1",
			},
			&config_service.ConfigNode{
				Name: "simulators/devserver1/common/disabled_rows",
				Def: &config_service.ConfigDef{
					Type: config_service.ConfigType_LIST,
				},
			},
			nil,
			false,
			true,
		},
		{
			"AddToList failed",
			"devserver1",
			&config_service.AddToListRequest{
				Key:  "simulators/devserver1/common/disabled_rows",
				Name: "1",
			},
			&config_service.ConfigNode{},
			assert.AnError,
			true,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			timeNow = func() time.Time { return testTime }

			parts := strings.Split(test.request.Key, "/")
			relativeConfigKey := path.Join(parts[2:]...)

			mockConfigCache := NewMockConfigCache(t)

			mockConfigCache.On("AddToList",
				mock.Anything,
				mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
				relativeConfigKey,
				test.request.Name,
				uint64(testTime.UnixMilli()),
			).Return(test.addToListErr)

			if test.addToListErr == nil && !test.expectNotifyRobot {
				mockConfigCache.On("AddUnsyncedKeys",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
					mock.MatchedBy(func(in []config.UnsyncedKey) bool {
						return len(in) > 0 &&
							in[0].Key == relativeConfigKey &&
							in[0].Ts == uint64(testTime.UnixMilli()) &&
							in[0].NewValue == test.request.Name
					}),
				).Return()
			}

			mockStreamCache := NewMockStreamCache(t)
			if test.addToListErr == nil {
				mockStreamCache.On("NotifyRobot",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					relativeConfigKey,
					uint64(testTime.UnixMilli()),
				).Return(test.expectNotifyRobot)
			}

			mockAuditLogger := NewMockAuditLogger(t)
			if test.addToListErr == nil {
				mockAuditLogger.On("LogEvent", mock.AnythingOfType("*audit.AuditLogMessage")).Return(nil)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
				streamCache: mockStreamCache,
				auditLogger: mockAuditLogger,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.AddToList(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.IsType(t, &config_service.AddToListResponse{}, got)
			}
		})
	}
}

func TestConfigService_RemoveFromList(t *testing.T) {
	saveTimeNow := timeNow
	defer func() { timeNow = saveTimeNow }()

	testTime := time.Now()

	tests := []struct {
		name               string
		robotSerial        string
		request            *config_service.RemoveFromListRequest
		existingConfigNode *config_service.ConfigNode
		removeFromListErr  error
		expectError        bool
		expectNotifyRobot  bool
	}{
		{
			"happy path",
			"devserver1",
			&config_service.RemoveFromListRequest{
				Key:  "simulators/devserver1/common/disabled_rows",
				Name: "1",
			},
			&config_service.ConfigNode{
				Name: "simulators/devserver1/common/disabled_rows",
				Def: &config_service.ConfigDef{
					Type: config_service.ConfigType_LIST,
				},
				Children: []*config_service.ConfigNode{
					{
						Name: "1",
					},
				},
			},
			nil,
			false,
			true,
		},
		{
			"happy path, robot offline",
			"devserver1",
			&config_service.RemoveFromListRequest{
				Key:  "simulators/devserver1/common/disabled_rows",
				Name: "1",
			},
			&config_service.ConfigNode{
				Name: "simulators/devserver1/common/disabled_rows",
				Def: &config_service.ConfigDef{
					Type: config_service.ConfigType_LIST,
				},
				Children: []*config_service.ConfigNode{
					{
						Name: "1",
					},
				},
			},
			nil,
			false,
			false,
		},
		{
			"AddToList failed",
			"devserver1",
			&config_service.RemoveFromListRequest{
				Key:  "simulators/devserver1/common/disabled_rows",
				Name: "1",
			},
			&config_service.ConfigNode{},
			assert.AnError,
			true,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			timeNow = func() time.Time { return testTime }

			parts := strings.Split(test.request.Key, "/")
			parentPath := path.Join(parts[2 : len(parts)-1]...)
			relativeConfigKey := path.Join(parts[2:]...)
			removedNodePath := path.Join(relativeConfigKey, test.request.Name)

			mockConfigCache := NewMockConfigCache(t)

			mockConfigCache.On("RemoveFromList",
				mock.Anything,
				mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
				removedNodePath,
				uint64(testTime.UnixMilli()),
			).Return(test.removeFromListErr)

			if test.removeFromListErr == nil && !test.expectNotifyRobot {
				mockConfigCache.On("GetConfigNodeFromPath",
					mock.Anything,
					mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
					relativeConfigKey,
				).Return(test.existingConfigNode, nil)

				mockConfigCache.On("AddUnsyncedListRemovals",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
					mock.MatchedBy(func(in []config.UnsyncedKey) bool {
						return len(in) > 0 &&
							in[0].Key == removedNodePath &&
							in[0].Ts == uint64(testTime.UnixMilli())
					}),
				).Return()
				mockConfigCache.On("AddUnsyncedKeys",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool { return in.String() == test.robotSerial }),
					mock.MatchedBy(func(in []config.UnsyncedKey) bool {
						return len(in) > 0 &&
							in[0].Key == relativeConfigKey &&
							in[0].Ts == uint64(testTime.UnixMilli()) &&
							in[0].OldValue == test.existingConfigNode.Name
					}),
				).Return()
			}

			mockStreamCache := NewMockStreamCache(t)
			if test.removeFromListErr == nil {
				mockStreamCache.On("NotifyRobot",
					mock.MatchedBy(func(in *carbon.ValidSerial) bool {
						return in.String() == test.robotSerial
					}),
					parentPath,
					uint64(testTime.UnixMilli()),
				).Return(test.expectNotifyRobot)
			}

			mockAuditLogger := NewMockAuditLogger(t)
			if test.removeFromListErr == nil {
				mockAuditLogger.On("LogEvent", mock.AnythingOfType("*audit.AuditLogMessage")).Return(nil)
			}

			svc := &ConfigService{
				configCache: mockConfigCache,
				streamCache: mockStreamCache,
				auditLogger: mockAuditLogger,
			}
			ctx := context.TODO()

			ctx = metadata.NewIncomingContext(ctx, metadata.MD{"robot": []string{test.robotSerial}})
			got, err := svc.RemoveFromList(ctx, test.request)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.IsType(t, &config_service.RemoveFromListResponse{}, got)
			}
		})
	}
}

func makeConfigValue(t *testing.T, value any, timestamp time.Time) *config_service.ConfigValue {
	t.Helper()

	cfgValue := &config_service.ConfigValue{}
	if !timestamp.IsZero() {
		cfgValue.TimestampMs = uint64(timestamp.UnixMilli())
	}
	if value == nil {
		return cfgValue
	}
	switch v := value.(type) {
	case int64:
		cfgValue.Value = &config_service.ConfigValue_Int64Val{Int64Val: v}
	case uint64:
		cfgValue.Value = &config_service.ConfigValue_Uint64Val{Uint64Val: v}
	case bool:
		cfgValue.Value = &config_service.ConfigValue_BoolVal{BoolVal: v}
	case float64:
		cfgValue.Value = &config_service.ConfigValue_FloatVal{FloatVal: v}
	case string:
		cfgValue.Value = &config_service.ConfigValue_StringVal{StringVal: v}
	default:
		t.Fatalf("failed to create config value, invalid type (%T)", value)
	}
	return cfgValue
}

func assertEqualProto(t *testing.T, expected, actual proto.Message) {
	t.Helper()

	diffOpts := []cmp.Option{protocmp.Transform()}
	if diff := cmp.Diff(expected, actual, diffOpts...); diff != "" {
		t.Errorf("%T proto mismatch (-want +got):\n%s", expected, diff)
	}
}
