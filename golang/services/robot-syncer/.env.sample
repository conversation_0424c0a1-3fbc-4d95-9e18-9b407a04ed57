ENVIRONMENT=DEVELOPMENT
# ENVIRONMENT=PRODUCTION

###
# PORTS ON HOST
###
ROBOT_SYNCER_PORT=8181
ROBOT_SYNCER_GRPC_PORT=8182
ROBOT_SYNCER_DB_PORT=8183

ROBOT_SYNCER_ENV=staging
PORTAL_URL=http://portal
PORTAL_PORT=8085

###
# Database connection
###
PSQL_USER=test
PSQL_PASSWORD=testing
PSQL_HOST=db
PSQL_PORT=5432
PSQL_DB=rosylocal

PSQL_CONNECTION_CONTAINER=postgres://${PSQL_USER}:${PSQL_PASSWORD}@${PSQL_HOST}:${PSQL_PORT}/${PSQL_DB}?sslmode=disable

# Convenience definition for devs, to access the database from outside the
# container in a typical Docker compose configuration.
#
# e.g.: psql "$PSQL_CONNECTION_HOST"
# e.g.: PSQL_CONNECTION="$PSQL_CONNECTION_HOST" go run ./cmd/migrate list
PSQL_CONNECTION_HOST=postgres://${PSQL_USER}:${PSQL_PASSWORD}@localhost:${ROBOT_SYNCER_DB_PORT}/${PSQL_DB}?sslmode=disable

# Connection string used by the application.
#
# In docker-compose.yml, the variable of this name should be set to
# $PSQL_CONNECTION_CONTAINER. For convenience of running commands outside of
# the container, we set it to $PSQL_CONNECTION_HOST here.
PSQL_CONNECTION=$PSQL_CONNECTION_HOST

###
# AUTH0
###
## TESTING ##
# AUTH0_AUTH_DOMAIN=dev-jx2b6o0d.us.auth0.com
# AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
# AUTH0_GRPC_CLIENT_ID=p27gPTHGb2dfqZaJEb0D7K51i4Kl1S62
# AUTH0_GRPC_CLIENT_SECRET=<GET FROM AUTH0>
# AUTH0_SERVER_ID=p7rmzYoTeZkMRLQvpDmXLPw8zjXDkkkK
# AUTH0_SERVER_SECRET=<GET FROM AUTH0>
# AUTH0_TENANT_DOMAIN=carbonrobotics-dev.us.auth0.com
##
## STAGING ##
AUTH0_AUTH_DOMAIN=dev-jx2b6o0d.us.auth0.com
AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
AUTH0_GRPC_CLIENT_ID=tCT0FqPUvC2sVuBHOUFfdZHWcSPX49pe
AUTH0_GRPC_CLIENT_SECRET=<GET FROM AUTH0>
AUTH0_SERVER_ID=Itb9a2viOdn06v30vXExdacHdyN9dx6Q
AUTH0_SERVER_SECRET=<GET FROM AUTH0>
AUTH0_TENANT_DOMAIN=dev-jx2b6o0d.us.auth0.com
##
## PRODUCTION ##
# AUTH0_AUTH_DOMAIN=auth.carbonrobotics.com
# AUTH0_GRPC_AUDIENCE=https://robot.carbonrobotics.com
# AUTH0_GRPC_CLIENT_ID=zkuRcsVeAZDnBVOcvPmjKT69iALemQkj
# AUTH0_GRPC_CLIENT_SECRET=<GET FROM AUTH0>
# AUTH0_SERVER_ID=2xCVmxAmWtG7vdheT8aKhlw0Tf94gRpu
# AUTH0_SERVER_SECRET=<GET FROM AUTH0>
# AUTH0_TENANT_DOMAIN=carbonrobotics.us.auth0.com
##

SHARED_CONFIG_SCHEMA_PATH="/data/shared_config_schemas"
## DEV ##
AWS_BUCKET=carbon-robot-syncer-testing
## STAGING ##
# AWS_BUCKET=carbon-robot-syncer-staging
## PRODUCTION ##
# AWS_BUCKET=carbon-robot-syncer

AWS_CREDENTIALS_FILE=${HOME}/.aws/credentials
