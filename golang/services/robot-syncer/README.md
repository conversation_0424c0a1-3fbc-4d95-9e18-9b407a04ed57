# Robot Syncer

Robot Syncer is a Golang [gin](https://github.com/gin-gonic/gin) web framework managing robot
configs, config schemas, and config templates. Here is a link to the initial
[design document](https://docs.google.com/document/d/1pdEDOPUOnG0r4w6qR2dbpnp_J7UwPSrDRcbH8NRGa1I).

## Schema, Config, and Template storage

### Config Schemas

Schemas are checked into the [config_schemas repository](https://github.com/carbonrobotics/config_schemas).
That repository is submoduled by robot-syncer, the robot, and by any service
that communicates with robot-syncer (schema YAMLs are required to instantiate
the robot syncer client).

### S3 backend

Configs and config templates are stored in S3 buckets. There is one bucket
per environment (named carbon-robot-syncer<-testing|-staging|>). Each of the
3 buckets contains a folder for each robot classification (buds/simulators\etc)

Inside each of those folders is a single JSON file containing the template for
that robot class and a folder named after its associated robot serial.

Here is how that looks visually

```
carbon-robot-syncer-staging/
│
└───slayers/
│   │   template.json
│   │
│   └───slayer0/
│   |   │   robot_config.json
|   |   |   unsynced_keys.json
|   |   |   ...
│   │
│   └───slayer1/
│   |   │   robot_config.json
│   │
│   └───slayer2/
│   |   │   robot_config.json
│
└───reapers/
│   │   template.json
│   │
│   └───reaper0/
│       │   robot_config.json
|
└───simulators/
|   ...
```

Note that paths refering to nodes in a config schema start with the robot
generation, paths referring to configNodes do not.

```
ex. schema tree: simulator/command/commander/alarm/...
    config tree: command/commander/alarm...
```

### PostgreSQL database

RoSy also has a fledgling Postgres database. Code to interact with this
database is in the `./database` directory. Migrations are in
`./database/migrations`, and `./cmd/migrate` is a command-line tool to execute
migration operations.

You can run

    go run ./cmd/migrate list

to view the migrations that have been run on your database host. Run

    go run ./cmd/migrate up MIGRATION

to apply a migration, specifying it either by number or by a unique substring
of its migration name. Likewise `down` to roll back. Migration actions that are
not marked "safe" (non-destructive of data) need to be run with `--force`:

    go run ./cmd/migrate down --force MIGRATION

To add a new migration, run:

    go run ./cmd/migrate create YOUR_NEW_MIGRATION_NAME

where the migration name takes the form of an unexported Go identifier, like
`createMyTable`. This will create a new file under `database/migrations/` and
print the file name. Edit that file and fill in the TODOs as appropriate.

### Helper libraries

The code to perform custom JSON serialization and deserialization of the
ConfigNode proto model and the robot-syncer client code live in
[crgo](https://github.com/carbonrobotics/crgo/tree/master/robot_syncer)

To upgrade the crgo package after publishing changes in that repository, run

```bash
go get -u github.com/carbonrobotics/crgo
```

### GRPC API (9090)

The gRPC API is used to communicate with robots and requires a valid OAuth2
token. Configs transmitted over gRPC use protocol buffers. The proto
definitions used can be found in the protos repo under config/.

Both the Config Service running on the Robot and Robot Syncer act as servers
and clients of the defined protobuf API. They use the same request/response
definitions to communicate robot config changes to each other.

Auth is disabled when ENVIRONMENT is not set to PRODUCTION for ease of testing
locally. A list of all endpoints can be found using

```bash
grpcurl -plaintext localhost:9090 list
```

Here is an example test request

```bash
grpcurl -plaintext -d '{"x": 10}' localhost:9090 carbon.config.proto.ConfigService/Ping
grpcurl  -plaintext -d '{"key": "simulators/devserver6/rtc/tractor_ctl/speed"}' localhost:9090 carbon.config.proto.ConfigService/GetLeaves
```

### REST API (8080)

The REST API is used internally by services in the k8s cluster (such a
portal) and does not require authentication. Configs and schemas are serialized
to JSON when transmitted via REST.

### Audit logging

Audit logs containing information about when values in a config were changed
and by whom are exported to CloudWatch Log streams.

### Metrics and monitoring

Robot syncer exports metrics to prometheus. A folder was created in grafana to
organize all dashboards related to robot syncer. That folder can be found
[here](https://grafana.cloud.carbonrobotics.com/dashboards/f/GnHTtjmNk/robot-syncer).

## Setup

### Pull submodules

```bash
cd cloud
git submodule update --init --recursive
```

### Configure environment variables

```bash
cd cloud/golang/services/robot-syncer
cp .env.sample .env
```

Sensitive secrets are stored in Bitwarden (reach out to benw for access). Copy
the necessary values and paste them into the `.env` file under robot-syncer/

## Development

Start dev container

```bash
cd cloud/golang/services/robot-syncer
docker compose build
docker compose up
```

## Testing

### Unit testing

Note that there are a few tests that do not run by default as they require
AWS credentials. To activate these tests, comment out `t.SkipNow()` at the
start of the desired tests.

```bash
make test-go
```

### Integration testing

After deploying robot syncer in a local docker container, you can curl the api
with commands similar to the following.

```bash
curl http://localhost:8080/internal/v1/templates/slayers
```

### Devserver testing

Here are the instructions to override the Robot Syncer host address, directing
requests to the service running locally

1. Start Robot Syncer in its own terminal `docker compose build && docker compose up`
2. Create a reverse proxy to the command host of the desired machine and list
   the system logins

```bash
ssh -R 8080:localhost:9090 <your_devserver>
last -1 # note the source IP address
```

3. Note the location of the bot env config (likely /data/carbon/bot/robot.json)
4. Add the following override to override the robot's Robot Syncer host
   `sudo vim /data/carbon/bot/robot.json`

```json
{
  "carbon_host_overrides": {
    "CARBON_CONFIG_CLOUD_HOST": "<SOURCE_IP>",
    "CARBON_CONFIG_CLOUD_PORT": "9090"
  }
}
```

5. `bot restart commander config`
6. Confirm the changes took by running

```bash
➜ docker exec -it config bash
root@config:/robot# env | grep CLOUD
CARBON_CONFIG_CLOUD_PORT=9090
CARBON_CONFIG_CLOUD_HOST=<SOURCE_IP>
```

### Clearing out Test Data

There is a helper script for marking all profiles as deleted.
First, make sure your local environment is pointed at the test S3 bucket and NOT PROD.
Then you can run `make delete-all-profiles CUSTOMER_IDS="id1 id2"` which will delete all carbon provided profiles as well as the profiles associated with the customers you selected.

## Release

To create a release

```bash
cd cloud/golang/services/robot-syncer
make release # the appid for robot syncer is "rosy"
```

To push to staging or production, wait for the release build to complete and
[edit the image tag in argo](https://argocd.cloud.carbonrobotics.com/applications/production?node=argoproj.io%2FApplication%2Fargocd%2Fproduction%2F0&tab=parameters&resource=kind%3ADeployment%2Ckind%3APod)
to match the release version (eg. `v1.2.3`). Once a tag is released and stable, udpate the
fallback image tag for robot syncer in the kustomization.yaml for staging and production.

Then update the Robot Syncer Version History in [Confluence](https://carbonrobotics.atlassian.net/wiki/spaces/Engineering/pages/1124499469/RoSy+Version+History)
