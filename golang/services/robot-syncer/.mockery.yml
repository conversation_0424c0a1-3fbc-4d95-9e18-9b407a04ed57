all: false
dir: '{{.InterfaceDir}}'
filename: mocks_test.go
force-file-write: true
formatter: goimports
log-level: info
structname: '{{.Mock}}{{.InterfaceName}}'
pkgname: '{{.SrcPackageName}}'
recursive: false
require-template-schema-exists: true
template: testify
template-schema: '{{.Template}}.schema.json'
packages:
  github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache:
    config:
      all: true
  github.com/carbonrobotics/cloud/golang/services/robot-syncer/api/grpc:
    config:
      all: true
