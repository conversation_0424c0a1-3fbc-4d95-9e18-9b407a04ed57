###
# Server Builder
###
FROM public.ecr.aws/docker/library/golang:1.24.2-alpine AS builder
WORKDIR /build
# Install golang dependencies
ENV GOPRIVATE="github.com/carbonrobotics/*"
RUN --mount=type=secret,id=GITHUB_TOKEN export GITHUB_TOKEN=$(cat /run/secrets/GITHUB_TOKEN) && \
    apk add git && git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
COPY golang/go.* .
RUN --mount=type=secret,id=GITHUB_TOKEN export GITHUB_TOKEN=$(cat /run/secrets/GITHUB_TOKEN) && \
    go mod download
COPY protos protos

# Copy golang source files
COPY golang .
WORKDIR /build/services/robot-syncer
ARG VERSION="dev"
ARG COMMIT="unknown"
ARG BUILT_ON="0"
RUN mkdir -p /out && \
    go build \
    -ldflags="-X 'github.com/carbonrobotics/cloud/golang/pkg/build.Version=${VERSION}'\
    -X 'github.com/carbonrobotics/cloud/golang/pkg/build.Commit=${COMMIT}'\
    -X 'github.com/carbonrobotics/cloud/golang/pkg/build.BuiltOn=${BUILT_ON}'"\
    -v -o /out ./cmd/robot-syncer ./cmd/migrate

###
# Final Container
###
FROM public.ecr.aws/docker/library/alpine:3.19.1
RUN apk add --no-cache tzdata
COPY --chown=65534:0 --from=builder /out/robot-syncer /bin/app
COPY --chown=65534:0 --from=builder /out/migrate /bin/migrate
COPY --chown=65534:0 golang/services/robot-syncer/backend/shared_config_schemas /data/shared_config_schemas
USER 65534
EXPOSE 8080
WORKDIR /data
CMD ["/bin/app"]
