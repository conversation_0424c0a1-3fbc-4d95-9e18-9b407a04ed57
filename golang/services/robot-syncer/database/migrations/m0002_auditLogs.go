package migrations

import (
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

// Creates the `config_audit_logs` table.
//
// The `old_value` and `new_value` columns should be the protojson
// serialization of a `ConfigValue`. Depending on the type of action, a row may
// set neither (`SetTree`), just one (`AddToList`, `RemoveFromList`), or both
// (`SetValue`).
var m0002_auditLogs = &migrator.Migration{
	Sequence: 2,
	Name:     "m0002_auditLogs",
	Up:       migrator.SQLAction(migrator.Safe, m0002_sqlUp),
	Down:     migrator.SQLAction(migrator.Unsafe, m0002_sqlDown),
}

const m0002_sqlUp = `
CREATE TABLE config_audit_logs (
	id serial8 PRIMARY KEY,

	create_time timestamptz NOT NULL,
	user_id text NOT NULL,
	method text NOT NULL,

	serial text NOT NULL,
	key text NOT NULL,
	old_value jsonb,
	new_value jsonb
);
CREATE INDEX config_audit_logs_serial_key_create_time_idx
	ON config_audit_logs(serial, key, create_time);
`

const m0002_sqlDown = `DROP TABLE config_audit_logs;`
