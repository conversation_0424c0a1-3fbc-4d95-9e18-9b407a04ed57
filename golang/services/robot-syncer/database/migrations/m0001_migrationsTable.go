package migrations

import (
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/database/migrator"
)

// Creates the `migration_runs` table. A record in this table describes a
// single successful invocation of a migration, in either the forward direction
// or the reverse direction. There may be multiple records for a particular
// migration, in case it's rolled back and forth a few times. Each record
// includes the Git blob hash of the migration file at the time that it was
// run, so you can run `git show $theBlobHash` to see what happened.
var m0001_migrationRuns = &migrator.Migration{
	Sequence: 1,
	Name:     "m0001_migrationsTable",
	Up:       migrator.SQLAction(migrator.Safe, m0001_sqlUp),
	Down:     migrator.SQLAction(migrator.Unsafe, m0001_sqlDown),
}

const m0001_sqlUp = `
CREATE TABLE migration_runs (
	id serial8 PRIMARY KEY,

	sequence int NOT NULL,
	name text NOT NULL,

	apply_time timestamptz NOT NULL,
	direction text NOT NULL,
	blob_hash text NOT NULL,

	CONSTRAINT migration_runs_direction CHECK (direction = 'up' OR direction = 'down')
);
`

// The migration framework will never actually be able to run this
// successfully, because after dropping `migration_runs` it'll try to write a
// record to that table. But include it for completeness/example anyway.
const m0001_sqlDown = `DROP TABLE migration_runs;`
