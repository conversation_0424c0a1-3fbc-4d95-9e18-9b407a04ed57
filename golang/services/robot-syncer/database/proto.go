package database

import (
	"database/sql"
	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// ProtoJSON[*MyMessage, MyMessage] wraps a protobuf message type with a
// `database/sql` codec that stores the values in protojson. The representation
// always allows `Message` to be nil, which corresponds to `NULL::jsonb`.
//
// Since the name of a concrete instantiation of this type is unwieldy, clients
// may wish to define a transparent type alias:
//
//	type MyMessagePB = ProtoJSON[*MyMessage, MyMessage]
type ProtoJSON[T PointerToProto[P], P any] struct {
	Message T
}

type PointerToProto[P any] interface {
	*P
	proto.Message
}

// Scan implements database/sql.Scanner.
func (pj *ProtoJSON[T, P]) Scan(src any) error {
	var srcBytes []byte
	switch v := src.(type) {
	case nil:
		srcBytes = nil
	case string:
		srcBytes = []byte(v)
	case []byte:
		srcBytes = v
	default:
		return fmt.Errorf("unsupported data type for ProtoJSON: %T (%v)", v, v)
	}

	if len(srcBytes) == 0 {
		pj.Message = nil
		return nil
	}

	dst := T(new(P))
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(srcBytes, dst)
	if err != nil {
		return err
	}
	pj.Message = dst
	return nil
}

// Value implements database/sql/driver.Valuer.
func (pj ProtoJSON[T, P]) Value() (driver.Value, error) {
	if (*P)(pj.Message) == nil {
		return nil, nil
	}
	return protojson.Marshal(pj.Message)
}

// staticassertProtoJSONImplementsDatabaseSQL witnesses that `ProtoJSON[T, P]`
// implements `sql.Scanner` and `driver.Valuer` for any valid type parameters
// `T` and `P`.
func staticassertProtoJSONImplementsDatabaseSQL[T PointerToProto[P], P any]() (sql.Scanner, driver.Valuer) {
	var pj ProtoJSON[T, P]
	return &pj, pj
}
