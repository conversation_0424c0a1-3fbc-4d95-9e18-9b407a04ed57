package database

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/carbonrobotics/crgo/carbon"
	configpb "github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	rosypb "github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	"github.com/carbonrobotics/cloud/golang/pkg/util/soa"
)

type ConfigAuditLog struct {
	ID         int64     `db:"id"`
	CreateTime time.Time `db:"create_time"`

	// The Auth0 user ID of the user under whose authority this change was
	// made, or simply the robot hostname (e.g., "reaper114") if this was made
	// directly on a robot with no end-user credentials available.
	UserID string `db:"user_id"`
	// The method that was called in this operation, e.g. "UpdateConfigNode" or
	// "SetTree".
	Method string `db:"method"`

	// Serial of the robot for which the config was updated.
	Serial string `db:"serial"`
	// Full config key that was updated, under the root of the updated robot:
	// e.g., "common/software_manager/target_version".
	Key string `db:"key"`
	// Serialized protojson of old `ConfigValue`, if applicable for this
	// operation.
	OldValue ConfigValuePB `db:"old_value"`
	// Serialized protojson of new `ConfigValue`, if applicable for this
	// operation.
	NewValue ConfigValuePB `db:"new_value"`
}

type ConfigValuePB = ProtoJSON[*configpb.ConfigValue, configpb.ConfigValue]

// WriteAuditLogs writes the given audit logs to the database.
//
// CreateTime in the input will be ignored and overwritten with now(). ID will
// be ignored and auto-generated.
func WriteAuditLogs(ctx context.Context, db Querier, inputs []*ConfigAuditLog) error {
	query := `
INSERT INTO config_audit_logs (
	create_time,
	user_id, method, serial, key,
	old_value, new_value
)
SELECT
	now(),
	unnest($1::text[]), unnest($2::text[]), unnest($3::text[]), unnest($4::text[]),
	unnest($5::jsonb[]), unnest($6::jsonb[])
`
	columns, ok := soa.Collect(inputs, func(l *ConfigAuditLog) soa.Fields {
		return soa.Fields{
			soa.V(l.UserID), soa.V(l.Method), soa.V(l.Serial), soa.V(l.Key),
			soa.V(l.OldValue), soa.V(l.NewValue),
		}
	})
	if !ok {
		return nil
	}
	_, err := db.Exec(ctx, query, columns...)
	return err
}

func GetAuditLogs(ctx context.Context, db Querier, serial *carbon.ValidSerial, key string) ([]*ConfigAuditLog, error) {
	query := `
SELECT id, create_time, user_id, method, serial, key, old_value, new_value
FROM config_audit_logs
WHERE serial = $1::text AND key = $2::text
ORDER BY create_time DESC, id DESC
LIMIT $3::int
	`
	const limit = int32(1000)
	rows, _ := db.Query(ctx, query, serial.String(), key, limit)
	return pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[ConfigAuditLog])
}

func (log *ConfigAuditLog) ToProto() *rosypb.ConfigAuditLog {
	return &rosypb.ConfigAuditLog{
		CreateTime: timestamppb.New(log.CreateTime),
		UserId:     log.UserID,
		Method:     log.Method,
		Serial:     log.Serial,
		Key:        log.Key,
		OldValue:   log.OldValue.Message,
		NewValue:   log.NewValue.Message,
	}
}
