package migrator

import (
	"context"

	"github.com/jackc/pgx/v5"
)

type MigrationContext struct {
	// DB can be used to apply changes to the database. Uses of DB should not
	// commit or roll back the enclosing transaction.
	DB pgx.Tx
}

// A MigrationFunc applies a migration in the forward or backward direction. It
// should not begin, commit, or roll back any transactions; that's handled by
// the migration framework.
type MigrationFunc func(ctx context.Context, mctx MigrationContext) error

type Action struct {
	Apply MigrationFunc
	// Safety indicates whether it is safe to call this action without risking
	// data loss. For instance, a `DROP INDEX` is safe, but a `DROP TABLE` is
	// not.
	Safety Safety
}

// SQLAction is a convenient way to create an Action in the common case where
// all you need to do is execute some constant SQL text.
func SQLAction(safety Safety, sql string, args ...any) *Action {
	return &Action{
		Apply: func(ctx context.Context, mctx MigrationContext) error {
			_, err := mctx.DB.Exec(ctx, sql, args...)
			return err
		},
		Safety: safety,
	}
}

type Migration struct {
	// Sequence is a unique, globally ordered number for this migration that
	// determines the order in which migrations are run.
	Sequence int32
	// Name holds the name of this migration. This should be the same as the
	// basename of the source file containing the code for this migration,
	// minus the `.go` extension. It is used for display purposes, and also to
	// get the source file text in order to compute the hash of the migration.
	Name string

	// Up describes the forward direction of this migration.
	Up *Action
	// Down describes the reverse (rollback) direction of this migration. It
	// may be omitted if there is no way to roll back the migration, even with
	// data loss.
	Down *Action

	// Draft can be set to true to prevent the migration from being
	// automatically executed. This is intended to prevent a syntactically
	// correct but not-yet-ready migration from being auto-executed when a file
	// watcher restarts a server process on save. It should be set to false
	// once the migration is committed.
	Draft bool

	// Deprecated is set if a migration was shipped but turned out to be
	// problematic. A deprecated migration will be skipped for the purposes of
	// auto-migration.
	Deprecated bool
}

type Safety int

const (
	_ Safety = iota // leave zero value invalid, for safety
	Safe
	Unsafe
)

func (s Safety) String() string {
	switch s {
	case Safe:
		return "Safe"
	case Unsafe:
		return "Unsafe"
	default:
		return "SafetyUnspecified"
	}
}
