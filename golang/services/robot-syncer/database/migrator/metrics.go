package migrator

import (
	"context"
	"slices"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	log "github.com/sirupsen/logrus"
)

type Metrics struct {
	mg *Migrator

	migrations *prometheus.Desc

	// mu guards lastMetrics, shallowly.
	mu          sync.Mutex
	lastMetrics []prometheus.Metric
}

func (m *Metrics) setLastMetrics(metrics []prometheus.Metric) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.lastMetrics = metrics
}

func (m *Metrics) getLastMetrics() []prometheus.Metric {
	m.mu.Lock()
	defer m.mu.Unlock()
	return slices.Clone(m.lastMetrics)
}

// NewMetrics returns a Prometheus collector that records metrics about the
// state of migrations in the database.
//
// The caller should register the collector ([prometheus.Register]) and call
// its Watch method in a background goroutine.
func (mg *Migrator) NewMetrics() *Metrics {
	return &Metrics{
		mg: mg,
		migrations: prometheus.NewDesc(
			"dbmigrator_migrations",
			"Number of registered migrations known to the application",
			[]string{"state"},
			nil,
		),
	}
}

// Watch synchronously updates metrics from the bound Migrator on a timer until
// ctx expires, querying database state from db as needed.
func (m *Metrics) Watch(ctx context.Context, db Querier) {
	ticker := time.NewTicker(10 * time.Second)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.poll(ctx, db)
		}
	}
}

func (m *Metrics) poll(ctx context.Context, db Querier) {
	allRuns, err := func() ([]*MigrationRun, error) {
		ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		return ListRuns(ctx, db)
	}()
	if err != nil {
		log.WithContext(ctx).WithField("subsystem", "migrator/metrics").WithError(err).Error("Failed to list migration runs")
		m.setLastMetrics(nil)
		return
	}
	lastRuns, _ := m.mg.LastRuns(allRuns)

	type stateValue string
	const (
		stateNew        stateValue = "new"
		stateApplied    stateValue = "applied"
		stateRolledBack stateValue = "rolled_back"
		stateDeprecated stateValue = "deprecated"
		stateUnknown    stateValue = "unknown" // shouldn't happen
	)
	allStates := []stateValue{stateNew, stateApplied, stateRolledBack, stateDeprecated, stateUnknown}

	stateCounts := make(map[stateValue]int, len(allStates))
	for i, run := range lastRuns {
		state := stateUnknown
		switch run.GetDirection() {
		case DirectionUnset:
			state = stateNew
		case DirectionUp:
			state = stateApplied
		case DirectionDown:
			state = stateRolledBack
		}
		if m.mg.Migrations[i].Deprecated {
			state = stateDeprecated
		}
		stateCounts[state]++
	}
	metrics := make([]prometheus.Metric, len(allStates))
	for i, state := range allStates {
		count := stateCounts[state]
		metrics[i] = prometheus.MustNewConstMetric(m.migrations, prometheus.GaugeValue, float64(count), string(state))
	}
	m.setLastMetrics(metrics)
}

// Describe implements prometheus.Collector.
func (m *Metrics) Describe(ch chan<- *prometheus.Desc) {
	ch <- m.migrations
}

// Collect implements prometheus.Collector.
func (m *Metrics) Collect(ch chan<- prometheus.Metric) {
	for _, m := range m.getLastMetrics() {
		ch <- m
	}
}

var _ prometheus.Collector = &Metrics{}
