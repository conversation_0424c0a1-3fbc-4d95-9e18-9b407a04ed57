package migrator

import (
	"context"
	"fmt"
)

type BlockedReason string

const (
	blockedZero       BlockedReason = ""
	BlockedRolledBack               = "has been rolled back"
	BlockedDraft                    = "marked as draft"
)

// AutoMigrate attempts to run as many migrations as it safely can. A migration
// is safe to run if it has never been run, and all preceding migrations have
// been run "up" more recently than they have been run "down", and the
// migration is not marked as Draft.
//
// Thus, auto-migration may stop when it finds a migration that has been
// manually migrated down and not back up, in which case blockedOn will be that
// migration and err will be nil. This is not necessarily an error condition,
// and the application may choose to continue.
//
// If any migration fails, blockedOn will be that migration and err will be
// non-nil.
//
// If all migrations run successfully, the result will be blockedOn == nil &&
// err == nil. This includes the case where there is no work to do because all
// migrations have already been applied.
func (mg *Migrator) AutoMigrate(ctx context.Context, db Querier) (blockedOn *Migration, why BlockedReason, err error) {
	allRuns, err := ListRuns(ctx, db)
	if err != nil {
		return nil, blockedZero, err
	}
	lastRuns, _ := mg.LastRuns(allRuns)

	for i, m := range mg.Migrations {
		if m.Deprecated {
			continue
		}
		lastDir := lastRuns[i].GetDirection()
		switch lastDir {
		case DirectionUp:
			// Already applied: nothing to do.
		case DirectionDown:
			// Manually migrated down. Stop here: don't automatically re-apply,
			// and don't apply any later migrations that may depend on this.
			return m, BlockedRolledBack, nil
		case DirectionUnset:
			if m.Draft {
				return m, BlockedDraft, nil
			}
			// Not yet migrated. Let's do it.
			err := mg.ApplyAndLog(ctx, db, m, DirectionUp)
			if err != nil {
				return m, blockedZero, fmt.Errorf("applying migration #%d (%q): %w", m.Sequence, m.Name, err)
			}
		default:
			return m, blockedZero, fmt.Errorf("migration #%d (%q): unknown last direction %v", m.Sequence, m.Name, lastDir)
		}
	}
	return nil, blockedZero, nil
}
