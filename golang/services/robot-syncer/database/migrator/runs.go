package migrator

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

type MigrationRun struct {
	ID        int64     `db:"id"`
	Sequence  int32     `db:"sequence"`
	Name      string    `db:"name"`
	ApplyTime time.Time `db:"apply_time"`
	Direction Direction `db:"direction"`
	BlobHash  string    `db:"blob_hash"`
}

// GetDirection returns run.Direction, or DirectionUnset if run == nil.
func (run *MigrationRun) GetDirection() Direction {
	if run == nil {
		return DirectionUnset
	}
	return run.Direction
}

// A Querier is some kind of interface to a Postgres database that's capable of
// basic SQL operations. It might be a single connection, a connection pool, or
// a transaction context.
type Querier interface {
	Begin(ctx context.Context) (pgx.Tx, error)
	Exec(ctx context.Context, sql string, arguments ...any) (commandTag pgconn.CommandTag, err error)
	Query(ctx context.Context, sql string, args ...any) (pgx.Rows, error)
	QueryRow(ctx context.Context, sql string, args ...any) pgx.Row
}

// ListRuns finds all migration runs that have been successfully executed in a
// database, in chronological order.
func ListRuns(ctx context.Context, db Querier) ([]*MigrationRun, error) {
	rows, _ := db.Query(ctx, `
SELECT id, sequence, name, apply_time, direction, blob_hash
FROM migration_runs
ORDER BY apply_time, id
`)
	migrations, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByName[MigrationRun])
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) && pgErr.Code == sqlstateUndefinedTable {
		// Migrations not bootstrapped. This means that the initial migration
		// has never run.
		return nil, nil
	}
	return migrations, err
}

// recordRun inserts the given run. ID will be ignored and generated;
// ApplyTime will be ignored and now() will be used.
func recordRun(ctx context.Context, db Querier, run *MigrationRun) error {
	insert := `
INSERT INTO migration_runs(sequence, name, apply_time, direction, blob_hash)
VALUES ($1, $2, now(), $3, $4)
`
	args := []any{run.Sequence, run.Name, run.Direction, run.BlobHash}
	_, err := db.Exec(ctx, insert, args...)
	return err
}

// LastRuns finds the most recent succesful run of each migration. The result
// has len(lastRuns) == len(mg.Migrations), and lastRuns[i] represents the last
// run of mg.Migrations[i], which may be in either direction or may be nil if
// the migration has never successfully run. The slice `unrecognized` lists any
// migration runs that do not correspond to any migration known to mg, which is
// not necessarily an error: for instance, it can occur after rolling back a
// binary when newer migrations have been applied.
//
// The allRuns argument can be the result of mg.ListRuns, and should already be
// in chronological order.
func (mg *Migrator) LastRuns(allRuns []*MigrationRun) (lastRuns []*MigrationRun, unrecognized []*MigrationRun) {
	lastRuns = make([]*MigrationRun, len(mg.Migrations))
	seqToIndex := make(map[int32]int, len(mg.Migrations))
	for i, m := range mg.Migrations {
		seqToIndex[m.Sequence] = i
	}

	for _, run := range allRuns {
		i, ok := seqToIndex[run.Sequence]
		if !ok || mg.Migrations[i].Name != run.Name {
			unrecognized = append(unrecognized, run)
			continue
		}
		lastRuns[i] = run
	}
	return lastRuns, unrecognized
}
