package migrator

import (
	"context"
	"crypto/sha1"
	"embed"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/jackc/pgx/v5"
)

type Migrator struct {
	// Migrations is a list of all registered migrations, independent of
	// whether they have been run in any particular database.
	Migrations []*Migration
	// FS is a file system containing the source code for each migration.
	// Create this with a `//go:embed *.go` directive in the directory
	// containing the migrations. See godoc for package embed for more details.
	FS embed.FS
}

var (
	// ErrNoAction is returned when trying to migrate down a migration with no
	// Down action, or likewise for Up.
	ErrNoAction = errors.New("migration has no action in this direction")
)

// Apply runs a migration and, if successful, records the execution in the
// migration runs table.
func (mg *Migrator) Apply(ctx context.Context, db Querier, migration *Migration, dir Direction) error {
	var action *Action
	switch dir {
	case DirectionUp:
		action = migration.Up
	case DirectionDown:
		action = migration.Down
	default:
		return fmt.Errorf("unknown direction: %v", dir)
	}
	if action == nil {
		return fmt.Errorf("%w: %v", ErrNoAction, dir)
	}

	blobHash, err := mg.BlobHash(migration.Name)
	if err != nil {
		return fmt.Errorf("computing migration hash: %w", err)
	}

	return pgx.BeginFunc(ctx, db, func(tx pgx.Tx) error {
		if err := action.Apply(ctx, MigrationContext{DB: tx}); err != nil {
			return fmt.Errorf("invoking action: %w", err)
		}
		run := &MigrationRun{
			Sequence:  migration.Sequence,
			Name:      migration.Name,
			Direction: dir,
			BlobHash:  blobHash,
		}
		if err := recordRun(ctx, tx, run); err != nil {
			return fmt.Errorf("recording migration run: %w", err)
		}
		return nil
	})
}

// ApplyAndLog calls Apply and also writes a log line to the default Logrus
// logger, at either INFO or ERROR level depending on the result.
func (mg *Migrator) ApplyAndLog(ctx context.Context, db Querier, migration *Migration, dir Direction) error {
	start := time.Now()
	err := mg.Apply(ctx, db, migration, dir)
	elapsed := time.Since(start)

	logger := log.WithFields(log.Fields{
		"subsystem": "database/migrator",
		"migration": migration.Name,
		"direction": dir,
		"elapsed":   elapsed,
	})
	if err == nil {
		logger.Info("Applied migration")
	} else {
		logger.WithError(err).Error("Failed to apply migration")
	}
	return err
}

// BlobHash gets the Git blob hash of the current version of the migration with
// the given name. It returns a hex digest on success, or an error if the
// migration file does not exist in this migrator's virtual file system.
func (mg *Migrator) BlobHash(name string) (string, error) {
	filename := name + ".go"
	contents, err := mg.FS.ReadFile(filename)
	if err != nil {
		return "", err
	}

	hasher := sha1.New()
	// Match the format of `git hash-object -t blob`, so that you can take the
	// `blob_hash` from the database and give it directly to `git show`.
	hasher.Write([]byte(fmt.Sprintf("blob %d\x00", len(contents))))
	hasher.Write(contents)
	return hex.EncodeToString(hasher.Sum(nil)), nil
}
