package migrator

import (
	"slices"
	"testing"
)

func TestLastRuns(t *testing.T) {
	mg := &Migrator{
		Migrations: []*Migration{
			{Sequence: 1, Name: "m01_one"},
			{Sequence: 2, Name: "m02_two"},
			{Sequence: 3, Name: "m03_three"},
		},
	}
	runs := []*MigrationRun{
		{ID: 1, Sequence: 1, Name: "m01_one", Direction: DirectionUp, BlobHash: "aa"},
		{ID: 2, Sequence: 2, Name: "m02_two", Direction: DirectionUp, BlobHash: "bb"},
		{ID: 3, Sequence: 2, Name: "m02_two", Direction: DirectionDown, BlobHash: "bb"},
		{ID: 4, Sequence: 2, Name: "wrong", Direction: DirectionUp, BlobHash: "bb"},
		{ID: 5, Sequence: 99, Name: "m99_future", Direction: DirectionUp, BlobHash: "zz"},
	}

	gotLastRuns, gotUnrecognized := mg.LastRuns(runs)
	wantLastRuns := []*MigrationRun{runs[0], runs[2], nil}
	wantUnrecognized := []*MigrationRun{runs[3], runs[4]}

	if !slices.Equal(gotLastRuns, wantLastRuns) {
		t.Errorf("lastRuns: got %v, want %v", gotLastRuns, wantLastRuns)
	}
	if !slices.Equal(gotUnrecognized, wantUnrecognized) {
		t.Errorf("unrecognized: got %v, want %v", gotUnrecognized, wantUnrecognized)
	}
}
