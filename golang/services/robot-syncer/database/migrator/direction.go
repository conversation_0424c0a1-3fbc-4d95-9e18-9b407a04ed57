package migrator

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

// Direction is either "up", for the forward direction of a migration, or
// "down", for the reverse direction.
type Direction int

const (
	DirectionUnset Direction = iota // leave zero value invalid, for safety
	DirectionUp
	DirectionDown
)

// Persistent representations of direction in the database's migration runs
// table. Also used for String().
const (
	textDirectionUp   = "up"
	textDirectionDown = "down"
)

func (d Direction) DBValue() (string, bool) {
	switch d {
	case DirectionUp:
		return textDirectionUp, true
	case DirectionDown:
		return textDirectionDown, true
	default:
		return "", false
	}
}

func (d Direction) String() string {
	if name, ok := d.DBValue(); ok {
		return name
	}
	return fmt.Sprintf("Direction(%d)", int(d))
}

// Scan implements database/sql.Scanner.
func (d *Direction) Scan(src any) error {
	var srcText string
	switch v := src.(type) {
	case int64:
		result := int(v)
		if int64(result) != v {
			return fmt.Errorf("int out of range: %v", v)
		}
		*d = Direction(result)
		return nil
	case string:
		srcText = v
	case []byte:
		srcText = string(v)
	default:
		return fmt.Errorf("unsupported data type for Direction: %T (%v)", v, v)
	}
	switch srcText {
	case textDirectionUp:
		*d = DirectionUp
		return nil
	case textDirectionDown:
		*d = DirectionDown
		return nil
	default:
		return fmt.Errorf("unknown text representation for Direction: %q", srcText)
	}
}

// Value implements database/sql/driver.Valuer.
func (d Direction) Value() (driver.Value, error) {
	if name, ok := d.DBValue(); ok {
		return name, nil
	}
	return nil, fmt.Errorf("invalid direction: %d", int(d))
}

var _ sql.Scanner = (*Direction)(nil)
var _ driver.Valuer = Direction(0)
