package migrator

import (
	"embed"
	"errors"
	"io/fs"
	"testing"
)

//go:embed *.go
var testFS embed.FS

func TestReadBlobHash(t *testing.T) {
	const name = fakeMigrationName
	mg := &Migrator{FS: testFS}
	got, err := mg.BlobHash(name)
	if err != nil {
		t.Fatalf("BlobHash(%q) errored: %v", name, err)
	}
	// git hash-object -t blob "${name}.go"
	const want = "f812c89f8968585029f97161993c2ad7a83786ee"
	if got != want {
		t.<PERSON><PERSON><PERSON>("BlobHash(%q): got %q, want %q", name, got, want)
	}
}

func TestReadBlobHashNoSuchFile(t *testing.T) {
	const name = "please_dont_create_this_go_file"
	mg := &Migrator{FS: testFS}
	_, err := mg.BlobHash(name)
	if !errors.Is(err, fs.ErrNotExist) {
		t.<PERSON><PERSON>rf("BlobHash(%q): got err = %q, want ErrNotExist", name, err)
	}
}
