package migrator

import (
	"testing"
)

// Test runs tests for the given migrator.
//
// Note that this is not a free-standing test function itself. You should
// create a test file for _your specific_ migrator, and call Test on it from a
// top-level function.
func (mg *Migrator) Test(t *testing.T) {
	t.Run("NamesUnique", func(t *testing.T) {
		names := make(map[string]struct{})
		for _, m := range mg.Migrations {
			if _, ok := names[m.Name]; ok {
				t.Errorf("duplicate name: %q", m.Name)
			}
			names[m.Name] = struct{}{}
		}
	})

	t.Run("SequenceNumbersIncrementing", func(t *testing.T) {
		for i, m := range mg.Migrations {
			want := int32(i) + 1
			if m.Sequence != want {
				t.Errorf("mg.Migrations[%d].Sequence: got %v, want %v (name: %q)", i, m.Sequence, want, m.Name)
			}
		}
	})

	// In particular, this tests that each migration's name properly corresponds to
	// its source file.
	t.Run("BlobHashesComputable", func(t *testing.T) {
		for i, m := range mg.Migrations {
			_, err := mg.BlobHash(m.Name)
			if err != nil {
				t.Errorf("mg.Migrations[%d]: BlobHash(%q) errored: %v", i, m.Name, err)
			}
		}
	})

	t.Run("NoDrafts", func(t *testing.T) {
		for _, m := range mg.Migrations {
			if m.Draft {
				t.Errorf("migration %q is marked as draft", m.Name)
			}
		}
	})
}
