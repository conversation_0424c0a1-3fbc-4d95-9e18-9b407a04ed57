package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/slack-go/slack"
)

type onCallSlackGroup struct {
	service Service
	group   slack.UserGroup
}

type OnCallGroupManager struct {
	cache    map[string]onCallSlackGroup
	interval time.Duration
	sc       SlackClient
	pd       PagerDuty
}

type PagerDuty interface {
	ListServices(ctx context.Context) (ServiceList, error)
	GetOnCallUsers(ctx context.Context, serviceID string, escalationLevels []uint) ([]User, error)
}

// NewOnCallGroupManager creates an instance that, once Run is invoked,
// list existing pagerduty service groups, and create related slack user groups.
// periodically checking who is on call and updating the slack user group appropriately.
// no users will cause the user group to go disabled. Run will block, and is expecte to be ran in a
// go routine cancelling the context to exit gracefully.
func NewOnCallGroupManager(sc SlackClient, pd *PagerDutyClient, interval time.Duration) *OnCallGroupManager {
	return &OnCallGroupManager{
		cache:    make(map[string]onCallSlackGroup),
		interval: interval,
		sc:       sc,
		pd:       pd,
	}
}

func (m *OnCallGroupManager) Run(ctx context.Context) {
	log.Println("starting on call group manager interval:", m.interval)
	for {
		if err := m.createRefreshServiceGroups(ctx); err != nil {
			log.Println("failed to refresh ServiceGroups", err)
		}
		if err := m.syncOnCallGroups(ctx); err != nil {
			log.Println("failed to sync on call groups", err)
		}
		select {
		case <-ctx.Done():
			return
		case <-time.After(m.interval):
		}
	}
}

var serviceLevelsOverride = map[string][]uint{
	"P4CYPEN": {1, 2, 3}, // Technical Support On call -> https://carbonrobotics.pagerduty.com/service-directory/P4CYPEN
}

var slackGroupOverride = map[string]string{
	//"oncall-technical-support": "S03N4HQRCRF", // @oncall-technical-support 'S05BPK0N1JM' get's overridden with the members of @support-team 'S03N4HQRCRF'
}

func (m *OnCallGroupManager) syncOnCallGroups(ctx context.Context) error {
	log.Println("syncing on call groups")
	for id, ocg := range m.cache {
		escalationLevels := []uint{1}
		if override, ok := serviceLevelsOverride[ocg.service.ID]; ok {
			escalationLevels = override
		}
		userNames := make([]string, 0)
		onCallUIDs := make([]string, 0)

		if override, ok := slackGroupOverride[ocg.group.Handle]; ok {
			log.Println("Override for group:", ocg.group.Name, ocg.group.ID, "exists! replacing users with members of group:", override)
			overrideGroupUsers, err := m.sc.GetUserGroupMembersContext(ctx, override)
			if err != nil {
				log.Println("failed ot get group membership of GroupID:", override)
			} else {
				onCallUIDs = overrideGroupUsers
			}
		} else {
			onCallUsers, err := m.pd.GetOnCallUsers(ctx, ocg.service.ID, escalationLevels)
			if err != nil {
				// this is known to happen when someone deletes the service. So we allow failure and just move on to the next service.
				log.Println("failed to getOnCallUsers for service id:", ocg.service.ID)
				continue
			}
			for _, u := range onCallUsers {
				su, err := GetSlackUserByEmail(m.sc, u.Email)
				if err != nil {
					return err
				}
				onCallUIDs = append(onCallUIDs, su.ID)
				userNames = append(userNames, su.Name)
			}
		}

		group := ocg.group
		disabled := group.DateDelete > 0
		prevOnCall := ocg.group.Users
		groupUserIDs := strings.Join(onCallUIDs, ",")
		groupUserHandles := strings.Join(userNames, ",")

		if len(groupUserIDs) < 1 && !disabled {
			log.Println("no members, disabling slack group:", group.Name, "onCall:", groupUserHandles)
			if grp, err := m.sc.DisableUserGroupContext(ctx, group.ID); err != nil {
				log.Println("failed to disable group:", group.Name, group.ID, err)
			} else {
				group = grp
			}
		}
		if len(groupUserIDs) > 0 && disabled {
			log.Println("members for empty group, enabling:", group.Name)
			if grp, err := m.sc.EnableUserGroupContext(ctx, group.ID); err != nil {
				log.Println("failed to enable slack group:", group.Name, group.ID, err)
			} else {
				group = grp
				disabled = false
			}
		}

		if equalValuesStringSlice(prevOnCall, onCallUIDs) {
			log.Println("group:", group.Name, "disabled:", disabled, "onCall:", groupUserHandles, "no update")
			continue
		}

		if len(groupUserIDs) > 0 && !disabled {
			log.Printf("updating slack group members of %s (@%s) to %v\n", ocg.group.Name, ocg.group.Handle, groupUserHandles)
			if grp, err := m.sc.UpdateUserGroupMembersContext(ctx, group.ID, groupUserIDs); err != nil {
				log.Println("failed to update slack user group members group:", group.Name, group.ID, "members:", groupUserHandles, err)
			} else {
				group = grp
			}
		}
		ocg.group = group
		m.cache[id] = ocg
	}
	return nil
}

func (m *OnCallGroupManager) createRefreshServiceGroups(ctx context.Context) error {
	log.Println("retrieving pager duty services...")
	services, err := m.pd.ListServices(ctx)
	if err != nil {
		return err
	}
	log.Println("retrieving slack groups..")
	groups, err := m.sc.GetUserGroupsContext(ctx, slack.GetUserGroupsOptionIncludeUsers(true), slack.GetUserGroupsOptionIncludeDisabled(true))
	if err != nil {
		return err
	}

	for _, svc := range services {
		groupHandle := pdOnCallSlackHandle(svc.Name)
		groupName := fmt.Sprintf("%s On-Call", svc.Name)
		exist := false
		for _, group := range groups {
			if group.Handle == groupHandle {
				exist = true
				ocg, ok := m.cache[group.ID]
				if !ok {
					ocg = onCallSlackGroup{}
				}
				ocg.service = svc
				ocg.group = group
				m.cache[group.ID] = ocg
				log.Printf("updated existing group: %s (@%s) - %v\n", group.Name, group.Handle, group.Users)
				break
			}
		}
		if !exist {
			group, err := m.sc.CreateUserGroupContext(ctx, slack.UserGroup{
				IsUserGroup: true,
				Name:        groupName,
				Description: groupName,
				Handle:      groupHandle,
			})
			if err != nil {
				return err
			}
			ocg, ok := m.cache[group.ID]
			if !ok {
				ocg = onCallSlackGroup{}
			}
			ocg.service = svc
			ocg.group = group
			m.cache[group.ID] = ocg
			log.Printf("created new group: %s (@%s)\n", group.Name, group.Handle)
		}
	}

	return nil
}

func pdOnCallSlackHandle(svcName string) string {
	prefix := "oncall-"
	lower := strings.ToLower(strings.TrimSpace(svcName))
	handle := strings.ReplaceAll(lower, " ", "-")
	if !strings.HasPrefix(handle, prefix) {
		handle = fmt.Sprint(prefix, handle)
	}
	return handle
}

func equalValuesStringSlice(s1, s2 []string) bool {
	if len(s1) != len(s2) {
		return false
	}
	for _, v1 := range s1 {
		found := false
		for _, v2 := range s2 {
			if v1 == v2 {
				found = true
				break
			}
		}
		if found == false {
			return false
		}
	}
	return true
}
