package models

type Job struct {
	Base            Model   `json:"base" gorm:"embedded"`
	RobotID         uint    `json:"robot_id" gorm:"index"`
	JobId           string  `json:"job_id" gorm:"uniqueIndex"`
	Name            string  `json:"name"`
	TimestampMs     int64   `json:"timestamp_ms"`
	BandingProfile  string  `json:"banding_profile"`
	ThinningProfile string  `json:"thinning_profile"`
	StopTimestampMs int64   `json:"stop_timestamp_ms"`
	ExpectedAcreage float64 `json:"acreage"`
	Completed       bool    `json:"completed"`
	CropId          string  `json:"crop_id"`
	Almanac         string  `json:"almanac"`
	Discriminator   string  `json:"discriminator"`
}

type JobConfigDump struct {
	Base    Model  `json:"base" gorm:"embedded"`
	JobId   string `json:"job_id" gorm:"uniqueIndex"`
	Configs string `json:"configs" gorm:"type:text"`
}

type JobMetrics struct {
	Base                          Model   `json:"base" gorm:"embedded"`
	JobId                         string  `json:"job_id" gorm:"uniqueIndex"`
	AcresWeeded                   float64 `json:"acresWeeded"`
	AvgCropSizeMM                 float64 `json:"avgCropSizeMm"`
	AvgSpeedMPH                   float64 `json:"avgSpeedMph"`
	AvgTargetableReqLaserTime     int64   `json:"avgTargetableReqLaserTime"`
	AvgUntargetableReqLaserTime   int64   `json:"avgUntargetableReqLaserTime"`
	AvgWeedSizeMM                 float64 `json:"avgWeedSizeMm"`
	BandingConfigName             string  `json:"bandingConfigName"`
	BandingEnabled                bool    `json:"bandingEnabled"`
	BandingPercentage             float64 `json:"bandingPercentage"`
	CoverageSpeedAcresHr          float64 `json:"coverageSpeedAcresHr"`
	CropDensitySqFt               float64 `json:"cropDensitySqFt"`
	DistanceWeededMeters          float64 `json:"distanceWeededMeters"`
	EmbeddingsActiveUptimeSeconds float64 `json:"embeddingsActiveUptimeSeconds"`
	KeptCrops                     int64   `json:"keptCrops"`
	KilledWeeds                   int64   `json:"killedWeeds"`
	MissedWeeds                   int64   `json:"missedWeeds"`
	MissedCrops                   int64   `json:"missedCrops"`
	NotThinning                   int64   `json:"notThinning"`
	NotWeeding                    int64   `json:"notWeeding"`
	SkippedCrops                  int64   `json:"skippedCrops"`
	SkippedWeeds                  int64   `json:"skippedWeeds"`
	ThinnedCrops                  int64   `json:"thinnedCrops"`
	ThinningEfficiency            float64 `json:"thinningEfficiency"`
	TimeEfficiency                float64 `json:"timeEfficiency"`
	TotalCrops                    int64   `json:"totalCrops"`
	TotalWeeds                    int64   `json:"totalWeeds"`
	UptimeSeconds                 float64 `json:"uptimeSeconds"`
	ValidCrops                    int64   `json:"validCrops"`
	WeedDensitySqFt               float64 `json:"weedDensitySqFt"`
	WeedingUptimeSeconds          float64 `json:"weedingUptimeSeconds"`
	WeedingEfficiency             float64 `json:"weedingEfficiency"`
	WeedsTypeCountBroadleaf       int64   `json:"weedsTypeCountBroadleaf"`
	WeedsTypeCountGrass           int64   `json:"weedsTypeCountGrass"`
	WeedsTypeCountOffshoot        int64   `json:"weedsTypeCountOffshoot"`
	WeedsTypeCountPurslane        int64   `json:"weedsTypeCountPurslane"`
	CropId                        string  `json:"cropId"`
}
