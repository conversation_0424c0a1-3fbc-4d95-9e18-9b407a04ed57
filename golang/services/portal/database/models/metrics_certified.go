package models

type DailyMetricName string

const (
	ACRES_WEEDED                     DailyMetricName = "acres_weeded"
	AVG_CROP_SIZE_MM                 DailyMetricName = "avg_crop_size_mm"
	AVG_WEED_SIZE_MM                 DailyMetricName = "avg_weed_size_mm"
	AVG_TARGETABLE_REQ_LASER_TIME    DailyMetricName = "avg_targetable_req_laser_time"
	AVG_UNTARGETABLE_REQ_LASER_TIME  DailyMetricName = "avg_untargetable_req_laser_time"
	BANDING_CONFIG_NAME              DailyMetricName = "banding_config_name"
	BANDING_ENABLED                  DailyMetricName = "banding_enabled"
	BANDING_PERCENTAGE               DailyMetricName = "banding_percentage"
	DISTANCE_WEEDED                  DailyMetricName = "distance_weeded"
	EMBEDDINGS_ACTIVE_UPTIME_SECONDS DailyMetricName = "embeddings_active_uptime_seconds"
	KEPT_CROPS                       DailyMetricName = "kept_crops"
	KILLED_WEEDS                     DailyMetricName = "killed_weeds"
	MISSED_CROPS                     DailyMetricName = "missed_crops"
	MISSED_WEEDS                     DailyMetricName = "missed_weeds"
	NOT_THINNING                     DailyMetricName = "not_thinning"
	NOT_WEEDING                      DailyMetricName = "not_weeding"
	NOT_WEEDING_WEEDS                DailyMetricName = "not_weeding"
	SKIPPED_CROPS                    DailyMetricName = "skipped_crops"
	SKIPPED_WEEDS                    DailyMetricName = "skipped_weeds"
	THINNED_CROPS                    DailyMetricName = "thinned_crops"
	TOTAL_CROPS                      DailyMetricName = "total_crops"
	TOTAL_WEEDS                      DailyMetricName = "total_weeds"
	UPTIME_SECONDS                   DailyMetricName = "uptime_seconds"
	VALID_CROPS                      DailyMetricName = "valid_crops"
	WEEDING_UPTIME_SECONDS           DailyMetricName = "weeding_uptime_seconds"
	WEEDS_TYPE_COUNT_BROADLEAF       DailyMetricName = "weeds_type_count_BROADLEAF"
	WEEDS_TYPE_COUNT_GRASS           DailyMetricName = "weeds_type_count_GRASS"
	WEEDS_TYPE_COUNT_OFFSHOOT        DailyMetricName = "weeds_type_count_OFFSHOOT"
	WEEDS_TYPE_COUNT_PURSLANE        DailyMetricName = "weeds_type_count_PURSLANE"
)

type DailyMetric struct {
	Base    Model  `json:"base" gorm:"embedded"`
	Serial  string `json:"serial" gorm:"not null"`
	RobotID uint   `json:"robotId" gorm:"not null; index:idx_daily_metrics_robot_id_date,priority:1"`
	Robot   Robot  `json:"robot"`
	Date    string `json:"date" gorm:"index:,sort:asc; index:idx_daily_metrics_robot_id_date,priority:2,sort:desc"`
	JobID   string `json:"jobId"`

	// metrics
	AcresWeeded                   float64 `json:"acresWeeded"`
	AvgCropSizeMM                 float64 `json:"avgCropSizeMm"`
	AvgSpeedMPH                   float64 `json:"avgSpeedMph"`
	AvgTargetableReqLaserTime     int64   `json:"avgTargetableReqLaserTime"`
	AvgUntargetableReqLaserTime   int64   `json:"avgUntargetableReqLaserTime"`
	AvgWeedSizeMM                 float64 `json:"avgWeedSizeMm"`
	BandingConfigName             string  `json:"bandingConfigName"`
	BandingEnabled                bool    `json:"bandingEnabled"`
	BandingPercentage             float64 `json:"bandingPercentage"`
	CoverageSpeedAcresHr          float64 `json:"coverageSpeedAcresHr"`
	CropDensitySqFt               float64 `json:"cropDensitySqFt"`
	CropID                        string  `json:"cropId"`
	DistanceWeededMeters          float64 `json:"distanceWeededMeters"`
	KeptCrops                     int64   `json:"keptCrops"`
	EmbeddingsActiveUptimeSeconds float64 `json:"embeddingsActiveUptimeSeconds"`
	KilledWeeds                   int64   `json:"killedWeeds"`
	MissedCrops                   int64   `json:"missedCrops"`
	MissedWeeds                   int64   `json:"missedWeeds"`
	NotThinning                   int64   `json:"notThinning"`
	NotWeeding                    int64   `json:"notWeeding"`
	NotWeedingWeeds               int64   `json:"notWeedingWeeds"`
	SkippedCrops                  int64   `json:"skippedCrops"`
	SkippedWeeds                  int64   `json:"skippedWeeds"`
	ThinnedCrops                  int64   `json:"thinnedCrops"`
	ThinningEfficiency            float64 `json:"thinningEfficiency"`
	TimeEfficiency                float64 `json:"timeEfficiency"`
	TotalCrops                    int64   `json:"totalCrops"`
	TotalWeeds                    int64   `json:"totalWeeds"`
	UptimeSeconds                 float64 `json:"uptimeSeconds"`
	ValidCrops                    int64   `json:"validCrops"`
	WeedDensitySqFt               float64 `json:"weedDensitySqFt"`
	WeedingEfficiency             float64 `json:"weedingEfficiency"`
	WeedingUptimeSeconds          float64 `json:"weedingUptimeSeconds"`
	WeedsTypeCountBroadleaf       int64   `json:"weedsTypeCountBroadleaf"`
	WeedsTypeCountGrass           int64   `json:"weedsTypeCountGrass"`
	WeedsTypeCountOffshoot        int64   `json:"weedsTypeCountOffshoot"`
	WeedsTypeCountPurslane        int64   `json:"weedsTypeCountPurslane"`
}

type SegmentedDailyMetricName string

const (
	ACRES_WEEDED_BY_MODEL  SegmentedDailyMetricName = "acres_weeded"
	ACTIVE_UPTIME_BY_MODEL SegmentedDailyMetricName = "active_use"
)

/**
 * SegmentedDailyMetric is a struct that represents a daily metric that is
 * segmented by crop, robot, and model.
 **/
type SegmentedDailyMetric struct {
	Base Model `json:"base" gorm:"embedded"`

	/**
	 * COMPOSITE KEYS USED AS UNIQUE INDEX
	 * All of these fields are used as a composite key to uniquely identify a
	 * SegmentedDailyMetric. If changes are made to these fields, those changes
	 * should be reflected in any code reading or writing from this table.
	**/
	RobotID uint   `json:"robotId" gorm:"not null; uniqueIndex:idx_segment,priority:1; index"`
	CropID  string `json:"cropId" gorm:"not null; uniqueIndex:idx_segment,priority:2; index"`
	ModelID string `json:"modelId" gorm:"not null; uniqueIndex:idx_segment,priority:3; index"`
	Date    string `json:"date" gorm:"not null; uniqueIndex:idx_segment,priority:4; index:,sort:asc"`

	// foreign keys
	Robot Robot `json:"robot"`

	// metrics fields
	// NOTE: If you add a new metric, also add its column to the `DoUpdates`
	// clause that populates these fields.
	AcresWeeded         float64 `json:"acresWeeded"`
	ActiveUptimeSeconds float64 `json:"activeUptimeSeconds"`
}

/**
 * CalculatedCertifiedMetric is a struct that represents the cached value of
 * a daily or job metric that is calculated from spatial data
 **/
type CalculatedCertifiedMetric struct {
	Base Model `json:"base" gorm:"embedded"`

	RobotID uint   `json:"robotId" gorm:"not null; index:idx_calculated_date,priority:1; index:idx_calculated_job,priority:1;"`
	Date    string `json:"date" gorm:"check:chk_date_or_jobid,(nullif(date,'') IS NULL) <> (nullif(job_id, '') IS NULL); index:idx_calculated_date,priority:2; index:,sort:asc"`
	JobID   string `json:"jobId" gorm:"index:idx_calculated_job,priority:2; index"`

	// foreign keys
	Robot Robot `json:"robot"`

	// metrics fields
	CalculationsVersion      uint    `json:"calculationsVersion"`
	OperatorEffectiveness    float64 `json:"operatorEffectiveness"`
	TargetWeedingTimeSeconds uint    `json:"targetWeedingTimeSeconds"`
	IsClosed                 bool    `json:"closed"`
}
