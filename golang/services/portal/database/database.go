package database

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/prometheus/client_golang/prometheus"
	pgxgeom "github.com/twpayne/pgx-geom"
	gormPostgres "gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/pkg/util/pgxmon"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

type Config struct {
	// Set Trace to enable tracing of every client acquire and release, as well
	// as every query. When set, OpenPostgresDB will return a non-nil
	// *TraceDumper on which you can call Dump at any time to get a snapshot of
	// the current traces.
	Trace bool
	// PoolMaxConns, when nonzero, sets the maximum capacity of the database
	// connection pool. If zero, the default behavior of pgxpool is used.
	PoolMaxConns int32
	// GormPrepareStmt controls the value of gorm.Config.PrepareStmt. When set,
	// Gorm will automatically build a shared cache of prepared statements for
	// SQL queries. Note that pgx, beneath Gorm, already prepares and caches
	// statements by default (see pgx.QueryExecMode), so this should usually be
	// superfluous.
	//
	// Warning: this has been observed to cause deadlocks while preparing
	// queries, causing the entire server to lock up indefinitely:
	// https://github.com/go-gorm/gorm/issues/7465
	GormPrepareStmt bool
	// DiscourageTransactions is a hint to the rest of the Portal application
	// to avoid using Gorm's transaction functionality. Application code can
	// test whether this was set by calling TransactionsDiscouraged on the
	// returned *gorm.DB.
	DiscourageTransactions bool
}

func OpenPostgresDB(ctx context.Context, connectString string, migrate bool, config *Config) (*gorm.DB, *TraceDumper, error) {
	poolConfig, err := pgxpool.ParseConfig(connectString)
	if err != nil {
		return nil, nil, fmt.Errorf("Failed to parse connection string: %w", err)
	}

	var traceDumper *TraceDumper
	if config.Trace {
		tracer := newTracer()
		poolConfig.ConnConfig.Tracer = tracer
		traceDumper = &TraceDumper{tracer: tracer}
	}
	if config.PoolMaxConns != 0 {
		poolConfig.MaxConns = config.PoolMaxConns
	}
	poolConfig.AfterConnect = func(ctx context.Context, conn *pgx.Conn) error {
		// Make sure PostGIS extensions are installed, so that we can
		// initialize a fresh database.
		if _, err := conn.Exec(ctx, "CREATE EXTENSION IF NOT EXISTS postgis"); err != nil {
			return err
		}

		// Register codecs so that we can encode/decode PostGIS geometry types.
		if err := pgxgeom.Register(ctx, conn); err != nil {
			return fmt.Errorf("Failed to register pgxgeom type codecs: %w", err)
		}
		return nil
	}

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("Failed to create connection pool: %w", err)
	}
	if err := prometheus.Register(pgxmon.NewPoolMetrics(pool)); err != nil {
		return nil, nil, fmt.Errorf("registering Prometheus metrics: %w", err)
	}

	dialector := gormPostgres.New(gormPostgres.Config{
		Conn: stdlib.OpenDBFromPool(pool),
	})
	db, err := gorm.Open(dialector, &gorm.Config{
		PrepareStmt: config.GormPrepareStmt,
		Logger:      utils.NewGormLogger(),
		Plugins: map[string]gorm.Plugin{
			discourageTransactionsName: discourageTransactionsPlugin{
				discourage: config.DiscourageTransactions,
			},
		},
	})
	if err != nil {
		return nil, nil, err
	}

	if err := db.SetupJoinTable(&models.Customer{}, "Robots", &models.CustomerRobot{}); err != nil {
		return nil, nil, err
	}
	if migrate {
		if err := db.AutoMigrate(
			&models.User{},
			&models.Upload{},
			&models.Customer{},
			&models.Report{},
			&models.ReportInstance{},
			&models.Robot{},
			&models.CustomerRobot{},
			&models.HealthLog{},
			&models.Alarm{},
			&models.Message{},
			&models.DailyMetric{},
			&models.Job{},
			&models.JobConfigDump{},
			&models.JobMetrics{},
			&models.AlarmLog{},
			&models.AlarmRobotAllowlist{},
			&models.AlarmAllowlist{},
			&models.AlarmBlocklist{},
			&models.Profile{},
			&models.LaserStats{},
			&models.CarbonProvidedProfile{},
			&models.SpatialMetrics{},
			&models.LaserStatsHistory{},
			&models.ModelInfo{},
			&models.RenameModelCommand{},
			&models.SetActiveProfileCommand{},
			&models.ModelHistoryEvent{},
			&models.ConfigUpgradeVersion{},
			&models.DataMigration{},
			&models.StreamMessage{},
			&models.FieldDefinition{},
			&models.SegmentedDailyMetric{},
			&models.FleetView{},
			&models.CalculatedCertifiedMetric{},
			&models.Farm{},
			&models.PointDefinition{},
			&models.Zone{},
			&models.Global{},
		); err != nil {
			return nil, nil, err
		}
	}

	return db, traceDumper, nil
}

type TraceDumper struct {
	tracer *pgxTracer
}

func (td *TraceDumper) Dump() []*TraceData {
	return td.tracer.dump()
}

const discourageTransactionsName = "portal/database.DiscourageTransactions"

type discourageTransactionsPlugin struct {
	discourage bool
}

func (discourageTransactionsPlugin) Name() string {
	return discourageTransactionsName
}

func (discourageTransactionsPlugin) Initialize(_ *gorm.DB) error {
	return nil
}

// TransactionsDiscouraged tests whether Config.DiscourageTransactions was set
// at database initialization.
func TransactionsDiscouraged(db *gorm.DB) bool {
	if plugin, ok := db.Config.Plugins[discourageTransactionsName].(discourageTransactionsPlugin); ok {
		return plugin.discourage
	}
	return false
}

// MaybeTransaction runs db.Transaction(fc) and returns its error, unless
// Config.DiscourageTransactions was set at database initialization. In that
// case, it just runs fc(db) and returns the error from that call.
func MaybeTransaction(db *gorm.DB, fc func(db *gorm.DB) error) error {
	if TransactionsDiscouraged(db) {
		return fc(db)
	}
	return db.Transaction(fc)
}
