package certified

import (
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/metrics/spatial"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/crgo/geo"
	"github.com/carbonrobotics/crgo/math"
	timelib "github.com/carbonrobotics/crgo/time"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
)

func printErrorIfAny(err error, key string) {
	if err != nil {
		log.WithError(err).Warnf("Failed to parse metric: %s", key)
	}
}

func recordDailyMetrics(db *gorm.DB, robot *models.Robot, date string, jobID string, metrics *portal.DailyMetrics) error {
	var metricsError error
	results := []models.DailyMetric{}
	db.Model(&models.DailyMetric{}).Where("robot_id = ? AND date = ?", robot.Base.ID, date).Find(&results)

	var dailyMetrics models.DailyMetric
	if len(results) > 1 {
		// if we have too many records for some reason, clean it up and try again
		db.Unscoped().Delete(&models.DailyMetric{}, "robot_id = ? AND date = ?", robot.Base.ID, date)
		dailyMetrics = models.DailyMetric{}
	} else if len(results) == 1 {
		// record exists, update it
		dailyMetrics = results[0]
	} else {
		// no record, create new
		dailyMetrics = models.DailyMetric{}
	}

	dailyMetrics.Serial = robot.Serial
	dailyMetrics.RobotID = robot.Base.ID
	dailyMetrics.CropID = robot.CropID
	dailyMetrics.Date = date
	if jobID != "" {
		dailyMetrics.JobID = jobID
	}

	// parse raw metrics
	parsed := ParseRawMetrics(metrics.Metrics)

	// intermediate variables
	weedingUptimeHours := SetMetricWeedingUptime(parsed)
	squareFeetWeeded := SetMetricSquareFeetWeeded(parsed)

	dailyMetrics.AcresWeeded = parsed.AcresWeeded
	dailyMetrics.AvgSpeedMPH = SetAverageSpeed(parsed, weedingUptimeHours)
	dailyMetrics.AvgCropSizeMM = parsed.AvgCropSizeMM
	dailyMetrics.AvgWeedSizeMM = parsed.AvgWeedSizeMM
	dailyMetrics.AvgTargetableReqLaserTime = parsed.AvgTargetableReqLaserTime
	dailyMetrics.AvgUntargetableReqLaserTime = parsed.AvgUntargetableReqLaserTime
	dailyMetrics.BandingConfigName = parsed.BandingConfigName
	dailyMetrics.BandingEnabled = parsed.BandingEnabled
	dailyMetrics.BandingPercentage = parsed.BandingPercentage
	dailyMetrics.CoverageSpeedAcresHr = SetCoverageSpeed(parsed, weedingUptimeHours)
	dailyMetrics.CropDensitySqFt = SetCropDensity(parsed, squareFeetWeeded)
	dailyMetrics.DistanceWeededMeters = parsed.DistanceWeededMeters
	dailyMetrics.EmbeddingsActiveUptimeSeconds = parsed.EmbeddingsActiveUptimeSeconds
	dailyMetrics.KeptCrops = parsed.KeptCrops
	dailyMetrics.KilledWeeds = parsed.KilledWeeds
	dailyMetrics.MissedCrops = parsed.MissedCrops
	dailyMetrics.MissedWeeds = parsed.MissedWeeds
	dailyMetrics.NotThinning = parsed.NotThinning
	dailyMetrics.NotWeedingWeeds = parsed.NotWeeding
	dailyMetrics.SkippedCrops = parsed.SkippedCrops
	dailyMetrics.SkippedWeeds = parsed.SkippedWeeds
	dailyMetrics.ThinnedCrops = parsed.ThinnedCrops
	dailyMetrics.ThinningEfficiency = SetThinningEfficiency(parsed)
	dailyMetrics.TimeEfficiency = SetTimeEfficiency(parsed)
	dailyMetrics.TotalCrops = parsed.TotalCrops
	dailyMetrics.TotalWeeds = parsed.TotalWeeds
	dailyMetrics.UptimeSeconds = parsed.UptimeSeconds
	dailyMetrics.ValidCrops = parsed.ValidCrops
	dailyMetrics.WeedDensitySqFt = SetWeedDensity(parsed, squareFeetWeeded)
	dailyMetrics.WeedingEfficiency = SetWeedingEfficiency(parsed)
	dailyMetrics.WeedingUptimeSeconds = parsed.WeedingUptimeSeconds
	dailyMetrics.WeedsTypeCountBroadleaf = parsed.WeedsTypeCountBroadleaf
	dailyMetrics.WeedsTypeCountGrass = parsed.WeedsTypeCountGrass
	dailyMetrics.WeedsTypeCountOffshoot = parsed.WeedsTypeCountOffshoot
	dailyMetrics.WeedsTypeCountPurslane = parsed.WeedsTypeCountPurslane

	if err := db.Save(&dailyMetrics).Error; err != nil {
		log.WithError(err).Error("Failed to save daily metrics")
		metricsError = err
	}

	parsedSegmented := ParseSegmentedDailyMetrics(metrics.Metrics)

	var segmentedDailyMetrics []models.SegmentedDailyMetric
	for _, metric := range parsedSegmented {
		metric.Date = date
		metric.RobotID = robot.Base.ID

		segmentedDailyMetrics = append(segmentedDailyMetrics, *metric)
	}

	if len(segmentedDailyMetrics) > 0 {
		// if date, robotId, cropId, and modelId are the same, update the record instead of creating a new one
		/**
		 * COMPOSITE KEYS USED AS UNIQUE INDEX
		 * date, robot_id, crop_id, and model_id are used as a composite key to uniquely
		 * identify a SegmentedDailyMetric. If changes are made to this insert, changes
		 * likely need to be reflected in the SegmentedDailyMetric model definition.
		**/
		err := db.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "date"}, {Name: "robot_id"}, {Name: "crop_id"}, {Name: "model_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"updated_at", "acres_weeded", "active_uptime_seconds"}),
		}).Create(&segmentedDailyMetrics).Error
		if err != nil {
			log.WithError(err).Error("Failed to save segmented daily metrics")
			metricsError = err
		}
	}

	return metricsError
}

// if you change how any of the metrics are calculated, increment this version number
// to make sure that they are recalculated next time they are requested
const calculationsVersion = 1

func calculateTargetWeedingTimeSeconds(blocks []*portal.BlockResponse) (uint, error) {
	targetWeedingTimes := []uint64{}
	for _, block := range blocks {
		if !spatial.IsCountableBlock(block) {
			continue
		}
		// we swallow these errors because we don't want to stop the calculation for one bad block
		distance := geo.Distance(block.Block.Start.Latitude, block.Block.Start.Longitude, block.Block.End.Latitude, block.Block.End.Longitude)
		targetSpeed, err := spatial.GetTargetSpeed(block)
		if err != nil {
			continue
		}
		targetWeedingTimes = append(targetWeedingTimes, uint64(distance/targetSpeed))
	}
	return uint(math.Sum(targetWeedingTimes)), nil
}

func calculateOperatorEffectiveness(blocks []*portal.BlockResponse) (float64, error) {
	targetSpeeds := []float64{}
	for _, block := range blocks {
		if !spatial.IsCountableBlock(block) {
			continue
		}
		// we swallow these errors because we don't want to stop the calculation for one bad block
		averageSpeed, err := spatial.GetAverageSpeed(block)
		if err != nil {
			continue
		}
		targetSpeed, err := spatial.GetTargetSpeed(block)
		if err != nil {
			continue
		}
		if averageSpeed < targetSpeed {
			targetSpeeds = append(targetSpeeds, averageSpeed/targetSpeed)
		} else {
			targetSpeeds = append(targetSpeeds, targetSpeed/averageSpeed)
		}
	}
	return math.Mean(targetSpeeds), nil
}

type getBlocksFunc func() ([]*models.SpatialMetrics, bool, error)
type compositeKey struct {
	RobotID uint
	Date    *string
	JobID   *string
}

func getOrCalculateMetrics(context *gin.Context, env *utils.Environment, database *gorm.DB, caches cache.Caches, key compositeKey, isClosed bool, blocksLastUpdated *int64, calculatedMetrics *models.CalculatedCertifiedMetric, getBlocks getBlocksFunc) (*models.CalculatedCertifiedMetric, error) {
	select {
	case <-context.Done():
		return nil, context.Err()
	default:
	}

	// if no metrics were ever written, return blank calculations
	if blocksLastUpdated == nil {
		return calculatedMetrics, nil
	}

	// check to make sure calculations are saved and using the latest version
	areCalculationsValid := calculatedMetrics.Base.ID != 0 && calculatedMetrics.CalculationsVersion == calculationsVersion
	canReuseCalculations := false
	if areCalculationsValid {
		// if we calculated the cached metrics based on a closed window, use them
		if calculatedMetrics.IsClosed {
			canReuseCalculations = true
		}

		// if the underlying metrics haven't changed, use them
		if *blocksLastUpdated <= calculatedMetrics.Base.UpdatedAt {
			canReuseCalculations = true
		}
	}
	if canReuseCalculations {
		return calculatedMetrics, nil
	}

	// mark the calculations as closed if the request window is closed
	calculatedMetrics.IsClosed = isClosed

	// set the calculations version
	calculatedMetrics.CalculationsVersion = calculationsVersion

	// set the composite key
	calculatedMetrics.RobotID = key.RobotID
	if key.Date != nil && key.JobID != nil {
		return nil, errors.New("cannot set both date and job_id")
	}
	if key.Date != nil {
		calculatedMetrics.Date = *key.Date
	}
	if key.JobID != nil {
		calculatedMetrics.JobID = *key.JobID
	}

	// metrics have changed or calculations are invalid. get blocks and recalculate
	blocks, queryLimitReached, err := getBlocks()
	if err != nil {
		return nil, err
	}
	if queryLimitReached {
		return nil, errors.New("cannot calculate metrics, query limit reached")
	}

	serializedBlocks := make([]*portal.BlockResponse, len(blocks))
	for i, block := range blocks {
		serializedBlocks[i] = spatial.SerializeBlock(context, caches, block)
	}

	targetWeedingTimeSeconds, err := calculateTargetWeedingTimeSeconds(serializedBlocks)
	if err == nil {
		calculatedMetrics.TargetWeedingTimeSeconds = targetWeedingTimeSeconds
	} else {
		log.WithError(err).Warn("Failed to calculate target weeding time")
	}

	operatorEffectiveness, err := calculateOperatorEffectiveness(serializedBlocks)
	if err == nil {
		calculatedMetrics.OperatorEffectiveness = operatorEffectiveness
	} else {
		log.WithError(err).Warn("Failed to calculate operator effectiveness")
	}

	if env.DBReadOnly() {
		log.Warnf("Updated calculated certified metrics, but skipping due to read-only database: key %+v", key)
	} else {
		// save results, even if request window is open
		err = database.Save(calculatedMetrics).Error
		if err != nil {
			log.WithError(err).Warn("Failed to save calculated metrics")
		}
	}

	return calculatedMetrics, nil
}

func getOrCalculateMetricsByDate(context *gin.Context, env *utils.Environment, database *gorm.DB, caches cache.Caches, robotID uint, date time.Time, blocksLastUpdated *int64) (*models.CalculatedCertifiedMetric, error) {
	dateString := timelib.ToDateSlug(date)
	calculatedMetrics := &models.CalculatedCertifiedMetric{}
	err := database.WithContext(context).Where("robot_id = ? AND date = ?", robotID, dateString).First(calculatedMetrics).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	clientTime := context.GetTime(timelib.ClientTimeKey)
	isClosed := timelib.ToDateSlug(clientTime) != dateString
	compositeKey := compositeKey{
		RobotID: robotID,
		Date:    &dateString,
	}

	serial, err := caches.RobotSerialByIDCache.Get(context, robotID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get serial for robot %d", robotID)
	}

	getBlocksFunc := func() ([]*models.SpatialMetrics, bool, error) {
		return spatial.GetBlocksByDate(context, database, robotID, serial, date, false)
	}

	return getOrCalculateMetrics(context, env, database, caches, compositeKey, isClosed, blocksLastUpdated, calculatedMetrics, getBlocksFunc)
}

func getOrCalculateMetricsByJob(context *gin.Context, env *utils.Environment, database *gorm.DB, caches cache.Caches, robotID uint, jobID string, completed bool, blocksLastUpdated *int64) (*models.CalculatedCertifiedMetric, error) {
	calculatedMetrics := &models.CalculatedCertifiedMetric{}
	// check if we already have the calculations
	result := database.WithContext(context).Where("robot_id = ? AND job_id = ?", robotID, jobID).First(calculatedMetrics)
	if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, result.Error
	}

	compositeKey := compositeKey{
		RobotID: robotID,
		JobID:   &jobID,
	}

	serial, err := caches.RobotSerialByIDCache.Get(context, robotID)
	if err != nil {
		log.WithError(err).Errorf("Failed to get serial for robot %d", robotID)
	}

	getBlocks := func() ([]*models.SpatialMetrics, bool, error) {
		return spatial.GetBlocksByJob(context, database, robotID, serial, jobID)
	}

	return getOrCalculateMetrics(context, env, database, caches, compositeKey, completed, blocksLastUpdated, calculatedMetrics, getBlocks)
}
