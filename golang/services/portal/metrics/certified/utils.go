package certified

import (
	"context"
	"fmt"
	"maps"
	"math"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	geoutil "github.com/carbonrobotics/cloud/golang/pkg/util"
	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/metrics/spatial"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	timeutils "github.com/carbonrobotics/crgo/time"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
)

type CropModelIDPair struct {
	CropID  string
	ModelID string
}

type ParsedMetrics struct {
	AcresWeeded                   float64
	AvgCropSizeMM                 float64
	AvgWeedSizeMM                 float64
	AvgTargetableReqLaserTime     int64
	AvgUntargetableReqLaserTime   int64
	BandingConfigName             string
	BandingEnabled                bool
	BandingPercentage             float64
	DistanceWeededMeters          float64
	EmbeddingsActiveUptimeSeconds float64
	KeptCrops                     int64
	KilledWeeds                   int64
	MissedCrops                   int64
	MissedWeeds                   int64
	NotThinning                   int64
	NotWeeding                    int64
	SkippedCrops                  int64
	SkippedWeeds                  int64
	ThinnedCrops                  int64
	TotalCrops                    int64
	TotalWeeds                    int64
	UptimeSeconds                 float64
	ValidCrops                    int64
	WeedingUptimeSeconds          float64
	WeedsTypeCountBroadleaf       int64
	WeedsTypeCountGrass           int64
	WeedsTypeCountOffshoot        int64
	WeedsTypeCountPurslane        int64
}

func ParseRawMetrics(m map[string]string) *ParsedMetrics {
	metrics := &ParsedMetrics{}
	var err error
	metrics.AcresWeeded, err = strconv.ParseFloat(m[string(models.ACRES_WEEDED)], 64)
	printErrorIfAny(err, string(models.ACRES_WEEDED))
	if math.IsNaN(metrics.AcresWeeded) {
		metrics.AcresWeeded = 0
	}
	metrics.AvgWeedSizeMM, err = strconv.ParseFloat(m[string(models.AVG_WEED_SIZE_MM)], 64)
	printErrorIfAny(err, string(models.AVG_WEED_SIZE_MM))
	if math.IsNaN(metrics.AcresWeeded) {
		metrics.AvgWeedSizeMM = 0
	}
	metrics.AvgCropSizeMM, err = strconv.ParseFloat(m[string(models.AVG_CROP_SIZE_MM)], 64)
	printErrorIfAny(err, string(models.AVG_CROP_SIZE_MM))
	if math.IsNaN(metrics.AvgCropSizeMM) {
		metrics.AvgCropSizeMM = 0
	}
	avgTargetableReqLaserTime, err := strconv.ParseFloat(m[string(models.AVG_TARGETABLE_REQ_LASER_TIME)], 64)
	printErrorIfAny(err, string(models.AVG_TARGETABLE_REQ_LASER_TIME))
	metrics.AvgTargetableReqLaserTime = int64(avgTargetableReqLaserTime)
	avgUntargetableReqLaserTime, err := strconv.ParseFloat(m[string(models.AVG_UNTARGETABLE_REQ_LASER_TIME)], 64)
	printErrorIfAny(err, string(models.AVG_UNTARGETABLE_REQ_LASER_TIME))
	metrics.AvgUntargetableReqLaserTime = int64(avgUntargetableReqLaserTime)
	metrics.BandingConfigName = m[string(models.BANDING_CONFIG_NAME)]
	metrics.BandingEnabled = m[string(models.BANDING_ENABLED)] == "True"
	metrics.BandingPercentage, err = strconv.ParseFloat(m[string(models.BANDING_PERCENTAGE)], 64)
	printErrorIfAny(err, string(models.BANDING_PERCENTAGE))
	if math.IsNaN(metrics.BandingPercentage) {
		metrics.BandingPercentage = 0
	}
	metrics.DistanceWeededMeters, err = strconv.ParseFloat(m[string(models.DISTANCE_WEEDED)], 64)
	printErrorIfAny(err, string(models.DISTANCE_WEEDED))
	if math.IsNaN(metrics.DistanceWeededMeters) {
		metrics.DistanceWeededMeters = 0
	}
	metrics.KeptCrops, err = strconv.ParseInt(m[string(models.KEPT_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.KEPT_CROPS))
	metrics.KilledWeeds, err = strconv.ParseInt(m[string(models.KILLED_WEEDS)], 10, 64)
	printErrorIfAny(err, string(models.KILLED_WEEDS))
	metrics.MissedCrops, err = strconv.ParseInt(m[string(models.MISSED_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.MISSED_CROPS))
	metrics.MissedWeeds, err = strconv.ParseInt(m[string(models.MISSED_WEEDS)], 10, 64)
	printErrorIfAny(err, string(models.MISSED_WEEDS))
	metrics.NotThinning, err = strconv.ParseInt(m[string(models.NOT_THINNING)], 10, 64)
	printErrorIfAny(err, string(models.NOT_THINNING))
	metrics.NotWeeding, err = strconv.ParseInt(m[string(models.NOT_WEEDING)], 10, 64)
	printErrorIfAny(err, string(models.NOT_WEEDING))
	metrics.SkippedCrops, err = strconv.ParseInt(m[string(models.SKIPPED_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.SKIPPED_CROPS))
	metrics.SkippedWeeds, err = strconv.ParseInt(m[string(models.SKIPPED_WEEDS)], 10, 64)
	printErrorIfAny(err, string(models.SKIPPED_WEEDS))
	metrics.ThinnedCrops, err = strconv.ParseInt(m[string(models.THINNED_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.THINNED_CROPS))
	metrics.TotalCrops, err = strconv.ParseInt(m[string(models.TOTAL_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.TOTAL_CROPS))
	metrics.TotalWeeds, err = strconv.ParseInt(m[string(models.TOTAL_WEEDS)], 10, 64)
	printErrorIfAny(err, string(models.TOTAL_WEEDS))
	metrics.UptimeSeconds, err = strconv.ParseFloat(m[string(models.UPTIME_SECONDS)], 64)
	printErrorIfAny(err, string(models.UPTIME_SECONDS))
	if math.IsNaN(metrics.UptimeSeconds) {
		metrics.UptimeSeconds = 0
	}
	metrics.ValidCrops, err = strconv.ParseInt(m[string(models.VALID_CROPS)], 10, 64)
	printErrorIfAny(err, string(models.VALID_CROPS))
	metrics.WeedingUptimeSeconds, err = strconv.ParseFloat(m[string(models.WEEDING_UPTIME_SECONDS)], 64)
	printErrorIfAny(err, string(models.WEEDING_UPTIME_SECONDS))
	if math.IsNaN(metrics.WeedingUptimeSeconds) {
		metrics.WeedingUptimeSeconds = 0
	}
	metrics.EmbeddingsActiveUptimeSeconds, err = strconv.ParseFloat(m[string(models.EMBEDDINGS_ACTIVE_UPTIME_SECONDS)], 64)
	printErrorIfAny(err, string(models.EMBEDDINGS_ACTIVE_UPTIME_SECONDS))
	if math.IsNaN(metrics.EmbeddingsActiveUptimeSeconds) {
		metrics.EmbeddingsActiveUptimeSeconds = 0
	}
	metrics.WeedsTypeCountBroadleaf, err = strconv.ParseInt(m[string(models.WEEDS_TYPE_COUNT_BROADLEAF)], 10, 64)
	printErrorIfAny(err, string(models.WEEDS_TYPE_COUNT_BROADLEAF))
	metrics.WeedsTypeCountGrass, err = strconv.ParseInt(m[string(models.WEEDS_TYPE_COUNT_GRASS)], 10, 64)
	printErrorIfAny(err, string(models.WEEDS_TYPE_COUNT_GRASS))
	metrics.WeedsTypeCountOffshoot, err = strconv.ParseInt(m[string(models.WEEDS_TYPE_COUNT_OFFSHOOT)], 10, 64)
	printErrorIfAny(err, string(models.WEEDS_TYPE_COUNT_OFFSHOOT))
	metrics.WeedsTypeCountPurslane, err = strconv.ParseInt(m[string(models.WEEDS_TYPE_COUNT_PURSLANE)], 10, 64)
	printErrorIfAny(err, string(models.WEEDS_TYPE_COUNT_PURSLANE))
	return metrics
}

func ParseSegmentedDailyMetrics(m map[string]string) map[CropModelIDPair]*models.SegmentedDailyMetric {
	segmentedDailyMetrics := make(map[CropModelIDPair]*models.SegmentedDailyMetric)
	for key, value := range m {
		metricName, segment, err := ParseSegmentedMetricKey(key)
		if metricName == "" || err != nil {
			// silently ignore invalid metrics
			// all standard DailyMetrics will be ignored
			continue
		}

		switch metricName {
		case models.ACRES_WEEDED_BY_MODEL:
			acresWeeded, err := strconv.ParseFloat(value, 64)
			if err != nil {
				printErrorIfAny(err, key)
				continue
			}

			_, ok := segmentedDailyMetrics[segment]
			if !ok {
				segmentedDailyMetrics[segment] = &models.SegmentedDailyMetric{CropID: segment.CropID, ModelID: segment.ModelID}
				// segmentedDailyMetrics[segment] = segMetrics
			}
			segmentedDailyMetrics[segment].AcresWeeded = acresWeeded
		case models.ACTIVE_UPTIME_BY_MODEL:
			activeUptimeSeconds, err := strconv.ParseFloat(value, 64)
			if err != nil {
				printErrorIfAny(err, key)
				continue
			}
			_, ok := segmentedDailyMetrics[segment]
			if !ok {
				segmentedDailyMetrics[segment] = &models.SegmentedDailyMetric{CropID: segment.CropID, ModelID: segment.ModelID}
				// segmentedDailyMetrics[segment] = segMetrics
			}
			segmentedDailyMetrics[segment].ActiveUptimeSeconds = activeUptimeSeconds
		default:
			log.Warnf("Unrecognized segmented metric key: %s", metricName)
			continue
		}
	}

	return segmentedDailyMetrics
}

func ParseSegmentedMetricKey(key string) (metricName models.SegmentedDailyMetricName, segment CropModelIDPair, err error) {
	// these metrics are present in the form
	// key: "acres_weeded:<crop_id>:<model_id>"
	// value: "<acres_weeded>"
	// key: "active_use:<crop_id>:<model_id>"
	// value: "<active_uptime_seconds>"
	parts := strings.Split(key, ":")
	if len(parts) != 3 {
		return "", CropModelIDPair{}, fmt.Errorf("invalid segmented metric: %s", key)
	}

	switch parts[0] {
	case string(models.ACRES_WEEDED_BY_MODEL):
		return models.ACRES_WEEDED_BY_MODEL, CropModelIDPair{
			CropID:  parts[1],
			ModelID: parts[2],
		}, nil
	case string(models.ACTIVE_UPTIME_BY_MODEL):
		return models.ACTIVE_UPTIME_BY_MODEL, CropModelIDPair{
			CropID:  parts[1],
			ModelID: parts[2],
		}, nil
	default:
		return "", CropModelIDPair{}, fmt.Errorf("invalid segmented metric: %s", key)
	}
}

func SetMetricWeedingUptime(parsed *ParsedMetrics) float64 {
	return parsed.WeedingUptimeSeconds / timeutils.SEC_IN_HR
}

func SetMetricSquareFeetWeeded(parsed *ParsedMetrics) float64 {
	return parsed.AcresWeeded * geoutil.SQFT_IN_ACRE
}

func SetAverageSpeed(parsed *ParsedMetrics, weedingUptimeHours float64) float64 {
	if parsed.WeedingUptimeSeconds > 0 {
		if parsed.DistanceWeededMeters > 0 {
			// use distance if we have it
			miles := parsed.DistanceWeededMeters / geoutil.METERS_IN_MILE
			return miles / weedingUptimeHours
		} else if parsed.AcresWeeded > 0 {
			// use acres otherwise
			acresPerHour := parsed.AcresWeeded / weedingUptimeHours
			return (acresPerHour * 43560) / 5280 / 20
		}
	}
	return 0
}

func SetCoverageSpeed(parsed *ParsedMetrics, weedingUptimeHours float64) float64 {
	if parsed.AcresWeeded > 0 && parsed.WeedingUptimeSeconds > 0 {
		return (parsed.AcresWeeded / weedingUptimeHours)
	}
	return 0
}

func SetTimeEfficiency(parsed *ParsedMetrics) float64 {
	if parsed.WeedingUptimeSeconds > 0 && parsed.UptimeSeconds > 0 {
		return (parsed.WeedingUptimeSeconds / parsed.UptimeSeconds)
	}
	return 0
}

func SetWeedDensity(parsed *ParsedMetrics, squareFeetWeeded float64) float64 {
	if parsed.TotalWeeds > 0 && squareFeetWeeded > 0 {
		return (float64(parsed.TotalWeeds) / float64(squareFeetWeeded))
	}
	return 0
}

func SetCropDensity(parsed *ParsedMetrics, squareFeetWeeded float64) float64 {
	if squareFeetWeeded == 0 {
		return 0
	}
	return float64(parsed.ValidCrops) / squareFeetWeeded
}

func SetWeedingEfficiency(parsed *ParsedMetrics) float64 {
	if parsed.KilledWeeds > 0 {
		return (float64(parsed.KilledWeeds) / float64(parsed.KilledWeeds+parsed.MissedWeeds))
	}
	return 0
}

func SetThinningEfficiency(parsed *ParsedMetrics) float64 {
	if parsed.ThinnedCrops > 0 {
		return (float64(parsed.ThinnedCrops+parsed.KeptCrops) / float64(parsed.ThinnedCrops+parsed.KeptCrops+parsed.MissedCrops))
	}
	return 0
}

func FillCalculatedDailyMetrics(context *gin.Context, env *utils.Environment, database *gorm.DB, caches cache.Caches, metrics []*models.DailyMetric) ([]*portal.DailyMetricResponse, error) {
	parsedDates := make([]*time.Time, len(metrics))
	var lastUpdateQueries []spatial.RobotDate
	for i, metric := range metrics {
		date, err := time.Parse("2006-01-02", metric.Date)
		if err != nil {
			log.WithFields(log.Fields{
				"metricID": metric.Base.ID,
				"robotID":  metric.RobotID,
				"date":     metric.Date,
			}).WithError(err).Warn("failed to parse date in daily metric")
			continue
		}
		parsedDates[i] = &date
		query := spatial.RobotDate{RobotID: metric.RobotID, Date: date}
		lastUpdateQueries = append(lastUpdateQueries, query)
	}
	lastUpdates, err := spatial.GetLastUpdatesByDate(context, database, lastUpdateQueries)
	if err != nil {
		return nil, err
	}

	dailyMetricSink := make(chan *portal.DailyMetricResponse)
	dailyMetricListChan := make(chan []*portal.DailyMetricResponse)
	go func() {
		var dailyMetricList []*portal.DailyMetricResponse
		for metric := range dailyMetricSink {
			dailyMetricList = append(dailyMetricList, metric)
		}
		dailyMetricListChan <- dailyMetricList
	}()

	jobIDs := make(map[string]struct{})
	for _, metric := range metrics {
		id := metric.JobID
		if id != "" {
			jobIDs[id] = struct{}{}
		}
	}
	jobsQueryStart := time.Now()
	allJobs, err := getJobNamesByID(context, database, slices.Collect(maps.Keys(jobIDs)))
	jobsQueryDuration := time.Since(jobsQueryStart)
	if err != nil {
		log.WithError(err).Warn("Failed to fill job names into daily metrics")
	}
	jobNamesByID := make(map[string]string, len(allJobs))
	for _, job := range allJobs {
		jobNamesByID[job.JobId] = job.Name
	}

	fillCalculatedDailyMetricsJobQueriesTotal.Inc()
	fillCalculatedDailyMetricsJobQuerySecondsTotal.Add(jobsQueryDuration.Seconds())
	fillCalculatedDailyMetricsJobsRequestedTotal.Add(float64(len(jobIDs)))
	fillCalculatedDailyMetricsJobsRetrievedTotal.Add(float64(len(allJobs)))

	// This can likely be raised after initial rollout. Currently there is no
	// limit on the number of days a customer can query for.
	maxGoroutines := 30
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for metricIndex, metric := range metrics {
		select {
		case <-context.Done():
			return nil, context.Err()
		default:
		}

		semaphore <- struct{}{}
		wg.Add(1)
		go func(report *models.DailyMetric) {
			defer wg.Done()
			defer func() { <-semaphore }()

			dailyMetrics := SerializeDailyMetric(context, caches, jobNamesByID, metric)
			date := parsedDates[metricIndex]
			if date != nil {
				var lastUpdatedAt *int64
				if update, ok := lastUpdates[spatial.RobotDate{RobotID: metric.RobotID, Date: *date}]; ok {
					lastUpdatedAt = &update
				}
				calculatedMetrics, err := getOrCalculateMetricsByDate(context, env, database, caches, metric.RobotID, *date, lastUpdatedAt)
				// if we have calculations, add them
				if err == nil && calculatedMetrics != nil {
					dailyMetrics.OperatorEffectiveness = float32(calculatedMetrics.OperatorEffectiveness)
					dailyMetrics.TargetWeedingTimeSeconds = int64(calculatedMetrics.TargetWeedingTimeSeconds)
				}
			}

			dailyMetricSink <- dailyMetrics
		}(metric)
	}
	wg.Wait()
	close(dailyMetricSink)

	return <-dailyMetricListChan, nil
}

// getJobNamesByID looks up many jobs by their UUIDs, returning all matches and
// ignoring any IDs that do not correspond to a job. The resulting slice may
// not have the same length as `ids` and is in no particular order.
//
// This would more naturally be in the `jobs` package, but that would create an
// import cycle.
func getJobNamesByID(ctx context.Context, db *gorm.DB, ids []string) ([]*models.Job, error) {
	var jobs []*models.Job
	err := db.WithContext(ctx).
		Where("job_id = ANY(?)", pq.StringArray(ids)).
		Select("job_id", "name").
		Find(&jobs).
		Error
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

func FillCalculatedJobMetrics(context *gin.Context, env *utils.Environment, database *gorm.DB, caches cache.Caches, jobs []*portal.PortalJob) ([]*portal.PortalJob, error) {
	jobKey := func(job *portal.PortalJob) spatial.RobotJobID {
		return spatial.RobotJobID{RobotID: uint(job.RobotId), JobID: job.JobId}
	}
	lastUpdateQueries := make([]spatial.RobotJobID, len(jobs))
	for i, job := range jobs {
		lastUpdateQueries[i] = jobKey(job)
	}
	lastUpdates, err := spatial.GetLastUpdatesByJob(context, database, lastUpdateQueries)
	if err != nil {
		return nil, err
	}

	// Most customers have between 1 and 30 jobs. A handfull have more than
	// 200, and a few have over 300.
	maxGoroutines := 30
	semaphore := make(chan struct{}, maxGoroutines)
	var wg sync.WaitGroup
	for index, job := range jobs {
		select {
		case <-context.Done():
			return nil, context.Err()
		default:
		}

		semaphore <- struct{}{}
		wg.Add(1)
		go func(index int, report *portal.PortalJob) {
			defer wg.Done()
			defer func() { <-semaphore }()
			var lastUpdatedAt *int64
			if update, ok := lastUpdates[jobKey(job)]; ok {
				lastUpdatedAt = &update
			}
			calculatedMetrics, err := getOrCalculateMetricsByJob(context, env, database, caches, uint(job.RobotId), job.JobId, job.Completed, lastUpdatedAt)
			// if we have calculations, add them
			if err == nil && calculatedMetrics != nil {
				if job.Metrics == nil {
					job.Metrics = &portal.DailyMetricResponse{}
				}
				job.Metrics.OperatorEffectiveness = float32(calculatedMetrics.OperatorEffectiveness)
				job.Metrics.TargetWeedingTimeSeconds = int64(calculatedMetrics.TargetWeedingTimeSeconds)
			}
		}(index, job)
	}
	wg.Wait()

	return jobs, nil
}
