package certified

import (
	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/crops"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
)

func SerializeDailyMetric(ctx *gin.Context, caches cache.Caches, jobNamesByID map[string]string, dailyMetric *models.DailyMetric) *portal.DailyMetricResponse {
	if dailyMetric == nil {
		return &portal.DailyMetricResponse{}
	}

	response := &portal.DailyMetricResponse{
		Db:      models.ModelToPortalDB(dailyMetric.Base),
		Date:    dailyMetric.Date,
		RobotId: uint64(dailyMetric.RobotID),
		Serial:  dailyMetric.Serial,

		CropId:  dailyMetric.CropID,
		Crop:    crops.CropIDToCommonName(ctx, caches, dailyMetric.CropID),
		JobId:   dailyMetric.JobID,
		JobName: jobNamesByID[dailyMetric.JobID],

		AcresWeeded:                   utils.FloatOr0(dailyMetric.AcresWeeded),
		AvgSpeedMph:                   utils.FloatOr0(dailyMetric.AvgSpeedMPH),
		AvgWeedSizeMm:                 utils.FloatOr0(dailyMetric.AvgWeedSizeMM),
		BandingConfigName:             dailyMetric.BandingConfigName,
		BandingEnabled:                dailyMetric.BandingEnabled,
		BandingPercentage:             utils.FloatOr0(dailyMetric.BandingPercentage),
		CoverageSpeedAcresHr:          utils.FloatOr0(dailyMetric.CoverageSpeedAcresHr),
		DistanceWeededMeters:          utils.FloatOr0(dailyMetric.DistanceWeededMeters),
		EmbeddingsActiveUptimeSeconds: utils.FloatOr0(dailyMetric.EmbeddingsActiveUptimeSeconds),
		KeptCrops:                     dailyMetric.KeptCrops,
		KilledWeeds:                   dailyMetric.KilledWeeds,
		MissedCrops:                   dailyMetric.MissedCrops,
		MissedWeeds:                   dailyMetric.MissedWeeds,
		NotThinning:                   dailyMetric.NotThinning,
		NotWeeding:                    dailyMetric.NotWeeding,
		NotWeedingWeeds:               dailyMetric.NotWeedingWeeds,
		SkippedCrops:                  dailyMetric.SkippedCrops,
		SkippedWeeds:                  dailyMetric.SkippedWeeds,
		ThinnedCrops:                  dailyMetric.ThinnedCrops,
		ThinningEfficiency:            utils.FloatOr0(dailyMetric.ThinningEfficiency),
		TimeEfficiency:                utils.FloatOr0(dailyMetric.TimeEfficiency),
		TotalCrops:                    dailyMetric.TotalCrops,
		TotalWeeds:                    dailyMetric.TotalWeeds,
		UptimeSeconds:                 utils.FloatOr0(dailyMetric.UptimeSeconds),
		ValidCrops:                    dailyMetric.ValidCrops,
		WeedDensitySqFt:               utils.FloatOr0(dailyMetric.WeedDensitySqFt),
		WeedingEfficiency:             utils.FloatOr0(dailyMetric.WeedingEfficiency),
		WeedingUptimeSeconds:          utils.FloatOr0(dailyMetric.WeedingUptimeSeconds),
		WeedsTypeCountBroadleaf:       dailyMetric.WeedsTypeCountBroadleaf,
		WeedsTypeCountGrass:           dailyMetric.WeedsTypeCountGrass,
		WeedsTypeCountOffshoot:        dailyMetric.WeedsTypeCountOffshoot,
		WeedsTypeCountPurslane:        dailyMetric.WeedsTypeCountPurslane,
		CropDensitySqFt:               utils.FloatOr0(dailyMetric.CropDensitySqFt),
		AvgCropSizeMm:                 utils.FloatOr0(dailyMetric.AvgCropSizeMM),
		AvgTargetableReqLaserTime:     dailyMetric.AvgTargetableReqLaserTime,
		AvgUntargetableReqLaserTime:   dailyMetric.AvgUntargetableReqLaserTime,
	}

	return response
}
