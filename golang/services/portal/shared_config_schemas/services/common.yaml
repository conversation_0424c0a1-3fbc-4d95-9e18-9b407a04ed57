type: "node"
children:
  portal_host:
    type: "string"
    complexity: "expert"
    hint: "Deprecated: [2023-09-13] use carbon_host_overrides in robot.json"
  p2p:
    type: "node"
    children:
      model_id:
        type: "string"
        default_recommended: false
      capture_cache_interval_seconds:
        type: "int"
        default: 5
        units: "seconds"
  furrows:
    default_recommended: false
    type: "node"
    children:
      model_path:
        type: "string"
  cv_runtime:
    type: "node"
    children:
      gpu_assignment_scheme:
        type: string
        hint: "How to assign cameras to gpus and consequently sets predict/target framerate - options: (int8)"
      gpu_assignment_override:
        type: "list"
        item:
          type: "string"
          hint: "GPU assignment override. Name of node is number of gpus and string is json like: {'target1': 0, 'target2': 1}"
      async_grpc_server:
        type: "bool"
        default: false
        complexity: "developer"
        hint: "Enable async grpc server for cv runtime, requires reload"
  deepweed:
    type: "node"
    children:
      model_id:
        type: "string"
        default_recommended: false
      weed_point_threshold_image_scoring:
        type: "float"
        default: 0.05
        complexity: "developer"
      crop_point_threshold_image_scoring:
        type: "float"
        default: 0.05
        complexity: "developer"
      plant_point_threshold_image_scoring:
        type: "float"
        default: 0.05
        complexity: "developer"
      weed_point_threshold:
        type: "float"
        default: 0.5
        complexity: "developer"
      crop_point_threshold:
        type: "float"
        default: 0.5
        complexity: "developer"
      plant_point_threshold:
        type: "float"
        default: 0.5
        complexity: "developer"
      weed_point_threshold_diagnostics:
        type: "float"
        default: 0.1
        complexity: "developer"
      crop_point_threshold_diagnostics:
        type: "float"
        default: 0.1
        complexity: "developer"
      plant_point_threshold_diagnostics:
        type: "float"
        default: 0.1
        complexity: "developer"
      only_plant_points:
        type: "bool"
        default: false
        complexity: "developer"
        hint: "Deprecated: [v2.1], always true"
      pointCategoriesLegacy:
        type: "list"
        complexity: "developer"
        item:
          type: "node"
          children:
            threshold:
              type: "float"
              default: 0.5
            enabled:
              type: "bool"
              default: true
      segmentationCategories:
        type: "list"
        complexity: "developer"
        item:
          type: "node"
          children:
            threshold:
              type: "float"
              default: 0.5
            safety_radius_in:
              type: "float"
              default: 1.0
            enabled:
              type: "bool"
              default: true
  environment:
    type: "string"
    default: "development"
  use_experimental_models:
    type: bool
    default: false
    complexity: "developer"
    hint: "Deprecated: [2022-12-04] use experimental_flag_list"
  experimental_flag_list:
    type: "list"
    complexity: "developer"
    item:
      type: "string"
  software_manager:
    include: "services/software_manager.yaml"
  use_treadkill:
    type: "bool"
    default: false
    complexity: "developer"
  use_controls_weed_tracking:
    type: "bool"
    default: false
    complexity: "developer"
  exposure_update_interval_minutes:
    type: "int"
    default: 2
    units: "minutes"
    complexity: "developer"
  wheel_encoders:
    type: "node"
    children:
      non_zero_delta_mm:
        type: "float"
        default_recommended: true
        default: 10.0
        units: "mm"
        hint: "How far an encoder has to move for anomaly detection."
      max_allowable_percent_off:
        type: "float"
        default_recommended: true
        default: 0.05
        units: "%"
        hint: "How different in percent an encoder has to be to indicate an error for anomaly detection."
      anomaly_detection_enabled:
        type: "bool"
        default_recommended: true
        default: true
        hint: "Try to detect bad wheel encoders and automatically recover if possible."
      anomaly_detection_count:
        type: "uint"
        default_recommended: true
        default: 15
        hint: "How many consecutive errors must occur before taking action."
      anomaly_detection_delta_time:
        type: "uint"
        default_recommended: true
        default: 10000
        units: "ms"
        hint: "How long to average over"
      non_zero_vel:
        type: "float"
        default_recommended: true
        default: .01
        units: "m/s"
        hint: "How fast we must move to indicate we are not standing still"
      anomaly_detection_span:
        type: "uint"
        default_recommended: true
        default: 60000
        hint: "How long an error must be detected for to alarm"
        units: "ms"
      back_left:
        type: "node"
        children:
          diameter_in:
            default_recommended: false
            type: "float"
            default: 32.391739672
            units: "inches"
          enabled:
            type: "bool"
            default: false
      back_right:
        type: "node"
        children:
          diameter_in:
            default_recommended: false
            type: "float"
            default: 32.391739672
            units: "inches"
          enabled:
            type: "bool"
            default: false
      front_left:
        type: "node"
        children:
          diameter_in:
            default_recommended: false
            type: "float"
            default: 32.391739672
            units: "inches"
          enabled:
            type: "bool"
            default: true
      front_right:
        type: "node"
        children:
          diameter_in:
            default_recommended: false
            type: "float"
            default: 32.391739672
            units: "inches"
          enabled:
            type: "bool"
            default: true
      use_max_value:
        type: "bool"
        default_recommended: true
        default: false
        hint: "Use the max value of the encoders instead of the average"
  almanac:
    include: "components/almanac.yaml"
  global_scheduler:
    include: "components/global_scheduler.yaml"
  temp_humidity_bypass:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Deprecated: [2022-07-08] DO NOT USE"
  disabled_rows:
    type: "list"
    item:
      type: "int"
  row_width_in:
    type: "float"
    default: 80
    units: "inches"
  weeding_metrics:
    type: "node"
    children:
      area_persist_interval_sec:
        type: "uint"
        default: 60
        units: "seconds"
      area_update_interval:
        type: "uint"
        default: 5
        units: "seconds"
      kill_count_persist_interval_sec:
        type: "uint"
        default: 1
        units: "seconds"
      laser_time_persist_interval_sec:
        type: "uint"
        default: 1
        units: "seconds"
      power_on_persist_interval_sec:
        type: "uint"
        default: 60
        units: "seconds"
      enable_speculative_grid_metrics:
        type: "bool"
        default: false
        default_recommended: true
        hint: "Enable creating grafana metrics for speculative grid data. This is a lot of data so should be enabled only if needed."
  static_banding_v2_enabled:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Deprecated: [2022-12-07] permanently enabled"
  dynamic_banding_enabled:
    type: "bool"
    default: false
    complexity: "developer"
  crop_line_detection:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        complexity: "developer"
      work_interval_ms:
        type: "uint"
        default: 200
        complexity: "developer"
        units: "ms"
      num_lines:
        type: "uint"
        default: 8
        complexity: "developer"
      row_padding_mm:
        type: "float"
        default: 5.0
        complexity: "developer"
        units: "mm"
      min_items_in_crop_line:
        type: "uint"
        default: 3
        complexity: "developer"
      default_crop_radius_mm:
        type: "float"
        default: 1.0
        units: "mm"
        complexity: "developer"
      smoothing_factor:
        type: "float"
        default: 0.9
        complexity: "developer"
      use_clamping:
        type: "bool"
        default: true
      clamping_distance_mm:
        type: "uint"
        default: 200
        units: "mm"
      search_radius_initial_mm:
        type: "uint"
        default: 10
        units: "mm"
      search_radius_increment_mm:
        type: "uint"
        default: 10
        units: "mm"
      search_radius_max_mm:
        type: "uint"
        default: 50
        units: "mm"
      algorithm:
        type: "string"
        default: "kde"
        choices: ["search_radius", "kde", "diff_lock"]
      kde_a:
        type: "float"
        default: 1.0
        hint: "gaussian parameter, amplitude"
      kde_c:
        type: "float"
        default: 15.0
        hint: "gaussian parameter, standard deviation"
      kde_nearby_mm:
        type: "float"
        default: 100.0
        hint: "radius of neighborhood in mm to consider for computing kernel"
      kde_precision_mm:
        type: "float"
        default: 10.0
        hint: "compute density for every x mm"
      kde_select_factor:
        type: "float"
        default: 5.0
        hint: "cut off minima after this much of persistence drop"
      outlier_removal_percent:
        type: "float"
        default: 25.0
        hint: "remove this percent of outliers from both sides when computing center of band (average x of the middle)"
      kde_calculate_std:
        type: "bool"
        default: true
        hint: "calculate standard deviation and nearby radius automatically from average band width"
      kde_std_band_width_ratio:
        type: "float"
        default: 0.33
        hint: "stddev = ratio * avg band width ^ exponent + offset"
      kde_std_band_width_exponent:
        type: "float"
        default: 1.0
        hint: "stddev = ratio * avg band width ^ exponent + offset"
      kde_std_band_width_offset:
        type: "float"
        default: 0.0
        hint: "stddev = ratio * avg band width ^ exponent + offset"
      use_squeeze_averaging:
        type: "bool"
        default: false
        hint: "use squeeze algorithm to determine band positions"
      squeeze_distance_mm:
        type: "float"
        default: 25.4
        units: "mm"
        hint: "if removing outlier makes band smaller by this distance, continue removing"
      squeeze_outlier_count:
        type: "uint"
        default: 3
        hint: "how many outliers to consider at once"
      seed_line_spacing_inches:
        type: "float"
        default: -1
        units: "inches"
        hint: "Used for differential locked bands, the distance from the left-most to the right-most seedline in one band. (set to -1 to use the band width)"
      kde_require_all_minima:
        type: "bool"
        default: false
        default_recommended: false
        hint: "If true, kde must find n+1 minima to compute band locations, otherwise it will fallback too last known band locations."
  two_drop_thinning:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        default_recommended: false
        complexity: "user"
        hint: "Deprecated: [2022-12-21] see targeting_mode"
      vertical_search_dist:
        type: "float"
        default: 3.0
        default_recommended: false
        complexity: "user"
        hint: "Distance between centers in vertical search [inches]"
      horizontal_search_dist:
        type: "float"
        default: 1.0
        default_recommended: false
        complexity: "user"
        hint: "Distance between centers in horizontal search [inches]"
      size_based_shooting:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "expert"
        hint: "If true shoot smallest crop of each two drop otherwise shoot the bottom crop of each two drop"
  greedy_thinning:
    type: "node"
    children:
      window_offset:
        type: "float"
        default: 0.25
        default_recommended: true
        complexity: "user"
        hint: "[Modified: v2.1] Used only in Advanced Algo, the max box of a keeper must be below the decision_line by this amount before we use it to decide the next keeper"
        units: "inches"
      cpt_decision:
        type: "uint"
        default: 0
        default_recommended: true
        complexity: "developer"
        hint: "What we should do with crops that fail cpt: thin=0, keep=1, drop=2"
      mindoo_decision:
        type: "uint"
        default: 0
        default_recommended: true
        complexity: "developer"
        hint: "What we should do with crops that fail mindoo: thin=0, keep=1, drop=2"
  metrics:
    type: "node"
    children:
      conclusions:
        type: "node"
        children:
          rolling_duration_s:
            type: "uint"
            default: 60
            units: "seconds"
            hint: "Duration of the conclusion rolling average"
  cross_cam_deduplication:
    type: "node"
    children:
      radius:
        type: "float"
        default: 6.0
        complexity: "expert"
        units: "mm"
        hint: "Deprecated: [2022-11-09] see crop/weed specific version"
      delta_time_ms:
        type: "uint"
        default: 50
        complexity: "developer"
        units: "ms"
        hint: "Deprecated: [2022-11-09] see crop/weed specific version"
      enabled:
        type: "bool"
        default: true
        complexity: "expert"
        hint: "Deprecated: [2022-11-09] see crop/weed specific version"
      crop:
        include: "components/cross_cam_dedup.yaml"
      weed:
        include: "components/cross_cam_dedup.yaml"
      plant:
        include: "components/cross_cam_dedup.yaml"
  shooting:
    type: "node"
    children:
      scanner_settle_time:
        type: "list"
        item:
          type: "uint"
          hint: "Time to settle by scanner type name examples: scanner_gd_FAULHABER, h753a_MAXON"
          units: "ms"
      laser_shoot_update_interval:
        type: "uint"
        default: 25
        units: "ms"
        min: 5
        max: 50
        hint: "How often to update the laser shot time while shooting a target. smaller number means less overshoot, but may effect runtime performance."
        default_recommended: true
        complexity: "developer"
      max_error_time:
        type: "uint"
        default: 1500
        units: "ms"
        hint: "Cap avg error time to this value to prevent bad data from causing issues in VE"
        default_recommended: true
        complexity: "developer"
      kill_box_offset:
        type: "float"
        default: 100
        units: "mm"
        hint: "How much padding to add to end of kill box to check if a trajectory is still possibly in kill box"
        default_recommended: true
        complexity: "developer"
      rotary_and_p2p:
        type: "node"
        children:
          use_dynamic_move_velocity:
            type: "bool"
            default: false
            hint: "If on, this will make the initial move use a dynamically adjusted velocity based on the size of the move"
          dynamic_move_velocity_query_pos:
            type: "bool"
            default: false
            hint: "If on, and dynamic move velocity is used, this will query for the position in order to compute velocity, adds comms latency but can improve motion latency"
          servo_failure_detection:
            type: "node"
            children:
              enabled:
                type: "bool"
                default: false
                complexity: "expert"
                hint: "This enables alarming when servo is believed to have an error"
              min_bad_delta:
                type: "uint"
                default: 500
                complexity: "expert"
                hint: "Minimum servo ticks delta to be considered a bad move"
              minimum_good_ratio:
                type: "float"
                default: 0.5
                complexity: "expert"
                hint: "Ratio of Good to bad servo ticks delta to consider the scanner bad"
              smoothing:
                type: "float"
                default: 0.99
                complexity: "expert"
                hint: "Smoothing used in a moving average to track the success ratio"
          p2p_on_target_count:
            type: "uint"
            default: 100
            min: 1
            default_recommended: true
            hint: "Minimum number of successful p2ps before we can check if p2p target appears to have changed"
          max_p2p_target_switch_dist:
            type: "float"
            default: 25
            default_recommended: true
            hint: "How far away a p2p match must be to be considered a target change."
            units: "mm"
          p2p_target_switch_count:
            type: "uint"
            default: 3
            min: 1
            default_recommended: true
            hint: "How many p2p target changes can occur before we assume failure."
          p2p_target_switch_capture_rate:
            type: "float"
            default: 0.0
            min: 0
            max: 1
            default_recommended: true
            hint: "How often to capture p2p target switch failures"
          p2p_captures_write_to_disk:
            type: "bool"
            default: false
            hint: "Whether to write p2p captures triggered by aimbot to disk"
          first_p2p_capture_rate:
            type: "float"
            default: 0.0
            min: 0
            max: 1
            default_recommended: true
            hint: "How often to capture successful first p2p moves"
          first_p2p_miss_capture_rate:
            type: "float"
            default: 0.0
            min: 0
            max: 1
            default_recommended: true
            hint: "How often to capture failed first p2p moves"
          p2p_miss_capture_rate:
            type: "float"
            default: 0.0
            min: 0
            max: 1
            default_recommended: true
            hint: "How often to capture a p2p miss"
  laser_power:
    type: "node"
    children:
      baseline:
        type: "float"
        default: 140.0
        units: "watts"
        hint: "The baseline for the power a laser should be measuring at, this is what almanac shoot times are calculated at."
        default_recommended: true
        complexity: "expert"
      scaling_formula:
        type: "node"
        children:
          a:
            type: "float"
            default: 1.0
            hint: "scaling formula of the form a*x + b where x = baseline/laser_power"
            default_recommended: true
            complexity: "expert"
          b:
            type: "float"
            default: 0.0
            hint: "scaling formula of the form a*x + b where x = baseline/laser_power"
            default_recommended: true
            complexity: "expert"
  weeding_diagnostics:
    type: "node"
    children:
      work_interval_ms:
        type: "uint"
        default: 1000
      end_recording_images_timeout_ms:
        type: "uint"
        default: 5000
        hint: "Time between end of recording images and end of recording diagnostics, needed for consistency between trajectory snapshots and their burst recording"
  feature_flags:
    type: "node"
    children:
      tractor_enforced_safety_feature:
        type: "bool"
        default: false
        hint: "Enable autotractor to set a safety state on the implement"
        complexity: "expert"
      cruise_control_feature:
        type: "bool"
        default: false
        hint: "Enable tractor cruise control"
        complexity: "expert"
      crop_ids_feature:
        type: "bool"
        default: true
        hint: "Deprecated: [2023-07-23] this feature is enabled regardless of this value"
        complexity: "expert"
      messaging_feature:
        type: "bool"
        default: false
        hint: "enable support chat"
        complexity: "expert"
      dynamic_banding_feature:
        type: "bool"
        default: false
        hint: "enable dynamic banding"
        complexity: "expert"
      auto_crosshair_calibration_feature:
        type: "bool"
        default: false
        hint: "enable auto crosshair calibration"
        complexity: "expert"
      startup_task_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable startup tasks"
      jobs_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable jobs"
      mapping_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable history mapping"
      thinning_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable targeting mode"
      almanac_v3_ui_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable almanac v3"
      geo_calibrator_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable Geo Calibrator"
      speculative_laser_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable speculatively shooting the laser."
      invalidate_current_target:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable feature to invalidate current target in scheduler for faster target switching."
      velocity_laser_safety_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "When enabled only schedule shots if we are moving, to prevent continous shooting while stopped."
      eu_compliant_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "This robot supports EU Compliant features"
      pinning_default_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables pinning by default feature"
      copy_modelinator_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables copying modelinator from model to model"
      thinning_conf_sized_thinning:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables sized based not so greedy thinning"
      chat_authentication_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables chat authentication feature"
      crop_based_weed_targeting:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables crop based weed targeting"
      plant_captcha_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables plant captcha"
      almanac_translation_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables almanac and quick tune translation via GetNextConfigData"
      embedding_based_classification_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables the embedding based classification feature"
      upt_visualization_feature:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enables the UPT visualization feature"
      boundary_checker_feature:
        type: "bool"
        default: false
        default_recommended: true
        hint: "Enable boundary checker (rtc only)"
        complexity: "expert"
      banding_algorithm_diagnostic_feature:
        type: "bool"
        default: false
        default_recommended: true
        hint: "Enables the banding algorithm diagnostic feature"
        complexity: "expert"
  targeting_mode:
    type: "node"
    children:
      thinning:
        type: "node"
        children:
          enabled:
            type: "bool"
            default: false
            default_recommended: false
            hint: "Deprecated: [v1.15] use operator app"
            complexity: "expert"
          algorithm:
            type: "uint"
            default: 0
            default_recommended: false
            hint: "Deprecated: [v1.15] use operator app"
            complexity: "expert"
      weeding:
        type: "node"
        children:
          enabled:
            type: "bool"
            default: true
            default_recommended: false
            hint: "Deprecated: [v1.15] use operator app"
            complexity: "expert"
  expected_chiller_temp:
    type: "float"
    default: 16.5
    units: "C"
    hint: "Temperature to which chiller should be set"
  chiller_temp_bound:
    type: "float"
    default: 3
    units: "C"
    default_recommended: true
    hint: "The plus/minus bounds from expected_chiller_temp for what is considered an exceptable chiller temp"
  arc_detector:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        hint: "Enable arc detection"
      count_threshold:
        type: "uint"
        default: 1
        hint: "Number of above threshold LPSU currents to be considered arcing"
      count_period:
        type: "float"
        default: 1.0
        units: "hours"
        hint: "Period to count arc events"
      current_threshold:
        type: "float"
        default: 0.042
        units: "A"
        hint: "LPSU current threshold to be considered arcing"
  plant_captcha:
    type: "node"
    children:
      work_interval_ms:
        type: "uint"
        default: 200
        complexity: "developer"
        units: "ms"
        default_recommended: true
      images_per_sec:
        type: "uint"
        default: 10
        complexity: "developer"
        units: "seconds"
        default_recommended: true
      total_images:
        type: "uint"
        default: 300
        complexity: "developer"
        default_recommended: true
      algorithm:
        type: "string"
        default: "simple_trajectory"
        complexity: "developer"
        default_recommended: true
      veselka_algorithm:
        type: "string"
        default: "simple_trajectory"
        default_recommended: false
        complexity: "developer"
      goal_crops_targeted:
        type: "float"
        default: 0.02
        complexity: "developer"
        default_recommended: true
      goal_weeds_targeted:
        type: "float"
        default: 0.8
        complexity: "developer"
        default_recommended: true
      goal_unknown_targeted:
        type: "float"
        default: 0
        default_recommended: false
        complexity: "developer"
      min_recommended_mindoo:
        type: "float"
        default: 0
        complexity: "developer"
        default_recommended: true
      max_recommended_mindoo:
        type: "float"
        default: 1
        complexity: "developer"
        default_recommended: true
      min_recommended_weed_threshold:
        type: "float"
        default: 0.1
        complexity: "developer"
        default_recommended: true
      max_recommended_weed_threshold:
        type: "float"
        default: 0.9
        complexity: "developer"
        default_recommended: true
      min_recommended_crop_threshold:
        type: "float"
        default: 0.1
        complexity: "developer"
        default_recommended: true
      max_recommended_crop_threshold:
        type: "float"
        default: 0.9
        complexity: "developer"
        default_recommended: true
      min_items_for_recommendation:
        type: "int"
        default: 10
        complexity: "developer"
        default_recommended: true
        hint: "Deprecated: [v2.1] use min_ingest_plant_threshold"
      min_ingest_weed_threshold:
        type: "float"
        default: 0.2
        complexity: "developer"
        default_recommended: true
        hint: "Deprecated: [v2.1] use min_ingest_plant_threshold"
      min_ingest_crop_threshold:
        type: "float"
        default: 0.2
        complexity: "developer"
        default_recommended: true
      min_ingest_plant_threshold:
        type: "float"
        default: 0.2
        complexity: "developer"
        default_recommended: true
      max_retries_for_category:
        type: "uint"
        default: 10
        complexity: "developer"
        default_recommended: true
        hint: "The amount of times captcha can try to grab from a category bucket before moving on"
      max_retries_for_embedding:
        type: "uint"
        default: 10
        complexity: "developer"
        default_recommended: true
        hint: "The amount of times captcha can try to grab from an embedding bucket before moving on"
      min_doo_for_recommendation:
        type: "float"
        default: 0.1
        hint: "The min doo required for an item to be used to recommend thresholds/mindoo"
        complexity: "developer"
        default_recommended: true
      query_retries:
        type: "uint"
        default: 3
        complexity: "developer"
        default_recommended: true
      use_weed_categories_for_weed_labels:
        type: "bool"
        default: true
        hint: "Whether to use weed categories vs categories for weed labels. Enabling this means that items labeled weeds but predicted as crop can still be categorized as a weed category"
        complexity: "developer"
        default_recommended: true
      use_other_as_tiebreaker:
        type: "bool"
        default: true
        hint: "If true, thresholds/mindoo selection will use items labeled OTHER to tiebreak between equally-good weeds-targeted configurations"
        complexity: "developer"
        default_recommended: true
      limit_by_crops_missed:
        type: "bool"
        default: true
        hint: "If true, crop configurations will first be narrowed down to those that reduce crops missed to the level of the current thresholds/mindoo. This is meant to speed up the algorithm"
        complexity: "developer"
        default_recommended: true
      number_of_crop_configurations:
        type: "int"
        default: 50
        hint: "If positive, this will limit how many crop configurations we consider. This is meant to speed up the algorithm"
        complexity: "developer"
        default_recommended: true
      max_buffer_bucket_size:
        type: "uint"
        default: 100
        hint: "Limits the number of trajectories stored within a bucket. Buckets will remove old trajectories to make room for new ones"
        complexity: "developer"
        default_recommended: true
      unbanded_item_filter:
        type: "float"
        default: 0
        hint: "0 to 1, determines how many of the unbanded region samples are allowed to be processed. For example, if 0, no unbanded items are allowed. If 1, every unbanded item would be allowed."
        complexity: "developer"
        default_recommended: true
      default_label_is_max_hit_class:
        type: "bool"
        default: true
        hint: "If true, the default label presented to the user will be the one with max hit class (DEPRECATED 1.20)"
        complexity: "developer"
        default_recommended: true
      initial_label_strategy:
        type: "string"
        selected: "max_hit"
        hint: "This value determines how we set the initial category (default, max_hit, weeding_outcome_based)"
        complexity: "developer"
        default_recommended: false
        choices: ["default", "max_hit", "weeding_outcome_based"]
      tiebreaker:
        type: "string"
        default: "mean_square"
        hint: "This value determines how we tiebreak recommendations. Custom indicates to use tiebreaker_strategy_threshold_weed and tiebreaker_strategy_threshold_crop"
        complexity: "developer"
        default_recommended: false
        choices: ["mean_square", "random", "mean_absolute", "custom"]
      tiebreaker_strategy_threshold_weed:
        type: "string"
        default: "middle"
        hint: "If tiebreaker is custom, this will determine how to tiebreak weed thresholds"
        default_recommended: false
        choices: ["low", "middle", "high"]
      tiebreaker_strategy_threshold_crop:
        type: "string"
        default: "middle"
        hint: "If tiebreaker is custom, this will determine how to tiebreak crop thresholds"
        default_recommended: false
        choices: ["high", "middle", "low"]
      mindoo_tiebreaker:
        type: "string"
        default: "mean_highest"
        hint: "This value determines how we tiebreak mindoo recommendations. Custom indicates to use tiebreaker_strategy_mindoo_weed and tiebreaker_strategy_mindoo_crop"
        complexity: "developer"
        default_recommended: false
        choices: ["mean_highest", "random", "mean_lowest", "custom"]
      tiebreaker_strategy_mindoo_weed:
        type: "string"
        default: "middle"
        hint: "If mindoo_tiebreaker is custom, this will determine how to tiebreak weed mindoo"
        default_recommended: false
        choices: ["low", "middle", "high"]
      tiebreaker_strategy_mindoo_crop:
        type: "string"
        default: "middle"
        hint: "If mindoo_tiebreaker is custom, this will determine how to tiebreak crop mindoo"
        default_recommended: false
        choices: ["high", "middle", "low"]
      num_x_embedding_buckets:
        type: "uint"
        default: 10
        complexity: "developer"
        default_recommended: true
        hint: "The amount of discrete buckets embeddings will be placed into along the x-axis"
      num_y_embedding_buckets:
        type: "uint"
        default: 10
        complexity: "developer"
        default_recommended: true
        hint: "The amount of discrete buckets embeddings will be placed into along the x-axis"
      min_plant_size_mm:
        type: "float"
        default: 0
        complexity: "developer"
        default_recommendation: false
        units: "mm"
        hint: "The minimum size in mm for plants to be collected for captcha"
      allow_cropped_images:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "If true, captcha will include perspectives of a plant that are not centered in the image"
      pad_crop_configurations:
        type: "bool"
        default: true
        complexity: "developer"
        default_recommended: false
        hint:
          "If true, we pad crop configurations for crop sizes that don't have any items. This will allow for us to adjust the crop threshold/mindoo for items that don't have enough items to\
          reduce crop protection for corresponding weeds"
      use_beneficials_as_crops:
        type: "bool"
        default: true
        complexity: "developer"
        hint: "If true, things labeled beneficial will be treated as crops in the plant captcha algorithm"
      use_volunteers_as_weeds:
        type: "bool"
        default: true
        complexity: "developer"
        hint: "If true, things labeled volunteers will be treated as weeds in the plant captcha algorithm"
  geometry:
    type: "node"
    children:
      gps:
        type: "node"
        children:
          gps_offset_x:
            type: "float"
            default: -1068.0
            complexity: "expert"
            units: "mm"
            default_recommended: true
            hint: "Offset from real gps location to the reported position, x+ is rightward of the robot"
          gps_offset_y:
            type: "float"
            default: -1397.0
            complexity: "expert"
            units: "mm"
            default_recommended: true
            hint: "Offset from real gps location to the reported position, y+ is forward of the robot"
          gps_offset_z:
            type: "float"
            default: 2159.0
            complexity: "expert"
            units: "mm"
            default_recommended: true
            hint: "Offset from real gps location to the reported position, z+ is downward of the robot"
  crop_based_weed_targeting:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        default_recommended: false
        complexity: "developer"
        hint: "If true, use crop based weed targeting"
      kept_crops_only:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: false
        hint: "If true, only consider kept crops (in thinning) for weed targeting"
      kill_box_radius_in_x:
        type: "float"
        default: 4.0
        complexity: "developer"
        default_recommended: false
        hint: "The radius of the kill box in the x direction"
      kill_box_radius_in_y:
        type: "float"
        default: 2.0
        complexity: "developer"
        default_recommended: false
        hint: "The radius of the kill box in the y direction"

  protector:
    include: "components/protector.yaml"
  minicomputer_rows:
    type: "list"
    item:
      type: "int"
  decision_line:
    type: "node"
    children:
      weeding_scanner_offset_in:
        type: "float"
        default: 5.0
        units: "inches"
        hint: "Distance the top of targetable space to the decision line, used if weeding"
      thinning_scanner_offset_in:
        type: "float"
        default: 10.0
        units: "inches"
        hint: "Distance the top of targetable space to the decision line, used if thinning"
      use_middle_of_predict_space:
        type: "bool"
        default: false
        hint: "If true, use the middle of the predict space as the decision line"
