type: "node"
children:
  device_overrides:
    include: "components/device_overrides.yaml"
  autonomous_hb_required:
    type: "bool"
    default: true
    complexity: "expert"
    hint: "Deprecated: Require client hb even when in autonomous mode for safety."
  hb_timeout:
    type: "int"
    default: 3
    units: "seconds"
    complexity: "developer"
    default_recommended: true
    hint: "Max allowable time between HB before declaring disconnect from remote connection"
  only_hb_counts_for_connection:
    type: "bool"
    default: false
    default_recommended: true
    complexity: "developer"
    hint: "Deprecated: Used to control release of feature to work with FE, If other messages can count as connection alive"
  stream_min_connection_fps:
    type: "float"
    default: 10.0
    units: "fps"
    complexity: "developer"
    default_recommended: true
    hint: "Deprecated: (see min_instant_fps) What is the min frame rate to consider still connected"
  reported_stream_connection:
    type: "node"
    children:
      min_instant_fps:
        type: "float"
        default: 0.0
        units: "fps"
        complexity: "developer"
        default_recommended: true
        hint: "Deprecated: What is the min single measurement frame rate to consider still connected"
      rolling_window_size:
        type: "int"
        default: 3
        units: "seconds"
        min: 2
        complexity: "developer"
        default_recommended: true
        hint: "How much data to average over for avg fps (only read at boot), bigger means more resilient to quick drops, but can take too long to catch issues"
      min_avg_fps:
        type: "float"
        default: 5.0
        units: "fps"
        complexity: "developer"
        default_recommended: true
        hint: "What is the min avg measurement frame rate to consider still connected"
      min_consecutive_count_fps:
        type: "float"
        default: 3.0
        units: "fps"
        complexity: "developer"
        default_recommended: true
        hint: "If N consecutive frames are below this rate we are considered disconnected"
      consecutive_count_count:
        type: "uint"
        default: 5
        min: 1
        complexity: "developer"
        default_recommended: true
        hint: "number of consecutive frames below min rate at which we are considered disconnected"
  hitch_down_val:
    type: "float"
    default: 40.0
    default_recommended: false
    units: "%"
    complexity: "expert"
    hint: "When hitch pos reads less than or equal to this value we consider the hitch to be in the down position value is 0-100"
  steering_conf:
    type: "node"
    children:
      left_hard_stop:
        type: "float"
        default: -25.0
        units: "degrees"
        complexity: "expert"
        default_recommended: false
        hint: "The angle reading when wheels are fully left"
      right_hard_stop:
        type: "float"
        default: 25.0
        units: "degrees"
        complexity: "expert"
        default_recommended: false
        hint: "The angle reading when wheels are fully right"
      max_allowed_angle_with_engaged_implement:
        type: "float"
        default: -1.0
        units: "degrees"
        complexity: "expert"
        default_recommended: false
        hint: "Max allowed angle when the implement is lowered (less than 0 means ignore). Prevent harsh steering for 3pt implements"
  fw_conf:
    type: "node"
    children:
      fw_fuel_level_support:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "Flag to indicate if we can get fuel level info from FW"
      fw_engine_temp_support:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "Flag to indicate if we can get engine temp info from FW"
      pet_loss_is_stop:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "Change what happens to hh board on pet loss. In production this should always be false"
      tractor_variant:
        type: "string"
        default: "JD_6LH"
        choices: ["JD_6LH", "JD_6LHM", "JD_6PRO", "JD_7LH", "JD_7PRO", "JD_8RH"] # Must match lib/drivers/nanopb/hasselhoff_board/hasselhoff_board_connector.py (TractorVariantType)
        complexity: "developer"
        default_recommended: false
        hint: "The type of tractor we are controlling"
      wheel_angle_cal:
        type: "node"
        children:
          sensor_deg_per_bit:
            type: "float"
            default: 0.0
            complexity: "developer"
            default_recommended: true
            hint: "The number of degrees one bit of change represents in the wheel angle sensor"
          sensor_center_val:
            type: "int"
            default: 0
            complexity: "developer"
            default_recommended: false
            hint: "The sensor reading at wheels centered"
          center_trim:
            type: "float"
            default: 0.0
            units: "deg"
            complexity: "developer"
            default_recommended: false
            hint: "The center trim value for the sensor"
          right_lock:
            type: "float"
            default: 0.0
            units: "deg"
            complexity: "developer"
            default_recommended: false
            hint: "The wheel angle at full lock right"
          sensor_val_left_lock:
            type: "int"
            default: 0
            complexity: "developer"
            default_recommended: false
            hint: "The sensor reading at full lock left"
          sensor_val_right_lock:
            type: "int"
            default: 0
            complexity: "developer"
            default_recommended: false
            hint: "The sensor reading at full lock right"
      steering_conf:
        type: "node"
        children:
          kp:
            type: "float"
            default: 0.0
            complexity: "developer"
            default_recommended: false
            hint: "KP value for pid control loop"
          ki:
            type: "float"
            default: 0.0
            complexity: "developer"
            default_recommended: false
            hint: "KI value for pid control loop"
          kd:
            type: "float"
            default: 0.0
            complexity: "developer"
            default_recommended: false
            hint: "KD value for pid control loop"
          integral_limit:
            type: "float"
            default: 0.0
            complexity: "developer"
            default_recommended: false
            hint: "Integral limit for anti-windup in pid"
          update_rate:
            type: "uint"
            default: 20
            units: "hz"
            complexity: "developer"
            default_recommended: true
            hint: "Pid control loop frequency"
          min_steering_valve_current:
            type: "uint"
            default: 0
            units: "mA"
            complexity: "developer"
            default_recommended: false
            hint: "Valve deadband configuration"
          max_steering_valve_current:
            type: "uint"
            default: 0
            units: "mA"
            complexity: "developer"
            default_recommended: false
            hint: "Valve deadband configuration"
  autonomous_algos:
    type: "node"
    children:
      min_steering_control_speed:
        type: "float"
        default: -1.0
        units: "mph"
        complexity: "expert"
        hint: "Prevent autonomous steering control when moving below this speed"
      gps_path_follow:
        type: "node"
        children:
          stanley:
            include: "components/stanley_steering.yaml"
      furrow_following:
        type: "node"
        children:
          cam_name:
            type: "string"
            default: ""
            complexity: "developer"
            default_recommended: false
            hint: "What camera to use for doing furrow following"
          max_steering_angle:
            type: "float"
            default: 10.0
            complexity: "developer"
            units: "degrees"
            hint: "How much we can steer in furrows before erroring"
          max_xte:
            type: "float"
            default: 0.1
            complexity: "developer"
            hint: "How far away the furrow line can be"
          max_furrow_angle:
            type: "float"
            default: 10.0
            complexity: "developer"
            units: "degrees"
            hint: "Max angle the furrow detection can be at and still be considered valid"
          max_bad_detection_count:
            type: "int"
            default: 5
            complexity: "developer"
            hint: "How many consecutive bad detections before failing autonomy"
          max_failed_detection_count:
            type: "int"
            default: 5
            complexity: "developer"
            hint: "How many consecutive failed detections before failing autonomy"
          alt_wheel_y_pos:
            type: "float"
            default: 0.5
            complexity: "developer"
            hint: "If >= 0 use this as the wheel y pos rather than the actual wheel pos"
          lpf_alpha:
            type: "float"
            default: 0.6
            complexity: "developer"
            default_recommended: true
            help: "Low pass filter alpha value for steering angle"
          stanley:
            include: "components/stanley_steering.yaml"
      task_sequence_algo:
        type: "node"
        children:
          hitch_raised_min:
            type: "float"
            default: 0.75
            complexity: "developer"
            units: "%"
            default_recommended: false
            hint: "Min percentage [0-1] at which algo considers the hitch to be raised"
          hitch_lowered_max:
            type: "float"
            default: 0.45
            complexity: "developer"
            units: "%"
            default_recommended: false
            hint: "Max percentage [0-1] at which algo considers the hitch to be lowered"
          hitch_change_time:
            type: "float"
            default: 5.0
            complexity: "developer"
            default_recommended: false
            units: "seconds"
            hint: "Time to wait for hitch to change state"
          state_change_max_attempts:
            type: "int"
            default: 3
            complexity: "developer"
            default_recommended: false
            hint: "Maximum attempts to change tractor state before giving up"
          ff_max_path_xte:
            type: "float"
            default: 2.0
            units: "meters"
            complexity: "developer"
            default_recommended: true
            hint: "Max distance the robot can deviate from the AB path to maintain the furrow path."
          ff_max_start_xte:
            type: "float"
            default: 0.5
            units: "meters"
            complexity: "developer"
            default_recommended: true
            hint: "Max distance the robot can be off of the Infinite AB line to start with."
          ff_max_start_dist:
            type: "float"
            default: 3.0
            units: "meters"
            complexity: "developer"
            default_recommended: true
            hint: "Max distance the robot can be from the start point (A or B depending on direction), when starting a path. Negative means don't check."
  speed:
    type: "node"
    children:
      max_speed_mph:
        type: "float"
        default: 10.0
        complexity: "user"
        units: "mph"
        default_recommended: true
        hint: "How fast the tractor is allowed to drive."
      max_speed_safety_bypassed_mph:
        type: "float"
        default: 1.5
        complexity: "user"
        units: "mph"
        default_recommended: true
        hint: "How fast the tractor is allowed to drive, when any safety system is bypassed"
      fw_speed_limit_support:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "Does the firmware support setting speed limit"
  boundary_checker:
    type: "node"
    children:
      max_gps_age:
        type: "int"
        default: 5000
        complexity: "developer"
        units: "milliseconds"
        default_recommended: true
        hint: "How old the gps data can be and still be used for position estimation"
      implement_def:
        type: "node"
        children:
          attached:
            type: "bool"
            default: false
            complexity: "expert"
            default_recommended: false
            hint: "flag to count implement as part of polygon, (this will eventually be part of cloud implement definition)"
          width:
            type: "float"
            default: 6.01
            complexity: "expert"
            units: "meters"
            default_recommended: false
            hint: "How wide is the implement, (this will eventually be part of cloud implement definition)"
          length:
            type: "float"
            default: 3.0
            complexity: "expert"
            units: "meters"
            default_recommended: false
            hint: "How long is the implement, (this will eventually be part of cloud implement definition)"
      gps_quality_buffer:
        type: "node"
        children:
          fixed:
            type: "float"
            default: 0.0
            complexity: "user"
            units: "meters"
            default_recommended: true
            hint: "How much buffer to add to the tractor polygon when we have a fixed solution"
          floating:
            type: "float"
            default: 0.5
            complexity: "user"
            units: "meters"
            default_recommended: true
            hint: "How much buffer to add to the tractor polygon when we have a floating solution"
          none:
            type: "float"
            default: 3.0
            complexity: "user"
            units: "meters"
            default_recommended: true
            hint: "How much buffer to add to the tractor polygon when we have no rtk solution"
      update_timeout:
        type: "float"
        default: 1.0
        complexity: "developer"
        units: "seconds"
        default_recommended: true
        hint: "How long between updates before we consider boundary checker to be not running"
  vstop:
    type: "node"
    children:
      cloud_push_interval:
        type: "float"
        default: 10.0
        complexity: "developer"
        units: "seconds"
        default_recommended: true
        hint: "How often to push current state to cloud, without a state change"
      failure_grace_period:
        type: "float"
        default: 10.0
        complexity: "developer"
        units: "seconds"
        default_recommended: true
        hint: "How long between disconnect and reconnect we can tolerate before we consider connection broken"
      connection_required:
        type: "bool"
        default: true
        complexity: "developer"
        default_recommended: true
        hint: "If we consider connection to be broken, then this will be the stop state"
  differential_braking_enabled:
    type: "bool"
    default: false
    default_recommended: false
    complexity: "expert"
    hint: "Are we allowed to use differential braking on this tractor?"
  features:
    type: "node"
    children:
      implement_safety:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "Implement can indicate if it enforces safety state"
  rpms_conf:
    type: "node"
    children:
      min_pct:
        type: "float"
        default: 0.0
        complexity: "developer"
        units: "%"
        default_recommended: true
        hint: "The minimum rpm percentage that can be commanded between 0 and 1"
      max_pct:
        type: "float"
        default: 1.0
        complexity: "developer"
        units: "%"
        default_recommended: true
        hint: "The maximum rpm percentage that can be commanded between 0 and 1"
      freq_at_min:
        type: "uint"
        default: 500
        complexity: "developer"
        units: "rpms"
        default_recommended: true
        hint: "The actual rpms when at the commanded 'min_pct'"
      freq_at_max:
        type: "uint"
        default: 1860
        complexity: "developer"
        units: "rpms"
        default_recommended: true
        hint: "The actual rpms when at the commanded 'max_pct'"
