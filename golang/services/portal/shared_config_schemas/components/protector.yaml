type: "node"
children:
  crop_protect_radius:
    type: "float"
    default: 1.0
    default_recommended: true
    units: "mm"
    complexity: "developer"
    hint: "If a weed is within this distance of a valid crop then it will not be shot."
  reverse_crop_protect_radius:
    type: "float"
    default: -1.0
    default_recommended: true
    units: "mm"
    complexity: "developer"
    hint: "If a crop is within this distance of a valid weed then it will not be used as a keeper durring thinning."
  crop_protect_radius_multiplier:
    type: "float"
    default: 1.0
    default_recommended: true
    complexity: "developer"
    hint: "If using crops radius (crop_protect_radius is < 0) multiply radius by this value to get crop protect radius"
  speculative_crop_protect_radius:
    type: "float"
    default: -1.0
    default_recommended: true
    units: "mm"
    complexity: "developer"
    hint: "If a weed is within this distance of a valid crop then no speculative shooting can be done for this weed. (if <= 0 we use the crops radius instead)"
  allow_unprotect:
    type: "bool"
    default: false
    default_recommended: true
    complexity: "developer"
    hint: "If enabled we can unprotect a weed if the crop no longer exists to protect it (help with faulty detections)"
  speculative_crop_protect_min_radius:
    type: "float"
    default: -1.0
    default_recommended: true
    units: "mm"
    complexity: "developer"
    hint: "Only do speculative protection around crops that are of this size or larger"
  speculative_crop_protect_radius_multiplier:
    type: "float"
    default: 1.0
    default_recommended: true
    complexity: "developer"
    hint: "If using crops radius multiply by this value to get crop protect radius"
