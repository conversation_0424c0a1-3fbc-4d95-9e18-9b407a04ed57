package customers

import (
	"encoding/base64"
	"strings"

	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
)

const (
	pageTokenVer    = "v1|"
	defaultPageSize = 500
)

// ListCustomers retrieves all customers, if the returned pageToken string is not empty, it can be used
// to retrieve the next page of results.
func ListCustomers(db *gorm.DB, pageSize int, pageToken string) ([]*models.Customer, string, error) {
	if pageSize <= 0 {
		pageSize = defaultPageSize
	}

	query := db.Order("uuid asc")
	if pageToken != "" {
		if ptk, err := base64.URLEncoding.DecodeString(pageToken); err == nil {
			if strings.HasPrefix(string(ptk), pageTokenVer) {
				query.Where("uuid > ?", strings.TrimPrefix(string(ptk), pageTokenVer))
			}
		}
	}

	var results []*models.Customer
	if err := query.Limit(pageSize + 1).Find(&results).Error; err != nil {
		return nil, "", err
	}

	nextPageToken := ""
	if len(results) > pageSize {
		nextPageToken = base64.URLEncoding.EncodeToString([]byte(pageTokenVer + results[pageSize-1].Uuid.String()))
		results = results[:pageSize]
	}
	return results, nextPageToken, nil
}

func GetCustomers(db *gorm.DB) ([]*models.Customer, error) {
	var customers []*models.Customer
	err := db.Find(&customers).Error
	if err != nil {
		log.WithError(err).Error("Error getting customers")
		return nil, err
	}
	return customers, nil
}

func GetCustomer(db *gorm.DB, customerID uint) (*models.Customer, error) {
	var customer models.Customer
	err := db.First(&customer, customerID).Error
	if err != nil {
		return nil, err
	}
	return &customer, nil
}

func GetCustomerByRobotId(db *gorm.DB, robotID uint) (*models.Customer, error) {
	customer := new(models.Customer)
	err := db.
		Model(&models.Customer{}).
		Joins("left join customer_robots on customer_robots.customer_id = customers.id").
		Where("customer_robots.robot_id = ? and customer_robots.deleted_at is null", robotID).
		First(customer).
		Error
	if err != nil {
		return nil, err
	}
	return customer, nil
}

func SaveCustomer(db *gorm.DB, customer *models.Customer) error {
	return db.Save(customer).Error
}
