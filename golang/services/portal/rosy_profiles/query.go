package rosy_profiles

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	veselkalib "github.com/carbonrobotics/cloud/golang/pkg/veselka"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/protos/golang/generated/proto/category_profile"
	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
)

func protoMarshal(m protoreflect.ProtoMessage) (string, error) {
	b, err := protojson.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

func GetProfile(ctx context.Context, robotSyncerClient *robot_syncer.Client, uuid string) (*robot_syncer.Profile, error) {
	request := robot_syncer.GetProfileParams{ProfileId: uuid}
	return robotSyncerClient.GetProfile(ctx, request)
}

func saveProfile(ctx context.Context, robotSyncerClient *robot_syncer.Client, profile *robot_syncer.Profile) error {
	existingProfile, err := GetProfile(ctx, robotSyncerClient, profile.Id.String())

	var respErr robot_syncer.ResponseError
	newProfile := err != nil && errors.As(err, &respErr) && respErr.StatusCode == http.StatusNotFound

	if err != nil && !newProfile {
		log.WithError(err).Errorf("Failed to find profile %s", profile.Id.String())
		return err
	}

	if !newProfile && existingProfile != nil && existingProfile.Deleted == true {
		return fmt.Errorf("deleted profiles can not be edited")
	}

	if profile.Protected {
		err := robotSyncerClient.UploadProtectedProfile(ctx, *profile)
		if err != nil {
			return err
		}
	} else {
		err := robotSyncerClient.UploadCustomerProfile(ctx, *profile)
		if err != nil {
			return err
		}
	}
	return nil
}

func unmarshalCategoryCollectionResponse(profile *robot_syncer.Profile) (*portal.SavedCategoryCollection, error) {
	categoryCollection := &category_profile.CategoryCollection{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(profile.Profile), categoryCollection)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal profile %q: %w", profile.Id, err)
	}
	metadata := &portal.Metadata{
		UpdatedAt: &profile.UpdatedAt,
	}
	response := &portal.SavedCategoryCollection{
		Profile:  categoryCollection,
		Metadata: metadata,
	}
	return response, nil
}

func unmarshalSavedCategory(profile *robot_syncer.Profile) (*portal.SavedCategory, error) {
	category := &category_profile.Category{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(profile.Profile), category)
	if err != nil {
		return nil, err
	}

	metadata := &portal.Metadata{
		UpdatedAt: &profile.UpdatedAt,
	}

	response := &portal.SavedCategory{
		Profile:  category,
		Metadata: metadata,
	}
	return response, nil
}

func getCategory(ctx context.Context, robotSyncerClient *robot_syncer.Client, uuid string) (*portal.SavedCategory, error) {
	profile, err := GetProfile(ctx, robotSyncerClient, uuid)
	if err != nil {
		return nil, err
	}
	if profile.ProfileType != frontend.ProfileType_CATEGORY {
		return nil, fmt.Errorf("profile type is not a category: %s", uuid)
	}

	if profile.Deleted {
		return nil, fmt.Errorf("category is deleted: %s", uuid)
	}
	return unmarshalSavedCategory(profile)
}

func expandCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, categoryCollectionResponse *portal.SavedCategoryCollection) (*portal.SavedExpandedCategoryCollection, error) {
	savedCategories := make([]*portal.SavedCategory, len(categoryCollectionResponse.Profile.CategoryIds))
	for index, categoryId := range categoryCollectionResponse.Profile.CategoryIds {
		categoryResponse, err := getCategory(ctx, robotSyncerClient, categoryId)
		if err != nil {
			return nil, err
		}
		savedCategories[index] = categoryResponse
	}

	expandedCategoryCollection := &portal.SavedExpandedCategoryCollection{
		Profile:    categoryCollectionResponse,
		Categories: savedCategories,
	}
	return expandedCategoryCollection, nil
}

func categoryCollectionProfilesToExpanded(ctx context.Context, robotSyncerClient *robot_syncer.Client, profiles []*robot_syncer.Profile) ([]*portal.SavedExpandedCategoryCollection, error) {
	var expandedCategoryCollections []*portal.SavedExpandedCategoryCollection
	for _, profile := range profiles {
		categoryCollectionResponse, err := unmarshalCategoryCollectionResponse(profile)
		if err != nil {
			return nil, err
		}
		expandedCategoryCollection, err := expandCategoryCollection(ctx, robotSyncerClient, categoryCollectionResponse)
		if err != nil {
			return nil, err
		}
		expandedCategoryCollections = append(expandedCategoryCollections, expandedCategoryCollection)
	}

	return expandedCategoryCollections, nil
}

func GetProtectedExpandedCategoryCollections(ctx context.Context, robotSyncerClient *robot_syncer.Client) ([]*portal.SavedExpandedCategoryCollection, error) {
	request := robot_syncer.GetCarbonProvidedProfilesParams{ProfileType: strconv.Itoa(int(frontend.ProfileType_CATEGORY_COLLECTION))}
	profiles, err := robotSyncerClient.GetProtectedProfiles(ctx, request)
	if err != nil {
		return nil, err
	}
	return categoryCollectionProfilesToExpanded(ctx, robotSyncerClient, profiles)
}

func GetCustomerExpandedCategoryCollections(ctx context.Context, robotSyncerClient *robot_syncer.Client, customerID string) ([]*portal.SavedExpandedCategoryCollection, error) {
	request := robot_syncer.GetCustomerProfilesParams{CustomerId: customerID, ProfileType: strconv.Itoa(int(frontend.ProfileType_CATEGORY_COLLECTION))}
	profiles, err := robotSyncerClient.GetCustomerProfiles(ctx, request)
	if err != nil {
		return nil, err
	}
	return categoryCollectionProfilesToExpanded(ctx, robotSyncerClient, profiles)
}

func GetCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, uuid string) (*portal.SavedCategoryCollection, error) {
	profile, err := GetProfile(ctx, robotSyncerClient, uuid)
	if err != nil {
		return nil, err
	}
	if profile.ProfileType != frontend.ProfileType_CATEGORY_COLLECTION {
		return nil, fmt.Errorf("profile type is not a category collection: %s", uuid)
	}

	if profile.Deleted {
		return nil, fmt.Errorf("category collection is deleted: %s", uuid)
	}

	return unmarshalCategoryCollectionResponse(profile)
}

func GetExpandedCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, uuid string) (*portal.SavedExpandedCategoryCollection, error) {
	categoryCollectionResponse, err := GetCategoryCollection(ctx, robotSyncerClient, uuid)
	if err != nil {
		return nil, err
	}
	return expandCategoryCollection(ctx, robotSyncerClient, categoryCollectionResponse)
}

func saveCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, categoryProfile *category_profile.CategoryCollection, deleted bool) (*robot_syncer.Profile, error) {
	profileJSON, err := protoMarshal(categoryProfile)
	if err != nil {
		return nil, err
	}

	customerUUID, err := utils.StringToUUID(categoryProfile.CustomerId)
	if err != nil {
		log.WithError(err).Errorf("Invalid CustomerId: %v", categoryProfile.CustomerId)
		return nil, err
	}

	profileUUID, err := uuid.Parse(categoryProfile.Id)
	if err != nil {
		log.WithError(err).Errorf("Invalid profile Id: %v", categoryProfile.Id)
		return nil, err
	}

	profile := &robot_syncer.Profile{
		CustomerId:  customerUUID,
		Id:          profileUUID,
		ProfileType: frontend.ProfileType_CATEGORY_COLLECTION,
		Profile:     profileJSON,
		Protected:   categoryProfile.Protected,
		UpdatedAt:   time.Now().UnixMilli(),
		Deleted:     deleted,
	}
	err = saveProfile(ctx, robotSyncerClient, profile)
	if err != nil {
		return nil, err
	}
	return profile, nil
}

func saveCategory(ctx context.Context, robotSyncerClient *robot_syncer.Client, category *category_profile.Category, deleted bool) (*robot_syncer.Profile, error) {
	profileJSON, err := protoMarshal(category)
	if err != nil {
		return nil, err
	}

	customerUUID, err := utils.StringToUUID(category.CustomerId)
	if err != nil {
		log.WithError(err).Errorf("Invalid CustomerId: %v", category.CustomerId)
		return nil, err
	}

	profileUUID, err := uuid.Parse(category.Id)
	if err != nil {
		log.WithError(err).Errorf("Invalid profile Id: %v", category.Id)
		return nil, err
	}

	profile := &robot_syncer.Profile{
		CustomerId:  customerUUID,
		Id:          profileUUID,
		ProfileType: frontend.ProfileType_CATEGORY,
		Profile:     profileJSON,
		Protected:   category.Protected,
		UpdatedAt:   time.Now().UnixMilli(),
		Deleted:     deleted,
	}
	err = saveProfile(ctx, robotSyncerClient, profile)
	if err != nil {
		return nil, err
	}
	return profile, nil
}

func saveExpandedCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, veselkaClient *veselkalib.Client, expandedCategoryCollection *portal.UnsavedExpandedCategoryCollection) (*portal.SavedExpandedCategoryCollection, error) {
	// save categories first to minimize transactional failure risk
	savedCategories := make([]*portal.SavedCategory, len(expandedCategoryCollection.Categories))
	for i, unsavedCategory := range expandedCategoryCollection.Categories {
		category := &category_profile.Category{
			Id:         unsavedCategory.Id,
			CustomerId: unsavedCategory.CustomerId,
			Name:       unsavedCategory.Name,
			ChipIds:    unsavedCategory.ChipIds,
			Protected:  unsavedCategory.Protected,
		}
		categoryProfile, err := saveCategory(ctx, robotSyncerClient, category, false)
		if err != nil {
			return nil, fmt.Errorf("error saving category %s: %w", unsavedCategory.Id, err)
		}
		savedCategory, err := unmarshalSavedCategory(categoryProfile)
		if err != nil {
			return nil, err
		}
		savedCategories[i] = savedCategory
	}

	categoryCollectionProfile, err := saveCategoryCollection(ctx, robotSyncerClient, expandedCategoryCollection.Profile, false)
	if err != nil {
		return nil, err
	}
	savedCategoryCollectionResponse, err := unmarshalCategoryCollectionResponse(categoryCollectionProfile)
	if err != nil {
		return nil, err
	}
	response := &portal.SavedExpandedCategoryCollection{
		Profile:    savedCategoryCollectionResponse,
		Categories: savedCategories,
	}
	return response, nil
}

func SaveProtectedExpandedCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, veselkaClient *veselkalib.Client, expandedCategoryCollection *portal.UnsavedExpandedCategoryCollection) (*portal.SavedExpandedCategoryCollection, error) {
	expandedCategoryCollection.Profile.CustomerId = nil
	expandedCategoryCollection.Profile.Protected = true
	for _, category := range expandedCategoryCollection.Categories {
		category.CustomerId = nil
		category.Protected = true
	}
	return saveExpandedCategoryCollection(ctx, robotSyncerClient, veselkaClient, expandedCategoryCollection)
}

func SaveCustomerExpandedCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, veselkaClient *veselkalib.Client, customerID string, expandedCategoryCollection *portal.UnsavedExpandedCategoryCollection) (*portal.SavedExpandedCategoryCollection, error) {
	expandedCategoryCollection.Profile.CustomerId = &customerID
	expandedCategoryCollection.Profile.Protected = false
	for _, category := range expandedCategoryCollection.Categories {
		category.CustomerId = &customerID
		category.Protected = false
	}
	return saveExpandedCategoryCollection(ctx, robotSyncerClient, veselkaClient, expandedCategoryCollection)
}

func DeleteExpandedCategoryCollection(ctx context.Context, robotSyncerClient *robot_syncer.Client, uuid string) error {
	categoryCollectionProfile, err := GetProfile(ctx, robotSyncerClient, uuid)
	if err != nil {
		return err
	}
	if categoryCollectionProfile.ProfileType != frontend.ProfileType_CATEGORY_COLLECTION {
		return fmt.Errorf("profile type is not a category collection: %s", uuid)
	}

	// access the profile contents to get the category profile ids
	categoryCollection, err := unmarshalCategoryCollectionResponse(categoryCollectionProfile)
	if err != nil {
		return err
	}

	// delete the profile if it's not already deleted
	if !categoryCollectionProfile.Deleted {
		shouldDelete := true
		_, err := saveCategoryCollection(ctx, robotSyncerClient, categoryCollection.Profile, shouldDelete)
		if err != nil {
			return err
		}

		// if collection deletion succeeded, perform best effort deletion of the underlying categories
		var errs []error
		for _, categoryId := range categoryCollection.Profile.CategoryIds {
			categoryProfile, err := GetProfile(ctx, robotSyncerClient, categoryId)
			if err != nil {
				errs = append(errs, fmt.Errorf("failed to get category profile %s: %w", categoryId, err))
				continue
			}
			if categoryProfile.ProfileType == frontend.ProfileType_CATEGORY && !categoryProfile.Deleted {
				category, err := unmarshalSavedCategory(categoryProfile)
				if err != nil {
					errs = append(errs, fmt.Errorf("failed to unmarshal category %s: %w", categoryId, err))
					continue
				}

				_, err = saveCategory(ctx, robotSyncerClient, category.Profile, shouldDelete)
				if err != nil {
					errs = append(errs, fmt.Errorf("failed to save deleted category %s: %w", categoryId, err))
					continue
				}
			}
		}
		if len(errs) > 0 {
			log.Warnf("Encountered errors while deleting categories underlying collection %s: %v", uuid, errors.Join(errs...))
		}
	}

	return nil
}
