package jobs

import (
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/crops"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
)

func SerializeJob(ctx *gin.Context, caches cache.Caches, job *models.Job, metrics *models.JobMetrics) *portal.PortalJob {
	result := &portal.PortalJob{
		Db:              models.ModelToPortalDB(job.Base),
		RobotId:         uint64(job.RobotID),
		JobId:           job.JobId,
		Name:            job.Name,
		TimestampMs:     job.TimestampMs,
		BandingProfile:  job.BandingProfile,
		ThinningProfile: job.ThinningProfile,
		StopTimestampMs: job.StopTimestampMs,
		Acreage:         float32(job.ExpectedAcreage),
		Completed:       job.Completed,
		Crop:            crops.CropIDToCommonName(ctx, caches, job.CropId),
		CropId:          job.CropId,
		Almanac:         job.Almanac,
		Discriminator:   job.Discriminator,
	}

	// serialize metrics if provided
	if metrics != nil {
		// Crop differs on job vs. metrics in a significant proportion of jobs;
		// in that case, avoid showing inconsistent data to the user. More info:
		// https://carbonrobotics.atlassian.net/browse/SOFTWARE-1164
		if job.CropId != "" && job.CropId != metrics.CropId {
			newMetrics := *metrics
			newMetrics.CropId = ""
			metrics = &newMetrics
		}
		result.Metrics = SerializeJobMetrics(ctx, caches, job, metrics)
	}

	return result
}

func SerializeJobMetrics(ctx *gin.Context, caches cache.Caches, job *models.Job, metrics *models.JobMetrics) *portal.DailyMetricResponse {
	var rawSerial string
	serial, err := caches.RobotSerialByIDCache.Get(ctx, job.RobotID)
	if err != nil {
		log.WithError(err).Warnf("Failed to get serial for robot %d", job.RobotID)
	} else {
		rawSerial = serial.String()
	}

	response := &portal.DailyMetricResponse{
		Db:      models.ModelToPortalDB(metrics.Base),
		RobotId: uint64(job.RobotID),
		Serial:  rawSerial,

		Crop:    crops.CropIDToCommonName(ctx, caches, metrics.CropId),
		CropId:  metrics.CropId,
		JobId:   job.JobId,
		JobName: job.Name,

		AcresWeeded:                 utils.FloatOr0(metrics.AcresWeeded),
		AvgCropSizeMm:               utils.FloatOr0(metrics.AvgCropSizeMM),
		AvgSpeedMph:                 utils.FloatOr0(metrics.AvgSpeedMPH),
		AvgTargetableReqLaserTime:   metrics.AvgTargetableReqLaserTime,
		AvgUntargetableReqLaserTime: metrics.AvgUntargetableReqLaserTime,
		AvgWeedSizeMm:               utils.FloatOr0(metrics.AvgWeedSizeMM),
		BandingConfigName:           metrics.BandingConfigName,
		BandingEnabled:              metrics.BandingEnabled,
		BandingPercentage:           utils.FloatOr0(metrics.BandingPercentage),
		CoverageSpeedAcresHr:        utils.FloatOr0(metrics.CoverageSpeedAcresHr),
		CropDensitySqFt:             utils.FloatOr0(metrics.CropDensitySqFt),
		DistanceWeededMeters:        utils.FloatOr0(metrics.DistanceWeededMeters),
		KeptCrops:                   metrics.KeptCrops,
		KilledWeeds:                 metrics.KilledWeeds,
		MissedCrops:                 metrics.MissedCrops,
		MissedWeeds:                 metrics.MissedWeeds,
		NotThinning:                 metrics.NotThinning,
		NotWeeding:                  metrics.NotWeeding,
		SkippedCrops:                metrics.SkippedCrops,
		SkippedWeeds:                metrics.SkippedWeeds,
		ThinnedCrops:                metrics.ThinnedCrops,
		ThinningEfficiency:          utils.FloatOr0(metrics.ThinningEfficiency),
		TimeEfficiency:              utils.FloatOr0(metrics.TimeEfficiency),
		TotalCrops:                  metrics.TotalCrops,
		TotalWeeds:                  metrics.TotalWeeds,
		UptimeSeconds:               utils.FloatOr0(metrics.UptimeSeconds),
		ValidCrops:                  metrics.ValidCrops,
		WeedDensitySqFt:             utils.FloatOr0(metrics.WeedDensitySqFt),
		WeedingEfficiency:           utils.FloatOr0(metrics.WeedingEfficiency),
		WeedingUptimeSeconds:        utils.FloatOr0(metrics.WeedingUptimeSeconds),
		WeedsTypeCountBroadleaf:     metrics.WeedsTypeCountBroadleaf,
		WeedsTypeCountGrass:         metrics.WeedsTypeCountGrass,
		WeedsTypeCountOffshoot:      metrics.WeedsTypeCountOffshoot,
		WeedsTypeCountPurslane:      metrics.WeedsTypeCountPurslane,
	}

	return response
}
