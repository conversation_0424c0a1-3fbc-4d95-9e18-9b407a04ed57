package jobs

import (
	"context"

	"github.com/golang/protobuf/jsonpb"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/metrics/certified"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
	"github.com/carbonrobotics/protos/golang/generated/proto/util"
)

func UploadJob(context context.Context, db *gorm.DB, caches cache.Caches, req *portal.UploadJobRequest) (*util.Empty, error) {
	var robot models.Robot
	err := db.Where("serial = ?", req.Robot).First(&robot).Error
	if err != nil {
		return nil, err
	}

	var job models.Job
	err = db.Where("job_id = ?", req.Job.JobDescription.JobId).First(&job).Error
	isNew := err != nil

	job.RobotID = robot.Base.ID
	job.JobId = req.Job.JobDescription.JobId
	job.Name = req.Job.JobDescription.Name
	job.TimestampMs = req.Job.JobDescription.TimestampMs
	job.BandingProfile = req.Job.BandingProfile
	job.ThinningProfile = req.Job.ThinningProfile
	job.StopTimestampMs = req.Job.StopTimeMs
	job.ExpectedAcreage = float64(req.Job.ExpectedAcreage)
	job.Completed = req.Job.Completed
	job.Almanac = req.Job.Almanac
	job.Discriminator = req.Job.Discriminator
	job.CropId = req.Job.CropId

	if isNew {
		err = db.Create(&job).Error
	} else {
		err = db.Save(&job).Error
	}
	if err == nil {
		caches.JobNameByUUIDCache.Set(context, job.JobId, &job.Name)
	}
	return &util.Empty{}, err
}

func UploadJobConfigDump(db *gorm.DB, req *portal.UploadJobConfigDumpRequest) (*util.Empty, error) {
	var dump models.JobConfigDump
	err := db.Where("job_id = ?", req.JobId).First(&dump).Error
	newJob := err != nil

	m := jsonpb.Marshaler{}
	str, err := m.MarshalToString(req.RootConfig)
	if err != nil {
		return nil, err
	}

	dump.Configs = str
	dump.JobId = req.JobId

	if newJob {
		result := db.Create(&dump)
		return &util.Empty{}, result.Error
	} else {
		result := db.Save(&dump)
		return &util.Empty{}, result.Error
	}
}

func UploadJobMetrics(db *gorm.DB, req *portal.UploadJobMetricsRequest) (*util.Empty, error) {
	var robot *models.Robot
	job, err := GetJob(db, req.JobId)
	if err != nil {
		log.WithError(err).Warnf("Failed to get job %s", req.JobId)
	} else {
		robot, err = robots.GetRobot(db, job.RobotID, nil)
		if err != nil {
			log.WithError(err).Warnf("Failed to get robot %d", job.RobotID)
		}
	}
	var metrics models.JobMetrics
	err = db.Where("job_id = ?", req.JobId).First(&metrics).Error
	newJob := err != nil

	parsed := certified.ParseRawMetrics(req.JobMetrics.Metrics)
	// intermediate variables
	weedingUptimeHours := certified.SetMetricWeedingUptime(parsed)
	squareFeetWeeded := certified.SetMetricSquareFeetWeeded(parsed)

	// acres weeded
	metrics.AcresWeeded = parsed.AcresWeeded

	// average speed
	metrics.AvgSpeedMPH = certified.SetAverageSpeed(parsed, weedingUptimeHours)
	metrics.DistanceWeededMeters = parsed.DistanceWeededMeters

	// crop and weed radii
	metrics.AvgCropSizeMM = parsed.AvgCropSizeMM
	metrics.AvgWeedSizeMM = parsed.AvgWeedSizeMM

	// laser shoot time
	metrics.AvgTargetableReqLaserTime = parsed.AvgTargetableReqLaserTime
	metrics.AvgUntargetableReqLaserTime = parsed.AvgUntargetableReqLaserTime

	// banding config
	metrics.BandingConfigName = parsed.BandingConfigName

	// banding enabled
	metrics.BandingEnabled = parsed.BandingEnabled

	// coverage speed
	metrics.CoverageSpeedAcresHr = certified.SetCoverageSpeed(parsed, weedingUptimeHours)

	// active working time while embeddings are enabled
	metrics.EmbeddingsActiveUptimeSeconds = parsed.EmbeddingsActiveUptimeSeconds

	// killed weeds
	metrics.KilledWeeds = parsed.KilledWeeds

	// missed weeds
	metrics.MissedWeeds = parsed.MissedWeeds

	metrics.NotWeeding = parsed.NotWeeding

	// skipped weeds
	metrics.SkippedWeeds = parsed.SkippedWeeds

	// time efficiency
	metrics.TimeEfficiency = certified.SetTimeEfficiency(parsed)

	// total weeds
	metrics.TotalWeeds = parsed.TotalWeeds

	// uptime seconds
	metrics.UptimeSeconds = parsed.UptimeSeconds

	metrics.ThinnedCrops = parsed.ThinnedCrops
	metrics.SkippedCrops = parsed.SkippedCrops
	metrics.MissedCrops = parsed.MissedCrops
	metrics.TotalCrops = parsed.TotalCrops
	metrics.ValidCrops = parsed.ValidCrops
	metrics.KeptCrops = parsed.KeptCrops
	metrics.NotThinning = parsed.NotThinning

	// density
	metrics.WeedDensitySqFt = certified.SetWeedDensity(parsed, squareFeetWeeded)
	metrics.CropDensitySqFt = certified.SetCropDensity(parsed, squareFeetWeeded)

	// weeding efficiency
	metrics.WeedingEfficiency = certified.SetWeedingEfficiency(parsed)

	// weeding uptime seconds
	metrics.WeedingUptimeSeconds = parsed.WeedingUptimeSeconds

	// weed types
	metrics.WeedsTypeCountBroadleaf = parsed.WeedsTypeCountBroadleaf
	metrics.WeedsTypeCountGrass = parsed.WeedsTypeCountGrass
	metrics.WeedsTypeCountPurslane = parsed.WeedsTypeCountPurslane
	metrics.WeedsTypeCountOffshoot = parsed.WeedsTypeCountOffshoot

	metrics.BandingPercentage = parsed.BandingPercentage

	// thinning efficiency
	metrics.ThinningEfficiency = certified.SetThinningEfficiency(parsed)

	metrics.JobId = req.JobId

	if robot != nil {
		metrics.CropId = robot.CropID
	}

	if newJob {
		result := db.Create(&metrics)
		return &util.Empty{}, result.Error
	} else {
		result := db.Save(&metrics)
		return &util.Empty{}, result.Error
	}
}
