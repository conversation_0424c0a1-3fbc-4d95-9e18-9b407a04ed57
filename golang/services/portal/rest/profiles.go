package rest

import (
	"fmt"
	"net/http"

	"github.com/auth0/go-auth0/management"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	veselkalib "github.com/carbonrobotics/cloud/golang/pkg/veselka"
	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/customers"
	"github.com/carbonrobotics/cloud/golang/services/portal/middleware"
	"github.com/carbonrobotics/cloud/golang/services/portal/profiles"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/rosy_profiles"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/almanac"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"
	"github.com/carbonrobotics/protos/golang/generated/proto/target_velocity_estimator"
)

func RegisterProfiles(router *gin.RouterGroup, env *utils.Environment, auth0Middleware gin.HandlerFunc, domain string, auth0Client *management.Management, database *gorm.DB, caches cache.Caches, robotSyncerClient *robot_syncer.Client, veselkaClient *veselkalib.Client) {
	routes := router.Group("/profiles")
	routes.Use(auth0Middleware)
	{
		routes.GET("almanacs/robots/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get almanacs"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to get almanacs for %s", serial)

			if !profiles.CanReadAlmanacs(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			response, err := profiles.GetAlmanacs(database, robot.Base.ID)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanacs not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("almanacs/global", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get Carbon provided almanacs"

			if !profiles.CanReadAlmanacTemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			response, err := profiles.GetCarbonProvidedAlmanacs(database)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanacs not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("almanacs/global/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to get Carbon provided almanac %s", uuid)

			if !profiles.CanReadAlmanacTemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			response, err := profiles.GetCarbonProvidedAlmanac(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanac not found")
				return
			}

			ReturnProtoJSON(context, response, &errorPrefix)
		})

		routes.DELETE("almanacs/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to delete Carbon provided almanac %s", uuid)

			if !profiles.CanUpdateAlmanacTemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			err := profiles.DeleteCarbonProvidedProfile(context, database, caches, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}

			context.JSON(http.StatusOK, gin.H{})
		})

		routes.POST("almanacs/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to update Carbon provided almanac %s", uuid)

			if !profiles.CanUpdateAlmanacTemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			almanac := &almanac.AlmanacConfig{}
			err := ReadProtoJSON(context, almanac)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			err = profiles.SaveCarbonProvidedAlmanac(context, database, caches, almanac)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Update failed")
				return
			}

			ReturnProtoJSON(context, almanac, &errorPrefix)
		})

		routes.GET("almanacs/profiles/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to get almanac %s", uuid)

			almanac, profile, err := profiles.GetAlmanac(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanac not found")
				return
			}

			if !profiles.CanReadProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			ReturnProtoJSON(context, almanac, &errorPrefix)
		})

		routes.DELETE("almanacs/profiles/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to delete almanac %s", uuid)

			_, profile, err := profiles.GetAlmanac(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanac not found")
				return
			}

			if !profiles.CanUpdateProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			err = profiles.DeleteProfile(context, database, caches, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}

			context.JSON(http.StatusOK, gin.H{})
		})

		routes.POST("almanacs/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to create almanac"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to create almanac for %s", serial)

			if !profiles.CanUpdateAlmanacs(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			almanac := &almanac.AlmanacConfig{}
			err = ReadProtoJSON(context, almanac)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			errorPrefix = fmt.Sprintf("Failed to create %s almanac %s", serial, almanac.Name)

			err = profiles.SaveAlmanac(context, database, caches, robot.Base.ID, almanac, nil)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Save failed")
				return
			}

			ReturnProtoJSON(context, almanac, &errorPrefix)
		})

		routes.GET("discriminators/robots/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get discriminators"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to get discriminators for %s", serial)

			if !profiles.CanReadDiscriminators(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			response, err := profiles.GetDiscriminators(database, robot.Base.ID)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Discriminators not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("discriminators/profiles/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to get discriminator %s", uuid)

			discriminator, profile, err := profiles.GetDiscriminator(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Discriminator not found")
				return
			}

			if !profiles.CanReadProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			ReturnProtoJSON(context, discriminator, &errorPrefix)
		})

		routes.DELETE("discriminators/profiles/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to delete discriminator %s", uuid)

			_, profile, err := profiles.GetDiscriminator(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Discriminator not found")
				return
			}

			if !profiles.CanUpdateProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			err = profiles.DeleteProfile(context, database, caches, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}

			context.JSON(http.StatusOK, gin.H{})
		})

		routes.POST("discriminators/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to save discriminator"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to save discriminator for %s", serial)

			if !profiles.CanUpdateDiscriminators(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			discriminator := &almanac.DiscriminatorConfig{}
			err = ReadProtoJSON(context, discriminator)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			errorPrefix = fmt.Sprintf("Failed to save %s discriminator %s", serial, discriminator.Name)

			err = profiles.SaveDiscriminator(context, database, caches, robot.Base.ID, discriminator)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Failed to save")
				return
			}

			ReturnProtoJSON(context, discriminator, &errorPrefix)
		})

		routes.GET("modelinators/robots/:serial/crops/:cropId", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get modelinators"
			cropID := context.Param("cropId")

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to get modelinators for %s", serial)

			if !profiles.CanReadModelinators(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			response, err := profiles.GetModelinatorsByCrop(database, robot.Base.ID, cropID)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Discriminators not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.POST("modelinators/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to save modelinator"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to save modelinator for %s", serial)

			if !profiles.CanUpdateModelinators(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			modelinator := &almanac.ModelinatorConfig{}
			err = ReadProtoJSON(context, modelinator)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			errorPrefix = fmt.Sprintf("Failed to save %s modelinator %s-%s", serial, modelinator.CropId, modelinator.ModelId)

			err = profiles.SaveModelinator(context, database, caches, robot.Base.ID, robot.Serial, modelinator)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Failed to save")
				return
			}

			ReturnProtoJSON(context, modelinator, &errorPrefix)
		})

		routes.GET("velocity/global", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get Carbon provided velocity estimators"
			if !profiles.CanReadTVETemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			response, err := profiles.GetCarbonProvidedTargetVelocityEstimators(database)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Almanacs not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("velocity/global/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to get Carbon provided velocity estimator %s", uuid)

			if !profiles.CanReadTVETemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			response, err := profiles.GetCarbonProvidedTargetVelocityEstimator(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "target velocity estimator not found")
				return
			}

			ReturnProtoJSON(context, response, &errorPrefix)
		})

		routes.POST("velocity/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to update Carbon provided velocity estimator %s", uuid)

			if !profiles.CanUpdateTVETemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			targetVelocityEstimator := &target_velocity_estimator.TVEProfile{}
			err := ReadProtoJSON(context, targetVelocityEstimator)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			err = profiles.SaveCarbonProvidedTargetVelocityEstimator(context, database, caches, targetVelocityEstimator)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Update failed")
				return
			}

			ReturnProtoJSON(context, targetVelocityEstimator, &errorPrefix)
		})

		routes.DELETE("velocity/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to delete Carbon provided velocity estimator %s", uuid)

			if !profiles.CanUpdateTVETemplates(context, &errorPrefix) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			err := profiles.DeleteCarbonProvidedProfile(context, database, caches, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}

			context.JSON(http.StatusOK, gin.H{})
		})

		routes.GET("velocity/robots/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get target velocity estimators"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to get target velocity estimators for %s", serial)

			if !profiles.CanReadTVE(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			response, err := profiles.GetTargetVelocityEstimators(database, robot.Base.ID)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Target velocity estimators not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("velocity/profiles/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to get target velocity estimator %s", uuid)

			response, profile, err := profiles.GetTargetVelocityEstimator(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Target velocity estimator not found")
				return
			}

			if !profiles.CanReadProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			ReturnProtoJSON(context, response, &errorPrefix)
		})

		routes.POST("velocity/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to save target velocity estimator"

			serial, err := carbon.ParseSerial(context.Param("serial"))
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", context.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to save target velocity estimator for %s", serial)

			if !profiles.CanUpdateTVE(context, caches, &errorPrefix, serial) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			robot, err := TryGetRobotBySerial(context, database, caches, serial)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Robot not found")
				return
			}

			targetVelocityEstimator := &target_velocity_estimator.TVEProfile{}
			err = ReadProtoJSON(context, targetVelocityEstimator)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			errorPrefix = fmt.Sprintf("Failed to save target velocity estimator %s on %s", targetVelocityEstimator.Name, serial)

			err = profiles.SaveTargetVelocityEstimator(context, database, caches, robot.Base.ID, robot.Serial, targetVelocityEstimator, nil)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Failed to save")
				return
			}

			ReturnProtoJSON(context, targetVelocityEstimator, &errorPrefix)
		})

		routes.DELETE("velocity/profiles/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			uuid := context.Param("uuid")
			errorPrefix := fmt.Sprintf("Failed to delete target velocity estimator %s", uuid)

			_, profile, err := profiles.GetTargetVelocityEstimator(database, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Target velocity estimator not found")
				return
			}

			if !profiles.CanUpdateProfile(context, caches, &errorPrefix, profile) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			err = profiles.DeleteProfile(context, database, caches, uuid)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
				return
			}

			context.JSON(http.StatusOK, gin.H{})
		})

		routes.GET("category-collections/global", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
			errorPrefix := "Failed to get Carbon provided category collections"
			if !rosy_profiles.CanReadCategoryCollectionTemplates(context) {
				ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			response, err := rosy_profiles.GetProtectedExpandedCategoryCollections(context, robotSyncerClient)
			if err != nil {
				ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Category collection profiles were not found")
				return
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})
	}

	routes.GET("category-collections/robots/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		inputSerial := context.Param("serial")
		errorPrefix := fmt.Sprintf("Failed to get category collections for %s", inputSerial)

		serial, err := carbon.ParseSerial(inputSerial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		if !rosy_profiles.CanReadCustomerCategoryCollections(context) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		robotIds, err := robots.SerialsToRobotsIds(context, caches, []*carbon.ValidSerial{serial})
		if err != nil || len(robotIds) != 1 {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		customer, err := customers.GetCustomerByRobotId(database, robotIds[0])
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Customer associated with robot not found")
			return
		}

		response, err := rosy_profiles.GetCustomerExpandedCategoryCollections(context, robotSyncerClient, customer.Uuid.String())
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Category collections not found")
			return
		}

		ReturnProtoJSONList(context, response, &errorPrefix)
	})

	routes.GET("category-collections/global/:uuid", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to get Carbon provided category collection %s", uuid)

		if !rosy_profiles.CanReadCategoryCollectionTemplates(context) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		response, err := rosy_profiles.GetExpandedCategoryCollection(context, robotSyncerClient, uuid)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Category collection or categories not found")
			return
		}

		ReturnProtoJSON(context, response, &errorPrefix)
	})

	routes.GET("category-collections/:uuid/robots/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		inputSerial := context.Param("serial")
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to get category collection %s and categories for %s", uuid, inputSerial)

		serial, err := carbon.ParseSerial(inputSerial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		robotIds, err := robots.SerialsToRobotsIds(context, caches, []*carbon.ValidSerial{serial})
		if err != nil || len(robotIds) != 1 {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid serial")
			return
		}

		customer, err := customers.GetCustomerByRobotId(database, robotIds[0])
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Customer associated with robot not found")
			return
		}

		response, err := rosy_profiles.GetExpandedCategoryCollection(context, robotSyncerClient, uuid)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Category collection or categories not found")
			return
		}

		if !rosy_profiles.CanReadCategoryCollection(context, &customer.Uuid, response.Profile.Profile) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		ReturnProtoJSON(context, response, &errorPrefix)
	})

	routes.POST("category-collections/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to update Carbon provided category collection and categories %s", uuid)

		if !rosy_profiles.CanUpdateCategoryCollectionTemplates(context, robotSyncerClient, uuid) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		expandedCategoryCollection := &portal.UnsavedExpandedCategoryCollection{}
		err := ReadProtoJSON(context, expandedCategoryCollection)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
			return
		}

		updatedExpandedCategoryCollection, err := rosy_profiles.SaveProtectedExpandedCategoryCollection(context, robotSyncerClient, veselkaClient, expandedCategoryCollection)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Update failed")
			return
		}

		ReturnProtoJSON(context, updatedExpandedCategoryCollection, &errorPrefix)
	})

	routes.POST("category-collections/:uuid/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		inputSerial := context.Param("serial")
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to update category collection %s and categories for %s", uuid, inputSerial)

		serial, err := carbon.ParseSerial(inputSerial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		robotIds, err := robots.SerialsToRobotsIds(context, caches, []*carbon.ValidSerial{serial})
		if err != nil || len(robotIds) != 1 {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		customer, err := customers.GetCustomerByRobotId(database, robotIds[0])
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Customer associated with robot not found")
			return
		}

		if !rosy_profiles.CanUpdateCustomerCategoryCollection(context, robotSyncerClient, customer.Uuid, uuid) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		expandedCategoryCollection := &portal.UnsavedExpandedCategoryCollection{}
		err = ReadProtoJSON(context, expandedCategoryCollection)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
			return
		}

		updatedExpandedCategoryCollection, err := rosy_profiles.SaveCustomerExpandedCategoryCollection(context, robotSyncerClient, veselkaClient, customer.Uuid.String(), expandedCategoryCollection)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Update failed")
			return
		}

		ReturnProtoJSON(context, updatedExpandedCategoryCollection, &errorPrefix)
	})

	routes.DELETE("category-collections/global/:uuid", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to delete Carbon provided category collection and categories %s", uuid)

		if !rosy_profiles.CanUpdateCategoryCollectionTemplates(context, robotSyncerClient, uuid) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		err := rosy_profiles.DeleteExpandedCategoryCollection(context, robotSyncerClient, uuid)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
			return
		}

		context.JSON(http.StatusOK, gin.H{})
	})

	routes.DELETE("category-collections/:uuid/robots/:serial", requireDBWritable(env), middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(context *gin.Context) {
		inputSerial := context.Param("serial")
		uuid := context.Param("uuid")
		errorPrefix := fmt.Sprintf("Failed to delete category collection %s and categories for %s", uuid, inputSerial)

		serial, err := carbon.ParseSerial(inputSerial)
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		robotIds, err := robots.SerialsToRobotsIds(context, caches, []*carbon.ValidSerial{serial})
		if err != nil || len(robotIds) != 1 {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", inputSerial))
			return
		}

		customer, err := customers.GetCustomerByRobotId(database, robotIds[0])
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Customer associated with robot not found")
			return
		}

		if !rosy_profiles.CanUpdateCustomerCategoryCollection(context, robotSyncerClient, customer.Uuid, uuid) {
			ReturnError(context, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		err = rosy_profiles.DeleteExpandedCategoryCollection(context, robotSyncerClient, uuid)
		if err != nil {
			ReturnError(context, http.StatusInternalServerError, err, &errorPrefix, "Delete failed")
			return
		}

		context.JSON(http.StatusOK, gin.H{})
	})
}
