package rest

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ringsaturn/tzf"
	log "github.com/sirupsen/logrus"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	portalerrors "github.com/carbonrobotics/cloud/golang/services/portal/errors"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
)

func ReadProtoJSON(context *gin.Context, result proto.Message) error {
	jsonBody, err := context.GetRawData()
	if err != nil {
		return err
	}
	err = protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}.Unmarshal(jsonBody, result)
	if err != nil {
		return portalerrors.Public(err)
	}
	return nil
}

func ReturnError(context *gin.Context, status int, err error, errorPrefix *string, errorMessage string) {
	var fullMessage string
	if errorPrefix == nil {
		fullMessage = errorMessage
	} else {
		fullMessage = fmt.Sprintf("%s: %s", *errorPrefix, errorMessage)
	}

	select {
	case <-context.Done():
		log.Debugf("Context done: %s", fullMessage)
		return
	default:
	}

	if err == nil {
		log.Error(fullMessage)
	} else {
		log.WithError(err).Error(fullMessage)
	}

	var publicErr portalerrors.PublicError
	if errors.As(err, &publicErr) {
		fullMessage = fmt.Sprintf("%s: %s", fullMessage, publicErr.PublicError())
	}
	context.JSON(status, gin.H{"error": fullMessage})
}

// ReturnProtoJSON serializes the given protobuf message as JSON and sends it
// as an HTTP 200 response, or sends an HTTP 500 response with an error message
// if serialization fails. It always writes to the response body, and thus
// should generally be the last call in a request handler.
//
// This function uses protojson encoding with options set to be compatible with
// our "ts2" generated bindings, so frontend endpoints should import proto
// definitions from that module tree. Compared to encoding/json, this serialzes
// oneofs correctly, always populates primitives and lists even if they have
// default values (zero/empty), and uses strings for 64-bit integer types so
// that they can be reliably parsed in JavaScript.
func ReturnProtoJSON(ctx *gin.Context, msg proto.Message, errorPrefix *string) error {
	response, err := marshalProtoJSON(ctx, msg, errorPrefix)
	if err != nil {
		return err
	}
	ctx.JSON(http.StatusOK, response)
	return nil
}

// ReturnProtoJSONList is like ReturnProtoJSON but accepts a slice of protobuf
// messages rather than a single message. See ReturnProtoJSON for details.
func ReturnProtoJSONList[T proto.Message](ctx *gin.Context, msgs []T, errorPrefix *string) error {
	response := make([]json.RawMessage, len(msgs))
	for i, msg := range msgs {
		encoded, err := marshalProtoJSON(ctx, msg, errorPrefix)
		if err != nil {
			return fmt.Errorf("at index %v: %w", i, err)
		}
		response[i] = encoded
	}
	ctx.JSON(http.StatusOK, response)
	return nil
}

func marshalProtoJSON(ctx *gin.Context, msg proto.Message, errorPrefix *string) (response json.RawMessage, err error) {
	encoded, err := protojson.Marshal(msg)
	if err != nil {
		ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "Failed to marshal response proto")
		return nil, err
	}
	return json.RawMessage(encoded), nil
}

func GetTimeBounds(context *gin.Context, timezoneFinder tzf.F, robot *models.Robot, errorPrefix string, date string) (*time.Time, *time.Time, error) {
	inputTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid date")
		return nil, nil, err
	}
	year, month, day := inputTime.Date()
	var location *time.Location
	if robot.Location != nil {
		location, err = time.LoadLocation(timezoneFinder.GetTimezoneName(robot.Location.Y, robot.Location.X))
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid location")
			return nil, nil, err
		}
	}
	if location == nil {
		location, err = time.LoadLocation("UTC")
		if err != nil {
			ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Unable to load default location")
			return nil, nil, err
		}
	}

	startTime := time.Date(year, month, day, 0, 0, 0, 0, location)
	endTime := time.Date(year, month, day, 23, 59, 59, 999999999, location)

	return &startTime, &endTime, nil
}

func IsGrpcRequest(request *http.Request) bool {
	contentType := request.Header.Get("Content-Type")
	return contentType == "application/grpc"
}

func ParseCommaSeparatedParams(str string) []string {
	parts := strings.Split(str, ",")
	var result []string
	for _, item := range parts {
		trimmed := strings.TrimSpace(item)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

func ParseDateRange(dateRangeStr string) (*time.Time, *time.Time, error) {
	parts := strings.Split(dateRangeStr, ",")
	if len(parts) != 2 {
		return nil, nil, fmt.Errorf("invalid date range format: %s", dateRangeStr)
	}

	var beginDate, endDate *time.Time

	// begin date
	if strings.TrimSpace(parts[0]) != "" {
		b, err := time.Parse(time.RFC3339, strings.TrimSpace(parts[0]))
		if err != nil {
			return nil, nil, fmt.Errorf("invalid begin date: %s", parts[0])
		}
		beginDate = &b
	}

	// end date
	if strings.TrimSpace(parts[1]) != "" {
		e, err := time.Parse(time.RFC3339, strings.TrimSpace(parts[1]))
		if err != nil {
			return nil, nil, fmt.Errorf("invalid end date: %s", parts[1])
		}
		endDate = &e
	}

	return beginDate, endDate, nil
}

// https://developers.google.com/workspace/slides/api/guides/field-masks
func fieldInMask(mask *fieldmaskpb.FieldMask, field string) bool {
	for _, path := range mask.GetPaths() {
		if path == field {
			return true
		}
	}
	return false
}

// RequireDBWritable fails all requests when env.DBReadOnly() is true.
func requireDBWritable(env *utils.Environment) gin.HandlerFunc {
	if env.DBReadOnly() {
		return func(ctx *gin.Context) {
			ctx.AbortWithStatusJSON(http.StatusServiceUnavailable, gin.H{"error": "Ops Center is in read-only mode for maintenance"})
		}
	}
	return func(ctx *gin.Context) { ctx.Next() }
}
