package rest

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/carbonrobotics/cloud/golang/services/portal/middleware"
	"github.com/carbonrobotics/cloud/golang/services/portal/robots"
	"github.com/carbonrobotics/cloud/golang/services/portal/rosy_profiles"
	"github.com/carbonrobotics/cloud/golang/services/portal/utils"
	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	veselkalib "github.com/carbonrobotics/cloud/golang/pkg/veselka"
	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/crops"
	"github.com/carbonrobotics/cloud/golang/services/portal/images"
	"github.com/carbonrobotics/cloud/golang/services/portal/models"
	"github.com/carbonrobotics/protos/golang/generated/proto/veselka/prediction_point"
)

type CropModel struct {
	HasModel bool   `json:"hasModel"`
	Name     string `json:"name"`
}

func RegisterVeselka(router *gin.RouterGroup, auth0Middleware gin.HandlerFunc, domain string, auth0Client *management.Management, veselkaClient *veselkalib.Client, database *gorm.DB, caches cache.Caches, s3 awslib.GetObjectRequester, robotSyncerClient *robot_syncer.Client) {
	routes := router.Group("/veselka")
	routes.Use(auth0Middleware)
	{
		routes.GET("/uploads/:serial", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
			errorPrefix := "Failed to get images"

			serial, err := carbon.ParseSerial(ctx.Param("serial"))
			if err != nil {
				ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", ctx.Param("serial")))
				return
			}
			errorPrefix = fmt.Sprintf("Failed to get images for %s", serial)

			if !images.CanRead(ctx, caches, &errorPrefix, serial) {
				ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}

			params := veselkalib.ListImageParameters{
				RobotId: serial.String(),
			}
			rawPage, hasPage := ctx.GetQuery("page")
			page, err := strconv.ParseInt(rawPage, 10, 64)
			if !hasPage || err != nil {
				log.WithError(err).Errorf("Failed to parse page: %s", rawPage)
				params.Page = 1
			} else {
				params.Page = page
			}
			rawLimit, hasLimit := ctx.GetQuery("limit")
			limit, err := strconv.ParseInt(rawLimit, 10, 64)
			if !hasLimit || err != nil {
				log.WithError(err).Errorf("Failed to parse limit: %s", rawLimit)
				params.PerPage = 100
			} else {
				params.PerPage = limit
			}

			rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 15*time.Second)
			defer cancel()
			imageList, err := veselkaClient.ListImages(rqCtx, params)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Images not found")
				return
			}

			response := []*portal.Image{}
			for _, image := range imageList {
				serialized := images.SerializeImage(ctx, caches, s3, image)
				if serialized != nil {
					response = append(response, serialized)
				}
			}

			ReturnProtoJSONList(ctx, response, &errorPrefix)
		})

		routes.GET("/models/crops", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
			errorPrefix := "Failed to get crops"
			rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 15*time.Second)
			defer cancel()
			parameters := veselkalib.ListCropsParameters{}
			parameters.SetArchived(true)
			cropList, err := veselkaClient.ListCrops(rqCtx, parameters)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Crops not found")
				return
			}

			response := make([]*portal.Crop, len(cropList))
			for index, crop := range cropList {
				response[index] = crops.SerializeCrop(crop)
			}

			ReturnProtoJSONList(ctx, response, &errorPrefix)
		})

		routes.GET("/models/:id", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
			id := ctx.Param("id")
			errorPrefix := fmt.Sprintf("Failed to get model %s", id)
			rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 15*time.Second)
			defer cancel()
			modelList, err := veselkaClient.GetModels(rqCtx, veselkalib.GetModelsParameters{
				IDs: []string{id},
			})
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Model not found")
				return
			}
			for _, model := range modelList {
				if model.ID == id {
					response := models.SerializeModel(model)
					ReturnProtoJSON(ctx, response, &errorPrefix)
					return
				}
			}
			ReturnError(ctx, http.StatusNotFound, nil, &errorPrefix, "Model not found")
		})

		routes.GET("/models", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
			errorPrefix := "Failed to get models"
			crops, ok := ctx.GetQuery("crops")
			if !ok || crops == "" {
				ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Invalid crops")
				return
			}

			rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 15*time.Second)
			defer cancel()
			file, err := veselkaClient.DownloadModelsForCrops(rqCtx, veselkalib.DownloadModelsForCropsParameters{
				Environment: "production",
				Crops:       crops,
			})
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Models not found")
				return
			}
			ctx.Header("Content-Length", strconv.Itoa(len(file)))
			ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=carbon-detector_%s_%s.zip", strings.Join(strings.Split(crops, ","), "-"), time.Now().Format("2006-01-02-15-04")))
			ctx.Data(http.StatusOK, "application/octet-stream", file)
		})
	}

	routes.GET("/prediction-points", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
		var filters struct {
			Page          int    `form:"page"`
			PageSize      int    `form:"pageSize"`
			SortBy        string `form:"sortBy"`
			SortDirection string `form:"sortDirection"`

			IDs                string `form:"ids"`
			CapturedAt         string `form:"capturedAt"`
			Radius             string `form:"radius"`
			Crops              string `form:"crops"`
			Robots             string `form:"robots"`
			SessionID          string `form:"sessionId"`
			CategoryIDs        string `form:"categoryIds"`
			UploadedByOperator *bool  `form:"uploadedByOperator"`
		}

		errorPrefix := "Failed to get prediction points"
		if err := ctx.ShouldBindQuery(&filters); err != nil {
			ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, err.Error())
			return
		}

		if filters.Page < 1 {
			ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Invalid page")
			return
		}

		if filters.PageSize < 1 || filters.PageSize > 100 {
			ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Invalid page size")
			return
		}

		if filters.SortDirection != "" && !slices.Contains([]string{"ASC", "DESC"}, filters.SortDirection) {
			ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Invalid sort direction")
		}

		var capturedAt [2]time.Time
		var timeErr error
		parts := strings.SplitN(filters.CapturedAt, ",", 2)

		parseTime := func(raw string) (parsed time.Time) {
			raw = strings.TrimSpace(raw)
			if raw == "" {
				return time.Time{}
			}
			parsed, timeErr = time.Parse(time.RFC3339, raw)
			return parsed
		}

		capturedAt[0] = parseTime(parts[0])
		if len(parts) == 2 {
			capturedAt[1] = parseTime(parts[1])
		}
		if timeErr != nil {
			ReturnError(ctx, http.StatusBadRequest, timeErr, &errorPrefix, fmt.Sprintf("Invalid captured at"))
			return
		}

		var radius [2]*float64
		var radiusErr error
		parts = strings.SplitN(filters.Radius, ",", 2)
		parseFloat := func(raw string) *float64 {
			raw = strings.TrimSpace(raw)
			if raw == "" {
				return nil
			}
			parsed, err := strconv.ParseFloat(raw, 64)
			if err != nil {
				radiusErr = err
				return nil
			}
			return &parsed
		}

		radius[0] = parseFloat(parts[0])
		if len(parts) == 2 {
			radius[1] = parseFloat(parts[1])
		}

		if radiusErr != nil {
			ReturnError(ctx, http.StatusBadRequest, radiusErr, &errorPrefix, "Invalid radius")
			return
		}

		var crops []string
		if filters.Crops != "" {
			crops = strings.Split(filters.Crops, ",")
		}

		var robotFilters []string
		if filters.Robots != "" {
			robotFilters = strings.Split(filters.Robots, ",")
			for _, rawSerial := range robotFilters {
				_, err := carbon.ParseSerial(rawSerial)
				if err != nil {
					ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid robot serial: %s", rawSerial))
					return
				}
			}
		}

		var robotParams []string
		availableRobotList, err := TryGetRobots(ctx, database, caches, true, true)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Robots not found")
			return
		}
		availableRobotSerials := make([]string, len(availableRobotList))
		for i, robot := range availableRobotList {
			availableRobotSerials[i] = robot.Serial
		}

		if len(robotFilters) > 0 {
			availableRobotSet := make(map[string]struct{}, len(availableRobotSerials))
			for _, serial := range availableRobotSerials {
				availableRobotSet[serial] = struct{}{}
			}
			for _, filter := range robotFilters {
				if _, exists := availableRobotSet[filter]; !exists {
					ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User is not authorized to view one or more specified robots")
					return
				}
			}
		}

		userAccess, err := middleware.GetUserAccess(ctx, portal.PermissionResource_robots)
		if userAccess.CanReadAll {
			robotParams = robotFilters
		} else if userAccess.CanReadCustomer {
			if len(robotFilters) == 0 {
				// If no filters are provided, use all available robots
				robotParams = availableRobotSerials
			} else {
				robotParams = robotFilters
			}
		} else {
			ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
			return
		}

		var IDs []string
		if filters.IDs != "" {
			IDs = strings.Split(filters.IDs, ",")
		}

		var categoryIDs []string
		if filters.CategoryIDs != "" {
			categoryIDs = strings.Split(filters.CategoryIDs, ",")
		}

		rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 60*time.Second)
		defer cancel()

		parameters := veselkalib.ListPredictionPointsParameters{
			Page:               filters.Page,
			PageSize:           filters.PageSize,
			SortBy:             filters.SortBy,
			SortDirection:      filters.SortDirection,
			IDs:                IDs,
			CapturedAt:         capturedAt,
			Radius:             radius,
			Crops:              crops,
			Robots:             robotParams,
			SessionID:          filters.SessionID,
			CategoryIDs:        categoryIDs,
			UploadedByOperator: filters.UploadedByOperator,
		}

		predictionPointResponse, err := veselkaClient.ListPredictionPoints(rqCtx, parameters)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Prediction Points not found")
			return
		}

		ReturnProtoJSON(ctx, predictionPointResponse, &errorPrefix)
	})

	routes.GET("/category-collections/session/:sessionId", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
		errorPrefix := "Failed to get session status"
		sessionId := ctx.Param("sessionId")

		rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 60*time.Second)
		defer cancel()

		sessionResponse, err := veselkaClient.GetPredictionPointSessionStatus(rqCtx, sessionId)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error fetching from Veselka")
			return
		}

		ReturnProtoJSON(ctx, sessionResponse, &errorPrefix)
	})

	routes.POST("/category-collections/session", middleware.GetAuth0User(auth0Client, caches.UserCache, domain), func(ctx *gin.Context) {
		// Note: even though we could derive a user's Plant Profile based on ID and grab the Base Model Id from the DB,
		// I intentionally want to the user to pass both explicitly so that their session doesn't get interrupted and so they can request sessions on unsaved changes
		errorPrefix := "Failed to create new session"

		var inputProto portal.CreateCategoryCollectionSessionRequest
		err := ReadProtoJSON(ctx, &inputProto)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
			return
		}

		// fetching saved profile to do customer permissions checks on
		categoryCollectionProfile, err := rosy_profiles.GetCategoryCollection(ctx, robotSyncerClient, inputProto.CategoryCollectionProfile.Profile.Id)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error getting Category Collection")
			return
		}

		var requestingCustomerUUID *uuid.UUID // note - this will be blank for admins and populated for customers
		requestingCustomerIDRaw, exists := ctx.Get(middleware.CustomerIDContextKey)
		if exists {
			requestingCustomerID := requestingCustomerIDRaw.(uint)
			requestingCustomer, err := caches.CustomerCache.Get(ctx, requestingCustomerID)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error fetching customer")
				return
			}
			requestingCustomerUUID = &requestingCustomer.Uuid
		}

		robotSerials := []string{}
		if categoryCollectionProfile.Profile.Protected {
			if !rosy_profiles.CanReadCategoryCollectionTemplates(ctx) {
				ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}
		} else {
			if !rosy_profiles.CanReadCategoryCollection(ctx, requestingCustomerUUID, categoryCollectionProfile.Profile) {
				ReturnError(ctx, http.StatusForbidden, nil, &errorPrefix, "User not authorized")
				return
			}
			if inputProto.CustomerId == nil {
				ReturnError(ctx, http.StatusBadRequest, nil, &errorPrefix, "Customer must be provided for customer profiles")
				return
			}

			profileCustomerID, err := utils.SafeUint64ToUint(*inputProto.CustomerId)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error parsing customer id")
				return
			}

			profileCustomer, err := caches.CustomerCache.Get(ctx, profileCustomerID)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error fetching customer")
				return
			}

			robots, err := robots.GetRobots(ctx, database, &profileCustomer.Base.ID, true, true)
			if err != nil || len(robots) <= 0 {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error fetching robots")
				return
			}

			for _, robot := range robots {
				robotSerials = append(robotSerials, robot.Serial)
			}
		}

		rqCtx, cancel := context.WithTimeout(ctx.Request.Context(), 60*time.Second)
		defer cancel()

		profileRequest := expandedCategoryCollectionToVeselka(inputProto.CategoryCollectionProfile)

		parameters := prediction_point.CreatePredictionPointSessionRequest{
			ModelId:                   inputProto.ModelId,
			RobotIds:                  robotSerials,
			CategoryCollectionProfile: profileRequest,
		}

		sessionResponse, err := veselkaClient.CreatePredictionPointSession(rqCtx, &parameters)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Error creating Prediction Point Session")
			return
		}

		ReturnProtoJSON(ctx, sessionResponse, &errorPrefix)
	})
}

func expandedCategoryCollectionToVeselka(
	expanded *portal.UnsavedExpandedCategoryCollection,
) *prediction_point.CategoryCollectionProfile {

	var categoryProfiles []*prediction_point.CategoryProfile
	for _, category := range expanded.Categories {
		categoryProfiles = append(categoryProfiles, &prediction_point.CategoryProfile{
			Id:                 category.Id,
			PredictionPointIds: category.ChipIds,
		})
	}

	return &prediction_point.CategoryCollectionProfile{
		Id:               expanded.Profile.Id,
		CategoryProfiles: categoryProfiles,
	}
}
