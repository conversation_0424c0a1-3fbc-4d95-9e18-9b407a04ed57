package rest

import (
	"fmt"
	"net/http"
	"slices"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/portal"

	"github.com/carbonrobotics/cloud/golang/services/portal/cache"
	"github.com/carbonrobotics/cloud/golang/services/portal/customers"
	"github.com/carbonrobotics/cloud/golang/services/portal/database"
	"github.com/carbonrobotics/cloud/golang/services/portal/database/models"
	"github.com/carbonrobotics/cloud/golang/services/portal/health"
)

type Input struct {
	Serial    string `json:"serial"`
	Timestamp int    `json:"timestamp"`
	Model     string `json:"model,omitempty"`
}

func RegisterInternal(router *gin.RouterGroup, database *gorm.DB, traceDumper *database.TraceDumper, caches cache.Caches) {
	routes := router.Group("/internal")
	{
		routes.POST("/health", func(context *gin.Context) {
			errorPrefix := "Failed to get health logs"

			// parse input params
			input := struct {
				Requests []Input `json:"requests"`
			}{}
			err := context.Bind(&input)
			if err != nil {
				ReturnError(context, http.StatusBadRequest, err, &errorPrefix, "Invalid input")
				return
			}

			// make response of same length
			response := make([]*portal.HealthLog, len(input.Requests))

			// loop through params
			for index, params := range input.Requests {
				healthLog := &models.HealthLog{}
				// get health logs
				query := database.Model(&models.HealthLog{})
				// filter by model
				if params.Model != "" {
					query = query.Where("model = ?", params.Model)
				}
				// ensure location is set
				query = query.Where("location IS NOT NULL")
				// query = query.Where("location != '{\"x\": 0, \"y\": 0, \"z\": 0}'")
				// filter by serial
				serial, err := carbon.ParseSerial(params.Serial)
				if err != nil {
					log.WithError(err).Warnf("Failed to get parse serial %s", params.Serial)
					response[index] = nil
					continue
				}
				robotId, err := caches.RobotIDBySerialCache.Get(context, cache.GetCacheableSerial(serial))
				if err != nil {
					// just skip this param on error
					log.WithError(err).Warnf("Failed to get robot ID from serial %s, time %d, model %s", params.Serial, params.Timestamp, params.Model)
					response[index] = nil
					continue
				}
				query = query.Where("robot_id = ?", robotId)
				// get closest
				query = query.
					Where("reported_at <= ?", params.Timestamp).
					Order("reported_at DESC").
					Limit(1)
				// get health log
				transaction := query.Find(&healthLog)
				if transaction.RowsAffected == 0 {
					// just skip this param on error
					log.WithError(transaction.Error).Warnf("No health logs found for serial %s, time %d, model %s", params.Serial, params.Timestamp, params.Model)
					response[index] = nil
					continue
				}
				response[index] = health.SerializeHealthLog(healthLog)
			}

			ReturnProtoJSONList(context, response, &errorPrefix)
		})

		routes.GET("dbtrace", func(ctx *gin.Context) {
			dbTrace(ctx, traceDumper)
		})

		routes.GET("/robots/:serial/customer", func(ctx *gin.Context) {
			errorPrefix := "Failed to get customer for serial"
			serial, err := carbon.ParseSerial(ctx.Param("serial"))
			if err != nil {
				ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", ctx.Param("serial")))
				return
			}

			// get robot id from serial
			robotID, err := caches.RobotIDBySerialCache.Get(ctx, cache.GetCacheableSerial(serial))
			if err != nil {
				ReturnError(ctx, http.StatusNotFound, err, &errorPrefix, "Failed to get robot ID")
				return
			}

			customer, err := customers.GetCachedCustomerByRobotId(ctx, caches, *robotID)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Failed to get customer association")
				return
			}

			response := &portal.Customer{
				Uuid: customer.Uuid.String(),
				Name: customer.Name,
			}
			ReturnProtoJSON(ctx, response, &errorPrefix)
		})

		// DEPRECATED - please use /robot/:serial/customer
		routes.GET("/customer_uuid/:serial", func(ctx *gin.Context) {
			errorPrefix := "Failed to get customer UUID"
			serial, err := carbon.ParseSerial(ctx.Param("serial"))
			if err != nil {
				ReturnError(ctx, http.StatusBadRequest, err, &errorPrefix, fmt.Sprintf("Invalid serial: %s", ctx.Param("serial")))
				return
			}

			// get robot id from serial
			robotID, err := caches.RobotIDBySerialCache.Get(ctx, cache.GetCacheableSerial(serial))
			if err != nil {
				ReturnError(ctx, http.StatusNotFound, err, &errorPrefix, "Failed to get robot ID")
				return
			}

			customer, err := customers.GetCachedCustomerByRobotId(ctx, caches, *robotID)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Failed to get customer association")
				return
			}

			resp := struct {
				CustomerUuid uuid.UUID `json:"customerUuid"`
			}{
				CustomerUuid: customer.Uuid,
			}

			ctx.JSON(http.StatusOK, resp)
		})

		routes.GET("/customers", func(ctx *gin.Context) {
			pageToken := ctx.Query("pageToken")
			pageSize, _ := strconv.Atoi(ctx.Query("pageSize"))
			errorPrefix := "Failed to get customers"
			allCustomers, nextToken, err := customers.ListCustomers(database, pageSize, pageToken)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Failed to get customers from database")
				return
			}

			list := make([]*portal.Customer, 0)
			for _, customer := range allCustomers {
				list = append(list, &portal.Customer{
					Uuid: customer.Uuid.String(),
					Name: customer.Name,
				})
			}

			response := &portal.ListCustomersResponse{Customers: list, NextPageToken: nextToken}
			ReturnProtoJSON(ctx, response, &errorPrefix)
		})

		// DEPRECATED please use /customers
		routes.GET("/customer-ids", func(ctx *gin.Context) {
			errorPrefix := "Failed to get customer Ids"
			customers, err := customers.GetCustomers(database)
			if err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, &errorPrefix, "Failed to get customers from database")
				return
			}

			customerIds := []string{}
			for _, customer := range customers {
				customerIds = append(customerIds, customer.Uuid.String())
			}

			slices.Sort(customerIds)
			customerIds = slices.Compact(customerIds)
			ctx.JSON(http.StatusOK, gin.H{"customerUuids": customerIds})
		})
	}
}
