import * as yup from "yup";
import { buildPermission, getCustomerId } from "common/utils/auth";
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  MenuItem,
} from "@mui/material";
import { Field, Form, Formik } from "formik";
import { InferType } from "yup";
import { makeGeoId } from "common/utils/geo";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { Select, TextField } from "formik-mui";
import { t } from "i18next";
import { titleCase } from "common/utils/strings";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import { useCreateFarmMutation } from "common/state/portalApi/farm";
import { useListCustomersQuery } from "portal/state/portalApi";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import React, { ReactNode, useState } from "react";

const validationSchema = yup.object({
  name: yup.string().required(),
  customerId: yup.number().required(),
});

type Inputs = InferType<typeof validationSchema>;

export function useNewFarmDialog(): {
  renderDialog: () => ReactNode;
  open: () => void;
  close: () => void;
} {
  const { user } = useSelf();
  const customerId = getCustomerId(user);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [createFarm] = useMutationPopups(useCreateFarmMutation());

  const canReadCustomers = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.customers,
      PermissionDomain.all
    ),
  ]);

  const { data: customers, isLoading } = useQueryPopups(
    useListCustomersQuery(undefined, {
      skip: !isOpen || !canReadCustomers,
    })
  );

  const initialValues: Inputs = {
    name: "",
    customerId: customerId ?? 0,
  };

  const handleSubmit = async (inputs: Inputs): Promise<void> => {
    const result = await createFarm({
      id: { id: makeGeoId() },
      version: undefined,
      pointDefs: [],
      zones: [],
      ...inputs,
    });

    if (!result.error) {
      setIsOpen(false);
    }
  };

  const handleCancel = (): void => {
    setIsOpen(false);
  };

  return {
    renderDialog: () => (
      <Dialog open={isOpen} onClose={handleCancel} fullWidth>
        <DialogTitle>
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.farms.farm_one"),
            })
          )}
        </DialogTitle>
        <DialogContent>
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={(values) => handleSubmit(values)}
          >
            {({ isValid, isSubmitting, submitForm }) => (
              <Form className="flex flex-col gap-4">
                <FormControl fullWidth>
                  <Field
                    component={TextField}
                    name="name"
                    label={titleCase(t("utils.descriptors.name"))}
                  />
                </FormControl>
                <FormControl fullWidth>
                  <Field
                    component={Select}
                    name="customerId"
                    disabled={isLoading}
                    label={titleCase(t("models.customers.customer_one"))}
                    placeHolder={
                      isLoading ? t("components.Loading.placeholder") : ""
                    }
                  >
                    {customers?.map(({ db, name }) => (
                      <MenuItem value={String(db?.id)} key={String(db?.id)}>
                        {name}
                      </MenuItem>
                    ))}
                  </Field>
                </FormControl>
                <div className="flex">
                  <Button
                    fullWidth
                    variant="contained"
                    disabled={!isValid || isSubmitting}
                    type="submit"
                    onClick={submitForm}
                  >
                    {t("utils.actions.create")}
                  </Button>
                  <Button
                    fullWidth
                    color="info"
                    onClick={() => {
                      handleCancel();
                    }}
                  >
                    {t("utils.actions.cancel")}
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    ),
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
}
