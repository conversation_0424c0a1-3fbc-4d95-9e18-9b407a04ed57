import { DeepPartial } from "portal/utils/objects";
import { Divide<PERSON>, IconButton, Paper, Typography } from "@mui/material";
import { Farm as FarmProto, Zone as ZoneType } from "protos/portal/farm";
import { Field } from "./Field";
import { Obstacle } from "./Obstacle";
import { Point } from "./Point";
import { setFocus, setSelection } from "portal/state/farmExplorer";
import { useDispatch } from "react-redux";
import { useFarmExplorer } from "portal/state/store";
import { Zone } from "./Zone";
import CloseIcon from "@mui/icons-material/Close";
import React, { FunctionComponent, ReactNode } from "react";

interface DetailsPanelProps {
  updateFarm: (updates: DeepPartial<FarmProto>) => void;
}

export const DetailsPanel: FunctionComponent<DetailsPanelProps> = ({
  updateFarm,
}) => {
  const { showDetails, selection, farm } = useFarmExplorer();
  const dispatch = useDispatch();

  const updateZone = (zoneUpdate: DeepPartial<ZoneType>): void => {
    updateFarm({ ...farm, zones: [zoneUpdate] });
  };
  const getDetailComponent = (): ReactNode => {
    switch (selection?.type) {
      case "zone": {
        if (selection.selection.contents?.field) {
          return <Field zone={selection.selection} updateZone={updateZone} />;
        }
        if (selection.selection.contents?.obstacle) {
          return (
            <Obstacle zone={selection.selection} updateZone={updateZone} />
          );
        }
        return <Zone zone={selection.selection} updateZone={updateZone} />;
      }
      case "point": {
        return <Point point={selection.selection} />;
      }
      default:
    }
  };
  const getSelectionName = (): ReactNode => {
    switch (selection?.type) {
      case "zone":
      case "point": {
        return selection.selection.name;
      }
      default:
    }
  };

  return (
    showDetails && (
      <Paper className="flex flex-col gap-2 w-full h-full pt-2">
        <div className="flex justify-between px-2">
          <Typography variant="h4" className="break-all">
            {getSelectionName()}
          </Typography>
          <IconButton
            color="info"
            className="flex my-auto"
            onClick={() => {
              dispatch(setSelection(undefined));
              dispatch(setFocus(undefined));
            }}
          >
            <CloseIcon />
          </IconButton>
        </div>
        <Divider />
        {getDetailComponent()}
      </Paper>
    )
  );
};
