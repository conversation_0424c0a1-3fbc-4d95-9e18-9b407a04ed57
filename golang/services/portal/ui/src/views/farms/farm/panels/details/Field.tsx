/* eslint-disable camelcase */
import {
  addShownPointIds,
  removeShownPointIds,
  selectFarmId,
} from "portal/state/farmExplorer";
import { buildPermission } from "portal/utils/auth";
import { BUTTON, classes } from "portal/theme/theme";
import { Button, Paper, Typography } from "@mui/material";
import { CenterPivot } from "./CenterPivot";
import { DeepPartial, mergeDeep } from "portal/utils/objects";
import { getJobPath } from "portal/utils/routing";
import { JobType } from "protos/rtc/jobs";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import {
  CenterPivot as PivotType,
  Zone,
  Zone as ZoneType,
} from "protos/portal/farm";
import { Point } from "./Point";
import { PointList } from "./PointList";
import { Section } from "portal/components/map/navigation/Section";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import { useDispatch, useSelector } from "react-redux";
import { useFarmExplorer } from "portal/state/store";
import { useListJobsQuery } from "common/state/rtcJobsApi";
import { useLocation, useNavigate } from "react-router-dom";
import { useNewJobDialog } from "portal/views/autotractor/jobs/SetJobForm";
import { useTranslation } from "react-i18next";
import AddIcon from "@mui/icons-material/Add";
import React, { FC, useMemo } from "react";

interface FieldProps {
  zone: ZoneType;
  updateZone: (newZone: DeepPartial<Zone>) => void;
}

export const Field: FC<FieldProps> = ({ zone, updateZone }: FieldProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const jobs = useListJobsQuery({
    fieldIds: zone.id?.id ? [zone.id.id.toString()] : undefined,
  });
  const farmId = useSelector(selectFarmId);
  const { renderDialog: renderNewJobDialog, open: openNewJobDialog } =
    useNewJobDialog({
      type: JobType.LASER_WEED,
      farmId,
      fieldId: zone.id?.id,
    });

  const { pointsById } = useFarmExplorer();

  const centerPivot = zone.contents?.field?.centerPivot;

  const createButtonProps = {
    ...BUTTON,
    className: classes(BUTTON.className, "m-2 w-1/2 self-center"),
  };

  const { boundaryPoints, boundaryIds } = useMemo(() => {
    const boundaryIds = [];
    const boundaryPoints = zone.areas.flatMap((area) => {
      return area.polygon?.boundary?.points;
    });
    for (const p of boundaryPoints) {
      if (p?.id?.id) {
        boundaryIds.push(p.id.id);
      }
    }
    return { boundaryPoints, boundaryIds };
  }, [zone]);

  const updateCenterPivot = (newPivot: DeepPartial<PivotType>): void => {
    const updatedZone = mergeDeep(zone, {
      contents: {
        field: {
          centerPivot: newPivot,
        },
      },
      version: {
        changed: true,
      },
    });
    updateZone(updatedZone);
  };

  const pivot = centerPivot && (
    <Paper>
      <CenterPivot
        pivot={centerPivot}
        fieldId={zone.id?.id}
        updateCenterPivot={updateCenterPivot}
      />
    </Paper>
  );

  const heading = zone.contents?.field?.plantingHeading;
  const a = heading?.abLine?.a?.id?.id
    ? pointsById?.[heading.abLine.a.id.id]
    : undefined;
  const b = heading?.abLine?.b?.id?.id
    ? pointsById?.[heading.abLine.b.id.id]
    : undefined;
  const plantingHeading = heading && (
    <Paper className="pl-4">
      <Point point={a} />
      <Point point={b} />
    </Paper>
  );

  const points = <PointList points={boundaryPoints} />;

  const canCreateJob = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.autotractor_jobs,
      PermissionDomain.all
    ),
  ]);

  return (
    <>
      {renderNewJobDialog()}
      <div className="flex flex-col h-full">
        <Section
          header={
            <Typography>{t("views.farms.detailsPanel.centerPivot")}</Typography>
          }
        >
          {pivot}
        </Section>
        <Section
          header={
            <Typography>
              {t("views.farms.detailsPanel.plantingHeading")}
            </Typography>
          }
        >
          {plantingHeading}
        </Section>
        <Section
          startCollapsed
          header={<Typography>{t("models.jobs.job_other")}</Typography>}
        >
          {jobs.data?.jobs.map((j) => (
            <div key={j.id} className="flex flex-col">
              <Button
                className="justify-start flex"
                onClick={() =>
                  navigate({
                    pathname: getJobPath(j.id.toString()),
                    search: `?returnTo=${location.pathname}`,
                  })
                }
                color="info"
                variant="text"
              >
                {j.name}
              </Button>
            </div>
          ))}
          {canCreateJob && (
            <Button
              {...createButtonProps}
              startIcon={<AddIcon />}
              onClick={openNewJobDialog}
            >
              {t("utils.actions.newLong", {
                subject: t("models.jobs.job_one"),
              })}
            </Button>
          )}
        </Section>
        <Section
          startCollapsed
          onExpand={() => dispatch(addShownPointIds(boundaryIds))}
          onCollapse={() => dispatch(removeShownPointIds(boundaryIds))}
          header={
            <Typography>{t("views.farms.detailsPanel.boundary")}</Typography>
          }
        >
          {points}
        </Section>
      </div>
    </>
  );
};
