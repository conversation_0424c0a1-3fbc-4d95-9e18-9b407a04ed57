import { Bounds } from "portal/utils/geo";
import { buildPermission } from "common/utils/auth";
import { Button, LinearProgress, Paper, Typography } from "@mui/material";
import { capitalize, titleCase } from "portal/utils/strings";
import { DeepPartial } from "portal/utils/objects";
import { DetailsPanel } from "./panels/details/DetailsPanel";
import { FarmMap } from "./FarmMap";
import { Farm as FarmProto } from "protos/portal/farm";
import { getFieldBounds } from "common/utils/farm";
import { Header } from "portal/components/header/Header";
import { Navigate, useLocation, useParams } from "react-router-dom";
import { Page } from "portal/components/Page";
import { Path } from "portal/utils/routing";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import {
  Selection,
  setFarm,
  setFocus,
  setPointsById,
  setSelection,
} from "portal/state/farmExplorer";
import { SelectionPanel } from "./panels/selection/SelectionPanel";
import { skipToken } from "@reduxjs/toolkit/query/react";
import { SMALL_BUTTON } from "portal/theme/theme";
import { useAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import { useDispatch } from "react-redux";
import { useFarmExplorer, useSelf } from "portal/state/store";
import { useFarmFieldExportDialog } from "./ExportDialog";
import { useFarmFieldImportDialog } from "./ImportDialog";
import { useFarmMapData } from "common/hooks/useFarmMapData";
import {
  useGetFarmQuery,
  useUpdateFarmMutation,
} from "common/state/portalApi/farm";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { WithSkeleton } from "portal/components/WithSkeleton";
import ExportIcon from "@mui/icons-material/FileDownload";
import ImportIcon from "@mui/icons-material/FileUpload";
import React, { FunctionComponent, useEffect } from "react";

export const Farm: FunctionComponent = () => {
  const { t } = useTranslation();
  const { farmId } = useParams();
  const dispatch = useDispatch();
  const {
    data: fetchedFarm,
    isSuccess,
    isLoading: farmLoading,
    refetch,
  } = useQueryPopups(useGetFarmQuery(farmId ?? skipToken));

  const { farm } = useFarmExplorer();

  useEffect(() => {
    dispatch(setFarm(fetchedFarm));
  }, [fetchedFarm, dispatch]);

  const [updateFarm, { isLoading: updateLoading }] = useMutationPopups(
    useUpdateFarmMutation(),
    {
      success: titleCase(
        t("utils.actions.saved", {
          subject: t("views.farms.farm"),
        })
      ),
    }
  );

  const mutateFarm = (newFarm: DeepPartial<FarmProto>): void => {
    updateFarm(newFarm).then(({ data }) => {
      if (data) {
        // TODO: don't refetch, just set farm data to this.
        // this part of the farms api returns CaptureInfo enums incorrectly, so we can't use it just yet
        // setFarm(data);
        refetch();
      }
    });
  };

  if (!farmId) {
    return <Navigate to={`/${Path.FARMS}`} />;
  }

  return (
    <>
      <Header
        title={capitalize(t("models.farms.farm_one"))}
        pageTitle={
          farm && `${farm.name} - ${capitalize(t("models.farms.farm_one"))}`
        }
        parentLink={Path.FARMS}
      />
      <Page maxWidth={false}>
        {/* like `<NoScroll>` but only for `md:` and above */}
        <div className="flex flex-col absolute inset-10 md:overflow-hidden">
          <WithSkeleton variant="rectangular" success={isSuccess}>
            {farm && (
              <FarmView
                farm={farm}
                updateFarm={mutateFarm}
                isLoading={farmLoading || updateLoading}
              />
            )}
          </WithSkeleton>
        </div>
      </Page>
    </>
  );
};

interface FarmViewProps {
  farm: FarmProto;
  updateFarm: (newFarm: DeepPartial<FarmProto>) => void;
  isLoading: boolean;
}
const FarmView: FunctionComponent<FarmViewProps> = ({
  farm,
  isLoading,
  updateFarm,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { state: locationState } = useLocation();
  const farmMapData = useFarmMapData(farm);
  const { showDetails } = useFarmExplorer();
  const self = useSelf();
  const importExportVisible =
    useAuthorizationRequired([
      buildPermission(
        PermissionAction.update,
        PermissionResource.farms,
        PermissionDomain.all
      ),
    ]) && self.isInternal;

  useEffect(() => {
    // cannot use dispatch inside memo so i need to listen for farmmapdata changes.
    dispatch(setPointsById(farmMapData.pointsById));
  }, [farmMapData, dispatch]);

  // use location.state to allow other views to link directly to a specific
  // part of the farm
  const locationStateSelection = locationState as Selection | undefined;
  const selectedZone =
    locationStateSelection?.type === "zone"
      ? farm.zones.find(
          (zone) => zone.id === locationStateSelection.selection.id
        )
      : undefined;
  const bounds: Bounds | undefined = selectedZone
    ? getFieldBounds(selectedZone, farmMapData.pointsById)
    : undefined;

  useEffect(() => {
    dispatch(setSelection(locationState));
  }, [locationState, dispatch]);

  const { pointsById, shownPointIds, selection, focus } = useFarmExplorer();
  const { dialog: importDialog, open: openImportDialog } =
    useFarmFieldImportDialog(farm);
  const { dialog: exportDialog, open: openExportDialog } =
    useFarmFieldExportDialog(farm);

  return (
    <div className="flex flex-col h-screen">
      <Typography variant="h1" className="flex flex-row gap-2 text-4xl mb-3">
        {farm.name}
        {importExportVisible && (
          <>
            {importDialog}
            {exportDialog}
            <Button
              startIcon={<ImportIcon />}
              {...SMALL_BUTTON}
              onClick={openImportDialog}
            >
              {t("views.farms.actions.importField")}
            </Button>
            <Button
              startIcon={<ExportIcon />}
              {...SMALL_BUTTON}
              onClick={openExportDialog}
            >
              {t("views.farms.actions.exportField")}
            </Button>
          </>
        )}
      </Typography>
      {isLoading && <LinearProgress variant="indeterminate" />}
      <div className="flex flex-col md:flex-row h-full">
        <div className="flex flex-col w-1/6">
          <SelectionPanel farm={farm} />
          <Paper className="flex flex-col grow" />
        </div>
        {/* extra wrapper to prevent map from having a weird effective min-height */}
        <div className="farm-map-container relative flex min-h-64 md:min-h-0 flex-1">
          <FarmMap
            farm={farm}
            className="absolute inset-0"
            selectedPointId={
              selection?.type === "point"
                ? selection.selection.id?.id
                : undefined
            }
            selectedZoneId={
              selection?.type === "zone"
                ? selection.selection.id?.id
                : undefined
            }
            focusedPointId={
              focus?.type === "point" ? focus.selection.id?.id : undefined
            }
            focusedZoneId={
              focus?.type === "zone" ? focus.selection.id?.id : undefined
            }
            shownPoints={shownPointIds}
            farmMapData={farmMapData}
            onPointClick={(f) => {
              const id = f?.properties.pointId;
              if (id) {
                const point = pointsById?.[id];
                dispatch(
                  setSelection(
                    point ? { type: "point", selection: point } : undefined
                  )
                );
              }
            }}
            onPointHover={(f) => {
              const id = f?.properties.pointId;
              if (id) {
                if (focus?.type === "point" && focus.selection.id?.id === id) {
                  return;
                }
                const point = pointsById?.[id];
                const newFocus: Selection | undefined = point
                  ? { type: "point", selection: point }
                  : undefined;
                dispatch(setFocus(newFocus));
              }
            }}
            onZoneClick={(f) => {
              const id = f?.properties.zoneId;
              if (id) {
                const zone = farm.zones.find((z) => z.id?.id === id);
                dispatch(
                  setSelection(
                    zone ? { type: "zone", selection: zone } : undefined
                  )
                );
              }
            }}
            onZoneHover={(f) => {
              const id = f?.properties.zoneId;
              if (id) {
                if (focus?.type === "zone" && focus.selection.id?.id === id) {
                  return;
                }
                const zone = farm.zones.find((z) => z.id?.id === id);
                dispatch(
                  setFocus(zone ? { type: "zone", selection: zone } : undefined)
                );
              } else {
                dispatch(setFocus());
              }
            }}
            bounds={bounds}
          />
          {showDetails && (
            <div className="flex relative w-1/4 ml-auto">
              <DetailsPanel updateFarm={updateFarm} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
