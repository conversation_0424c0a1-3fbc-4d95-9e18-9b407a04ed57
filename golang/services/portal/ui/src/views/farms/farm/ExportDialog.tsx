import * as yup from "yup";
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogProps,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
} from "@mui/material";
import { Farm, Zone } from "protos/portal/farm";
import { Field, Formik } from "formik";
import { referencedPointIdsForArea } from "common/utils/farm";
import { Select } from "formik-mui";
import { TFunction } from "i18next";
import { titleCase } from "common/utils/strings";
import { useTranslation } from "react-i18next";
import ExportIcon from "@mui/icons-material/FileDownload";
import React, { FC, ReactElement, useState } from "react";

function displayZoneType(t: TFunction, zone: Zone): string {
  if (zone.contents?.farmBoundary) {
    return t("views.farms.zoneTypes.farmBoundary");
  } else if (zone.contents?.field) {
    return t("views.farms.zoneTypes.field");
  } else if (zone.contents?.headland) {
    return t("views.farms.zoneTypes.headland");
  } else if (zone.contents?.obstacle) {
    return t("views.farms.zoneTypes.obstacle");
  } else if (zone.contents?.privateRoad) {
    return t("views.farms.zoneTypes.privateRoad");
  } else {
    return t("views.farms.zoneTypes.unknown");
  }
}

function serializeAndDownloadZone(farm: Farm, zoneId: string): void {
  // pick the zone out of the farm definition
  const zone = farm.zones.find((z) => z.id?.id === zoneId);

  if (!zone) {
    return undefined;
  }

  // some of the points in a field can be incorporated by reference; identify
  // all the points we need to include in the exported file.
  let pointIdsToInclude = new Set<string>();

  for (const area of zone.areas) {
    pointIdsToInclude = pointIdsToInclude.union(
      referencedPointIdsForArea(area)
    );
  }

  if (zone.contents?.field) {
    if (
      zone.contents.field.centerPivot?.center &&
      zone.contents.field.centerPivot.center.id?.id
    ) {
      pointIdsToInclude.add(zone.contents.field.centerPivot.center.id.id);
    }

    if (zone.contents.field.plantingHeading?.abLine?.a?.id?.id) {
      pointIdsToInclude.add(zone.contents.field.plantingHeading.abLine.a.id.id);
    }

    if (zone.contents.field.plantingHeading?.abLine?.b?.id?.id) {
      pointIdsToInclude.add(zone.contents.field.plantingHeading.abLine.b.id.id);
    }
  }

  const partialFarm: Partial<Farm> = {
    pointDefs: farm.pointDefs.filter(
      (pdef) => pdef.point?.id?.id && pointIdsToInclude.has(pdef.point.id.id)
    ),
    zones: [zone],
  };

  // blobbify it
  const blob = new Blob([JSON.stringify(partialFarm)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);

  // throw it into a link and click that link
  const elem = document.createElement("a");
  elem.href = url;
  elem.target = "_blank";
  elem.download = `${farm.name}-${zone.name}.${Date.now()}.json`;
  elem.click();
}

interface FarmFieldExportDialogProps extends DialogProps {
  farm: Farm;
  onClose: () => void;
}

const FarmFieldExportDialog: FC<FarmFieldExportDialogProps> = ({
  farm,
  onClose,
  ...props
}) => {
  const { t } = useTranslation();

  const validationSchema = yup.object({
    fieldId: yup.string().required(),
  });

  const initialValues = {
    fieldId: "",
  };

  return (
    <Dialog {...props} onClose={onClose} maxWidth="sm" fullWidth={true}>
      <DialogTitle>{t("views.farms.actions.exportField")}</DialogTitle>
      <DialogContent className="flex flex-col gap-4">
        <DialogContentText>
          {t("views.farms.exportField.warning")}
        </DialogContentText>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={(values) => {
            serializeAndDownloadZone(farm, values.fieldId);
            onClose();
          }}
        >
          {({ isValid, submitForm }) => (
            <>
              <FormControl fullWidth>
                <InputLabel id="zone-label"></InputLabel>
                <Field
                  component={Select}
                  label={titleCase(t("models.farms.zone_one"))}
                  name="fieldId"
                >
                  {farm.zones.map((z) => (
                    <MenuItem key={z.id?.id} value={z.id?.id}>
                      {z.name} ({displayZoneType(t, z)})
                    </MenuItem>
                  ))}
                </Field>
              </FormControl>
              <Button
                variant="contained"
                disabled={!isValid}
                type="submit"
                startIcon={<ExportIcon />}
                onClick={submitForm}
              >
                {t("views.farms.actions.exportField")}
              </Button>
            </>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export const useFarmFieldExportDialog = (
  farm: Farm
): { dialog: ReactElement; open: () => void; close: () => void } => {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = (): void => {
    setIsOpen(false);
  };

  return {
    dialog: (
      <FarmFieldExportDialog open={isOpen} farm={farm} onClose={onClose} />
    ),
    open: () => {
      setIsOpen(true);
    },
    close: onClose,
  };
};
