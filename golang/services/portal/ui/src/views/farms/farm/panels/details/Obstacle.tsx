import {
  addShownPointIds,
  removeShownPointIds,
} from "portal/state/farmExplorer";
import { Checkbox, FormControlLabel, FormGroup } from "@mui/material";
import { DeepPartial, mergeDeep } from "portal/utils/objects";
import { PointList } from "./PointList";
import { Section } from "portal/components/map/navigation/Section";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Zone as ZoneType } from "protos/portal/farm";
import React, { FC, useMemo } from "react";

interface ObstacleProps {
  zone: ZoneType;
  updateZone: (newZone: DeepPartial<ZoneType>) => void;
}

export const Obstacle: FC<ObstacleProps> = ({
  zone,
  updateZone,
}: ObstacleProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { boundaryPoints, boundaryIds } = useMemo(() => {
    const boundaryIds = [];
    const boundaryPoints = zone.areas.flatMap((area) => {
      return area.polygon?.boundary?.points;
    });
    for (const p of boundaryPoints) {
      if (p?.id?.id) {
        boundaryIds.push(p.id.id);
      }
    }
    return { boundaryPoints, boundaryIds };
  }, [zone]);

  const { holePoints, holeIds } = useMemo(() => {
    const holeIds = [];
    const holePoints = zone.areas.flatMap((area) => {
      return area.polygon?.holes.flatMap((h) => h.points);
    });
    for (const p of holePoints) {
      if (p?.id?.id) {
        holeIds.push(p.id.id);
      }
    }
    return { holePoints, holeIds };
  }, [zone]);

  return (
    <div className="flex flex-col">
      {zone.contents?.obstacle && (
        <Section header={t("models.farms.obstacle_one")}>
          <FormGroup className="px-4">
            <FormControlLabel
              control={<Checkbox checked={zone.contents.obstacle.passable} />}
              label={t("utils.descriptors.passable")}
              onChange={(_e, checked) => {
                const updatedZone = mergeDeep(zone, {
                  contents: {
                    obstacle: { passable: checked },
                  },
                  version: {
                    changed: true,
                  },
                });
                updateZone(updatedZone);
              }}
            />
          </FormGroup>
        </Section>
      )}
      <Section
        header={t("views.farms.detailsPanel.boundary")}
        startCollapsed
        onExpand={() => dispatch(addShownPointIds(boundaryIds))}
        onCollapse={() => dispatch(removeShownPointIds(boundaryIds))}
      >
        <PointList points={boundaryPoints} />
      </Section>
      <Section
        header={t("views.farms.detailsPanel.holes")}
        startCollapsed
        onExpand={() => dispatch(addShownPointIds(holeIds))}
        onCollapse={() => dispatch(removeShownPointIds(holeIds))}
      >
        <PointList points={holePoints} />
      </Section>
    </div>
  );
};
