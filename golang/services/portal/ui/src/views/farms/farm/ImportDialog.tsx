import { addNotification } from "portal/state/notifications";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogContentText,
  DialogProps,
  DialogTitle,
} from "@mui/material";
import { DateTime } from "luxon";
import { Farm } from "protos/portal/farm";
import { regenerateIdsForZone } from "common/utils/farm";
import { useDispatch } from "react-redux";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { useUpdateFarmMutation } from "common/state/portalApi/farm";
import ImportIcon from "@mui/icons-material/FileUpload";
import React, {
  ChangeEventHandler,
  FC,
  ReactElement,
  useRef,
  useState,
} from "react";

interface FarmFieldImportDialogProps extends DialogProps {
  farm: Farm;
}

const FarmFieldImportDialog: FC<FarmFieldImportDialogProps> = ({
  farm,
  onClose,
  ...props
}) => {
  const { t } = useTranslation();
  const filePicker = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch();
  type SelectedFileStatus = "none-selected" | "ok" | "old" | "no-timestamp";
  const [selectedFileStatus, setSelectedFileStatus] =
    useState<SelectedFileStatus>("none-selected");
  let dialogText: string | undefined;
  const [updateFarm] = useMutationPopups(useUpdateFarmMutation());

  const onCloseWrapper: FarmFieldImportDialogProps["onClose"] = (
    event,
    reason
  ) => {
    // reset dialog state
    setSelectedFileStatus("none-selected");

    if (onClose) {
      onClose(event, reason);
    }
  };

  const onFileChange: ChangeEventHandler<HTMLInputElement> = () => {
    // look at the file the user selected and check the timestamp.
    const file = filePicker.current?.files?.item(0);

    if (file) {
      // the filename format we export in is "[farm_name]-[field_name].[timestamp].json"
      const filenameSegments = file.name.split(".");
      const timestamp = filenameSegments.at(-2);

      if (timestamp && !Number.isNaN(Number.parseInt(timestamp))) {
        const timestampDt = DateTime.fromMillis(Number.parseInt(timestamp));
        const timeSince = timestampDt.diffNow(["day"]);

        if (timeSince.days > 0) {
          // assume the user isn't a time traveler
          setSelectedFileStatus("no-timestamp");
        } else if (timeSince.days < -30) {
          setSelectedFileStatus("old");
        } else {
          setSelectedFileStatus("ok");
        }
      } else {
        setSelectedFileStatus("no-timestamp");
      }
    } else {
      setSelectedFileStatus("none-selected");
    }
  };

  const onSubmit = async (): Promise<void> => {
    if (!filePicker.current?.files) {
      console.error("invalid filePicker ref?");
      return;
    }

    const file = filePicker.current.files.item(0);

    if (!file) {
      console.error("form submitted without selecting a file?");
      return;
    }

    const o = JSON.parse(await file.text());
    const loadedField: Partial<Farm> = Farm.fromPartial(o);

    if (!loadedField.zones || loadedField.zones.length === 0) {
      dispatch(
        addNotification({
          message: t("views.farms.importField.importFailedNoFields"),
          variant: "error",
        })
      );
      return;
    }

    const loadedZoneZero = loadedField.zones[0]!;

    // check to make sure there's not already a zone with the name of the
    // zone being loaded in the existing farm, as a simple protection against
    // accidentally importing the same field multiple times.
    if (farm.zones.findIndex((z) => z.name === loadedZoneZero.name) !== -1) {
      dispatch(
        addNotification({
          message: t("views.farms.importField.importFailedNameCollision"),
          variant: "error",
        })
      );
      return;
    }

    // glue our existing farm ID into the partial
    const submittedPartialFarm: Pick<Farm, "pointDefs" | "zones" | "id"> &
      Partial<Farm> = {
      ...loadedField,
      id: farm.id,
      pointDefs: [],
      zones: [],
    };

    // geo entity IDs must be globally unique; regenerate all of the IDs of
    // entities in this field on import.
    const { pointDefs: pointDefsOut, zone: zoneOut } = regenerateIdsForZone(
      loadedField.pointDefs ?? [],
      loadedField.zones[0]!
    );

    submittedPartialFarm.pointDefs = pointDefsOut;
    submittedPartialFarm.zones = [zoneOut];

    try {
      await updateFarm(submittedPartialFarm);

      dispatch(
        addNotification({
          message: t("views.farms.importField.importSuccessful"),
          variant: "success",
        })
      );
    } catch (error) {
      dispatch(
        addNotification({
          message: t("views.farms.importField.importFailed", { error }),
          variant: "error",
        })
      );
    }
  };

  if (selectedFileStatus === "old") {
    dialogText = t("views.farms.importField.oldExportWarning");
  } else if (selectedFileStatus === "no-timestamp") {
    dialogText = t("views.farms.importField.notAnExportWarning");
  }

  return (
    <Dialog {...props} onClose={onCloseWrapper} maxWidth="sm" fullWidth={true}>
      <DialogTitle>{t("views.farms.actions.importField")}</DialogTitle>
      <DialogContent className="flex flex-col gap-4">
        {dialogText && (
          <DialogContentText className="text-warning">
            {dialogText}
          </DialogContentText>
        )}
        <input
          ref={filePicker}
          type="file"
          accept=".json,application/json"
          onChange={onFileChange}
        />
        <Button
          variant="contained"
          disabled={selectedFileStatus === "none-selected"}
          type="submit"
          startIcon={<ImportIcon />}
          onClick={onSubmit}
        >
          {t("views.farms.actions.importField")}
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export const useFarmFieldImportDialog = (
  farm: Farm
): {
  dialog: ReactElement;
  open: () => void;
  close: () => void;
} => {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = (): void => {
    setIsOpen(false);
  };

  return {
    dialog: (
      <FarmFieldImportDialog open={isOpen} farm={farm} onClose={onClose} />
    ),
    open: () => {
      setIsOpen(true);
    },
    close: onClose,
  };
};
