import { areArraysEqual } from "portal/utils/equality";
import { CenterPivot } from "common/components/map/CenterPivot";
import {
  DrivePathMapDataEntry,
  Property,
  TractorHistoryLayers,
} from "portal/components/map/sharedLayers/TractorHistoryLayers";
import { Farm } from "protos/portal/farm";
import { FarmLayers } from "common/components/map/layers/FarmLayers";
import { Feature, Geometry, MultiLineString } from "geojson";
import { getFarmRowData } from "portal/utils/rows";
import { groupSegments } from "portal/utils/geo";
import { Map } from "portal/components/map/Map";
import { RobotOutlineLayers } from "./RobotOutlineLayers";
import { RobotSummaryResponse } from "protos/portal/robots";
import { RowLayers } from "portal/components/map/sharedLayers/RowLayers";
import { useFarmMapData } from "common/hooks/useFarmMapData";
import { useLocation } from "portal/state/store";
import React, { FunctionComponent, useMemo, useState } from "react";

interface AutotractorMapProps {
  robots: string[];
  selectedFarm?: Farm;
  rows: { showRows: boolean; rowWidthMeters: number };
  tractorHistoryWidthMeters: number;
  focusedRobotSerial?: string;
  setFocusedRobot: (serial: string) => void;
}

export const AutotractorMap: FunctionComponent<AutotractorMapProps> = ({
  robots: serials,
  selectedFarm,
  rows,
  tractorHistoryWidthMeters,
  focusedRobotSerial,
  setFocusedRobot,
}) => {
  const { history: locationHistory } = useLocation();
  const [isAutoPanning, setIsAutoPanning] = useState(true);
  const [isAutoZooming, setIsAutoZooming] = useState(true);
  const [prevFocusedRobotSerial, setPrevFocusedRobotSerial] = useState<
    string | undefined
  >();

  if (focusedRobotSerial !== prevFocusedRobotSerial) {
    // reset autopanning but not autozooming on new serial
    setIsAutoPanning(true);
    if (focusedRobotSerial === undefined) {
      // reset autozooming if we've cleared out the focus
      setIsAutoZooming(true);
    }
    setPrevFocusedRobotSerial(focusedRobotSerial);
  }

  const [prevRobotSerials, setPrevRobotSerials] = useState<string[]>();
  if (!areArraysEqual(serials, prevRobotSerials)) {
    // reset panning and zooming when new robots are selected
    setIsAutoPanning(true);
    setIsAutoZooming(true);
    setPrevRobotSerials(serials);
  }

  const farmMapData = useFarmMapData(selectedFarm);

  const farmRowData = useMemo(
    () => farmMapData && getFarmRowData(farmMapData, rows.rowWidthMeters),
    [farmMapData, rows]
  );

  const drivePathMapData = useMemo(() => {
    const result: DrivePathMapDataEntry[] = [];
    for (const serial of serials) {
      const records = locationHistory[serial];
      if (!records) {
        continue;
      }
      const last = records.at(-1)?.point;
      if (!last) {
        continue;
      }
      const robot = RobotSummaryResponse.fromPartial({
        robot: {
          serial,
          // XXX: These are intentionally backward to match corresponding
          // errors in the Map component, which are hard to fix because they
          // pervade the stack all the way down to robots sending incorrect
          // health logs.
          health: { location: { x: last.lat, y: last.lng } },
        },
      });

      const { combined: combinedLine, grouped: linesByActive } = groupSegments(
        records,
        ({ data, point }) => {
          if (!data || !point) {
            return;
          }
          const position = [point.lng, point.lat, point.alt];
          return { group: data.active, position };
        }
      );

      const pathFeature = <G extends Geometry>(geometry: G): Feature<G> => ({
        type: "Feature",
        properties: {
          [Property.SERIAL]: serial,
          [Property.LATITUDE_RADIANS]: last.lat * (Math.PI / 180),
          [Property.LINE_WIDTH_METERS]: tractorHistoryWidthMeters,
        },
        geometry,
      });
      const combinedPath = pathFeature(combinedLine);
      // eslint-disable-next-line unicorn/consistent-function-scoping
      const emptyLine = (): MultiLineString => ({
        type: "MultiLineString",
        coordinates: [],
      });
      const activePath = pathFeature(linesByActive.get(true) ?? emptyLine());
      const inactivePath = pathFeature(linesByActive.get(false) ?? emptyLine());
      result.push({ serial, robot, combinedPath, activePath, inactivePath });
    }
    return result;
  }, [serials, locationHistory, tractorHistoryWidthMeters]);

  const robots = useMemo(
    () => drivePathMapData.map(({ robot }) => robot),
    [drivePathMapData]
  );

  return (
    <Map
      className="w-full flex-grow h-[600px] max-h-[80vh] md:h-full md:max-h-full"
      allowEmpty
      robots={robots}
      onRobotClick={(serial) => {
        setIsAutoPanning(true);
        setFocusedRobot(serial);
      }}
      animationDuration={2000}
      boundsOffset={25}
      focusedRobotSerial={focusedRobotSerial}
      isAutoPanning={isAutoPanning}
      setIsAutoPanning={setIsAutoPanning}
      isAutoZooming={isAutoZooming}
      setIsAutoZooming={setIsAutoZooming}
      extraControls={
        <>
          {farmMapData && (
            <FarmLayers farmMapData={farmMapData} hidePivots shownPoints={[]} />
          )}
          {farmMapData && farmRowData && rows.showRows && (
            <RowLayers
              farmMapData={farmMapData}
              farmRowData={farmRowData}
              debug={false}
            />
          )}
          {farmMapData &&
            [...farmMapData.centerPivotsByFieldId.values()].map((pivot) => (
              <CenterPivot
                key={`${pivot.center?.id?.id}-${pivot.endpointDeviceId}`}
                centerPivot={pivot}
              />
            ))}
          {drivePathMapData.map((data) => (
            <TractorHistoryLayers key={data.serial} drivePathMapData={data} />
          ))}
          {farmMapData &&
            [...farmMapData.centerPivotsByFieldId.values()].map((pivot) => (
              <CenterPivot
                key={`${pivot.center?.id?.id}-${pivot.endpointDeviceId}`}
                centerPivot={pivot}
              />
            ))}
          <RobotOutlineLayers serials={serials} />
        </>
      }
    ></Map>
  );
};
