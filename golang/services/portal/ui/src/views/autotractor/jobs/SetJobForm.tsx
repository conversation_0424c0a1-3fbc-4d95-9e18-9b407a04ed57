/* eslint-disable camelcase */
import * as yup from "yup";
import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Input,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import {
  CreateJobRequest,
  Job,
  JobType,
  jobTypeFromJSON,
  State,
} from "protos/rtc/jobs";
import {
  Direction,
  directionFromJSON,
  PathPlanConfiguration,
} from "protos/rtc/path_plan";
import { Field, Form, Formik } from "formik";
import { Select as FormikSelect, Switch } from "formik-mui";
import { getCustomerId } from "portal/utils/auth";
import { t } from "i18next";
import { titleCase } from "portal/utils/strings";
import {
  useCreateJobMutation,
  useUpdateJobMutation,
} from "common/state/rtcJobsApi";
import { useListFarmsQuery } from "common/state/portalApi/farm";
import { useMutationPopups } from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import React, { FC, ReactNode, useCallback, useState } from "react";

interface SetJobFormOutput extends Omit<CreateJobRequest, "customerId"> {}

interface SetJobFormProps {
  initialJob?: Partial<Job>;
  mode: "new-job" | "edit-pending" | "edit-active";
  onSubmit: (job: SetJobFormOutput) => void;
  onCancel: () => void;
}

interface SetJobFormState {
  name: string;
  // MUI only special-cases "" for an invalid sentinel value in selects; if we
  // use the more obvious JobType.JOB_TYPE_UNSPECIFIED, the styling on the
  // select box breaks and it yells at us in console.warn
  type: JobType | "";
  farmId: string;
  fieldId: string;
  groundPrep: {
    pathPlan: PathPlanConfiguration;
  };
}

function initialFormStateFromExistingJob(
  job: Partial<Job> | undefined
): SetJobFormState {
  const defaultGroundPrepState: SetJobFormState["groundPrep"] = {
    pathPlan: {
      doHeadlandFirst: true,
      rowHeadingDeg: 45,
      headlandWidthM: 6,
      turnDirection: Direction.CW,
      numHeadlandPasses: 2,
      combinedTurnRadiusM: 5,
    },
  };

  if (job) {
    const jobCoreState: Pick<
      SetJobFormState,
      "name" | "type" | "farmId" | "fieldId"
    > = {
      name: job.name ?? "",
      type: job.type ?? "",
      farmId: job.farmId ?? "",
      fieldId: job.fieldId ?? "",
    };

    // eslint-disable-next-line sonarjs/no-all-duplicated-branches
    if (job.type === JobType.GROUND_PREP) {
      return {
        ...jobCoreState,
        // FIXME: the job object doesn't currently contain the ground prep
        // configuration for ground prep jobs
        groundPrep: defaultGroundPrepState,
      };
      // eslint-disable-next-line sonarjs/no-duplicated-branches
    } else {
      return {
        ...jobCoreState,
        groundPrep: defaultGroundPrepState,
      };
    }
  } else {
    return {
      name: "",
      type: "",
      farmId: "",
      fieldId: "",
      groundPrep: defaultGroundPrepState,
    };
  }
}

function createJobRequestFromFormState(
  state: SetJobFormState
): SetJobFormOutput {
  if (state.type === JobType.LASER_WEED) {
    return {
      name: state.name,
      type: state.type,
      farmId: state.farmId,
      fieldId: state.fieldId,
      laserWeed: {},
      priority: 0,
      generateObjectives: true,
    };
  } else if (state.type === JobType.GROUND_PREP) {
    return {
      name: state.name,
      type: state.type,
      farmId: state.farmId,
      fieldId: state.fieldId,
      groundPrep: {
        ...state.groundPrep,
        tractorDefinition: undefined,
        implementDefinition: undefined,
      },
      priority: 0,
      generateObjectives: true,
    };
  } else {
    throw new Error("createJobRequestFromFormState: invalid job type?");
  }
}

const SetJobForm: FC<SetJobFormProps> = ({
  onSubmit,
  onCancel,
  initialJob,
  mode,
}) => {
  const showFarm = mode === "new-job";
  const showZone = mode === "new-job" || mode === "edit-pending";
  const { data: farms, isLoading: farmsLoading } = useListFarmsQuery(
    {
      contents: true,
    },
    { skip: !showFarm && !showZone }
  );

  const validationSchema = yup.object({
    type: yup
      .mixed<JobType>()
      .oneOf(Object.values(JobType).map((t) => jobTypeFromJSON(t)))
      .required(),
    farmId: yup.string().required(),
    fieldId: yup.string().required(),
    name: yup.string().required(),
    groundPrep: yup.object().when("type", {
      is: JobType.GROUND_PREP,
      // eslint-disable-next-line unicorn/no-thenable
      then: (schema) =>
        schema
          .shape({
            pathPlan: yup.object().shape({
              doHeadlandFirst: yup.boolean().required(),
              rowHeadingDeg: yup.number().min(0).max(360).required(),
              headlandWidthM: yup.number().min(0).required(),
              turnDirection: yup
                .mixed<Direction>()
                .oneOf(
                  Object.values(Direction).map((t) => directionFromJSON(t))
                )
                .required(),
              numHeadlandPasses: yup.number().integer().min(1).required(),
              combinedTurnRadiusM: yup.number().min(0).required(),
            }),
          })
          .required(),
      otherwise: (schema) => schema.notRequired().nullable(),
    }),
  });

  return farmsLoading ? (
    <div className="flex flex-row justify-center">
      <CircularProgress variant="indeterminate" className="mr-2" />
      <Typography className="text-3xl">
        {t("components.Loading.placeholder")}
      </Typography>
    </div>
  ) : (
    <div className="flex flex-col gap-4 p-4">
      <Formik
        initialValues={initialFormStateFromExistingJob(initialJob)}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          onSubmit(createJobRequestFromFormState(values));
        }}
      >
        {({
          values,
          handleChange,
          handleBlur,
          submitForm,
          handleReset,
          isSubmitting,
          isValid,
          setFieldTouched,
          setFieldValue,
          validateForm,
        }) => (
          <Form className="flex flex-col gap-4">
            <FormControl fullWidth>
              <InputLabel id="name-label">
                {titleCase(t("utils.descriptors.name"))}
              </InputLabel>
              <Input
                id="name"
                type="string"
                value={values.name}
                onChange={handleChange}
                onBlur={handleBlur}
              />
            </FormControl>
            <FormControl fullWidth>
              <InputLabel id="type-label">
                {titleCase(t("utils.descriptors.type_one"))}
              </InputLabel>
              <Select
                title={titleCase(t("utils.descriptors.type_one"))}
                labelId="type-label"
                id="type-selector"
                name="type"
                value={values.type}
                label={titleCase(t("utils.descriptors.type_one"))}
                onChange={async (e) => {
                  // work around https://github.com/jaredpalmer/formik/issues/1191
                  // and https://github.com/jaredpalmer/formik/issues/2083
                  setFieldValue("type", e.target.value).finally(() =>
                    setFieldTouched("type", true)
                  );
                  validateForm();
                }}
              >
                <MenuItem value={JobType.LASER_WEED}>
                  {t("models.autotractor.jobTypes.laserWeed")}
                </MenuItem>
                <MenuItem value={JobType.GROUND_PREP}>
                  {t("models.autotractor.jobTypes.groundPrep")}
                </MenuItem>
              </Select>
            </FormControl>
            {showFarm && (
              <FormControl fullWidth>
                <Field
                  component={FormikSelect}
                  title={titleCase(t("models.farms.farm_one"))}
                  id="farmId"
                  name="farmId"
                  value={values.farmId}
                  label={titleCase(t("models.farms.farm_one"))}
                  onChange={handleChange}
                >
                  {farms?.map((f) => (
                    <MenuItem key={f.id?.id} value={f.id?.id}>
                      {f.name}
                    </MenuItem>
                  ))}
                </Field>
              </FormControl>
            )}
            {showZone && (
              <FormControl fullWidth>
                <Field
                  component={FormikSelect}
                  disabled={!values.farmId}
                  title={titleCase(t("models.farms.zone_one"))}
                  id="fieldId"
                  name="fieldId"
                  label={titleCase(t("models.farms.zone_one"))}
                >
                  {values.farmId &&
                    farms
                      ?.find((f) => f.id?.id === values.farmId)
                      ?.zones.filter((z) => Boolean(z.contents?.field)) // todo: should we allow jobs on non-fields?
                      .map((z) => (
                        <MenuItem key={z.id?.id} value={z.id?.id}>
                          {z.name}
                        </MenuItem>
                      ))}
                </Field>
              </FormControl>
            )}
            {values.type === JobType.GROUND_PREP && (
              <FormControl className="flex flex-col gap-4">
                <FormControl fullWidth className="pb-2 pl-2">
                  <FormControlLabel
                    control={
                      <Field
                        component={Switch}
                        type="checkbox"
                        name="groundPrep.pathPlan.doHeadlandFirst"
                      />
                    }
                    label={t("models.pathPlanning.doHeadlandFirst")}
                  />
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel id="turn-direction-label">
                    {titleCase(t("models.pathPlanning.turnDirection"))}
                  </InputLabel>
                  <Select
                    value={values.groundPrep.pathPlan.turnDirection}
                    labelId="turn-direction-label"
                    name="groundPrep.pathPlan.turnDirection"
                    title={t("models.pathPlanning.turnDirection")}
                    label={t("models.pathPlanning.turnDirection")}
                    onChange={(e) => {
                      // work around https://github.com/jaredpalmer/formik/issues/1191
                      // and https://github.com/jaredpalmer/formik/issues/2083
                      setFieldValue(
                        "groundPrep.pathPlan.turnDirection",
                        e.target.value
                      ).finally(() =>
                        setFieldTouched(
                          "groundPrep.pathPlan.turnDirection",
                          true
                        )
                      );
                      validateForm();
                    }}
                  >
                    <MenuItem value={Direction.CW}>
                      {t("utils.units.cwLong")}
                    </MenuItem>
                    <MenuItem value={Direction.CCW}>
                      {t("utils.units.ccwLong")}
                    </MenuItem>
                  </Select>
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>{t("models.pathPlanning.rowHeading")}</InputLabel>
                  <Input
                    type="number"
                    value={values.groundPrep.pathPlan.rowHeadingDeg}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    name="groundPrep.pathPlan.rowHeadingDeg"
                    inputProps={{ min: 0, max: 360, step: 0.1 }}
                    endAdornment={t("utils.units.deg_long")}
                  />
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>
                    {t("models.pathPlanning.headlandWidth")}
                  </InputLabel>
                  <Input
                    type="number"
                    value={values.groundPrep.pathPlan.headlandWidthM}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    name="groundPrep.pathPlan.headlandWidthM"
                    inputProps={{ min: 0, step: 0.05 }}
                    endAdornment={t("utils.units.m")}
                  />
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>
                    {t("models.pathPlanning.headlandPasses")}
                  </InputLabel>
                  <Input
                    type="number"
                    value={values.groundPrep.pathPlan.numHeadlandPasses}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    name="groundPrep.pathPlan.numHeadlandPasses"
                    inputProps={{ min: 0, step: 1 }}
                  />
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>
                    {t("models.pathPlanning.combinedTurnRadius")}
                  </InputLabel>
                  <Input
                    type="number"
                    value={values.groundPrep.pathPlan.combinedTurnRadiusM}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    name="groundPrep.pathPlan.combinedTurnRadiusM"
                    inputProps={{ min: 0, step: 0.1 }}
                    endAdornment={t("utils.units.m")}
                  />
                </FormControl>
              </FormControl>
            )}
            <div className="flex">
              <Button
                fullWidth
                variant="contained"
                disabled={!isValid || isSubmitting}
                type="submit"
                onClick={submitForm}
              >
                {mode === "new-job"
                  ? t("utils.actions.create")
                  : t("utils.actions.save")}
              </Button>
              <Button
                fullWidth
                color="info"
                onClick={() => {
                  onCancel();
                  handleReset();
                }}
              >
                {t("utils.actions.cancel")}
              </Button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export function useNewJobDialog(initialJob?: Partial<Job>): {
  renderDialog: () => ReactNode;
  open: () => void;
  close: () => void;
} {
  const { user } = useSelf();
  const userCustomerId = getCustomerId(user);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [createJob] = useMutationPopups(useCreateJobMutation());

  const handleCreateJob = useCallback(
    async (j: SetJobFormOutput): Promise<void> => {
      const result = await createJob({
        job: {
          ...j,
          customerId: userCustomerId?.toString() ?? "",
        },
      });
      if (!result.error) {
        setIsOpen(false);
      }
    },
    [createJob, userCustomerId]
  );

  const handleCancelJob = useCallback((): void => {
    setIsOpen(false);
  }, []);

  return {
    renderDialog: () => (
      <Dialog open={isOpen} fullWidth>
        <DialogTitle>
          {titleCase(
            t("utils.actions.newLong", {
              subject: t("models.jobs.job_one"),
            })
          )}
        </DialogTitle>
        <DialogContent>
          <SetJobForm
            initialJob={initialJob}
            onCancel={handleCancelJob}
            onSubmit={handleCreateJob}
            mode="new-job"
          />
        </DialogContent>
      </Dialog>
    ),
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
}

export function useEditJobDialog(initialJob: Job): {
  renderDialog: () => ReactNode;
  open: () => void;
  close: () => void;
} {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [updateJob] = useMutationPopups(useUpdateJobMutation());

  // after a job has been readied, don't allow the user to move it between
  // fields; this requires regenerating the job objectives, which can't be
  // done at that point.
  const mode =
    initialJob.state === State.PENDING ? "edit-pending" : "edit-active";

  const handleUpdateJob = async (j: SetJobFormOutput): Promise<void> => {
    const updatedJob = { ...initialJob, ...j };
    const result = await updateJob({
      job: updatedJob,
      generateObjectives:
        initialJob.state === State.PENDING && j.fieldId !== initialJob.fieldId,
    });
    if (!result.error) {
      setIsOpen(false);
    }
  };

  const handleCancelJob = (): void => {
    setIsOpen(false);
  };

  return {
    renderDialog: () => (
      <Dialog open={isOpen} fullWidth>
        <DialogTitle>
          {titleCase(
            t("utils.actions.editLong", {
              subject: t("models.jobs.job_one"),
            })
          )}
        </DialogTitle>
        <DialogContent>
          <SetJobForm
            initialJob={initialJob}
            onCancel={handleCancelJob}
            onSubmit={handleUpdateJob}
            mode={mode}
          />
        </DialogContent>
      </Dialog>
    ),
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
  };
}
