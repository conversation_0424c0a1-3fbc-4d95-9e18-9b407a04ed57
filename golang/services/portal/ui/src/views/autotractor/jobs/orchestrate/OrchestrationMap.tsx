import {
  AutotractorPropsType,
  AutotractorSlotIDs,
} from "common/components/map/layers/types";
import { DateTime } from "luxon";
import { derefPoint, getFieldBounds } from "common/utils/farm";
import { isWithinBounds } from "portal/utils/geo";
import { Job } from "protos/rtc/jobs";
import {
  JobSelectionType,
  Selection,
  setFocus,
  setSelection,
} from "common/state/jobExplorer";
import { LiveRtcDeviceResponse } from "common/hooks/useLiveRtcLocation";
import { Map } from "portal/components/map/Map";
import {
  ObjectiveFeature,
  TaskFeature,
  TractorFeature,
} from "common/utils/rtcJobs";
import { useCenterPivotLayers } from "common/components/map/layers/CenterPivotLayers";
import { useDispatch } from "react-redux";
import { useFarmLayers } from "common/components/map/layers/FarmLayers";
import { useFarmMapData } from "common/hooks/useFarmMapData";
import { useJobExplorer } from "portal/state/store";
import { useListRobotsQuery } from "common/state/rtcLocatorApi";
import { useListTasksQuery } from "common/state/rtcJobsApi";
import { useMapSlots } from "portal/components/map/MapSlots";
import { useObjectiveLayers } from "common/components/map/layers/ObjectiveLayers";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTaskLayers } from "common/components/map/layers/TaskLayers";
import { useTractorLayers } from "common/components/map/layers/TractorLayers";
import { viteImageResolver } from "common/utils/map/imageResolver.vite";
import PivotIcon from "common/images/icons/irrigation.svg?react";
import React, { FunctionComponent, useMemo } from "react";

interface OrchestrationMapProps {
  job: Job;
  hideTasks?: boolean;
  showAllTasks?: boolean;
}

export const OrchestrationMap: FunctionComponent<OrchestrationMapProps> = ({
  job,
  showAllTasks,
}) => {
  const dispatch = useDispatch();
  const {
    farm,
    selection,
    focus,
    hiddenTractors,
    colorsBySerial,
    highlightedObjectives,
    assignmentsByTractorSerial,
    liveTractorData,
    tractorIsMoving,
    tractorIsStale,
    liveDeviceData,
    deviceIsMoving,
    deviceIsStale,
  } = useJobExplorer();
  const farmMapData = useFarmMapData(farm);

  const { data: availableTractors } = useQueryPopups(
    useListRobotsQuery({
      serials: job.robotWhitelist?.entries.map((r) => r.robotSerial),
    })
  );
  const tractorSerials = availableTractors?.robots.map((v) => v.serial);

  // todo: re-evaluate if we move to specifying co-located zones
  const field = useMemo(() => {
    return farm?.zones.find((z) => z.id?.id === job.fieldId);
  }, [farm, job]);
  const centerPivot = useMemo(() => {
    // todo: change to it's own zone when the farm definition supports it
    return field?.contents?.field?.centerPivot?.center && farmMapData
      ? {
          ...field.contents.field.centerPivot,
          center: derefPoint(
            farmMapData.pointsById,
            field.contents.field.centerPivot.center
          ),
        }
      : undefined;
  }, [field, farmMapData]);

  const zoomBounds = useMemo(() => {
    return field && farmMapData?.pointsById
      ? getFieldBounds(field, farmMapData.pointsById)
      : farmMapData?.bounds;
  }, [field, farmMapData]);

  const {
    Layers: FarmLayers,
    idsToLoad: farmIdsToLoad,
    interactionLayers: farmInteractionLayers,
  } = useFarmLayers({
    hoverZone: (feature) => {
      if (!feature?.properties.zoneId) {
        return;
      }
      const zone = farmMapData?.zonesById.get(feature.properties.zoneId);
      if (zone?.contents?.obstacle || !zone) {
        dispatch(
          setFocus(
            zone ? { type: JobSelectionType.ZONE, selection: zone } : undefined
          )
        );
      }
    },
    clickZone: (feature) => {
      if (!feature?.properties.zoneId) {
        return;
      }
      const zone = farmMapData?.zonesById.get(feature.properties.zoneId);
      if (zone?.contents?.obstacle || !zone) {
        dispatch(
          setSelection(
            zone ? { type: JobSelectionType.ZONE, selection: zone } : undefined
          )
        );
      }
    },
  });

  const enabledZoneIds = useMemo(() => {
    // if we can find co-located zones, use those, otherwise use everything
    // todo: as of writing, jobs only specify a field. We may consider changing it to specify
    // multiple zones
    const ids: string[] = [];
    if (field && farmMapData) {
      const fieldBounds = getFieldBounds(field, farmMapData.pointsById);
      if (farm?.zones && fieldBounds) {
        for (const zone of farm.zones) {
          if (
            zone.id?.id &&
            zone.areas[0]?.polygon?.boundary?.points[0]?.id?.id
          ) {
            const ptId = zone.areas[0].polygon.boundary.points[0].id.id;
            const pt = farmMapData.pointsById[ptId];
            const PADDING_METERS = 100;
            if (pt && isWithinBounds(pt, fieldBounds, PADDING_METERS)) {
              ids.push(zone.id.id);
            }
          }
        }
      }
    }
    return ids.length > 0 ? ids : undefined;
  }, [farm, field, farmMapData]);

  const centerPivotLayers = useCenterPivotLayers({
    centerPivot,
    clickPivot: () =>
      centerPivot &&
      dispatch(
        setSelection({
          type: JobSelectionType.CENTER_PIVOT,
          selection: centerPivot,
        })
      ),
    hoverPivot: (f) =>
      dispatch(
        setFocus(
          f && centerPivot
            ? { type: JobSelectionType.CENTER_PIVOT, selection: centerPivot }
            : undefined
        )
      ),
  });

  const {
    Layers: TractorLayers,
    idsToLoad: tractorIdsToLoad,
    interactionLayers: tractorInteractionLayers,
  } = useTractorLayers({
    serials: tractorSerials,
    onClick: (f?: TractorFeature) =>
      dispatch(
        setSelection(
          f?.properties.serial
            ? {
                selection: { serial: f.properties.serial },
                type: JobSelectionType.TRACTOR,
              }
            : undefined
        )
      ),
    onEnter: (f?: TractorFeature) =>
      dispatch(
        setFocus(
          f?.properties.serial
            ? {
                selection: { serial: f.properties.serial },
                type: JobSelectionType.TRACTOR,
              }
            : undefined
        )
      ),
    onLeave: () => dispatch(setFocus(undefined)),
  });

  const getObjOrTask = (
    f: ObjectiveFeature | TaskFeature | undefined
  ): Selection | undefined => {
    if (!f) {
      return undefined;
    }
    if (f.properties.type === AutotractorPropsType.OBJECTIVE) {
      return {
        type: JobSelectionType.OBJECTIVE,
        selection: f.properties.objective,
      };
    }
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (f.properties.type === AutotractorPropsType.TASK) {
      return {
        type: JobSelectionType.TASK,
        selection: f.properties.task,
      };
    }
  };

  const {
    Layers: ObjectiveLayers,
    idsToLoad: objectiveIdsToLoad,
    interactionLayers: objectiveInteractionLayers,
  } = useObjectiveLayers({
    onEnter: (f) => {
      dispatch(setFocus(getObjOrTask(f)));
    },
    onClick: (f) => {
      dispatch(setSelection(getObjOrTask(f)));
    },
    onLeave: () => dispatch(setFocus(undefined)),
  });

  const {
    Layers: TaskLayers,
    idsToLoad: taskIdsToLoad,
    interactionLayers: taskInteractionLayers,
  } = useTaskLayers({
    onEnter: (f) => {
      dispatch(setFocus(getObjOrTask(f)));
    },
    onClick: (f) => {
      dispatch(setSelection(getObjOrTask(f)));
    },
    onLeave: () => dispatch(setFocus(undefined)),
  });

  const CenterPivotLayers = centerPivotLayers?.Layers;
  const liveCenterPivotData: LiveRtcDeviceResponse | undefined =
    centerPivot && liveDeviceData[centerPivot.endpointDeviceId]
      ? {
          location: liveDeviceData[centerPivot.endpointDeviceId],
          isMoving: Boolean(deviceIsMoving[centerPivot.endpointDeviceId]),
          isStale: Boolean(deviceIsStale[centerPivot.endpointDeviceId]),
          lastCheckedTimestamp: DateTime.now().toISO(),
        }
      : undefined;

  const interactionLayers = useMemo(() => {
    return [
      ...farmInteractionLayers,
      ...tractorInteractionLayers,
      ...objectiveInteractionLayers,
      ...taskInteractionLayers,
      ...(centerPivotLayers?.interactionLayers ?? []),
    ];
  }, [
    farmInteractionLayers,
    tractorInteractionLayers,
    objectiveInteractionLayers,
    taskInteractionLayers,
    centerPivotLayers,
  ]);

  const mapSlots = useMapSlots(Object.values(AutotractorSlotIDs));

  const { data: tasks } = useListTasksQuery(
    {
      objectiveIds: job.objectives.map((o) => o.id),
    },
    { refetchOnMountOrArgChange: 5, pollingInterval: 5000 }
  );

  const shownTasks =
    (showAllTasks
      ? tasks?.tasks
      : tasks?.tasks.filter((t) => {
          return (
            (selection?.type === JobSelectionType.OBJECTIVE &&
              selection.selection.id === t.objectiveId) ||
            (selection?.type === JobSelectionType.TASK &&
              selection.selection.id === t.id)
          );
        })) ?? [];

  return (
    <Map
      allowEmpty
      extraBounds={zoomBounds}
      interactionLayers={interactionLayers}
      hideHoverInfo
      imageConfig={{
        resolver: viteImageResolver,
        idsToLoad: [
          ...(centerPivotLayers ? centerPivotLayers.idsToLoad : []),
          ...farmIdsToLoad,
          ...objectiveIdsToLoad,
          ...taskIdsToLoad,
          ...tractorIdsToLoad,
        ],
      }}
      extraControls={
        <>
          {mapSlots}
          {farmMapData && (
            <FarmLayers
              beforeId={AutotractorSlotIDs.FARMS}
              enabledZoneIds={enabledZoneIds}
              shownPoints={[]}
              selectedZoneId={
                selection?.type === JobSelectionType.ZONE
                  ? selection.selection.id?.id
                  : undefined
              }
              focusedZoneId={
                focus?.type === JobSelectionType.ZONE
                  ? focus.selection.id?.id
                  : undefined
              }
              hidePivots
              farmMapData={farmMapData}
            />
          )}

          {CenterPivotLayers && centerPivot && liveCenterPivotData && (
            <CenterPivotLayers
              beforeId={AutotractorSlotIDs.OBSTACLES}
              liveData={liveCenterPivotData}
              warnOnMove
              isSelected={
                selection?.type === JobSelectionType.CENTER_PIVOT &&
                selection.selection.endpointDeviceId ===
                  centerPivot.endpointDeviceId
              }
              isFocused={
                focus?.type === JobSelectionType.CENTER_PIVOT &&
                focus.selection.endpointDeviceId ===
                  centerPivot.endpointDeviceId
              }
              centerPivot={centerPivot}
              onClick={() =>
                dispatch(
                  setSelection({
                    type: JobSelectionType.CENTER_PIVOT,
                    selection: centerPivot,
                  })
                )
              }
              onMouseEnter={() =>
                dispatch(
                  setFocus({
                    type: JobSelectionType.CENTER_PIVOT,
                    selection: centerPivot,
                  })
                )
              }
              onMouseLeave={() => dispatch(setFocus(undefined))}
              PivotIcon={PivotIcon}
            />
          )}
          {availableTractors &&
            tractorSerials &&
            tractorSerials
              .filter((s) => !hiddenTractors.includes(s))
              .map(
                (s) =>
                  liveTractorData[s] && (
                    <TractorLayers
                      liveData={{
                        location: liveTractorData[s],
                        isMoving: Boolean(tractorIsMoving[s]),
                        isStale: Boolean(tractorIsStale[s]),
                        lastCheckedTimestamp: DateTime.now().toISO(),
                      }}
                      beforeId={AutotractorSlotIDs.OBSTACLES}
                      key={s}
                      serial={s}
                      color={colorsBySerial[s]}
                      isSelected={
                        selection?.type === JobSelectionType.TRACTOR &&
                        selection.selection.serial === s
                      }
                      onClick={() =>
                        dispatch(
                          setSelection({
                            type: JobSelectionType.TRACTOR,
                            selection: { serial: s },
                          })
                        )
                      }
                      isFocused={
                        focus?.type === JobSelectionType.TRACTOR &&
                        focus.selection.serial === s
                      }
                    />
                  )
              )}
          <ObjectiveLayers
            beforeId={AutotractorSlotIDs.JOB_OBJECTIVES}
            objectives={job.objectives}
            focusedTractor={
              focus?.type === JobSelectionType.TRACTOR
                ? focus.selection.serial
                : undefined
            }
            hiddenTractors={hiddenTractors}
            selectedObjective={
              selection?.type === JobSelectionType.OBJECTIVE
                ? selection.selection.id
                : undefined
            }
            focusedObjective={
              focus?.type === JobSelectionType.OBJECTIVE
                ? focus.selection.id
                : undefined
            }
            highlightedObjectives={highlightedObjectives}
            colorsBySerial={colorsBySerial}
            assignmentsByTractorSerial={assignmentsByTractorSerial}
          />
          <TaskLayers
            showTasks={true}
            beforeId={AutotractorSlotIDs.JOB_TASKS}
            tasks={shownTasks}
          />
        </>
      }
    ></Map>
  );
};
