import { AppStore, useSelf } from "portal/state/store";
import { auth, buildPermission } from "portal/utils/auth";
import { AutotractorMap } from "./AutotractorMap";
import {
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  IconButton,
  Input,
  LinearProgress,
  Paper,
  Skeleton,
  Snackbar,
  ToggleButton,
  Typography,
} from "@mui/material";
import { classes } from "portal/theme/theme";
import {
  clearHistory,
  clearTractorHistory,
  updateHistory,
} from "portal/state/location";
import { DateTime, Duration } from "luxon";
import { DateTimeRangePicker } from "@mui/x-date-pickers-pro";
import { Farm } from "protos/portal/farm";
import { formatMeasurement } from "portal/components/measurement/formatters";
import { ListLocationHistoryResponse } from "protos/rtc/location_history";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { range } from "portal/utils/arrays";
import { SelectionPanel } from "./SelectionPanel";
import {
  timeRangeParams,
  useListRobotsQuery,
} from "common/state/rtcLocatorApi";
import { titleCase } from "portal/utils/strings";
import { useDispatch, useStore } from "react-redux";
import { useListFarmsQuery } from "common/state/portalApi/farm";
import { useListRobotsQuery as usePortalListRobotsQuery } from "portal/state/portalApi";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { withAuthorizationRequired } from "portal/components/auth/WithAuthorizationRequired";
import CircleIcon from "@mui/icons-material/Circle";
import FocusIcon from "@mui/icons-material/CenterFocusStrongOutlined";
import React, {
  Fragment,
  FunctionComponent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import SyncIcon from "@mui/icons-material/Sync";

export const _AutotractorOverwatch: FunctionComponent = () => {
  const { i18n, t } = useTranslation();
  const { measurementSystem } = useSelf();
  const store: AppStore = useStore();
  const dispatch = useDispatch();

  // rtc tracked robots
  const { data, isLoading } = useListRobotsQuery({
    serials: undefined,
  });

  const [selections, setSelections] = useState<
    {
      serial: string;
      customerId?: number;
      farms?: Farm[];
      selectedFarm?: Farm;
    }[]
  >([]);
  const [showRows, setShowRows] = useState<boolean>(false);
  const [rowWidthMeters, setRowWidthMeters] = useState<number>(6.7056);
  const [workingRowWidth, setWorkingRowWidth] = useState<string>(`264`);
  const [tractorHistoryWidthMeters, setTractorHistoryWidthMeters] =
    useState<number>(6.7056);
  const [workingTractorHistoryWidth, setWorkingTractorHistoryWidth] =
    useState<string>(`22`);
  const [focusedRobotSerial, setFocusedRobotSerial] = useState<
    string | undefined
  >();
  const selectedRobotSerials = useMemo(
    () => selections.map(({ serial }) => serial),
    [selections]
  );
  interface DateRange {
    start: DateTime;
    end?: DateTime;
  }
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>({
    start: DateTime.now().minus(Duration.fromObject({ hours: 6 })),
  });
  const lastFetchedTime = useRef<DateTime>(DateTime.now());
  const liveUpdates = useRef<boolean>(selectedDateRange.end === undefined);
  const [isFetchingLocationData, setIsFetchingLocationData] =
    useState<boolean>(false);
  const [hasError, setHasError] = useState<string | undefined>();
  const [retryCount, setRetryCount] = useState<number>(0);
  const [selectedFarmId, setSelectedFarmId] = useState<string | undefined>(
    undefined
  );

  // eventually we are indexing off of jobId, which only has one associated farm.
  // we should revisit this as soon as jobs are in a workable state.
  // I'm not concerned about performance here since we only have one production farm until the summer time.
  const { data: farms, isLoading: isFarmsLoading } = useQueryPopups(
    useListFarmsQuery({ contents: true })
  );
  const selectedFarm = farms?.find((f) => f.id?.id === selectedFarmId);

  useEffect(() => {
    dispatch(clearHistory());
  }, [selectedDateRange, dispatch]);

  useEffect(() => {
    const fetchLocations = async (
      start: DateTime,
      serials: string[],
      signal: AbortSignal,
      doBacktrack: boolean,
      end?: DateTime
    ): Promise<void> => {
      for (const serial of serials) {
        const totalHistory = [];
        let nextPage = true;
        let timestamp = start;
        // collate the large initial fetch. I found this to be a better experience than rapid state updates
        // (the map component feels more responsive with one large update than with many tiny updates)
        while (nextPage && !signal.aborted) {
          const accessToken = await auth.getAccessTokenSilently();
          const headers = {
            authorization: `Bearer ${accessToken}`,
            "x-auth0-audience": window._jsenv.REACT_APP_AUTH0_AUDIENCE,
          };
          const searchParameters = new URLSearchParams({
            ...timeRangeParams({ start: timestamp, end }),
            desc: "false",
          });
          const serialComponent = encodeURIComponent(serial);
          const origin = window._jsenv.REACT_APP_RTC_LOCATOR_URL;
          const response = await fetch(
            `${origin}/v1/robots/${serialComponent}/location-history?${searchParameters}`,
            { headers, signal }
          );
          if (!response.ok) {
            throw new Error(
              `Not OK: ${response.status} ${response.statusText}`
            );
          }
          const data = await response.json();
          const { history, nextPageToken } =
            ListLocationHistoryResponse.fromJSON(data);
          nextPage = Boolean(nextPageToken);
          if (!history) {
            nextPage = false;
            continue;
          }
          totalHistory.push(...history.records);
          const lastTimestamp = history.records.at(-1)?.timestamp;
          if (!lastTimestamp || !nextPageToken || !doBacktrack) {
            nextPage = false;
            continue;
          }
          timestamp = DateTime.fromISO(lastTimestamp);
        }
        dispatch(updateHistory({ serial, updates: totalHistory }));
      }
      if (doBacktrack === true) {
        setIsFetchingLocationData(false);
      }
    };
    const ac = new AbortController();
    const signal = ac.signal;
    liveUpdates.current = selectedDateRange.end === undefined;
    setIsFetchingLocationData(true);
    const timeout = setTimeout(() => {
      ac.abort("Request timed out.");
      clearInterval(refetchInterval);
      setIsFetchingLocationData(false);
      setHasError("Location fetch timed out");
    }, 300_000); // 5 min timeout
    fetchLocations(
      selectedDateRange.start,
      selectedRobotSerials,
      signal,
      true,
      selectedDateRange.end
    )
      .then(() => clearTimeout(timeout))
      .catch(console.warn);
    let refetchInterval: NodeJS.Timeout | undefined;
    if (liveUpdates.current) {
      lastFetchedTime.current = DateTime.now();
      refetchInterval = setInterval(() => {
        fetchLocations(
          lastFetchedTime.current,
          selectedRobotSerials,
          signal,
          false
        ).catch(console.warn);
        lastFetchedTime.current = DateTime.now();
      }, 5000);
    }

    return () => {
      ac.abort("date range or serials changed");
      clearTimeout(timeout);
      clearInterval(refetchInterval);
      setIsFetchingLocationData(false);
    };
  }, [selectedRobotSerials, selectedDateRange, retryCount, dispatch]);

  useEffect(() => {
    const existingSerials = Object.keys(store.getState().location.history);
    const selectedSet = new Set(selectedRobotSerials);
    for (const existing of existingSerials) {
      if (!selectedSet.has(existing)) {
        dispatch(clearTractorHistory({ serial: existing }));
      }
    }
  }, [selectedRobotSerials, store, dispatch]);

  const robots = data?.robots.map(({ serial }) => serial) ?? [];

  // portal robot data - note purposefully fetching all instead of one at a time because the individual query is overloaded for this use case
  const { data: portalRobots, isLoading: isPortalRobotsLoading } =
    useQueryPopups(usePortalListRobotsQuery({}));

  const connectSerialToFarmData = useCallback(
    (serial: string): { customerId?: number; farms?: Farm[] } | undefined => {
      const match = portalRobots?.find((r) => r.robot?.serial === serial);
      const customerId = match ? match.customer?.db?.id : undefined;
      let customerFarms: Farm[] | undefined;
      if (customerId && farms) {
        customerFarms = farms.filter((f) => f.customerId === customerId);
      }
      return { customerId, farms: customerFarms };
    },
    [portalRobots, farms]
  );

  const onSelectRobot = useCallback(
    (serial: string, checked: boolean): void => {
      let farmData;
      if (checked) {
        farmData = connectSerialToFarmData(serial);
      }
      let newSelections = [];
      if (checked) {
        const alreadyExists = selections.some(
          ({ serial: selectionSerial }) => selectionSerial === serial
        );
        newSelections = alreadyExists
          ? selections
          : [...selections, { serial, ...farmData }];
      } else {
        if (focusedRobotSerial === serial) {
          setFocusedRobotSerial(undefined);
        }
        newSelections = selections.filter((s) => s.serial !== serial);
      }
      setSelections(newSelections);
    },
    [connectSerialToFarmData, focusedRobotSerial, selections]
  );

  const handleRowWidthChange = (value: string): void => {
    const parsed = Number.parseFloat(value);
    let val = parsed;
    if (Number.isNaN(val)) {
      val = 264;
    }
    if (val < 12) {
      val = 12;
    }
    if (val > 1000) {
      val = 1000;
    }
    const converted = formatMeasurement(t, i18n, measurementSystem, val, "in", {
      toUnits: "m",
    });
    if (converted.converted) {
      setRowWidthMeters(converted.converted);
      setWorkingRowWidth(`${val}`);
    }
  };

  const handleTractorHistoryWidthChange = (value: string): void => {
    const parsed = Number.parseFloat(value);
    let val = parsed;
    if (Number.isNaN(val)) {
      val = 264;
    }
    if (val < 12) {
      val = 12;
    }
    if (val > 1000) {
      val = 1000;
    }
    const converted = formatMeasurement(t, i18n, measurementSystem, val, "ft", {
      toUnits: "m",
    });
    if (converted.converted) {
      setTractorHistoryWidthMeters(converted.converted);
      setWorkingTractorHistoryWidth(`${val}`);
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row gap-3 flex-grow mb-[4rem]">
        <div className="flex flex-col w-1/5">
          <SelectionPanel
            farms={farms ?? []}
            selectedFarmId={selectedFarmId}
            setSelectedFarmId={(id) => setSelectedFarmId(id)}
          >
            {isLoading ? (
              <div className="flex flex-col gap-3">
                {range(3).map((index) => (
                  <Skeleton
                    key={index}
                    variant="rectangular"
                    className="w-full h-12"
                  />
                ))}
              </div>
            ) : (
              <RobotSelector
                isLoading={isPortalRobotsLoading || isFarmsLoading}
                options={robots}
                selected={selectedRobotSerials}
                onSelect={onSelectRobot}
                focused={focusedRobotSerial}
                onFocus={(serial, isFocused) => {
                  setFocusedRobotSerial(isFocused ? serial : undefined);
                  onSelectRobot(serial, true);
                }}
              />
            )}
          </SelectionPanel>
        </div>
        <div className="flex-grow flex-col">
          <Paper className="flex flex-grow justify-between p-3 gap-3">
            <div className="flex gap-3">
              <ToggleButton
                value="check"
                className="text-white"
                selected={showRows}
                onChange={() => setShowRows(!showRows)}
              >
                {showRows
                  ? t("views.autotractor.hideRows")
                  : t("views.autotractor.showRows")}
              </ToggleButton>
              {showRows && (
                <div>
                  <Typography>{`${t("views.autotractor.rowWidthUnits", {
                    units: t("utils.units.in"),
                  })}`}</Typography>
                  <Input
                    title={t("views.autotractor.rowWidthUnits", {
                      units: t("utils.units.in"),
                    })}
                    type="number"
                    value={workingRowWidth}
                    onChange={(e) => setWorkingRowWidth(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleRowWidthChange(
                          (e.target as HTMLInputElement).value
                        );
                      }
                    }}
                    onBlur={(e) => handleRowWidthChange(e.target.value)}
                  />
                </div>
              )}
              <div>
                <Typography>
                  {t("views.autotractor.historyWidthUnits", {
                    units: t("utils.units.ft"),
                  })}
                </Typography>
                <Input
                  title={t("views.autotractor.historyWidthUnits", {
                    units: t("utils.units.ft"),
                  })}
                  type="number"
                  value={workingTractorHistoryWidth}
                  onChange={(e) =>
                    setWorkingTractorHistoryWidth(e.target.value)
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleTractorHistoryWidthChange(
                        (e.target as HTMLInputElement).value
                      );
                    }
                  }}
                  onBlur={(e) =>
                    handleTractorHistoryWidthChange(e.target.value)
                  }
                />
              </div>
            </div>
            <DateTimeRangePicker
              className="ml-auto"
              disabled={selectedRobotSerials.length === 0}
              closeOnSelect={false}
              value={[
                selectedDateRange.start,
                // eslint-disable-next-line unicorn/no-null
                selectedDateRange.end ?? null,
              ]}
              minDate={DateTime.fromISO("2020-01-01T00:00:00.000")}
              onAccept={([start, end]) => {
                if (start !== null) {
                  setSelectedDateRange({
                    start,
                    end: end ?? undefined,
                  });
                }
              }}
            />
            {selectedDateRange.end === undefined ? (
              <div className="flex my-auto gap-1">
                <span>{titleCase(t("views.autotractor.live"))}</span>
                <CircleIcon
                  className="flex my-auto"
                  color="primary"
                  fontSize="small"
                />
              </div>
            ) : (
              <IconButton
                className="flex my-auto"
                aria-label={t("views.autotractor.goLive")}
                onClick={() =>
                  setSelectedDateRange({ start: selectedDateRange.start })
                }
              >
                <SyncIcon color="primary" />
              </IconButton>
            )}
          </Paper>
          {isFetchingLocationData && <LinearProgress className="flex" />}
          <AutotractorMap
            robots={selectedRobotSerials}
            selectedFarm={selectedFarm}
            rows={{ showRows, rowWidthMeters }}
            tractorHistoryWidthMeters={tractorHistoryWidthMeters}
            focusedRobotSerial={focusedRobotSerial}
            setFocusedRobot={setFocusedRobotSerial}
          />
        </div>
      </div>
      <Snackbar
        open={Boolean(hasError)}
        message={t("views.autotractor.fetchFailed")}
        action={
          <Button
            onClick={() => {
              setHasError(undefined);
              setRetryCount(retryCount + 1);
            }}
          >
            {titleCase(t("utils.actions.retry"))}
          </Button>
        }
      />
    </>
  );
};

interface RobotSelectorProps {
  options: string[];
  focused?: string;
  selected: string[];
  onSelect: (serial: string, checked: boolean) => void;
  onFocus: (serial: string, isFocused: boolean) => void;
  className?: string;
  isLoading?: boolean;
}
const RobotSelector: FunctionComponent<RobotSelectorProps> = ({
  options,
  focused,
  selected,
  onSelect,
  onFocus,
  className,
  isLoading = false,
}) => {
  const selectedSet = new Set(selected);
  return (
    <div className={classes("flex flex-col items-start", className)}>
      {options.map((serial) => {
        let focusIconColor = isLoading ? "text-gray-300" : "text-white";
        if (focused === serial) {
          focusIconColor = "text-carbon-orange";
        }

        return (
          <div key={serial}>
            <IconButton
              onClick={() => onFocus(serial, focused !== serial)}
              disabled={isLoading}
            >
              <FocusIcon className={focusIconColor} />
            </IconButton>
            <FormControlLabel
              label={serial}
              control={
                <Checkbox
                  checked={selectedSet.has(serial)}
                  onChange={(event, checked) => onSelect(serial, checked)}
                  disabled={isLoading}
                />
              }
            />
            {isLoading && (
              <CircularProgress
                className="w-3 h-3"
                classes={{ circle: "text-white" }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export const AutotractorOverwatch = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.autotractor_jobs,
      PermissionDomain.all
    ),
    buildPermission(
      PermissionAction.read,
      PermissionResource.autotractor_jobs,
      PermissionDomain.customer
    ),
  ],
  _AutotractorOverwatch
);
