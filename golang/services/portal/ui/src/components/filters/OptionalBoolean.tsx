import { MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { SMALL_SELECT_DARK } from "portal/theme/theme";
import { useTranslation } from "react-i18next";
import React, { FC, ReactElement } from "react";

type OptionalBooleanValue = "true" | "false" | "undefined";

interface OptionalBooleanSelectProps {
  value: boolean | undefined;
  onChange: (value: boolean | undefined) => void;
  trueText?: string;
  trueIcon?: ReactElement;
  falseText?: string;
  falseIcon?: ReactElement;
}

export const OptionalBooleanSelect: FC<OptionalBooleanSelectProps> = ({
  value,
  onChange,
  trueText,
  trueIcon,
  falseText,
  falseIcon,
}) => {
  const { t } = useTranslation();
  const options: Array<{
    id: OptionalBooleanValue;
    name: string;
    icon?: ReactElement;
  }> = [
    {
      id: "true",
      name: trueText ?? t("utils.descriptors.yes"),
      icon: trueIcon,
    },
    {
      id: "false",
      name: falseText ?? t("utils.descriptors.no"),
      icon: falseIcon,
    },
    {
      id: "undefined",
      name: t("utils.descriptors.none"),
    },
  ];
  const handleChange = (event: SelectChangeEvent<unknown>): void => {
    const val = event.target.value;
    if (val === "true") {
      onChange(true);
    } else if (val === "false") {
      onChange(false);
    } else {
      onChange(undefined);
    }
  };

  const stringifiedValue: OptionalBooleanValue =
    // eslint-disable-next-line no-nested-ternary
    value === true ? "true" : value === false ? "false" : "undefined";

  return (
    <Select
      {...SMALL_SELECT_DARK}
      variant="outlined"
      value={stringifiedValue}
      onChange={handleChange}
    >
      {options.map(({ id, name, icon }) => (
        <MenuItem key={id} value={id}>
          <div className="flex items-center gap-1">
            {icon}
            {name}
          </div>
        </MenuItem>
      ))}
    </Select>
  );
};
