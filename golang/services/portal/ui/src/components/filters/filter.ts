import { DateRangeValue, Entity, NumericalRangeValue } from "./filter.types";
import { DateTime } from "luxon";
import { i18n as I18n, TFunction } from "i18next";
import { isNil } from "common/utils/identity";

const DISPLAY_SEPARATOR = ", ";

/** Converts a date to a human readable string */
export const dateToString = (i18n: I18n, date?: Date): string =>
  date ? new Date(date).toLocaleString(i18n.language) : "";

/** Converts a date range to a human readable string */
export const dateRangeToReadableString = (
  t: TFunction,
  i18n: I18n,
  range: DateRangeValue | undefined
): string => {
  if (!range || (!range[0] && !range[1])) {
    return "";
  }
  const format = (date: DateTime): string =>
    dateToString(i18n, date.toJSDate());
  if (!range[0] && range[1]) {
    return t("components.filters.lessOrEqualTo", { value: format(range[1]) });
  }
  if (!range[1] && range[0]) {
    return t("components.filters.greaterOrEqualTo", {
      value: format(range[0]),
    });
  }
  return t("components.filters.range", {
    min: range[0] ? format(range[0]) : "",
    max: range[1] ? format(range[1]) : "",
  });
};

/** Converts a numerical range to a human readable string */
export const numericalRangeToReadableString = (
  t: TFunction,
  range: NumericalRangeValue | undefined
): string => {
  if (!range || (!range[0] && !range[1])) {
    return "";
  }
  if (!range[0] && range[1]) {
    return t("components.filters.lessOrEqualTo", { value: range[1] });
  }
  if (!range[1] && range[0]) {
    return t("components.filters.greaterOrEqualTo", { value: range[0] });
  }
  return t("components.filters.range", { min: range[0], max: range[1] });
};

const entityToReadableString = (entity: Entity): string => entity.name;
export const entitiesToReadableString = (entities: Entity[]): string =>
  entities.map((e) => entityToReadableString(e)).join(DISPLAY_SEPARATOR);

export const booleanToReadableString = (
  t: TFunction,
  value?: boolean
): string => {
  if (isNil(value)) {
    return "";
  }
  return value ? t("utils.descriptors.yes") : t("utils.descriptors.no");
};
