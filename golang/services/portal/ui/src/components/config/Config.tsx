import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  FormControl,
  FormHelperText,
  InputAdornment,
  MenuItem,
  OutlinedInput,
  Paper,
} from "@mui/material";
import { AnyObjectSchema, boolean, number, object, string } from "yup";
import {
  BLUE_LOADING_BUTTON,
  classes,
  INPUT_DARK,
  RED_LOADING_BUTTON,
  SELECT_DARK,
  TEXT_FIELD_DARK,
} from "portal/theme/theme";
import { buildPermission } from "portal/utils/auth";
import { capitalize, titleCase } from "portal/utils/strings";
import { ConfigAuditLog } from "./ConfigAuditLog";
import { ConfigCacheRefresh } from "./ConfigCacheRefresh";
import { ConfigNode } from "protos/config/api/config_service";
import { Field, Form, Formik } from "formik";
import {
  filterConfigTree,
  getConfigChanges,
  getNodeFromPath,
  getParentPath,
  include<PERSON><PERSON><PERSON>,
  isBooleanNode,
  isFloatNode,
  isIntNode,
  isLeafNode,
  isListNode,
  isStringNode,
  isUintNode,
} from "portal/utils/configs";
import { TextField as FormikTextField, Select } from "formik-mui";
import { LoadingButton } from "@mui/lab";
import {
  MutationActionCreatorResult,
  QueryStatus,
  skipToken,
} from "@reduxjs/toolkit/query";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { SwitchWithLabel } from "portal/components/SwitchWithLabel";
import { Tree } from "./ConfigTree";
import { UnsyncedKeyWarning } from "./UnsyncedKeyWarning";
import {
  useAuthorizationRequired,
  withAuthorizationRequired,
} from "../auth/WithAuthorizationRequired";
import {
  useDeleteConfigPathMutation,
  useDeleteConfigTemplatePathMutation,
  useGetConfigQuery,
  useGetConfigTemplateQuery,
  useSetConfigTemplateValueMutation,
  useSetConfigValueMutation,
} from "portal/state/portalApi";
import { useLocation, useNavigate } from "react-router-dom";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { usePageRef } from "../Page";
import { useTranslation } from "react-i18next";
import AddIcon from "@mui/icons-material/AddOutlined";
import DeleteIcon from "@mui/icons-material/DeleteOutlined";
import React, {
  FunctionComponent,
  ReactElement,
  useEffect,
  useMemo,
  useState,
} from "react";
import SaveIcon from "@mui/icons-material/SaveOutlined";

interface ConfigPaths {
  changedPathsTree?: ConfigNode | undefined;
  changedPathsAncestors?: string[] | undefined;
  addedPaths?: string[] | undefined;
  changedPaths?: string[] | undefined;
  removedPaths?: string[] | undefined;
}

interface BaseProps {
  basePath?: string;
  readOnly?: boolean;
}

interface SerialProps extends BaseProps {
  serial?: string;
}

interface TemplateProps extends BaseProps {
  robotClass?: string;
}

const isPropsSerial = (
  props: SerialProps | TemplateProps
): props is SerialProps => "serial" in props;

const isPropsTemplate = (
  props: SerialProps | TemplateProps
): props is TemplateProps => "robotClass" in props;

const _Config: FunctionComponent<SerialProps | TemplateProps> = ({
  basePath: inputPath,
  readOnly = false,
  ...props
}) => {
  const { t } = useTranslation();
  const basePath = inputPath?.replace(/\/+$/, "") ?? "";
  const isSerial = isPropsSerial(props);
  const isTemplate = isPropsTemplate(props);
  const useSetValueMutation = isTemplate
    ? useSetConfigTemplateValueMutation
    : useSetConfigValueMutation;
  const useDeletePathMutation = isTemplate
    ? useDeleteConfigTemplatePathMutation
    : useDeleteConfigPathMutation;
  let serial: string | undefined;
  if (isTemplate) {
    serial = props.robotClass;
  }
  if (isSerial) {
    serial = props.serial;
  }

  const pageRef = usePageRef();
  const [isAuditLogOpen, setAuditLogOpen] = useState(false);

  const { data: configData } = useQueryPopups(
    useGetConfigQuery(isSerial && serial ? { serial } : skipToken)
  );
  const { data: templateData } = useQueryPopups(
    useGetConfigTemplateQuery(isTemplate && serial ? { serial } : skipToken)
  );
  const config = isTemplate ? templateData : configData?.config;
  const template = isSerial ? configData?.template : undefined;
  const unsyncedKeys = isSerial ? configData?.unsyncedKeys : [];

  const canUpdate = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.configs,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.update,
      PermissionResource.configs,
      PermissionDomain.all
    ),
  ]);

  // get default config path from URL
  const { pathname, state: locationState } = useLocation();
  let path = pathname.replaceAll(basePath, "");
  // fix any double slashed
  path = path.replaceAll(/\/{2,}/g, "/");
  // remove leading slash if any
  path = path.replace(/^\//, "");
  const [selectedPath, setSelectedPath] = useState<string>(path || "");

  // keep config path up to date
  const navigate = useNavigate();
  useEffect(() => {
    if (selectedPath) {
      navigate(`${basePath}/${selectedPath}`, {
        replace: true,
        state: locationState,
      });
    }
  }, [basePath, navigate, selectedPath, locationState]);

  const {
    changedPathsTree,
    changedPathsAncestors,
    addedPaths,
    changedPaths,
    removedPaths,
  } = useMemo<ConfigPaths>(() => {
    if (!config || !template) {
      return {};
    }
    const { addedPaths, changedPaths, removedPaths, allChangedPaths } =
      getConfigChanges(config, template);
    const changedPathsAncestors = includeAncestors(allChangedPaths);
    return {
      changedPathsTree: filterConfigTree(config, changedPathsAncestors, "", {
        ignoreRoot: true,
        showChildren: false,
      }),
      changedPathsAncestors,
      addedPaths,
      changedPaths,
      removedPaths,
    };
  }, [config, template]);

  const selectedNode = getNodeFromPath(config, selectedPath);
  const parentPath = getParentPath(selectedPath);
  const parentNode = getNodeFromPath(config, parentPath);
  const templateNode = getNodeFromPath(template, selectedPath);

  // handle update value
  const [setConfigValue] = useMutationPopups(useSetValueMutation(), {
    success: capitalize(
      t("utils.actions.savedLong", {
        subject: t("models.configs.value_one"),
      })
    ),
  });
  const updateItem = async (
    value: any
  ): Promise<MutationActionCreatorResult<any> | undefined> => {
    if (!serial || !config) {
      return;
    }
    const response = await setConfigValue({
      serial,
      path: selectedPath,
      value,
    });
    if (isListNode(selectedNode)) {
      setSelectedPath(`${selectedPath}/${value}`);
    }
    return response;
  };

  // handle list delete
  const [deleteValue, { status }] = useMutationPopups(useDeletePathMutation(), {
    success: capitalize(
      t("utils.actions.deletedLong", {
        subject: t("models.configs.key_one"),
      })
    ),
  });

  const isDeleting = status === QueryStatus.pending;
  const deleteItem = async (path: string): Promise<void> => {
    if (!serial) {
      return;
    }
    await deleteValue({ serial, path });
    setSelectedPath(parentPath);
  };

  // generarte appropriate field
  let field: ReactElement | null;
  let defaultValue: string | number | boolean = "";
  let schema: AnyObjectSchema | undefined;
  let value: string | number | boolean = "";
  const units = selectedNode?.def?.units;
  let hint = selectedNode?.def?.hint;
  const isDeprecated = hint && hint.toLowerCase().includes("deprecated");
  const deprecationWarning = isDeprecated ? hint : undefined;
  if (isDeprecated) {
    hint = undefined;
  }
  const deprecationAlert = deprecationWarning ? (
    <Alert severity="error">{deprecationWarning}</Alert>
  ) : (
    <></>
  );
  if (isBooleanNode(selectedNode)) {
    const formattedUnits = units ? ` ${units}` : "";
    field = (
      <FormControl>
        <div className="flex gap-2">
          <Field
            component={SwitchWithLabel}
            type="checkbox"
            name="value"
            label={`${selectedNode?.name}${formattedUnits}`}
            disabled={readOnly || !canUpdate}
          />
        </div>
        {hint && <FormHelperText>{hint}</FormHelperText>}
        {deprecationAlert}
      </FormControl>
    );
    schema = object({
      value: boolean()
        .required(t("utils.form.required"))
        .typeError(t("utils.form.booleanType")),
    });
    value = selectedNode?.value?.boolVal ?? false;
    defaultValue = templateNode?.value?.boolVal ?? false;
  } else if (isStringNode(selectedNode)) {
    const stringDef = selectedNode?.def?.stringDef;
    const sizeLimit = stringDef?.sizeLimit || Infinity; // zero means unlimited
    const choices = stringDef?.choices;
    if (Array.isArray(choices) && choices.length > 0) {
      field = (
        <>
          <Field
            {...SELECT_DARK}
            labelId={`field-${selectedNode?.name}`}
            component={Select}
            className={classes(SELECT_DARK.className, "min-w-52")}
            autoWidth
            name="value"
            label={selectedNode?.name}
            input={<OutlinedInput {...INPUT_DARK} label={selectedNode?.name} />}
            formHelperText={{ children: hint }}
            disabled={readOnly || !canUpdate}
          >
            {choices.map((choice: string) => (
              <MenuItem key={choice} value={choice}>
                {choice}
                {units ? ` ${units}` : ""}
              </MenuItem>
            ))}
          </Field>
          {deprecationAlert}
        </>
      );
      schema = object({
        value: string(),
      });
    } else {
      field = (
        <>
          <Field
            {...TEXT_FIELD_DARK}
            component={FormikTextField}
            InputProps={{
              endAdornment: units && (
                <InputAdornment position="end">{units}</InputAdornment>
              ),
            }}
            name="value"
            label={selectedNode?.name}
            helperText={hint}
            disabled={readOnly || !canUpdate}
          />
          {deprecationAlert}
        </>
      );
      schema = object({
        value: string()
          .test(
            "size_limit",
            t("utils.form.maxSize", { limit: sizeLimit }),
            (value) => (value ? value.length <= sizeLimit : true)
          )
          .typeError(t("utils.form.stringType")),
      });
    }
    value = selectedNode?.value?.stringVal ?? "";
    defaultValue = templateNode?.value?.stringVal ?? "";
  } else if (isIntNode(selectedNode) || isUintNode(selectedNode)) {
    field = (
      <>
        <Field
          {...TEXT_FIELD_DARK}
          component={FormikTextField}
          name="value"
          label={selectedNode?.name}
          InputProps={{
            endAdornment: units && (
              <InputAdornment position="end">{units}</InputAdornment>
            ),
          }}
          inputProps={{ inputMode: "numeric", pattern: "[0-9]+" }}
          helperText={hint}
          disabled={readOnly || !canUpdate}
        />
        {deprecationAlert}
      </>
    );
    if (isIntNode(selectedNode)) {
      value = selectedNode?.value?.int64Val ?? 0;
      defaultValue = String(templateNode?.value?.int64Val ?? 0);
    } else {
      value = selectedNode?.value?.uint64Val ?? 0;
      defaultValue = String(templateNode?.value?.uint64Val ?? 0);
    }
    schema = object({
      value: number()
        .integer(t("utils.form.integerType"))
        .required(t("utils.form.required"))
        .typeError(t("utils.form.numberType")),
    });
  } else if (isFloatNode(selectedNode)) {
    field = (
      <>
        <Field
          {...TEXT_FIELD_DARK}
          component={FormikTextField}
          InputProps={{
            endAdornment: units && (
              <InputAdornment position="end">{units}</InputAdornment>
            ),
          }}
          name="value"
          label={selectedNode?.name}
          inputProps={{ inputMode: "numeric", pattern: "[0-9]+\\.?[0-9]*" }}
          helperText={hint}
          disabled={readOnly || !canUpdate}
        />
        {deprecationAlert}
      </>
    );
    schema = object({
      value: number()
        .required(t("utils.form.required"))
        .typeError(t("utils.form.numberType")),
    });
    value = selectedNode?.value?.floatVal ?? 0;
    defaultValue = templateNode?.value?.floatVal ?? 0;
  } else if (isListNode(selectedNode)) {
    if (readOnly || !canUpdate) {
      field = <></>;
      schema = undefined;
    } else {
      field = (
        <>
          <Field
            {...TEXT_FIELD_DARK}
            component={FormikTextField}
            name="value"
            label={t("components.config.newKey", {
              key: selectedNode?.name,
            })}
            helperText={hint}
          />
          {deprecationAlert}
        </>
      );
      schema = object({
        value: string()
          .required(t("utils.form.required"))
          .matches(/^[\w.-]+$/, t("components.config.stringReqs"))
          .typeError(t("utils.form.stringType")),
      });
    }
  }

  const missingDefaults: Set<string> = new Set();
  if (removedPaths) {
    for (const path of removedPaths) {
      if (path.startsWith(selectedPath)) {
        const truncated = path.replace(`${selectedPath}/`, "");
        const segments = truncated.split("/");
        if (segments.length > 0) {
          const firstSegment = segments[0];
          if (!firstSegment) {
            continue;
          }
          missingDefaults.add(firstSegment);
        }
      }
    }
  }

  const showContent =
    isLeafNode(selectedNode) ||
    isListNode(selectedNode) ||
    isListNode(parentNode);

  return (
    <>
      <UnsyncedKeyWarning
        serial={serial}
        config={config}
        unsyncedKeys={unsyncedKeys}
        className="max-h-60"
      />
      <ConfigCacheRefresh serial={serial} />
      <div className="flex flex-col-reverse md:flex-row gap-8 h-full">
        <Tree
          className="w-full md:w-auto"
          config={config}
          initialShowChanged={Boolean(locationState?.showChanged)}
          onSelect={(path) => {
            setSelectedPath(path || "");
            pageRef?.current?.scrollTo(0, 0);
          }}
          selectedPath={selectedPath}
          addedPaths={addedPaths}
          removedPaths={removedPaths}
          changedPathsAncestors={changedPathsAncestors}
          changedPathsTree={changedPathsTree}
          isTemplate={isTemplate}
        />
        {showContent && serial && (
          <Paper
            key={selectedPath}
            className={classes("md:flex-grow", "p-8", "bg-gray-700")}
          >
            {" "}
            <Formik
              enableReinitialize
              initialValues={{ value }}
              validationSchema={schema}
              onSubmit={async (values, { resetForm }) => {
                const response = await updateItem(schema?.cast(values).value);
                if (response && !response.error) {
                  resetForm({ values });
                }
              }}
            >
              {({ submitForm, isSubmitting, dirty, setFieldValue }) => (
                <Form className="flex flex-col items-start gap-4">
                  {field}
                  <div className="flex items-center gap-4">
                    <LoadingButton
                      {...BLUE_LOADING_BUTTON}
                      disabled={readOnly || !canUpdate || !dirty || isDeleting}
                      loading={isSubmitting}
                      onClick={submitForm}
                      startIcon={
                        isListNode(selectedNode) ? <AddIcon /> : <SaveIcon />
                      }
                    >
                      {isListNode(selectedNode)
                        ? t("utils.actions.addLong", {
                            subject: titleCase(t("models.configs.key_one")),
                          })
                        : t("utils.actions.save")}
                    </LoadingButton>
                    <Button
                      variant="text"
                      color="info"
                      onClick={() => setAuditLogOpen(true)}
                    >
                      {t("views.fleet.robots.config.auditLog.open")}
                    </Button>
                    <ConfigAuditLog
                      serial={serial}
                      path={selectedPath}
                      open={isAuditLogOpen}
                      onClose={() => setAuditLogOpen(false)}
                    />
                  </div>
                  {missingDefaults.size > 0 && isListNode(selectedNode) && (
                    <Alert severity="warning" className="mt-4 mr-8">
                      <AlertTitle>
                        {t("components.config.warnings.valueChanged.title")}
                      </AlertTitle>
                      {t("components.config.warnings.keyMissing.description", {
                        count: missingDefaults.size,
                        keys: [...missingDefaults].join(", "),
                      })}
                    </Alert>
                  )}
                  {isListNode(parentNode) && (
                    <>
                      <LoadingButton
                        {...RED_LOADING_BUTTON}
                        disabled={readOnly || !canUpdate || isSubmitting}
                        loading={isDeleting}
                        onClick={() => deleteItem(selectedPath)}
                        startIcon={<DeleteIcon />}
                      >
                        {t("utils.actions.deleteLong", {
                          subject: t("models.configs.key_one"),
                        })}
                      </LoadingButton>
                      {addedPaths?.includes(selectedPath) && (
                        <Alert severity="warning" className="mt-4 mr-8">
                          <AlertTitle>
                            {t("components.config.warnings.valueChanged.title")}
                          </AlertTitle>
                          {t("components.config.warnings.keyExtra.description")}
                          <LoadingButton
                            {...RED_LOADING_BUTTON}
                            disabled={readOnly || !canUpdate || isSubmitting}
                            loading={isDeleting}
                            onClick={() => deleteItem(selectedPath)}
                            startIcon={<DeleteIcon />}
                          >
                            {t("utils.actions.deleteLong", {
                              subject: t("models.configs.key_one"),
                            })}
                          </LoadingButton>
                        </Alert>
                      )}
                    </>
                  )}
                  {changedPaths?.includes(selectedPath) && (
                    <Alert severity="warning" className="mt-4 mr-8">
                      <AlertTitle>
                        {t("components.config.warnings.valueChanged.title")}
                      </AlertTitle>
                      {t(
                        "components.config.warnings.valueChanged.description",
                        {
                          default: String(defaultValue) || "<BLANK>",
                        }
                      )}
                      <Button
                        onClick={() => {
                          setFieldValue("value", defaultValue);
                          submitForm();
                        }}
                        disabled={readOnly || !canUpdate}
                      >
                        {t("utils.actions.resetLong", {
                          subject: t("models.configs.value_one"),
                        })}
                      </Button>
                    </Alert>
                  )}
                </Form>
              )}
            </Formik>
          </Paper>
        )}
      </div>
    </>
  );
};

export const Config = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.configs,
      PermissionDomain.customer
    ),
    buildPermission(
      PermissionAction.read,
      PermissionResource.configs,
      PermissionDomain.all
    ),
  ],
  _Config
);
