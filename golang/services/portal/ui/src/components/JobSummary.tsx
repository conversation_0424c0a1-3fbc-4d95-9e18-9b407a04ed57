import { BLANK_VALUE } from "./measurement/formatters";
import { buildPermission } from "portal/utils/auth";
import {
  ButtonBase,
  Card,
  CardContent,
  Grid2TypeMap,
  Link,
  SvgIcon,
  Typography,
} from "@mui/material";
import { classes } from "portal/theme/theme";
import { CycleSlot } from "portal/utils/metrics";
import { DailyMetricResponse } from "protos/portal/metrics";
import { DateTime } from "luxon";
import { FeatureFlag, useFeatureFlag } from "portal/utils/hooks/useFeatureFlag";
import {
  getMetricName,
  METRIC_COVERAGE_SPEED,
  METRIC_OVERALL_EFFICIENCY,
  METRIC_VALID_CROPS,
  METRIC_WEED_DENSITY,
} from "portal/utils/certifiedMetrics";
import { isSameDate, isToday } from "portal/utils/dates";
import { Measurement } from "./measurement/Measurement";
import { OverrideProps } from "@mui/material/OverridableComponent";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { PortalJob } from "protos/portal/jobs";
import {
  useAuthorizationRequired,
  withAuthorizationRequired,
} from "./auth/WithAuthorizationRequired";
import { useTranslation } from "react-i18next";
import AcreageIcon from "@mui/icons-material/StraightenOutlined";
import BandingIcon from "@mui/icons-material/ViewColumnOutlined";
import CropImage from "portal/images/icons/crop.svg?react";
import Grid from "@mui/material/Unstable_Grid2/Grid2";
import React, { Fragment, FunctionComponent } from "react";
import ThinningIcon from "@mui/icons-material/FlakyOutlined";

const OLD_SUMMARY_METRICS = [
  METRIC_VALID_CROPS,
  METRIC_WEED_DENSITY,
  METRIC_COVERAGE_SPEED,
];

const SUMMARY_METRICS = [METRIC_OVERALL_EFFICIENCY, METRIC_COVERAGE_SPEED];

// We shoehorn days into jobs. This type adds an explicit flag to distinguish
// the two.
export type TaggedJob = TrueJob | DayJob;
type TrueJob = PortalJob & { isDay: false };
type DayJob = Partial<PortalJob> &
  Pick<PortalJob, "name" | "timestampMs"> & { isDay: true };

export const isJob = (job: TaggedJob): job is TrueJob => !job.isDay;
export const isDay = (job: TaggedJob): job is DayJob => job.isDay;

const hasThinning = (job: TaggedJob): boolean =>
  (job.metrics?.thinnedCrops ?? 0) > 0;

interface JobSummaryProps {
  className?: string;
  job: TaggedJob;
  metrics?: DailyMetricResponse;
  onClick?: () => void;
  onMetricsClick?: () => void;
  showFullMetrics?: boolean;
}

const _JobSummary: FunctionComponent<JobSummaryProps> = ({
  className,
  job,
  onClick,
  metrics,
  showFullMetrics = false,
  onMetricsClick,
}) => {
  const { t, i18n } = useTranslation();
  const canReadInternalMetrics = useAuthorizationRequired([
    buildPermission(
      PermissionAction.read,
      PermissionResource.metrics_internal,
      PermissionDomain.all
    ),
  ]);
  const hasMetricsRedesign =
    useFeatureFlag(FeatureFlag.METRICS_REDESIGN).isEnabled ?? false;

  const chipProps: OverrideProps<Grid2TypeMap<object, "div">, "div"> = {
    xs: 6,
    className: classes(
      "flex items-center gap-1",
      "whitespace-nowrap ellipsis overflow-hidden"
    ),
  };
  const bigChipProps = hasMetricsRedesign
    ? { ...chipProps, xs: 12 }
    : chipProps;
  const iconProps = hasMetricsRedesign ? ({ fontSize: "small" } as const) : {};

  const startTime = DateTime.fromMillis(job.timestampMs);
  const endTime = job.stopTimestampMs
    ? DateTime.fromMillis(job.stopTimestampMs)
    : undefined;
  let formattedTime;
  if (isDay(job)) {
    formattedTime = startTime.toLocaleString(
      { month: "numeric", day: "numeric" },
      {
        locale: i18n.language,
      }
    );
  } else if (!endTime && isToday(startTime)) {
    formattedTime = t("components.JobSummary.singleDay", {
      date: startTime.toLocaleString(
        { month: "numeric", day: "numeric" },
        {
          locale: i18n.language,
        }
      ),
      startTime: startTime.toLocaleString(
        { hour: "numeric", minute: "2-digit" },
        {
          locale: i18n.language,
        }
      ),
      endTime: "___",
    });
  } else if (!endTime) {
    formattedTime = t("components.JobSummary.multiDay", {
      startDate: startTime.toLocaleString(
        { month: "numeric", day: "numeric" },
        {
          locale: i18n.language,
        }
      ),
      endDate: "___",
    });
  } else if (isSameDate(startTime, endTime)) {
    formattedTime = t("components.JobSummary.singleDay", {
      date: startTime.toLocaleString(
        { month: "numeric", day: "numeric" },
        {
          locale: i18n.language,
        }
      ),
      startTime: startTime.toLocaleString(
        { hour: "numeric", minute: "2-digit" },
        {
          locale: i18n.language,
        }
      ),
      endTime: endTime.toLocaleString(
        { hour: "numeric", minute: "2-digit" },
        {
          locale: i18n.language,
        }
      ),
    });
  } else {
    formattedTime = t("components.JobSummary.multiDay", {
      startDate: startTime.toLocaleString(
        { month: "numeric", day: "numeric" },
        {
          locale: i18n.language,
        }
      ),
      endDate: endTime.toLocaleString(
        { month: "numeric", day: "numeric" },
        {
          locale: i18n.language,
        }
      ),
    });
  }

  return (
    <Card className={className}>
      <ButtonBase
        className="w-full"
        onClick={() => onClick?.()}
        focusVisibleClassName="border-solid border-2 border-white"
      >
        <CardContent className="flex flex-col w-full gap-2">
          <div className="flex items-baseline justify-between">
            <Typography
              variant="h2"
              className={classes(
                "text-xl",
                hasMetricsRedesign && "font-poppins font-bold normal-case"
              )}
            >
              {job.name}
            </Typography>
            <span className="font-normal opacity-60">{formattedTime}</span>
          </div>
          {isJob(job) && (
            <Grid
              container
              gridArea={12}
              rowSpacing={1}
              columnSpacing={hasMetricsRedesign ? 0 : 1}
              className={classes(
                "flex justify-between items-center text-sm w-full",
                hasMetricsRedesign && "font-poppins"
              )}
            >
              <Grid {...chipProps}>
                <SvgIcon {...iconProps}>
                  <CropImage />
                </SvgIcon>
                {job.crop || t("models.crops.categories.unknown")}
              </Grid>
              <Grid
                {...chipProps}
                className={classes(
                  chipProps.className,
                  hasMetricsRedesign && "flex justify-end"
                )}
              >
                <AcreageIcon {...iconProps} />
                <Measurement
                  value={job.metrics?.acresWeeded}
                  fromUnits="ac"
                  className="justify-start flex-shrink w-auto"
                  unitClassName="hidden"
                  cycleSlot={CycleSlot.AREA}
                />
                {" / "}
                <Measurement
                  value={job.acreage}
                  fromUnits="ac"
                  longUnits
                  className="justify-start w-auto"
                  cycleSlot={CycleSlot.AREA}
                />
              </Grid>
              {(hasMetricsRedesign ? showFullMetrics : true) && (
                <>
                  <Grid {...bigChipProps}>
                    <BandingIcon {...iconProps} />
                    {t("components.robots.RobotSummary.banding.withName", {
                      name:
                        job.bandingProfile || t("utils.descriptors.unknown"),
                    })}
                  </Grid>
                  <Grid {...bigChipProps}>
                    <ThinningIcon {...iconProps} />
                    {t("components.robots.RobotSummary.thinning.withName", {
                      name: hasThinning(job)
                        ? job.thinningProfile || t("utils.descriptors.unknown")
                        : BLANK_VALUE,
                    })}
                  </Grid>
                </>
              )}
            </Grid>
          )}

          {showFullMetrics && !hasMetricsRedesign && (
            <div className="text-left">
              {metrics &&
                OLD_SUMMARY_METRICS.map((metric) => {
                  return (
                    <Fragment key={metric.id}>
                      <div className="border-0 border-t-1 border-slate-600 border-solid w-full" />
                      <Typography variant="h3" className="text-lg">
                        {getMetricName(t, metric, { hasMetricsRedesign })}
                      </Typography>
                      <span className="whitespace-nowrap text-md">
                        <Measurement
                          value={metric.getValue(
                            metrics[metric.id as keyof DailyMetricResponse],
                            metrics,
                            canReadInternalMetrics
                          )}
                          metric={metric}
                          className="justify-start"
                          valueClassName="font-mono"
                          unitClassName="text-base"
                        />
                      </span>
                    </Fragment>
                  );
                })}
              {onMetricsClick && (
                <Link
                  onClick={() => onMetricsClick()}
                  className="text-lighten-600 font-bold block mt-2 decoration-lighten-600"
                >
                  {t("views.fleet.robots.history.moreMetrics")}
                </Link>
              )}
            </div>
          )}

          {hasMetricsRedesign && metrics && (
            <div className="mt-1 flex flex-col gap-2 text-left text-sm">
              {SUMMARY_METRICS.map((metric) => {
                const value = metric.getValue(
                  metrics[metric.id as keyof DailyMetricResponse],
                  metrics,
                  true
                );
                return (
                  <Grid
                    key={metric.id}
                    xs={12}
                    className="flex justify-between items-end"
                  >
                    <span className="flex-1 text-sm/4">
                      {getMetricName(t, metric, { hasMetricsRedesign })}
                    </span>
                    <Measurement
                      metric={metric}
                      value={value}
                      className="w-auto"
                    />
                  </Grid>
                );
              })}
            </div>
          )}
        </CardContent>
      </ButtonBase>
    </Card>
  );
};

export const JobSummary = withAuthorizationRequired(
  [
    buildPermission(
      PermissionAction.read,
      PermissionResource.jobs,
      PermissionDomain.all
    ),
  ],
  _JobSummary
);
