import { classes } from "portal/theme/theme";
import { Dialog, IconButton } from "@mui/material";
import { HotKeys, KeyMap } from "react-hotkeys";
import { Image, ImageProperties } from "./Image";
import { isNil } from "common/utils/identity";
import { titleCase } from "common/utils/strings";
import { useTranslation } from "react-i18next";
import ArrowBackIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIcon from "@mui/icons-material/ArrowForwardIos";
import CloseIcon from "@mui/icons-material/Close";
import React, {
  FC,
  PropsWithChildren,
  useCallback,
  useMemo,
  useState,
} from "react";

interface ExpandedImageProps
  extends Pick<ImageProperties, "src" | "isSrcLoading" | "isSrcError"> {
  open: boolean;
  onClose: () => void;
  onExited?: () => void;
  sidePaneContent?: React.ReactNode;
  overlay?: React.ReactNode;
  defaultDimensions?: { width: number; height: number }; // size of container when image hasn't loaded yet. If your images will be consistent sized, it's nice to have this set so that there's no jump
  onNext?: () => void;
  onBack?: () => void;
}
export const ExpandedImage: FC<ExpandedImageProps> = ({
  open,
  onClose,
  onExited,
  src,
  isSrcLoading,
  isSrcError,
  sidePaneContent,
  overlay,
  defaultDimensions,
  onNext,
  onBack,
}) => {
  const { t } = useTranslation();
  const hasSidePane = !isNil(sidePaneContent);
  const [imageLoaded, setImageLoaded] = useState(false);

  const onReset = useCallback((): void => {
    setImageLoaded(false);
  }, [setImageLoaded]);

  const onDialogClose = (): void => {
    onReset();
    onClose();
  };

  const keyMap: KeyMap = useMemo(
    () => ({
      ...(onNext
        ? {
            NEXT_IMAGE: {
              name: titleCase(t("utils.actions.next")),
              action: "keyup",
              sequence: "right",
            },
          }
        : {}),
      ...(onBack
        ? {
            BACK_IMAGE: {
              name: titleCase(t("utils.actions.back")),
              action: "keyup",
              sequence: "left",
            },
          }
        : {}),
    }),
    [onNext, t, onBack]
  );

  const keyMapHandlers = useMemo(
    () => ({
      NEXT_IMAGE: () => {
        onReset();
        onNext?.();
      },
      BACK_IMAGE: () => {
        onReset();
        onBack?.();
      },
    }),
    [onNext, onBack, onReset]
  );

  return (
    <HotKeys keyMap={keyMap} handlers={keyMapHandlers} allowChanges={true}>
      <Dialog
        open={open}
        onClose={onDialogClose}
        TransitionProps={{
          onExited,
        }}
        maxWidth={false}
        PaperProps={{
          className: classes({
            "flex flex-row overflow-hidden": hasSidePane,
            "items-center justify-center": !hasSidePane,
          }),
          classes: {
            root: "bg-transparent",
          },
        }}
      >
        {/* negative margin bottom is to eliminate unwanted bottom space that MUI Dialog adds */}
        <div
          className={classes("bg-black -mb-3 relative", {
            "flex-grow lg:basis-3/4": hasSidePane,
          })}
          style={{
            minWidth:
              src && imageLoaded
                ? undefined
                : defaultDimensions?.width ?? "30vw",
            minHeight:
              src && imageLoaded
                ? undefined
                : defaultDimensions?.height ?? "30vh",
          }}
        >
          <Image
            className={{
              imageWrapper: "flex justify-center items-center",
            }}
            image={{
              src,
              isSrcLoading,
              isSrcError,
              renderOverlay: imageLoaded ? () => overlay : undefined,
            }}
            onImageLoaded={() => {
              setImageLoaded(true);
            }}
          />

          {/* Navigation Buttons */}
          {onBack && (
            <IconButton
              onClick={onBack}
              className="absolute top-1/2 left-4 -translate-y-1/2 z-10 text-white bg-black/50 hover:bg-black/70"
            >
              <ArrowBackIcon />
            </IconButton>
          )}
          {onNext && (
            <IconButton
              onClick={onNext}
              className="absolute top-1/2 right-4 -translate-y-1/2 z-10 text-white bg-black/50 hover:bg-black/70"
            >
              <ArrowForwardIcon />
            </IconButton>
          )}
          <CloseButton
            className={classes("absolute top-4 right-4", {
              hidden: hasSidePane,
            })}
            onClose={onDialogClose}
          />
        </div>
        {hasSidePane && (
          <SidePaneWrapper
            className="flex-grow lg:basis-1/4 lg:max-h-full overflow-y-auto min-w-[20vw]"
            onClose={onDialogClose}
          >
            {sidePaneContent}
          </SidePaneWrapper>
        )}
      </Dialog>
    </HotKeys>
  );
};

interface SidePaneWrapper extends PropsWithChildren {
  className: string;
  onClose: () => void;
}
const SidePaneWrapper: FC<SidePaneWrapper> = ({
  children,
  onClose,
  className,
}) => (
  <div className={classes("bg-gray-800 flex flex-col", className)}>
    <CloseButton className="block self-end" onClose={onClose} />
    <div className="px-2 lg:px-4">{children}</div>
  </div>
);

interface CloseButtonProps {
  className: string;
  onClose: () => void;
}
const CloseButton: FC<CloseButtonProps> = ({ className, onClose }) => (
  <IconButton className={classes("text-white", className)} onClick={onClose}>
    <CloseIcon />
  </IconButton>
);
