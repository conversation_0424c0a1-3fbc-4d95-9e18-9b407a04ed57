import { ButtonBase, Tooltip } from "@mui/material";
import { classes } from "portal/theme/theme";
import { IconImage } from "./IconImage";
import { isNil } from "common/utils/identity";
import { useDistinctClickTypes } from "portal/utils/hooks/useDistinctClickTypes";
import BrokenImageIcon from "@mui/icons-material/BrokenImage";
import ImageNotSupportedIcon from "@mui/icons-material/ImageNotSupported";
import React, { FC, FunctionComponent, useCallback, useState } from "react";

/**
 * General purpose component for rendering an image.
 * Supports:
 * - making the button clickable, double clickable (and long press-able for mobile)
 * - gracefully handling loading and error states visually
 * - callback for when the image is done loading
 * - supporting tooltips
 * - supporting overlays
 */
export interface ImageProperties {
  id?: string;
  alt?: string;
  src?: string;
  isSrcLoading?: boolean;
  isSrcError?: boolean;
  tooltip?: string;
  onClick?: () => void;
  onDoubleClick?: () => void;
  renderOverlay?: () => React.ReactNode;
  className?: string;
}
interface ImageProps {
  image?: ImageProperties;
  onImageLoaded?: (id?: string) => void;
  className?: {
    imageWrapper?: string;
    image?: string;
  };
}

enum LoadingState {
  NOT_LOADED,
  SUCCESS,
  ERROR,
}
export const Image: FunctionComponent<ImageProps> = ({
  image,
  onImageLoaded,
  className,
}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>(
    LoadingState.NOT_LOADED
  );

  // reset loading state when source on an image changes
  const [prevSrc, setPrevSrc] = useState<string | undefined>();
  if (image?.src !== prevSrc) {
    setLoadingState(LoadingState.NOT_LOADED);
    setPrevSrc(image?.src);
  }

  const onLoad = useCallback(
    (success: boolean, id?: string) => {
      setLoadingState(success ? LoadingState.SUCCESS : LoadingState.ERROR);
      onImageLoaded?.(id);
    },
    [onImageLoaded]
  );

  const clickHandlers = useDistinctClickTypes(
    image?.onClick,
    image?.onDoubleClick,
    image?.onDoubleClick
  );

  if (!image) {
    return (
      <IconImage
        icon={(className) => <ImageNotSupportedIcon className={className} />}
      />
    );
  }

  if (loadingState === LoadingState.ERROR || image.isSrcError) {
    if (image.isSrcError) {
      console.error("image srcError", image);
    }
    return (
      <IconImage
        icon={(className) => <BrokenImageIcon className={className} />}
      />
    );
  }
  const { tooltip, renderOverlay, onClick, onDoubleClick, src, alt, id } =
    image;
  const imageProps = {
    className: classes(className?.image, image.className),
    src,
    alt,
    onLoad: () => {
      onLoad(true, id);
    },
    onError: () => {
      console.error("<img> onError", image);
      onLoad(false, id);
    },
  };
  return (
    <div className={classes("relative w-full h-full", className?.imageWrapper)}>
      <Tooltip
        title={
          tooltip ? (
            <div className="whitespace-pre-line">{tooltip}</div>
          ) : undefined
        }
        placement="bottom"
      >
        <div className="aspect-square max-h-full max-w-full relative">
          {!isNil(onClick) || !isNil(onDoubleClick) ? (
            <ButtonBase {...clickHandlers} className="w-full h-full p-0">
              <ImageElement {...imageProps} />
            </ButtonBase>
          ) : (
            <ImageElement {...imageProps} />
          )}
          {renderOverlay?.()}
        </div>
      </Tooltip>

      {loadingState === LoadingState.NOT_LOADED || image.isSrcLoading ? (
        <div className="absolute inset-0 top-0 right-0 object-cover bg-gray-600 animate-pulse"></div>
      ) : undefined}
    </div>
  );
};

interface ImageElementProps {
  className?: string;
  src?: string;
  alt?: string;
  onLoad?: () => void;
  onError?: () => void;
}
const ImageElement: FC<ImageElementProps> = ({
  className,
  src,
  alt,
  onLoad,
  onError,
}) => {
  if (!src) {
    return;
  }
  return (
    <img
      className={classes("object-contain w-full h-full", className)}
      src={src}
      alt={alt}
      loading="lazy"
      onLoad={onLoad}
      onError={onError}
    />
  );
};
