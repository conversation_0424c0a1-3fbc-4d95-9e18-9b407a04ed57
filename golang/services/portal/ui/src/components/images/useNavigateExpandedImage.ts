import { isNil } from "common/utils/identity";
import { useCallback } from "react";

export const useNavigateExpandedImage = <T>(
  data: (T | undefined)[],
  setExpandedImage: (expandedImage: { index: number; data: T }) => void,
  expandedImage?: { index: number; data: T }
): ((indexDiff: number) => (() => void) | undefined) => {
  return useCallback(
    (indexDiff: number) => {
      if (!expandedImage || isNil(expandedImage.index)) {
        return;
      }

      const nextIndex = expandedImage.index + indexDiff;
      const nextData = data[nextIndex];
      if (!nextData) {
        return;
      }

      return () => {
        setExpandedImage({
          index: nextIndex,
          data: nextData,
        });
      };
    },
    [data, expandedImage, setExpandedImage]
  );
};
