import { AutocompleteFilter } from "./AutocompleteFilter";
import {
  booleanToReadableString,
  dateRangeToReadableString,
  entitiesToReadableString,
  numericalRangeToReadableString,
} from "portal/components/filters/filter";
import { buildPermission } from "portal/utils/auth";
import { Button, Typography } from "@mui/material";
import { CarbonDateTimeRangePicker } from "portal/components/CarbonDateTimeRangePicker";
import {
  classes,
  SMALL_OUTLINED_BUTTON_DARK,
  SMALL_TEXT_FIELD_DARK,
  SMALL_WHITE_BUTTON,
} from "portal/theme/theme";
import { DateRange } from "@mui/lab";
import { DateTime } from "luxon";
import {
  Entity,
  NumericalRangeValue,
} from "portal/components/filters/filter.types";
import {
  FilterChip,
  FilterChipProps,
} from "portal/components/filters/FilterChip";
import { getDateRangeShortcuts } from "portal/utils/reports";
import { getDiameterFilterUnits } from "portal/utils/categoryCollectionProfile";
import { isEqual } from "portal/utils/equality";
import { isNil } from "portal/utils/identity";
import { keys, pick } from "common/utils/objects";
import { NumericalRange } from "../../filters/NumericalRange";
import { OptionalBooleanSelect } from "portal/components/filters/OptionalBoolean";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { skipToken } from "@reduxjs/toolkit/query";
import { TFunction } from "i18next";
import { titleCase } from "portal/utils/strings";
import { UploadedByOperatorIcon } from "../imageOverlays/IconOverlay";
import { useAuthorizationRequired } from "../../auth/WithAuthorizationRequired";
import { useCrops } from "portal/utils/hooks/useCrops";
import { useGetRobotFilterOptions } from "../useGetRobotFilterOptions";
import { useGetRobotQuery } from "portal/state/portalApi";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import React, { FC, FunctionComponent, useMemo, useState } from "react";

interface PredictionPointSpecificFilters {
  crops: Entity[];
  robots: Entity[];
  capturedAt: DateRange<DateTime> | undefined;
  diameterUserUnits: NumericalRangeValue | undefined;
  uploadedByOperator: boolean | undefined;
}

const predictionPointSpecificFilterKeys: readonly (keyof PredictionPointSpecificFilters)[] =
  Object.freeze(
    keys({
      crops: true,
      robots: true,
      capturedAt: true,
      diameterUserUnits: true,
      uploadedByOperator: true,
    } satisfies { [K in keyof PredictionPointSpecificFilters]: true })
  );
interface SessionFilters {
  sessionId: string | undefined;
  categoryIds: Entity[];
}
export type PredictionPointFilter = PredictionPointSpecificFilters &
  SessionFilters;

export const INITIAL_PREDICTION_POINT_FILTERS: PredictionPointFilter = {
  crops: [],
  robots: [],
  capturedAt: undefined,
  diameterUserUnits: undefined,
  sessionId: undefined,
  categoryIds: [],
  uploadedByOperator: undefined,
};

const getDisplayNames = (
  t: TFunction
): Record<keyof PredictionPointSpecificFilters, string> => ({
  crops: t("models.crops.crop_other"),
  robots: t("models.robots.robot_other"),
  capturedAt: t("components.categoryCollectionProfile.filters.capturedAt"),
  diameterUserUnits: t("components.categoryCollectionProfile.filters.diameter"),
  uploadedByOperator: t(
    "components.categoryCollectionProfile.filters.uploadedByOperator"
  ),
});

interface PredictionPointFiltersProps {
  serial?: string;
  filters: PredictionPointFilter;
  onChange: (filters: PredictionPointFilter) => void;
}

export const PredictionPointFilters: FunctionComponent<
  PredictionPointFiltersProps
> = ({ serial, filters, onChange }) => {
  const { t } = useTranslation();
  const { measurementSystem } = useSelf();
  const diameterFilterUnits = getDiameterFilterUnits(measurementSystem);

  const [localFilters, setLocalFilters] =
    useState<PredictionPointFilter>(filters);

  const canSeeAllCrops = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.crops_basic,
      PermissionDomain.all
    ),
  ]);

  // crops
  const {
    sortedCrops: crops,
    isFetching: isCropsLoading,
    isError: isCropsError,
  } = useCrops({ serial });
  const cropOptions = useMemo(() => {
    return (canSeeAllCrops ? crops?.all : crops?.enabled)?.toSorted((a, b) =>
      a.commonName.localeCompare(b.commonName)
    );
  }, [canSeeAllCrops, crops?.all, crops?.enabled]);

  // robot
  const {
    data: robotSummary,
    isLoading: isRobotLoading,
    error: robotError,
  } = useQueryPopups(useGetRobotQuery(isNil(serial) ? skipToken : { serial }), {
    errorVariant: "warning",
  });
  const robotCustomerId = robotSummary?.customer?.db?.id;

  // robots
  const {
    robots,
    isLoading: isRobotsLoading,
    error: robotsError,
  } = useGetRobotFilterOptions(robotCustomerId);

  const hasActiveFilters = useMemo(() => {
    const local = pick(localFilters, predictionPointSpecificFilterKeys);
    const initial = pick(
      INITIAL_PREDICTION_POINT_FILTERS,
      predictionPointSpecificFilterKeys
    );
    return !isEqual(local, initial);
  }, [localFilters]);

  const touched = useMemo(() => {
    const local = pick(localFilters, predictionPointSpecificFilterKeys);
    const committed = pick(filters, predictionPointSpecificFilterKeys);
    return !isEqual(local, committed);
  }, [localFilters, filters]);

  const displayNames = getDisplayNames(t);
  const classNames = "md:max-w-1/3";
  const labelClassName = "p-0 m-0 text-xs";
  return (
    <div className="flex flex-col gap-2">
      <div className="flex gap-6 flex-wrap items-end">
        <div className={classNames}>
          <AutocompleteFilter
            loading={isCropsLoading}
            loadingError={isCropsError}
            label={titleCase(displayNames.crops)}
            options={(cropOptions || []).map(({ id, commonName }) => ({
              id,
              name: commonName,
            }))}
            value={localFilters.crops}
            onChange={(value) =>
              setLocalFilters({ ...localFilters, crops: value })
            }
          />
        </div>
        <div className={classNames}>
          <AutocompleteFilter
            loading={isRobotsLoading || isRobotLoading}
            loadingError={Boolean(robotsError || robotError)}
            label={titleCase(t(displayNames.robots))}
            options={robots || []}
            value={localFilters.robots}
            onChange={(value) =>
              setLocalFilters({ ...localFilters, robots: value })
            }
          />
        </div>
        <div className={classNames}>
          <p className={labelClassName}>{displayNames.diameterUserUnits}</p>
          <NumericalRange
            className="max-w-60"
            value={localFilters.diameterUserUnits ?? [undefined, undefined]}
            min={0}
            onChange={(diameterUserUnits) => {
              setLocalFilters({ ...localFilters, diameterUserUnits });
            }}
            units={diameterFilterUnits}
          />
        </div>
        <div>
          <p className={classes(labelClassName, "pb-2")}>
            {displayNames.capturedAt}
          </p>
          <CarbonDateTimeRangePicker
            className="gap-0"
            calendars={1}
            openEnded
            disableFuture
            // eslint-disable-next-line unicorn/no-null -- null here specifies that the component is controlled vs undefined which means uncontrolled
            value={localFilters.capturedAt ?? [null, null]}
            slotProps={{
              shortcuts: { items: getDateRangeShortcuts(t) },
              textField: {
                ...SMALL_TEXT_FIELD_DARK,
                className: "m-0",
              },
              actionBar: {
                actions: ["clear"],
              },
            }}
            onChange={(newRange) => {
              setLocalFilters({ ...localFilters, capturedAt: newRange });
            }}
          />
        </div>
        <div className="max-w-40">
          <p className={classes(labelClassName, "pb-2")}>
            {titleCase(displayNames.uploadedByOperator)}
          </p>
          <OptionalBooleanSelect
            value={localFilters.uploadedByOperator}
            onChange={(value) => {
              setLocalFilters({ ...localFilters, uploadedByOperator: value });
            }}
            trueText={t(
              "components.categoryCollectionProfile.filters.uploaded"
            )}
            falseText={t(
              "components.categoryCollectionProfile.filters.notUploaded"
            )}
            trueIcon={<UploadedByOperatorIcon />}
          />
        </div>
      </div>
      <div className="flex w-full justify-end items-center gap-4">
        {touched && (
          <Typography className="text-xs text-gray-300">
            {t("components.categoryCollectionProfile.filters.unappliedFilters")}
          </Typography>
        )}
        <Button
          {...SMALL_OUTLINED_BUTTON_DARK}
          size="small"
          onClick={() => {
            setLocalFilters(INITIAL_PREDICTION_POINT_FILTERS);
          }}
          disabled={!hasActiveFilters}
        >
          {t("utils.actions.clear")}
        </Button>
        <Button
          {...SMALL_WHITE_BUTTON}
          onClick={() => {
            onChange(localFilters);
          }}
          disabled={!touched}
        >
          {t("utils.actions.apply")}
        </Button>
      </div>
    </div>
  );
};

interface PredictionPointFilterChipSummaryProps {
  filters: PredictionPointSpecificFilters;
}
export const PredictionPointFilterChipSummary: FC<
  PredictionPointFilterChipSummaryProps
> = ({ filters }) => {
  const { t, i18n } = useTranslation();
  const displayNames = getDisplayNames(t);

  const chipMap: Record<keyof PredictionPointSpecificFilters, FilterChipProps> =
    useMemo(
      () => ({
        crops: {
          name: displayNames.crops,
          value: entitiesToReadableString(filters.crops),
        },
        robots: {
          name: displayNames.robots,
          value: entitiesToReadableString(filters.robots),
        },
        capturedAt: {
          name: displayNames.capturedAt,
          value: dateRangeToReadableString(t, i18n, filters.capturedAt),
        },
        diameterUserUnits: {
          name: displayNames.diameterUserUnits,
          value: numericalRangeToReadableString(t, filters.diameterUserUnits),
        },
        uploadedByOperator: {
          name: displayNames.uploadedByOperator,
          value: booleanToReadableString(t, filters.uploadedByOperator),
        },
      }),
      [
        displayNames.capturedAt,
        displayNames.crops,
        displayNames.diameterUserUnits,
        displayNames.robots,
        displayNames.uploadedByOperator,
        filters.capturedAt,
        filters.crops,
        filters.diameterUserUnits,
        filters.robots,
        filters.uploadedByOperator,
        i18n,
        t,
      ]
    );

  const chips: FilterChipProps[] = useMemo(() => {
    return Object.values(chipMap).filter(({ value }) => value !== "");
  }, [chipMap]);

  return chips.map(({ name, value }) => (
    <FilterChip key={name} name={name} value={value} />
  ));
};
