import { useTranslation } from "react-i18next";
// import LabelIcon from "@mui/icons-material/Adjust";
import { FormControlLabel, Switch } from "@mui/material";
import { updateCategoryCollectionShowLabels } from "portal/state/userPreferences";
import { useDispatch } from "react-redux";
import { useUserPreferences } from "portal/state/store";
import React, { FC } from "react";

export const ShowLabelsToggle: FC = () => {
  const { t } = useTranslation();
  const userPreferences = useUserPreferences();
  const dispatch = useDispatch();
  return (
    <FormControlLabel
      control={
        <Switch
          onClick={() => {
            dispatch(
              updateCategoryCollectionShowLabels(
                !userPreferences.categoryCollection.showLabels
              )
            );
          }}
          checked={userPreferences.categoryCollection.showLabels}
        />
      }
      label={t("components.ShowLabelsButton.text")}
    />
  );
};
