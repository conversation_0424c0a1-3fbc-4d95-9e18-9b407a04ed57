import { formatRadiusToDiameter } from "portal/utils/categoryCollectionProfile";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import React, { FC } from "react";

const RADIUS_STYLING = {
  fontSizeSmall: 12,
  fontSizeLarge: 14,
};
interface PointLabelOverlayProps {
  width: number;
  height: number;
  x: number;
  y: number;
  radius: number;
  radiusCm?: number;
}
export const PointLabelOverlay: FC<PointLabelOverlayProps> = ({
  width,
  height,
  x,
  y,
  radius,
  radiusCm,
}) => {
  const { t, i18n } = useTranslation();
  const { measurementSystem } = useSelf();

  const formattedDiameter = radiusCm
    ? formatRadiusToDiameter(t, i18n, measurementSystem, radiusCm)
    : undefined;
  const unitsPadding = 0.05 * width;
  return (
    <svg
      viewBox={`0 0 ${width} ${height}`}
      preserveAspectRatio="none"
      className="w-full h-full absolute top-0 left-0 pointer-events-none"
    >
      {/* Dashed circle for radius */}
      <circle
        cx={x}
        cy={y}
        r={radius}
        stroke="white"
        strokeWidth={1.5}
        strokeDasharray="5,5"
        fill="none"
      />
      {/* Center dot */}
      <circle cx={x} cy={y} r="2" fill="white" stroke="white" strokeWidth={1} />
      {/* Radius label in bottom-right */}
      {formattedDiameter && (
        <text
          x={width - unitsPadding}
          y={height - unitsPadding}
          fill="white"
          fontSize={
            width > 200
              ? RADIUS_STYLING.fontSizeLarge
              : RADIUS_STYLING.fontSizeSmall
          }
          textAnchor="end"
          dominantBaseline="baseline"
          style={{
            textShadow: Array.from({ length: 4 })
              .fill("0 0 2px black")
              .join(", "),
            fontFamily: "sans-serif",
          }}
        >
          {formattedDiameter.toString()}
        </text>
      )}
    </svg>
  );
};

interface PaddingDerivedPointLabelOverlayProps {
  dimensions: { width: number; height: number };
  radiusCm?: number;
  padding: number;
}
export const PaddingDerivedPointLabelOverlay: FC<
  PaddingDerivedPointLabelOverlayProps
> = ({ dimensions: { width, height }, radiusCm, padding }) => {
  const x = width / 2;
  const y = height / 2;
  const r = (width - padding) / 2;

  return (
    <PointLabelOverlay
      width={width}
      height={height}
      x={x}
      y={y}
      radius={r}
      radiusCm={radiusCm}
    />
  );
};
