import { classes } from "portal/theme/theme";
import { IconButton } from "@mui/material";
import { ReactComponent } from "react-hotkeys";
import React, { CSSProperties, FC } from "react";
import RemoveIcon from "@mui/icons-material/Close";
import StarIconMui from "@mui/icons-material/Star";

interface IconOverlayProps {
  icon?: ReactComponent;
  onClick?: () => void;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
}

export const IconOverlay: FC<IconOverlayProps> = ({
  icon,
  onClick,
  position = "top-left",
}) => {
  const baseClassName = "absolute min-w-6 p-1";
  const style = {
    top: ["top-right", "top-left"].includes(position) ? 0 : undefined,
    bottom: ["bottom-right", "bottom-left"].includes(position) ? 0 : undefined,
    right: ["top-right", "bottom-right"].includes(position) ? 0 : undefined,
    left: ["top-left", "bottom-left"].includes(position) ? 0 : undefined,
  };
  return onClick ? (
    <IconButton
      className={classes(
        baseClassName,
        "hover:bg-slate-200 hover:bg-opacity-30"
      )}
      onClick={onClick}
      style={style}
    >
      {icon}
    </IconButton>
  ) : (
    <div
      className={classes(baseClassName, "pointer-events-none")}
      style={style}
    >
      {icon}
    </div>
  );
};

interface RemoveImageOverlayProps {
  onClick: () => void;
}

export const RemoveImageOverlay: FC<RemoveImageOverlayProps> = ({
  onClick,
}) => {
  return (
    <IconOverlay
      onClick={onClick}
      position="top-right"
      icon={<RemoveIcon className="text-white" />}
    />
  );
};

export const UploadedByOperatorImageOverlay: FC = () => {
  return <IconOverlay position="top-left" icon={<UploadedByOperatorIcon />} />;
};

export const UploadedByOperatorIcon: FC<{
  style?: CSSProperties;
  className?: string;
}> = ({ style, className }) => (
  <StarIconMui
    style={style}
    className={classes("text-tailwind-yellow-300", className)}
  />
);
