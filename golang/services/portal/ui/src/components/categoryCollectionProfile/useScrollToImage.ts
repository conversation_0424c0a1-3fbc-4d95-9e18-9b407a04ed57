import { useCallback, useEffect } from "react";
import { usePageRef } from "../Page";

interface ScrollToImage {
  id?: string;
  visible?: boolean;
}

interface UseScrollToImageProps {
  scrollToImage?: ScrollToImage;
  setScrollToImage: (img?: ScrollToImage) => void;
}

const querySelector = (id: string): string => `#${CSS.escape(id)}`;

export const useScrollToImage = ({
  scrollToImage,
  setScrollToImage,
}: UseScrollToImageProps): {
  onScrollToImage: () => void;
} => {
  const pageRef = usePageRef();

  const onScrollToImage = useCallback(() => {
    if (!scrollToImage?.id) {
      return;
    }

    const el = document.querySelector<HTMLElement>(
      querySelector(scrollToImage.id)
    );

    if (el && pageRef?.current) {
      const container = pageRef.current;
      const containerRect = container.getBoundingClientRect();
      const elRect = el.getBoundingClientRect();
      const offset = elRect.top - containerRect.top + container.scrollTop;

      container.scrollTo({
        top: offset - container.clientHeight / 2 + el.clientHeight / 2,
        behavior: "smooth",
      });
    }
  }, [scrollToImage, pageRef]);

  useEffect(() => {
    if (!scrollToImage?.id) {
      return;
    }

    const el = document.querySelector<HTMLElement>(
      querySelector(scrollToImage.id)
    );
    if (!el) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry?.isIntersecting) {
          setScrollToImage({ id: scrollToImage.id, visible: true });
          observer.disconnect();
        }
      },
      {
        root: pageRef?.current,
        threshold: 1, // fully visible
      }
    );

    observer.observe(el);

    return () => observer.disconnect();
  }, [scrollToImage?.id, scrollToImage?.visible, pageRef, setScrollToImage]);

  return { onScrollToImage };
};
