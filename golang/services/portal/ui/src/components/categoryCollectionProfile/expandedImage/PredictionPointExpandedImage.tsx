import { assembleImageRequestParameters } from "portal/components/images/ThumbnailImage";
import {
  DEFAULT_AVERAGE_PPCM,
  DEFAULT_CHIP_UPLOAD_SIZE,
  getCropping,
} from "portal/utils/categoryCollectionProfile";
import { ExpandedImage } from "portal/components/images/ExpandedImage";
import { PointLabelOverlay } from "../imageOverlays/PointLabelOverlay";
import { PredictionPoint } from "protos/veselka/prediction_point";
import { PredictionPointSidePane } from "./PredictionPointSidePane";
import { RadioChipOption } from "portal/components/RadioChips";
import { skipToken } from "@reduxjs/toolkit/query";
import { useGetCropThumbnailQuery } from "portal/state/imageServiceApi";
import { useUserPreferences } from "portal/state/store";
import React, { FC, useMemo } from "react";

interface PredictionPointExpandedImageProps {
  open: boolean;
  onClose: () => void;
  onExited?: () => void;
  onNext?: () => void;
  onBack?: () => void;
  predictionPoint?: PredictionPoint;
  categoryOptions: RadioChipOption[];
  categoryId?: string;
  onClassify: (predictionPoint: PredictionPoint, categoryId: string) => void;
}
export const PredictionPointExpandedImage: FC<
  PredictionPointExpandedImageProps
> = ({
  open,
  onClose,
  onExited,
  onNext,
  onBack,
  predictionPoint,
  categoryOptions,
  categoryId,
  onClassify,
}) => {
  const userPreferences = useUserPreferences();
  const cropInfo = useMemo(() => {
    if (!predictionPoint?.image?.url) {
      return;
    }
    const croppingInfo = getCropping(
      predictionPoint.x,
      predictionPoint.y,
      DEFAULT_CHIP_UPLOAD_SIZE
    );
    return {
      ...predictionPoint.image,
      ...croppingInfo,
    };
  }, [predictionPoint]);

  const {
    currentData: fetchedImageSource,
    isLoading,
    error,
  } = useGetCropThumbnailQuery(
    open && cropInfo ? assembleImageRequestParameters(cropInfo) : skipToken,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  return (
    <ExpandedImage
      open={open}
      onClose={onClose}
      onExited={onExited}
      onNext={onNext}
      onBack={onBack}
      src={fetchedImageSource}
      isSrcLoading={isLoading || (!error && !fetchedImageSource)}
      isSrcError={Boolean(error)}
      sidePaneContent={
        predictionPoint && (
          <PredictionPointSidePane
            data={predictionPoint}
            categoryOptions={categoryOptions}
            categoryId={categoryId}
            onClassify={onClassify}
          />
        )
      }
      defaultDimensions={{
        width: DEFAULT_CHIP_UPLOAD_SIZE,
        height: DEFAULT_CHIP_UPLOAD_SIZE,
      }}
      overlay={
        userPreferences.categoryCollection.showLabels &&
        predictionPoint &&
        cropInfo && (
          <PointLabelOverlay
            width={cropInfo.width}
            height={cropInfo.height}
            x={cropInfo.width / 2}
            y={cropInfo.height / 2}
            radius={predictionPoint.radius}
            radiusCm={
              predictionPoint.radius /
              (cropInfo.ppcm > 0 ? cropInfo.ppcm : DEFAULT_AVERAGE_PPCM)
            }
          />
        )
      }
    />
  );
};
