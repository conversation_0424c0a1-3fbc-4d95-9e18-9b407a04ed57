import { Chip, SvgIcon, Typography } from "@mui/material";
import { CopyToClipboardButton } from "portal/components/CopyToClipboardButton";
import { PredictionPoint } from "protos/veselka/prediction_point";
import { RadioChipOption, RadioChips } from "portal/components/RadioChips";
import { ShowLabelsToggle } from "../ShowLabelsToggle";
import { userFacingDate } from "portal/utils/categoryCollectionProfile";
import { useSelf } from "portal/state/store";
import { useTranslation } from "react-i18next";
import ModelIcon from "portal/images/icons/model.svg?react";
import React, { FC, useMemo } from "react";

interface PredictionPointSidePaneProps {
  data: PredictionPoint;
  categoryOptions: RadioChipOption[];
  categoryId?: string;
  onClassify: (predictionPoint: PredictionPoint, categoryId: string) => void;
}
export const PredictionPointSidePane: FC<PredictionPointSidePaneProps> = ({
  data,
  categoryOptions,
  categoryId,
  onClassify,
}) => {
  const { t, i18n } = useTranslation();
  const { isInternal } = useSelf();

  const { id, x, y, radius, image, categoryId: predictedCategoryId } = data;
  const capturedAt = image?.capturedAt
    ? userFacingDate(i18n, image.capturedAt)
    : undefined;
  const externalInfo: MetadataProps[] = [
    {
      label: t("models.categoryCollectionProfiles.metadata.capturedAt"),
      value: capturedAt,
    },
  ];
  const internalInfo: MetadataProps[] = [
    {
      label: t("models.categoryCollectionProfiles.metadata.pointId"),
      value: id,
      type: "id",
    },
    {
      label: t("models.categoryCollectionProfiles.metadata.imageId"),
      value: image?.id,
      type: "id",
    },
    { label: t("models.categoryCollectionProfiles.metadata.x"), value: x },
    { label: t("models.categoryCollectionProfiles.metadata.y"), value: y },
    {
      label: t("models.categoryCollectionProfiles.metadata.radius"),
      value: radius,
    },
    {
      label: t("models.categoryCollectionProfiles.metadata.ppcm"),
      value: image?.ppcm,
    },
  ];

  const predictedCategory = useMemo(
    () => categoryOptions.find((c) => c.id === predictedCategoryId),
    [categoryOptions, predictedCategoryId]
  );
  if (predictedCategoryId) {
    internalInfo.push({
      label: t("models.categoryCollectionProfiles.metadata.categoryId"),
      value: predictedCategoryId,
      type: "id",
    });
  }

  const clipBoardData = useMemo(
    () => JSON.stringify(PredictionPoint.toJSON(data)),
    [data]
  );

  return (
    <div className="text-xs md:text-sm p-2 lg:p-4 flex flex-col gap-4 lg:gap-6 overflow-y-auto">
      <div className="flex gap-1 flex-col">
        <ShowLabelsToggle />
        <Typography variant="caption">
          {t("models.categoryCollectionProfiles.fields.categories.name")}
        </Typography>
        <RadioChips
          isLoading={false}
          options={categoryOptions}
          activeId={categoryId}
          setActiveId={(categoryId) => {
            onClassify(data, categoryId);
          }}
          size="small"
        />
      </div>
      {predictedCategory && (
        <div className="flex flex-col">
          <Typography variant="caption">
            {t("models.categoryCollectionProfiles.metadata.prediction")}
          </Typography>
          <Chip
            className="font-normal p-1 w-fit"
            style={{
              borderColor: predictedCategory.color?.bg,
              color: predictedCategory.color?.text,
              backgroundColor: predictedCategory.color?.bg,
            }}
            variant="outlined"
            label={predictedCategory.name}
            size="small"
            icon={
              <SvgIcon
                style={{ color: predictedCategory.color?.text }}
                className="text-sm"
              >
                <ModelIcon />
              </SvgIcon>
            }
          />
        </div>
      )}
      {externalInfo.map((info) => (
        <Metadata key={info.label} {...info} />
      ))}
      {isInternal && (
        <div>
          <div className="flex gap-2 justify-between">
            <Typography className="text-lg font-bold">
              {t("models.categoryCollectionProfiles.metadata.internal")}
            </Typography>
            <CopyToClipboardButton text={clipBoardData} />
          </div>
          <div className="bg-slate-900 p-1">
            {internalInfo.map((info) => (
              <Metadata key={info.label} {...info} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface MetadataProps {
  label: string;
  value?: string | number;
  type?: "text" | "id";
}
const Metadata: FC<MetadataProps> = ({ label, value, type = "text" }) => (
  <p className="m-0 p-0">
    <span className="pr-2">{label}</span>
    <span
      className={type === "id" ? "font-mono text-carbon-yellow" : undefined}
    >
      {value}
    </span>
  </p>
);
