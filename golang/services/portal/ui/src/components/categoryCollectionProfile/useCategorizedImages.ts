import { PaginationParameters } from "portal/utils/categoryCollectionProfile";
import { PredictionPoint } from "protos/veselka/prediction_point";
import { useCallback, useRef } from "react";

interface MetaState {
  saved: boolean;
  loading: boolean;
}
interface OrderedCategorizedImage {
  data?: PredictionPoint;
  state: MetaState;
}

/**
 * Helper hook to the CategorizedImages component that processes
 * thumbnail selections (accounting for their loading status)
 * to construct renderable ordered thumbnail data.
 */
export const useCategorizedImages = (): {
  processInfiniteScrollData: (
    isLoading: boolean,
    currentPagination: PaginationParameters,
    newResponse: PredictionPoint[],
    allIds: string[]
  ) => OrderedCategorizedImage[];
} => {
  const allFetchedData = useRef<Map<string, PredictionPoint>>(new Map()); // maps id to PredictionPoint

  const processInfiniteScrollData = useCallback(
    (
      isLoading: boolean,
      { page, pageSize }: PaginationParameters,
      newResponse: PredictionPoint[],
      allIds: string[]
    ): OrderedCategorizedImage[] => {
      const loadedPage = Math.max(isLoading ? page - 1 : page, 0);
      const loadedIds = allIds.slice(0, loadedPage * pageSize);

      for (const point of newResponse) {
        if (point.id) {
          allFetchedData.current.set(point.id, point);
        }
      }

      // saved and loaded
      const loadedData: OrderedCategorizedImage[] = loadedIds.map(
        (thumbnailId) => {
          const state = {
            saved: true,
            loading: false,
          };
          const match = allFetchedData.current.get(thumbnailId);
          if (!match || !match.image) {
            return {
              data: match,
              thumbnail: undefined,
              state,
            };
          }
          return {
            data: match,
            state,
          };
        }
      );

      // saved and loading
      const unLoadedCount = allIds.length - loadedPage * pageSize;
      const loadingCount = isLoading ? Math.min(pageSize, unLoadedCount) : 0;
      const loadingThumbnails: OrderedCategorizedImage[] = Array.from({
        length: loadingCount,
      }).map(() => ({
        data: undefined,
        thumbnail: undefined,
        state: {
          saved: true,
          loading: true,
        },
      }));

      return [...loadedData, ...loadingThumbnails];
    },
    []
  );
  return { processInfiniteScrollData };
};
