import { classes } from "portal/theme/theme";
import { DATE_PATH_FORMAT, isToday } from "portal/utils/dates";
import {
  DateRange,
  DateRangePicker,
  DateRangePickerProps,
  MultiInputDateRangeField,
} from "@mui/x-date-pickers-pro";
import { DateTime } from "luxon";
import { useShadowValue } from "portal/utils/hooks/useShadowValue";
import { useTranslation } from "react-i18next";
import { withErrorBoundary } from "portal/components/ErrorBoundary";
import React, { FunctionComponent } from "react";

export const getDateRangeDescription = (range: DateRange<DateTime>): string =>
  `${range[0]?.toFormat(DATE_PATH_FORMAT)} - ${range[1]?.toFormat(
    DATE_PATH_FORMAT
  )}`;

const _CarbonDateRangePicker: FunctionComponent<
  DateRangePickerProps<DateTime> & {
    openEnded?: boolean;
  }
> = ({
  slots,
  slotProps,
  value: externalValue,
  readOnly,
  onChange,
  openEnded = false,
  ...props
}) => {
  const { t } = useTranslation();

  const [value, setValue, { skipRender }] = useShadowValue<
    DateRange<DateTime> | undefined
  >(externalValue);

  if (skipRender) {
    return;
  }

  return (
    <DateRangePicker<DateTime>
      readOnly={readOnly}
      localeText={{
        start: t("components.DateRangePicker.startDate"),
        end: t("components.DateRangePicker.endDate"),
        clearButtonLabel: t("components.DateRangePicker.clear"),
      }}
      slots={{
        field: MultiInputDateRangeField,
        ...slots,
      }}
      value={value ?? externalValue}
      onChange={([start, end], event) => {
        // start and end are nullable, so we will in fact be using null.
        // eslint-disable-next-line unicorn/no-null
        const startDate = start?.startOf("day") ?? null;
        // eslint-disable-next-line unicorn/no-null
        let endDate = null;
        if (end && end.isValid) {
          endDate = isToday(end) ? DateTime.now() : end.endOf("day");
        }
        setValue([startDate, endDate]);
        if (!openEnded && (!startDate || !endDate)) {
          return;
        }

        if (startDate && !startDate.isValid) {
          return;
        }
        onChange?.([startDate, endDate], event);
      }}
      slotProps={{
        fieldRoot: {
          className: classes("group flex gap-2", props.className, {
            "Mui-readOnly": readOnly,
          }),
        },
        fieldSeparator: {
          className: "hidden",
        },
        previousIconButton: {
          className: "text-white",
        },
        nextIconButton: {
          className: "text-white",
        },
        ...slotProps,
      }}
      {...props}
    />
  );
};

export const CarbonDateRangePicker = withErrorBoundary(
  {
    i18nKey: "components.DateRangePicker.error",
    small: true,
  },
  _CarbonDateRangePicker
);
