import {
  Chip,
  MenuItem,
  Select,
  SelectChangeEvent,
  Skeleton,
} from "@mui/material";
import { classes, SELECT_DARK, theme } from "portal/theme/theme";
import { useTranslation } from "react-i18next";
import React, { CSSProperties, FunctionComponent, useMemo } from "react";

export interface RadioChipOption {
  id: string;
  name: string;
  color?: {
    bg: string;
    text?: string;
  };
}
interface RadioChipsProps {
  isLoading: boolean;
  options: RadioChipOption[];
  activeId?: string;
  setActiveId: (id: string) => void;
  createLabel?: (option: { id: string; name: string }) => string;
  size?: "medium" | "small";
  forceDropdown?: boolean;
}
export const RadioChips: FunctionComponent<RadioChipsProps> = ({
  isLoading = false,
  options,
  activeId,
  setActiveId,
  createLabel,
  size = "medium",
  forceDropdown = false,
}) => {
  const { t } = useTranslation();
  const colorsStyle = (
    isActive: boolean,
    color?: RadioChipOption["color"]
  ): CSSProperties => ({
    borderColor: !isActive && color?.bg ? color.bg : theme.colors.white,
    color: isActive && color?.text ? color.text : theme.colors.white,
    backgroundColor: isActive ? color?.bg : undefined,
  });

  const activeCategory = useMemo(
    () => options.find((o) => o.id === activeId),
    [activeId, options]
  );

  const chipWrapperClasses = classes("flex flex-wrap", {
    "gap-2": size === "small",
    "gap-4": size === "medium",
  });
  return (
    <>
      <div className={classes("hidden", { "md:flex": !forceDropdown })}>
        {isLoading ? (
          <div className={chipWrapperClasses}>
            {Array.from({ length: 5 })
              .fill(undefined)
              .map((_, index) => (
                <div key={index}>
                  <Skeleton
                    variant="rounded"
                    className="rounded-full h-6 w-28"
                  />
                </div>
              ))}
          </div>
        ) : (
          <div className={chipWrapperClasses}>
            {options.map(({ id, name, color }) => (
              <Chip
                key={id}
                className={classes("font-normal", { "p-1": size === "small" })}
                style={colorsStyle(activeId === id, color)}
                variant="outlined"
                label={createLabel?.({ id, name }) ?? name}
                onClick={() => {
                  setActiveId(id);
                }}
                size={size}
              />
            ))}
          </div>
        )}
      </div>
      <div
        className={classes("flex grow", {
          "md:hidden": !forceDropdown,
        })}
      >
        <Select<string>
          className="w-full rounded-full border-solid border-1"
          style={colorsStyle(true, activeCategory?.color)}
          size="small"
          defaultValue={options[0] ? options[0].id : ""}
          value={activeId ?? ""}
          onChange={(event: SelectChangeEvent) => {
            setActiveId(event.target.value as string);
          }}
          classes={{
            ...SELECT_DARK.classes,
            select: size === "small" ? "py-1" : undefined,
          }}
          renderValue={(selected) => {
            if (!selected) {
              return t("utils.descriptors.none");
            }
            const selectedOption = options.find((o) => o.id === selected);
            return createLabel?.(selectedOption!) ?? selectedOption?.name;
          }}
          displayEmpty
        >
          {options.map(({ name, id }) => (
            <MenuItem value={id} key={id}>
              {createLabel?.({ id, name }) ?? name}
            </MenuItem>
          ))}
        </Select>
      </div>
    </>
  );
};
