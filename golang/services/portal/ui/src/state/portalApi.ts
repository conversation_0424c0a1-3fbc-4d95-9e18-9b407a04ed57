export {
  // USERS
  useListUsersQuery,
  useLazyListUsersQuery,
  useGetUserQuery,
  useLazyGetUserQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useInviteUserMutation,
  useCreateFleetViewMutation,
  useUpdateFleetViewMutation,
  useDeleteFleetViewMutation,
} from "common/state/portalApi/user";
import { _basePortalApi, Tag } from "common/state/portalApi/base";
import { AlarmResponse } from "protos/portal/alarms";
import {
  AlmanacConfig,
  DiscriminatorConfig,
  ModelinatorConfig,
} from "protos/almanac/almanac";
import {
  BlocksByDateByRobotResponse,
  BlocksResponse,
} from "protos/portal/spatial";
import { BulkEditRequest, BulkEditResponse } from "protos/robot_syncer/bulk";
import { ConfigNode, SchemaNode } from "protos/config/api/config_service";
import { ConfigResponse } from "protos/portal/configs";
import {
  CreateCategoryCollectionSessionRequest,
  Crop,
  Image,
  Model,
  RobotCrop,
} from "protos/portal/veselka";
import {
  CreatePredictionPointSessionResponse,
  GetPredictionPointSessionResponse,
  ListPredictionPointsResponse,
} from "protos/veselka/prediction_point";
import { CustomerResponse } from "protos/portal/customers";
import {
  DailyMetricResponse,
  DailyMetricsByDateByRobotResponse,
} from "protos/portal/metrics";
import { entries } from "portal/utils/objects";
import { findWhere, sortBy, spliceIfExists } from "portal/utils/arrays";
import { getModelinatorId } from "portal/utils/almanac";
import {
  getNodeFromPath,
  getParentNodeFromPath,
  setValue,
  sortByName,
  sortSchemaByName,
} from "portal/utils/configs";
import { GlobalAlarmlists, RobotAlarmlists } from "protos/portal/admin";
import { Globals, UpdateGlobalsRequest } from "protos/portal/global";
import { HardwareResponse } from "protos/portal/hardware";
import { HistoryResponse } from "protos/portal/health";
import { Method, Pagination } from "common/utils/api";
import { PortalJob } from "protos/portal/jobs";
import { ReportInstanceResponse, ReportResponse } from "protos/portal/reports";
import { RobotClass, robotSort } from "portal/utils/robots";
import { RobotResponse, RobotSummaryResponse } from "protos/portal/robots";
import { RoSyAuditLogResponse } from "protos/portal/audit_logs";
import {
  SavedExpandedCategoryCollection,
  UnsavedExpandedCategoryCollection,
} from "protos/portal/category_profile";
import { TagDescription } from "@reduxjs/toolkit/query/react";
import { TVEProfile } from "protos/target_velocity_estimator/target_velocity_estimator";

const portalApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    // AWS
    getS3UploadUrl: builder.query<string, { filename: string }>({
      query: ({ filename }) => ({
        url: `aws/s3/upload`,
        params: { filename },
      }),
      transformResponse: ({ url }: { url: string }) => url,
    }),

    // ALARMS
    listAlarms: builder.query<AlarmResponse[], void>({
      query: () => ({
        url: `alarms`,
      }),
      transformResponse: (response: AlarmResponse[]) =>
        response.map((alarm) => AlarmResponse.fromJSON(alarm)),
      providesTags: (result) =>
        result
          ? result.map((alarm) => ({ type: Tag.ALARM, id: alarm.db?.id }))
          : [],
    }),

    // HARDWARE
    getHardware: builder.query<HardwareResponse, void>({
      query: () => `hardware`,
      transformResponse: (response: HardwareResponse) =>
        HardwareResponse.fromJSON(response),
      providesTags: (result) => {
        const tags: TagDescription<any>[] = [];
        const robots = new Map<number, boolean>();
        if (result?.lasers) {
          for (const laser of result.lasers) {
            robots.set(laser.robotId, true);
          }
        }
        for (const robotId of robots.keys()) {
          tags.push({
            type: Tag.ROBOT_HARDWARE,
            id: robotId,
          });
        }
        return tags;
      },
    }),

    // CONFIGS
    deleteConfigCache: builder.mutation<void, { serial: string }>({
      query: ({ serial }) => ({
        url: `configs/caches/${serial}`,
        method: Method.DELETE,
      }),
    }),
    getConfig: builder.query<
      ConfigResponse,
      { serial: string; fetchFirst?: boolean; noCache?: boolean }
    >({
      keepUnusedDataFor: 60 * 1, // 1 minute
      query: ({ serial, ...options }) => {
        if (options.noCache) {
          portalApi.util.invalidateTags([{ type: Tag.CONFIG, id: serial }]);
        }
        return { url: `configs/${serial}`, params: options };
      },
      transformResponse: (response: ConfigResponse) => {
        const { config, template, ...result } =
          ConfigResponse.fromJSON(response);
        return {
          ...result,
          config: config ? sortByName(config) : undefined,
          template: template ? sortByName(template) : undefined,
        };
      },
      providesTags: (result, error, { serial }) => [
        { type: Tag.CONFIG, id: serial },
      ],
    }),
    setConfigValue: builder.mutation<
      undefined,
      { serial: string; path: string; value: any }
    >({
      query: ({ serial, path, value }) => ({
        url: `configs/${serial}`,
        method: Method.POST,
        body: { path, value },
      }),
      onQueryStarted: async (
        { serial, path, value },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData("getConfig", { serial }, (data) => {
            const node = getNodeFromPath(data.config, path);
            if (!node) {
              dispatch(
                portalApi.util.invalidateTags([
                  { type: Tag.CONFIG, id: serial },
                ])
              );
              return data;
            }
            setValue(node, value);
            return data;
          })
        );
      },
    }),
    deleteConfigPath: builder.mutation<
      undefined,
      { serial: string; path: string }
    >({
      query: ({ serial, path }) => ({
        url: `configs/${serial}/${path}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async (
        { serial, path },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData("getConfig", { serial }, (data) => {
            const node = getParentNodeFromPath(data.config, path);
            const keyName = path.split("/").pop();
            if (!node?.children) {
              dispatch(
                portalApi.util.invalidateTags([
                  { type: Tag.CONFIG, id: serial },
                ])
              );
              return data;
            }
            for (let index = node.children.length - 1; index > 0; index--) {
              const child = node.children[index];
              if (child?.name === keyName) {
                node.children.splice(index, 1);
              }
            }
            return data;
          })
        );
      },
    }),
    bulkEditConfig: builder.mutation<BulkEditResponse, BulkEditRequest>({
      query: (request) => ({
        url: "configs/bulk-edit",
        method: Method.POST,
        body: BulkEditRequest.toJSON(request),
      }),
      transformResponse: (response: unknown) =>
        BulkEditResponse.fromJSON(response),
      invalidatesTags: (response: BulkEditResponse | undefined) => {
        return (response?.robots ?? []).map(({ serial }) => ({
          type: Tag.CONFIG,
          id: serial,
        }));
      },
    }),

    // CONFIG TEMPLATES
    getConfigTemplate: builder.query<ConfigNode, { serial: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ serial }) => ({ url: `configs/templates/${serial}` }),
      transformResponse: (response: ConfigNode) => {
        const result = ConfigNode.fromJSON(response);
        return sortByName(result);
      },
      providesTags: (result, error, { serial }) => [
        { type: Tag.CONFIG_TEMPLATE, id: serial },
      ],
    }),
    setConfigTemplateValue: builder.mutation<
      undefined,
      { serial: string; path: string; value: any }
    >({
      query: ({ serial, path, value }) => ({
        url: `configs/templates/${serial}`,
        method: Method.POST,
        body: { path, value, timestamp: Date.now() },
      }),
      onQueryStarted: async (
        { serial, path, value },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "getConfigTemplate",
            { serial },
            (template) => {
              const node = getNodeFromPath(template, path);
              if (!node) {
                dispatch(
                  portalApi.util.invalidateTags([
                    { type: Tag.CONFIG_TEMPLATE, id: serial },
                  ])
                );
                return template;
              }
              setValue(node, value);
              return template;
            }
          )
        );
      },
    }),
    deleteConfigTemplatePath: builder.mutation<
      undefined,
      { serial: string; path: string }
    >({
      query: ({ serial, path }) => ({
        url: `configs/templates/${serial}`,
        method: Method.DELETE,
        body: {
          path,
          timestamp: Date.now(),
        },
      }),
      onQueryStarted: async (
        { serial, path },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "getConfigTemplate",
            { serial },
            (template) => {
              const node = getParentNodeFromPath(template, path);
              const keyName = path.split("/").pop();
              if (!node?.children) {
                dispatch(
                  portalApi.util.invalidateTags([
                    { type: Tag.CONFIG_TEMPLATE, id: serial },
                  ])
                );
                return template;
              }
              for (let index = node.children.length - 1; index > 0; index--) {
                const child = node.children[index];
                if (child?.name === keyName) {
                  node.children.splice(index, 1);
                }
              }
              return template;
            }
          )
        );
      },
    }),

    // CONFIG SCHEMAS
    getConfigSchema: builder.query<SchemaNode, { robotClass: RobotClass }>({
      query: ({ robotClass }) => ({ url: `configs/schemas/${robotClass}` }),
      transformResponse: (response: SchemaNode) => {
        const result = SchemaNode.fromJSON(response);
        return sortSchemaByName(result);
      },
      providesTags: (result, error, { robotClass }) => [
        { type: Tag.CONFIG_SCHEMA, id: robotClass },
      ],
    }),

    // CONFIG AUDIT LOGS
    getConfigAuditLog: builder.query<
      RoSyAuditLogResponse,
      { serial: string; lookbackDays?: number; logLimit?: number; key: string }
    >({
      query: ({ serial, lookbackDays = 14, logLimit = 100, key }) => ({
        method: Method.POST,
        url: `rosy_audit_logs/${serial}`,
        body: { lookbackDays, logLimit, key },
      }),
      transformResponse: (response: RoSyAuditLogResponse) =>
        RoSyAuditLogResponse.fromJSON(response),
    }),

    // CUSTOMERS
    listCustomers: builder.query<CustomerResponse[], void>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "customers",
      transformResponse: (response: CustomerResponse[]) =>
        sortBy(response, "name").map((customer) =>
          CustomerResponse.fromJSON(customer)
        ),
      providesTags: (customers) => [
        Tag.CUSTOMER,
        ...(customers?.map((customer) => ({
          type: Tag.CUSTOMER,
          id: customer.db?.id,
        })) ?? []),
      ],
    }),
    getCustomer: builder.query<
      CustomerResponse | undefined,
      { customerId: number }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "customers",
      transformResponse: (
        response: CustomerResponse[] = [],
        _,
        { customerId }
      ) => {
        const customer = response.find(
          (customer) => customer.db?.id === customerId
        );
        return customer ? CustomerResponse.fromJSON(customer) : undefined;
      },
      onQueryStarted: async ({ customerId }, { dispatch, queryFulfilled }) => {
        const { data: customer } = await queryFulfilled;
        if (!customer) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listCustomers",
            undefined,
            (customers) => {
              const index = customers.findIndex(
                (customer) => customer.db?.id === customerId
              );
              customers[index] = customer;
              return customers;
            }
          )
        );
      },
      providesTags: (customer) => [
        { type: Tag.CUSTOMER, id: customer?.db?.id },
      ],
    }),
    createCustomer: builder.mutation<
      CustomerResponse,
      Omit<CustomerResponse, "db" | "featureFlags">
    >({
      query: (customer) => ({
        url: "customers",
        method: Method.POST,
        body: CustomerResponse.toJSON(CustomerResponse.fromPartial(customer)),
      }),
      transformResponse: (customer) => CustomerResponse.fromJSON(customer),
      onQueryStarted: async (customer, { dispatch, queryFulfilled }) => {
        const { data: newCustomer } = await queryFulfilled;
        if (!newCustomer.db?.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listCustomers",
            undefined,
            (customers) => {
              customers.push(newCustomer);
              return customers;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getCustomer",
            { customerId: newCustomer.db.id },
            () => newCustomer
          )
        );
      },
    }),
    updateCustomer: builder.mutation<
      CustomerResponse,
      {
        customerId: number;
        customer: Omit<CustomerResponse, "db" | "featureFlags">;
      }
    >({
      query: ({ customerId, customer }) => ({
        url: `customers/${customerId}`,
        method: Method.POST,
        body: CustomerResponse.toJSON(CustomerResponse.fromPartial(customer)),
      }),
      transformResponse: (customer) => CustomerResponse.fromJSON(customer),
      onQueryStarted: async ({ customerId }, { dispatch, queryFulfilled }) => {
        const { data: updatedCustomer } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listCustomers",
            undefined,
            (customers) => {
              const index = customers.findIndex(
                (customer) => customer.db?.id === customerId
              );
              customers[index] = updatedCustomer;
              return customers;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getCustomer",
            { customerId },
            () => updatedCustomer
          )
        );
      },
    }),

    // REPORTS
    listReports: builder.query<ReportResponse[], void>({
      query: () => "reports",
      transformResponse: (response: ReportResponse[]) =>
        response.map((report) => ReportResponse.fromJSON(report)),
      providesTags: (reports) => [
        Tag.REPORT,
        ...(reports?.map((report) => ({
          type: Tag.REPORT,
          id: report.slug,
        })) ?? []),
      ],
    }),
    getReport: builder.query<
      ReportResponse,
      { reportSlug: string; instance?: string }
    >({
      query: ({ reportSlug, instance }) => ({
        url: `reports/${reportSlug}`,
        params: { instance },
      }),
      transformResponse: (response: ReportResponse) =>
        ReportResponse.fromJSON(response),
      providesTags: (report) => [{ type: Tag.REPORT, id: report?.slug }],
      onQueryStarted: async ({ reportSlug }, { dispatch, queryFulfilled }) => {
        const { data: report } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReports",
            undefined,
            (reports) => {
              const index = reports.findIndex(
                (report) => report.slug === reportSlug
              );
              reports[index] = report;
              return reports;
            }
          )
        );
      },
    }),
    createReport: builder.mutation<ReportResponse, ReportResponse>({
      query: (report) => ({
        url: "reports/",
        method: Method.POST,
        body: ReportResponse.toJSON(ReportResponse.fromPartial(report)),
      }),
      transformResponse: (response: ReportResponse) =>
        ReportResponse.fromJSON(response),
      onQueryStarted: async (report, { dispatch, queryFulfilled }) => {
        const { data: newReport } = await queryFulfilled;
        if (!newReport.db?.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listReports",
            undefined,
            (reports) => {
              reports.push(newReport);
              return reports;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getReport",
            { reportSlug: newReport.slug },
            () => newReport
          )
        );
      },
    }),
    updateReport: builder.mutation<ReportResponse, ReportResponse>({
      query: (report) => ({
        url: `reports/${report.slug}`,
        method: Method.POST,
        body: ReportResponse.toJSON(ReportResponse.fromPartial(report)),
      }),
      transformResponse: (response: ReportResponse) =>
        ReportResponse.fromJSON(response),
      onQueryStarted: async (report, { dispatch, queryFulfilled }) => {
        const { data: updatedReport } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReports",
            undefined,
            (reports) => {
              const index = reports.findIndex(
                (report) => report.slug === updatedReport.slug
              );
              reports[index] = updatedReport;
              return reports;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getReport",
            { reportSlug: updatedReport.slug },
            () => updatedReport
          )
        );
      },
    }),
    deleteReport: builder.mutation<void, { slug: string }>({
      query: ({ slug }) => ({
        url: `reports/${slug}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async ({ slug }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReports",
            undefined,
            (reports) => {
              spliceIfExists(reports, (report) => report.slug === slug, 1);
              return reports;
            }
          )
        );
        dispatch(
          portalApi.util.invalidateTags([{ type: Tag.REPORT, id: slug }])
        );
      },
    }),
    listReportInstances: builder.query<
      ReportInstanceResponse[],
      { slug: string }
    >({
      query: ({ slug }) => `reports/${slug}/runs`,
      transformResponse: (response: ReportInstanceResponse[]) =>
        response.map((instance) => ReportInstanceResponse.fromJSON(instance)),
      providesTags: (instances) => [
        Tag.REPORT_INSTANCE,
        ...(instances?.map((instance) => ({
          type: Tag.REPORT_INSTANCE,
          id: instance.slug,
        })) ?? []),
      ],
    }),
    getReportInstance: builder.query<
      ReportInstanceResponse,
      { reportSlug: string; instanceSlug: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ reportSlug, instanceSlug }) =>
        `reports/${reportSlug}/runs/${instanceSlug}`,
      transformResponse: (response: ReportInstanceResponse) =>
        ReportInstanceResponse.fromJSON(response),
      providesTags: (instance) => [
        { type: Tag.REPORT_INSTANCE, id: instance?.slug },
      ],
      onQueryStarted: async (
        { reportSlug, instanceSlug },
        { dispatch, queryFulfilled }
      ) => {
        const { data: instance } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReportInstances",
            { slug: reportSlug },
            (instances) => {
              const index = instances.findIndex(
                (instance) => instance.slug === instanceSlug
              );
              instances[index] = instance;
              return instances;
            }
          )
        );
      },
    }),
    createReportInstance: builder.mutation<
      ReportInstanceResponse,
      { reportSlug: string; instance: ReportInstanceResponse }
    >({
      query: ({ reportSlug, instance }) => ({
        url: `reports/${reportSlug}/runs`,
        method: Method.POST,
        body: ReportInstanceResponse.toJSON(
          ReportInstanceResponse.fromPartial(instance)
        ),
      }),
      transformResponse: (response: ReportInstanceResponse) =>
        ReportInstanceResponse.fromJSON(response),
      onQueryStarted: async ({ reportSlug }, { dispatch, queryFulfilled }) => {
        const { data: newInstance } = await queryFulfilled;
        if (!newInstance.db?.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listReportInstances",
            { slug: reportSlug },
            (instances) => {
              instances.push(newInstance);
              return instances;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getReportInstance",
            { reportSlug, instanceSlug: newInstance.slug },
            () => newInstance
          )
        );
      },
    }),
    updateReportInstance: builder.mutation<
      ReportInstanceResponse,
      { reportSlug: string; instance: ReportInstanceResponse }
    >({
      query: ({ reportSlug, instance }) => ({
        url: `reports/${reportSlug}/runs/${instance.slug}`,
        method: Method.POST,
        body: ReportInstanceResponse.toJSON(
          ReportInstanceResponse.fromPartial(instance)
        ),
      }),
      transformResponse: (response: ReportInstanceResponse) =>
        ReportInstanceResponse.fromJSON(response),
      onQueryStarted: async ({ reportSlug }, { dispatch, queryFulfilled }) => {
        const { data: updatedInstance } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReportInstances",
            { slug: reportSlug },
            (instances) => {
              const index = instances.findIndex(
                (instance) => instance.slug === updatedInstance.slug
              );
              instances[index] = updatedInstance;
              return instances;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getReportInstance",
            { reportSlug, instanceSlug: updatedInstance.slug },
            () => updatedInstance
          )
        );
      },
    }),
    deleteReportInstance: builder.mutation<
      void,
      { reportSlug: string; instanceSlug: string }
    >({
      query: ({ reportSlug, instanceSlug }) => ({
        url: `reports/${reportSlug}/runs/${instanceSlug}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async (
        { reportSlug, instanceSlug },
        { dispatch, queryFulfilled }
      ) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listReportInstances",
            { slug: reportSlug },
            (instances) => {
              spliceIfExists(
                instances,
                (instance) => instance.slug === instanceSlug,
                1
              );
              return instances;
            }
          )
        );
        dispatch(
          portalApi.util.invalidateTags([
            { type: Tag.REPORT_INSTANCE, id: instanceSlug },
          ])
        );
      },
    }),
    getDateMetrics: builder.query<
      DailyMetricsByDateByRobotResponse,
      { serials: string[]; dates: string[]; instance?: string }
    >({
      query: (parameters) => ({
        url: "metrics/certified",
        params: parameters,
      }),
      transformResponse: (response: DailyMetricsByDateByRobotResponse) =>
        DailyMetricsByDateByRobotResponse.fromJSON(response),
      providesTags: (response) => {
        const tags: TagDescription<Tag>[] = [];
        for (const [serial, metricsByDate] of entries(
          response?.metrics ?? {}
        )) {
          for (const [date] of entries(metricsByDate.metrics)) {
            tags.push({ type: Tag.METRIC, id: `${serial}-${date}` });
          }
        }
        return tags;
      },
    }),
    getSpatial: builder.query<
      BlocksByDateByRobotResponse,
      { serials: string[]; dates: string[] }
    >({
      query: (parameters) => ({
        url: "metrics/spatial",
        params: parameters,
      }),
      transformResponse: (response: BlocksByDateByRobotResponse) =>
        BlocksByDateByRobotResponse.fromJSON(response),
      providesTags: (response) => {
        const tags: TagDescription<Tag>[] = [];
        for (const [serial, blocksByDate] of entries(response?.blocks ?? {})) {
          for (const [date] of entries(blocksByDate.blocks)) {
            tags.push({ type: Tag.SPATIAL, id: `${serial}-${date}` });
          }
        }
        return tags;
      },
    }),

    // ROBOTS
    listRobots: builder.query<
      RobotSummaryResponse[],
      {
        instance?: string;
        latestMetrics?: boolean;
      }
    >({
      keepUnusedDataFor: 60 * 1, // 1 minute
      query: ({ instance, latestMetrics = true }) => ({
        url: `robots`,
        params: {
          showOffline: true,
          showInternal: true,
          instance,
          latestMetrics,
        },
      }),
      transformResponse: (response: RobotSummaryResponse[] = []) => {
        response.sort(robotSort);
        for (const summary of response) {
          if (summary.config) {
            summary.config = sortByName(ConfigNode.fromJSON(summary.config));
          }
        }
        return response.map((summary) =>
          RobotSummaryResponse.fromJSON(summary)
        );
      },
      providesTags: (summaries) => [
        Tag.ROBOT,
        ...(summaries?.map((summary) => ({
          type: Tag.ROBOT,
          id: summary.robot?.db?.id,
        })) ?? []),
      ],
    }),
    getRobot: builder.query<RobotSummaryResponse, { serial: string }>({
      keepUnusedDataFor: 60 * 1, // 1 minute
      query: ({ serial }) => `robots/${serial}`,
      providesTags: (summary) => [
        { type: Tag.ROBOT, id: summary?.robot?.db?.id },
        { type: Tag.CONFIG, id: summary?.robot?.serial },
        ...(summary?.alarmList
          ? summary.alarmList.map((alarm) => ({
              type: Tag.ALARM,
              id: alarm.db?.id,
            }))
          : []),
      ],
      transformResponse: (summary: RobotSummaryResponse) =>
        RobotSummaryResponse.fromJSON(summary),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: summary } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData("listRobots", {}, (robots) => {
            const index = robots.findIndex(
              (robot) => robot.robot?.serial === serial
            );
            robots[index] = summary;
            return robots;
          })
        );
      },
    }),
    getRobotHistory: builder.query<
      HistoryResponse,
      { serial: string; date: string }
    >({
      query: ({ serial, date }) => `robots/${serial}/history/${date}`,
      transformResponse: (response: HistoryResponse) =>
        HistoryResponse.fromJSON(response),
    }),
    getRobotBlocks: builder.query<
      BlocksResponse,
      { serial: string; date: string }
    >({
      query: ({ serial, date }) => `robots/${serial}/blocks/${date}`,
      transformResponse: (response: BlocksResponse) =>
        BlocksResponse.fromJSON(response),
    }),
    getRobotMetrics: builder.query<
      DailyMetricResponse,
      { serial: string; date: string }
    >({
      query: ({ serial, date }) => `robots/${serial}/metrics/${date}`,
      transformResponse: (response: DailyMetricResponse) =>
        DailyMetricResponse.fromJSON(response),
      providesTags: (result, error, { serial, date }) => [
        { type: Tag.METRIC, id: `${serial}-${date}` },
      ],
    }),
    createRobot: builder.mutation<
      RobotResponse,
      { serial: string; copyFrom: string }
    >({
      query: (parameters) => ({
        url: "robots",
        method: Method.POST,
        body: parameters,
      }),
      transformResponse: (summary: RobotResponse) =>
        RobotResponse.fromJSON(summary),
      // can't splice this into the cache because it's not a RobotSummaryResponse
      invalidatesTags: () => [Tag.ROBOT],
    }),
    updateRobot: builder.mutation<
      RobotResponse,
      {
        serial: string;
        robot: Partial<
          Pick<RobotResponse, "implementationStatus" | "supportSlack">
        >;
      }
    >({
      query: ({ serial, robot }) => ({
        url: `robots/${serial}`,
        method: Method.POST,
        body: RobotResponse.toJSON(RobotResponse.fromPartial(robot)),
      }),
      transformResponse: (summary: RobotResponse) =>
        RobotResponse.fromJSON(summary),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: robot } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData("listRobots", {}, (summaries) => {
            const index = summaries.findIndex(
              (instance) => instance.robot?.serial === serial
            );
            if (summaries[index]) {
              summaries[index].robot = robot;
            }
            return summaries;
          })
        );
        dispatch(
          portalApi.util.updateQueryData("getRobot", { serial }, (summary) => {
            summary.robot = robot;
            return summary;
          })
        );
      },
    }),
    assignRobot: builder.mutation<
      RobotResponse,
      { serial: string; customerId: number }
    >({
      query: ({ serial, customerId }) => ({
        url: `robots/${serial}/assign`,
        method: Method.POST,
        body: { customerId },
      }),
      transformResponse: (summary: RobotResponse) =>
        RobotResponse.fromJSON(summary),
      // can't splice this into the cache because it's not a RobotSummaryResponse
      invalidatesTags: (robot) => [
        Tag.ROBOT,
        { type: Tag.ROBOT, id: robot?.db?.id },
      ],
    }),
    getRobotProxy: builder.mutation<RobotResponse, { serial: string }>({
      query: ({ serial }) => `robots/${serial}/remote`,
      transformResponse: (summary: RobotResponse) =>
        RobotResponse.fromJSON(summary),
    }),
    listRobotCrops: builder.query<RobotCrop[], { serial: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ serial }) => `robots/${serial}/crops`,
      transformResponse: (response: RobotCrop[]) =>
        response.map((crop) => RobotCrop.fromJSON(crop)),
      providesTags: (robotCrops) => [
        Tag.ROBOT_CROP,
        ...(robotCrops?.map(({ crop }) => ({
          type: Tag.ROBOT_CROP,
          id: crop?.id,
        })) ?? []),
      ],
    }),
    listRobotJobs: builder.query<
      PortalJob[],
      { serial: string; startDate: string; endDate: string }
    >({
      query: ({ serial, startDate, endDate }) =>
        `robots/${serial}/jobs/${startDate}/${endDate}`,
      transformResponse: (response: PortalJob[]) =>
        response.map((job) => PortalJob.fromJSON(job)),
      providesTags: (jobs) => [
        Tag.ROBOT_JOB,
        ...(jobs?.map((job) => ({
          type: Tag.ROBOT_JOB,
          id: job.jobId,
        })) ?? []),
      ],
    }),
    getJob: builder.query<PortalJob, { jobId: string }>({
      query: ({ jobId }) => `jobs/${jobId}`,
      transformResponse: (response: PortalJob) => PortalJob.fromJSON(response),
      providesTags: (job) => [
        Tag.ROBOT_JOB,
        { type: Tag.ROBOT_JOB, id: job?.jobId },
        { type: Tag.JOB_METRIC, id: job?.jobId },
      ],
    }),
    getJobHistory: builder.query<HistoryResponse, { jobId: string }>({
      query: ({ jobId }) => `jobs/${jobId}/history`,
      transformResponse: (response: HistoryResponse) =>
        HistoryResponse.fromJSON(response),
    }),
    getJobBlocks: builder.query<BlocksResponse, { jobId: string }>({
      query: ({ jobId }) => `jobs/${jobId}/blocks`,
      transformResponse: (response: BlocksResponse) =>
        BlocksResponse.fromJSON(response),
    }),
    getRobotHardware: builder.query<HardwareResponse, { serial: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ serial }) => `robots/${serial}/hardware`,
      transformResponse: (response: HardwareResponse) =>
        HardwareResponse.fromJSON(response),
      providesTags: (robotCrops, error, { serial }) => [
        Tag.ROBOT_HARDWARE,
        {
          type: Tag.ROBOT_HARDWARE,
          id: serial,
        },
      ],
    }),

    // VESELKA
    listUploads: builder.query<
      Image[],
      { serial: string; pagination?: Pagination }
    >({
      query: ({ serial, pagination }) => ({
        url: `veselka/uploads/${serial}`,
        params: pagination,
      }),
      transformResponse: (images: Image[]) =>
        images.map((image) => Image.fromJSON(image)),
      providesTags: (images) => [
        Tag.IMAGE,
        ...(images?.map((image) => ({
          type: Tag.IMAGE,
          id: image.id,
        })) ?? []),
      ],
    }),
    listCrops: builder.query<Crop[], void>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "veselka/models/crops",
      transformResponse: (crops: Crop[]) =>
        crops.map((crop) => Crop.fromJSON(crop)),
      providesTags: (crops) => [
        Tag.CROP,
        ...(crops?.map((crop) => ({
          type: Tag.CROP,
          id: crop.id,
        })) ?? []),
      ],
    }),
    getCrop: builder.query<Crop | undefined, { cropId: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "veselka/models/crops",
      providesTags: (crop) =>
        crop
          ? [
              {
                type: Tag.CROP,
                id: crop.id,
              },
            ]
          : [],
      transformResponse: (crops: Crop[], error, { cropId }) => {
        const crop = findWhere(crops, { id: cropId });
        if (!crop) {
          return;
        }
        return Crop.fromJSON(crop);
      },
      onQueryStarted: async ({ cropId }, { dispatch, queryFulfilled }) => {
        const { data: crop } = await queryFulfilled;
        if (!crop) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData("listCrops", undefined, (crops) => {
            const index = crops.findIndex((crop) => crop.id === cropId);
            crops[index] = crop;
            return crops;
          })
        );
      },
    }),
    listPredictionPoints: builder.query<
      ListPredictionPointsResponse,
      {
        page: number;
        pageSize: number;
        ids?: string[];
        crops?: string[];
        robots?: string[];
        capturedAt?: [string | undefined, string | undefined];
        radius?: [number | undefined, number | undefined];
        sessionId?: string;
        categoryIds?: string[];
        uploadedByOperator?: boolean;
      }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({
        page,
        pageSize,
        ids,
        crops,
        robots,
        capturedAt,
        radius,
        sessionId,
        categoryIds,
        uploadedByOperator,
      }) => ({
        url: "veselka/prediction-points",
        params: {
          page,
          pageSize,
          crops,
          robots,
          capturedAt: (capturedAt || []).map((value) => value ?? "").join(","),
          radius: (radius || []).map((value) => value ?? "").join(","),
          ids,
          sessionId,
          categoryIds,
          uploadedByOperator,
        },
      }),
      transformResponse: (response: ListPredictionPointsResponse) =>
        ListPredictionPointsResponse.fromJSON(response),
    }),
    createCategoryCollectionSession: builder.mutation<
      CreatePredictionPointSessionResponse,
      CreateCategoryCollectionSessionRequest
    >({
      query: (body) => ({
        url: "veselka/category-collections/session",
        method: Method.POST,
        body: CreateCategoryCollectionSessionRequest.toJSON(body),
      }),
      transformResponse: (response: CreatePredictionPointSessionResponse) =>
        CreatePredictionPointSessionResponse.fromJSON(response),
    }),
    getModel: builder.query<Model, { modelId: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ modelId }) => `veselka/models/${modelId}`,
      transformResponse: (model: Model) => Model.fromJSON(model),
      providesTags: (result, error, { modelId }) => [
        { type: Tag.MODEL, id: modelId },
      ],
    }),
    getCategoryCollectionSession: builder.query<
      GetPredictionPointSessionResponse,
      { sessionId: string }
    >({
      query: ({ sessionId }) => ({
        url: `veselka/category-collections/session/${sessionId}`,
        method: Method.GET,
      }),
      transformResponse: (response: GetPredictionPointSessionResponse) =>
        GetPredictionPointSessionResponse.fromJSON(response),
    }),

    // ADMIN
    deleteCaches: builder.mutation<void, void>({
      query: () => ({
        url: `/admin/caches`,
        method: Method.DELETE,
      }),
    }),
    getGlobalAlarmLists: builder.query<GlobalAlarmlists, void>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "/admin/alarms/lists",
      transformResponse: (response: GlobalAlarmlists) =>
        GlobalAlarmlists.fromJSON(response),
      providesTags: () => [
        {
          type: Tag.ADMIN_ALARM,
          id: -1,
        },
      ],
    }),
    getRobotAlarmLists: builder.query<RobotAlarmlists, { serial: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ serial }) => `/admin/alarms/lists/${serial}`,
      transformResponse: (response: RobotAlarmlists) =>
        RobotAlarmlists.fromJSON(response),
      providesTags: (result, error, { serial }) => [
        {
          type: Tag.ADMIN_ALARM,
          id: serial,
        },
      ],
    }),
    setGlobalAlarmLists: builder.mutation<
      GlobalAlarmlists,
      Omit<GlobalAlarmlists, "db">
    >({
      query: (lists) => ({
        url: "admin/alarms/lists",
        method: Method.POST,
        body: GlobalAlarmlists.toJSON(lists),
      }),
      transformResponse: (response: GlobalAlarmlists) =>
        GlobalAlarmlists.fromJSON(response),
      onQueryStarted: async (lists, { dispatch, queryFulfilled }) => {
        const { data: newLists } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "getGlobalAlarmLists",
            undefined,
            () => newLists
          )
        );
      },
    }),
    setRobotAlarmLists: builder.mutation<
      RobotAlarmlists,
      { serial: string; lists: Omit<RobotAlarmlists, "db"> }
    >({
      query: ({ serial, lists }) => ({
        url: `admin/alarms/lists/${serial}`,
        method: Method.POST,
        body: RobotAlarmlists.toJSON(lists),
      }),
      transformResponse: (response: GlobalAlarmlists) =>
        RobotAlarmlists.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newLists } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "getRobotAlarmLists",
            { serial },
            () => newLists
          )
        );
      },
    }),

    // ALMANANC
    listAlmanacsForRobot: builder.query<AlmanacConfig[], { serial?: string }>({
      query: ({ serial }) => `/profiles/almanacs/robots/${serial}`,
      transformResponse: (response: AlmanacConfig[]) =>
        response.map((almanac) => AlmanacConfig.fromJSON(almanac)),
      providesTags: (almanacs) =>
        almanacs?.map((almanac) => ({
          type: Tag.ALMANAC,
          id: almanac.id,
        })) ?? [],
    }),
    getAlmanac: builder.query<
      AlmanacConfig,
      {
        uuid: string;
      }
    >({
      query: ({ uuid }) => `/profiles/almanacs/profiles/${uuid}`,
      transformResponse: (response: AlmanacConfig) =>
        AlmanacConfig.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.ALMANAC,
          id: uuid,
        },
      ],
    }),
    deleteAlmanac: builder.mutation<void, { uuid: string }>({
      query: ({ uuid }) => ({
        url: `/profiles/almanacs/profiles/${uuid}`,
        method: Method.DELETE,
      }),
      invalidatesTags: (result, error, { uuid }) => [
        { type: Tag.ALMANAC, id: uuid },
      ],
    }),
    setAlmanac: builder.mutation<
      AlmanacConfig,
      { serial?: string; almanac: AlmanacConfig }
    >({
      query: ({ serial, almanac }) => ({
        url: `/profiles/almanacs/robots/${serial}`,
        method: Method.POST,
        body: AlmanacConfig.toJSON(almanac),
      }),
      transformResponse: (response: AlmanacConfig) =>
        AlmanacConfig.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newAlmanac } = await queryFulfilled;
        if (!serial || !newAlmanac.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listAlmanacsForRobot",
            { serial },
            (almanacs) => {
              almanacs.push(newAlmanac);
              return almanacs;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getAlmanac",
            { uuid: newAlmanac.id },
            () => newAlmanac
          )
        );
      },
    }),
    // any string is valid for the input. Just matching the signature of
    // listAlmanacsForRobot so they can be used interchangeably
    listGlobalAlmanacs: builder.query<AlmanacConfig[], { serial?: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => `/profiles/almanacs/global`,
      transformResponse: (response: AlmanacConfig[]) =>
        response.map((almanac) => AlmanacConfig.fromJSON(almanac)),
      providesTags: (almanacs) =>
        almanacs?.map((almanac) => ({
          type: Tag.ADMIN_ALMANAC,
          id: almanac.id,
        })) ?? [],
    }),
    getGlobalAlmanac: builder.query<AlmanacConfig, { uuid: string }>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ uuid }) => `/profiles/almanacs/global/${uuid}`,
      transformResponse: (response: AlmanacConfig) =>
        AlmanacConfig.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.ADMIN_ALMANAC,
          id: uuid,
        },
      ],
      onQueryStarted: async ({ uuid }, { dispatch, queryFulfilled }) => {
        const { data: newAlmanac } = await queryFulfilled;
        if (!newAlmanac.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalAlmanacs",
            {},
            (almanacs) => {
              const index = almanacs.findIndex(
                (almanac) => almanac.id === uuid
              );
              if (index === -1) {
                almanacs.push(newAlmanac);
              } else {
                almanacs[index] = newAlmanac;
              }
              return almanacs;
            }
          )
        );
      },
    }),
    deleteGlobalAlmanac: builder.mutation<void, { uuid: string }>({
      query: ({ uuid }) => ({
        url: `/profiles/almanacs/global/${uuid}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async ({ uuid }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalAlmanacs",
            {},
            (almanacs) => {
              spliceIfExists(almanacs, (almanac) => almanac.id === uuid, 1);
              return almanacs;
            }
          )
        );
        dispatch(
          portalApi.util.invalidateTags([{ type: Tag.ADMIN_ALMANAC, id: uuid }])
        );
      },
    }),
    setGlobalAlmanac: builder.mutation<
      AlmanacConfig,
      { serial?: string; almanac: AlmanacConfig }
    >({
      query: ({ almanac }) => {
        return {
          url: `/profiles/almanacs/global/${almanac.id}`,
          method: Method.POST,
          body: AlmanacConfig.toJSON(almanac),
        };
      },
      transformResponse: (response: AlmanacConfig) =>
        AlmanacConfig.fromJSON(response),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data: newAlmanac } = await queryFulfilled;
        if (!newAlmanac.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalAlmanacs",
            {},
            (almanacs) => {
              const index = almanacs.findIndex(
                (almanac) => almanac.id === newAlmanac.id
              );
              if (index === -1) {
                almanacs.push(newAlmanac);
              } else {
                almanacs[index] = newAlmanac;
              }
              return almanacs;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getGlobalAlmanac",
            { uuid: newAlmanac.id },
            () => newAlmanac
          )
        );
      },
    }),

    // DISCRIMINATOR
    listDiscriminatorsForRobot: builder.query<
      DiscriminatorConfig[],
      { serial: string }
    >({
      query: ({ serial }) => `/profiles/discriminators/robots/${serial}`,
      transformResponse: (response: DiscriminatorConfig[]) =>
        response.map((discriminator) =>
          DiscriminatorConfig.fromJSON(discriminator)
        ),
      providesTags: (discriminators) =>
        discriminators?.map((discrimininator) => ({
          type: Tag.DISCRIMINATOR,
          id: discrimininator.id,
        })) ?? [],
    }),
    getDiscriminator: builder.query<DiscriminatorConfig, { uuid: string }>({
      query: ({ uuid }) => `/profiles/discriminators/profiles/${uuid}`,
      transformResponse: (response: DiscriminatorConfig) =>
        DiscriminatorConfig.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.DISCRIMINATOR,
          id: uuid,
        },
      ],
    }),
    deleteDiscriminator: builder.mutation<void, { uuid: string }>({
      query: ({ uuid }) => ({
        url: `/profiles/discriminators/profiles/${uuid}`,
        method: Method.DELETE,
      }),
      invalidatesTags: (result, error, { uuid }) => [
        { type: Tag.DISCRIMINATOR, id: uuid },
      ],
    }),
    setDiscriminator: builder.mutation<
      DiscriminatorConfig,
      { serial: string; discriminator: DiscriminatorConfig }
    >({
      query: ({ serial, discriminator }) => ({
        url: `/profiles/discriminators/robots/${serial}`,
        method: Method.POST,
        body: DiscriminatorConfig.toJSON(discriminator),
      }),
      transformResponse: (response: DiscriminatorConfig) =>
        DiscriminatorConfig.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newDiscriminator } = await queryFulfilled;
        if (!newDiscriminator.id) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listDiscriminatorsForRobot",
            { serial },
            (discriminators) => {
              const index = discriminators.findIndex(
                (discriminator) => discriminator.id === newDiscriminator.id
              );
              if (index === -1) {
                discriminators.push(newDiscriminator);
              } else {
                discriminators[index] = newDiscriminator;
              }
              return discriminators;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getDiscriminator",
            { uuid: newDiscriminator.id },
            () => newDiscriminator
          )
        );
      },
    }),

    // MODELINATOR
    listModelinatorsForRobotAndCrop: builder.query<
      ModelinatorConfig[],
      { serial: string; cropId: string }
    >({
      query: ({ serial, cropId }) =>
        `/profiles/modelinators/robots/${serial}/crops/${cropId}`,
      transformResponse: (response: ModelinatorConfig[]) =>
        response.map((modelinator) => ModelinatorConfig.fromJSON(modelinator)),
      providesTags: (modelinators, error, { serial }) =>
        modelinators?.map((modelinator) => ({
          type: Tag.MODELINDATOR,
          id: getModelinatorId(serial, modelinator),
        })) ?? [],
    }),
    getModelinator: builder.query<
      ModelinatorConfig | undefined,
      { serial: string; cropId: string; modelId: string }
    >({
      query: ({ serial, cropId }) =>
        `/profiles/modelinators/robots/${serial}/crops/${cropId}`,
      transformResponse: (
        modelinators: ModelinatorConfig[],
        error,
        { cropId, modelId }
      ) => {
        const modelinator = findWhere(modelinators, { cropId, modelId });
        if (!modelinator) {
          return;
        }
        return ModelinatorConfig.fromJSON(modelinator);
      },
      onQueryStarted: async (
        { serial, cropId, modelId },
        { dispatch, queryFulfilled }
      ) => {
        const { data: newModelinator } = await queryFulfilled;
        if (!newModelinator) {
          return;
        }
        dispatch(
          portalApi.util.updateQueryData(
            "listModelinatorsForRobotAndCrop",
            { serial, cropId },
            (modelinators) => {
              const index = modelinators.findIndex(
                (modelinator) => modelinator.modelId === modelId
              );
              if (index === -1) {
                modelinators.push(newModelinator);
              } else {
                modelinators[index] = newModelinator;
              }
              return modelinators;
            }
          )
        );
      },
    }),
    setModelinator: builder.mutation<
      ModelinatorConfig,
      { serial: string; modelinator: ModelinatorConfig }
    >({
      query: ({ serial, modelinator }) => ({
        url: `/profiles/modelinators/robots/${serial}`,
        method: Method.POST,
        body: ModelinatorConfig.toJSON(modelinator),
      }),
      transformResponse: (response: ModelinatorConfig) =>
        ModelinatorConfig.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newModelinator } = await queryFulfilled;
        const { cropId, modelId } = newModelinator;
        dispatch(
          portalApi.util.updateQueryData(
            "listModelinatorsForRobotAndCrop",
            { serial, cropId: newModelinator.cropId },
            (modelinators) => {
              const index = modelinators.findIndex(
                (modelinator) => modelinator.modelId === newModelinator.modelId
              );
              if (index === -1) {
                modelinators.push(newModelinator);
              } else {
                modelinators[index] = newModelinator;
              }
              return modelinators;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getModelinator",
            { serial, cropId, modelId },
            () => newModelinator
          )
        );
      },
    }),

    // TARGET VELOCITY ESTIMATOR
    // any string is valid for the input. Just matching the signature of
    // listTargetVelocityEstimators so they can be used interchangeably
    listGlobalTargetVelocityEstimators: builder.query<
      TVEProfile[],
      { serial?: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => `/profiles/velocity/global`,
      transformResponse: (response: TVEProfile[]) =>
        response.map((targetVelocityEstimator) =>
          TVEProfile.fromJSON(targetVelocityEstimator)
        ),
      providesTags: (targetVelocityEstimators) =>
        targetVelocityEstimators?.map((targetVelocityEstimator) => ({
          type: Tag.TARGET_VELOCITY_ESTIMATOR,
          id: targetVelocityEstimator.id,
        })) ?? [],
    }),
    getGlobalTargetVelocityEstimator: builder.query<
      TVEProfile,
      { uuid: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ uuid }) => `/profiles/velocity/global/${uuid}`,
      transformResponse: (response: TVEProfile) =>
        TVEProfile.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.ADMIN_TARGET_VELOCITY_ESTIMATOR,
          id: uuid,
        },
      ],
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data: newTargetVelocityEstimator } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalTargetVelocityEstimators",
            {},
            (targetVelocityEstimators) => {
              const index = targetVelocityEstimators.findIndex(
                (targetVelocityEstimator) =>
                  targetVelocityEstimator.id === newTargetVelocityEstimator.id
              );
              if (index === -1) {
                targetVelocityEstimators.push(newTargetVelocityEstimator);
              } else {
                targetVelocityEstimators[index] = newTargetVelocityEstimator;
              }
              return targetVelocityEstimators;
            }
          )
        );
      },
    }),
    deleteTargetVelocityEstimator: builder.mutation<void, { uuid: string }>({
      query: ({ uuid }) => ({
        url: `/profiles/velocity/profiles/${uuid}`,
        method: Method.DELETE,
      }),
      invalidatesTags: (result, error, { uuid }) => [
        { type: Tag.TARGET_VELOCITY_ESTIMATOR, id: uuid },
      ],
    }),
    deleteGlobalTargetVelocityEstimator: builder.mutation<
      void,
      { uuid: string }
    >({
      query: ({ uuid }) => ({
        url: `/profiles/velocity/global/${uuid}`,
        method: Method.DELETE,
      }),
      onQueryStarted: async ({ uuid }, { dispatch, queryFulfilled }) => {
        await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalTargetVelocityEstimators",
            {},
            (targetVelocityEstimators) => {
              spliceIfExists(
                targetVelocityEstimators,
                (targetVelocityEstimator) =>
                  targetVelocityEstimator.id === uuid,
                1
              );
              return targetVelocityEstimators;
            }
          )
        );
        dispatch(
          portalApi.util.invalidateTags([
            { type: Tag.ADMIN_TARGET_VELOCITY_ESTIMATOR, id: uuid },
          ])
        );
      },
    }),
    setGlobalTargetVelocityEstimator: builder.mutation<
      TVEProfile,
      { serial?: string; targetVelocityEstimator: TVEProfile }
    >({
      query: ({ targetVelocityEstimator }) => {
        return {
          url: `/profiles/velocity/global/${targetVelocityEstimator.id}`,
          method: Method.POST,
          body: TVEProfile.toJSON(targetVelocityEstimator),
        };
      },
      transformResponse: (response: TVEProfile) =>
        TVEProfile.fromJSON(response),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data: newTargetVelocityEstimator } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalTargetVelocityEstimators",
            {},
            (targetVelocityEstimators) => {
              const index = targetVelocityEstimators.findIndex(
                (targetVelocityEstimator) =>
                  targetVelocityEstimator.id === newTargetVelocityEstimator.id
              );
              if (index === -1) {
                targetVelocityEstimators.push(newTargetVelocityEstimator);
              } else {
                targetVelocityEstimators[index] = newTargetVelocityEstimator;
              }
              return targetVelocityEstimators;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getGlobalTargetVelocityEstimator",
            { uuid: newTargetVelocityEstimator.id },
            () => newTargetVelocityEstimator
          )
        );
      },
    }),
    listTargetVelocityEstimators: builder.query<
      TVEProfile[],
      { serial?: string }
    >({
      query: ({ serial }) => `/profiles/velocity/robots/${serial}`,
      transformResponse: (response: TVEProfile[]) =>
        response.map((targetVelocityEstimator) =>
          TVEProfile.fromJSON(targetVelocityEstimator)
        ),
      providesTags: (targetVelocityEstimators) =>
        targetVelocityEstimators?.map((targetVelocityEstimator) => ({
          type: Tag.TARGET_VELOCITY_ESTIMATOR,
          id: targetVelocityEstimator.id,
        })) ?? [],
    }),
    getTargetVelocityEstimator: builder.query<TVEProfile, { uuid: string }>({
      query: ({ uuid }) => `/profiles/velocity/profiles/${uuid}`,
      transformResponse: (response: TVEProfile) =>
        TVEProfile.fromJSON(response),
      providesTags: (targetVelocityTestimator) =>
        targetVelocityTestimator
          ? [
              {
                type: Tag.TARGET_VELOCITY_ESTIMATOR,
                id: targetVelocityTestimator.id,
              },
            ]
          : [],
    }),
    setTargetVelocityEstimator: builder.mutation<
      TVEProfile,
      { serial?: string; targetVelocityEstimator: TVEProfile }
    >({
      query: ({ serial, targetVelocityEstimator }) => ({
        url: `/profiles/velocity/robots/${serial}`,
        method: Method.POST,
        body: TVEProfile.toJSON(targetVelocityEstimator),
      }),
      transformResponse: (response: TVEProfile) =>
        TVEProfile.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newTargetVelocityEstimator } = await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listTargetVelocityEstimators",
            { serial },
            (targetVelocityEstimators) => {
              const index = targetVelocityEstimators.findIndex(
                (targetVelocityEstimator) =>
                  targetVelocityEstimator.id === newTargetVelocityEstimator.id
              );
              if (index === -1) {
                targetVelocityEstimators.push(newTargetVelocityEstimator);
              } else {
                targetVelocityEstimators[index] = newTargetVelocityEstimator;
              }
              return targetVelocityEstimators;
            }
          )
        );
        dispatch(
          portalApi.util.updateQueryData(
            "getTargetVelocityEstimator",
            { uuid: newTargetVelocityEstimator.id },
            () => newTargetVelocityEstimator
          )
        );
      },
    }),

    // CATEGORY COLLECTION PROFILE
    listCustomerCategoryCollectionProfiles: builder.query<
      SavedExpandedCategoryCollection[],
      { serial?: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ serial }) => `/profiles/category-collections/robots/${serial}`,
      transformResponse: (response: SavedExpandedCategoryCollection[]) =>
        response.map((categoryCollectionProfile) =>
          SavedExpandedCategoryCollection.fromJSON(categoryCollectionProfile)
        ),
      providesTags: (categoryCollectionProfiles) => [
        Tag.CATEGORY_COLLECTION_PROFILE,
        ...(categoryCollectionProfiles?.map((categoryCollectionProfile) => ({
          type: Tag.CATEGORY_COLLECTION_PROFILE,
          id: categoryCollectionProfile.profile?.profile?.id,
        })) ?? []),
      ],
    }),
    listGlobalCategoryCollectionProfiles: builder.query<
      SavedExpandedCategoryCollection[],
      { serial?: string } // only exists to match input/output interface between global and customer equivalent queries
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => `/profiles/category-collections/global`,
      transformResponse: (response: SavedExpandedCategoryCollection[]) =>
        response.map((categoryCollectionProfile) =>
          SavedExpandedCategoryCollection.fromJSON(categoryCollectionProfile)
        ),
      providesTags: (categoryCollectionProfiles) => [
        Tag.ADMIN_CATEGORY_COLLECTION_PROFILE,
        ...(categoryCollectionProfiles?.map((categoryCollectionProfile) => ({
          type: Tag.ADMIN_CATEGORY_COLLECTION_PROFILE,
          id: categoryCollectionProfile.profile?.profile?.id,
        })) ?? []),
      ],
    }),
    getCustomerCategoryCollectionProfile: builder.query<
      SavedExpandedCategoryCollection,
      { uuid: string; serial?: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ uuid, serial }) =>
        `/profiles/category-collections/${uuid}/robots/${serial}`,
      transformResponse: (response: SavedExpandedCategoryCollection) =>
        SavedExpandedCategoryCollection.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.CATEGORY_COLLECTION_PROFILE,
          id: uuid,
        },
      ],
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newExpandedCategoryCollectionProfile } =
          await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listCustomerCategoryCollectionProfiles",
            { serial },
            (categoryCollectionProfiles) => {
              const index = categoryCollectionProfiles.findIndex(
                (categoryCollectionProfile) =>
                  categoryCollectionProfile.profile?.profile?.id ===
                  newExpandedCategoryCollectionProfile.profile?.profile?.id
              );
              if (index === -1) {
                categoryCollectionProfiles.push(
                  newExpandedCategoryCollectionProfile
                );
              } else {
                categoryCollectionProfiles[index] =
                  newExpandedCategoryCollectionProfile;
              }
              return categoryCollectionProfiles;
            }
          )
        );
      },
    }),
    getGlobalCategoryCollectionProfile: builder.query<
      SavedExpandedCategoryCollection,
      { uuid: string; serial?: string }
    >({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: ({ uuid }) => `/profiles/category-collections/global/${uuid}`,
      transformResponse: (response: SavedExpandedCategoryCollection) =>
        SavedExpandedCategoryCollection.fromJSON(response),
      providesTags: (result, error, { uuid }) => [
        {
          type: Tag.ADMIN_CATEGORY_COLLECTION_PROFILE,
          id: uuid,
        },
      ],
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data: newExpandedCategoryCollectionProfile } =
          await queryFulfilled;
        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalCategoryCollectionProfiles",
            {},
            (categoryCollectionProfiles) => {
              const index = categoryCollectionProfiles.findIndex(
                (categoryCollectionProfile) =>
                  categoryCollectionProfile.profile?.profile?.id ===
                  newExpandedCategoryCollectionProfile.profile?.profile?.id
              );
              if (index === -1) {
                categoryCollectionProfiles.push(
                  newExpandedCategoryCollectionProfile
                );
              } else {
                categoryCollectionProfiles[index] =
                  newExpandedCategoryCollectionProfile;
              }
              return categoryCollectionProfiles;
            }
          )
        );
      },
    }),
    setGlobalCategoryCollectionProfile: builder.mutation<
      SavedExpandedCategoryCollection,
      {
        serial?: string;
        expandedCategoryCollection: UnsavedExpandedCategoryCollection;
      }
    >({
      query: ({ expandedCategoryCollection }) => {
        return {
          url: `/profiles/category-collections/global/${expandedCategoryCollection.profile?.id}`,
          method: Method.POST,
          body: UnsavedExpandedCategoryCollection.toJSON(
            expandedCategoryCollection
          ),
        };
      },
      transformResponse: (response: SavedExpandedCategoryCollection) =>
        SavedExpandedCategoryCollection.fromJSON(response),
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        const { data: newExpandedCategoryCollectionProfile } =
          await queryFulfilled;

        dispatch(
          portalApi.util.updateQueryData(
            "listGlobalCategoryCollectionProfiles",
            {},
            (categoryCollectionProfiles) => {
              const index = categoryCollectionProfiles.findIndex(
                (categoryCollectionProfile) =>
                  categoryCollectionProfile.profile?.profile?.id ===
                  newExpandedCategoryCollectionProfile.profile?.profile?.id
              );
              if (index === -1) {
                categoryCollectionProfiles.push(
                  newExpandedCategoryCollectionProfile
                );
              } else {
                categoryCollectionProfiles[index] =
                  newExpandedCategoryCollectionProfile;
              }
              return categoryCollectionProfiles;
            }
          )
        );
        if (newExpandedCategoryCollectionProfile.profile?.profile?.id) {
          dispatch(
            portalApi.util.updateQueryData(
              "getGlobalCategoryCollectionProfile",
              {
                uuid: newExpandedCategoryCollectionProfile.profile.profile.id,
              },
              () => newExpandedCategoryCollectionProfile
            )
          );
        }
      },
    }),
    setCustomerCategoryCollectionProfile: builder.mutation<
      SavedExpandedCategoryCollection,
      {
        serial?: string;
        expandedCategoryCollection: UnsavedExpandedCategoryCollection;
      }
    >({
      query: ({ serial, expandedCategoryCollection }) => {
        return {
          url: `/profiles/category-collections/${expandedCategoryCollection.profile?.id}/robots/${serial}`,
          method: Method.POST,
          body: UnsavedExpandedCategoryCollection.toJSON(
            expandedCategoryCollection
          ),
        };
      },
      transformResponse: (response: SavedExpandedCategoryCollection) =>
        SavedExpandedCategoryCollection.fromJSON(response),
      onQueryStarted: async ({ serial }, { dispatch, queryFulfilled }) => {
        const { data: newExpandedCategoryCollectionProfile } =
          await queryFulfilled;
        const newProfileId =
          newExpandedCategoryCollectionProfile.profile?.profile?.id;
        dispatch(
          portalApi.util.updateQueryData(
            "listCustomerCategoryCollectionProfiles",
            { serial },
            (categoryCollectionProfiles) => {
              const index = categoryCollectionProfiles.findIndex(
                (categoryCollectionProfile) =>
                  categoryCollectionProfile.profile?.profile?.id ===
                  newProfileId
              );
              if (index === -1) {
                categoryCollectionProfiles.push(
                  newExpandedCategoryCollectionProfile
                );
              } else {
                categoryCollectionProfiles[index] =
                  newExpandedCategoryCollectionProfile;
              }
              return categoryCollectionProfiles;
            }
          )
        );
        if (newProfileId) {
          dispatch(
            portalApi.util.updateQueryData(
              "getCustomerCategoryCollectionProfile",
              {
                uuid: newProfileId,
                serial,
              },
              () => newExpandedCategoryCollectionProfile
            )
          );
        }
      },
    }),
    deleteGlobalCategoryCollectionProfile: builder.mutation<
      void,
      { uuid: string; serial?: string }
    >({
      query: ({ uuid }) => ({
        url: `/profiles/category-collections/global/${uuid}`,
        method: Method.DELETE,
      }),
      invalidatesTags: (result, error, { uuid }) => [
        { type: Tag.ADMIN_CATEGORY_COLLECTION_PROFILE, id: uuid },
      ],
    }),
    deleteCustomerCategoryCollectionProfile: builder.mutation<
      void,
      { uuid: string; serial?: string }
    >({
      query: ({ uuid, serial }) => {
        return {
          url: `/profiles/category-collections/${uuid}/robots/${serial}`,
          method: Method.DELETE,
        };
      },
      invalidatesTags: (result, error, { uuid }) => [
        { type: Tag.CATEGORY_COLLECTION_PROFILE, id: uuid },
      ],
    }),
    getGlobals: builder.query<Globals, void>({
      keepUnusedDataFor: 60 * 60, // 1 hour
      query: () => "/globals",
      transformResponse: (response: any) => Globals.fromJSON(response),
      providesTags: [Tag.GLOBAL],
    }),
    setGlobals: builder.mutation<Globals, UpdateGlobalsRequest>({
      query: (global) => {
        return {
          url: `/globals`,
          method: Method.PUT,
          body: UpdateGlobalsRequest.toJSON(global),
        };
      },
      transformResponse: (response: Global) => Globals.fromJSON(response),
      invalidatesTags: [Tag.GLOBAL],
    }),
  }),
  overrideExisting: "throw",
});

export const {
  // AWS
  useGetS3UploadUrlQuery,
  useLazyGetS3UploadUrlQuery,

  // ALARMS
  useListAlarmsQuery,
  useLazyListAlarmsQuery,

  // HARDWARE
  useGetHardwareQuery,
  useLazyGetHardwareQuery,

  // CONFIGS
  useDeleteConfigCacheMutation,
  useGetConfigQuery,
  useLazyGetConfigQuery,
  useSetConfigValueMutation,
  useDeleteConfigPathMutation,
  useBulkEditConfigMutation,

  // CONFIG TEMPLATES
  useGetConfigTemplateQuery,
  useLazyGetConfigTemplateQuery,
  useSetConfigTemplateValueMutation,
  useDeleteConfigTemplatePathMutation,

  // CONFIG SCHEMAS
  useGetConfigSchemaQuery,

  // CONFIG AUDIT LOGS
  useGetConfigAuditLogQuery,
  useLazyGetConfigAuditLogQuery,

  // CUSTOMERS
  useListCustomersQuery,
  useLazyListCustomersQuery,
  useGetCustomerQuery,
  useLazyGetCustomerQuery,
  useCreateCustomerMutation,
  useUpdateCustomerMutation,

  // REPORTS
  useListReportsQuery,
  useLazyListReportsQuery,
  useGetReportQuery,
  useLazyGetReportQuery,
  useCreateReportMutation,
  useDeleteReportMutation,
  useUpdateReportMutation,
  useListReportInstancesQuery,
  useLazyListReportInstancesQuery,
  useGetReportInstanceQuery,
  useLazyGetReportInstanceQuery,
  useCreateReportInstanceMutation,
  useUpdateReportInstanceMutation,
  useDeleteReportInstanceMutation,

  // METRICS
  useLazyGetDateMetricsQuery,
  useLazyGetSpatialQuery,

  // ROBOTS
  useListRobotsQuery,
  useLazyListRobotsQuery,
  useGetRobotQuery,
  useLazyGetRobotQuery,
  useGetRobotHistoryQuery,
  useLazyGetRobotHistoryQuery,
  useGetRobotBlocksQuery,
  useLazyGetRobotBlocksQuery,
  useGetRobotMetricsQuery,
  useLazyGetRobotMetricsQuery,
  useCreateRobotMutation,
  useUpdateRobotMutation,
  useAssignRobotMutation,
  useGetRobotProxyMutation,
  useListRobotCropsQuery,
  useLazyListRobotCropsQuery,
  useListRobotJobsQuery,
  useLazyListRobotJobsQuery,
  useGetJobQuery,
  useLazyGetJobQuery,
  useGetJobHistoryQuery,
  useLazyGetJobHistoryQuery,
  useGetJobBlocksQuery,
  useLazyGetJobBlocksQuery,
  useGetRobotHardwareQuery,
  useLazyGetRobotHardwareQuery,

  // VESELKA
  useListUploadsQuery,
  useLazyListUploadsQuery,
  useListCropsQuery,
  useLazyListCropsQuery,
  useGetCropQuery,
  useLazyGetCropQuery,
  useGetModelQuery,
  useLazyGetModelQuery,
  useLazyListPredictionPointsQuery,
  useCreateCategoryCollectionSessionMutation,
  useGetCategoryCollectionSessionQuery,

  // ADMIN
  useDeleteCachesMutation,
  useGetGlobalAlarmListsQuery,
  useLazyGetGlobalAlarmListsQuery,
  useGetRobotAlarmListsQuery,
  useLazyGetRobotAlarmListsQuery,
  useSetGlobalAlarmListsMutation,
  useSetRobotAlarmListsMutation,

  // ALMANAC
  useListAlmanacsForRobotQuery,
  useLazyListAlmanacsForRobotQuery,
  useGetAlmanacQuery,
  useLazyGetAlmanacQuery,
  useDeleteAlmanacMutation,
  useSetAlmanacMutation,
  useListGlobalAlmanacsQuery,
  useLazyListGlobalAlmanacsQuery,
  useGetGlobalAlmanacQuery,
  useLazyGetGlobalAlmanacQuery,
  useDeleteGlobalAlmanacMutation,
  useSetGlobalAlmanacMutation,

  // DISCRIMINATOR
  useListDiscriminatorsForRobotQuery,
  useLazyListDiscriminatorsForRobotQuery,
  useGetDiscriminatorQuery,
  useLazyGetDiscriminatorQuery,
  useDeleteDiscriminatorMutation,
  useSetDiscriminatorMutation,

  // MODELINATOR
  useListModelinatorsForRobotAndCropQuery,
  useLazyListModelinatorsForRobotAndCropQuery,
  useGetModelinatorQuery,
  useLazyGetModelinatorQuery,
  useSetModelinatorMutation,

  // TARGET VELOCITY ESTIMATOR
  useDeleteGlobalTargetVelocityEstimatorMutation,
  useDeleteTargetVelocityEstimatorMutation,
  useGetGlobalTargetVelocityEstimatorQuery,
  useLazyGetGlobalTargetVelocityEstimatorQuery,
  useGetTargetVelocityEstimatorQuery,
  useLazyGetTargetVelocityEstimatorQuery,
  useListGlobalTargetVelocityEstimatorsQuery,
  useLazyListGlobalTargetVelocityEstimatorsQuery,
  useListTargetVelocityEstimatorsQuery,
  useLazyListTargetVelocityEstimatorsQuery,
  useSetGlobalTargetVelocityEstimatorMutation,
  useSetTargetVelocityEstimatorMutation,

  // CATEGORY COLLECTION & CATEGORY PROFILES
  useListGlobalCategoryCollectionProfilesQuery,
  useListCustomerCategoryCollectionProfilesQuery,
  useGetGlobalCategoryCollectionProfileQuery,
  useGetCustomerCategoryCollectionProfileQuery,
  useSetGlobalCategoryCollectionProfileMutation,
  useSetCustomerCategoryCollectionProfileMutation,
  useDeleteGlobalCategoryCollectionProfileMutation,
  useDeleteCustomerCategoryCollectionProfileMutation,

  // GLOBALS
  useGetGlobalsQuery,
  useSetGlobalsMutation,
} = portalApi;
