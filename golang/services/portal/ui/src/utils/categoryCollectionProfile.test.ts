import {
  categoryCollectionProfileImageToggle,
  getThumbnailPaddingPx,
  imagesToCategoryIds,
} from "./categoryCollectionProfile";
import { UnsavedExpandedCategoryCollection } from "protos/portal/category_profile";

const getMockProfile = (): UnsavedExpandedCategoryCollection => ({
  profile: {
    id: "id",
    name: "",
    protected: true,
    categoryIds: [],
  },
  categories: [
    {
      id: "category1",
      chipIds: ["chip1"],
      protected: true,
      name: "Category 1",
    },
    {
      id: "category2",
      chipIds: ["chip2"],
      protected: true,
      name: "Category 2",
    },
  ],
});

describe("categoryCollectionProfile", () => {
  describe("imagesToCategoryIds", () => {
    test("returns empty when profile is undefined", () => {
      const result = imagesToCategoryIds(undefined);
      expect(result).toEqual({});
    });
    test("maps images to category ids", () => {
      const categoryId1 = "category1";
      const pointId1 = "point1";
      const pointId2 = "point2";
      const profile: UnsavedExpandedCategoryCollection = {
        profile: {
          id: "id",
          name: "",
          protected: true,
          categoryIds: [],
        },
        categories: [
          {
            id: categoryId1,
            chipIds: [pointId1, pointId2],
            protected: true,
            name: "Category 1",
          },
        ],
      };
      const result = imagesToCategoryIds(profile);
      expect(result).toEqual({
        [pointId1]: categoryId1,
        [pointId2]: categoryId1,
      });
    });
  });

  describe("categoryCollectionProfileImageToggle", () => {
    test("should add chipId to active category", () => {
      const profile = getMockProfile();
      const result = categoryCollectionProfileImageToggle(
        profile,
        "category1",
        "chip3"
      );
      // active category
      expect(result.categories[0]?.chipIds).toEqual(["chip1", "chip3"]);
      // non active category
      expect(result.categories[1]?.chipIds).toEqual(["chip2"]);
    });

    test("should remove chipId from active category", () => {
      const profile = getMockProfile();
      const result = categoryCollectionProfileImageToggle(
        profile,
        "category1",
        "chip1"
      );
      // active category
      expect(result.categories[0]?.chipIds).toEqual([]);
      // non active category
      expect(result.categories[1]?.chipIds).toEqual(["chip2"]);
    });

    test("should return profile unmodified if activeCategoryId is not provided", () => {
      const profile = getMockProfile();
      const result = categoryCollectionProfileImageToggle(
        profile,
        undefined,
        "chip1"
      );
      expect(result).toEqual(profile);
    });

    test("should return profile unmodified if chipId is not provided", () => {
      const profile = getMockProfile();
      // active category
      const result = categoryCollectionProfileImageToggle(
        profile,
        "category1",
        ""
      );
      // non active category
      expect(result).toEqual(profile);
    });

    test("should remove chipId from non-active categories", () => {
      const profile = getMockProfile();
      const result = categoryCollectionProfileImageToggle(
        profile,
        "category1",
        "chip2"
      );
      // active category
      expect(result.categories[0]?.chipIds).toEqual(["chip1", "chip2"]);
      // non active category
      expect(result.categories[1]?.chipIds).toEqual([]);
    });
  });
  describe("getThumbnailPaddingPx", () => {
    test("small", () => {
      const radius = 5;
      const minRadius = 30;
      const minDimensionsPx = 100;
      const result = getThumbnailPaddingPx(radius, minRadius, minDimensionsPx);
      expect(result).toEqual(45);
    });
    test("large", () => {
      const radius = 100;
      const minRadius = 10;
      const minDimensionsPx = 50;
      const largeImagePaddingFactor = 0.1;
      const result = getThumbnailPaddingPx(
        radius,
        minRadius,
        minDimensionsPx,
        largeImagePaddingFactor
      );
      // 10 is 10% of 100
      expect(result).toEqual(10);
    });
  });
});
