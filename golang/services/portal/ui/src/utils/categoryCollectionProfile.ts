import {
  Category,
  CategoryCollection,
} from "protos/category_profile/category_profile";
import { convert, ConvertUnits, MeasurementSystem } from "./units/units";
import { CropThumbnailPlacement } from "portal/state/imageServiceApi";
import { DateTime } from "luxon";
import {
  formatMeasurement,
  FormattedMeasurement,
} from "portal/components/measurement/formatters";
import { i18n, TFunction } from "i18next";
import { PredictionPoint } from "protos/veselka/prediction_point";
import {
  SavedExpandedCategoryCollection,
  UnsavedExpandedCategoryCollection,
} from "protos/portal/category_profile";
import { titleCase } from "./strings";
import { v4 as uuid } from "uuid";

export const DEFAULT_AVERAGE_PPCM = 78.740_16; // 200px/in
export const DEFAULT_CHIP_UPLOAD_SIZE = 600;
export interface PaginationParameters {
  page: number;
  pageSize: number;
}

// for M1, the robot will be able to match categories to the previous static ones specifically by matching the English name.
export const getDefaultCategoryNames = (
  t: TFunction,
  i18n: boolean = true
): string[] => {
  return i18n
    ? [
        t("models.weeds.categories.broadleaf"),
        t("models.categoryCollectionProfiles.fields.categories.disregard"),
        t("models.weeds.categories.grass"),
        t("models.weeds.categories.offshoot"),
        t("models.weeds.categories.purslane"),
        t("models.crops.crop_one"),
      ].map((categoryName) => titleCase(categoryName))
    : ["Broadleaf", "Disregard", "Grass", "Offshoot", "Purslane", "Crop"];
};

// helps enforce that specifically named categories exist
export const hasStrictCategories = (
  values: { name: string }[],
  requiredCategories: string[]
): boolean => {
  const actual = new Set(values.map((v) => v.name));
  return requiredCategories.every((name) => actual.has(name));
};

export interface DefaultCategoryCollectionProfile {
  profile: CategoryCollection;
  categories: Category[];
}
export const getDefaultCategoryCollectionProfile = (
  t: TFunction,
  i18n: boolean = true,
  isProtected: boolean = false
): DefaultCategoryCollectionProfile => {
  const categories = getDefaultCategoryNames(t, i18n).map((name) =>
    Category.fromPartial({
      id: uuid(),
      name,
      chipIds: [],
      protected: isProtected,
    })
  );
  const profile = CategoryCollection.fromPartial({
    id: uuid(),
    name: "",
    protected: isProtected,
    categoryIds: categories.map(({ id }) => id),
  });
  return {
    profile,
    categories,
  };
};

export const expandedCollectionHasProfile = (
  profile: UnsavedExpandedCategoryCollection | undefined
): profile is DefaultCategoryCollectionProfile => {
  return Boolean(profile && profile.profile);
};

export type CategorizationMap = Record<string, string>; // point id to category id

/** Creates a convenience lookup for which images are categorized */
export const imagesToCategoryIds = (
  profile: UnsavedExpandedCategoryCollection | undefined
): CategorizationMap => {
  const categoryMap: CategorizationMap = {};
  if (!profile) {
    return categoryMap;
  }
  for (const { id: categoryId, chipIds } of profile.categories) {
    for (const pointId of chipIds) {
      categoryMap[pointId] = categoryId;
    }
  }
  return categoryMap;
};

export const categoryCollectionProfileImageToggle = (
  profile: UnsavedExpandedCategoryCollection,
  activeCategoryId: string | undefined,
  pointId: string
): UnsavedExpandedCategoryCollection => {
  if (!activeCategoryId || !pointId) {
    return profile;
  }

  const updatedCategories = profile.categories.map((category) => {
    const isActiveCategory = category.id === activeCategoryId;

    const updatedChipIds = [...category.chipIds];
    if (pointId) {
      const existingIndex = category.chipIds.indexOf(pointId);
      if (existingIndex > -1) {
        // remove the chip id
        updatedChipIds.splice(existingIndex, 1);
      } else if (isActiveCategory) {
        // add the chip id to the active category
        updatedChipIds.push(pointId);
      }
    }

    return Category.fromJSON({
      ...category,
      chipIds: [...new Set(updatedChipIds)],
    });
  });
  return {
    ...profile,
    categories: updatedCategories,
  };
};

const DEFAULT_PADDING_FACTOR = 0.1;
const RADIUS_CUTOFF_PX = 100;
const MINIMUM_DIM_PX = RADIUS_CUTOFF_PX * 2;

export const getThumbnailPaddingPx = (
  radius: number,
  radiusCutoff: number = RADIUS_CUTOFF_PX,
  minDimensionsPx: number = MINIMUM_DIM_PX,
  largeImagePaddingFactor: number = DEFAULT_PADDING_FACTOR
): number => {
  return radius < radiusCutoff
    ? Math.ceil(minDimensionsPx / 2 - radius)
    : Math.ceil(radius * largeImagePaddingFactor);
};

const derivePaddingFactor = (
  thumbnailWidth: number,
  paddingPx: number
): number => (thumbnailWidth ? paddingPx / (thumbnailWidth / 2) : 0);

/**
 * Given a prediction point, create a cropping that shows as much as the plant
 * as possible while also balancing image clarity
 */
export const getPlantAwareCropping = ({
  x,
  y,
  radius,
}: Pick<PredictionPoint, "x" | "y" | "radius">): CropThumbnailPlacement & {
  paddingFactor: number;
} => {
  const paddingPx = getThumbnailPaddingPx(radius);
  const imageCropDimensions = Math.ceil((radius + paddingPx) * 2);
  const paddingFactor = derivePaddingFactor(imageCropDimensions, paddingPx);
  const topLeftCoord =
    radius < RADIUS_CUTOFF_PX
      ? {
          x: Math.ceil(x - MINIMUM_DIM_PX / 2),
          y: Math.ceil(y - MINIMUM_DIM_PX / 2),
        }
      : {
          x: Math.ceil(x - (radius + paddingPx)),
          y: Math.ceil(y - (radius + paddingPx)),
        };
  return {
    ...topLeftCoord,
    width: imageCropDimensions,
    height: imageCropDimensions,
    paddingFactor,
  };
};

export const getCropping = (
  x: number,
  y: number,
  dimension = 800
): CropThumbnailPlacement => {
  const topLeftCoord = {
    x: Math.ceil(x - dimension / 2),
    y: Math.ceil(y - dimension / 2),
  };
  return {
    ...topLeftCoord,
    width: dimension,
    height: dimension,
  };
};

export interface CategoryOption extends Category {
  profile: string;
}

export const createCategoryOptions = (
  t: TFunction,

  availableProfiles: SavedExpandedCategoryCollection[]
): CategoryOption[] => {
  const options: CategoryOption[] = [];
  for (const { categories, profile: categoryCollection } of availableProfiles) {
    if (!categoryCollection) {
      continue;
    }
    const categoryCollectionName =
      categoryCollection.profile?.name ?? t("utils.descriptors.unknown");
    for (const { profile: category } of categories) {
      if (!category) {
        continue;
      }
      options.push({
        ...category,
        profile: categoryCollectionName,
      });
    }
  }
  return options;
};

// used to differentiate input values of plain text string vs a fully fledged Category object
export const isCategoryNameOnly = (value: Category | string): value is string =>
  typeof value === "string";

export const getCategoryOptionLabel = (
  t: TFunction,
  { name, chipIds }: CategoryOption
): string => {
  const chipInfo =
    chipIds.length > 0
      ? ` (${chipIds.length} ${t("models.images.image", {
          count: chipIds.length,
        })})`
      : "";
  return `${name}${chipInfo}`;
};

export const radiusPixelsToDiameterUnits = (
  radiusPixels: number,
  ppcm: number,
  units: ConvertUnits
): number => {
  const diameterPixels = radiusPixels * 2;
  const diameterCm = diameterPixels / ppcm;
  return convert(diameterCm).from("cm").to(units);
};

export const diameterUnitsToRadiusPixels = (
  diameterUnits: number,
  ppcm: number,
  units: ConvertUnits
): number => {
  const diameterCm = convert(diameterUnits).from(units).to("cm");
  const diameterPixels = diameterCm * ppcm;
  return diameterPixels / 2;
};

// users want to consistently see 'mm' or 'in'
export const getDiameterFilterUnits = (
  measurementSystem: MeasurementSystem
): ConvertUnits =>
  measurementSystem === MeasurementSystem.imperial ? "in" : "mm";

export const formatRadiusToDiameter = (
  t: TFunction,
  i18n: i18n,
  measurementSystem: MeasurementSystem,
  radiusCm: number
): FormattedMeasurement =>
  formatMeasurement(t, i18n, measurementSystem, radiusCm * 2, "cm", {
    decimalPlaces: 2,
    toUnits: getDiameterFilterUnits(measurementSystem),
  });

export const userFacingDate = (i18n: i18n, date: number): string =>
  DateTime.fromMillis(date).toLocaleString(
    {
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    },
    { locale: i18n.language }
  );
