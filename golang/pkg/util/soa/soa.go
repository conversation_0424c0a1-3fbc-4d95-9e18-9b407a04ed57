// Package soa implements a Structure-of-Arrays transform.
//
// This makes it convenient to convert a slice of values into a collection of
// correlated slices of individual fields, each representing one "column". For
// example, if you have a slice of N users, you can use [Collect] to extract a
// slice of N user IDs, a slice of N user names, and a slice of N user emails.
// Those slices can be conveniently passed as array parameters to SQL queries.
//
// See: https://en.wikipedia.org/wiki/AoS_and_SoA
//
// See: https://github.com/jackc/pgx/discussions/2359
package soa

// Collect converts a slice of items into a slice of columns. Provide a
// callback that returns a list of fields of interest for a given item.
// Collect will return a slice of columns, each of length len(items), where
// columns[i][j] is the ith field of items[j]. Fields can be constructed by
// calling `V`.
//
// Across invocations of the callback, it should always return the same number
// of fields, and the field at any given index should always have the same
// static type parameter. Collect will panic if this is not the case.
//
// If the field at index i had type parameter E, then result[i] will have
// runtime type []E. For example, if `project(item)` returns a slice whose
// first element is `V(item.SomeString)`, then result[0] will be a []string
// that has the values of the SomeString field for each item.
//
// If items is empty, then columns will be nil and ok will be false, because
// there is no way invoke the callback to learn what the columns should be.
func Collect[T any](items []T, project func(item T) Fields) (columns []any, ok bool) {
	if len(items) == 0 {
		return nil, false
	}

	wr := &writer{firstPass: true, itemsLen: len(items)}
	fields := project(items[0])
	wr.sinks = make([]any, len(fields))
	for _, f := range fields {
		f.emit(wr)
	}
	wr.firstPass = false
	wr.itemIndex++

	for _, item := range items[1:] {
		wr.sinkIndex = 0
		fields = project(item)
		if len(fields) != len(wr.sinks) {
			panic(reasonFieldCount)
		}
		for _, f := range project(item) {
			f.emit(wr)
		}
		wr.itemIndex++
	}

	return wr.sinks, true
}

type field[E any] struct {
	value E
}

// V bundles a field with its static type parameter. V should be called by the
// callback passed to Collect.
func V[E any](value E) Field {
	return field[E]{value: value}
}

type writer struct {
	sinks     []any
	sinkIndex int
	firstPass bool
	itemsLen  int
	itemIndex int
}

func (f field[E]) emit(wr *writer) {
	var sink []E
	if wr.firstPass {
		sink = make([]E, wr.itemsLen)
		wr.sinks[wr.sinkIndex] = sink
	} else {
		var ok bool
		sink, ok = wr.sinks[wr.sinkIndex].([]E)
		if !ok {
			panic(reasonFieldTypes)
		}
	}
	sink[wr.itemIndex] = f.value
	wr.sinkIndex++
}

// A Field is returned by V. See Collect for more details.
type Field interface {
	emit(wr *writer)
}

type Fields []Field

type panicReason string

const (
	reasonFieldCount panicReason = "soa: inconsistent number of fields"
	reasonFieldTypes             = "soa: inconsistent type parameter for field"
)
