package pgxmon

import (
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/prometheus/client_golang/prometheus"
)

type PoolMetrics struct {
	pool *pgxpool.Pool

	idleConns                        *prometheus.Desc
	acquiredConns                    *prometheus.Desc
	constructingConns                *prometheus.Desc
	maxConns                         *prometheus.Desc
	acquiresTotal                    *prometheus.Desc
	canceledAcquiresTotal            *prometheus.Desc
	emptyAcquiresTotal               *prometheus.Desc
	acquireDurationSecondsTotal      *prometheus.Desc
	emptyAcquireWaitTimeSecondsTotal *prometheus.Desc
}

// NewPoolMetrics returns a Prometheus collector that captures database
// connection pool statistics:
// https://pkg.go.dev/github.com/jackc/pgx/v5/pgxpool#Stat
func NewPoolMetrics(pool *pgxpool.Pool) *PoolMetrics {
	makeDesc := func(name string, help string) *prometheus.Desc {
		fqName := "pgxpool_" + name
		return prometheus.NewDesc(fqName, help, nil, nil) // no labels
	}

	return &PoolMetrics{
		pool: pool,

		idleConns:                        makeDesc("idle_conns", "Number of currently idle database connections in the pool"),
		acquiredConns:                    makeDesc("acquired_conns", "Number of currently acquired database connections in the pool"),
		constructingConns:                makeDesc("constructing_conns", "Number of database connections with construction in progress in the pool"),
		maxConns:                         makeDesc("max_conns", "Maximum size of the database connection pool"),
		acquiresTotal:                    makeDesc("acquires_total", "Cumulative count of successful acquires from the database connection pool"),
		canceledAcquiresTotal:            makeDesc("canceled_acquires_total", "Cumulative count of acquires from the database connection pool that were canceled by a context"),
		emptyAcquiresTotal:               makeDesc("empty_acquires_total", "Cumulative count of successful acquires from the database connection pool that waited for a resource to be released or constructed because the pool was empty"),
		acquireDurationSecondsTotal:      makeDesc("acquire_duration_seconds_total", "Total duration of all successful acquires from the database connection pool"),
		emptyAcquireWaitTimeSecondsTotal: makeDesc("empty_acquire_wait_time_seconds_total", "Cumulative time waited for successful acquires from the database connection pool for a resource to be released or constructed because the pool was empty"),
	}
}

// Describe implements prometheus.Collector.
func (pm *PoolMetrics) Describe(ch chan<- *prometheus.Desc) {
	ch <- pm.idleConns
	ch <- pm.acquiredConns
	ch <- pm.constructingConns
	ch <- pm.maxConns
	ch <- pm.acquiresTotal
	ch <- pm.canceledAcquiresTotal
	ch <- pm.emptyAcquiresTotal
	ch <- pm.acquireDurationSecondsTotal
	ch <- pm.emptyAcquireWaitTimeSecondsTotal
}

// Collect implements prometheus.Collector.
func (pm *PoolMetrics) Collect(ch chan<- prometheus.Metric) {
	stat := pm.pool.Stat()

	ch <- prometheus.MustNewConstMetric(pm.idleConns, prometheus.GaugeValue, float64(stat.IdleConns()))
	ch <- prometheus.MustNewConstMetric(pm.acquiredConns, prometheus.GaugeValue, float64(stat.AcquiredConns()))
	ch <- prometheus.MustNewConstMetric(pm.constructingConns, prometheus.GaugeValue, float64(stat.ConstructingConns()))
	ch <- prometheus.MustNewConstMetric(pm.maxConns, prometheus.GaugeValue, float64(stat.MaxConns()))

	ch <- prometheus.MustNewConstMetric(pm.acquiresTotal, prometheus.GaugeValue, float64(stat.AcquireCount()))
	ch <- prometheus.MustNewConstMetric(pm.canceledAcquiresTotal, prometheus.GaugeValue, float64(stat.CanceledAcquireCount()))
	ch <- prometheus.MustNewConstMetric(pm.emptyAcquiresTotal, prometheus.GaugeValue, float64(stat.EmptyAcquireCount()))

	ch <- prometheus.MustNewConstMetric(pm.acquireDurationSecondsTotal, prometheus.GaugeValue, float64(stat.AcquireDuration().Seconds()))
	ch <- prometheus.MustNewConstMetric(pm.emptyAcquireWaitTimeSecondsTotal, prometheus.GaugeValue, float64(stat.EmptyAcquireWaitTime().Seconds()))
}

var _ prometheus.Collector = &PoolMetrics{}
