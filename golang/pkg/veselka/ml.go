package veselka

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/carbonrobotics/protos/golang/generated/proto/veselka/prediction_point"

	"github.com/carbonrobotics/cloud/golang/pkg/carbon/models"
)

type ListPipelinesParameters struct {
	Enabled bool `url:"enabled,omitempty"`
}

func (c *Client) ListPipelines(ctx context.Context, parameters ListPipelinesParameters) ([]*models.Pipeline, error) {
	targetPath := c.buildPath(mlPrefix, "pipelines")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		pipelineSlice := make([]*models.Pipeline, 0)
		if err = json.NewDecoder(resp.Body).Decode(&pipelineSlice); err != nil {
			return nil, err
		}
		return pipelineSlice, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type ListModelStubsParameters struct{}

func (c *Client) ListModelStubs(ctx context.Context, parameters ListModelStubsParameters) ([]*models.Model, error) {
	if c.internal {
		return nil, fmt.Errorf("route: %s, error: %w", "internal/models/stubs", ErrInvalidRoute)
	}
	targetPath := c.buildPath(mlPrefix, "models", "stubs")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		modelSlice := make([]*models.Model, 0)
		if err = json.NewDecoder(resp.Body).Decode(&modelSlice); err != nil {
			return nil, err
		}
		return modelSlice, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type ExportModelsParameters struct{}

func (c *Client) ExportModels(ctx context.Context, parameters ExportModelsParameters) ([]*models.Model, error) {
	targetPath := c.buildPath(mlPrefix, "models", "export")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		modelSlice := make([]*models.Model, 0)
		if err = json.NewDecoder(resp.Body).Decode(&modelSlice); err != nil {
			return nil, err
		}
		return modelSlice, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type GetModelParentVersionParameters struct {
	SubType     string `url:"sub_type,omitempty"`
	Environment string `url:"environment,omitempty"`
	Target      string `url:"target,omitempty"`
	Crop        string `url:"crop,omitempty"`
	Pipeline    string `url:"pipeline,omitempty"`
}

func (c *Client) GetModelParentVersion(ctx context.Context, parameters GetModelParentVersionParameters) (map[string]string, error) {
	targetPath := c.buildPath(mlPrefix, "modelsvc", "parent_versions")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		versionMap := make(map[string]string)
		if err = json.NewDecoder(resp.Body).Decode(&versionMap); err != nil {
			return nil, err
		}
		return versionMap, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type ExportLabelsParameters struct {
	ImageURL string `url:"imageUrl,omitempty"`
	Location string `url:"location,omitempty"`

	Option string `url:"-"` // done, all, admin, review, labeling
}

func (c *Client) ExportLabels(ctx context.Context, parameters ExportLabelsParameters) ([]*models.Label, error) {
	if c.internal {
		return nil, fmt.Errorf("route: %s, error: %w", "internal/labels/export", ErrInvalidRoute)
	}
	targetPath := c.buildPath(mlPrefix, "labels", "export", parameters.Option)
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		labelSlice := make([]*models.Label, 0)
		if err = json.NewDecoder(resp.Body).Decode(&labelSlice); err != nil {
			return nil, err
		}
		return labelSlice, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type GetModelParameters struct {
	ID string `url:"-"`
}

func (c *Client) GetModel(ctx context.Context, parameters GetModelParameters) (*models.Model, error) {
	if c.internal {
		return nil, fmt.Errorf("route: %s, error: %w", "internal/model", ErrInvalidRoute)
	}
	targetPath := c.buildPath(mlPrefix, "model", parameters.ID)
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		m := new(models.Model)
		if err = json.NewDecoder(resp.Body).Decode(&m); err != nil {
			return nil, err
		}
		return m, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type CreatePopulateModelParameters struct{}

func (c *Client) CreatePopulateModel(ctx context.Context, parameters CreatePopulateModelParameters, model *models.Model) error {
	targetPath := c.buildPath(mlPrefix, "model")
	body, err := json.Marshal(model)
	if err != nil {
		return err
	}
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodPost, targetPath, parameters, bytes.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		return nil
	}
	return fmt.Errorf("error response: %s", resp.Status)
}

type CreateEvaluationParameters struct{}

func (c *Client) CreateEvaluation(ctx context.Context, parameters CreateEvaluationParameters, evaluation *models.Evaluation) error {
	targetPath := c.buildPath(mlPrefix, "evaluation")
	body, err := json.Marshal(evaluation)
	if err != nil {
		return err
	}
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodPost, targetPath, parameters, bytes.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		return nil
	}
	return fmt.Errorf("error response: %s", resp.Status)
}

type GetEvaluationParameters struct {
	EvaluationID string `url:"evaluation_id,omitempty"`
	ModelID      string `url:"model_id,omitempty"`
}

func (c *Client) GetEvaluation(ctx context.Context, parameters GetEvaluationParameters) (*models.Evaluation, error) {
	targetPath := c.buildPath(mlPrefix, "evaluation")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		eval := new(models.Evaluation)
		if err = json.NewDecoder(resp.Body).Decode(&eval); err != nil {
			return nil, err
		}
		return eval, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type ListDatasetParameters struct{}

func (c *Client) ListDatasets(ctx context.Context, parameters ListDatasetParameters) ([]*models.Dataset, error) {
	targetPath := c.buildPath(mlPrefix, "datasets")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		ds := make([]*models.Dataset, 0)
		if err = json.NewDecoder(resp.Body).Decode(&ds); err != nil {
			return nil, err
		}
		return ds, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type GetDatasetParameters struct {
	ID string `url:"id,omitempty"`
}

func (c *Client) GetDataset(ctx context.Context, parameters GetDatasetParameters) (*models.Dataset, error) {
	targetPath := c.buildPath(mlPrefix, "dataset")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		ds := new(models.Dataset)
		if err = json.NewDecoder(resp.Body).Decode(&ds); err != nil {
			return nil, err
		}
		return ds, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type CreateDatasetParameters struct {
	StartTimestamp int64    `json:"start_timestamp,omitempty" url:"-"`
	StopTimestamp  int64    `json:"stop_timestamp,omitempty" url:"-"`
	CropIDs        []string `json:"crop_ids,omitempty" url:"-"`
	Crops          []string `json:"crops,omitempty" url:"-"`
	Robots         []string `json:"robots,omitempty" url:"-"`
	Name           string   `json:"name,omitempty" url:"-"`
	ParentID       string   `json:"parent_id,omitempty" url:"-"`
	FastRun        bool     `json:"fast_run,omitempty" url:"-"`
	CreateAsync    bool     `json:"create_async,omitempty" url:"-"`
}

func (c *Client) CreateDataset(ctx context.Context, parameters CreateDatasetParameters) (*models.Dataset, error) {
	targetPath := c.buildPath(mlPrefix, "dataset")
	body, err := json.Marshal(parameters)
	if err != nil {
		return nil, err
	}
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodPost, targetPath, parameters, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		ds := new(models.Dataset)
		if err = json.NewDecoder(resp.Body).Decode(&ds); err != nil {
			return nil, err
		}
		return ds, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type GetPipelineParameters struct {
	ID string `url:"id,omitempty"`
}

func (c *Client) GetPipeline(ctx context.Context, parameters GetPipelineParameters) (*models.Pipeline, error) {
	targetPath := c.buildPath(mlPrefix, "pipeline")
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodGet, targetPath, parameters, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		pipeline := new(models.Pipeline)
		if err = json.NewDecoder(resp.Body).Decode(&pipeline); err != nil {
			return nil, err
		}
		return pipeline, nil
	}
	return nil, fmt.Errorf("error response: %s", resp.Status)
}

type CreatePipelineParameters struct {
	CropIDs []string `json:"crop_ids" url:"-"`
}

func (c *Client) CreatePipeline(ctx context.Context, parameters CreatePipelineParameters, pipeline *models.Pipeline) error {
	targetPath := c.buildPath(mlPrefix, "pipeline")
	body, err := json.Marshal(struct {
		CropIDs []string `json:"crop_ids"`
		*models.Pipeline
	}{
		CropIDs:  parameters.CropIDs,
		Pipeline: pipeline,
	})
	if err != nil {
		return err
	}
	resp, err := c.makeRequest(ctx, c.mlHttpClient, http.MethodPost, targetPath, parameters, bytes.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		return nil
	}
	return fmt.Errorf("error response: %s", resp.Status)
}

func formatRange[T any](values [2]T, formatter func(T) string) string {
	start, end := formatter(values[0]), formatter(values[1])

	if start == "" && end == "" {
		return ""
	}
	return start + "," + end
}

func formatDateRange(dates [2]time.Time) string {
	formatTime := func(t time.Time) string {
		if t.IsZero() {
			return ""
		}
		return t.Format(time.RFC3339)
	}
	return formatRange(dates, formatTime)
}

func formatFloatRange(floatRange [2]*float64) string {
	formatFloat := func(f *float64) string {
		if f == nil {
			return ""
		}
		return strconv.FormatFloat(*f, 'f', -1, 64)
	}
	return formatRange(floatRange, formatFloat)
}

func formatBoolean(b *bool) string {
	if b == nil {
		return ""
	}
	return strconv.FormatBool(*b)
}

type ListPredictionPointsParameters struct {
	Page          int
	PageSize      int
	SortBy        string
	SortDirection string

	CapturedAt         [2]time.Time
	Radius             [2]*float64
	Crops              []string
	Robots             []string
	IDs                []string
	SessionID          string
	CategoryIDs        []string
	UploadedByOperator *bool
}

func (c *Client) ListPredictionPoints(ctx context.Context, parameters ListPredictionPointsParameters) (*prediction_point.ListPredictionPointsResponse, error) {
	var formattedQueryParams = struct {
		Page          int    `url:"pg_page,omitempty"`
		PageSize      int    `url:"pg_page_size,omitempty"`
		SortBy        string `url:"pg_sort_by,omitempty"`
		SortDirection string `url:"pg_sort_direction,omitempty"`

		CapturedAt         string `url:"imgf_captured_at,omitempty"`
		Radius             string `url:"ppf_radius_range,omitempty"`
		Crops              string `url:"imgf_crop_ids,omitempty"`
		Robots             string `url:"imgf_robot_ids,omitempty"`
		IDs                string `url:"ppf_ids,omitempty"`
		SessionID          string `url:"classf_session_id,omitempty"`
		CategoryIDs        string `url:"classf_category_ids,omitempty"`
		UploadedByOperator string `url:"imgf_uploaded_by_operator,omitempty"`
	}{
		Page:               parameters.Page,
		PageSize:           parameters.PageSize,
		SortBy:             parameters.SortBy,
		SortDirection:      parameters.SortDirection,
		CapturedAt:         formatDateRange(parameters.CapturedAt),
		Radius:             formatFloatRange(parameters.Radius),
		Crops:              strings.Join(parameters.Crops, ","),
		Robots:             strings.Join(parameters.Robots, ","),
		IDs:                strings.Join(parameters.IDs, ","),
		SessionID:          parameters.SessionID,
		CategoryIDs:        strings.Join(parameters.CategoryIDs, ","),
		UploadedByOperator: formatBoolean(parameters.UploadedByOperator),
	}

	targetPath := c.buildPath("prediction-points")
	resp, err := c.makeRequest(ctx, c.robotHttpClient, http.MethodGet, targetPath, formattedQueryParams, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response: %s", resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	listPredictionPointsResponse := &prediction_point.ListPredictionPointsResponse{}
	err = protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}.Unmarshal(bodyBytes, listPredictionPointsResponse)
	if err != nil {
		return nil, err
	}

	return listPredictionPointsResponse, nil
}

func (c *Client) CreatePredictionPointSession(ctx context.Context, request *prediction_point.CreatePredictionPointSessionRequest) (*prediction_point.CreatePredictionPointSessionResponse, error) {
	requestBody, err := protojson.MarshalOptions{
		EmitUnpopulated: true, // so we don't omit required fields with legitimately empty values
		UseProtoNames:   true, // so we properly snake_case the request as veselka expects
	}.Marshal(request)
	if err != nil {
		return nil, err
	}

	targetPath := c.buildPath("experiment", "predict-points-classification", "session")

	resp, err := c.makeRequest(ctx, c.robotHttpClient, http.MethodPost, targetPath, nil, bytes.NewReader(requestBody))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response: %s", resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	session := &prediction_point.CreatePredictionPointSessionResponse{}
	err = protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}.Unmarshal(bodyBytes, session)
	if err != nil {
		return nil, err
	}
	return session, nil
}

func (c *Client) GetPredictionPointSessionStatus(ctx context.Context, sessionId string) (*prediction_point.GetPredictionPointSessionResponse, error) {
	targetPath := c.buildPath("experiment", "predict-points-classification", "session", sessionId)
	resp, err := c.makeRequest(ctx, c.robotHttpClient, http.MethodGet, targetPath, nil, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response: %s", resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	statusResponse := &prediction_point.GetPredictionPointSessionResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(bodyBytes, statusResponse)
	if err != nil {
		return nil, err
	}
	return statusResponse, nil

}
