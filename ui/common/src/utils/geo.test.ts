import { describe, expect, it } from "@jest/globals";
import { makeGeoId } from "./geo";

describe("makeGeoId", () => {
  it("returns a 12-character base62 id", () => {
    const id = makeGeoId();

    expect(id.length).toEqual(12);
    expect(id.match(/[^a-zA-Z0-9]/)).toBeNull();
  });

  it("doesn't return the same thing every time", () => {
    const idA = makeGeoId();
    const idB = makeGeoId();

    expect(idA).not.toEqual(idB);
  });
});
