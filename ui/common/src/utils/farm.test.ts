import { Area, PointDefinition, Zone } from "protos/portal/farm";
import { describe, expect, it } from "@jest/globals";
import { FixType, Point } from "protos/geo/geo";
import {
  reassignIdForPointFromMap,
  referencedPointIdsForArea,
  regenerateIdsForZone,
} from "./farm";

function pointFactory(id: string, name: string): Point {
  return {
    lng: (Math.random() - 0.5) * 360,
    lat: (Math.random() - 0.5) * 180,
    alt: Math.random() * 7000,
    captureInfo: {
      fixType: FixType.RTK_FIXED,
      captureTime: new Date().toISOString(),
    },
    id: { id },
    name,
  };
}

function expectPointDefsIsomorphic(
  beforePointDefs: PointDefinition[],
  beforeIndex: number,
  afterPointDefs: PointDefinition[],
  afterIndex: number
): void {
  expect(afterPointDefs[afterIndex]).toEqual({
    ...beforePointDefs[beforeIndex],
    point: {
      ...beforePointDefs[beforeIndex]?.point,
      id: afterPointDefs[afterIndex]?.point?.id,
    },
  });
}

describe("referencedPointIdsForArea", () => {
  it("works correctly", () => {
    const area: Area = {
      bufferMeters: 3,
      point: pointFactory("a", ""),
      lineString: {
        points: [pointFactory("b", ""), pointFactory("c", "")],
      },
      polygon: {
        boundary: { points: [pointFactory("d", ""), pointFactory("e", "")] },
        holes: [
          { points: [pointFactory("f", ""), pointFactory("g", "")] },
          { points: [pointFactory("h", ""), pointFactory("i", "")] },
        ],
      },
    };

    expect(referencedPointIdsForArea(area)).toEqual(new Set("abcdefghi"));
  });
});

describe("reassignIdForPointFromMap", () => {
  it("gives a resolved-by-reference point a new id but leaves the rest unchanged", () => {
    const pt: Point = {
      lat: 0,
      lng: 0,
      alt: 0,
      captureInfo: {
        fixType: FixType.DIFFERENTIAL_GNSS,
        captureTime: "whenever",
      },
      id: { id: "123" },
      name: "456",
    };
    const idMap = new Map([["123", "789"]]);

    expect(reassignIdForPointFromMap(pt, idMap)).toEqual({
      ...pt,
      id: { id: "789" },
    });
  });

  it("does nothing to a point that isn't resolved by reference", () => {
    const pt: Point = {
      lat: 1,
      lng: 2,
      alt: 3,
      captureInfo: {
        fixType: FixType.DIFFERENTIAL_GNSS,
        captureTime: "whenever",
      },
      id: undefined,
      name: "def",
    };

    expect(reassignIdForPointFromMap(pt, new Map())).toEqual(pt);
  });

  it("throws an error if the point's id is not in idMap", () => {
    const pt: Point = {
      lat: 0,
      lng: 0,
      alt: 0,
      captureInfo: undefined,
      id: { id: "123" },
      name: "",
    };

    expect(() => {
      reassignIdForPointFromMap(pt, new Map());
    }).toThrow();
  });
});

describe("regenerateIdsForZone", () => {
  it("should change the zone ID but leave the other contents unchanged", () => {
    const zone: Zone = {
      id: { id: "a" },
      version: {
        ordinal: 42,
        updateTime: "whenever",
        deleted: false,
        changed: false,
      },
      name: "cool name",
      areas: [],
      contents: {
        field: { plantingHeading: undefined, centerPivot: undefined },
      },
    };

    const { zone: newZone } = regenerateIdsForZone([], zone);
    expect(newZone).toEqual({ ...zone, id: newZone.id });
  });

  it("should reassign point IDs for a zone with a point area", () => {
    const pointDefs: PointDefinition[] = [
      {
        point: pointFactory("1", "abc"),
        version: undefined,
      },
    ];
    const zone: Zone = {
      id: { id: "a" },
      version: undefined,
      name: "",
      areas: [
        {
          bufferMeters: 3,
          point: {
            lat: 0,
            lng: 0,
            alt: 0,
            id: { id: "1" },
            captureInfo: undefined,
            name: "abc",
          },
        },
      ],
      contents: undefined,
    };

    const { pointDefs: outPointDefs, zone: outZone } = regenerateIdsForZone(
      pointDefs,
      zone
    );

    expect(outPointDefs).toHaveLength(1);
    expectPointDefsIsomorphic(pointDefs, 0, outPointDefs, 0);

    expect(outZone).toEqual({
      ...zone,
      id: outZone.id,
      areas: [
        {
          ...zone.areas[0],
          point: {
            ...zone.areas[0]?.point,
            id: outPointDefs[0]?.point?.id,
          },
        },
      ],
    });
  });

  it("should reassign point IDs for a zone with a linestring area", () => {
    const pointDefs: PointDefinition[] = [
      { point: pointFactory("1", "abc"), version: undefined },
      { point: pointFactory("2", "def"), version: undefined },
    ];
    const zone: Zone = {
      id: { id: "a" },
      version: undefined,
      name: "",
      areas: [
        {
          bufferMeters: 3,
          lineString: {
            points: [pointFactory("1", "abc"), pointFactory("2", "def")],
          },
        },
      ],
      contents: undefined,
    };

    const { pointDefs: outPointDefs, zone: outZone } = regenerateIdsForZone(
      pointDefs,
      zone
    );

    expect(outPointDefs).toHaveLength(2);
    const abcIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "abc"
    );
    const defIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "def"
    );
    expectPointDefsIsomorphic(pointDefs, 0, outPointDefs, abcIndexOut);
    expectPointDefsIsomorphic(pointDefs, 1, outPointDefs, defIndexOut);

    expect(outZone).toEqual({
      ...zone,
      id: outZone.id,
      areas: [
        {
          ...zone.areas[0],
          lineString: {
            points: [
              {
                ...zone.areas[0]?.lineString?.points[0],
                id: outPointDefs[abcIndexOut]?.point?.id,
              },
              {
                ...zone.areas[0]?.lineString?.points[1],
                id: outPointDefs[defIndexOut]?.point?.id,
              },
            ],
          },
        },
      ],
    });
  });

  it("should reassign point IDs for a zone with a polygon area", () => {
    const pointDefs: PointDefinition[] = [
      { point: pointFactory("1", "abc"), version: undefined },
      { point: pointFactory("2", "def"), version: undefined },
      { point: pointFactory("3", "ghi"), version: undefined },
      { point: pointFactory("4", "jkl"), version: undefined },
      { point: pointFactory("5", "mno"), version: undefined },
      { point: pointFactory("6", "pqr"), version: undefined },
    ];
    const zone: Zone = {
      id: { id: "a" },
      version: undefined,
      name: "",
      areas: [
        {
          bufferMeters: 3,
          polygon: {
            boundary: {
              points: [pointFactory("1", "abc"), pointFactory("2", "def")],
            },
            holes: [
              {
                points: [pointFactory("3", "ghi"), pointFactory("4", "jkl")],
              },
              {
                points: [pointFactory("5", "mno"), pointFactory("6", "pqr")],
              },
            ],
          },
        },
      ],
      contents: undefined,
    };

    const { pointDefs: outPointDefs, zone: outZone } = regenerateIdsForZone(
      pointDefs,
      zone
    );

    expect(outPointDefs).toHaveLength(6);

    const abcIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "abc"
    );
    const defIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "def"
    );
    const ghiIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "ghi"
    );
    const jklIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "jkl"
    );
    const mnoIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "mno"
    );
    const pqrIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "pqr"
    );

    expectPointDefsIsomorphic(pointDefs, 0, outPointDefs, abcIndexOut);
    expectPointDefsIsomorphic(pointDefs, 1, outPointDefs, defIndexOut);
    expectPointDefsIsomorphic(pointDefs, 2, outPointDefs, ghiIndexOut);
    expectPointDefsIsomorphic(pointDefs, 3, outPointDefs, jklIndexOut);
    expectPointDefsIsomorphic(pointDefs, 4, outPointDefs, mnoIndexOut);
    expectPointDefsIsomorphic(pointDefs, 5, outPointDefs, pqrIndexOut);

    expect(outZone).toEqual({
      ...zone,
      id: outZone.id,
      areas: [
        {
          ...zone.areas[0],
          polygon: {
            boundary: {
              points: [
                {
                  ...zone.areas[0]?.polygon?.boundary?.points[0],
                  id: outPointDefs[abcIndexOut]?.point?.id,
                },
                {
                  ...zone.areas[0]?.polygon?.boundary?.points[1],
                  id: outPointDefs[defIndexOut]?.point?.id,
                },
              ],
            },
            holes: [
              {
                points: [
                  {
                    ...zone.areas[0]?.polygon?.holes[0]?.points[0],
                    id: outPointDefs[ghiIndexOut]?.point?.id,
                  },
                  {
                    ...zone.areas[0]?.polygon?.holes[0]?.points[1],
                    id: outPointDefs[jklIndexOut]?.point?.id,
                  },
                ],
              },
              {
                points: [
                  {
                    ...zone.areas[0]?.polygon?.holes[1]?.points[0],
                    id: outPointDefs[mnoIndexOut]?.point?.id,
                  },
                  {
                    ...zone.areas[0]?.polygon?.holes[1]?.points[1],
                    id: outPointDefs[pqrIndexOut]?.point?.id,
                  },
                ],
              },
            ],
          },
        },
      ],
      contents: undefined,
    });
  });

  it("should reassign point IDs for a field's planting heading and center pivot", () => {
    const pointDefs: PointDefinition[] = [
      { point: pointFactory("1", "pha"), version: undefined },
      { point: pointFactory("2", "phb"), version: undefined },
      { point: pointFactory("3", "cpc"), version: undefined },
    ];
    const zone: Zone = {
      id: { id: "a" },
      version: undefined,
      name: "",
      areas: [],
      contents: {
        field: {
          centerPivot: {
            center: pointFactory("3", "cpc"),
            widthMeters: 5,
            lengthMeters: 200,
            endpointDeviceId: "1212-3434-5656",
          },
          plantingHeading: {
            azimuthDegrees: 240,
            abLine: {
              a: pointFactory("1", "pha"),
              b: pointFactory("2", "phb"),
            },
          },
        },
      },
    };

    const { pointDefs: outPointDefs, zone: outZone } = regenerateIdsForZone(
      pointDefs,
      zone
    );

    expect(outPointDefs).toHaveLength(3);
    const phaIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "pha"
    );
    const phbIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "phb"
    );
    const cpcIndexOut = outPointDefs.findIndex(
      (pd) => pd.point?.name === "cpc"
    );
    expectPointDefsIsomorphic(pointDefs, 0, outPointDefs, phaIndexOut);
    expectPointDefsIsomorphic(pointDefs, 1, outPointDefs, phbIndexOut);
    expectPointDefsIsomorphic(pointDefs, 2, outPointDefs, cpcIndexOut);

    expect(outZone).toEqual({
      ...zone,
      id: outZone.id,
      contents: {
        field: {
          centerPivot: {
            ...zone.contents?.field?.centerPivot,
            center: {
              ...zone.contents?.field?.centerPivot?.center,
              id: outPointDefs[cpcIndexOut]?.point?.id,
            },
          },
          plantingHeading: {
            ...zone.contents?.field?.plantingHeading,
            abLine: {
              ...zone.contents?.field?.plantingHeading?.abLine,
              a: {
                ...zone.contents?.field?.plantingHeading?.abLine?.a,
                id: outPointDefs[phaIndexOut]?.point?.id,
              },
              b: {
                ...zone.contents?.field?.plantingHeading?.abLine?.b,
                id: outPointDefs[phbIndexOut]?.point?.id,
              },
            },
          },
        },
      },
    });
  });
});
