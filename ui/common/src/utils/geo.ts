import { Position } from "geojson";
import bearing from "@turf/bearing";
import destination from "@turf/destination";

export interface Bounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

/**
 * Given two points, calculate the line (LineAB)
 * and return a line segment that starts at pointA and ends at a distance of 'distanceMeters' such that
 * the returned line is colinear to LineAB
 */
export const destinationAlongLine = (
  pointA: Position,
  pointB: Position,
  lengthMeters: number
): [Position, Position] => {
  const lineBearing = bearing(pointA, pointB);
  const newEnd = destination(pointA, lengthMeters, lineBearing, {
    units: "meters",
  });
  return [pointA, newEnd.geometry.coordinates];
};

export function makeGeoId(): string {
  let id = "";
  const charset = [
    ..."0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
  ] as const;

  const NUM_WORDS = 3;
  // this shouldn't be more than 5 unless you start using Uint64s; each
  // character of base62 consumes 5.954 (lg 62) bits of entropy
  const CHARS_PER_WORD = 4;
  const entropy = new Uint32Array(NUM_WORDS);
  crypto.getRandomValues(entropy);

  for (let word = 0; word < NUM_WORDS; word++) {
    let x = entropy[word]!;

    for (let char = 0; char < CHARS_PER_WORD; char++) {
      id += charset[x % charset.length];
      x = Math.floor(x / charset.length);
    }
  }

  return id;
}
