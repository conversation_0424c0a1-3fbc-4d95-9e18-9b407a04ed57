import {
  MapImageId,
  MapImageResolver,
} from "common/components/map/layers/types";
import goToAndFace from "common/images/icons/go-to-and-face.png";
import gpsPoint from "common/images/icons/gps-point.png";
import rtkFixed from "common/images/icons/rtk-fixed.png";
import rtkFloat from "common/images/icons/rtk-float.png";

/**
 * the "vite" here is a bit of a misnomer; this resolver works for any bundler/
 * plugin that natively imports image files as a URL, so both the default
 * behavior of vite static imports and webpack `file-loader`.
 */
export const viteImageResolver: MapImageResolver = (id) => {
  switch (id) {
    case MapImageId.GoToAndFace: {
      return goToAndFace;
    }
    case MapImageId.GpsPoint: {
      return gpsPoint;
    }
    case MapImageId.RtkFixed: {
      return rtkFixed;
    }
    case MapImageId.RtkFloat: {
      return rtkFloat;
    }
  }
};
