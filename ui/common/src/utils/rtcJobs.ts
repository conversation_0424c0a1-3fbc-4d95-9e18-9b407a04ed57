import {
  AutotractorBaseProps,
  AutotractorFeature,
  AutotractorPropsType,
  Domain,
} from "common/components/map/layers/types";
import { Feature, Geometry } from "geojson";
import { number, object } from "yup";
import { Objective, Task } from "protos/rtc/jobs";

// poll frequency
// how frequently we want to poll for the device/tractor's position
// recommend less that 1/2 of movement threshold.
export const PIVOT_POLL_INTERVAL_S = 5;
export const TRACTOR_POLL_INTERVAL_S = 1;

// movement threshold
// if the device/tractor's position has not changed in this amount of time,
// it is considered stationary (not moving)
export const PIVOT_IS_MOVING_THRESHOLD_S = 15;
export const TRACTOR_IS_MOVING_THRESHOLD_S = 3;

// stale threshold
// if we have not received a position update in this amount of time,
// the device/tractor is considered stale.
export const PIVOT_STALE_THRESHOLD_S = 30;
export const TRACTOR_STALE_THRESHOLD_S = 10;

// about 1cm - any higher and low-speed movement won't show as moving
// (we could maybe get rid of this and accept that tractors will usually look like they are moving)
export const TRACTOR_GPS_JITTER_THRESHOLD_M = 0.01;
export const PIVOT_GPS_JITTER_THRESHOLD_M = 0.05; // this can be higher for pivots since they poll less frequently.

export type JobFeatureProperties = TractorFeatureProperties;
export type ObjectiveFeature = Feature<Geometry, ObjectiveFeatureProperties>;
export type TractorFeature = Feature<Geometry, TractorFeatureProperties>;
export type TaskFeature = Feature<Geometry, TaskFeatureProperties>;
export type JobFeature = TractorFeature | ObjectiveFeature | TaskFeature;

export const getTractorFeature = (
  f?: AutotractorFeature
): TractorFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.TRACTOR) {
    return;
  }
  return { ...f, properties: f.properties };
};
export const getObjectiveFeature = (
  f?: AutotractorFeature
): ObjectiveFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.OBJECTIVE) {
    return;
  }
  return {
    ...f,
    properties: {
      ...f.properties,
      // this object is getting stringified by mapbox but the types don't suggest it
      // https://github.com/mapbox/mapbox-gl-js/issues/2434
      objective: parseMapBoxFlattenedObject<Objective>(f.properties.objective),
    },
  };
};
export const getTaskFeature = (
  f?: AutotractorFeature
): TaskFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.TASK) {
    return;
  }
  return {
    ...f,
    properties: {
      ...f.properties,
      // this object is getting stringified by mapbox but the types don't suggest it
      // https://github.com/mapbox/mapbox-gl-js/issues/2434
      task: parseMapBoxFlattenedObject<Task>(f.properties.task),
    },
  };
};

const parseMapBoxFlattenedObject = <T>(obj: any): T =>
  JSON.parse(obj as unknown as string);

export interface ObjectiveFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.OBJECTIVE;
  objective: Objective;
}

export interface TaskFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.TASK;
  task: Task;
}

export interface TractorFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.TRACTOR;
  serial: string;
}

export const LatLngSchema = object({
  lat: number().required(),
  lng: number().required(),
});

export const TASK_BASE_PROPS: Omit<TaskFeatureProperties, "task"> = {
  __portalFeatureProps: true,
  domain: Domain.AUTOTRACTOR,
  type: AutotractorPropsType.TASK,
};

export const OBJECTIVE_BASE_PROPS: Omit<
  ObjectiveFeatureProperties,
  "objective"
> = {
  __portalFeatureProps: true,
  domain: Domain.AUTOTRACTOR,
  type: AutotractorPropsType.OBJECTIVE,
};

export { TRACTOR_COLORS as AVAILABLE_COLORS } from "common/theme/theme";
