import { <PERSON><PERSON><PERSON><PERSON>, CustomLayerInterface } from "mapbox-gl";
import {
  Area,
  CenterPivot,
  Farm,
  PointDefinition,
  Zone,
} from "protos/portal/farm";
import {
  AutotractorBaseProps,
  AutotractorFeature,
  AutotractorPropsType,
} from "common/components/map/layers/types";
import { Bounds, makeGeoId } from "common/utils/geo";
import {
  Feature,
  FeatureCollection,
  Geometry,
  Point as GeoPoint,
  LineString,
} from "geojson";
import { Id, Point } from "protos/geo/geo";

// simplify control flow with exceptions, for want of an option monad
export class NoSuchPoint {
  constructor(public point: Point) {}
}

// ===
// custom farm element properties are part of the autotractor purview, so they extend the AT base props.
export interface PointFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.POINT;
  pointId: string | undefined;
}

export interface ZoneFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.ZONE;
  zoneId: string | undefined;
  zoneColor?: any; // theme colors aren't typed right now
}

export interface CenterPivotFeatureProperties extends AutotractorBaseProps {
  type: AutotractorPropsType.PIVOT;
  data: CenterPivot;
}

export type FarmFeatureProperties =
  | PointFeatureProperties
  | ZoneFeatureProperties;

export type PointFeature = Feature<GeoPoint, PointFeatureProperties>;
export type ZoneFeature = Feature<Geometry, ZoneFeatureProperties>;
export type CenterPivotFeature = Feature<
  Geometry,
  CenterPivotFeatureProperties
>;

export type FarmFeature = PointFeature | ZoneFeature | CenterPivotFeature;

export const getPointFeature = (
  f?: AutotractorFeature
): PointFeature | undefined => {
  if (
    !f ||
    f.geometry.type !== "Point" ||
    f.properties.type !== AutotractorPropsType.POINT
  ) {
    return;
  }
  return { ...f, geometry: f.geometry, properties: f.properties };
};

export const getZoneFeature = (
  f?: AutotractorFeature
): ZoneFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.ZONE) {
    return;
  }
  return { ...f, properties: f.properties };
};

export const getCenterPivotFeature = (
  f?: AutotractorFeature
): CenterPivotFeature | undefined => {
  if (!f || f.properties.type !== AutotractorPropsType.PIVOT) {
    return;
  }
  return { ...f, properties: f.properties };
};

export const derefPoint = (
  pointsById: Record<string, Point>,
  reference: Point
): Point => {
  const id = reference.id?.id;
  if (!id) {
    throw new NoSuchPoint(reference);
  }
  const referent = pointsById[id];
  if (!referent) {
    throw new NoSuchPoint(reference);
  }
  return referent;
};

export interface FarmMapData {
  id?: Id;
  pointsById: Record<string, Point>;
  zonesById: Map<string, Zone>;
  pointIdToZoneIds: Map<string, string[]>;
  centerPivotsByFieldId: Map<string, CenterPivot>;
  plantingHeadingsByFieldId: Map<string, LineString>;
  farmBoundary: FeatureCollection<Geometry, ZoneFeatureProperties>;
  fields: FeatureCollection<Geometry, ZoneFeatureProperties>;
  headlands: FeatureCollection<Geometry, ZoneFeatureProperties>;
  obstacles: FeatureCollection<Geometry, ZoneFeatureProperties>;
  roads: FeatureCollection<Geometry, ZoneFeatureProperties>;
  areasGeojson: FeatureCollection<Geometry, ZoneFeatureProperties>;
  pointsGeojson: FeatureCollection<GeoPoint, PointFeatureProperties>;
  bounds?: Bounds;
}

export const getFarmBounds = (
  farm: Farm,
  pointsById: Record<string, Point>
): Bounds | undefined => {
  let bounds: Bounds | undefined;
  if (Object.entries(pointsById).length > 0) {
    bounds = {
      minX: Infinity,
      minY: Infinity,
      maxX: -Infinity,
      maxY: -Infinity,
    };
    for (const point of Object.values(pointsById)) {
      // XXX: These are intentionally extracted backward to match
      // corresponding errors in the Map component, which are hard to fix
      // because they pervade the stack all the way down to robots sending
      // incorrect health logs.
      const { lng: y, lat: x } = point;

      bounds.minX = Math.min(bounds.minX, x);
      bounds.minY = Math.min(bounds.minY, y);
      bounds.maxX = Math.max(bounds.maxX, x);
      bounds.maxY = Math.max(bounds.maxY, y);
    }
  }
  return bounds;
};

export const getFieldBounds = (
  field: Zone,
  pointsById: Record<string, Point>
): Bounds | undefined => {
  const fieldPoints = field.areas.flatMap((a) =>
    a.polygon?.boundary?.points
      ? a.polygon.boundary.points.flatMap((p) => [derefPoint(pointsById, p)])
      : []
  );

  let bounds: Bounds | undefined;
  if (fieldPoints.length > 0) {
    bounds = {
      minX: Infinity,
      minY: Infinity,
      maxX: -Infinity,
      maxY: -Infinity,
    };
    for (const point of fieldPoints) {
      // XXX: These are intentionally extracted backward to match
      // corresponding errors in the Map component, which are hard to fix
      // because they pervade the stack all the way down to robots sending
      // incorrect health logs.
      const { lng: y, lat: x } = point;

      bounds.minX = Math.min(bounds.minX, x);
      bounds.minY = Math.min(bounds.minY, y);
      bounds.maxX = Math.max(bounds.maxX, x);
      bounds.maxY = Math.max(bounds.maxY, y);
    }
  }
  return bounds;
};

export function referencedPointIdsForArea(area: Area): Set<string> {
  const ids = new Set<string>();

  if (area.point && area.point.id) {
    ids.add(area.point.id.id);
  }

  if (area.lineString) {
    for (const pt of area.lineString.points) {
      if (pt.id) {
        ids.add(pt.id.id);
      }
    }
  }

  if (area.polygon) {
    if (area.polygon.boundary) {
      for (const pt of area.polygon.boundary.points) {
        if (pt.id) {
          ids.add(pt.id.id);
        }
      }
    }

    for (const hole of area.polygon.holes) {
      for (const pt of hole.points) {
        if (pt.id) {
          ids.add(pt.id.id);
        }
      }
    }
  }

  return ids;
}

export function reassignIdForPointFromMap(
  point: Point,
  idMap: Map<string, string>
): Point {
  const oldId = point.id?.id;

  // point isn't resolved by reference; this is a no-op
  if (!oldId) {
    return point;
  }

  const newId = idMap.get(oldId);

  // id isn't mapped
  if (!newId) {
    throw new Error(`unmapped ID ${oldId}`);
  }

  return {
    ...point,
    id: { id: newId },
  };
}

export function regenerateIdsForZone(
  pointDefs: PointDefinition[],
  zone: Zone
): { pointDefs: PointDefinition[]; zone: Zone } {
  const pointDefsOut: PointDefinition[] = [];
  const zoneOut: Zone = { ...zone, id: { id: makeGeoId() }, areas: [] };
  const idMap = new Map<string, string>();

  for (const pointDef of pointDefs) {
    if (!pointDef.point || !pointDef.point.id?.id) {
      throw new Error("unexpected point def shape");
    }

    const oldId = pointDef.point.id.id;
    const newId = makeGeoId();
    idMap.set(oldId, newId);

    const pointDefOut = {
      version: pointDef.version,
      point: reassignIdForPointFromMap(pointDef.point, idMap),
    };

    pointDefsOut.push(pointDefOut);
  }

  for (const area of zone.areas) {
    const areaOut: Area = { bufferMeters: area.bufferMeters };

    if (area.point) {
      areaOut.point = reassignIdForPointFromMap(area.point, idMap);
    }

    if (area.lineString) {
      areaOut.lineString = {
        ...area.lineString,
        points: area.lineString.points.map((pt) =>
          reassignIdForPointFromMap(pt, idMap)
        ),
      };
    }

    if (area.polygon) {
      areaOut.polygon = {
        ...area.polygon,
        boundary: area.polygon.boundary
          ? {
              points: area.polygon.boundary.points.map((pt) =>
                reassignIdForPointFromMap(pt, idMap)
              ),
            }
          : undefined,
        holes: area.polygon.holes.map((hole) => ({
          points: hole.points.map((pt) => reassignIdForPointFromMap(pt, idMap)),
        })),
      };
    }

    zoneOut.areas.push(areaOut);
  }

  if (zone.contents) {
    zoneOut.contents = {
      farmBoundary: zone.contents.farmBoundary,
      headland: zone.contents.headland,
      privateRoad: zone.contents.privateRoad,
      obstacle: zone.contents.obstacle,
    };

    if (zone.contents.field) {
      zoneOut.contents.field = {
        plantingHeading: undefined,
        centerPivot: undefined,
      };

      if (zone.contents.field.plantingHeading) {
        zoneOut.contents.field.plantingHeading = {
          ...zone.contents.field.plantingHeading,
          abLine: zone.contents.field.plantingHeading.abLine
            ? {
                a: zone.contents.field.plantingHeading.abLine.a
                  ? reassignIdForPointFromMap(
                      zone.contents.field.plantingHeading.abLine.a,
                      idMap
                    )
                  : undefined,
                b: zone.contents.field.plantingHeading.abLine.b
                  ? reassignIdForPointFromMap(
                      zone.contents.field.plantingHeading.abLine.b,
                      idMap
                    )
                  : undefined,
              }
            : undefined,
        };
      }

      if (zone.contents.field.centerPivot) {
        zoneOut.contents.field.centerPivot = {
          ...zone.contents.field.centerPivot,
          center: zone.contents.field.centerPivot.center
            ? reassignIdForPointFromMap(
                zone.contents.field.centerPivot.center,
                idMap
              )
            : undefined,
        };
      }
    }
  }

  return { pointDefs: pointDefsOut, zone: zoneOut };
}

// align Mapbox's generic layer type to `<Marker />`'s more restrictive props
export type AnyStandardLayer = Exclude<AnyLayer, CustomLayerInterface>;

export const polygonFillLayerStyle: AnyStandardLayer = {
  id: "farm-area-polygons-fill",
  type: "fill",
  filter: ["==", ["geometry-type"], "Polygon"],
  paint: {
    "fill-color": ["get", "zoneColor"],
    "fill-opacity": 0.6,
  },
};

export const pointLayerStyle: AnyStandardLayer = {
  id: "farm-area-points",
  type: "circle",
  paint: {
    "circle-radius": 5,
    "circle-color": ["get", "zoneColor"],
    "circle-stroke-color": "black",
    "circle-stroke-width": 2,
  },
  filter: ["==", ["geometry-type"], "Point"],
};

export const polygonOutlineLayerStyle: AnyStandardLayer = {
  id: "farm-area-polygons-outline",
  filter: ["==", ["geometry-type"], "Polygon"],
  type: "line",
  paint: {
    "line-color": "black",
    "line-width": 2,
  },
};
