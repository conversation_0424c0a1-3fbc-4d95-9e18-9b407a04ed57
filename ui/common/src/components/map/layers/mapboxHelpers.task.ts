import { Expression } from "mapbox-gl";
import { State } from "protos/rtc/jobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";

const colorsByState: Record<State, string> = {
  [State.ACKNOWLEDGED]: theme.colors.gray[500],
  [State.CANCELLED]: theme.colors.orange[500],
  [State.COMPLETED]: theme.colors.green[500],
  [State.FAILED]: theme.colors.red[500],
  [State.IN_PROGRESS]: theme.colors.blue[500],
  [State.NEW]: theme.colors.gray[500],
  [State.PAUSED]: theme.colors.blue[500],
  [State.PENDING]: theme.colors.gray[400],
  [State.READY]: theme.colors.gray[500],
  [State.STATE_UNSPECIFIED]: theme.colors.gray[500],
  [State.UNRECOGNIZED]: theme.colors.gray[500],
};

const opacityByState: Record<State, number> = {
  [State.ACKNOWLEDGED]: 0.5,
  [State.CANCELLED]: 1,
  [State.COMPLETED]: 1,
  [State.FAILED]: 1,
  [State.IN_PROGRESS]: 1,
  [State.NEW]: 0.5,
  [State.PAUSED]: 1,
  [State.PENDING]: 0.5,
  [State.READY]: 0.5,
  [State.STATE_UNSPECIFIED]: 0.5,
  [State.UNRECOGNIZED]: 0.5,
};

export const mapboxGetColorForTaskState: Expression = [
  "get",
  ["to-string", ["get", "state", ["get", "task"]]],
  ["literal", colorsByState],
];
export const mapboxGetOpacityForTaskState: Expression = [
  "get",
  ["to-string", ["get", "state", ["get", "task"]]],
  ["literal", opacityByState],
];
