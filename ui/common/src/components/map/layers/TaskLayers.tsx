import {
  getTaskFeature,
  ObjectiveFeature,
  TaskFeature,
} from "common/utils/rtcJobs";
import {
  InteractionLayer,
  LayerConfig,
  MapImageId,
} from "common/components/map/layers/types";
import { renderFollowPathTask } from "./taskRenderers/FollowPath";
import { renderGoToAndFaceTask } from "./taskRenderers/GoToAndFace";
import { renderLaserWeedRowTask } from "./taskRenderers/LaserWeedRow";
import { Task } from "protos/rtc/jobs";
import React, { FC, useMemo } from "react";

// a Task, but if Task[TKey] is TValue | undefined, TaskWith[TKey] is TValue
export type TaskWith<TKey extends keyof Task> = Task &
  Record<TKey, Exclude<Task[TKey], undefined>>;

interface TaskRendererProps<TDetailsField extends keyof Task> {
  tasks: TaskWith<TDetailsField>[];
  beforeId: string | undefined;
}

interface TaskRenderer<TDetailsField extends keyof Task> {
  detailsField: TDetailsField;
  Render: React.FC<TaskRendererProps<TDetailsField>>;
  interactiveLayerIds: string[];
  imagesToLoad: MapImageId[];
}

// this function looks like it does nothing and it does -- but calling it forces
// typescript to infer values for the type arguments (or throw a compile error
// if it can't) and gives you the type information on the return value for free.
export function makeTaskRenderer<TDetailsField extends keyof Task>(
  options: TaskRenderer<TDetailsField>
): TaskRenderer<TDetailsField> {
  return options;
}

interface TaskLayersProps {
  tasks: Task[];
  beforeId?: string;
  showTasks?: boolean;
}

const taskRenderers: TaskRenderer<any>[] = [
  renderLaserWeedRowTask,
  renderFollowPathTask,
  renderGoToAndFaceTask,
];

export const TaskLayers: FC<TaskLayersProps> = ({
  tasks,
  beforeId,
  showTasks,
}) => {
  // a note for future us: this block does some unsound things because
  // typescript's type system isn't expressive enough to allow us to "carry
  // along" the type information for each renderer and its tasks with it
  // (at least, not without some weird hacks.)  make sure that each element of
  // result contains only tasks with the appropriate field set before returning.
  const tasksByType = useMemo(() => {
    const result: {
      renderer: TaskRenderer<keyof Task>;
      tasks: TaskWith<keyof Task>[];
    }[] = [];

    // skip partitioning tasks if we don't need to display them
    if (showTasks) {
      for (const renderer of taskRenderers) {
        // partition tasks by type
        const detailsField = renderer.detailsField as keyof Task;
        const tasksThisType = tasks.filter((tsk) => Boolean(tsk[detailsField]));

        result.push({
          renderer,
          tasks: tasksThisType as TaskWith<keyof Task>[],
        });
      }
    }

    return result;
  }, [tasks, showTasks]);

  return (
    <>
      {showTasks &&
        tasksByType.map(({ renderer, tasks }) => (
          <React.Fragment key={`task-type-${renderer.detailsField}`}>
            <renderer.Render tasks={tasks} beforeId={beforeId} />
          </React.Fragment>
        ))}
    </>
  );
};

export const useTaskLayers = ({
  onClick,
  onEnter,
  onLeave,
}: {
  onClick?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onEnter?: (feature?: ObjectiveFeature | TaskFeature) => void;
  onLeave?: () => void;
}): LayerConfig<TaskLayersProps> => {
  const interactionLayers = taskRenderers
    .flatMap((renderer) => renderer.interactiveLayerIds)
    .map<InteractionLayer>((id) => ({
      cursor: "pointer",
      id,
      onMouseMove: (f) => onEnter?.(getTaskFeature(f)),
      onLeave: () => onLeave?.(),
      onClick: (f) => onClick?.(getTaskFeature(f)),
    }));
  const Layers = TaskLayers;
  const imagesToLoad: MapImageId[] = taskRenderers.flatMap(
    (renderer) => renderer.imagesToLoad
  );

  return { interactionLayers, Layers, idsToLoad: imagesToLoad };
};
