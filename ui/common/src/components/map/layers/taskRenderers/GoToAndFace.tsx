import { FeatureCollection, Point } from "geojson";
import { Layer, Source } from "react-map-gl";
import { makeTaskRenderer } from "../TaskLayers";
import { MapImageId } from "../types";
import { TASK_BASE_PROPS, TaskFeatureProperties } from "common/utils/rtcJobs";
import React from "react";

enum LayerIds {
  SYMBOL = "g2t-symbol",
}

export const renderGoToAndFaceTask = makeTaskRenderer({
  detailsField: "goToAndFace",
  Render: ({ tasks, beforeId }) => {
    const markerData: FeatureCollection<Point, TaskFeatureProperties> = {
      type: "FeatureCollection",
      features: tasks.flatMap((t) => {
        if (t.goToAndFace.point) {
          return {
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [t.goToAndFace.point.lng, t.goToAndFace.point.lat],
            },
            properties: {
              ...TASK_BASE_PROPS,
              task: t,
            },
          };
        } else {
          return [];
        }
      }),
    };

    return (
      <Source data={markerData} type="geojson">
        <Layer
          id={LayerIds.SYMBOL}
          beforeId={beforeId}
          type="symbol"
          layout={{
            "icon-image": MapImageId.GoToAndFace,
            "icon-rotate": [
              "get",
              "heading",
              ["get", "goToAndFace", ["get", "task"]],
            ],
          }}
        />
      </Source>
    );
  },
  interactiveLayerIds: [LayerIds.SYMBOL],
  imagesToLoad: [MapImageId.GoToAndFace],
});
