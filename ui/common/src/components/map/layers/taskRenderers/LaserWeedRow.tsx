import { Feature, FeatureCollection, LineString, Polygon } from "geojson";
import { getCoord } from "@turf/invariant";
import { Layer, Source } from "react-map-gl";
import { lineString, polygon } from "@turf/helpers";
import { makeTaskRenderer, TaskWith } from "../TaskLayers";
import {
  mapboxGetColorForTaskState,
  mapboxGetOpacityForTaskState,
} from "common/components/map/layers/mapboxHelpers.task";
import { Point as ProtoPoint } from "protos/geo/geo";
import { TASK_BASE_PROPS, TaskFeatureProperties } from "common/utils/rtcJobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import bearing from "@turf/bearing";
import buffer from "@turf/buffer";
import destination from "@turf/destination";
import distance from "@turf/distance";
import React from "react";

// TODO: remove this and use the one from planting when it is available ==
export const DEFAULT_ROW_WIDTH = 6.7056;
// ==

function taskBoxesForLaserWeedTasks(
  tasks: TaskWith<"laserWeed">[]
): FeatureCollection<Polygon, TaskFeatureProperties> {
  const rowWidthMeters = DEFAULT_ROW_WIDTH;

  function createRectangle(
    point1: ProtoPoint,
    point2: ProtoPoint,
    width: number
  ): Feature<Polygon> {
    const p1 = [point1.lng, point1.lat];
    const p2 = [point2.lng, point2.lat];
    const lineBearing = bearing(p1, p2);

    const perpBearing1 = lineBearing + 90;
    const perpBearing2 = lineBearing - 90;

    const halfWidth = width / 2;

    const corner1 = destination(p1, halfWidth, perpBearing1, {
      units: "meters",
    });
    const corner2 = destination(p1, halfWidth, perpBearing2, {
      units: "meters",
    });
    const corner3 = destination(p2, halfWidth, perpBearing2, {
      units: "meters",
    });
    const corner4 = destination(p2, halfWidth, perpBearing1, {
      units: "meters",
    });

    return polygon([
      [
        getCoord(corner1),
        getCoord(corner2),
        getCoord(corner3),
        getCoord(corner4),
        getCoord(corner1),
      ],
    ]);
  }

  return {
    type: "FeatureCollection",
    features: tasks.flatMap((t) => {
      let start = t.startLocation;
      let end = t.endLocation;
      if (t.laserWeed.path?.points) {
        start = start ?? t.laserWeed.path.points[0];
        end = end ?? t.laserWeed.path.points[1];
      }
      if (!start || !end) {
        return [];
      }
      const properties = {
        ...TASK_BASE_PROPS,
        task: t,
      };
      if (
        distance([start.lng, start.lat], [end.lng, end.lat], {
          units: "meters",
        }) > rowWidthMeters
      ) {
        return [
          { ...createRectangle(start, end, rowWidthMeters / 2), properties },
        ];
      }

      const bufferedLine = buffer<LineString>(
        lineString([
          [start.lng, start.lat],
          [end.lng, end.lat],
        ]),
        rowWidthMeters / 4,
        { units: "meters" }
      );

      return [{ ...bufferedLine, properties }];
    }),
  };
}

enum LayerIds {
  FILL = "lwt-fill",
  OUTLINE = "lwt-outline",
}

export const renderLaserWeedRowTask = makeTaskRenderer({
  detailsField: "laserWeed",
  Render: ({ tasks, beforeId }) => {
    return (
      <Source data={taskBoxesForLaserWeedTasks(tasks)} type="geojson">
        <Layer
          id={LayerIds.FILL}
          beforeId={beforeId}
          type="fill"
          maxzoom={24}
          minzoom={16}
          paint={{
            "fill-color": ["coalesce", mapboxGetColorForTaskState, "#FFF"],
            "fill-opacity": mapboxGetOpacityForTaskState,
          }}
        />
        <Layer
          id={LayerIds.OUTLINE}
          beforeId={beforeId}
          maxzoom={24}
          minzoom={16}
          type="line"
          paint={{
            "line-color": theme.colors.black,
          }}
        />
      </Source>
    );
  },
  interactiveLayerIds: [LayerIds.FILL],
  imagesToLoad: [],
});
