import { FeatureCollection, LineString, Polygon } from "geojson";
import { Layer, Source } from "react-map-gl";
import { lineString } from "@turf/helpers";
import { makeTaskRenderer } from "../TaskLayers";
import {
  mapboxGetColorForTaskState,
  mapboxGetOpacityForTaskState,
} from "../mapboxHelpers.task";
import { TASK_BASE_PROPS, TaskFeatureProperties } from "common/utils/rtcJobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import buffer from "@turf/buffer";
import React from "react";

// TODO: remove this and use the one from planting when it is available ==
export const DEFAULT_ROW_WIDTH = 6.7056;
// ==

enum LayerIds {
  CENTERLINE = "fpt-centerline",
  FILL = "fpt-fill",
  OUTLINE = "fpt-outline",
}

export const renderFollowPathTask = makeTaskRenderer({
  detailsField: "followPath",
  Render: ({ tasks, beforeId }) => {
    const centerlineData: FeatureCollection<LineString, TaskFeatureProperties> =
      {
        type: "FeatureCollection",
        features: tasks.flatMap((t) => {
          return {
            type: "Feature",
            geometry: {
              type: "LineString",
              coordinates:
                t.followPath.path?.points.map((pt) => [pt.lng, pt.lat]) ?? [],
            },
            properties: {
              ...TASK_BASE_PROPS,
              task: t,
            },
          };
        }),
      };

    const swathData: FeatureCollection<Polygon, TaskFeatureProperties> = {
      type: "FeatureCollection",
      features: tasks.flatMap((t) => {
        return {
          ...buffer<LineString>(
            lineString(
              t.followPath.path?.points.map((pt) => [pt.lng, pt.lat]) ?? []
            ),
            DEFAULT_ROW_WIDTH / 4,
            { units: "meters" }
          ),
          properties: {
            ...TASK_BASE_PROPS,
            task: t,
          },
        };
      }),
    };

    return (
      <>
        <Source data={centerlineData} type="geojson">
          <Layer
            id={LayerIds.CENTERLINE}
            beforeId={beforeId}
            maxzoom={24}
            minzoom={16}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-dasharray": [16, 8],
            }}
          />
        </Source>
        <Source data={swathData} type="geojson">
          <Layer
            id={LayerIds.FILL}
            beforeId={LayerIds.CENTERLINE}
            maxzoom={24}
            minzoom={16}
            type="fill"
            paint={{
              "fill-color": ["coalesce", mapboxGetColorForTaskState, "#FFF"],
              "fill-opacity": mapboxGetOpacityForTaskState,
            }}
          />
          <Layer
            id={LayerIds.OUTLINE}
            beforeId={beforeId}
            maxzoom={24}
            minzoom={16}
            type="line"
            paint={{
              "line-color": theme.colors.black,
            }}
          />
        </Source>
      </>
    );
  },
  interactiveLayerIds: [LayerIds.FILL],
  imagesToLoad: [],
});
