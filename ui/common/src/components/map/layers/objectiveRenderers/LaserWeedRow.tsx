import { FeatureCollection, LineString, Point, Polygon } from "geojson";
import {
  LatLngSchema,
  OBJECTIVE_BASE_PROPS,
  ObjectiveFeatureProperties,
} from "common/utils/rtcJobs";
import { Layer, Source } from "react-map-gl";
import { makeObjectiveRenderer, RestrictedObjective } from "../ObjectiveLayers";
import { number, object } from "yup";
import { Objective_ObjectiveType as ObjectiveType } from "protos/rtc/jobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import buffer from "@turf/buffer";
import React from "react";

// TODO: remove this and use the one from planting when it is available ==
export const DEFAULT_ROW_WIDTH = 6.7056;
// ==

const LaserWeedRowSchema = object({
  rowNum: number().required(),
  abLine: object({
    a: LatLngSchema.required(),
    b: LatLngSchema.required(),
  }).required(),
});

type LaserWeedRowObjective = RestrictedObjective<
  ObjectiveType.LASER_WEED_ROW,
  typeof LaserWeedRowSchema
>;

function rowLinesForLaserWeedRowObjectives(
  objectives: LaserWeedRowObjective[]
): FeatureCollection<LineString, ObjectiveFeatureProperties> {
  return {
    type: "FeatureCollection",
    features: objectives.flatMap((o) => {
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...o,
        },
      };
      return {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [
            [o.data.abLine.a.lng, o.data.abLine.a.lat],
            [o.data.abLine.b.lng, o.data.abLine.b.lat],
          ],
        },
        properties,
      };
    }),
  };
}

function rowBoxesForLaserWeedRowObjectives(
  objectives: LaserWeedRowObjective[]
): FeatureCollection<Polygon, ObjectiveFeatureProperties> {
  return {
    type: "FeatureCollection",
    features: objectives.flatMap((o) => {
      const lineGeoJson: LineString = {
        type: "LineString",
        coordinates: [
          [o.data.abLine.a.lng, o.data.abLine.a.lat],
          [o.data.abLine.b.lng, o.data.abLine.b.lat],
        ],
      };

      const bufferedLine = buffer(lineGeoJson, DEFAULT_ROW_WIDTH / 2, {
        units: "meters",
        steps: 1,
      });
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...o,
        },
      };

      return {
        ...bufferedLine,
        properties,
      };
    }),
  };
}

function endPointsForLaserWeedRowObjectives(
  objectives: LaserWeedRowObjective[]
): FeatureCollection<Point, ObjectiveFeatureProperties> {
  return {
    type: "FeatureCollection",
    features: objectives.flatMap((o) => {
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...o,
        },
      };
      return [
        {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [o.data.abLine.a.lng, o.data.abLine.a.lat],
          },
          properties,
        },
        {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [o.data.abLine.b.lng, o.data.abLine.b.lat],
          },
          properties,
        },
      ];
    }),
  };
}

enum LayerIds {
  ROW_NUMBERS = "lwo-row-numbers",
  CENTERLINES = "lwo-centerlines",
  CENTERLINES_HIGHLIGHT = "lwo-centerlines-highlighted",
  CENTERLINES_HIGHLIGHT_FIRST = "lwo-centerlines-highlighted-first",
  BOX_OUTLINE = "lwo-row-outline",
  BOX_FILL = "lwo-row-fill",
  BOX_FOCUSED = "lwo-focused",
  BOX_SELECTED_OUTLINE = "lwo-selected-outline",
  BOX_SELECTED = "lwo-selected-fill",
  BOX_TRACTOR_HOVER = "lwo-tractor-hover",
  BOX_HIGHLIGHTED = "lwo-highlighted",
  BOX_HIGHLIGHTED_FIRST = "lwo-first-highlighted",
}

export const renderLaserWeedRowObjective = makeObjectiveRenderer({
  type: ObjectiveType.LASER_WEED_ROW,
  dataSchema: LaserWeedRowSchema,
  Render: ({ objectives, beforeId, api }) => {
    return (
      <>
        <Source
          data={endPointsForLaserWeedRowObjectives(objectives)}
          type="geojson"
        >
          <Layer
            id={LayerIds.ROW_NUMBERS}
            beforeId={beforeId}
            type="symbol"
            paint={{
              "text-color": ["coalesce", api.getTractorAssignmentColor, "#FFF"],
              "text-halo-color": theme.colors.black,
              "text-halo-width": 2,
            }}
            layout={{
              "text-field": [
                "get",
                "rowNum",
                ["get", "data", api.getObjective],
              ],
            }}
          />
        </Source>
        <Source
          data={rowLinesForLaserWeedRowObjectives(objectives)}
          type="geojson"
        >
          <Layer
            id={LayerIds.CENTERLINES}
            beforeId={LayerIds.ROW_NUMBERS}
            type="line"
            minzoom={16}
            maxzoom={24}
            paint={{
              "line-color": theme.colors.black,
              "line-dasharray": [16, 8],
            }}
          />
          <Layer
            id={LayerIds.CENTERLINES_HIGHLIGHT}
            beforeId={LayerIds.ROW_NUMBERS}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={api.isHighlightedObjective}
          />
          <Layer
            id={LayerIds.CENTERLINES_HIGHLIGHT_FIRST}
            beforeId={LayerIds.ROW_NUMBERS}
            type="line"
            paint={{
              "line-color": theme.colors.white,
              "line-width": 2,
            }}
            filter={api.isFirstHighlightedObjective}
          />
        </Source>
        <Source
          data={rowBoxesForLaserWeedRowObjectives(objectives)}
          type="geojson"
        >
          <Layer
            id={LayerIds.BOX_OUTLINE}
            beforeId={LayerIds.CENTERLINES}
            minzoom={16}
            maxzoom={24}
            type="line"
            paint={{
              "line-color": theme.colors.black,
            }}
          />
          <Layer
            id={LayerIds.BOX_FILL}
            beforeId={LayerIds.CENTERLINES}
            type="fill"
            paint={{
              "fill-color": ["coalesce", api.getTractorAssignmentColor, "#000"],
              "fill-opacity": api.getTractorAssignmentOpacity,
            }}
          />
          <Layer
            id={LayerIds.BOX_FOCUSED}
            beforeId={LayerIds.CENTERLINES}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[400],
              "fill-opacity": 0.6,
            }}
            filter={api.isFocusedObjective}
          />
          <Layer
            id={LayerIds.BOX_SELECTED_OUTLINE}
            beforeId={LayerIds.CENTERLINES}
            type="line"
            paint={{
              "line-color": theme.colors.blue[600],
              "line-width": 2,
            }}
            filter={api.isSelectedObjective}
          />
          <Layer
            id={LayerIds.BOX_SELECTED}
            beforeId={LayerIds.CENTERLINES}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[500],
              "fill-opacity": 0.2,
            }}
            filter={api.isSelectedObjective}
          />
          <Layer
            id={LayerIds.BOX_TRACTOR_HOVER}
            beforeId={beforeId}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={api.isFocusedTractorObjective}
          />
          <Layer
            id={LayerIds.BOX_HIGHLIGHTED}
            beforeId={beforeId}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={api.isHighlightedObjective}
          />
          <Layer
            id={LayerIds.BOX_HIGHLIGHTED_FIRST}
            beforeId={LayerIds.BOX_FILL}
            type="line"
            paint={{
              "line-blur": 8,
              "line-color": theme.colors.white,
              "line-width": 16,
            }}
            filter={api.isFirstHighlightedObjective}
          />
        </Source>
      </>
    );
  },
  interactiveLayerIds: [LayerIds.BOX_FILL],
});
