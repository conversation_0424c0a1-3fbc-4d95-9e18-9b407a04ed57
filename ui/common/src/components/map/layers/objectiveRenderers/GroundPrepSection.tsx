import { array, number, object } from "yup";
import { FeatureCollection, Polygon } from "geojson";
import {
  LatLngSchema,
  OBJECTIVE_BASE_PROPS,
  ObjectiveFeatureProperties,
} from "common/utils/rtcJobs";
import { Layer, Source } from "react-map-gl";
import { makeObjectiveRenderer, RestrictedObjective } from "../ObjectiveLayers";
import { Objective_ObjectiveType as ObjectiveType } from "protos/rtc/jobs";
import { resolvedTheme as theme } from "common/theme/tailwind.theme";
import React from "react";

const GroundPrepSectionDataSchema = object({
  sectionNum: number().required(),
  boundary: object({
    points: array(LatLngSchema).required(),
  }).required(),
});

type GroundPrepSectionObjective = RestrictedObjective<
  ObjectiveType.GROUND_PREP_SECTION,
  typeof GroundPrepSectionDataSchema
>;

function boundariesForGroundPrepSectionObjectives(
  objectives: GroundPrepSectionObjective[]
): FeatureCollection<Polygon, ObjectiveFeatureProperties> {
  return {
    type: "FeatureCollection",
    features: objectives.flatMap((o) => {
      const properties: ObjectiveFeatureProperties = {
        ...OBJECTIVE_BASE_PROPS,
        objective: {
          ...o,
        },
      };

      return {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [o.data.boundary.points.map((pt) => [pt.lng, pt.lat])],
        },
        properties,
      };
    }),
  };
}

enum LayerIds {
  FILL = "gpo-fill",
  OUTLINE = "gpo-outline",
  OUTLINE_SELECTED = "gpo-outline-selected",
  OUTLINE_TRACTOR_HOVER = "gpo-outline-tractor-hover",
  FOCUSED = "gpo-fill-focused",
  SELECTED = "gpo-fill-selected",
}

export const renderGroundPrepSectionObjective = makeObjectiveRenderer({
  type: ObjectiveType.GROUND_PREP_SECTION,
  dataSchema: GroundPrepSectionDataSchema,
  Render: ({ objectives, beforeId, api }) => {
    return (
      <>
        <Source
          data={boundariesForGroundPrepSectionObjectives(objectives)}
          type="geojson"
        >
          <Layer
            id={LayerIds.OUTLINE}
            beforeId={beforeId}
            minzoom={16}
            maxzoom={24}
            type="line"
            paint={{
              "line-color": theme.colors.black,
            }}
          />
          <Layer
            id={LayerIds.FILL}
            beforeId={LayerIds.OUTLINE}
            type="fill"
            paint={{
              "fill-color": ["coalesce", api.getTractorAssignmentColor, "#000"],
              "fill-opacity": api.getTractorAssignmentOpacity,
            }}
          />
          <Layer
            id={LayerIds.FOCUSED}
            beforeId={LayerIds.OUTLINE}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[400],
              "fill-opacity": 0.6,
            }}
            filter={api.isFocusedObjective}
          />
          <Layer
            id={LayerIds.OUTLINE_SELECTED}
            beforeId={LayerIds.OUTLINE}
            type="line"
            paint={{
              "line-color": theme.colors.blue[600],
              "line-width": 2,
            }}
            filter={api.isSelectedObjective}
          />
          <Layer
            id={LayerIds.SELECTED}
            beforeId={LayerIds.OUTLINE}
            type="fill"
            paint={{
              "fill-color": theme.colors.blue[500],
              "fill-opacity": 0.2,
            }}
            filter={api.isSelectedObjective}
          />
          <Layer
            id={LayerIds.OUTLINE_TRACTOR_HOVER}
            beforeId={beforeId}
            type="line"
            paint={{
              "line-color": theme.colors.black,
              "line-width": 2,
            }}
            filter={api.isFocusedTractorObjective}
          />
        </Source>
      </>
    );
  },
  interactiveLayerIds: [LayerIds.FILL],
});
