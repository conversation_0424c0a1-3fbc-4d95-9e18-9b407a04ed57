import {
  CARBON_PALETTE,
  GRAY_PALETTE,
  GREEN_LIGHT_PALETTE,
  OCEAN_PALETTE,
  RED_LIGHT_PALETTE,
  YELLOW_LIGHT_PALETTE,
} from "./colors";
import { Config } from "tailwindcss";
import {
  FONT_SIZE,
  LATO_FONT_FAMILY,
  POPPINS_FONT_FAMILY,
  RUSSO_FONT_FAMILY,
} from "./typography";
import Color from "color";

import defaultConfig from "tailwindcss/defaultConfig";
import resolveConfig from "tailwindcss/resolveConfig";

const resolvedDefaultConfig = resolveConfig(defaultConfig);

const CARBON_BLUE = "#6D8FAD";
const CARBON_GRAY_DARK = "#111518";
const CARBON_GRAY_LIGHT = "#a2aaad";
const CARBON_GRAY_MEDIUM = "#333f48";
const CARBON_GRAY_SEMIDARK = "#1f272c";
const CARBON_GREEN = "#518C66";
const CARBON_ORANGE = "#A9431E";
const CARBON_YELLOW = "#FFBA63";

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
const toTailwindRange = (sourceColor: string) => {
  const color500 = new Color(sourceColor);
  const color100 = color500.lighten(0.5).saturate(0.5);
  const color300 = color500.mix(color100);
  const color200 = color300.mix(color100);
  const color400 = color500.mix(color300);
  const color900 = color500.darken(0.5).saturate(0.5);
  const color700 = color500.mix(color900);
  const color600 = color500.mix(color700);
  const color800 = color900.mix(color700);
  return {
    50: color100.lighten(0.5).hex(),
    100: color100.hex(),
    200: color200.hex(),
    300: color300.hex(),
    400: color400.hex(),
    500: color500.hex(),
    600: color600.hex(),
    700: color700.hex(),
    800: color800.hex(),
    900: color900.hex(),
    950: color900.darken(0.5).hex(),
  };
};

export const tailwindTheme: Config["theme"] = {
  darken: {
    100: "rgba(0, 0, 0, 0.1)",
    200: "rgba(0, 0, 0, 0.2)",
    300: "rgba(0, 0, 0, 0.3)",
    400: "rgba(0, 0, 0, 0.4)",
    500: "rgba(0, 0, 0, 0.5)",
    600: "rgba(0, 0, 0, 0.6)",
    700: "rgba(0, 0, 0, 0.7)",
    800: "rgba(0, 0, 0, 0.8)",
    900: "rgba(0, 0, 0, 0.9)",
  },
  lighten: {
    100: "rgba(255, 255, 255, 0.1)",
    200: "rgba(255, 255, 255, 0.2)",
    300: "rgba(255, 255, 255, 0.3)",
    400: "rgba(255, 255, 255, 0.4)",
    500: "rgba(255, 255, 255, 0.5)",
    600: "rgba(255, 255, 255, 0.6)",
    700: "rgba(255, 255, 255, 0.7)",
    800: "rgba(255, 255, 255, 0.8)",
    900: "rgba(255, 255, 255, 0.9)",
  },
  fontSize: FONT_SIZE,
  fontFamily: {
    lato: LATO_FONT_FAMILY,
    russo: RUSSO_FONT_FAMILY,
    poppins: POPPINS_FONT_FAMILY,
  },
  colors: {
    // we're using zinc for the neutral palette so I'm going to override other gray-ish colors so that the palette doesn't diverge
    "rtc-gray": GRAY_PALETTE,
    carbon: {
      orange: CARBON_ORANGE,
      yellow: CARBON_YELLOW,
      gray: {
        dark: CARBON_GRAY_DARK,
        semidark: CARBON_GRAY_SEMIDARK,
        medium: CARBON_GRAY_MEDIUM,
        light: CARBON_GRAY_LIGHT,
      },
      green: CARBON_GREEN,
      blue: CARBON_BLUE,
      map: {
        draw: {
          fill: "#95cced",
          border: "#1da1f2",
          vertex: "#1da1f2",
          text: "white",
          textHalo: "black",
        },
        farms: {
          field: "#98ed95",
          obstacle: "#a34315",
          headland: "#ecf16e",
          default: "#bbbbbb",
          focused: "#95cced",
          centerPivot: "#6ab5f7",
        },
      },
    },
    "carbon-orange": CARBON_PALETTE,
    primary: OCEAN_PALETTE,
    success: GREEN_LIGHT_PALETTE,
    warning: YELLOW_LIGHT_PALETTE,
    error: RED_LIGHT_PALETTE,
    darken: {
      100: "rgba(0, 0, 0, 0.1)",
      200: "rgba(0, 0, 0, 0.2)",
      300: "rgba(0, 0, 0, 0.3)",
      400: "rgba(0, 0, 0, 0.4)",
      500: "rgba(0, 0, 0, 0.5)",
      600: "rgba(0, 0, 0, 0.6)",
      700: "rgba(0, 0, 0, 0.7)",
      800: "rgba(0, 0, 0, 0.8)",
      900: "rgba(0, 0, 0, 0.9)",
    },
    lighten: {
      100: "rgba(255, 255, 255, 0.1)",
      200: "rgba(255, 255, 255, 0.2)",
      300: "rgba(255, 255, 255, 0.3)",
      400: "rgba(255, 255, 255, 0.4)",
      500: "rgba(255, 255, 255, 0.5)",
      600: "rgba(255, 255, 255, 0.6)",
      700: "rgba(255, 255, 255, 0.7)",
      800: "rgba(255, 255, 255, 0.8)",
      900: "rgba(255, 255, 255, 0.9)",
    },
    blue: toTailwindRange(CARBON_BLUE),
    orange: toTailwindRange(CARBON_ORANGE),
    yellow: toTailwindRange(CARBON_YELLOW),
    green: toTailwindRange(CARBON_GREEN),
    "green-primary": "#009951",
    // for when you want colors that are a bit closer to consensus reality
    "tailwind-orange": resolvedDefaultConfig.theme.colors.orange,
    "tailwind-yellow": resolvedDefaultConfig.theme.colors.yellow,
  },
  animation: {
    "reverse-spin": "reverse-spin 1s linear infinite",
    "mask-sweep": "mask-sweep 3s linear infinite",
    emphasize: "emphasize 1s ease-out 1",
  },
  keyframes: {
    semispin: {
      "0%": { transform: "rotate(0deg)" },
      "100%": { transform: "rotate(180deg)" },
    },
    "reverse-spin": {
      from: {
        transform: "rotate(360deg)",
      },
    },
    "mask-sweep": {
      "0%": { maskPosition: "200% 0", WebkitMaskPosition: "200% 0" },
      "100%": { maskPosition: "0% 0", WebkitMaskPosition: "0% 0" },
    },
    emphasize: {
      "0%, 100%": {
        zIndex: "inherit",
        transform: "scale(1)",
        filter: "brightness(1)",
      },
      "1%, 99%": { zIndex: "10" }, // jump super early so it’s “on top” for the whole run
      "50%": {
        transform: "scale(1.2)",
        filter: "brightness(1.5)",
      },
    },
  },
  borderRadius: {
    xs: "0.05rem",
  },
  leading: {
    inherit: "inherit",
  },
  height: {
    unset: "unset",
  },
  width: {
    "1/10": "10%",
    "1/8": "calc(100%/8)",
  },
};

const commonConfig: Config = {
  ...defaultConfig,
  theme: {
    ...defaultConfig.theme,
    extend: { ...tailwindTheme },
  },
};

export const resolvedTheme: Record<string, any> =
  resolveConfig(commonConfig).theme;
