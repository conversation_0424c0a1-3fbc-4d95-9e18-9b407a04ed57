import { _basePortalApi, Tag } from "./base";
import { DeepPartial } from "common/utils/objects";
import { Farm, ListFarmsResponse } from "protos/portal/farm";

const farmApi = _basePortalApi.injectEndpoints({
  endpoints: (builder) => ({
    createFarm: builder.mutation<Farm, Farm>({
      query: (farm) => ({
        url: `farms`,
        method: "POST",
        body: Farm.toJSON(farm),
      }),
      transformResponse: (response: unknown) => Farm.fromJSON(response),
      invalidatesTags: (_result, error) => (error ? [] : [Tag.FARM]),
    }),
    listFarms: builder.query<Farm[], { contents: boolean }>({
      // Ideally, we'd let this paginate, but RTK Query doesn't have good
      // support for this... yet:
      //
      // https://github.com/reduxjs/redux-toolkit/discussions/1171#discussioncomment-11465176
      //
      // ...so we just fetch exhaustively for now.
      queryFn: async (_argument, _api, _options, baseQuery) => {
        const pages: Farm[][] = [];
        const contents = _argument.contents;
        let response: ListFarmsResponse | undefined;
        do {
          const pageToken = response?.nextPageToken ?? "";
          const queryResult = await baseQuery({
            url: "farms",
            params: { pageToken, contents },
          });
          const { data, error } = queryResult;
          if (error) {
            return { error };
          }
          response = ListFarmsResponse.fromJSON(data);
          pages.push(response.farms);
        } while (response.nextPageToken);
        return { data: pages.flat() };
      },
      providesTags: (farms: Farm[] | undefined) => [
        Tag.FARM,
        ...(farms ?? []).flatMap((farm) => {
          const id = farm.id?.id;
          return id ? [{ type: Tag.FARM, id }] : [];
        }),
      ],
    }),
    getFarm: builder.query<Farm, string>({
      query: (farmId) => `farms/${farmId}`,
      transformResponse: (response: unknown) => Farm.fromJSON(response),
      providesTags: (farm) => {
        const id = farm?.id?.id;
        return id ? [{ type: Tag.FARM, id }] : [];
      },
    }),
    updateFarm: builder.mutation<Farm, DeepPartial<Farm>>({
      query: (farm) => ({
        url: `farms/${farm.id?.id}`,
        method: "PATCH",
        body: Farm.toJSON(Farm.fromPartial(farm)),
      }),
      invalidatesTags: (_result, _error, farm) => [
        { type: Tag.FARM, id: farm.id?.id },
      ],
    }),
  }),
  overrideExisting: "throw",
});

export const {
  useCreateFarmMutation,
  useListFarmsQuery,
  useLazyListFarmsQuery,
  useGetFarmQuery,
  useLazyGetFarmQuery,
  useUpdateFarmMutation,
} = farmApi;
