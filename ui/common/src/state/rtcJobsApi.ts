import {
  type BaseQueryFn,
  createApi,
  type Fetch<PERSON>rgs,
  fetchBaseQuery,
  type FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { camelToSnake } from "common/utils/strings";
import {
  CreateJobRequest,
  Intervention,
  Job,
  ListInterventionsRequest,
  ListInterventionsResponse,
  ListJobsResponse,
  ListObjectiveAssignmentsResponse,
  ListTasksResponse,
  Objective,
  ObjectiveAssignment,
  ObjectiveList,
  StartManualTaskRequest,
  State,
  stateToJSON,
  StopManualTaskRequest,
  Task,
  UpdateTaskResponse,
} from "protos/rtc/jobs";
import { Method } from "common/utils/api";
import { QueryEnv } from "common/state/queryEnv";
import { spliceIfExists } from "common/utils/arrays";
import { toQuery } from "common/utils/browser";
import { transformKeys } from "common/utils/objects";

const baseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (arguments_, api, extraOptions = {}) => {
  return await fetchBaseQuery({
    baseUrl: `${QueryEnv.get().rtcJobsBaseUrl}/v1/`,
    prepareHeaders: async (headers) => {
      const accessToken = await QueryEnv.get().accessTokenProvider(api);

      if (accessToken) {
        headers.set("Authorization", `Bearer ${accessToken}`);
      }

      headers.set("X-AUTH0-audience", QueryEnv.get().auth0Audience);
      return headers;
    },
    paramsSerializer: toQuery,
  })(arguments_, api, extraOptions);
};

enum JobsAPITag {
  JOB = "Job",
  OBJECTIVE = "Objective",
  ASSIGNMENT = "Assignment",
  INTERVENTION = "Intervention",
  TASK = "Task",
}

const arrayToString = (arr?: string[] | number[]): string | undefined =>
  arr ? arr.join(",") : undefined;

export const rtcJobsApi = createApi({
  reducerPath: "rtcJobsApi",
  baseQuery,
  tagTypes: Object.values(JobsAPITag),
  endpoints: (builder) => ({
    listJobs: builder.query<
      ListJobsResponse,
      {
        pageSize?: number;
        pageToken?: string;
        fieldIds?: string[];
        ids?: number[];
      }
    >({
      query: ({ pageSize, pageToken, fieldIds, ids }) => ({
        url: "/jobs",
        params: {
          ids: arrayToString(ids),
          page_size: pageSize,
          page_token: pageToken,
          field_ids: arrayToString(fieldIds),
        },
      }),
      providesTags: (resp) => [
        JobsAPITag.JOB,
        ...(resp?.jobs.map((j) => ({
          type: JobsAPITag.JOB,
          id: j.id,
        })) ?? []),
      ],
      transformResponse: (response: unknown) =>
        ListJobsResponse.fromJSON(response),
    }),
    createJob: builder.mutation<Job, { job: CreateJobRequest }>({
      query: ({ job }) => ({
        url: "/jobs",
        method: "POST",
        body: job,
      }),
      invalidatesTags: (result) => [
        JobsAPITag.JOB,
        ...(result?.id ? [{ type: JobsAPITag.JOB, id: result.id }] : []),
      ],
    }),
    getJob: builder.query<Job, { id: string }>({
      query: ({ id }) => ({
        url: `/jobs/${id}`,
      }),
      transformResponse: (response: unknown) => Job.fromJSON(response),
      providesTags: (resp, err, { id }) =>
        resp ? [{ type: JobsAPITag.JOB, id }] : [JobsAPITag.JOB],
    }),
    getObjective: builder.query<Objective, { id: number }>({
      query: ({ id }) => ({
        url: `/objectives/${id}`,
      }),
      transformResponse: (response: unknown) => Objective.fromJSON(response),
      providesTags: (resp, err, { id }) =>
        resp ? [{ type: JobsAPITag.OBJECTIVE, id }] : [],
    }),
    listObjectives: builder.query<
      ObjectiveList,
      {
        name_prefix?: string;
        name_suffix?: string;
        ids?: number[]; // list of objectiveIds
        jobIds?: number[]; // list of job ids
        types?: string[]; // list of job types
        priorities?: string[]; // list of priorities
        page_size?: number;
        page_token?: string;
      }
    >({
      query: ({ ids, jobIds, types, priorities, ...rest }) => ({
        url: `/objectives`,
        params: {
          ids: arrayToString(ids),
          job_ids: arrayToString(jobIds),
          types: arrayToString(types),
          priorities: arrayToString(priorities),
          ...rest,
        },
      }),
      transformResponse: (response: unknown) =>
        ObjectiveList.fromJSON(response),
      providesTags: (resp) => [
        JobsAPITag.OBJECTIVE,
        ...(resp?.objectives.map((ob) => ({
          type: JobsAPITag.OBJECTIVE,
          id: ob.id,
        })) ?? []),
      ],
    }),
    listAssignments: builder.query<
      ListObjectiveAssignmentsResponse,
      {
        objectiveIds?: string[];
        robotSerials?: string[];
        pageSize?: number;
        pageToken?: string;
      }
    >({
      query: ({ objectiveIds, pageSize, pageToken, robotSerials }) => ({
        url: `/objective-assignments`,
        params: {
          objective_ids: arrayToString(objectiveIds),
          robot_serials: arrayToString(robotSerials),
          page_size: pageSize,
          page_token: pageToken,
        },
      }),
      transformResponse: (resp: unknown) =>
        ListObjectiveAssignmentsResponse.fromJSON(resp),
      providesTags: (resp) => [
        JobsAPITag.ASSIGNMENT,
        ...(resp?.assignments.map((ass) => ({
          type: JobsAPITag.ASSIGNMENT,
          id: ass.id,
        })) ?? []),
      ],
    }),
    setAssignment: builder.mutation<
      ObjectiveAssignment,
      { objectiveId: number; robotSerial: string }
    >({
      query: ({ objectiveId, robotSerial }) => ({
        url: `/objectives/${objectiveId}/assignment`,
        method: Method.PUT,
        body: { robotSerial },
      }),
      transformResponse: (response: unknown) =>
        ObjectiveAssignment.fromJSON(response),
      onQueryStarted: async ({ objectiveId }, { dispatch, queryFulfilled }) => {
        const { data: assignment } = await queryFulfilled;
        dispatch(
          rtcJobsApi.util.invalidateTags([
            JobsAPITag.ASSIGNMENT,
            { type: JobsAPITag.ASSIGNMENT, id: assignment.id },
          ])
        );
        dispatch(
          rtcJobsApi.util.invalidateTags([
            { type: JobsAPITag.OBJECTIVE, id: objectiveId },
          ])
        );
      },
    }),
    removeAssignment: builder.mutation<
      ObjectiveAssignment,
      { objectiveId: number }
    >({
      query: ({ objectiveId }) => ({
        url: `/objectives/${objectiveId}/assignment`,
        method: Method.DELETE,
      }),
      transformResponse: (response: unknown) =>
        ObjectiveAssignment.fromJSON(response),
      onQueryStarted: async ({ objectiveId }, { dispatch, queryFulfilled }) => {
        const { data } = await queryFulfilled;
        dispatch(
          rtcJobsApi.util.updateQueryData("listAssignments", {}, (resp) => {
            spliceIfExists(
              resp.assignments,
              (assignment) => assignment.id === data.id,
              1
            );
            return resp;
          })
        );
        dispatch(
          rtcJobsApi.util.invalidateTags([
            JobsAPITag.ASSIGNMENT,
            { type: JobsAPITag.ASSIGNMENT, id: data.id },
          ])
        );
        dispatch(
          rtcJobsApi.util.invalidateTags([
            { type: JobsAPITag.OBJECTIVE, id: objectiveId },
          ])
        );
      },
    }),
    updateJob: builder.mutation<
      Job,
      { job: Partial<Job> & { id: number }; generateObjectives: boolean }
    >({
      query: ({ job, ...params }) => ({
        url: `/jobs/${job.id}`,
        params: transformKeys(params, camelToSnake),
        method: "PATCH",
        body: job,
      }),
      transformResponse: (response: unknown) => Job.fromJSON(response),
      invalidatesTags: (_resp, _error, { job }) => [
        { type: JobsAPITag.JOB, id: job.id },
      ],
    }),
    updateAvailableRobots: builder.mutation<
      { entries: { robotSerial: string }[] },
      { jobId: number; serials: string[] }
    >({
      query: ({ jobId, serials }) => ({
        url: `/jobs/${jobId}/robot-whitelist`,
        method: "PUT",
        body: {
          entries: serials.map((serial) => ({ robotSerial: serial })),
        },
      }),
      invalidatesTags: (_resp, _error, { jobId }) => [
        { type: JobsAPITag.JOB, id: jobId },
      ],
    }),
    updateObjective: builder.mutation<
      Objective,
      Partial<Objective> & Pick<Objective, "id">
    >({
      query: (params) => ({
        url: `/objectives/${params.id}`,
        method: "PATCH",
        body: transformKeys(params, camelToSnake),
      }),
      transformResponse: (response: unknown) => Objective.fromJSON(response),
      invalidatesTags: (_resp, _err, { id }) => [
        { type: JobsAPITag.OBJECTIVE, id },
      ],
    }),
    updateObjectivePriorities: builder.mutation<
      void,
      { jobId: number; changes: { id: number; priority: number }[] }
    >({
      queryFn: async (params, _queryApi, _extraOptions, fetchWithBQ) => {
        const updateResult = await fetchWithBQ({
          url: "/objectives",
          method: "PATCH",
          body: {
            objectives: params.changes,
          },
        });

        if (updateResult.error) {
          return { error: updateResult.error as FetchBaseQueryError };
        }

        return { data: undefined };
      },
      invalidatesTags: (_resp, _error, { jobId }) => [
        { type: JobsAPITag.JOB, id: jobId },
      ],
    }),
    deleteJob: builder.mutation<Job, { id: number }>({
      query: ({ id }) => ({
        url: `/jobs/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_resp, _error, { id }) => [
        JobsAPITag.JOB,
        { type: JobsAPITag.JOB, id },
      ],
    }),
    changeJobState: builder.mutation<Job, { id: number; state: State }>({
      query: ({ id, state }) => ({
        url: `/jobs/${id}/state`,
        method: "PUT",
        body: { state: stateToJSON(state) },
      }),
      invalidatesTags: (_resp, _error, { id }) => [
        { type: JobsAPITag.JOB, id },
      ],
    }),
    getActiveObjective: builder.query<Objective, { serial: string }>({
      query: ({ serial }) => ({
        url: `/robots/${serial}/active-objective`,
      }),
      transformResponse: (response: unknown) => Objective.fromJSON(response),
      providesTags: (resp, _err) =>
        resp ? [{ type: JobsAPITag.OBJECTIVE, id: resp.id }] : [],
    }),
    deleteActiveObjective: builder.mutation<
      Partial<Objective>,
      { serial: string }
    >({
      query: ({ serial }) => ({
        url: `/robots/${serial}/active-objective`,
        method: "DELETE",
      }),
      transformResponse: (response: unknown) => Objective.fromJSON(response),
      invalidatesTags: (resp, _err) =>
        resp
          ? [
              { type: JobsAPITag.OBJECTIVE, id: resp.id },
              { type: JobsAPITag.ASSIGNMENT, id: resp.assignment?.id },
            ]
          : [],
    }),
    getActiveTask: builder.query<Task, { serial: string }>({
      query: ({ serial }) => ({
        url: `/robots/${serial}/active-task`,
        params: {
          include_next_task: true,
        },
      }),
      transformResponse: (response: unknown) => Task.fromJSON(response),
      providesTags: (resp, _err) =>
        resp ? [{ type: JobsAPITag.TASK, id: resp.id }] : [],
    }),
    getTask: builder.query<Task, { id: number }>({
      query: ({ id }) => ({
        url: `/tasks/${id}`,
      }),
      transformResponse: (response: unknown) => Task.fromJSON(response),
      providesTags: (resp, _err, { id }) =>
        resp ? [{ type: JobsAPITag.TASK, id }] : [],
    }),
    startManualTask: builder.mutation<
      Task,
      StartManualTaskRequest & { id: number }
    >({
      query: ({ id, ...params }) => ({
        url: `/tasks/${id}/manual-start`,
        method: "POST",
        body: transformKeys(params, camelToSnake),
      }),
      invalidatesTags: (_resp, _err, { id }) => [{ type: JobsAPITag.TASK, id }],
    }),
    stopManualTask: builder.mutation<
      Task,
      StopManualTaskRequest & { id: number }
    >({
      query: ({ id, ...params }) => ({
        url: `/tasks/${id}/manual-stop`,
        body: transformKeys(params, camelToSnake),
        method: "POST",
      }),
      invalidatesTags: (_resp, _err, { id }) => [{ type: JobsAPITag.TASK, id }],
    }),
    updateIntervention: builder.mutation<
      Intervention,
      Partial<Intervention> & { id: number }
    >({
      query: (params) => ({
        url: `/interventions/${params.id}`,
        method: "PATCH",
        body: transformKeys(params, camelToSnake),
      }),
      transformResponse: (response: unknown) => Intervention.fromJSON(response),
      invalidatesTags: (_resp, _err, { id }) => [
        { type: JobsAPITag.INTERVENTION, id },
      ],
    }),
    listInterventions: builder.query<
      ListInterventionsResponse,
      Partial<ListInterventionsRequest>
    >({
      query: (params) => ({
        url: "/interventions",
        params: transformKeys(params, camelToSnake),
      }),
      transformResponse: (response: unknown) =>
        ListInterventionsResponse.fromJSON(response),
      providesTags: (resp) =>
        resp
          ? [
              JobsAPITag.INTERVENTION,
              ...resp.interventions.map((intv) => ({
                type: JobsAPITag.INTERVENTION,
                id: intv.id,
              })),
            ]
          : [JobsAPITag.INTERVENTION],
    }),
    listTasks: builder.query<
      ListTasksResponse,
      {
        objectiveIds?: number[];
        states?: State[];
        pageSize?: number;
        pageToken?: string;
      }
    >({
      query: (params) => ({
        url: `/tasks`,
        params: transformKeys(params, camelToSnake),
      }),
      transformResponse: (response: unknown) =>
        ListTasksResponse.fromJSON(response),
      providesTags: (resp) =>
        resp
          ? [
              JobsAPITag.TASK,
              ...resp.tasks.map((t) => ({
                type: JobsAPITag.TASK,
                id: t.id,
              })),
            ]
          : [],
    }),
    updateTask: builder.mutation<
      UpdateTaskResponse,
      Partial<Task> & Pick<Task, "id">
    >({
      query: (params) => ({
        url: `/tasks/${params.id}`,
        method: "PATCH",
        body: transformKeys(params, camelToSnake),
      }),
      transformResponse: (response: unknown) =>
        UpdateTaskResponse.fromJSON(response),
      invalidatesTags: (_resp, _err, { id }) => [{ type: JobsAPITag.TASK, id }],
    }),
  }),
});

export const {
  useListJobsQuery,
  useCreateJobMutation,
  useGetJobQuery,
  useGetObjectiveQuery,
  useSetAssignmentMutation,
  useRemoveAssignmentMutation,
  useListAssignmentsQuery,
  useListObjectivesQuery,
  useListTasksQuery,
  useUpdateJobMutation,
  useUpdateAvailableRobotsMutation,
  useUpdateObjectivePrioritiesMutation,
  useUpdateObjectiveMutation,
  useDeleteJobMutation,
  useChangeJobStateMutation,
  useListInterventionsQuery,
  useUpdateInterventionMutation,
  useGetActiveObjectiveQuery,
  useGetTaskQuery,
  useDeleteActiveObjectiveMutation,
  useUpdateTaskMutation,
  useGetActiveTaskQuery,
  useStartManualTaskMutation,
  useStopManualTaskMutation,
} = rtcJobsApi;
