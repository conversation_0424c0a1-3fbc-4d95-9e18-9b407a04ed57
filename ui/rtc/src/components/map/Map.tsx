import * as turf from "@turf/turf";
import { carbonEnv } from "@main/env";
import { degreesToRadians } from "@main/logic/utils/angles";
import {
  type Feature,
  featureCollection,
  lineString,
  type LineString,
  type MultiPolygon,
} from "@turf/helpers";
import { GeoJsonProperties } from "geojson";
import {
  GREEN_LIGHT_PALETTE,
  MAP_COLORS,
  OCEAN_PALETTE,
} from "@main/theme/colors";
import { hex6ToRgbaString } from "@main/utils/theme";
import {
  MapImageId,
  MapImageResolver,
} from "common/components/map/layers/types";
import { RobotLocation } from "@main/store/driving.slice";
import { Tooltip } from "@mui/material";
import { withErrorBoundary } from "../ErrorBoundary";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import clsx from "clsx";
import MapboxMap, {
  Layer,
  type MapRef,
  Marker,
  ScaleControl,
  Source,
} from "react-map-gl";
import React, {
  FC,
  Fragment,
  ReactNode,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import type { Expression, LinePaint } from "mapbox-gl";

const { MAPBOX_ACCESS_TOKEN } = carbonEnv;

const MAP_DEFAULTS = {
  longitude: -120.009_048_324_219_38,
  latitude: 46.665_038_285_723_55,
  zoom: 10,
};

const MIN_HISTORY_LINE_ZOOM = 10;
const FOCUSED_ZOOM = 19;

interface TractorInfo {
  name: string;
  location: RobotLocation;
  requestedAutonomyPath?: Feature<LineString>;
  currentAutonomyPath?: Feature<LineString>;
}
interface MapProps {
  extraControls?: ReactNode;
  fieldBoundaries: {
    fieldId: string;
    name: string;
    shape: Feature<MultiPolygon, GeoJsonProperties>;
  }[];
  focusedFieldId: string | undefined;
  imageConfig?: { resolver: MapImageResolver; idsToLoad: MapImageId[] };
  isFollowingTractor: boolean;
  isInactive: boolean;
  selectedTractor?: string;
  setSelectedTractor: (tractor: string | undefined) => void;
  setIsFollowingTractor: (isFollowing: boolean) => void;
  showMapScaleBar?: boolean;
  tractors: Array<TractorInfo>;
  tractorWidthM: number;
}

export const Map: FC<MapProps> = withErrorBoundary(
  ({
    extraControls,
    fieldBoundaries,
    focusedFieldId,
    imageConfig,
    isFollowingTractor,
    isInactive,
    selectedTractor,
    setIsFollowingTractor,
    setSelectedTractor,
    showMapScaleBar,
    tractors,
    tractorWidthM,
  }) => {
    const map = useRef<MapRef | null>(null);
    const [isInitialized, setInitialized] = useState<boolean>(false);
    const [hasCustomZoom, setHasCustomZoom] = useState<boolean>(false);
    // easeTo operations we issue to follow a tractor interrupt user-initiated
    // UI interactions; while the user is interacting with the board,
    // suspend these operations
    const [isFollowSuspended, setIsFollowSuspended] = useState<boolean>(false);

    const hasFocusedField = useMemo(
      () =>
        focusedFieldId &&
        fieldBoundaries.some((f) => f.fieldId === focusedFieldId),
      [focusedFieldId, fieldBoundaries]
    );

    const tractorsMap = useMemo(
      () =>
        tractors.reduce(
          (tMap, tractor) => {
            return { ...tMap, [tractor.name]: tractor };
          },
          {} as { [robotName: string]: TractorInfo }
        ),
      [tractors]
    );

    useEffect(() => {
      if (imageConfig && isInitialized && map.current) {
        for (const id of imageConfig.idsToLoad) {
          if (!map.current.hasImage(id)) {
            map.current.loadImage(
              imageConfig.resolver(id),
              (error, imageData) => {
                if (error || !imageData) {
                  console.error("failed to load image", error);
                  return;
                }
                if (map.current) {
                  map.current.addImage(id, imageData);
                }
              }
            );
          }
        }
      }
    }, [imageConfig, map, isInitialized]);

    useEffect(() => {
      setIsFollowingTractor(Boolean(selectedTractor));
    }, [selectedTractor, setIsFollowingTractor]);

    useEffect(() => {
      if (
        isInitialized &&
        selectedTractor &&
        tractorsMap[selectedTractor] &&
        isFollowingTractor
      ) {
        const center = tractorsMap[selectedTractor].location.current;
        if (
          center &&
          center.lat &&
          center.lng &&
          isFollowingTractor &&
          !isFollowSuspended
        ) {
          map.current?.easeTo(
            hasCustomZoom
              ? {
                  center: [center.lng, center.lat],
                }
              : {
                  center: [center.lng, center.lat],
                  zoom: FOCUSED_ZOOM,
                }
          );
        }
      }
    }, [
      hasCustomZoom,
      isFollowingTractor,
      isFollowSuspended,
      isInitialized,
      selectedTractor,
      tractorsMap,
    ]);

    useEffect(() => {
      if (map.current) {
        map.current.resize();
      }
    }, [isInactive]);

    return (
      <div
        className={clsx("absolute w-full h-full", {
          "grayscale brightness-200": isInactive,
        })}
      >
        <MapboxMap
          ref={map}
          mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
          mapStyle="mapbox://styles/carbonrobotics/ckz5r36ej004115p4f2wpxw1b"
          initialViewState={MAP_DEFAULTS}
          style={{ width: "auto", height: "100%" }}
          maxZoom={23}
          onRender={() => {
            setInitialized(true);
          }}
          onDrag={() => {
            setIsFollowingTractor(false);
          }}
          onClick={() => {
            setSelectedTractor(undefined);
          }}
          onWheel={() => {
            setHasCustomZoom(true);
          }}
          onRotateStart={() => {
            setIsFollowSuspended(true);
          }}
          onRotateEnd={() => {
            setIsFollowSuspended(false);
          }}
          pitchWithRotate={false}
        >
          {showMapScaleBar && (
            <ScaleControl maxWidth={75} position="top-left" />
          )}
          {fieldBoundaries
            .filter((f) => !hasFocusedField || f.fieldId === focusedFieldId)
            .map(({ name, shape }) => {
              const selectedRobotPosition = selectedTractor
                ? tractorsMap[selectedTractor].location.current
                : undefined;
              const isActiveTractorInField = selectedRobotPosition
                ? turf.booleanPointInPolygon(
                    [selectedRobotPosition.lng, selectedRobotPosition.lat],
                    shape
                  )
                : false;
              const sourceId = `field-${name}`;
              return (
                <Source key={name} id={sourceId} type="geojson" data={shape}>
                  <Layer
                    type="line"
                    paint={{
                      "line-color": isActiveTractorInField
                        ? MAP_COLORS.field.active
                        : MAP_COLORS.field.inactive,
                      "line-width": 3,
                    }}
                    source={sourceId}
                  />
                </Source>
              );
            })}
          {tractors.map(
            ({
              name,
              location: { current, history },
              requestedAutonomyPath,
              currentAutonomyPath,
            }) => {
              if (!current) {
                return null;
              }
              const { lng, lat } = current;
              const historicalLineGeoJson =
                history.length >= 2
                  ? featureCollection([
                      lineString(
                        [
                          ...history.map(({ lat, lng }) => [lng, lat]),
                          [lng, lat],
                        ],
                        {
                          latitudeRadians: degreesToRadians(lat),
                          tractorWidthM,
                        }
                      ),
                    ])
                  : undefined;

              const pathOpacity = selectedTractor === name ? 1 : 0.7;

              return (
                <Fragment key={name}>
                  <Marker
                    style={{ cursor: "pointer" }}
                    latitude={lat}
                    longitude={lng}
                    rotationAlignment="map"
                    rotation={current.heading}
                    onClick={(e) => {
                      e.originalEvent.stopPropagation();
                      setSelectedTractor(name);
                      setIsFollowingTractor(true);
                      setHasCustomZoom(false);
                    }}
                  >
                    <Tooltip
                      title={
                        <div className="flex flex-col">
                          <strong>{name}</strong>
                        </div>
                      }
                      arrow
                    >
                      {/* this wrapper prevents react from trying to ref an SVG which breaks*/}
                      <div
                        className={clsx(
                          "bg-white rounded-full p-1 shadow-md  flex items-center justify-center",
                          {
                            "border-4 border-solid border-primary-500":
                              selectedTractor,
                            "border-none": !selectedTractor,
                          }
                        )}
                      >
                        <ArrowBackIcon
                          className={clsx("text-md rotate-90", {
                            "text-rtc-gray-700": !selectedTractor,
                            "text-primary-500": selectedTractor,
                          })}
                        />
                      </div>
                    </Tooltip>
                  </Marker>
                  {historicalLineGeoJson ? (
                    <Source
                      id="historical-line"
                      type="geojson"
                      lineMetrics={true}
                      data={historicalLineGeoJson}
                    >
                      <Layer
                        type="line"
                        paint={historicalLinePaint}
                        minzoom={MIN_HISTORY_LINE_ZOOM}
                        source="historical-line"
                      />
                    </Source>
                  ) : null}
                  {currentAutonomyPath ? (
                    <Source
                      id="currentAutonomyPath"
                      type="geojson"
                      data={currentAutonomyPath}
                    >
                      <Layer
                        type="line"
                        paint={{
                          "line-color": GREEN_LIGHT_PALETTE["400"],
                          "line-width": 5,
                          "line-opacity": pathOpacity,
                        }}
                        source="currentAutonomyPath"
                      />
                    </Source>
                  ) : null}
                  {requestedAutonomyPath ? (
                    <Source
                      id="requestedAutonomyPath"
                      type="geojson"
                      data={requestedAutonomyPath}
                    >
                      <Layer
                        type="line"
                        paint={{
                          "line-color": GREEN_LIGHT_PALETTE["200"],
                          "line-width": 5,
                          "line-opacity": pathOpacity,
                          "line-dasharray": [1, 0.5],
                        }}
                        source="requestedAutonomyPath"
                      />
                    </Source>
                  ) : null}
                </Fragment>
              );
            }
          )}
          {extraControls}
        </MapboxMap>
      </div>
    );
  },
  { small: true }
);

// https://github.com/mapbox/mapbox-gl-js/issues/5861#issuecomment-2654740939
const pxPerMeterAtZoom = (zoomLevel: number): Expression => {
  // At zoom level 16, one pixel at the equator corresponds to 1.194 meters:
  // https://docs.mapbox.com/help/glossary/zoom-level/
  const baseZoom = 16;
  const baseWidthAtEquator = 1 / 1.194; // px/m
  // Each zoom level magnifies by a factor of two.
  const zoomScale = 2 ** (zoomLevel - baseZoom);
  const zoomedWidthAtEquator = baseWidthAtEquator * zoomScale;
  // And, away from the equator, there are more pixels per meter.
  const projectionScale = ["/", 1, ["cos", ["get", "latitudeRadians"]]];
  return ["*", zoomedWidthAtEquator, projectionScale];
};

const historicalLinePaint: LinePaint = {
  "line-color": "red",
  "line-width": [
    "interpolate",
    ["exponential", 2],
    ["zoom"],
    ...[10, 24].flatMap((zoomLevel) => {
      return [
        zoomLevel,
        ["*", ["get", "tractorWidthM"], pxPerMeterAtZoom(zoomLevel)],
      ];
    }),
  ],
  "line-gradient": [
    "interpolate",
    ["linear"],
    ["line-progress"],
    0,
    hex6ToRgbaString(OCEAN_PALETTE[900], 0.5),
    0.25,
    hex6ToRgbaString(OCEAN_PALETTE.DEFAULT, 0.75),
    0.75,
    hex6ToRgbaString(OCEAN_PALETTE.DEFAULT, 0.95),
    1,
    hex6ToRgbaString(OCEAN_PALETTE[50], 1),
  ],
};
