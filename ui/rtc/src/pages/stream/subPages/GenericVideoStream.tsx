import { Alerts } from "@main/components/alerts/Alerts";
import { FullScreen, useFullScreenHandle } from "react-full-screen";
import { IconButton, Paper } from "@mui/material";
import { Link } from "react-router-dom";
import { ROUTES } from "@main/pages/routes";
import { useFullScreenContext } from "@main/components/fullScreenContext";
import { useVideoStreamContext } from "../videoStreamContext";
import { Video } from "@main/components/Video";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import React, { FC } from "react";

export const GenericVideoStream: FC = () => {
  const fullScreenHandle = useFullScreenHandle();
  const { recalculate } = useFullScreenContext();
  const { stream } = useVideoStreamContext();

  return (
    <FullScreen handle={fullScreenHandle} onChange={recalculate}>
      <div className="flex h-screen w-screen">
        <div className="flex flex-col h-full w-full">
          <Paper className="p-1">
            <IconButton component={Link} to={ROUTES.Tractors.path}>
              <ArrowBackIcon aria-label="back" />
            </IconButton>
          </Paper>
          <div className="flex-grow overflow-hidden">
            <Video className="h-full w-full" srcObject={stream} />
          </div>
        </div>
      </div>
      <Alerts />
    </FullScreen>
  );
};
